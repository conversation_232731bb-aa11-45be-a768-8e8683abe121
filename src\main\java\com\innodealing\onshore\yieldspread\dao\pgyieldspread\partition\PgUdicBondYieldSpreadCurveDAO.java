package com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.AreaTypeEnum;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvUdicBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.repository.PgUdicBondYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.enums.UdicRegionEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.UdicShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.DivisionYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.InduSpreadPanoramaBO;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.UdicShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.*;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.between;
import static com.innodealing.commons.object.ObjectExtensionUtils.isAllEmpty;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.divideHundredThousand;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE;

/**
 * 行业利差曲线DAO(不包含行业1和行业2)
 *
 * <AUTHOR>
 */
@Repository
public class PgUdicBondYieldSpreadCurveDAO {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private UdicShardBondYieldSpreadCurveMapper udicShardBondYieldSpreadCurveMapper;

    @Resource
    private MvUdicBondYieldSpreadCurveDAO mvUdicBondYieldSpreadCurveDAO;

    @Resource
    private PgUdicBondYieldSpreadCurveRepository pgUdicBondYieldSpreadCurveRepository;

    /**
     * 查询行业利差曲线-物化视图
     *
     * @param searchParameter 搜索参数DTO
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 物化视图中行业利差曲线数据响应集
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurves(UdicBondYieldSpreadParamDTO searchParameter) {
        List<? extends BasePgUdicBondYieldSpreadCurveDO> induBondYieldSpreadCurveList;
        if (isAllEmpty(searchParameter.getProvinceUniCode(), searchParameter.getCityUniCode())) {
            induBondYieldSpreadCurveList = pgUdicBondYieldSpreadCurveRepository.listYieldSpreadCurvesForAll(searchParameter);
        } else if (Objects.nonNull(searchParameter.getProvinceUniCode())) {
            induBondYieldSpreadCurveList = pgUdicBondYieldSpreadCurveRepository.listYieldSpreadCurvesForProvince(searchParameter);
        } else {
            induBondYieldSpreadCurveList = pgUdicBondYieldSpreadCurveRepository.listYieldSpreadCurvesForCity(searchParameter);
        }
        if (CollectionUtils.isEmpty(induBondYieldSpreadCurveList)) {
            return Collections.emptyList();
        }
        return induBondYieldSpreadCurveList.stream().map(mvCurve -> {
            BondYieldSpreadCurveBO curve = BeanCopyUtils.copyProperties(mvCurve, BondYieldSpreadCurveBO.class);
            // 物化视图中的数据乘以100_000,需要除以100_000
            ObjectExtensionUtils.ifNonNull(mvCurve.getBondCreditSpread(), v -> divideHundredThousand(BigDecimal.valueOf(v)).ifPresent(curve::setBondCreditSpread));
            ObjectExtensionUtils.ifNonNull(mvCurve.getBondExcessSpread(), v -> divideHundredThousand(BigDecimal.valueOf(v)).ifPresent(curve::setBondExcessSpread));
            ObjectExtensionUtils.ifNonNull(mvCurve.getCbYield(), v -> divideHundredThousand(BigDecimal.valueOf(v), YIELD_SPREAD_KEEP_SCALE).ifPresent(curve::setCbYield));
            return curve;
        }).collect(Collectors.toList());
    }

    /**
     * 从物化视图同步昨日利差曲线数据
     */
    public void syncCurveIncrFromMV() {
        pgUdicBondYieldSpreadCurveRepository.syncCurveIncrFromMV();
    }

    /**
     * 从物化视图刷新城投利差曲线数据
     *
     * @param udicRegionEnum 城投区域
     */
    public void refreshUdicBondYieldSpreadCurveFromMV(UdicRegionEnum udicRegionEnum) {
        pgUdicBondYieldSpreadCurveRepository.refreshUdicBondYieldSpreadCurveFromMV(udicRegionEnum);
    }

    /**
     * 查询整个城投数据作为一个行业
     *
     * @param searchParameter 查询整个城投数据作为一个行业请求参数
     * @return {@link Optional}<{@link InduSpreadPanoramaBO}> 查询整个城投数据作为一个行业响应数据
     */
    public Optional<InduSpreadPanoramaBO> getInduBondYieldSpreadPanorama(UdicBondYieldSpreadParamDTO searchParameter) {
        Optional<PgUdicBondYieldSpreadCurveAllDO> spreadCurveAll = pgUdicBondYieldSpreadCurveRepository.getInduBondYieldSpreadPanorama(searchParameter);
        return spreadCurveAll.map(curve -> {
            InduSpreadPanoramaBO response = BeanCopyUtils.copyProperties(curve, InduSpreadPanoramaBO.class);
            ObjectExtensionUtils.ifNonNull(curve.getBondCreditSpread(), credit -> divideHundredThousand(BigDecimal.valueOf(credit)).ifPresent(response::setBondCreditSpread));
            ObjectExtensionUtils.ifNonNull(curve.getBondExcessSpread(), excess -> divideHundredThousand(BigDecimal.valueOf(excess)).ifPresent(response::setBondExcessSpread));
            return response;
        });
    }

    /**
     * 查询整个城投数据作为一个行业集合
     *
     * @param searchParameter 查询整个城投数据作为一个行业请求参数
     * @return {@link List}<{@link InduSpreadPanoramaBO}> 查询整个城投数据作为一个行业响应数据集
     */
    public List<InduSpreadPanoramaBO> listInduBondYieldSpreadPanoramas(UdicBondYieldSpreadParamDTO searchParameter) {
        List<PgUdicBondYieldSpreadCurveAllDO> spreadCurveAllList = pgUdicBondYieldSpreadCurveRepository.listInduBondYieldSpreadPanoramas(searchParameter);
        return spreadCurveAllList.stream().map(curve -> {
            InduSpreadPanoramaBO response = BeanCopyUtils.copyProperties(curve, InduSpreadPanoramaBO.class);
            ObjectExtensionUtils.ifNonNull(curve.getBondCreditSpread(), credit -> divideHundredThousand(BigDecimal.valueOf(credit)).ifPresent(response::setBondCreditSpread));
            ObjectExtensionUtils.ifNonNull(curve.getBondExcessSpread(), excess -> divideHundredThousand(BigDecimal.valueOf(excess)).ifPresent(response::setBondExcessSpread));
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 同步城投利差曲线数据
     *
     * @param router 路由
     * @param param  参数
     */
    public void syncCurveShardUdicForMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        UdicBondYieldSpreadCurveParameter parameter = builderParameter(router);
        if (param.isTableRefresh()) {
            pgUdicBondYieldSpreadCurveRepository.refreshTable(parameter.getTableName(), parameter);
        }
        DynamicQuery<UdicShardBondYieldSpreadCurveDO> query = builderDynamicQuery(router);
        int count = udicShardBondYieldSpreadCurveMapper.selectCountByDynamicQuery(query, router);
        if (count > 0) {
            udicShardBondYieldSpreadCurveMapper.deleteByDynamicQueryRouter(query, router);
            logger.info("[refreshMvInduBondYieldSpreadBondCurveRouter]重刷数据:{},dateRange:{}", parameter.getTableName(), router.getSpreadDateRange());
        }
        //同步最新数据
        logger.info("[refreshMvInduBondYieldSpreadBondCurveRouter]开始同步城投评级分片表数据:{},dateRange:{} ", parameter.getTableName(), router.getSpreadDateRange());
        pgUdicBondYieldSpreadCurveRepository.syncCurveIncrFromMV(parameter.getTableName(), mvUdicBondYieldSpreadCurveDAO.getMvName(router));
        logger.info("[refreshMvInduBondYieldSpreadBondCurveRouter]结束同步城投评级分片表数据:{} ,dateRange:{}", parameter.getTableName(), router.getSpreadDateRange());

    }

    private UdicBondYieldSpreadCurveParameter builderParameter(AbstractRatingRouter router) {
        UdicBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, UdicBondYieldSpreadCurveParameter.class);
        parameter.setOperatorLevel(router.getLevel());
        parameter.setTableName(getTableName(router));
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (spreadDateRange != null) {
            parameter.setStartDate(spreadDateRange.getStartDate());
            parameter.setEndDate(spreadDateRange.getEndDate());
        }
        return parameter;
    }

    private DynamicQuery<UdicShardBondYieldSpreadCurveDO> builderDynamicQuery(AbstractRatingRouter router) {
        DynamicQuery<UdicShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(UdicShardBondYieldSpreadCurveDO.class);
        if (Objects.nonNull(router.getSpreadDateRange()) &&
                Objects.nonNull(router.getSpreadDateRange().getStartDate()) &&
                Objects.nonNull(router.getSpreadDateRange().getEndDate())) {
            query.and(Objects.nonNull(router.getSpreadDateRange()), UdicShardBondYieldSpreadCurveDO::getSpreadDate,
                    between(router.getSpreadDateRange().getStartDate(), router.getSpreadDateRange().getEndDate()));
        }
        return query;

    }

    private String getTableName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()), udicShardBondYieldSpreadCurveMapper.getLogicTable(),
                RatingCombinationHelper.getTableNameSuffix(router));
    }

    /**
     * 查询城投利差
     *
     * @param searchParam 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listBondYieldSpreads(UdicYieldSearchParam searchParam) {
        final List<BondYieldSpreadBO> yieldSpreadList = new ArrayList<>();
        if (Objects.nonNull(searchParam.getCityUniCode())) {
            List<PgUdicBondYieldSpreadCurveCityDO> yieldSpreads = pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromCity(searchParam);
            yieldSpreads.forEach(ys -> yieldSpreadList.add(convertToBondYieldSpreadBO(ys)));
        } else if (Objects.nonNull(searchParam.getProvinceUniCode())) {
            List<PgUdicBondYieldSpreadCurveProvinceDO> yieldSpreads = pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromProvince(searchParam);
            yieldSpreads.forEach(ys -> yieldSpreadList.add(convertToBondYieldSpreadBO(ys)));
        } else if (Objects.nonNull(searchParam.getDistrictUniCode())) {
            List<PgUdicBondYieldSpreadCurveDistrictDO> yieldSpreads = pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromDistrict(searchParam);
            yieldSpreads.forEach(ys -> yieldSpreadList.add(convertToBondYieldSpreadBO(ys)));
        } else {
            List<PgUdicBondYieldSpreadCurveAllDO> yieldSpreads = pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromAll(searchParam);
            yieldSpreads.forEach(ys -> yieldSpreadList.add(convertToBondYieldSpreadBO(ys)));
        }
        return yieldSpreadList;
    }

    /**
     * 获取城投各平台利差
     *
     * @param spreadDate 利差日期
     * @param areaCode   区域code
     * @param areaType   区域类型
     * @return Optional<PgUdicBondYieldSpreadCurveCityDO>
     */
    public DivisionYieldSpreadBO getDivisionYieldSpread(Date spreadDate, Long areaCode, AreaTypeEnum areaType) {
        DivisionYieldSpreadBO divisionYieldSpread = new DivisionYieldSpreadBO();
        if (Objects.isNull(spreadDate) || Objects.isNull(areaCode) || Objects.isNull(areaType)) {
            return divisionYieldSpread;
        }
        final Map<Integer, Long> yieldSpreadMap = new HashMap<>(AreaTypeEnum.values().length);
        UdicYieldSearchParam searchParam = new UdicYieldSearchParam();
        searchParam.setSpreadDate(spreadDate);
        if (Objects.equals(areaType, AreaTypeEnum.PROVINCE)) {
            searchParam.setProvinceUniCode(areaCode);
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromProvince(searchParam).stream().findFirst().ifPresent(
                    v -> divisionYieldSpread.setBondCreditSpread(CalculationHelper.divideHundredThousand(v.getBondCreditSpread(), YIELD_SPREAD_KEEP_SCALE).orElse(null)));
            searchParam.setAdministrativeDivision(AreaTypeEnum.PROVINCE.getValue());
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromProvince(searchParam).stream().findFirst()
                    .ifPresent(v -> yieldSpreadMap.put(AreaTypeEnum.PROVINCE.getValue(), v.getBondCreditSpread()));
            searchParam.setAdministrativeDivision(AreaTypeEnum.CITY.getValue());
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromProvince(searchParam).stream().findFirst()
                    .ifPresent(v -> yieldSpreadMap.put(AreaTypeEnum.CITY.getValue(), v.getBondCreditSpread()));
            searchParam.setAdministrativeDivision(AreaTypeEnum.DISTRICT.getValue());
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromProvince(searchParam).stream().findFirst()
                    .ifPresent(v -> yieldSpreadMap.put(AreaTypeEnum.DISTRICT.getValue(), v.getBondCreditSpread()));
        } else if (Objects.equals(areaType, AreaTypeEnum.CITY)) {
            searchParam.setCityUniCode(areaCode);
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromCity(searchParam).stream().findFirst().ifPresent(
                    v -> divisionYieldSpread.setBondCreditSpread(CalculationHelper.divideHundredThousand(v.getBondCreditSpread(), YIELD_SPREAD_KEEP_SCALE).orElse(null)));
            searchParam.setAdministrativeDivision(AreaTypeEnum.PROVINCE.getValue());
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromCity(searchParam).stream().findFirst()
                    .ifPresent(v -> yieldSpreadMap.put(AreaTypeEnum.PROVINCE.getValue(), v.getBondCreditSpread()));
            searchParam.setAdministrativeDivision(AreaTypeEnum.CITY.getValue());
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromCity(searchParam).stream().findFirst()
                    .ifPresent(v -> yieldSpreadMap.put(AreaTypeEnum.CITY.getValue(), v.getBondCreditSpread()));
            searchParam.setAdministrativeDivision(AreaTypeEnum.DISTRICT.getValue());
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromCity(searchParam).stream().findFirst()
                    .ifPresent(v -> yieldSpreadMap.put(AreaTypeEnum.DISTRICT.getValue(), v.getBondCreditSpread()));
        } else if (Objects.equals(areaType, AreaTypeEnum.DISTRICT)) {
            //区县级没有平台选项
            searchParam.setDistrictUniCode(areaCode);
            pgUdicBondYieldSpreadCurveRepository.listYieldSpreadsFromDistrict(searchParam).stream().findFirst().ifPresent(
                    v -> divisionYieldSpread.setBondCreditSpread(CalculationHelper.divideHundredThousand(v.getBondCreditSpread(), YIELD_SPREAD_KEEP_SCALE).orElse(null)));
        }
        CalculationHelper.divideHundredThousand(yieldSpreadMap.get(AreaTypeEnum.PROVINCE.getValue()), YIELD_SPREAD_KEEP_SCALE)
                .ifPresent(divisionYieldSpread::setProvinceBondCreditSpread);
        CalculationHelper.divideHundredThousand(yieldSpreadMap.get(AreaTypeEnum.CITY.getValue()), YIELD_SPREAD_KEEP_SCALE)
                .ifPresent(divisionYieldSpread::setCityBondCreditSpread);
        CalculationHelper.divideHundredThousand(yieldSpreadMap.get(AreaTypeEnum.DISTRICT.getValue()), YIELD_SPREAD_KEEP_SCALE)
                .ifPresent(divisionYieldSpread::setDistrictBondCreditSpread);
        return divisionYieldSpread;
    }

    private BondYieldSpreadBO convertToBondYieldSpreadBO(BaseYieldSpreadDataDO baseYieldSpreadDataDO) {
        BondYieldSpreadBO bondYieldSpreadBO = BeanCopyUtils.copyProperties(baseYieldSpreadDataDO, BondYieldSpreadBO.class);
        bondYieldSpreadBO.setBondCreditSpread(CalculationHelper.divideHundredThousand(baseYieldSpreadDataDO.getBondCreditSpread()).orElse(null));
        bondYieldSpreadBO.setBondExcessSpread(CalculationHelper.divideHundredThousand(baseYieldSpreadDataDO.getBondExcessSpread()).orElse(null));
        bondYieldSpreadBO.setCbYield(CalculationHelper.divideHundredThousand(baseYieldSpreadDataDO.getCbYield(), YIELD_SPREAD_KEEP_SCALE).orElse(null));
        bondYieldSpreadBO.setAvgBondCreditSpread(CalculationHelper.divideHundredThousand(baseYieldSpreadDataDO.getAvgBondCreditSpread()).orElse(null));
        bondYieldSpreadBO.setAvgBondExcessSpread(CalculationHelper.divideHundredThousand(baseYieldSpreadDataDO.getAvgBondExcessSpread()).orElse(null));
        bondYieldSpreadBO.setAvgCbYield(CalculationHelper.divideHundredThousand(baseYieldSpreadDataDO.getAvgCbYield(), YIELD_SPREAD_KEEP_SCALE).orElse(null));
        return bondYieldSpreadBO;
    }

}
