package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.QueryHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.SecuBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.SecuBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.dto.SecuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.SecuBondYieldSpreadGroupDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 证券债利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class SecuBondYieldSpreadDAO {

    @Resource
    private SecuBondYieldSpreadMapper secuBondYieldSpreadMapper;

    @Resource
    private SecuBondYieldSpreadGroupMapper secuBondYieldSpreadGroupMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    private final QueryHelper queryHelper = new QueryHelper();

    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    public void createShardingTable(String tableName) {
        secuBondYieldSpreadMapper.createShardingTable(tableName);
    }

    /**
     * 批量更新
     *
     * @param startDate                 开始日期
     * @param endDate                   结束日期
     * @param secuBondYieldSpreadDOList 证券债利差列表
     * @return 受影响的行数
     */
    public int saveSecuBondYieldSpreadDOList(Date startDate, Date endDate, List<SecuBondYieldSpreadDO> secuBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(secuBondYieldSpreadDOList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        long startPk = ShardingUtils.getMinPkOfDate(startDate);
        long endPk = ShardingUtils.getMaxPkOfDate(endDate);
        Set<Long> bondUniCodes = secuBondYieldSpreadDOList.stream().map(SecuBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<SecuBondYieldSpreadDO> query = DynamicQuery.createQuery(SecuBondYieldSpreadDO.class)
                .select(SecuBondYieldSpreadDO::getId, SecuBondYieldSpreadDO::getBondUniCode,
                        SecuBondYieldSpreadDO::getSpreadDate, SecuBondYieldSpreadDO::getInduLevel1Code,
                        SecuBondYieldSpreadDO::getInduLevel1Name, SecuBondYieldSpreadDO::getInduLevel2Code,
                        SecuBondYieldSpreadDO::getInduLevel2Name, SecuBondYieldSpreadDO::getBusinessNature)
                .and(SecuBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(SecuBondYieldSpreadDO::getSpreadDate, isEqual(startDate))
                .and(SecuBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<SecuBondYieldSpreadDO> existDataList = secuBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(secuBondYieldSpreadDOList));
        } else {
            Map<String, SecuBondYieldSpreadDO> existSecuBondYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<SecuBondYieldSpreadDO> insertList = new ArrayList<>();
            List<SecuBondYieldSpreadDO> updateList = new ArrayList<>();
            for (SecuBondYieldSpreadDO secuBondYieldSpreadDO : secuBondYieldSpreadDOList) {
                SecuBondYieldSpreadDO existSecuBondYieldSpreadDO = existSecuBondYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, secuBondYieldSpreadDO.getBondUniCode(),
                                secuBondYieldSpreadDO.getSpreadDate().getTime()));
                if (isNull(existSecuBondYieldSpreadDO)) {
                    insertList.add(secuBondYieldSpreadDO);
                } else {
                    secuBondYieldSpreadDO.setId(existSecuBondYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existSecuBondYieldSpreadDO.getInduLevel1Code(),
                            secuBondYieldSpreadDO::setInduLevel1Code);
                    ObjectExtensionUtils.ifNonNull(existSecuBondYieldSpreadDO.getInduLevel1Name(),
                            secuBondYieldSpreadDO::setInduLevel1Name);
                    ObjectExtensionUtils.ifNonNull(existSecuBondYieldSpreadDO.getInduLevel2Code(),
                            secuBondYieldSpreadDO::setInduLevel2Code);
                    ObjectExtensionUtils.ifNonNull(existSecuBondYieldSpreadDO.getInduLevel2Name(),
                            secuBondYieldSpreadDO::setInduLevel2Name);
                    ObjectExtensionUtils.ifNonNull(existSecuBondYieldSpreadDO.getBusinessNature(),
                            secuBondYieldSpreadDO::setBusinessNature);
                    updateList.add(secuBondYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 证券债利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<SecuBondYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<SecuBondYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(SecuBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (SecuBondYieldSpreadDO secuBondYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<SecuBondYieldSpreadDO> updateQuery = DynamicQuery.createQuery(SecuBondYieldSpreadDO.class)
                        .and(SecuBondYieldSpreadDO::getId, isEqual(secuBondYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(secuBondYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 证券债利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<SecuBondYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<SecuBondYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(SecuBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (SecuBondYieldSpreadDO secuBondYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(secuBondYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 根据主体分组获取证券债利差
     *
     * @param spreadDate 开始日期
     * @return 证券债利差
     */
    public List<SecuBondYieldSpreadGroupDO> listSecuBondYieldSpreadGroupDOs(Date spreadDate) {
        long startPk = ShardingUtils.getMinPkOfDate(spreadDate);
        long endPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        GroupedQuery<SecuBondYieldSpreadDO, SecuBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(SecuBondYieldSpreadDO.class, SecuBondYieldSpreadGroupDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER)
                        .select(SecuBondYieldSpreadGroupDO::getComUniCode,
                                SecuBondYieldSpreadGroupDO::getInduLevel1Code, SecuBondYieldSpreadGroupDO::getInduLevel1Name,
                                SecuBondYieldSpreadGroupDO::getInduLevel2Code,
                                SecuBondYieldSpreadGroupDO::getInduLevel2Name, SecuBondYieldSpreadGroupDO::getBusinessNature,
                                SecuBondYieldSpreadGroupDO::getComExtRatingMapping, SecuBondYieldSpreadGroupDO::getSpreadDate)
                        .and(SecuBondYieldSpreadDO::getId, between(startPk, endPk))
                        .and(SecuBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(g -> g.and(SecuBondYieldSpreadDO::getBondCreditSpread, notEqual(null))
                                .or(SecuBondYieldSpreadDO::getBondExcessSpread, notEqual(null)))
                        .groupBy(SecuBondYieldSpreadDO::getComUniCode);
        return secuBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 查询证券单券利差
     *
     * @param param 查询参数
     * @return 单券利差
     */
    public NormPagingResult<SecuBondYieldSpreadDO> listSecuSingleBondYieldSpreads(SecuYieldSearchParam param) {
        NormPagingQuery<SecuBondYieldSpreadDO> query = NormPagingQuery
                .createQuery(SecuBondYieldSpreadDO.class, param.getPageNum(), param.getPageSize())
                .and(this.listCommonFilters(param));
        if (Objects.nonNull(param.getSort())) {
            SortDTO sort = param.getSort();
            String columnName = queryHelper.getQueryColumnByProperty(BankBondYieldSpreadDO.class, sort.getPropertyName());
            query.addSorts(new CustomSortDescriptor(columnName + " IS NULL"));
            query.addSorts(new SortDescriptor(sort.getPropertyName(), sort.getSortDirection()));
        }
        return secuBondYieldSpreadMapper.selectByNormalPaging(query);
    }

    private BaseFilterDescriptor<SecuBondYieldSpreadDO>[] listCommonFilters(SecuYieldSearchParam param) {
        long startPk = ShardingUtils.getMinPkOfDate(param.getSpreadDate());
        long endPk = ShardingUtils.getMaxPkOfDate(param.getSpreadDate());
        FilterGroupDescriptor<SecuBondYieldSpreadDO> filterGroup = FilterGroupDescriptor.create(SecuBondYieldSpreadDO.class)
                .and(SecuBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(SecuBondYieldSpreadDO::getSpreadDate, isEqual(param.getSpreadDate()))
                .and(nonNull(param.getSpreadBondType()), SecuBondYieldSpreadDO::getSecuritySeniorityRanking, isEqual(param.getSpreadBondType()))
                .and(nonNull(param.getRemainingTenor()), SecuBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(param.getRemainingTenor()))
                .and(nonNull(param.getComUniCode()), SecuBondYieldSpreadDO::getComUniCode, isEqual(param.getComUniCode()))
                .and(nonNull(param.getBondUniCode()), SecuBondYieldSpreadDO::getBondUniCode, isEqual(param.getBondUniCode()))
                .and(isNotEmpty(param.getBondImpliedRatingMappings()), SecuBondYieldSpreadDO::getBondImpliedRatingMapping,
                        in(param.getBondImpliedRatingMappings()))
                .and(SecuBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return filterGroup.getFilters();
    }

    /**
     * 获取债券
     *
     * @return 债券
     */
    public List<SecuBondYieldSpreadGroupDO> listBonds() {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(YieldSpreadConst.CURVE_START_DATE);
        Long manPkOfDate = ShardingUtils.getMaxPkOfDate(Date.valueOf(LocalDate.now()));
        GroupedQuery<SecuBondYieldSpreadDO, SecuBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(SecuBondYieldSpreadDO.class, SecuBondYieldSpreadGroupDO.class)
                        .select(SecuBondYieldSpreadGroupDO::getComUniCode,
                                SecuBondYieldSpreadGroupDO::getBondUniCode,
                                SecuBondYieldSpreadGroupDO::getBondCode)
                        .and(SecuBondYieldSpreadDO::getId, between(minPkOfDate, manPkOfDate))
                        .and(SecuBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                        .groupBy(SecuBondYieldSpreadDO::getBondUniCode);
        return secuBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 查询证券债券利差数据集
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link SecuBondYieldSpreadDO}>
     */
    public List<SecuBondYieldSpreadDO> listSecuBondYieldSpreads(Date spreadDate, Set<Long> bondUniCodes) {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPkOfDate = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<SecuBondYieldSpreadDO> query = DynamicQuery.createQuery(SecuBondYieldSpreadDO.class)
                .and(SecuBondYieldSpreadDO::getId, between(minPkOfDate, maxPkOfDate))
                .and(SecuBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        return secuBondYieldSpreadMapper.selectByDynamicQuery(query);
    }

}
