package com.innodealing.onshore.yieldspread.enums;

import com.google.common.collect.Lists;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * 期限枚举
 *
 * <AUTHOR>
 * @date 2024/10/18 13:52
 **/
public enum PeriodEnum implements ITextValueEnum {
    /**
     * 1月
     */
    ONE_MONTH(1, "1M", "{%s.yield1M}"),
    THREE_MONTHS(3, "3M", "{%s.yield3M}"),
    SIX_MONTHS(6, "6M", "{%s.yield6M}"),
    NINE_MONTHS(9, "9M", "{%s.yield9M}"),
    ONE_YEAR(12, "1Y", "{%s.yield1Y}"),
    TWO_YEARS(24, "2Y", "{%s.yield2Y}"),
    THREE_YEARS(36, "3Y", "{%s.yield3Y}"),
    FIVE_YEARS(60, "5Y", "{%s.yield5Y}"),
    SEVEN_YEARS(84, "7Y", "{%s.yield7Y}"),
    TEN_YEARS(120, "10Y", "{%s.yield10Y}"),
    FIFTEEN_YEARS(180, "15Y", "{%s.yield15Y}"),
    TWENTY_YEARS(240, "20Y", "{%s.yield20Y}"),
    THIRTY_YEARS(360, "30Y", "{%s.yield30Y}"),
    FIFTY_YEARS(600, "50Y", "{%s.yield50Y}");

    PeriodEnum(int value, String text, String periodExcelFill) {
        this.value = value;
        this.text = text;
        this.periodExcelFill = periodExcelFill;
    }

    private final int value;

    private final String text;

    private final String periodExcelFill;


    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }

    /**
     * 获取excel期限 填充字段
     *
     * @return 填充字段
     */
    public String getPeriodExcelFill() {
        return periodExcelFill;
    }


    /**
     * 期限利差 以下期限与10Y同时存在时需要过滤掉10Y
     */
    private static final Set<PeriodEnum> FILTER_10Y_PEROID_TRACE_REFER = EnumSet.of(PeriodEnum.FIFTEEN_YEARS, PeriodEnum.TWENTY_YEARS,
            PeriodEnum.THIRTY_YEARS);

    /**
     * 获取 期限利差 以下期限 与10Y同时存在时需要过滤掉10Y
     *
     * @return {@link Set}<{@link PeriodEnum}> 参考期限
     */
    public static Set<PeriodEnum> getFilter10yPeroidTraceRefer() {
        return EnumSet.copyOf(FILTER_10Y_PEROID_TRACE_REFER);
    }

    /**
     * 获取 当前债券类型下 根据用户配置的期限 期限利差 实际对应的期限
     *
     * @param sourcePeriodEnumList 用户配置的期限
     * @param bondTypeEnum         债券类型
     * @return {@link PeriodEnum}
     */
    public static List<PeriodEnum> listPeriodTraceBy(List<PeriodEnum> sourcePeriodEnumList, YieldPanoramaBondTypeEnum bondTypeEnum) {
        if (CollectionUtils.isEmpty(sourcePeriodEnumList) || Objects.isNull(bondTypeEnum)) {
            return Lists.newArrayList();
        }
        List<PeriodEnum> periodEnums = new ArrayList<>(sourcePeriodEnumList);
        //特殊处理, 如果 存在 10Y 和 (15Y 20Y 30 Y)中的任意一个,期限利差过滤掉10Y
        if (periodEnums.contains(PeriodEnum.TEN_YEARS) && periodEnums.stream().anyMatch(FILTER_10Y_PEROID_TRACE_REFER::contains)) {
            periodEnums.remove(PeriodEnum.TEN_YEARS);
        }
        return bondTypeEnum.getTracePeriodEnum().map(tracePeriodEnum ->
                ITracePeriodCommonEnum.listPeroidByDistinctDesc(tracePeriodEnum, periodEnums)).orElse(periodEnums);
    }

    /**
     * 根据 value列表获得text列表
     *
     * @param values value列表
     * @return text列表
     */
    public static List<String> getTextsByValues(List<Integer> values) {
        List<String> textList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(values)) {
            return textList;
        }
        for (Integer value : values) {
            for (PeriodEnum period : PeriodEnum.values()) {
                if (Integer.valueOf(period.getValue()).equals(value)) {
                    textList.add(period.getText());
                    break;
                }
            }
        }
        return textList;
    }

    /**
     * 根据 value列表获得PeriodEnum列表
     *
     * @param values value列表
     * @return PeriodEnum列表
     */
    public static List<PeriodEnum> getPeriodEnumByValues(List<Integer> values) {
        List<PeriodEnum> periodEnumList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(values)) {
            return periodEnumList;
        }
        for (Integer value : values) {
            for (PeriodEnum period : PeriodEnum.values()) {
                if (Integer.valueOf(period.getValue()).equals(value)) {
                    periodEnumList.add(period);
                    break;
                }
            }
        }
        return periodEnumList;
    }


}
