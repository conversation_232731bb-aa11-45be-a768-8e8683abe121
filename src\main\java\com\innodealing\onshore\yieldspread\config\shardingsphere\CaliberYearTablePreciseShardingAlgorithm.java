package com.innodealing.onshore.yieldspread.config.shardingsphere;

import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.helper.ShardingHindStrParamUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.sql.Date;
import java.util.Collection;

/**
 * 自定义Precise分片算法,支持线程本地内存传递分片参数
 *
 * <AUTHOR>
 * @date 2024/6/12 9:58
 **/
public class CaliberYearTablePreciseShardingAlgorithm implements PreciseShardingAlgorithm<Long> {
    public String doSharding(Collection<String> collection, PreciseShardingValue<Long> preciseShardingValue) {
        Long pk = preciseShardingValue.getValue();
        String tableName = preciseShardingValue.getLogicTableName();
        String shardingHindStrParam = ShardingHindStrParamUtil.getHindStrParam();
        if (StringUtils.isNotBlank(shardingHindStrParam)) {
            tableName = String.format("%s_%s", tableName, shardingHindStrParam);
        }
        Date pkDate = ShardingUtils.getPkDate(pk);
        return String.format("%s_%tY", tableName, pkDate);
    }
}
