package com.innodealing.onshore.yieldspread.dao.pgshard.partition;

import com.github.wz2cool.dynamic.BaseFilterDescriptor;
import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.BankShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.BankYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.BankShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 银行债券利差曲线分片数据源
 *
 * <AUTHOR>
 */
@Repository
public class BankBondShardYieldSpreadCurveRepository {

    @Resource
    private BankShardBondYieldSpreadCurveMapper bankShardBondYieldSpreadCurveMapper;

    /**
     * 银行债券利差分片查询
     *
     * @param params 查询参数
     * @param router 路由
     * @return 分片表结果集
     */
    public List<BankShardBondYieldSpreadCurveDO> listBankYieldSpreads(BankYieldSearchParam params, AbstractRatingRouter router) {
        BaseFilterDescriptor<BankShardBondYieldSpreadCurveDO>[] baseFilterDescriptors = this.listFilters(params).getFilters();
        DynamicQuery<BankShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(BankShardBondYieldSpreadCurveDO.class)
                .and(baseFilterDescriptors);
        return bankShardBondYieldSpreadCurveMapper.selectByDynamicQueryRouter(query, router);
    }

    private FilterGroupDescriptor<BankShardBondYieldSpreadCurveDO> listFilters(BankYieldSearchParam params) {
        return FilterGroupDescriptor.create(BankShardBondYieldSpreadCurveDO.class)
                .and(nonNull(params.getSpreadBondType()), BankShardBondYieldSpreadCurveDO::getBankSeniorityRanking, isEqual(params.getSpreadBondType()))
                .and(isNull(params.getSpreadBondType()), BankShardBondYieldSpreadCurveDO::getUsingBankSeniorityRanking, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(CollectionUtils.isNotEmpty(params.getBankTypes()), BankShardBondYieldSpreadCurveDO::getBankType, in(params.getBankTypes()))
                .and(CollectionUtils.isEmpty(params.getBankTypes()), BankShardBondYieldSpreadCurveDO::getUsingBankType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(params.getRemainingTenor()), BankShardBondYieldSpreadCurveDO::getSpreadRemainingTenorTag, isEqual(params.getRemainingTenor()))
                .and(isNull(params.getRemainingTenor()), BankShardBondYieldSpreadCurveDO::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                ;
    }

}