package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgLgBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadDO;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 地方债利差 DAO
 *
 * <AUTHOR>
 * @create: 2024-10-25
 */
@Repository
public class PgLgBondYieldSpreadDAO {

    @Resource
    private PgLgBondYieldSpreadMapper pgLgBondYieldSpreadMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 批量更新
     *
     * @param pgLgBondYieldSpreadDOList   地方债利差列表
     * @param spreadDate                  利差日期
     * @return 受影响的行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int savePgLgBondYieldSpreadDOList(Date spreadDate, List<PgLgBondYieldSpreadDO> pgLgBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(pgLgBondYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> bondUniCodes = pgLgBondYieldSpreadDOList.stream().map(PgLgBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<PgLgBondYieldSpreadDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadDO.class)
                .select(PgLgBondYieldSpreadDO::getId, PgLgBondYieldSpreadDO::getBondUniCode, PgLgBondYieldSpreadDO::getSpreadDate)
                .and(PgLgBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(PgLgBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<PgLgBondYieldSpreadDO> existDataList = pgLgBondYieldSpreadMapper.selectByDynamicQuery(query);
        Map<String, PgLgBondYieldSpreadDO> existPgLgBondYieldSpreadDO = existDataList.stream().collect(Collectors.toMap(x ->
                        String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                Function.identity(), (x1, x2) -> x2));
        MapperBatchAction<PgLgBondYieldSpreadMapper> batchAction = MapperBatchAction.
                create(PgLgBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.MIN_BATCH_SIZE);
        for (PgLgBondYieldSpreadDO pgLgBondYieldSpreadDO : pgLgBondYieldSpreadDOList) {
            PgLgBondYieldSpreadDO existData = existPgLgBondYieldSpreadDO.get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                    pgLgBondYieldSpreadDO.getBondUniCode(),
                    pgLgBondYieldSpreadDO.getSpreadDate().getTime()));
            batchAction.addAction(mapper -> {
                if (Objects.nonNull(existData)) {
                    pgLgBondYieldSpreadDO.setId(existData.getId());
                    mapper.updateByPrimaryKey(pgLgBondYieldSpreadDO);
                } else {
                    mapper.insert(pgLgBondYieldSpreadDO);
                }
            });
        }
        List<BatchResult> batchResults = batchAction.doBatchActionWithResults();
        return batchResults.stream().mapToInt(x -> {
            int effectRows = Arrays.stream(x.getUpdateCounts()).sum();
            if (effectRows > 0) {
                return effectRows;
            } else {
                return x.getParameterObjects().size();
            }
        }).sum();
    }
}
