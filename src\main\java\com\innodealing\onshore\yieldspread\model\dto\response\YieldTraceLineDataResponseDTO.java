package com.innodealing.onshore.yieldspread.model.dto.response;


import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 利差追踪折线图
 *
 * <AUTHOR>
 */
public class YieldTraceLineDataResponseDTO {

    @ApiModelProperty("类型code")
    private Integer typeCode;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("数据值")
    private List<String> yields;

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public List<String> getYields() {
        return yields;
    }

    public void setYields(List<String> yields) {
        this.yields = Objects.isNull(yields) ? new ArrayList<>() : new ArrayList<>(yields);
    }
}
