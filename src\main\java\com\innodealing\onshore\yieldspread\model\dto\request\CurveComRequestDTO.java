package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 主体利差请求参数
 *
 * <AUTHOR>
 */
public class CurveComRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("利差债券类型、求偿顺序 0:私募债(产业、城投)|普通债(银行、证券), 1:公募债(产业、城投)|次级债(银行、证券), 2:永续债(产业、城投、银行、证券)")
    private Integer spreadBondRanking;
    @ApiModelProperty("主体唯一编码")
    @NotNull(message = "主体唯一编码不能为空")
    private Long comUniCode;
    @ApiModelProperty("曲线名称(导出时前端出入)")
    private String curveName;
    @ApiModelProperty("曲线类型 1:产业 2:城投 3:银行 4:证券 5:自定义")
    @NotNull(message = "曲线类型不能为空")
    private Integer curveType;

    public Integer getSpreadBondRanking() {
        return spreadBondRanking;
    }

    public void setSpreadBondRanking(Integer spreadBondRanking) {
        this.spreadBondRanking = spreadBondRanking;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

    public Integer getCurveType() {
        return curveType;
    }

    public void setCurveType(Integer curveType) {
        this.curveType = curveType;
    }
}
