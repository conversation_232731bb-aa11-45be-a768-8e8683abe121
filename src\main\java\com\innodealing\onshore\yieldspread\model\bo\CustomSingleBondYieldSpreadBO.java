package com.innodealing.onshore.yieldspread.model.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 自定义曲线单券利差
 *
 * <AUTHOR>
 */
public class CustomSingleBondYieldSpreadBO {

    @ApiModelProperty("债券code")
    private Long bondUniCode;

    @ApiModelProperty("利差日期")
    private Date spreadDate;

    @ApiModelProperty("剩余期限")
    private String remainingTenor;

    @ApiModelProperty("票面利率(%)")
    private BigDecimal latestCouponRate;

    @ApiModelProperty("剩余规模(亿)")
    private BigDecimal bondBalance;

    @ApiModelProperty("主体外部评级映射")
    private Integer comExtRatingMapping;

    @ApiModelProperty("债券外部评级映射")
    private Integer bondExtRatingMapping;

    @ApiModelProperty("隐含评级")
    private Integer bondImpliedRatingMapping;

    @ApiModelProperty("债券信用利差;单位(BP)")
    private BigDecimal bondCreditSpread;

    @ApiModelProperty("债券超额利差;单位(BP)")
    private BigDecimal bondExcessSpread;

    @ApiModelProperty("中债估值;单位(%)")
    private BigDecimal cbYield;

    @ApiModelProperty("国开收益率;单位(%)")
    private BigDecimal cdbLerpYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer getBondImpliedRatingMapping() {
        return bondImpliedRatingMapping;
    }

    public void setBondImpliedRatingMapping(Integer bondImpliedRatingMapping) {
        this.bondImpliedRatingMapping = bondImpliedRatingMapping;
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(BigDecimal cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

}
