package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgBondYieldSpreadAreaViewDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 地方债区域利差视图 Mapper
 *
 * <AUTHOR>
 * @create: 2024-11-04
 */
public interface PgLgBondYieldSpreadAreaViewMapper extends DynamicQueryMapper<PgLgBondYieldSpreadAreaViewDO> {

    /**
     * 创建地方债利差区域视图
     *
     * @param lgMvName   地方债物化视图
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param comUniCode 区域code 为null,则默认按照所有地区code建立物化食物
     */
    void createLgBondYieldSpreadAreaView(@Param("lgMvName") String lgMvName, @Param("startDate") String startDate,
                                         @Param("endDate") String endDate, @Param("comUniCode") Long comUniCode);

    /**
     * 删除视图
     *
     * @param tableName 视图名称
     * @return boolean
     */
    @Update("DROP MATERIALIZED VIEW IF EXISTS ${tableName} cascade;")
    Boolean dropMv(@Param("tableName") String tableName);

    /**
     * 按照利差日期查询地方债区域利差数据
     *
     * @param lgMvName   视图名称
     * @param spreadDate 利差日期
     * @return 视图数据
     */
    List<PgLgBondYieldSpreadAreaViewDO> listLgBondYieldSpreadAreaView(@Param("lgMvName") String lgMvName,
                                                                      @Param("spreadDate") String spreadDate);
}
