package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.service.BondYieldPanoramaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.Date;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (内部)收益率全景控制器
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 收益率全景-v1版本")
@RestController
@RequestMapping("internal/v1/bond/yield/panorama")
public class InternalBondYieldPanoramaV1Controller {
    @Resource
    private BondYieldPanoramaService bondYieldPanoramaService;


    @ApiOperation(value = "按曲线代码同步同步收益率全景(历史)")
    @PostMapping("/sync/by-curve-codes")
    public int syncBondYieldPanoramaByCurveCodes(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "curveCodes", value = "曲线代码列表，逗号分隔，为空表示所有曲线") @RequestParam(required = false) String curveCodes) {

        List<Integer> curveCodeList = null;
        if (curveCodes != null && !curveCodes.trim().isEmpty()) {
            curveCodeList = Arrays.stream(curveCodes.split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        return bondYieldPanoramaService.syncBondYieldSpreadPanoramaByCurveCodes(startDate, endDate, curveCodeList);
    }
}
