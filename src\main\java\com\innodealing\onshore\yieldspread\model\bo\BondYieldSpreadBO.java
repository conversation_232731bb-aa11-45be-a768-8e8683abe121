package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 债券利差
 *
 * <AUTHOR>
 */
public class BondYieldSpreadBO {

    /**
     * 利差日期
     */
    private Date spreadDate;

    /**
     * 信用利差中位数;单位(BP)
     */
    private BigDecimal bondCreditSpread;

    /**
     * 超额利差中位数;单位(BP)
     */
    private BigDecimal bondExcessSpread;

    /**
     * 中债收益率中位数;单位(BP)
     */
    private BigDecimal cbYield;

    /**
     * 信用利差平均数;单位(BP)
     */
    private BigDecimal avgBondCreditSpread;

    /**
     * 超额利差平均数;单位(BP)
     */
    private BigDecimal avgBondExcessSpread;

    /**
     * 中债收益率平均数;单位(BP)
     */
    private BigDecimal avgCbYield;

    /**
     * 信用利差不为空的债券样本数量
     */
    private Integer bondCreditSpreadCount;

    /**
     * 超额利差不为空的债券样本数量
     */
    private Integer bondExcessSpreadCount;

    /**
     * 中债收益率不为空的债券样本数量
     */
    private Integer cbYieldCount;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getCbYieldCount() {
        return cbYieldCount;
    }

    public void setCbYieldCount(Integer cbYieldCount) {
        this.cbYieldCount = cbYieldCount;
    }

    @Override
    public String toString() {
        return "BondYieldSpreadBO{" +
                "spreadDate=" + spreadDate +
                ", bondCreditSpread=" + bondCreditSpread +
                ", bondExcessSpread=" + bondExcessSpread +
                ", cbYield=" + cbYield +
                ", avgBondCreditSpread=" + avgBondCreditSpread +
                ", avgBondExcessSpread=" + avgBondExcessSpread +
                ", avgCbYield=" + avgCbYield +
                ", bondCreditSpreadCount=" + bondCreditSpreadCount +
                ", bondExcessSpreadCount=" + bondExcessSpreadCount +
                ", cbYieldCount=" + cbYieldCount +
                '}';
    }

}
