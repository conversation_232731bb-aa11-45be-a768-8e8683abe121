package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.BusinessFilterNatureEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.enums.SpreadComYyRatingMappingTagEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadInduBondImpliedRatingMappingTagEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 行业曲线生成债券筛选条件
 *
 * <AUTHOR>
 */
public class InduCurveGenerateConditionReqDTO extends AbstractInduAndUdicPartCommonConditionReqDTO {

    @ApiModelProperty("行业编码")
    private Long industryCode;

    @ApiModelProperty("行业名称")
    private String industryName;

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.BusinessFilterNatureEnum
     */
    @ApiModelProperty("企业性质 1:央企, 2:国企, 3:民企")
    protected List<Integer> businessFilterNatures;

    private static final String CURVE_NAME_PREFIX = "产业债";

    @Override
    public String getCurveName() {
        String prefix;
        if (Objects.isNull(industryCode) || Objects.isNull(industryName)) {
            prefix = CURVE_NAME_PREFIX;
        } else {
            prefix = "银行".equals(industryName) ? ("产业," + industryName) : industryName;
        }
        return prefix +
                super.jointShortName() +
                super.jointSpreadBondTypeName() +
                super.jointGuaranteeStatusName() +
                super.jointExtRatingName() +
                super.jointBondImpliedRatingName(SpreadInduBondImpliedRatingMappingTagEnum.class) +
                this.jointYyRatingName() +
                this.jointBusinessFilterNaturesName() +
                super.jointRemainingTenorName();
    }

    private String jointYyRatingName() {
        if (ArrayUtils.isEmpty(this.comYyRatingMappings)) {
            return "";
        }
        SpreadComYyRatingMappingTagEnum comYyRatingMappingEnum = SpreadComYyRatingMappingTagEnum
                .getComYyRatingEnum(comYyRatingMappings[0]).orElseThrow(() -> new TipsException("YY评级不正确"));
        Integer[] mapping = comYyRatingMappingEnum.getMapping();
        Arrays.sort(comYyRatingMappings);
        boolean isSame = Arrays.equals(mapping, comYyRatingMappings);
        StringBuilder sb = new StringBuilder(SEPARATOR).append("YY");
        if (isSame) {
            return sb.append(comYyRatingMappingEnum.getText()).toString();
        } else {
            for (Integer comYyRatingMapping : comYyRatingMappings) {
                sb.append(comYyRatingMapping).append(OBLIQUE_LINE);
            }
            return sb.substring(0, sb.length() - 1);
        }
    }

    private String jointBusinessFilterNaturesName() {
        if (CollectionUtils.isEmpty(this.businessFilterNatures)) {
            return "";
        }
        StringBuilder sb = new StringBuilder(SEPARATOR);
        for (Integer businessFilterNature : this.businessFilterNatures) {
            sb.append(EnumUtils.getEnum(BusinessFilterNatureEnum.class, businessFilterNature).getText()).append(OBLIQUE_LINE);
        }
        return sb.substring(0, sb.length() - 1);
    }

    public Long getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(Long industryCode) {
        this.industryCode = industryCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public List<Integer> getBusinessFilterNatures() {
        return Objects.isNull(businessFilterNatures) ? new ArrayList<>() : new ArrayList<>(businessFilterNatures);
    }

    public void setBusinessFilterNatures(List<Integer> businessFilterNatures) {
        this.businessFilterNatures = Objects.isNull(businessFilterNatures) ? new ArrayList<>() : new ArrayList<>(businessFilterNatures);
    }

}
