package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 利差追踪折线图(同评级)
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class YieldSpreadTraceLineChartRatingResponseDTO {

    @ApiModelProperty("日期")
    private List<Date> issueDates;

    @ApiModelProperty("1M")
    private List<String> ytm1Ms;

    @ApiModelProperty("3M")
    private List<String> ytm3Ms;

    @ApiModelProperty("6M")
    private List<String> ytm6Ms;

    @ApiModelProperty("9M")
    private List<String> ytm9Ms;

    @ApiModelProperty("1Y")
    private List<String> ytm1Ys;

    @ApiModelProperty("2Y")
    private List<String> ytm2Ys;

    @ApiModelProperty("3Y")
    private List<String> ytm3Ys;

    @ApiModelProperty("5Y")
    private List<String> ytm5Ys;

    @ApiModelProperty("7Y")
    private List<String> ytm7Ys;

    @ApiModelProperty("10Y")
    private List<String> ytm10Ys;
    @ApiModelProperty("15Y")
    private List<String> ytm15Ys;
    @ApiModelProperty("20Y")
    private List<String> ytm20Ys;
    @ApiModelProperty("30Y")
    private List<String> ytm30Ys;
    @ApiModelProperty("50Y")
    private List<String> ytm50Ys;

    public List<Date> getIssueDates() {
        return issueDates;
    }

    public void setIssueDates(List<Date> issueDates) {
        this.issueDates = Objects.isNull(issueDates) ? new ArrayList<>() : new ArrayList<>(issueDates);
    }

    public List<String> getYtm1Ys() {
        return ytm1Ys;
    }

    public void setYtm1Ys(List<String> ytm1Ys) {
        this.ytm1Ys = Objects.isNull(ytm1Ys) ? new ArrayList<>() : new ArrayList<>(ytm1Ys);
    }

    public List<String> getYtm2Ys() {
        return ytm2Ys;
    }

    public void setYtm2Ys(List<String> ytm2Ys) {
        this.ytm2Ys = Objects.isNull(ytm2Ys) ? new ArrayList<>() : new ArrayList<>(ytm2Ys);
    }

    public List<String> getYtm3Ys() {
        return ytm3Ys;
    }

    public void setYtm3Ys(List<String> ytm3Ys) {
        this.ytm3Ys = Objects.isNull(ytm3Ys) ? new ArrayList<>() : new ArrayList<>(ytm3Ys);
    }

    public List<String> getYtm5Ys() {
        return ytm5Ys;
    }

    public void setYtm5Ys(List<String> ytm5Ys) {
        this.ytm5Ys = Objects.isNull(ytm5Ys) ? new ArrayList<>() : new ArrayList<>(ytm5Ys);
    }

    public List<String> getYtm7Ys() {
        return ytm7Ys;
    }

    public void setYtm7Ys(List<String> ytm7Ys) {
        this.ytm7Ys = Objects.isNull(ytm7Ys) ? new ArrayList<>() : new ArrayList<>(ytm7Ys);
    }

    public List<String> getYtm1Ms() {
        return ytm1Ms;
    }

    public void setYtm1Ms(List<String> ytm1Ms) {
        this.ytm1Ms = Objects.isNull(ytm1Ms) ? new ArrayList<>() : new ArrayList<>(ytm1Ms);
    }

    public List<String> getYtm3Ms() {
        return ytm3Ms;
    }

    public void setYtm3Ms(List<String> ytm3Ms) {
        this.ytm3Ms = Objects.isNull(ytm3Ms) ? new ArrayList<>() : new ArrayList<>(ytm3Ms);
    }

    public List<String> getYtm6Ms() {
        return ytm6Ms;
    }

    public void setYtm6Ms(List<String> ytm6Ms) {
        this.ytm6Ms = Objects.isNull(ytm6Ms) ? new ArrayList<>() : new ArrayList<>(ytm6Ms);
    }

    public List<String> getYtm9Ms() {
        return ytm9Ms;
    }

    public void setYtm9Ms(List<String> ytm9Ms) {
        this.ytm9Ms = Objects.isNull(ytm9Ms) ? new ArrayList<>() : new ArrayList<>(ytm9Ms);
    }

    public List<String> getYtm10Ys() {
        return ytm10Ys;
    }

    public void setYtm10Ys(List<String> ytm10Ys) {
        this.ytm10Ys = Objects.isNull(ytm10Ys) ? new ArrayList<>() : new ArrayList<>(ytm10Ys);
    }

    public List<String> getYtm15Ys() {
        return ytm15Ys;
    }

    public void setYtm15Ys(List<String> ytm15Ys) {
        this.ytm15Ys = Objects.isNull(ytm15Ys) ? new ArrayList<>() : new ArrayList<>(ytm15Ys);
    }

    public List<String> getYtm20Ys() {
        return ytm20Ys;
    }

    public void setYtm20Ys(List<String> ytm20Ys) {
        this.ytm20Ys = Objects.isNull(ytm20Ys) ? new ArrayList<>() : new ArrayList<>(ytm20Ys);
    }

    public List<String> getYtm30Ys() {
        return ytm30Ys;
    }

    public void setYtm30Ys(List<String> ytm30Ys) {
        this.ytm30Ys = Objects.isNull(ytm30Ys) ? new ArrayList<>() : new ArrayList<>(ytm30Ys);
    }

    public List<String> getYtm50Ys() {
        return ytm50Ys;
    }

    public void setYtm50Ys(List<String> ytm50Ys) {
        this.ytm50Ys = Objects.isNull(ytm50Ys) ? new ArrayList<>() : new ArrayList<>(ytm50Ys);
    }
}
