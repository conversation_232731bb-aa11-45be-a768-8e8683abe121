package com.innodealing.onshore.yieldspread.config.redisson;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * RedissonClientProperties
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "spring.redis")
public class RedissonClientProperties {
    /**
     * 地址
     */
    private String host;
    /**
     * 端口
     */
    private Integer port;
    /**
     * 数据库
     */
    private Integer database;
    /**
     * 密码
     */
    private String password;

    public String getHost() {
        return host;
    }
    public void setHost(String host) {
        this.host = host;
    }
    public Integer getPort() {
        return port;
    }
    public void setPort(Integer port) {
        this.port = port;
    }
    public Integer getDatabase() {
        return database;
    }
    public void setDatabase(Integer database) {
        this.database = database;
    }
    public String getPassword() {
        return password;
    }
    public void setPassword(String password) {
        this.password = password;
    }
}
