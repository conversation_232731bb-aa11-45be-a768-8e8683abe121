package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 利差债券
 *
 * <AUTHOR>
 */
@Table(name = "yield_spread_bond")
public class YieldSpreadBondDO {

    @Id
    @Column
    private Long id;

    @Column
    private Long comUniCode;

    @Column
    private Long bondUniCode;

    @Column
    private String bondCode;

    /**
     * 利差债券主体类型 1 产业 2 城投 3 银行 4 证券 7 保险
     */
    @Column
    private Integer comSpreadSector;

    /**
     * 是否删除
     */
    @Column
    private Integer deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public Integer getComSpreadSector() {
        return comSpreadSector;
    }

    public void setComSpreadSector(Integer comSpreadSector) {
        this.comSpreadSector = comSpreadSector;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

}
