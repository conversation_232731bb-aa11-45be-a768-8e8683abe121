package com.innodealing.onshore.yieldspread.model.dto;

import com.innodealing.onshore.yieldspread.model.bo.YieldLgDataBO;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 地方债区域利差获取数据
 *
 * <AUTHOR>
 * @date 2024/5/7 10:35
 **/
public class PgBondSpreadLgDTO {
    /**
     * 利差追踪数据
     */
    private List<YieldLgDataBO> yieldLgDataBOList;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    public List<YieldLgDataBO> getYieldLgDataBOList() {
        return Objects.isNull(yieldLgDataBOList) ? new ArrayList<>() : new ArrayList<>(yieldLgDataBOList);
    }

    public void setYieldLgDataBOList(List<YieldLgDataBO> yieldLgDataBOList) {
        this.yieldLgDataBOList = Objects.isNull(yieldLgDataBOList) ? new ArrayList<>() : new ArrayList<>(yieldLgDataBOList);
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public Date getEndDate() {
        return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public void setEndDate(Date endDate) {
        this.endDate = Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }
}
