package com.innodealing.onshore.yieldspread.dao.dmdc;

import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.innodealing.onshore.yieldspread.mapper.dmdc.BondInterestCtzMapper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.BondInterestCtzDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.group.BondInterestCtzGroupDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;
import java.util.Set;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 城投债利差(老表) DAO
 *
 * <AUTHOR>
 **/
@Repository
public class BondInterestCtzDAO {

    @Resource
    private BondInterestCtzMapper bondInterestCtzMapper;

    /**
     * 城投债数据
     *
     * @param interestDate 利差日期
     * @param bondUniCodes 债券code
     * @return 城投债数据
     */
    public List<BondInterestCtzDO> listBondInterestCtzDOByInterestDate(Date interestDate, Set<Long> bondUniCodes) {
        GroupedQuery<BondInterestCtzGroupDO, BondInterestCtzDO> groupedQuery =
                GroupByQuery.createQuery(BondInterestCtzGroupDO.class, BondInterestCtzDO.class)
                        .select(BondInterestCtzDO::getId, BondInterestCtzDO::getBondUniCode,
                                BondInterestCtzDO::getComUniCode, BondInterestCtzDO::getBondCode,
                                BondInterestCtzDO::getInterestDate, BondInterestCtzDO::getAreaUniCode1,
                                BondInterestCtzDO::getAreaName1, BondInterestCtzDO::getAreaUniCode2,
                                BondInterestCtzDO::getAreaName2, BondInterestCtzDO::getAreaLevelId)
                        .and(BondInterestCtzGroupDO::getInterestDate, isEqual(interestDate))
                        .and(CollectionUtils.isNotEmpty(bondUniCodes), BondInterestCtzGroupDO::getBondUniCode, in(bondUniCodes))
                        .groupBy(BondInterestCtzGroupDO::getBondUniCode, BondInterestCtzGroupDO::getInterestDate);
        return bondInterestCtzMapper.selectByGroupedQuery(groupedQuery);
    }
}
