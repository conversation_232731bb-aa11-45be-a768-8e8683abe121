package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 城投利差-导出成分数据请求参数
 *
 * <AUTHOR>
 */
public class UdicListExportRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("组合曲线查询集合")
    private List<UdicCurveCompositionConditionDTO> compositionConditions;
    @ApiModelProperty(value = "利差日期", example = "2020-04-07")
    protected Date spreadDate;
    @ApiModelProperty("排序")
    protected SortDTO sort;

    public List<UdicCurveCompositionConditionDTO> getCompositionConditions() {
        return Objects.isNull(compositionConditions) ? new ArrayList<>() : new ArrayList<>(compositionConditions);
    }

    public void setCompositionConditions(List<UdicCurveCompositionConditionDTO> compositionConditions) {
        this.compositionConditions = Objects.isNull(compositionConditions) ? new ArrayList<>() : new ArrayList<>(compositionConditions);
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }
}
