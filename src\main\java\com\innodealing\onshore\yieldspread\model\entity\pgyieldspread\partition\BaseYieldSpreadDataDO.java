package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * 利差数据DO基类
 *
 * <AUTHOR>
 */
public class BaseYieldSpreadDataDO {

    /**
     * 信用利差;单位(BP)
     */
    @Column
    private Long bondCreditSpread;

    /**
     * 超额利差;单位(BP)
     */
    @Column
    private Long bondExcessSpread;

    /**
     * 中债收益率;单位(BP)
     */
    @Column
    private Long cbYield;

    /**
     * 信用利差平均数;单位(BP)
     */
    @Column
    private BigDecimal avgBondCreditSpread;

    /**
     * 超额利差平均数;单位(BP)
     */
    @Column
    private BigDecimal avgBondExcessSpread;

    /**
     * 中债收益率平均数;单位(BP)
     */
    @Column
    private BigDecimal avgCbYield;

    public Long getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(Long bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public Long getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(Long bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public Long getCbYield() {
        return cbYield;
    }

    public void setCbYield(Long cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }

}
