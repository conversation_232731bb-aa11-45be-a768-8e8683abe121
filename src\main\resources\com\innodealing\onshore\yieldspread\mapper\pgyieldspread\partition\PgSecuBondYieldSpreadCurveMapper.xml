<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgSecuBondYieldSpreadCurveMapper">

    <update id="createTableRatingRouter">
        CREATE TABLE "yield_spread".${parameter.tableName}
        (
        "id" int8,

        "bond_credit_spread" int8,
        "bond_excess_spread" int8,
        "cb_yield" int8,

        "avg_bond_credit_spread" int8,
        "avg_bond_excess_spread" int8,
        "avg_cb_yield" int8,

        "bond_credit_spread_count" int8,
        "bond_excess_spread_count" int8,
        "cb_yield_count" int8,

        "spread_date" date NOT NULL,
        "security_seniority_ranking" int2,
        "spread_remaining_tenor_tag" int2,

        "using_security_seniority_ranking" int2,
        "using_spread_remaining_tenor_tag" int2
        )
        DISTRIBUTED BY (spread_date)
        PARTITION BY RANGE (spread_date)
        ( START (date '2019-01-01') INCLUSIVE
        END (date '2026-01-01') EXCLUSIVE
        EVERY (INTERVAL '1 year') );
        <include refid="createIndex"/>
    </update>



    <sql id="createIndex">
        create index ${parameter.tableName}_idx_spread_date
        on yield_spread.${parameter.tableName} (spread_date);
        create index ${parameter.tableName}_idx_security_seniority_ranking
        on yield_spread.${parameter.tableName} (security_seniority_ranking);
        create index ${parameter.tableName}_idx_spread_remaining_tenor_tag
        on yield_spread.${parameter.tableName} (spread_remaining_tenor_tag);
    </sql>

    <update id="syncCurveIncrFromMV">
        INSERT INTO yield_spread.${tableName}
        SELECT *
        from yield_spread.${mvTableName};
    </update>
</mapper>