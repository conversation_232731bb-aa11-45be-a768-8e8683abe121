package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 担保状态枚举
 *
 * <AUTHOR>
 */
public enum GuaranteedStatusEnum implements ITextValueEnum {
    /**
     * 无担保
     */
    NO_GUARANTEE(0, "无", "无担保"),
    /**
     * 有担保
     */
    HAS_GUARANTEE(1, "有", "有担保");

    private final Integer code;

    private final String text;

    private final String desc;

    GuaranteedStatusEnum(Integer code, String text, String desc) {
        this.code = code;
        this.text = text;
        this.desc = desc;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
