package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 银行主体利差
 *
 * <AUTHOR>
 */
public class SecuComYieldSpreadResDTO extends BaseFinanceComYieldSpreadResDTO {

    @ApiModelProperty("资本杠杆率(%)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal capitalLeverageRatio;

    @ApiModelProperty("主体信用利差(二级资本);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comSubordinatedCreditSpread;

    @ApiModelProperty("主体超额利差(二级资本);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comSubordinatedExcessSpread;

    @ApiModelProperty("主体估值收益率(二级资本);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comSubordinatedCbYield;

    public BigDecimal getCapitalLeverageRatio() {
        return capitalLeverageRatio;
    }

    public void setCapitalLeverageRatio(BigDecimal capitalLeverageRatio) {
        this.capitalLeverageRatio = capitalLeverageRatio;
    }

    public BigDecimal getComSubordinatedCreditSpread() {
        return comSubordinatedCreditSpread;
    }

    public void setComSubordinatedCreditSpread(BigDecimal comSubordinatedCreditSpread) {
        this.comSubordinatedCreditSpread = comSubordinatedCreditSpread;
    }

    public BigDecimal getComSubordinatedExcessSpread() {
        return comSubordinatedExcessSpread;
    }

    public void setComSubordinatedExcessSpread(BigDecimal comSubordinatedExcessSpread) {
        this.comSubordinatedExcessSpread = comSubordinatedExcessSpread;
    }

    public BigDecimal getComSubordinatedCbYield() {
        return comSubordinatedCbYield;
    }

    public void setComSubordinatedCbYield(BigDecimal comSubordinatedCbYield) {
        this.comSubordinatedCbYield = comSubordinatedCbYield;
    }

}
