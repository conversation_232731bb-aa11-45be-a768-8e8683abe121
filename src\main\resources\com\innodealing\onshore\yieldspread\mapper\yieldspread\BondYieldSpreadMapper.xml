<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.yieldspread.BondYieldSpreadMapper">
    <update id="createShardingTable" parameterType="String">
        CREATE TABLE IF NOT EXISTS ${tableName}
        (
            `id`                    bigint(20) UNSIGNED NOT NULL COMMENT '主键id',
            `com_uni_code`          bigint(20)          NULL COMMENT '发行人代码',
            `bond_uni_code`         bigint(20)          NOT NULL COMMENT '债券统一编码',
            `bond_code`             varchar(64)         NULL COMMENT '债券编码',
            `spread_date`           date                NOT NULL COMMENT '利差日期',
            `remaining_tenor`       varchar(20)         NULL COMMENT '剩余期限',
            `remaining_tenor_day`   int                 NULL COMMENT '剩余期限天数',
            `bond_credit_spread`    decimal(12, 4)      NULL COMMENT '债券信用利差',
            `bond_excess_spread`    decimal(12, 4)      NULL COMMENT '债券超额利差',
            `create_time`           datetime(3)         DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
            `update_time`           datetime(3)         DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
            PRIMARY KEY (`id`),
            INDEX `idx_bond_uni_code_spread_date_yield_spread` (`bond_uni_code`, `spread_date`, `bond_credit_spread`, `bond_excess_spread`),
            INDEX `idx_spread_date_bond_uni_code_yield_spread` (`spread_date`, `bond_uni_code`, `bond_credit_spread`, `bond_excess_spread`)
            ) COMMENT ='债券利差';
    </update>
</mapper>