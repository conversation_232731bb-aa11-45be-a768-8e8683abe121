package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.dto.cbpermission.CBPermissionResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.cbpermission.CBValuationPermissionTodayResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.user.UserBrokersAuthDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 授权服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "onshore-user-service", url = "${user.service.api.url}", path = "/internal")
public interface UserService {

    /**
     * 获取用户经纪商权限, 该方法可能会返回null，所以进行了二次封装
     *
     * @param userId 用户id
     * @return 用户经纪商权限
     */
    @GetMapping("/user/broker/auth")
    @Nullable
    UserBrokersAuthDTO getUserBrokersAuthNullable(@RequestParam Long userId);

    /**
     * 判断用户是否有CFETS权限
     *
     * @param userId 用户id
     * @return true：有  false：没有
     */
    @GetMapping("/permission/has-cfets/resources")
    boolean hasCfetsResourcePermission(@RequestParam Long userId);

    /**
     * 获取用户经纪商权限
     *
     * @param userId 用户id
     * @return 用户经纪商权限
     */
    @Nonnull
    default UserBrokersAuthDTO getUserBrokersAuth(Long userId) {
        UserBrokersAuthDTO userBrokersAuthDTO = this.getUserBrokersAuthNullable(userId);
        if (Objects.isNull(userBrokersAuthDTO)) {
            return new UserBrokersAuthDTO();
        }
        return userBrokersAuthDTO;
    }

    /**
     * 是否有中债权限
     *
     * @param userId 用户id
     * @return 中债权限
     */
    default boolean hasCbPermission(Long userId) {
        return this.getUserBrokersAuth(userId).isCbAuthFlag();
    }

    /**
     * 是否有中证权限
     *
     * @param userId 用户id
     * @return 中证权限
     */
    default boolean hasCsPermission(Long userId) {
        return this.getUserBrokersAuth(userId).isCsAuthFlag();
    }

    /**
     * 获取中债当年和历史分割的时间点
     *
     * @return date
     */
    @GetMapping("/cb/permission/current-year")
    Date getCbPermissionDividingDate();

    /**
     * 中债权限
     *
     * @param userid 用户id
     * @return List<CBPermissionResponseDTO>
     */
    @GetMapping("/cb/permission")
    List<CBPermissionResponseDTO> listCbPermissions(@RequestParam Long userid);

    /**
     * 根据用户id和PermissionConstant获取用户中债权限
     *
     * @param userid             用户id
     * @param permissionConstant 权限code常量 PermissionConstant
     * @return boolean
     * @see PermissionConstant
     */
    default boolean hasCbPermission(Long userid, String permissionConstant) {
        List<CBPermissionResponseDTO> cbPermissionResponses = this.listCbPermissions(userid);
        if (CollectionUtils.isEmpty(cbPermissionResponses)) {
            return false;
        }
        Boolean permission = cbPermissionResponses.stream()
                .collect(Collectors.toMap(CBPermissionResponseDTO::getAuthCode, CBPermissionResponseDTO::getHasPermission, (k1, k2) -> k1))
                .get(permissionConstant);
        return Objects.equals(permission, Boolean.TRUE);
    }

    /**
     * 获取债券中债估值权限
     *
     * @param userid       用户id
     * @param bondUniCodes 债券code
     * @return List<CBValuationPermissionTodayResponseDTO>
     */
    @PostMapping("/cb/permission/today")
    List<CBValuationPermissionTodayResponseDTO> listBondTodayCbPermissions(@RequestParam Long userid, @RequestBody List<Long> bondUniCodes);

    /**
     * 根据用户id和债券code获取中债估值权限
     *
     * @param userid       用户id
     * @param bondUniCodes 债券code
     * @return Map<Long, Boolean>
     */
    default Map<Long, Boolean> getBondTodayCbPermissionMap(Long userid, List<Long> bondUniCodes) {
        return this.listBondTodayCbPermissions(userid, bondUniCodes).stream()
                .collect(Collectors.toMap(CBValuationPermissionTodayResponseDTO::getBondUniCode, CBValuationPermissionTodayResponseDTO::getHasPermission, (k1, k2) -> k1));
    }

}
