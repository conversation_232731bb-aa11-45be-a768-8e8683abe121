package com.innodealing.onshore.yieldspread.model.dto.response.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.innodealing.international.common.template.source.excel.convert.ExcelDateConvert;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.CURVE_EXPORT_TITLE;

/**
 * 自定义曲线单券利差
 *
 * <AUTHOR>
 */
public class CustomSingleBondYieldSpreadExcelDTO extends BaseCurveYieldSpreadExcelDTO {

    @ApiModelProperty("剩余期限")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "剩余期限"})
    @ColumnWidth(25)
    private String remainingTenor;

    @ApiModelProperty("发行人")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "发行人"})
    @ColumnWidth(25)
    private String comUniName;

    @ApiModelProperty("利差日期")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "利差日期"}, converter = ExcelDateConvert.class)
    @ColumnWidth(25)
    private Date spreadDate;

    @ApiModelProperty("债券简称")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "债券简称"})
    @ColumnWidth(25)
    private String bondShortName;

    @ApiModelProperty("债券代码")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "债券代码"})
    @ColumnWidth(25)
    private String bondCode;

    @ApiModelProperty("票面利率(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "票面利率(%)"})
    @ColumnWidth(25)
    private BigDecimal latestCouponRate;

    @ApiModelProperty("剩余规模(亿)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "剩余规模(亿)"})
    @ColumnWidth(25)
    private BigDecimal bondBalance;

    @ApiModelProperty("主/债评级")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "主/债评级"})
    @ColumnWidth(25)
    private String ratingStr;

    @ApiModelProperty("隐含评级")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "隐含评级"})
    @ColumnWidth(25)
    private String bondImpliedRatingMappingStr;

    @ApiModelProperty("信用利差(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)"})
    @ColumnWidth(25)
    private BigDecimal bondCreditSpread;

    @ApiModelProperty("超额利差(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)"})
    @ColumnWidth(25)
    private BigDecimal bondExcessSpread;

    @ApiModelProperty("估值收益率")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率"})
    @ColumnWidth(25)
    private String cbYieldStr;

    @ApiModelProperty("国开收益率(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "国开收益率(%)"})
    @ColumnWidth(25)
    private String cdbLerpYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public String getRatingStr() {
        return ratingStr;
    }

    public void setRatingStr(String ratingStr) {
        this.ratingStr = ratingStr;
    }

    public String getBondImpliedRatingMappingStr() {
        return bondImpliedRatingMappingStr;
    }

    public void setBondImpliedRatingMappingStr(String bondImpliedRatingMappingStr) {
        this.bondImpliedRatingMappingStr = bondImpliedRatingMappingStr;
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public String getCbYieldStr() {
        return cbYieldStr;
    }

    public void setCbYieldStr(String cbYieldStr) {
        this.cbYieldStr = cbYieldStr;
    }

    public String getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(String cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

}
