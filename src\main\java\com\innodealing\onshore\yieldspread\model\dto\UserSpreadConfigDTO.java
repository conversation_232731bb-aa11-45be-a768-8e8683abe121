package com.innodealing.onshore.yieldspread.model.dto;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户利差配置
 *
 * <AUTHOR>
 * @date 2024/10/17 10:51
 **/
public class UserSpreadConfigDTO {

    /**
     * 配置id
     */
    @ApiModelProperty("配置id")
    private Long tabConfigId;
    /**
     * json 配置
     */
    @ApiModelProperty("配置json 配置")
    private List<Object> configDetails;

    public Long getTabConfigId() {
        return tabConfigId;
    }

    public void setTabConfigId(Long tabConfigId) {
        this.tabConfigId = tabConfigId;
    }

    public List<Object> getConfigDetails() {
        return Objects.isNull(configDetails) ? new ArrayList<>() : new ArrayList<>(configDetails);
    }

    public void setConfigDetails(List<Object> configDetails) {
        this.configDetails = Objects.isNull(configDetails) ? new ArrayList<>() : new ArrayList<>(configDetails);
    }
}
