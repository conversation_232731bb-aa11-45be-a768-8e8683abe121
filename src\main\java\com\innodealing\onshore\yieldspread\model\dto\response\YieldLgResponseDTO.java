package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 地方债区域利差返回
 *
 * <AUTHOR>
 */
public class YieldLgResponseDTO extends YieldStatisticBaseResponseDTO {

    @ApiModelProperty("地方债区域利差数据")
    private List<YieldLgDataResponseDTO> yieldLgDataList;

    public List<YieldLgDataResponseDTO> getYieldLgDataList() {
        return Objects.isNull(yieldLgDataList) ? new ArrayList<>() : new ArrayList<>(yieldLgDataList);
    }

    public void setYieldLgDataList(List<YieldLgDataResponseDTO> yieldLgDataList) {
        this.yieldLgDataList = Objects.isNull(yieldLgDataList) ? new ArrayList<>() : new ArrayList<>(yieldLgDataList);
    }
}
