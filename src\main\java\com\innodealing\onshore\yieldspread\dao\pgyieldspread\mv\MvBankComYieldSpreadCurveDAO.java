package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvBankComYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.MvBankBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvBankComYieldSpreadCurveDO;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.persistence.Table;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;

/**
 * 银行利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvBankComYieldSpreadCurveDAO extends AbstractMvComYieldSpreadCurveDAO<MvBankBondYieldSpreadCurveParameter> {
    private static final String TABLE_NAME = MvBankComYieldSpreadCurveDO.class.getAnnotation(Table.class).name();

    private MvBankComYieldSpreadCurveMapper mvBankComYieldSpreadCurveMapper;

    /**
     * 构造函数
     *
     * @param mvBankComYieldSpreadCurveMapper mapper
     */
    protected MvBankComYieldSpreadCurveDAO(MvBankComYieldSpreadCurveMapper mvBankComYieldSpreadCurveMapper) {
        super(mvBankComYieldSpreadCurveMapper);
        this.mvBankComYieldSpreadCurveMapper = mvBankComYieldSpreadCurveMapper;
    }

    /**
     * 刷新物化视图
     */
    @Override
    public void refresh() {
        mvBankComYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurve(TABLE_NAME);
    }

    @Override
    public List<ComYieldSpreadCurveBO> list(@NonNull Long comUniCode, Integer bankSeniorityRanking) {
        DynamicQuery<MvBankComYieldSpreadCurveDO> query = DynamicQuery.createQuery(MvBankComYieldSpreadCurveDO.class)
                .and(MvBankComYieldSpreadCurveDO::getComUniCode, x -> x.isEqual(comUniCode))
                .and(Objects.nonNull(bankSeniorityRanking), MvBankComYieldSpreadCurveDO::getBankSeniorityRanking, x -> x.isEqual(bankSeniorityRanking))
                .and(Objects.isNull(bankSeniorityRanking), MvBankComYieldSpreadCurveDO::getUsingBankSeniorityRanking, x -> x.isEqual(UNUSED_FIELD_GROUP.getValue()))
                .orderBy(MvBankComYieldSpreadCurveDO::getSpreadDate, SortDirections::asc);
        List<MvBankComYieldSpreadCurveDO> mvBankComYieldSpreadCurveList = mvBankComYieldSpreadCurveMapper.selectByDynamicQuery(query);
        return mvBankComYieldSpreadCurveList.stream().map(super::handlePrecision).collect(Collectors.toList());
    }

    @Override
    protected String tableName() {
        return TABLE_NAME;
    }
}
