package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * 用户曲线
 *
 * <AUTHOR>
 */
@Table(name = "user_curve")
public class UserCurveDO {

    /**
     * 主键id
     */
    @Id
    @Column
    @GeneratedValue(generator = "JDBC", strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column
    private Long userId;

    /**
     * 曲线组id
     */
    @Column
    private Long curveGroupId;

    /**
     * 曲线名称
     */
    @Column
    private String spreadCurveName;

    /**
     * 曲线类型：产业=1，城投=2，银行=3，证券=4，自选债=5，基准曲线=6
     */
    @Column
    private Integer spreadCurveType;

    /**
     * 曲线生成状态：待生成=1，生成中=2，已生成=3，生成失败=4
     */
    @Column
    private Integer generateStatus;

    /**
     * 生成开始时间
     */
    @Column
    private Timestamp generateStartTime;

    /**
     * 生成结束时间
     */
    @Column
    private Timestamp generateEndTime;

    /**
     * 筛选条件对应的json字符串
     */
    @Column
    private String filterCondition;

    /**
     * 用户导入的债券bondUniCode json字符串，只有自选债时才有值
     */
    @Column
    private String importedBond;

    /**
     * 曲线数据，json字符串，只有自选债时才有值
     */
    @Column
    private String spreadCurveData;

    @Column
    private Integer curveOrder;

    /**
     * 是否删除
     */
    @Column
    private Integer deleted;

    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;

    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCurveGroupId() {
        return curveGroupId;
    }

    public void setCurveGroupId(Long curveGroupId) {
        this.curveGroupId = curveGroupId;
    }

    public String getSpreadCurveName() {
        return spreadCurveName;
    }

    public void setSpreadCurveName(String spreadCurveName) {
        this.spreadCurveName = spreadCurveName;
    }

    public Integer getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(Integer spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public Integer getGenerateStatus() {
        return generateStatus;
    }

    public void setGenerateStatus(Integer generateStatus) {
        this.generateStatus = generateStatus;
    }

    public Timestamp getGenerateStartTime() {
        return Objects.isNull(generateStartTime) ? null : new Timestamp(generateStartTime.getTime());
    }

    public void setGenerateStartTime(Timestamp generateStartTime) {
        this.generateStartTime = Objects.isNull(generateStartTime) ? null : new Timestamp(generateStartTime.getTime());
    }

    public Timestamp getGenerateEndTime() {
        return Objects.isNull(generateEndTime) ? null : new Timestamp(generateEndTime.getTime());
    }

    public void setGenerateEndTime(Timestamp generateEndTime) {
        this.generateEndTime = Objects.isNull(generateEndTime) ? null : new Timestamp(generateEndTime.getTime());
    }

    public String getFilterCondition() {
        return filterCondition;
    }

    public void setFilterCondition(String filterCondition) {
        this.filterCondition = filterCondition;
    }

    public String getImportedBond() {
        return importedBond;
    }

    public void setImportedBond(String importedBond) {
        this.importedBond = importedBond;
    }

    public String getSpreadCurveData() {
        return spreadCurveData;
    }

    public void setSpreadCurveData(String spreadCurveData) {
        this.spreadCurveData = spreadCurveData;
    }

    public Integer getCurveOrder() {
        return curveOrder;
    }

    public void setCurveOrder(Integer curveOrder) {
        this.curveOrder = curveOrder;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Timestamp getCreateTime() {
        return Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public Timestamp getUpdateTime() {
        return Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

}
