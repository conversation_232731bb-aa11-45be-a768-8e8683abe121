package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import io.swagger.annotations.ApiModelProperty;

import java.sql.Date;
import java.util.Arrays;
import java.util.Objects;

/**
 * 城投主体/单券利差请求参数
 *
 * <AUTHOR>
 */
public class UdicListRequestDTO extends BaseRequest {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("2. 组合条件查询")
    private UdicCurveCompositionConditionDTO compositionCondition;
    @ApiModelProperty(value = "利差日期", example = "2020-04-07", required = true)
    protected Date spreadDate;
    @ApiModelProperty("排序")
    protected SortDTO sort;
    @ApiModelProperty("主体comUniCode/单券bondUniCode 编码")
    private Long searchCode;
    @ApiModelProperty("编码类型 1主体编码,2债券编码")
    private Integer codeType;
    @ApiModelProperty("发行人唯一编码数组")
    private Long[] comUniCodes;
    @ApiModelProperty("债券唯一编码数组")
    private Long[] bondUniCodes;

    @Override
    public String toString() {
        return "UdicListRequestDTO{" +
                "compositionCondition=" + compositionCondition +
                ", spreadDate=" + spreadDate +
                ", sort=" + sort +
                ", searchCode=" + searchCode +
                ", codeType=" + codeType +
                ", comUniCodes=" + Arrays.toString(comUniCodes) +
                ", bondUniCodes=" + Arrays.toString(bondUniCodes) +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }

    public UdicCurveCompositionConditionDTO getCompositionCondition() {
        return compositionCondition;
    }

    public void setCompositionCondition(UdicCurveCompositionConditionDTO compositionCondition) {
        this.compositionCondition = compositionCondition;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }

    public Long getSearchCode() {
        return searchCode;
    }

    public void setSearchCode(Long searchCode) {
        this.searchCode = searchCode;
    }

    public Integer getCodeType() {
        return codeType;
    }

    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    public Long[] getComUniCodes() {
        return Objects.isNull(comUniCodes) ? new Long[0] : comUniCodes.clone();
    }

    public void setComUniCodes(Long[] comUniCodes) {
        this.comUniCodes = Objects.isNull(comUniCodes) ? new Long[0] : comUniCodes.clone();
    }

    public Long[] getBondUniCodes() {
        return Objects.isNull(bondUniCodes) ? new Long[0] : bondUniCodes.clone();
    }

    public void setBondUniCodes(Long[] bondUniCodes) {
        this.bondUniCodes = Objects.isNull(bondUniCodes) ? new Long[0] : bondUniCodes.clone();
    }
}
