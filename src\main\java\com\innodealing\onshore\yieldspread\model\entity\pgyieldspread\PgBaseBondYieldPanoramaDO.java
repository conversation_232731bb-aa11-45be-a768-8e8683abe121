package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread;

import com.innodealing.onshore.bondmetadata.enums.CurveCode;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Objects;


/**
 * 基础债券收益率全景DO
 *
 * <AUTHOR>
 */
public class PgBaseBondYieldPanoramaDO {
    /**
     *
     */
    @Id
    @Column
    private Long id;

    /**
     * 1 国债, 2 国开债, 3 地方政府债, 4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债,
     * 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单,
     * 14 保险资本补充, 15进出口,16 农发
     */
    @Column
    private Integer bondType;

    /**
     * 收益率曲线编码 1 国债, 4 国开债,
     * 11 中短期票据(AAA), 19 中短期票据(AAA-), 20 中短期票据(AA+), 25 中短期票据(AA), 21 中短期票据(AA-),
     * 10 城投(AAA), 26 城投(AA+), 28 城投(AA), 29 城投(AA(2)),
     * 30 银行普通债(AAA-), 31 银行普通债(AA＋), 32 银行普通债(AA), 33 银行普通债(AA-),
     * 34 银行二级资本债(AAA-), 35 银行二级资本债(AA＋), 36 银行二级资本债(AA), 37 银行二级资本债(AA-),
     * 38 证券公司债(AAA), 39 证券公司债(AAA-), 40 证券公司债(AA＋), 41 证券公司债(AA),
     * 42 同业存单(AAA), 43 同业存单(AAA-), 44 同业存单(AA＋), 45 同业存单(AA), 46 同业存单(AA-),
     * 47 银行永续债(AAA-), 48 银行永续债(AA＋), 49 银行永续债(AA), 50 银行永续债(AA-),
     * 51 地方政府债
     * <p>
     * 100 产业债(AAA), 101 产业债(AAA-), 102 产业债(AA+), 103 产业债(AA),
     * 104 证券次级债, 105 证券永续债
     * 200 保险资本补充(AA+),201 保险资本补充(AA),202 保险资本补充(AA-),203 进出口,204 农发
     *
     * @see CurveCode
     */
    @Column
    private Integer curveCode;

    /**
     * 1月到期收益率
     */
    @Column(name = "ytm_1M")
    private BigDecimal ytm1M;
    /**
     * 3月到期收益率
     */
    @Column(name = "ytm_3M")
    private BigDecimal ytm3M;
    /**
     * 6月到期收益率
     */
    @Column(name = "ytm_6M")
    private BigDecimal ytm6M;
    /**
     * 9月到期收益率
     */
    @Column(name = "ytm_9M")
    private BigDecimal ytm9M;
    /**
     * 1年到期收益率
     */
    @Column(name = "ytm_1Y")
    private BigDecimal ytm1Y;
    /**
     * 2年到期收益率
     */
    @Column(name = "ytm_2Y")
    private BigDecimal ytm2Y;
    /**
     * 3年到期收益率
     */
    @Column(name = "ytm_3Y")
    private BigDecimal ytm3Y;
    /**
     * 4年到期收益率
     */
    @Column(name = "ytm_4Y")
    private BigDecimal ytm4Y;
    /**
     * 5年到期收益率
     */
    @Column(name = "ytm_5Y")
    private BigDecimal ytm5Y;
    /**
     * 6年到期收益率
     */
    @Column(name = "ytm_6Y")
    private BigDecimal ytm6Y;
    /**
     * 7年到期收益率
     */
    @Column(name = "ytm_7Y")
    private BigDecimal ytm7Y;
    /**
     * 10年到期收益率
     */
    @Column(name = "ytm_10Y")
    private BigDecimal ytm10Y;
    /**
     * 15年到期收益率
     */
    @Column(name = "ytm_15Y")
    private BigDecimal ytm15Y;
    /**
     * 20年到期收益率
     */
    @Column(name = "ytm_20Y")
    private BigDecimal ytm20Y;
    /**
     * 30年到期收益率
     */
    @Column(name = "ytm_30Y")
    private BigDecimal ytm30Y;
    /**
     * 50年到期收益率
     */
    @Column(name = "ytm_50Y")
    private BigDecimal ytm50Y;

    /**
     * 发布日期
     */
    @Column
    private Date issueDate;

    /**
     * 刪除状态 (0 未刪除， 1 已刪除)
     */
    @Column
    private Integer deleted;

    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;

    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCurveCode() {
        return curveCode;
    }

    public void setCurveCode(Integer curveCode) {
        this.curveCode = curveCode;
    }

    public BigDecimal getYtm1M() {
        return ytm1M;
    }

    public void setYtm1M(BigDecimal ytm1M) {
        this.ytm1M = ytm1M;
    }

    public BigDecimal getYtm3M() {
        return ytm3M;
    }

    public void setYtm3M(BigDecimal ytm3M) {
        this.ytm3M = ytm3M;
    }

    public BigDecimal getYtm6M() {
        return ytm6M;
    }

    public void setYtm6M(BigDecimal ytm6M) {
        this.ytm6M = ytm6M;
    }

    public BigDecimal getYtm9M() {
        return ytm9M;
    }

    public void setYtm9M(BigDecimal ytm9M) {
        this.ytm9M = ytm9M;
    }

    public BigDecimal getYtm1Y() {
        return ytm1Y;
    }

    public void setYtm1Y(BigDecimal ytm1Y) {
        this.ytm1Y = ytm1Y;
    }

    public BigDecimal getYtm2Y() {
        return ytm2Y;
    }

    public void setYtm2Y(BigDecimal ytm2Y) {
        this.ytm2Y = ytm2Y;
    }

    public BigDecimal getYtm3Y() {
        return ytm3Y;
    }

    public void setYtm3Y(BigDecimal ytm3Y) {
        this.ytm3Y = ytm3Y;
    }

    public BigDecimal getYtm4Y() {
        return ytm4Y;
    }

    public void setYtm4Y(BigDecimal ytm4Y) {
        this.ytm4Y = ytm4Y;
    }

    public BigDecimal getYtm5Y() {
        return ytm5Y;
    }

    public void setYtm5Y(BigDecimal ytm5Y) {
        this.ytm5Y = ytm5Y;
    }

    public BigDecimal getYtm6Y() {
        return ytm6Y;
    }

    public void setYtm6Y(BigDecimal ytm6Y) {
        this.ytm6Y = ytm6Y;
    }

    public BigDecimal getYtm7Y() {
        return ytm7Y;
    }

    public void setYtm7Y(BigDecimal ytm7Y) {
        this.ytm7Y = ytm7Y;
    }

    public BigDecimal getYtm10Y() {
        return ytm10Y;
    }

    public void setYtm10Y(BigDecimal ytm10Y) {
        this.ytm10Y = ytm10Y;
    }

    public BigDecimal getYtm15Y() {
        return ytm15Y;
    }

    public void setYtm15Y(BigDecimal ytm15Y) {
        this.ytm15Y = ytm15Y;
    }

    public BigDecimal getYtm20Y() {
        return ytm20Y;
    }

    public void setYtm20Y(BigDecimal ytm20Y) {
        this.ytm20Y = ytm20Y;
    }

    public BigDecimal getYtm30Y() {
        return ytm30Y;
    }

    public void setYtm30Y(BigDecimal ytm30Y) {
        this.ytm30Y = ytm30Y;
    }

    public BigDecimal getYtm50Y() {
        return ytm50Y;
    }

    public void setYtm50Y(BigDecimal ytm50Y) {
        this.ytm50Y = ytm50Y;
    }

    public Date getIssueDate() {
        return Objects.isNull(issueDate) ? null : new Date(issueDate.getTime());
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = Objects.isNull(issueDate) ? null : new Date(issueDate.getTime());
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Timestamp getCreateTime() {
        return Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public Timestamp getUpdateTime() {
        return Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

    public Integer getBondType() {
        return bondType;
    }

    public void setBondType(Integer bondType) {
        this.bondType = bondType;
    }
}