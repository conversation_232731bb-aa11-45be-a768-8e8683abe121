package com.innodealing.onshore.yieldspread.mapper.pgsharding.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.BankShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.ibatis.annotations.Param;
import org.apache.shardingsphere.api.hint.HintManager;

import javax.persistence.Table;
import java.util.List;

/**
 * 银行利差曲线-物化视图
 *
 * <AUTHOR>
 */

public interface BankShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<BankShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     *
     * @return tableName
     */
    default String getLogicTable() {
        return BankShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }

    /**
     * 查询分片表count
     *
     * @param dynamicQuery DynamicQuery
     * @param router       RatingRouter 路由
     * @return count
     */
    default int selectCountByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<BankShardBondYieldSpreadCurveDO> dynamicQuery, AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectCountByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 银行利差曲线
     *
     * @param dynamicQuery 查询条件
     * @param router       路由
     * @return list
     */
    default List<BankShardBondYieldSpreadCurveDO> selectByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<BankShardBondYieldSpreadCurveDO> dynamicQuery,
                                                                             AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 删除分片表叔
     * @param dynamicQuery DynamicQuery
     * @param router RatingRouter 路由
     * @return count 删除数量
     */
    default int deleteByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<BankShardBondYieldSpreadCurveDO> dynamicQuery, AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return deleteByDynamicQuery(dynamicQuery);
        }
    }
}
