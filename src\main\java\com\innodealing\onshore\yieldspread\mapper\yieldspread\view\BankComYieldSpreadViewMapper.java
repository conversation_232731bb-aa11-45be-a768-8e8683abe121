package com.innodealing.onshore.yieldspread.mapper.yieldspread.view;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.github.wz2cool.dynamic.mybatis.mapper.SelectViewByDynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.view.BankComYieldSpreadDynamicView;


/**
 * 银行主体利差视图
 *
 * <AUTHOR>
 */
public interface BankComYieldSpreadViewMapper extends SelectViewByDynamicQueryMapper<BankComYieldSpreadDynamicView>,
        DynamicQueryMapper<BankComYieldSpreadDynamicView> {
}
