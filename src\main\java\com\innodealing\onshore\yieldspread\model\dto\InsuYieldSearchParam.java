package com.innodealing.onshore.yieldspread.model.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 保险债券利差查询参数
 *
 * <AUTHOR>
 */
public class InsuYieldSearchParam extends UniversalYieldSpreadSearchParam {

    /**
     * 企业性质（经营类型过滤使用）1:央企, 2:国企, 3:民企, 999:其他
     */
    private List<Integer> businessFilterNatures;

    public List<Integer> getBusinessFilterNatures() {
        return Objects.isNull(businessFilterNatures) ? new ArrayList<>() : new ArrayList<>(businessFilterNatures);
    }

    public void setBusinessFilterNatures(List<Integer> businessFilterNatures) {
        this.businessFilterNatures = Objects.isNull(businessFilterNatures) ? new ArrayList<>() : new ArrayList<>(businessFilterNatures);
    }
}
