package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldTraceExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceLineChartPeriodResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceLineChartRatingResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceTabResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldTraceLineCommonResponseDTO;
import org.springframework.lang.NonNull;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;
import java.util.List;

/**
 * 利差追踪服务
 *
 * <AUTHOR>
 */
public interface BondYieldSpreadTraceService {

    /**
     * 获取利差追踪基础数据
     *
     * @param userId     用户id
     * @param bondType   债券类型
     * @param spreadDate 利率日期
     * @return 利差追踪基础数据返回前端
     */
    List<YieldSpreadTraceTabResponseDTO> listYieldSpreadTraceAbs(Long userId, Integer bondType, Date spreadDate);

    /**
     * 获取利差追踪历史分位
     *
     * @param userId       用户id
     * @param bondType     债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充
     * @param spreadDate   利差日期
     * @param quantileType 分位类型 1:3年，2:5年
     * @param startDate    自定义开始时间
     * @param endDate      自定义结束时间
     * @return 利差追踪历史分位数据返回前端
     */
    List<YieldSpreadTraceTabResponseDTO> listYieldSpreadTraceQuantile(Long userId, Integer bondType, Date spreadDate, Integer quantileType, Date startDate, Date endDate);

    /**
     * 获取利差追踪有数据的最大日期
     *
     * @return 利差日期
     */
    Date maxSpreadDate();

    /**
     * 利差跟踪区间变动
     *
     * @param userId     用户id
     * @param bondType   债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充
     * @param spreadDate 利率时间
     * @param changeType 变动类型(1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动 6 一日变动 7 两周变动)
     * @param startDate  自定义开始时间
     * @param endDate    自定义结束时间
     * @return 利差追踪区间变动返回前端
     */
    List<YieldSpreadTraceTabResponseDTO> listYieldSpreadTraceChange(Long userId, Integer bondType, Date spreadDate, Integer changeType, Date startDate, Date endDate);

    /**
     * 同步利差追踪(历史)
     *
     * @param startDate 开始日期
     * @return int 同步行数
     */
    int syncHistBondYieldSpreadTrace(Date startDate);

    /**
     * 同步利差追踪(增量),不传日期默认同步上一天数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return int 同步行数
     */
    int syncBondYieldSpreadTrace(Date startDate, Date endDate);

    /**
     * 同步利差追踪-区间变动(历史)
     *
     * @param startDate 开始日期
     * @return int
     */
    int syncBondYieldSpreadTraceChange(Date startDate);

    /**
     * 同步利差追踪-历史分位(历史)
     *
     * @param startDate 开始日期
     * @return int
     */
    int syncBondYieldSpreadTraceQuantile(Date startDate);

    /**
     * 利差追踪-折线图(同评级)
     *
     * @param bondType   债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单，14 保险资本补充)
     * @param chartType  图类型，1 到期收益率 2 信用利差 3 等级利差 4 期限利差 5 品种利差 6 条款利差
     * @param ratingType 评级类型，1:AAA+ 2:AAA 3:AAA- 4:AA+ 5:AA 6:AA(2) 7:AA- 0:证券次级债，证券永续债时，评级类型为0,因为这两种没有评级
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param userid     userid
     * @return {@link YieldSpreadTraceLineChartRatingResponseDTO}
     */
    YieldSpreadTraceLineChartRatingResponseDTO lineChartWithRating(@NonNull Integer bondType, @NonNull Integer chartType,
                                                                   @NonNull Integer ratingType, @NonNull Date startDate,
                                                                   @NonNull Date endDate, @NonNull Long userid);

    /**
     * 利差追踪-折线图(同期限)
     *
     * @param bondType   债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单，14 保险资本补充)
     * @param chartType  图类型，1 到期收益率 2 信用利差 3 等级利差 4 期限利差 5 品种利差 6 条款利差
     * @param periodType 期限类型，1:1Y 2:2Y 3:3Y 5:5Y 7:7Y 0:证券次级债，证券永续债时，期限类型为0,因为只有一个评级，展示所有期限
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param userid     userid
     * @return {@link YieldSpreadTraceLineChartPeriodResponseDTO}
     */
    YieldSpreadTraceLineChartPeriodResponseDTO lineChartWithPeriod(@NonNull Integer bondType, @NonNull Integer chartType,
                                                                   @NonNull Integer periodType, @NonNull Date startDate,
                                                                   @NonNull Date endDate, Long userid);

    /**
     * 缓存利差追踪折线图
     */
    void cacheLineChart();

    /**
     * 导出利差追踪
     *
     * @param httpServletResponse        响应体
     * @param userId                     用户id
     * @param bondYieldTraceExportReqDTO 请求参数
     * @throws IOException Exception
     */
    void exportYieldSpreadTrace(HttpServletResponse httpServletResponse, Long userId, BondYieldTraceExportReqDTO bondYieldTraceExportReqDTO) throws IOException;

    /**
     * 利差追踪-品种类折线图(同品种)
     *
     * @param bondType  债券类型(17利率债)
     * @param chartType 图类型，1 到期收益率 2 信用利差 3 等级利差 4 期限利差 5 品种利差 6 条款利差
     * @param curveCode 利差追踪:1 国债 2 国开 4国开 203 农发 204 进出口 51 地方债
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userid    userid
     * @return {@link YieldSpreadTraceLineChartRatingResponseDTO}
     */
    YieldSpreadTraceLineChartRatingResponseDTO lineChartVarietyWithVarieties(Integer bondType, Integer chartType, Integer curveCode, Date startDate, Date endDate, Long userid);

    /**
     * 利差追踪-品种类折线图(同期限)
     *
     * @param bondType   债券类型(17利率债)
     * @param chartType  图类型，1 到期收益率 2 信用利差 3 等级利差 4 期限利差 5 品种利差 6 条款利差
     * @param periodType 期限类型，期限类型，期限类型，1:1M 3:3M 6:6M 9:9M 12:1Y 24:2Y 36:3Y 60:5Y 84:7Y 120:10Y 180:15Y 240:20Y 360:30Y  600:50Y0,因为只有一个评级，展示所有期限
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param userid     userid
     * @return {@link YieldTraceLineCommonResponseDTO}
     */
    YieldTraceLineCommonResponseDTO lineChartVarietyWithPeriod(Integer bondType, Integer chartType, Integer periodType, Date startDate, Date endDate, Long userid);

    /**
     * 同步利差追踪-品种类(历史)
     *
     * @param startDate 开始日期
     * @return int 同步行数
     */
    int syncBondYieldSpreadTraceByCurveCodes(Date startDate, Date endDate, List<Integer> curveCodeList);
}
