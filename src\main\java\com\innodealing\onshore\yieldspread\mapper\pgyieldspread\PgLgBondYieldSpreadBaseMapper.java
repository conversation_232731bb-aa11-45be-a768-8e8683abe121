package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.dto.request.LgSpreadBaseRequestDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadBaseDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgQuantileStatisticsViewDO;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;

/**
 * 地方债利差统计base Mapper
 *
 * <AUTHOR>
 * @create: 2024-11-05
 */
public interface PgLgBondYieldSpreadBaseMapper extends DynamicQueryMapper<PgLgBondYieldSpreadBaseDO> {

    /**
     * 获取指定时间范围内的某个发行时间 对应的分位统计的数据
     *
     * @param requestDTO     筛选参数
     * @param comUniCodeList 地区code列表
     * @param startDate      时间范围开始
     * @param endDate        时间范围结束
     * @return 分位统计的数据
     */
    List<PgLgQuantileStatisticsViewDO> listLgQuantileStatisticsViews(@Param("requestDTO") LgSpreadBaseRequestDTO requestDTO,
                                                                     @Param("comUniCodeList") List<Long> comUniCodeList,
                                                                     @Param("startDate") Date startDate,
                                                                     @Param("endDate") Date endDate);
}
