<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgLgBondYieldSpreadBaseMapper">

    <select id="listLgQuantileStatisticsViews"
            resultType="com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgQuantileStatisticsViewDO">
        SELECT t1.com_uni_code,
        <choose>
            <when test="requestDTO.spreadDataType==1">
                CASE WHEN COUNT(t2.credit_spread_1m) > 0 THEN COUNT(t1.credit_spread_1m &lt; t2.credit_spread_1m or null) ELSE NULL END ytm1MLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_1m) > 0 THEN COUNT(t1.credit_spread_1m) ELSE NULL END ytm1MCount,
                CASE WHEN COUNT(t2.credit_spread_3m) > 0 THEN COUNT(t1.credit_spread_3m &lt; t2.credit_spread_3m or null) ELSE NULL END ytm3MLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_3m) > 0 THEN COUNT(t1.credit_spread_3m) ELSE NULL END ytm3MCount,
                CASE WHEN COUNT(t2.credit_spread_6m) > 0 THEN COUNT(t1.credit_spread_6m &lt; t2.credit_spread_6m or null) ELSE NULL END ytm6MLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_6m) > 0 THEN COUNT(t1.credit_spread_6m) ELSE NULL END ytm6MCount,
                CASE WHEN COUNT(t2.credit_spread_9m) > 0 THEN COUNT(t1.credit_spread_9m &lt; t2.credit_spread_9m or null) ELSE NULL END ytm9MLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_9m) > 0 THEN COUNT(t1.credit_spread_9m) ELSE NULL END ytm9MCount,
                CASE WHEN COUNT(t2.credit_spread_1y) > 0 THEN COUNT(t1.credit_spread_1y &lt; t2.credit_spread_1y or null) ELSE NULL END ytm1YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_1y) > 0 THEN COUNT(t1.credit_spread_1y) ELSE NULL END ytm1YCount,
                CASE WHEN COUNT(t2.credit_spread_2y) > 0 THEN COUNT(t1.credit_spread_2y &lt; t2.credit_spread_2y or null) ELSE NULL END ytm2YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_2y) > 0 THEN COUNT(t1.credit_spread_2y) ELSE NULL END ytm2YCount,
                CASE WHEN COUNT(t2.credit_spread_3y) > 0 THEN COUNT(t1.credit_spread_3y &lt; t2.credit_spread_3y or null) ELSE NULL END ytm3YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_3y) > 0 THEN COUNT(t1.credit_spread_3y) ELSE NULL END ytm3YCount,
                CASE WHEN COUNT(t2.credit_spread_5y) > 0 THEN COUNT(t1.credit_spread_5y &lt; t2.credit_spread_5y or null) ELSE NULL END ytm5YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_5y) > 0 THEN COUNT(t1.credit_spread_5y) ELSE NULL END ytm5YCount,
                CASE WHEN COUNT(t2.credit_spread_7y) > 0 THEN COUNT(t1.credit_spread_7y &lt; t2.credit_spread_7y or null) ELSE NULL END ytm7YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_7y) > 0 THEN COUNT(t1.credit_spread_7y) ELSE NULL END ytm7YCount,
                CASE WHEN COUNT(t2.credit_spread_10y) > 0 THEN COUNT(t1.credit_spread_10y &lt; t2.credit_spread_10y or null) ELSE NULL END ytm10YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_10y) > 0 THEN COUNT(t1.credit_spread_10y) ELSE NULL END ytm10YCount,
                CASE WHEN COUNT(t2.credit_spread_15y) > 0 THEN COUNT(t1.credit_spread_15y &lt; t2.credit_spread_15y or null) ELSE NULL END ytm15YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_15y) > 0 THEN COUNT(t1.credit_spread_15y) ELSE NULL END ytm15YCount,
                CASE WHEN COUNT(t2.credit_spread_20y) > 0 THEN COUNT(t1.credit_spread_20y &lt; t2.credit_spread_20y or null) ELSE NULL END ytm20YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_20y) > 0 THEN COUNT(t1.credit_spread_20y) ELSE NULL END ytm20YCount,
                CASE WHEN COUNT(t2.credit_spread_30y) > 0 THEN COUNT(t1.credit_spread_30y &lt; t2.credit_spread_30y or null) ELSE NULL END ytm30YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_30y) > 0 THEN COUNT(t1.credit_spread_30y) ELSE NULL END ytm30YCount
            </when>
            <when test="requestDTO.spreadDataType==4">
                CASE WHEN COUNT(t2.credit_spread_tb_1m) > 0 THEN COUNT(t1.credit_spread_tb_1m &lt; t2.credit_spread_tb_1m or null) ELSE NULL END ytm1MLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_1m) > 0 THEN COUNT(t1.credit_spread_tb_1m) ELSE NULL END ytm1MCount,
                CASE WHEN COUNT(t2.credit_spread_tb_3m) > 0 THEN COUNT(t1.credit_spread_tb_3m &lt; t2.credit_spread_tb_3m or null) ELSE NULL END ytm3MLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_3m) > 0 THEN COUNT(t1.credit_spread_tb_3m) ELSE NULL END ytm3MCount,
                CASE WHEN COUNT(t2.credit_spread_tb_6m) > 0 THEN COUNT(t1.credit_spread_tb_6m &lt; t2.credit_spread_tb_6m or null) ELSE NULL END ytm6MLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_6m) > 0 THEN COUNT(t1.credit_spread_tb_6m) ELSE NULL END ytm6MCount,
                CASE WHEN COUNT(t2.credit_spread_tb_9m) > 0 THEN COUNT(t1.credit_spread_tb_9m &lt; t2.credit_spread_tb_9m or null) ELSE NULL END ytm9MLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_9m) > 0 THEN COUNT(t1.credit_spread_tb_9m) ELSE NULL END ytm9MCount,
                CASE WHEN COUNT(t2.credit_spread_tb_1y) > 0 THEN COUNT(t1.credit_spread_tb_1y &lt; t2.credit_spread_tb_1y or null) ELSE NULL END ytm1YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_1y) > 0 THEN COUNT(t1.credit_spread_tb_1y) ELSE NULL END ytm1YCount,
                CASE WHEN COUNT(t2.credit_spread_tb_2y) > 0 THEN COUNT(t1.credit_spread_tb_2y &lt; t2.credit_spread_tb_2y or null) ELSE NULL END ytm2YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_2y) > 0 THEN COUNT(t1.credit_spread_tb_2y) ELSE NULL END ytm2YCount,
                CASE WHEN COUNT(t2.credit_spread_tb_3y) > 0 THEN COUNT(t1.credit_spread_tb_3y &lt; t2.credit_spread_tb_3y or null) ELSE NULL END ytm3YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_3y) > 0 THEN COUNT(t1.credit_spread_tb_3y) ELSE NULL END ytm3YCount,
                CASE WHEN COUNT(t2.credit_spread_tb_5y) > 0 THEN COUNT(t1.credit_spread_tb_5y &lt; t2.credit_spread_tb_5y or null) ELSE NULL END ytm5YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_5y) > 0 THEN COUNT(t1.credit_spread_tb_5y) ELSE NULL END ytm5YCount,
                CASE WHEN COUNT(t2.credit_spread_tb_7y) > 0 THEN COUNT(t1.credit_spread_tb_7y &lt; t2.credit_spread_tb_7y or null) ELSE NULL END ytm7YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_7y) > 0 THEN COUNT(t1.credit_spread_tb_7y) ELSE NULL END ytm7YCount,
                CASE WHEN COUNT(t2.credit_spread_tb_10y) > 0 THEN COUNT(t1.credit_spread_tb_10y &lt; t2.credit_spread_tb_10y or null) ELSE NULL END ytm10YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_10y) > 0 THEN COUNT(t1.credit_spread_tb_10y) ELSE NULL END ytm10YCount,
                CASE WHEN COUNT(t2.credit_spread_tb_15y) > 0 THEN COUNT(t1.credit_spread_tb_15y &lt; t2.credit_spread_tb_15y or null) ELSE NULL END ytm15YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_15y) > 0 THEN COUNT(t1.credit_spread_tb_15y) ELSE NULL END ytm15YCount,
                CASE WHEN COUNT(t2.credit_spread_tb_20y) > 0 THEN COUNT(t1.credit_spread_tb_20y &lt; t2.credit_spread_tb_20y or null) ELSE NULL END ytm20YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_20y) > 0 THEN COUNT(t1.credit_spread_tb_20y) ELSE NULL END ytm20YCount,
                CASE WHEN COUNT(t2.credit_spread_tb_30y) > 0 THEN COUNT(t1.credit_spread_tb_30y &lt; t2.credit_spread_tb_30y or null) ELSE NULL END ytm30YLessIssueCount,
                CASE WHEN COUNT(t2.credit_spread_tb_30y) > 0 THEN COUNT(t1.credit_spread_tb_30y) ELSE NULL END ytm30YCount
            </when>
            <otherwise>
                CASE WHEN COUNT(t2.cb_yield_1m) > 0 THEN COUNT(t1.cb_yield_1m &lt; t2.cb_yield_1m or null) ELSE NULL END ytm1MLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_1m) > 0 THEN COUNT(t1.cb_yield_1m) ELSE NULL END ytm1MCount,
                CASE WHEN COUNT(t2.cb_yield_3m) > 0 THEN COUNT(t1.cb_yield_3m &lt; t2.cb_yield_3m or null) ELSE NULL END ytm3MLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_3m) > 0 THEN COUNT(t1.cb_yield_3m) ELSE NULL END ytm3MCount,
                CASE WHEN COUNT(t2.cb_yield_6m) > 0 THEN COUNT(t1.cb_yield_6m &lt; t2.cb_yield_6m or null) ELSE NULL END ytm6MLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_6m) > 0 THEN COUNT(t1.cb_yield_6m) ELSE NULL END ytm6MCount,
                CASE WHEN COUNT(t2.cb_yield_9m) > 0 THEN COUNT(t1.cb_yield_9m &lt; t2.cb_yield_9m or null) ELSE NULL END ytm9MLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_9m) > 0 THEN COUNT(t1.cb_yield_9m) ELSE NULL END ytm9MCount,
                CASE WHEN COUNT(t2.cb_yield_1y) > 0 THEN COUNT(t1.cb_yield_1y &lt; t2.cb_yield_1y or null) ELSE NULL END ytm1YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_1y) > 0 THEN COUNT(t1.cb_yield_1y) ELSE NULL END ytm1YCount,
                CASE WHEN COUNT(t2.cb_yield_2y) > 0 THEN COUNT(t1.cb_yield_2y &lt; t2.cb_yield_2y or null) ELSE NULL END ytm2YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_2y) > 0 THEN COUNT(t1.cb_yield_2y) ELSE NULL END ytm2YCount,
                CASE WHEN COUNT(t2.cb_yield_3y) > 0 THEN COUNT(t1.cb_yield_3y &lt; t2.cb_yield_3y or null) ELSE NULL END ytm3YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_3y) > 0 THEN COUNT(t1.cb_yield_3y) ELSE NULL END ytm3YCount,
                CASE WHEN COUNT(t2.cb_yield_5y) > 0 THEN COUNT(t1.cb_yield_5y &lt; t2.cb_yield_5y or null) ELSE NULL END ytm5YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_5y) > 0 THEN COUNT(t1.cb_yield_5y) ELSE NULL END ytm5YCount,
                CASE WHEN COUNT(t2.cb_yield_7y) > 0 THEN COUNT(t1.cb_yield_7y &lt; t2.cb_yield_7y or null) ELSE NULL END ytm7YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_7y) > 0 THEN COUNT(t1.cb_yield_7y) ELSE NULL END ytm7YCount,
                CASE WHEN COUNT(t2.cb_yield_10y) > 0 THEN COUNT(t1.cb_yield_10y &lt; t2.cb_yield_10y or null) ELSE NULL END ytm10YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_10y) > 0 THEN COUNT(t1.cb_yield_10y) ELSE NULL END ytm10YCount,
                CASE WHEN COUNT(t2.cb_yield_15y) > 0 THEN COUNT(t1.cb_yield_15y &lt; t2.cb_yield_15y or null) ELSE NULL END ytm15YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_15y) > 0 THEN COUNT(t1.cb_yield_15y) ELSE NULL END ytm15YCount,
                CASE WHEN COUNT(t2.cb_yield_20y) > 0 THEN COUNT(t1.cb_yield_20y &lt; t2.cb_yield_20y or null) ELSE NULL END ytm20YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_20y) > 0 THEN COUNT(t1.cb_yield_20y) ELSE NULL END ytm20YCount,
                CASE WHEN COUNT(t2.cb_yield_30y) > 0 THEN COUNT(t1.cb_yield_30y &lt; t2.cb_yield_30y or null) ELSE NULL END ytm30YLessIssueCount,
                CASE WHEN COUNT(t2.cb_yield_30y) > 0 THEN COUNT(t1.cb_yield_30y) ELSE NULL END ytm30YCount
            </otherwise>
        </choose>
        FROM bond_spread_lg_base as t1
            INNER JOIN bond_spread_lg_base t2
            ON t1.com_uni_code = t2.com_uni_code AND t2.spread_date = #{requestDTO.spreadDate} AND t2.deleted = 0
            AND t1.using_lg_bond_type=t2.using_lg_bond_type
            <if test="requestDTO.lgBondType!=null">
                AND t1.lg_bond_type=t2.lg_bond_type
            </if>
            AND t1.using_prepayment_status=t2.using_prepayment_status
            <if test="requestDTO.prepaymentStatus!=null">
                AND t1.prepayment_status=t2.prepayment_status
            </if>
            AND t1.using_fund_use_type=t2.using_fund_use_type
            <if test="requestDTO.fundUseType!=null">
                AND t1.fund_use_type=t2.fund_use_type
            </if>
        WHERE t1.spread_date >= #{startDate}
            AND t1.spread_date &lt;= #{endDate}
            <if test="requestDTO.lgBondType!=null">
                AND t1.using_lg_bond_type = 0
                AND t1.lg_bond_type=#{requestDTO.lgBondType}
            </if>
            <if test="requestDTO.lgBondType==null">
                AND t1.using_lg_bond_type = 1
            </if>
            <if test="requestDTO.prepaymentStatus!=null">
                AND t1.using_prepayment_status = 0
                AND t1.prepayment_status=#{requestDTO.prepaymentStatus}
            </if>
            <if test="requestDTO.prepaymentStatus==null">
                AND t1.using_prepayment_status = 1
            </if>
            <if test="requestDTO.fundUseType!=null">
                AND t1.using_fund_use_type = 0
                AND t1.fund_use_type=#{requestDTO.fundUseType}
            </if>
            <if test="requestDTO.fundUseType==null">
                AND t1.using_fund_use_type = 1
            </if>
            AND t1.com_uni_code in
            <foreach collection="comUniCodeList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
            AND t1.deleted = 0
            GROUP BY t1.com_uni_code;
    </select>
</mapper>