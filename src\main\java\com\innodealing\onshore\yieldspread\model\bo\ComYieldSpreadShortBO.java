package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 主体利差剪短数据
 *
 * <AUTHOR>
 */
public class ComYieldSpreadShortBO {
    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 主体唯一编码
     */
    private Long comUniCode;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    private BigDecimal comCreditSpread;
    /**
     * 超额利差(全部债券);单位(BP)
     */
    private BigDecimal comExcessSpread;
    /**
     * 估值收益率(全部债券);单位(%)
     */
    private BigDecimal comCbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }
}
