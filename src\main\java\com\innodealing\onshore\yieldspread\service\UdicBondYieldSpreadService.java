package com.innodealing.onshore.yieldspread.service;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.AreaYieldSpreadDTO;
import com.innodealing.onshore.yieldspread.enums.UdicRegionEnum;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.InduPanoramaDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.InduPanoramaExportExcelDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicBondYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicSpreadPanoramaResponseDTO;
import org.springframework.lang.Nullable;

import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 城投债利差 Service
 *
 * <AUTHOR>
 **/
public interface UdicBondYieldSpreadService {

    /**
     * 查询行业利差全景图-城投作为行业利差中的一个行业
     *
     * @param request 行业全景图请求DTO
     * @return {@link Optional}<{@link InduPanoramaDTO}> 行业全景图响应数据
     */
    Optional<InduPanoramaDTO> getInduPanorama(InduPanoramaRequestDTO request);

    /**
     * 导出行业利差全景图-城投作为行业利差中的一个行业
     *
     * @param request   导出行业全景图请求DTO
     * @param startDate 利差范围开始时间
     * @param endDate   利差范围结束时间
     * @return {@link List}<{@link InduPanoramaExportExcelDTO}> 行业全景图响应数据集
     */
    List<InduPanoramaExportExcelDTO> listInduPanoramaExcels(InduPanoramaRequestDTO request, Date startDate, Date endDate);

    /**
     * 获取最大利差日期
     *
     * @return {@link String} 利差日期
     */
    Date getMaxSpreadDate();

    /**
     * 城投债利差计算
     *
     * @param onshoreBondInfoDTOs 债券信息
     * @param bondYieldCurveMap   收益率曲线
     * @param spreadDate          利差日期
     * @param isEnableOldData     是否启用老数据
     * @return InduBondYieldSpreadDO
     */
    int calcUdicBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs,
                                             Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                             Date spreadDate, Boolean isEnableOldData);

    /**
     * 城投债利差-全景图列表查询
     *
     * @param request 城投利差全景请求参数
     * @return UdicSpreadPanoramaResponseDTO
     */
    List<UdicSpreadPanoramaResponseDTO> listUdicPanoramas(UdicPanoramaRequestDTO request);

    /**
     * 刷新城投利差全景物化视图
     */
    void refreshMvUdicBondYieldSpreadPanorama();

    /**
     * 城投单券利差分页查询
     *
     * @param request 城投单券利差分页查询请求参数
     * @return {@link NormPagingResult}<{@link UdicBondYieldSpreadResponseDTO}> 城投单券利差分页查询响应数据
     */
    NormPagingResult<UdicBondYieldSpreadResponseDTO> getBondYieldSpreadPaging(UdicListRequestDTO request);

    /**
     * 城投利差曲线查询
     *
     * @param request 城投利差曲线查询请求参数
     * @return <{@link List}<{@link UdicCurveResponseDTO}>> 城投利差曲线查询响应数据集
     */
    List<UdicCurveResponseDTO> listCurves(UdicCurveRequestDTO request);

    /**
     * 刷新城投利差曲线物化视图
     *
     * @param udicRegionEnum 城投区域
     */
    void refreshMvUdicBondYieldSpreadCurve(UdicRegionEnum udicRegionEnum);

    /**
     * 刷新上一天城投利差曲线数据
     */
    void refreshCurveYesterday();

    /**
     * 刷新历史物化视图
     *
     * @param isTableRefresh 是否刷新表结构
     */
    void refreshMvUdicBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh);

    /**
     * 刷新物化视图区间
     *
     * @param param 时间区间
     */
    void refreshMvUdicBondYieldSpreadRatingCurve(RefreshYieldCurveParam param);

    /**
     * 计算城投区域利差
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    void calcUdicAreaYieldSpreadsBySpreadDate(Date startDate, Date endDate);

    /**
     * 保存曲线
     *
     * @param userid       用户id
     * @param curveGroupId 曲线组id
     * @param request      请求参数
     * @return 操作结果
     */
    boolean saveCurve(Long userid, Long curveGroupId, UdicCurveGenerateConditionReqDTO request);

    /**
     * 更新曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @param request 更新参数
     * @return 执行结果
     */
    boolean updateCurve(Long userid, Long curveId, UdicCurveGenerateConditionReqDTO request);

    /**
     * 获取单券利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 单券利差
     */
    NormPagingResult<UdicBondYieldSpreadResponseDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 曲线数据集
     *
     * @param bondUniCode 债券唯一编码
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveDTO}>
     */
    List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate);

    /**
     * 查询债券数据集合
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link UdicBondYieldSpreadResponseDTO}>
     */
    List<UdicBondYieldSpreadResponseDTO> listBonds(Date spreadDate, @Nullable Set<Long> bondUniCodes);

    /**
     * 查询区域利差
     *
     * @param spreadDate 利差日期
     * @param areaCode   区域code
     * @return AreaYieldSpreadDTO
     */
    AreaYieldSpreadDTO areaYieldSpread(Date spreadDate, Long areaCode);

}
