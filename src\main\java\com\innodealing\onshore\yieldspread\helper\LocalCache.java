package com.innodealing.onshore.yieldspread.helper;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.yieldspread.dao.yieldspread.YieldSpreadBondDAO;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 本地缓存
 *
 * <AUTHOR>
 */
@Component
public class LocalCache implements InitializingBean {

    @Resource
    private YieldSpreadBondDAO yieldSpreadBondDAO;

    /**
     * key:bondCode
     * value:bondUniCode
     */
    private static final Map<String, Long> BOND_CODE_BOND_UNI_CODE_MAP = new HashMap<>();

    /**
     * key:ComSpreadSector
     * value:bondUniCodes
     */
    private static final Map<Integer, Set<Long>> SECTOR_BOND_UNI_CODES_MAP = new HashMap<>();

    private static final int PAGE_SIZE = 2000;

    @Override
    public void afterPropertiesSet() {
        cacheYieldSpreadBonds();
    }

    /**
     * 缓存利差债
     */
    @Scheduled(cron = "0 0 5 * * ?")
    public void cacheYieldSpreadBonds() {
        BOND_CODE_BOND_UNI_CODE_MAP.clear();
        SECTOR_BOND_UNI_CODES_MAP.clear();
        int pageNum = 1;
        while (true) {
            NormPagingResult<YieldSpreadBondDO> result = yieldSpreadBondDAO.pagingYieldSpreadBonds(pageNum, PAGE_SIZE);
            List<YieldSpreadBondDO> bonds = result.getList();
            this.cacheYieldSpreadBonds(bonds);
            if (!result.isHasNextPage()) {
                return;
            }
            pageNum++;
        }
    }

    /**
     * 缓存利差债券
     *
     * @param bonds 利差债券
     */
    public void cacheYieldSpreadBonds(List<YieldSpreadBondDO> bonds) {
        if (CollectionUtils.isEmpty(bonds)) {
            return;
        }
        BOND_CODE_BOND_UNI_CODE_MAP.putAll(bonds.stream().filter(bond -> StringUtils.isNotBlank(bond.getBondCode()))
                .collect(Collectors.toMap(YieldSpreadBondDO::getBondCode, YieldSpreadBondDO::getBondUniCode, (k1, k2) -> k1)));
        Map<Integer, Set<Long>> bondUniCodeMap = bonds.stream()
                .collect(Collectors.groupingBy(YieldSpreadBondDO::getComSpreadSector, Collectors.mapping(YieldSpreadBondDO::getBondUniCode, Collectors.toSet())));
        for (Map.Entry<Integer, Set<Long>> entry : bondUniCodeMap.entrySet()) {
            Set<Long> bondUniCodes = SECTOR_BOND_UNI_CODES_MAP.get(entry.getKey());
            if (CollectionUtils.isEmpty(bondUniCodes)) {
                SECTOR_BOND_UNI_CODES_MAP.put(entry.getKey(), entry.getValue());
            } else {
                bondUniCodes.addAll(entry.getValue());
            }
        }
    }

    /**
     * 获取缓存中包含的bondUniCode
     *
     * @param bondCodes bondCodes
     * @return bondUniCodes
     */
    public Set<Long> listExistsBondUniCodesByBondCodes(Set<String> bondCodes) {
        Set<Long> result = new HashSet<>();
        if (CollectionUtils.isEmpty(bondCodes)) {
            return result;
        }
        bondCodes.stream().filter(StringUtils::isNotBlank).forEach(bondCode -> {
            Long bondUniCode = BOND_CODE_BOND_UNI_CODE_MAP.get(bondCode);
            if (Objects.nonNull(bondUniCode)) {
                result.add(bondUniCode);
            }
        });
        return result;
    }

    /**
     * 获取缓存中包含的bondUniCode
     *
     * @param bondUniCodes bondUniCodes
     * @return bondUniCodes
     */
    public Set<Long> listExistsBondUniCodes(Set<Long> bondUniCodes) {
        Set<Long> result = new HashSet<>();
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return result;
        }
        for (Map.Entry<Integer, Set<Long>> entry : SECTOR_BOND_UNI_CODES_MAP.entrySet()) {
            result.addAll(bondUniCodes.stream().filter(entry.getValue()::contains).collect(Collectors.toSet()));
        }
        return result;
    }

    /**
     * 把一批bondUniCode转换为 Sector：bondUniCodes 的map
     *
     * @param bondUniCodes bondUniCodes
     * @return Map<Integer, Set < Long>>
     */
    public Map<Integer, Set<Long>> convertToSectorBondUniCodesMap(Set<Long> bondUniCodes) {
        Map<Integer, Set<Long>> result = new HashMap<>(ComYieldSpreadSectorEnum.values().length);
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return result;
        }
        for (Map.Entry<Integer, Set<Long>> entry : SECTOR_BOND_UNI_CODES_MAP.entrySet()) {
            result.put(entry.getKey(), bondUniCodes.stream().filter(entry.getValue()::contains).collect(Collectors.toSet()));
        }
        return result;
    }

    /**
     * 根据bondCode获取bondUniCode
     *
     * @param bondCode 债券代码
     * @return bondUniCode
     */
    public Optional<Long> getBondUniCodeByBondCode(String bondCode) {
        return Optional.ofNullable(BOND_CODE_BOND_UNI_CODE_MAP.get(bondCode));
    }

}
