package com.innodealing.onshore.yieldspread.config.datasource;

import com.alibaba.druid.pool.DruidAbstractDataSource;
import com.google.common.collect.Maps;
import com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.mv.*;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.*;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.shardingsphere.api.config.sharding.ShardingRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.TableRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.HintShardingStrategyConfiguration;
import org.apache.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.spring.annotation.MapperScan;

import javax.persistence.Table;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Map;
import java.util.Properties;


/**
 * PgShardingJdbc配置文件
 *
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.innodealing.onshore.yieldspread.mapper.pgsharding"}, sqlSessionFactoryRef = PgShardingYieldSpreadDataSourceConfig.SESSION_SHARDING_FACTORY_NAME)
@SuppressWarnings({"squid:S00103"})
public class PgShardingYieldSpreadDataSourceConfig extends BaseSourceConfig {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String TRANSACTION_NAME = "pgShardingYieldspreadTransactionManager";

    private static final String DATA_SOURCE_NAME = "pgyieldspreadDataSource";
    private static final String SHARDING_DATA_SOURCE_NAME = "shardingPgbondpriceDataSource";

    public static final String SESSION_SHARDING_FACTORY_NAME = "pgyShardingSessionFactory";
    protected static final String[] ALIAS_PACKAGES = {"com.innodealing.onshore.yieldspread.model.entity.pgyieldspread"};
    public static final String TRANSACTION_TEMPLATE_NAME = "pgShardingYieldspreadTransactionTemplate";

    private static final String DATABASE_TABLE_PLACEHOLDER = "%s.%s";
    private static final String DATABASE_NAME = "yield_spread";

    /**
     * 分片数据源
     *
     * @param dataSource 数据源
     * @return 返回分片数据源
     * @throws SQLException sql 异常
     */
    @Bean(name = SHARDING_DATA_SOURCE_NAME)
    public DataSource shardingDataSource(@Qualifier(DATA_SOURCE_NAME) DataSource dataSource) throws SQLException {
        // 配置分片规则
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        Map<String, DataSource> dataSourceMap = Maps.newHashMap();
        dataSourceMap.put(DATABASE_NAME, dataSource);

        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(InduShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));
        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(UdicShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));
        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(SecuShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));
        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(BankShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));
        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(InsuShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));

        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(MvInduShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));
        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(MvUdicShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));
        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(MvSecuShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));
        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(MvBankShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));
        shardingRuleConfig.getTableRuleConfigs().add(this.getSelectDataTypeTableHintShardingStrategyConfiguration(MvInsuShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name()));

        Properties props = new Properties();


        logger.info("[创建分片数据源:{}]", ((DruidAbstractDataSource) dataSource).getUrl());

        //创建分片数据源
        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, props);
    }

    /**
     * 使用Hint进行虚拟的逻辑分片
     *
     * @param logicTableName 逻辑表名
     * @return 分片规则配置
     * @see RatingTableHintShardingAlgorithm
     */
    private TableRuleConfiguration getSelectDataTypeTableHintShardingStrategyConfiguration(String logicTableName) {
        TableRuleConfiguration tableRuleConfiguration = new TableRuleConfiguration(logicTableName, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, logicTableName));
        tableRuleConfiguration.setTableShardingStrategyConfig(new HintShardingStrategyConfiguration(new RatingTableHintShardingAlgorithm()));
        return tableRuleConfiguration;
    }

    /**
     * 创建SqlSessionFactory对象
     *
     * @param dataSource 数据源
     * @return SqlSessionFactory对象
     * @throws Exception 异常
     */
    @Bean(name = SESSION_SHARDING_FACTORY_NAME)
    public SqlSessionFactory sqlSessionShardingFactory(@Qualifier(SHARDING_DATA_SOURCE_NAME) DataSource dataSource) throws Exception {
        return super.getSessionFactory(dataSource, ALIAS_PACKAGES);
    }


    /**
     * DataSourceTransactionManager 事务管理器
     *
     * @param dataSource 数据源
     * @return 事务管理器
     * @throws SQLException 异常
     */
    @Bean(name = TRANSACTION_NAME)
    public DataSourceTransactionManager transactionManager(@Qualifier(DATA_SOURCE_NAME) DataSource dataSource) throws SQLException {
        return new DataSourceTransactionManager(shardingDataSource(dataSource));
    }

    /**
     * 创建TransactionTemplate对象
     *
     * @param transactionManager 事务管理器
     * @return 事务模板
     */
    @Bean(name = TRANSACTION_TEMPLATE_NAME)
    @Primary
    public TransactionTemplate transactionTemplate(@Qualifier(TRANSACTION_NAME) DataSourceTransactionManager transactionManager) {
        return new TransactionTemplate(transactionManager);
    }

}
