package com.innodealing.onshore.yieldspread.model.view;

import com.github.wz2cool.dynamic.mybatis.View;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;


/**
 * 证券主体利差视图
 *
 * <AUTHOR>
 */
@View("secu_com_yield_spread LEFT JOIN com_yield_spread_change ON secu_com_yield_spread.com_uni_code = com_yield_spread_change.com_uni_code " +
        "and secu_com_yield_spread.spread_date = com_yield_spread_change.spread_date\n" +
        "and com_yield_spread_change.deleted = 0\n" +
        "and com_yield_spread_change.com_spread_sector = 3")
public class SecuComYieldSpreadDynamicView {
    /**
     * 发行人代码
     */
    @Column(table = "secu_com_yield_spread")
    private Long comUniCode;
    /**
     * 一级行业编码
     */
    @Column(table = "secu_com_yield_spread")
    private Long induLevel1Code;
    /**
     * 一级行业名称
     */
    @Column(table = "secu_com_yield_spread")
    private String induLevel1Name;
    /**
     * 二级行业编码
     */
    @Column(table = "secu_com_yield_spread")
    private Long induLevel2Code;
    /**
     * 二级行业名称
     */
    @Column(table = "secu_com_yield_spread")
    private String induLevel2Name;
    /**
     * 企业类型(性质)
     */
    @Column(table = "secu_com_yield_spread")
    private Integer businessNature;
    /**
     * 利差日期
     */
    @Column(table = "secu_com_yield_spread")
    private Date spreadDate;
    /**
     * 主体外部评级映射
     */
    @Column(table = "secu_com_yield_spread")
    private Integer comExtRatingMapping;
    /**
     * 主体信用利差(全部债券);单位(BP)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comCreditSpread;
    /**
     * 主体超额利差(全部债券);单位(BP)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comExcessSpread;
    /**
     * 主体信用利差(普通);单位(BP)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comSeniorCreditSpread;
    /**
     * 主体超额利差(普通);单位(BP)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comSeniorExcessSpread;
    /**
     * 主体信用利差(次级);单位(BP)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comSubordinatedCreditSpread;
    /**
     * 主体超额利差(次级);单位(BP)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comSubordinatedExcessSpread;
    /**
     * 主体信用利差(永续);单位(BP)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comPerpetualCreditSpread;
    /**
     * 主体超额利差(永续);单位(BP)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comPerpetualExcessSpread;
    /**
     * 主体估值收益率(全部债券);单位(%)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comCbYield;
    /**
     * 主体估值收益率(普通);单位(%)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comSeniorCbYield;
    /**
     * 主体估值收益率(次级);单位(%)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comSubordinatedCbYield;
    /**
     * 主体估值收益率(永续);单位(%)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal comPerpetualCbYield;
    /**
     * 总资产(万元)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal totalAssets;
    /**
     * 净利润(万元)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal netProfit;
    /**
     * 资本杠杆率(%)
     */
    @Column(table = "secu_com_yield_spread")
    private BigDecimal capitalLeverageRatio;
    /**
     * 信用利差近3个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_change_3m")
    private BigDecimal creditSpreadChange3M;
    /**
     * 信用利差近6个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_change_6m")
    private BigDecimal creditSpreadChange6M;
    /**
     * 超额利差近3个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_change_3m")
    private BigDecimal excessSpreadChange3M;
    /**
     * 超额利差近6个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_change_6m")
    private BigDecimal excessSpreadChange6M;

    /**
     * 信用利差3年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_quantile_3y")
    private BigDecimal creditSpreadQuantile3Y;

    /**
     * 信用利差5年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_quantile_5y")
    private BigDecimal creditSpreadQuantile5Y;

    /**
     * 超额利差3年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_quantile_3y")
    private BigDecimal excessSpreadQuantile3Y;

    /**
     * 超额利差5年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_quantile_5y")
    private BigDecimal excessSpreadQuantile5Y;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }

    public String getInduLevel1Name() {
        return induLevel1Name;
    }

    public void setInduLevel1Name(String induLevel1Name) {
        this.induLevel1Name = induLevel1Name;
    }

    public Long getInduLevel2Code() {
        return induLevel2Code;
    }

    public void setInduLevel2Code(Long induLevel2Code) {
        this.induLevel2Code = induLevel2Code;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }

    public Integer getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(Integer businessNature) {
        this.businessNature = businessNature;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComSeniorCreditSpread() {
        return comSeniorCreditSpread;
    }

    public void setComSeniorCreditSpread(BigDecimal comSeniorCreditSpread) {
        this.comSeniorCreditSpread = comSeniorCreditSpread;
    }

    public BigDecimal getComSeniorExcessSpread() {
        return comSeniorExcessSpread;
    }

    public void setComSeniorExcessSpread(BigDecimal comSeniorExcessSpread) {
        this.comSeniorExcessSpread = comSeniorExcessSpread;
    }

    public BigDecimal getComSubordinatedCreditSpread() {
        return comSubordinatedCreditSpread;
    }

    public void setComSubordinatedCreditSpread(BigDecimal comSubordinatedCreditSpread) {
        this.comSubordinatedCreditSpread = comSubordinatedCreditSpread;
    }

    public BigDecimal getComSubordinatedExcessSpread() {
        return comSubordinatedExcessSpread;
    }

    public void setComSubordinatedExcessSpread(BigDecimal comSubordinatedExcessSpread) {
        this.comSubordinatedExcessSpread = comSubordinatedExcessSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComSeniorCbYield() {
        return comSeniorCbYield;
    }

    public void setComSeniorCbYield(BigDecimal comSeniorCbYield) {
        this.comSeniorCbYield = comSeniorCbYield;
    }

    public BigDecimal getComSubordinatedCbYield() {
        return comSubordinatedCbYield;
    }

    public void setComSubordinatedCbYield(BigDecimal comSubordinatedCbYield) {
        this.comSubordinatedCbYield = comSubordinatedCbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    public BigDecimal getCapitalLeverageRatio() {
        return capitalLeverageRatio;
    }

    public void setCapitalLeverageRatio(BigDecimal capitalLeverageRatio) {
        this.capitalLeverageRatio = capitalLeverageRatio;
    }

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }
}
