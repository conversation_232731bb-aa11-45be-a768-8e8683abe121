package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.commons.stream.ExtCollectors;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationShortInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondpriceapollo.CurveMaturityStructureDTO;
import com.innodealing.onshore.bondmetadata.dto.common.DateRangeDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.BondCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.BondYieldSpreadDTO;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.yieldspread.BondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InduBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UdicBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.*;
import com.innodealing.onshore.yieldspread.model.bo.BondCreditSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.bo.CurveMaturityStructureBO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.UniCodeCollectResultDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.BondYieldSpreadItemListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.BondYieldSpreadListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.service.*;
import com.innodealing.onshore.yieldspread.service.internal.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.FOUR_DECIMAL_PLACE;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.WORK_THREAD_NUM;
import static com.innodealing.onshore.yieldspread.enums.CurveTypeEnum.*;

/**
 * 单券利差服务
 *
 * <AUTHOR>
 */
@Service
public class BondYieldSpreadServiceImpl implements BondYieldSpreadService, InitializingBean {

    private static final int MAX_SELECTED_BOND_SIZE = 4;

    private final Map<CurveTypeEnum, CurveQueryRunner> queryRunnerMap = new ConcurrentHashMap<>();

    private final Map<ComYieldSpreadSectorEnum, BiConsumer<UniCodeCollectResultDTO, Set<Long>>> collectBondMap = new ConcurrentHashMap<>();

    private static final int BOND_YIELD_SPREAD_CURVE_YEAR_SPAN = 1;

    private static final int YIELD_SPREAD_CURVE_YEAR_LIMIT = 3;

    private static final Logger LOGGER = LoggerFactory.getLogger(BondYieldSpreadServiceImpl.class);

    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("BondYieldSpreadServiceImpl-pool-").build());

    @Resource
    private InduBondYieldSpreadDAO induBondYieldSpreadDAO;

    @Resource
    private UdicBondYieldSpreadDAO udicBondYieldSpreadDAO;

    @Resource
    private InduBondYieldSpreadService induBondYieldSpreadService;

    @Resource
    private UdicBondYieldSpreadService udicBondYieldSpreadService;

    @Resource
    private SecuBondYieldSpreadService secuBondYieldSpreadService;

    @Resource
    private BankBondYieldSpreadService bankBondYieldSpreadService;

    @Resource
    private InsuBondYieldSpreadService insuBondYieldSpreadService;

    @Resource
    private LocalCache localCache;

    @Resource
    private BondYieldSpreadDAO bondYieldSpreadDAO;

    @Resource
    private HolidayService holidayService;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private BondPriceApolloService bondPriceApolloService;

    @Resource
    private RedisService redisService;

    @Resource
    private UserService userService;

    @Override
    public void afterPropertiesSet() {
        queryRunnerMap.put(INDU, (bondUniCode, startDate, endDate) -> induBondYieldSpreadService.curves(bondUniCode, startDate, endDate));
        queryRunnerMap.put(UDIC, (bondUniCode, startDate, endDate) -> udicBondYieldSpreadService.curves(bondUniCode, startDate, endDate));
        queryRunnerMap.put(SECURITY, (bondUniCode, startDate, endDate) -> secuBondYieldSpreadService.curves(bondUniCode, startDate, endDate));
        queryRunnerMap.put(BANK, (bondUniCode, startDate, endDate) -> bankBondYieldSpreadService.curves(bondUniCode, startDate, endDate));
        queryRunnerMap.put(CUSTOMIZATION, this::listCustomCurves);
        queryRunnerMap.put(INSURANCE, (bondUniCode, startDate, endDate) -> insuBondYieldSpreadService.curves(bondUniCode, startDate, endDate));
        collectBondMap.put(ComYieldSpreadSectorEnum.INDU, UniCodeCollectResultDTO::addInduUniCodes);
        collectBondMap.put(ComYieldSpreadSectorEnum.SECU, UniCodeCollectResultDTO::addSecuUniCodes);
        collectBondMap.put(ComYieldSpreadSectorEnum.BANK, UniCodeCollectResultDTO::addBankUniCodes);
        collectBondMap.put(ComYieldSpreadSectorEnum.UDIC, UniCodeCollectResultDTO::addUdicUniCodes);
        collectBondMap.put(ComYieldSpreadSectorEnum.INSU, UniCodeCollectResultDTO::addInsuUniCodes);
    }

    private List<BondYieldSpreadCurveDTO> listCustomCurves(Long bondUniCode, Date startDate, Date endDate) {
        List<BondYieldSpreadCurveDTO> curves = udicBondYieldSpreadService.curves(bondUniCode, startDate, endDate);
        if (CollectionUtils.isNotEmpty(curves)) {
            return curves;
        }
        curves = bankBondYieldSpreadService.curves(bondUniCode, startDate, endDate);
        if (CollectionUtils.isNotEmpty(curves)) {
            return curves;
        }
        curves = secuBondYieldSpreadService.curves(bondUniCode, startDate, endDate);
        if (CollectionUtils.isNotEmpty(curves)) {
            return curves;
        }
        return induBondYieldSpreadService.curves(bondUniCode, startDate, endDate);
    }

    @Override
    public List<BondCreditSpreadDTO> listBondCreditSpreads(Date spreadDate, Set<Long> bondUniCodes) {
        // 从产业中查询信用利差
        List<BondCreditSpreadBO> induBondCreditSpreadList = induBondYieldSpreadDAO.listBondCreditSpreads(spreadDate, bondUniCodes);
        // 从城投中查询信用利差
        List<BondCreditSpreadBO> udicBondCreditSpreadList = udicBondYieldSpreadDAO.listBondCreditSpreads(spreadDate, bondUniCodes);
        List<BondCreditSpreadDTO> responseList =
                Lists.newArrayListWithExpectedSize(induBondCreditSpreadList.size() + udicBondCreditSpreadList.size());
        responseList.addAll(BeanCopyUtils.copyList(induBondCreditSpreadList, BondCreditSpreadDTO.class));
        responseList.addAll(BeanCopyUtils.copyList(udicBondCreditSpreadList, BondCreditSpreadDTO.class));
        return responseList;
    }

    @Override
    public List<BondYieldSpreadCurveResponseDTO> listCurves(Long bondUniCode, Integer curveType, Date startDate, Date endDate) {
        Optional<CurveTypeEnum> curveTypeEnum = EnumUtils.ofNullable(CurveTypeEnum.class, curveType);
        if (!curveTypeEnum.isPresent()) {
            return Collections.emptyList();
        }
        CurveQueryRunner curveQueryRunner = queryRunnerMap.get(curveTypeEnum.get());
        List<BondYieldSpreadCurveDTO> bondYieldSpreadCurveList = Optional.ofNullable(curveQueryRunner)
                .map(runner -> runner.run(bondUniCode, startDate, endDate)).orElse(Collections.emptyList());
        return bondYieldSpreadCurveList.stream().map(this::toResp).collect(Collectors.toList());
    }

    private BondYieldSpreadCurveResponseDTO toResp(BondYieldSpreadCurveDTO bondYieldSpreadCurveDTO) {
        BondYieldSpreadCurveResponseDTO response = BeanCopyUtils.copyProperties(bondYieldSpreadCurveDTO, BondYieldSpreadCurveResponseDTO.class);
        response.setAvgBondCreditSpread(bondYieldSpreadCurveDTO.getBondCreditSpread());
        response.setAvgBondExcessSpread(bondYieldSpreadCurveDTO.getBondExcessSpread());
        response.setAvgCbYield(bondYieldSpreadCurveDTO.getCbYield());
        return response;
    }

    @Override
    public BondYieldSpreadListResponseDTO listBonds(BondYieldSpreadListRequestDTO request, Long userid) {
        this.checkCodeSize(request);
        final Date spreadDate = request.getSpreadDate();
        final Set<Long> customBonds = this.collectBonds(request.getCustomBonds());
        Map<Integer, Set<Long>> customBondMap = localCache.convertToSectorBondUniCodesMap(customBonds);
        UniCodeCollectResultDTO bondCollectResult = new UniCodeCollectResultDTO();
        bondCollectResult.addInduUniCodes(this.collectBonds(request.getIndus()));
        bondCollectResult.addUdicUniCodes(this.collectBonds(request.getUdics()));
        bondCollectResult.addSecuUniCodes(this.collectBonds(request.getSecus()));
        bondCollectResult.addBankUniCodes(this.collectBonds(request.getBanks()));
        bondCollectResult.addInsuUniCodes(this.collectBonds(request.getInsus()));
        for (Map.Entry<Integer, Set<Long>> entry : customBondMap.entrySet()) {
            ComYieldSpreadSectorEnum spreadSectorEnum = EnumUtils.getEnum(ComYieldSpreadSectorEnum.class, entry.getKey());
            collectBondMap.get(spreadSectorEnum).accept(bondCollectResult, entry.getValue());
        }
        List<InduBondYieldSpreadResponseDTO> induResponseList = induBondYieldSpreadService.listBonds(spreadDate, bondCollectResult.getInduUniCodes());
        List<UdicBondYieldSpreadResponseDTO> udicResponseList = udicBondYieldSpreadService.listBonds(spreadDate, bondCollectResult.getUdicUniCodes());
        List<BankSingleBondYieldSpreadResDTO> bankResponseList = bankBondYieldSpreadService.listBonds(spreadDate, bondCollectResult.getBankUniCodes());
        List<SecuSingleBondYieldSpreadResDTO> secuResponseList = secuBondYieldSpreadService.listBonds(spreadDate, bondCollectResult.getSecuUniCodes());
        List<InsuSingleBondYieldSpreadResDTO> insuResponseList = insuBondYieldSpreadService.listBonds(spreadDate, bondCollectResult.getInsuUniCodes());
        BondYieldSpreadListResponseDTO response = new BondYieldSpreadListResponseDTO();
        response.setIndus(this.bindingInduCurveId(request, induResponseList, spreadDate, userid));
        response.setUdics(this.bindingUdicCurveId(request, udicResponseList, spreadDate, userid));
        response.setBanks(this.bindingBankCurveId(request, bankResponseList, spreadDate, userid));
        response.setSecus(this.bindingSecuCurveId(request, secuResponseList, spreadDate, userid));
        response.setInsus(this.bindingInsuCurveId(request, insuResponseList, spreadDate, userid));
        if (CollectionUtils.isEmpty(request.getCustomBonds())) {
            return response;
        }
        // 自选债有先后顺序，如果某支债在城投，产业，银行，证券中都有，则按照 1.udic 2.bank 3.secu  4 保险 5.indu 先后顺序获取
        List<CustomSingleBondYieldSpreadResDTO> customBondResponseList = Lists.newArrayListWithExpectedSize(request.getCustomBonds().size());
        // 1.udic
        List<CustomSingleBondYieldSpreadResDTO> udicCustomBondList = udicResponseList.stream().filter(udic -> customBonds.contains(udic.getBondUniCode()))
                .map(udic -> BeanCopyUtils.copyProperties(udic, CustomSingleBondYieldSpreadResDTO.class)).collect(Collectors.toList());
        final Set<Long> udicBondUniCodes = udicCustomBondList.stream().map(BaseSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toSet());
        customBonds.removeIf(udicBondUniCodes::contains);
        customBondResponseList.addAll(udicCustomBondList);
        // 2.bank
        List<CustomSingleBondYieldSpreadResDTO> bankCustomBondList = bankResponseList.stream().filter(bank -> customBonds.contains(bank.getBondUniCode()))
                .map(bank -> BeanCopyUtils.copyProperties(bank, CustomSingleBondYieldSpreadResDTO.class)).collect(Collectors.toList());
        final Set<Long> bankBondUniCodes = bankCustomBondList.stream().map(BaseSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toSet());
        customBonds.removeIf(bankBondUniCodes::contains);
        customBondResponseList.addAll(bankCustomBondList);
        // 3.secu
        List<CustomSingleBondYieldSpreadResDTO> secuCustomBondList = secuResponseList.stream().filter(secu -> customBonds.contains(secu.getBondUniCode()))
                .map(secu -> BeanCopyUtils.copyProperties(secu, CustomSingleBondYieldSpreadResDTO.class)).collect(Collectors.toList());
        final Set<Long> secuBondUniCodes = secuCustomBondList.stream().map(BaseSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toSet());
        customBonds.removeIf(secuBondUniCodes::contains);
        customBondResponseList.addAll(secuCustomBondList);
        // 4.insu
        List<CustomSingleBondYieldSpreadResDTO> insuCustomBondList = insuResponseList.stream().filter(insu -> customBonds.contains(insu.getBondUniCode()))
                .map(insu -> BeanCopyUtils.copyProperties(insu, CustomSingleBondYieldSpreadResDTO.class)).collect(Collectors.toList());
        final Set<Long> insuBondUniCodes = insuCustomBondList.stream().map(BaseSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toSet());
        customBonds.removeIf(insuBondUniCodes::contains);
        customBondResponseList.addAll(insuCustomBondList);
        // 5.indu
        List<CustomSingleBondYieldSpreadResDTO> induCustomBondList = induResponseList.stream().filter(indu -> customBonds.contains(indu.getBondUniCode()))
                .map(indu -> BeanCopyUtils.copyProperties(indu, CustomSingleBondYieldSpreadResDTO.class)).collect(Collectors.toList());
        customBondResponseList.addAll(induCustomBondList);
        response.setCustomBonds(this.bindingCustomCurveId(request, customBondResponseList, spreadDate, userid));
        return response;
    }

    private List<CustomSingleBondYieldSpreadResDTO> bindingCustomCurveId(BondYieldSpreadListRequestDTO request,
                                                                         List<CustomSingleBondYieldSpreadResDTO> customBondResponseList,
                                                                         Date spreadDate, Long userid) {
        List<CustomSingleBondYieldSpreadResDTO> totalCustomList = Lists.newArrayListWithExpectedSize(customBondResponseList.size());
        for (BondYieldSpreadItemListRequestDTO customRequest : request.getCustomBonds()) {
            customBondResponseList.stream().filter(custom -> Objects.equals(customRequest.getBondUniCode(), custom.getBondUniCode())).findFirst().ifPresent(custom -> {
                CustomSingleBondYieldSpreadResDTO customResponse = BeanCopyUtils.copyProperties(custom, CustomSingleBondYieldSpreadResDTO.class);
                customResponse.setCurveId(customRequest.getCurveId());
                totalCustomList.add(customResponse);
            });
        }
        permissionBase(userid, spreadDate, totalCustomList);
        return totalCustomList;
    }

    private List<SecuSingleBondYieldSpreadResDTO> bindingSecuCurveId(BondYieldSpreadListRequestDTO request,
                                                                     List<SecuSingleBondYieldSpreadResDTO> secuResponseList, Date spreadDate, Long userid) {
        List<SecuSingleBondYieldSpreadResDTO> totalSecuList = Lists.newArrayListWithExpectedSize(secuResponseList.size());
        for (BondYieldSpreadItemListRequestDTO secuRequest : request.getSecus()) {
            secuResponseList.stream().filter(secu -> Objects.equals(secuRequest.getBondUniCode(), secu.getBondUniCode())).findFirst().ifPresent(secu -> {
                SecuSingleBondYieldSpreadResDTO secuResponse = BeanCopyUtils.copyProperties(secu, SecuSingleBondYieldSpreadResDTO.class);
                secuResponse.setCurveId(secuRequest.getCurveId());
                totalSecuList.add(secuResponse);
            });
        }
        permissionBase(userid, spreadDate, totalSecuList);
        return totalSecuList;
    }

    private List<InsuSingleBondYieldSpreadResDTO> bindingInsuCurveId(BondYieldSpreadListRequestDTO request,
                                                                     List<InsuSingleBondYieldSpreadResDTO> insuResponseList, Date spreadDate, Long userid) {
        List<InsuSingleBondYieldSpreadResDTO> totalInsuList = Lists.newArrayListWithExpectedSize(insuResponseList.size());
        for (BondYieldSpreadItemListRequestDTO insuRequest : request.getInsus()) {
            insuResponseList.stream().filter(insu -> Objects.equals(insuRequest.getBondUniCode(), insu.getBondUniCode())).findFirst().ifPresent(insu -> {
                InsuSingleBondYieldSpreadResDTO insuResponse = BeanCopyUtils.copyProperties(insu, InsuSingleBondYieldSpreadResDTO.class);
                insuResponse.setCurveId(insuRequest.getCurveId());
                totalInsuList.add(insuResponse);
            });
        }
        permissionBase(userid, spreadDate, totalInsuList);
        return totalInsuList;
    }

    private List<BankSingleBondYieldSpreadResDTO> bindingBankCurveId(BondYieldSpreadListRequestDTO request,
                                                                     List<BankSingleBondYieldSpreadResDTO> bankResponseList,
                                                                     Date spreadDate, Long userid) {
        List<BankSingleBondYieldSpreadResDTO> totalBankList = Lists.newArrayListWithExpectedSize(bankResponseList.size());
        for (BondYieldSpreadItemListRequestDTO bankRequest : request.getBanks()) {
            bankResponseList.stream().filter(bank -> Objects.equals(bankRequest.getBondUniCode(), bank.getBondUniCode())).findFirst().ifPresent(bank -> {
                BankSingleBondYieldSpreadResDTO bankResponse = BeanCopyUtils.copyProperties(bank, BankSingleBondYieldSpreadResDTO.class);
                bankResponse.setCurveId(bankRequest.getCurveId());
                totalBankList.add(bankResponse);
            });
        }
        permissionBase(userid, spreadDate, totalBankList);
        return totalBankList;
    }

    private void permissionBase(Long userid, Date spreadDate, List<? extends BaseSingleBondYieldSpreadResDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(BaseSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toList()));
            for (BaseSingleBondYieldSpreadResDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRatingMappingStr(CommonUtils.desensitized(yieldSpread.getBondImpliedRatingMappingStr(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();

        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (BaseSingleBondYieldSpreadResDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
        }
    }

    private List<UdicBondYieldSpreadResponseDTO> bindingUdicCurveId(BondYieldSpreadListRequestDTO request,
                                                                    List<UdicBondYieldSpreadResponseDTO> udicResponseList, Date spreadDate, Long userid) {
        List<UdicBondYieldSpreadResponseDTO> totalUdicList = Lists.newArrayListWithExpectedSize(udicResponseList.size());
        for (BondYieldSpreadItemListRequestDTO udicRequest : request.getUdics()) {
            udicResponseList.stream().filter(udic -> Objects.equals(udicRequest.getBondUniCode(), udic.getBondUniCode())).findFirst().ifPresent(udic -> {
                UdicBondYieldSpreadResponseDTO udicResponse = BeanCopyUtils.copyProperties(udic, UdicBondYieldSpreadResponseDTO.class);
                udicResponse.setCurveId(udicRequest.getCurveId());
                totalUdicList.add(udicResponse);
            });
        }
        permissionUdic(userid, spreadDate, totalUdicList);
        return totalUdicList;
    }

    private void permissionUdic(Long userid, Date spreadDate, List<UdicBondYieldSpreadResponseDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(UdicBondYieldSpreadResponseDTO::getBondUniCode).collect(Collectors.toList()));
            for (UdicBondYieldSpreadResponseDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRating(CommonUtils.desensitized(yieldSpread.getBondImpliedRating(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();

        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (UdicBondYieldSpreadResponseDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
            yieldSpread.setImpliedRatingLerpYield(CommonUtils.desensitized(yieldSpread.getImpliedRatingLerpYield(), hasPermission));
        }
    }

    private List<InduBondYieldSpreadResponseDTO> bindingInduCurveId(BondYieldSpreadListRequestDTO request,
                                                                    List<InduBondYieldSpreadResponseDTO> induResponseList, Date spreadDate, Long userid) {
        List<InduBondYieldSpreadResponseDTO> totalInduList = Lists.newArrayListWithExpectedSize(induResponseList.size());
        for (BondYieldSpreadItemListRequestDTO induRequest : request.getIndus()) {
            induResponseList.stream().filter(indu -> Objects.equals(induRequest.getBondUniCode(), indu.getBondUniCode())).findFirst().ifPresent(indu -> {
                InduBondYieldSpreadResponseDTO induResponse = BeanCopyUtils.copyProperties(indu, InduBondYieldSpreadResponseDTO.class);
                induResponse.setCurveId(induRequest.getCurveId());
                totalInduList.add(induResponse);
            });
        }
        permissionIndu(userid, spreadDate, totalInduList);
        return totalInduList;
    }

    private void permissionIndu(Long userid, Date spreadDate, List<InduBondYieldSpreadResponseDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(InduBondYieldSpreadResponseDTO::getBondUniCode).collect(Collectors.toList()));
            for (InduBondYieldSpreadResponseDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRating(CommonUtils.desensitized(yieldSpread.getBondImpliedRating(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();

        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (InduBondYieldSpreadResponseDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
            yieldSpread.setImpliedRatingLerpYield(CommonUtils.desensitized(yieldSpread.getImpliedRatingLerpYield(), hasPermission));
        }
    }

    private void checkCodeSize(BondYieldSpreadListRequestDTO request) {
        int size = this.size(request.getCustomBonds()) + this.size(request.getBanks()) + this.size(request.getUdics())
                + this.size(request.getIndus()) + this.size(request.getSecus());
        if (size > MAX_SELECTED_BOND_SIZE) {
            throw new TipsException(String.format("最大只能查询%d支债券", MAX_SELECTED_BOND_SIZE));
        }
    }

    private int size(List<BondYieldSpreadItemListRequestDTO> requests) {
        return CollectionUtils.isEmpty(requests) ? 0 : requests.size();
    }

    private Set<Long> collectBonds(List<BondYieldSpreadItemListRequestDTO> itemListRequests) {
        if (CollectionUtils.isEmpty(itemListRequests)) {
            return Collections.emptySet();
        }
        return itemListRequests.stream().map(BondYieldSpreadItemListRequestDTO::getBondUniCode).collect(Collectors.toSet());
    }

    /**
     * 曲线查询运行器
     *
     * <AUTHOR>
     */
    @FunctionalInterface
    private interface CurveQueryRunner {

        /**
         * 查询曲线数据
         *
         * @param bondUniCode 债券唯一编码
         * @param startDate   开始日期
         * @param endDate     结束日期
         * @return {@link List}<{@link BondYieldSpreadCurveDTO}>
         */
        List<BondYieldSpreadCurveDTO> run(Long bondUniCode, Date startDate, Date endDate);

    }

    /**
     * 检查分片表
     */
    //@Scheduled(initialDelay = 1000, fixedDelay = 3_600_000)
    public void checkShardingTables() {
        Date currentDate = new Date(System.currentTimeMillis());
        Date endDate = new Date(DateUtils.addDays(currentDate, 1).getTime());
        String logicTableName = BondYieldSpreadDO.class.getAnnotation(Table.class).name();
        Collection<String> shardingTableNames = ShardingUtils.getYearShardingTableNames(logicTableName, YieldSpreadConst.BOND_YIELD_SPREAD_START_DATE, endDate);
        for (String shardingTableName : shardingTableNames) {
            bondYieldSpreadDAO.createShardingTable(shardingTableName);
        }
    }

    @Override
    public List<BondYieldSpreadDTO> batchYieldSpreadByBondUniCodes(@Nullable Date spreadDate, List<Long> bondUniCodes) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        spreadDate = Objects.isNull(spreadDate) ? holidayService.lastWorkDay(Date.valueOf(LocalDate.now())) : spreadDate;
        List<BondYieldSpreadShortBO> yieldSpreads = bondYieldSpreadDAO.listBondsYieldSpread(spreadDate, bondUniCodes);
        return BeanCopyUtils.copyList(yieldSpreads, BondYieldSpreadDTO.class);
    }

    @Override
    public BondCreditYieldCurvesResDTO listCreditSpreadCurves(List<Long> bondUniCodes, Date startDate, Date endDate) {
        DateRangeDTO dateRange = redressAndCheckDate(startDate, endDate);
        startDate = dateRange.getStartDate();
        endDate = dateRange.getEndDate();
        List<BondYieldSpreadShortBO> yieldSpreads = bondYieldSpreadDAO.listBondsYieldSpreads(bondUniCodes, startDate, endDate);
        BondCreditYieldCurvesResDTO curves = new BondCreditYieldCurvesResDTO();
        if (CollectionUtils.isEmpty(yieldSpreads)) {
            return curves;
        }
        Map<Date, List<BondYieldSpreadShortBO>> yieldSpreadMap = yieldSpreads.stream()
                .collect(Collectors.groupingBy(BondYieldSpreadShortBO::getSpreadDate, LinkedHashMap::new, Collectors.toList()));
        List<Date> spreadDates = new ArrayList<>(yieldSpreadMap.size());
        List<List<String>> creditSpreads = new ArrayList<>(yieldSpreadMap.size());
        //开头的数据，如果几个BondUniCode的数据都为空，过滤掉
        boolean allEmptyFlag = true;
        for (Map.Entry<Date, List<BondYieldSpreadShortBO>> entry : yieldSpreadMap.entrySet()) {
            if (allEmptyFlag) {
                allEmptyFlag = entry.getValue().stream().allMatch(v -> Objects.nonNull(v.getBondCreditSpread()));
            }
            spreadDates.add(entry.getKey());
            Map<Long, BigDecimal> creditMap = entry.getValue().stream()
                    .collect(ExtCollectors.toMap(BondYieldSpreadShortBO::getBondUniCode, BondYieldSpreadShortBO::getBondCreditSpread, (k1, k2) -> k1));
            List<String> credits = new ArrayList<>(bondUniCodes.size());
            //按照前端的传参顺序来设置值
            for (Long bondUniCode : bondUniCodes) {
                BigDecimal value = creditMap.get(bondUniCode);
                credits.add(Objects.isNull(value) ? null : String.valueOf(value.setScale(YieldSpreadConst.TWO_DECIMAL_PLACE, RoundingMode.HALF_UP)));
            }
            creditSpreads.add(credits);
        }
        curves.setSpreadDates(spreadDates);
        curves.setCreditYieldSpreads(creditSpreads);
        return curves;
    }

    private DateRangeDTO redressAndCheckDate(Date startDate, Date endDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            startDate = Date.valueOf(yesterday.minusYears(BOND_YIELD_SPREAD_CURVE_YEAR_SPAN));
            endDate = Date.valueOf(yesterday);
        }
        if (startDate.compareTo(endDate) > 0) {
            Date temp = startDate;
            startDate = endDate;
            endDate = temp;
        }
        Date startDateAfterManyYear = new Date(DateUtils.addYears(startDate, YIELD_SPREAD_CURVE_YEAR_LIMIT).getTime());
        AssertUtil.isTrue(startDateAfterManyYear.after(endDate), "最多支持查询" + YIELD_SPREAD_CURVE_YEAR_LIMIT + "年的曲线数据");
        return new DateRangeDTO(startDate, endDate);
    }

    @Override
    public int calcBondYieldSpread(Date spreadDate, List<Long> bondUniCodes) {
        int effectRows = 0;
        List<CurveMaturityStructureDTO> curveMaturityStructures = bondPriceApolloService.listSortedCurveMaturityStructures(YieldSpreadConst.CDB_CURVE_UNI_CODE, spreadDate);
        if (CollectionUtils.isEmpty(curveMaturityStructures)) {
            LOGGER.info("[calcBondYieldSpread] getCurveMaturityStructureMap is empty,spreadDate:{}.", spreadDate);
            return effectRows;
        }
        //是否是指定的债
        boolean designatedBonds = CollectionUtils.isNotEmpty(bondUniCodes);
        List<List<Long>> partition = designatedBonds ? Lists.partition(bondUniCodes, YieldSpreadHelper.BATCH_SIZE) : Collections.emptyList();
        Long startBondUniCode = 0L;
        for (int i = 0; ; i++) {
            List<Long> batchBondUniCodes;
            if (CollectionUtils.isNotEmpty(partition)) {
                batchBondUniCodes = i > partition.size() - 1 ? Collections.emptyList() : partition.get(i);
            } else {
                batchBondUniCodes = bondPriceService.listHasValuationBondUniCodes(spreadDate, startBondUniCode);
                startBondUniCode = batchBondUniCodes.stream().max(Long::compare).orElse(Long.MAX_VALUE);
            }
            if (CollectionUtils.isEmpty(batchBondUniCodes)) {
                break;
            }
            effectRows += doCalcBondYieldSpread(spreadDate, batchBondUniCodes, curveMaturityStructures);
        }
        return effectRows;
    }

    private int doCalcBondYieldSpread(Date spreadDate, List<Long> batchBondUniCodes, List<CurveMaturityStructureDTO> curveMaturityStructures) {
        SwThreadPoolWorker<Object> worker = SwThreadPoolWorker.of(EXECUTOR_SERVICE);
        CompletableFuture<List<OnshoreBondInfoDTO>> onshoreBondInfosFuture = worker
                .submit(() -> bondInfoService.listOnshoreBondInfos(Sets.newHashSet(batchBondUniCodes), spreadDate));
        CompletableFuture<Map<Long, CbValuationShortInfoResponseDTO>> cbValuationMapFuture = worker
                .submit(() -> bondPriceService.getCbValuationMap(spreadDate, batchBondUniCodes));
        worker.doWorks(onshoreBondInfosFuture, cbValuationMapFuture);
        List<OnshoreBondInfoDTO> onshoreBondInfos = onshoreBondInfosFuture.join();
        Map<Long, CbValuationShortInfoResponseDTO> cbValuationMap = cbValuationMapFuture.join();
        List<BondYieldSpreadDO> batchInsertList = new ArrayList<>();
        for (OnshoreBondInfoDTO onshoreBondInfo : onshoreBondInfos) {
            Integer remainingTenorDay = onshoreBondInfo.getRemainingTenorDay();
            if (Objects.isNull(remainingTenorDay) || remainingTenorDay < 0) {
                continue;
            }
            BigDecimal bondCreditSpread = this.calcBondCreditSpread(curveMaturityStructures, cbValuationMap.get(onshoreBondInfo.getBondUniCode()), remainingTenorDay);
            if (Objects.nonNull(bondCreditSpread)) {
                batchInsertList.add(buildBondYieldSpreadDO(spreadDate, onshoreBondInfo, bondCreditSpread));
            }
        }
        if (CollectionUtils.isEmpty(batchInsertList)) {
            return 0;
        }
        bondYieldSpreadDAO.physicalDelete(spreadDate, batchBondUniCodes);
        bondYieldSpreadDAO.batchInsert(batchInsertList);
        return batchInsertList.size();
    }

    private BondYieldSpreadDO buildBondYieldSpreadDO(Date spreadDate, OnshoreBondInfoDTO onshoreBondInfo, BigDecimal bondCreditSpread) {
        BondYieldSpreadDO bondYieldSpreadDO = new BondYieldSpreadDO();
        bondYieldSpreadDO.setId(redisService.generatePk(YieldSpreadCacheConst.BOND_YIELD_SPREAD_PK_GENERATE_KEY, spreadDate));
        bondYieldSpreadDO.setComUniCode(onshoreBondInfo.getComUniCode());
        bondYieldSpreadDO.setBondUniCode(onshoreBondInfo.getBondUniCode());
        bondYieldSpreadDO.setBondCode(onshoreBondInfo.getBondCode());
        bondYieldSpreadDO.setSpreadDate(spreadDate);
        bondYieldSpreadDO.setBondCreditSpread(bondCreditSpread);
        bondYieldSpreadDO.setRemainingTenor(onshoreBondInfo.getRemainingTenor());
        bondYieldSpreadDO.setRemainingTenorDay(onshoreBondInfo.getRemainingTenorDay());
        return bondYieldSpreadDO;
    }

    @Nullable
    private BigDecimal calcBondCreditSpread(List<CurveMaturityStructureDTO> curveMaturityStructures,
                                            CbValuationShortInfoResponseDTO cbValuationInfo,
                                            Integer remainingTenorDay) {
        //国开插值收益率
        if (CollectionUtils.isEmpty(curveMaturityStructures) || Objects.isNull(cbValuationInfo)
                || Objects.isNull(cbValuationInfo.getYield()) || Objects.isNull(remainingTenorDay)) {
            return null;
        }
        List<CurveMaturityStructureBO> structures = BeanCopyUtils.copyList(curveMaturityStructures, CurveMaturityStructureBO.class);
        BigDecimal interpolationYield = CalculationHelper.calcInterpolationYield(remainingTenorDay, structures, false);
        if (Objects.isNull(interpolationYield) || interpolationYield.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return cbValuationInfo.getYield().subtract(interpolationYield).multiply(YieldSpreadHelper.BP_WEIGHT)
                .setScale(FOUR_DECIMAL_PLACE, RoundingMode.HALF_UP);
    }

}
