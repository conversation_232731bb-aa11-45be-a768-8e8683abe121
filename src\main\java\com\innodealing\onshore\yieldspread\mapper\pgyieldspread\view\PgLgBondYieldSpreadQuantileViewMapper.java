package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgBondYieldSpreadQuantileViewDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 地方债区域利差分位数视图Mapper
 *
 * <AUTHOR>
 * @create: 2024-11-05
 */
public interface PgLgBondYieldSpreadQuantileViewMapper extends DynamicQueryMapper<PgLgBondYieldSpreadQuantileViewDO> {


    /**
     * 创建地方债区域利差历史分位视图
     *
     * @param lgQuantileMvName 物化视图名称
     * @param startDate        开始日期
     * @param endDate          结束日期
     */
    void createLgBondYieldSpreadQuantileView(String lgQuantileMvName, String startDate, String endDate);

    /**
     * 查询地方债区域利差历史分位视图
     *
     * @param lgQuantileMvName 视图名称
     * @param spreadDate       利差日期
     * @return 数据
     */
    List<PgLgBondYieldSpreadQuantileViewDO> listLgBondYieldSpreadQuantileView(@Param("lgQuantileMvName") String lgQuantileMvName, @Param("spreadDate") String spreadDate);

    /**
     * 删除视图
     *
     * @param tableName 视图名称
     * @return boolean
     */
    @Update("DROP VIEW IF EXISTS ${tableName}")
    Boolean dropMv(@Param("tableName") String tableName);
}
