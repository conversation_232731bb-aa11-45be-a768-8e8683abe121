package com.innodealing.onshore.yieldspread.config.shardingsphere;

import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.hint.HintShardingValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * 利差Sharding策略
 *
 * <AUTHOR>
 */
public class RatingTableHintShardingAlgorithm implements HintShardingAlgorithm<AbstractRatingRouter> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, HintShardingValue<AbstractRatingRouter> shardingValue) {
        String logicTableName = shardingValue.getLogicTableName();
        Collection<AbstractRatingRouter> values = shardingValue.getValues();
        if (CollectionUtils.isEmpty(values)) {
            return Collections.singletonList(logicTableName);
        }
        return values.stream().map(router -> formatShardTableName(logicTableName, router)).collect(Collectors.toList());
    }


    private String formatShardTableName(String logicTableName, AbstractRatingRouter router) {
        String tableNameSuffix = RatingCombinationHelper.getTableNameSuffix(router);
        String tableFormat = RatingCombinationHelper.getTableFormat(router.getClass());
        String format = String.format(tableFormat, logicTableName, tableNameSuffix);
        logger.info("[RatingTableHintShardingAlgorithm] doSharding,shardTableName:{}", format);
        return format;
    }

}
