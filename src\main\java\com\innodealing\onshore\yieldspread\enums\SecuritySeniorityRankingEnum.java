package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 证券公司债求偿顺序
 *
 * <AUTHOR>
 **/
public enum SecuritySeniorityRankingEnum implements ITextValueEnum {
    /**
     * 普通（剔除永续）
     */
    SENIOR(1, "普通"),
    /**
     * 次级（剔除永续）
     */
    SUBORDINATED(2, "次级"),
    /**
     * 永续
     */
    PERPETUAL(3, "永续");

    private final int value;
    private final String text;

    SecuritySeniorityRankingEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }


    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
