package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 地方债区域利差分位数视图DO
 *
 * <AUTHOR>
 * @create: 2024-11-05
 */
@Table(name = "v_lg_bond_yield_spread_quantile")
public class PgLgBondYieldSpreadQuantileViewDO {


    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;

    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;

    /**
     * 区域名称
     */
    @Column
    private String lgAreaName;

    /**
     * 地方债类型： 1 一般债; 2 专项债;  99 其他
     */
    @Column
    private Integer lgBondType;

    /**
     * 提前还本状态 0: 不提前还本 1: 提前还本
     */
    @Column
    private Integer prepaymentStatus;

    /**
     * 资金用途性质: 1 新增; 2 再融资; 3 置换;  99 其他
     */
    @Column
    private Integer fundUseType;

    @Column
    private Integer usingLgBondType;

    @Column
    private Integer usingPrepaymentStatus;

    @Column
    private Integer usingFundUseType;

    /**
     * 1月到期利差
     */
    @Column(name = "credit_spread_1m")
    private BigDecimal creditSpread1M;

    /**
     * 1月到期国债-利差
     */
    @Column(name = "credit_spread_tb_1m")
    private BigDecimal creditSpreadTb1M;

    /**
     * 1月到期收益率
     */
    @Column(name = "cb_yield_1m")
    private BigDecimal cbYield1M;

    /**
     * 3月到期利差
     */
    @Column(name = "credit_spread_3m")
    private BigDecimal creditSpread3M;

    @Column(name = "credit_spread_tb_3m")
    private BigDecimal creditSpreadTb3M;

    /**
     * 3月到期收益率
     */
    @Column(name = "cb_yield_3m")
    private BigDecimal cbYield3M;

    /**
     * 6月到期利差
     */
    @Column(name = "credit_spread_6m")
    private BigDecimal creditSpread6M;

    @Column(name = "credit_spread_tb_6m")
    private BigDecimal creditSpreadTb6M;

    /**
     * 6月到期收益率
     */
    @Column(name = "cb_yield_6m")
    private BigDecimal cbYield6M;

    /**
     * 9月到期利差
     */
    @Column(name = "credit_spread_9m")
    private BigDecimal creditSpread9M;

    @Column(name = "credit_spread_tb_9m")
    private BigDecimal creditSpreadTb9M;

    /**
     * 9月到期收益率
     */
    @Column(name = "cb_yield_9m")
    private BigDecimal cbYield9M;

    /**
     * 1Y到期利差
     */
    @Column(name = "credit_spread_1y")
    private BigDecimal creditSpread1Y;

    /**
     * 1Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_1y")
    private BigDecimal creditSpreadTb1Y;

    /**
     * 1Y到期收益率
     */
    @Column(name = "cb_yield_1y")
    private BigDecimal cbYield1Y;

    /**
     * 2Y到期利差
     */
    @Column(name = "credit_spread_2y")
    private BigDecimal creditSpread2Y;

    /**
     * 2Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_2y")
    private BigDecimal creditSpreadTb2Y;

    /**
     * 2Y到期收益率
     */
    @Column(name = "cb_yield_2y")
    private BigDecimal cbYield2Y;

    /**
     * 3Y到期利差
     */
    @Column(name = "credit_spread_3y")
    private BigDecimal creditSpread3Y;

    /**
     * 3Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_3y")
    private BigDecimal creditSpreadTb3Y;

    /**
     * 3Y到期收益率
     */
    @Column(name = "cb_yield_3y")
    private BigDecimal cbYield3Y;

    /**
     * 5Y到期利差
     */
    @Column(name = "credit_spread_5y")
    private BigDecimal creditSpread5Y;

    /**
     * 5Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_5y")
    private BigDecimal creditSpreadTb5Y;

    /**
     * 5Y到期收益率
     */
    @Column(name = "cb_yield_5y")
    private BigDecimal cbYield5Y;

    /**
     * 7Y到期利差
     */
    @Column(name = "credit_spread_7y")
    private BigDecimal creditSpread7Y;

    /**
     * 7Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_7y")
    private BigDecimal creditSpreadTb7Y;

    /**
     * 7Y到期收益率
     */
    @Column(name = "cb_yield_7y")
    private BigDecimal cbYield7Y;

    /**
     * 10Y到期利差
     */
    @Column(name = "credit_spread_10y")
    private BigDecimal creditSpread10Y;

    /**
     * 10Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_10y")
    private BigDecimal creditSpreadTb10Y;

    /**
     * 10Y到期收益率
     */
    @Column(name = "cb_yield_10y")
    private BigDecimal cbYield10Y;

    /**
     * 15Y到期利差
     */
    @Column(name = "credit_spread_15y")
    private BigDecimal creditSpread15Y;

    /**
     * 15Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_15y")
    private BigDecimal creditSpreadTb15Y;

    /**
     * 15Y到期收益率
     */
    @Column(name = "cb_yield_15y")
    private BigDecimal cbYield15Y;

    /**
     * 20Y到期利差
     */
    @Column(name = "credit_spread_20y")
    private BigDecimal creditSpread20Y;

    /**
     * 20Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_20y")
    private BigDecimal creditSpreadTb20Y;

    /**
     * 20Y到期收益率
     */
    @Column(name = "cb_yield_20y")
    private BigDecimal cbYield20Y;

    /**
     * 30Y到期利差
     */
    @Column(name = "credit_spread_30y")
    private BigDecimal creditSpread30Y;

    /**
     * 30Y到期国债-利差
     */
    @Column(name = "credit_spread_tb_30y")
    private BigDecimal creditSpreadTb30Y;

    /**
     * 30Y到期收益率
     */
    @Column(name = "cb_yield_30y")
    private BigDecimal cbYield30Y;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getLgAreaName() {
        return lgAreaName;
    }

    public void setLgAreaName(String lgAreaName) {
        this.lgAreaName = lgAreaName;
    }

    public Integer getLgBondType() {
        return lgBondType;
    }

    public void setLgBondType(Integer lgBondType) {
        this.lgBondType = lgBondType;
    }

    public Integer getPrepaymentStatus() {
        return prepaymentStatus;
    }

    public void setPrepaymentStatus(Integer prepaymentStatus) {
        this.prepaymentStatus = prepaymentStatus;
    }

    public Integer getFundUseType() {
        return fundUseType;
    }

    public void setFundUseType(Integer fundUseType) {
        this.fundUseType = fundUseType;
    }

    public Integer getUsingLgBondType() {
        return usingLgBondType;
    }

    public void setUsingLgBondType(Integer usingLgBondType) {
        this.usingLgBondType = usingLgBondType;
    }

    public Integer getUsingPrepaymentStatus() {
        return usingPrepaymentStatus;
    }

    public void setUsingPrepaymentStatus(Integer usingPrepaymentStatus) {
        this.usingPrepaymentStatus = usingPrepaymentStatus;
    }

    public Integer getUsingFundUseType() {
        return usingFundUseType;
    }

    public void setUsingFundUseType(Integer usingFundUseType) {
        this.usingFundUseType = usingFundUseType;
    }

    public BigDecimal getCreditSpread1M() {
        return creditSpread1M;
    }

    public void setCreditSpread1M(BigDecimal creditSpread1M) {
        this.creditSpread1M = creditSpread1M;
    }

    public BigDecimal getCbYield1M() {
        return cbYield1M;
    }

    public void setCbYield1M(BigDecimal cbYield1M) {
        this.cbYield1M = cbYield1M;
    }

    public BigDecimal getCreditSpread3M() {
        return creditSpread3M;
    }

    public void setCreditSpread3M(BigDecimal creditSpread3M) {
        this.creditSpread3M = creditSpread3M;
    }

    public BigDecimal getCbYield3M() {
        return cbYield3M;
    }

    public void setCbYield3M(BigDecimal cbYield3M) {
        this.cbYield3M = cbYield3M;
    }

    public BigDecimal getCreditSpread6M() {
        return creditSpread6M;
    }

    public void setCreditSpread6M(BigDecimal creditSpread6M) {
        this.creditSpread6M = creditSpread6M;
    }

    public BigDecimal getCbYield6M() {
        return cbYield6M;
    }

    public void setCbYield6M(BigDecimal cbYield6M) {
        this.cbYield6M = cbYield6M;
    }

    public BigDecimal getCreditSpread9M() {
        return creditSpread9M;
    }

    public void setCreditSpread9M(BigDecimal creditSpread9M) {
        this.creditSpread9M = creditSpread9M;
    }

    public BigDecimal getCbYield9M() {
        return cbYield9M;
    }

    public void setCbYield9M(BigDecimal cbYield9M) {
        this.cbYield9M = cbYield9M;
    }

    public BigDecimal getCreditSpread1Y() {
        return creditSpread1Y;
    }

    public void setCreditSpread1Y(BigDecimal creditSpread1Y) {
        this.creditSpread1Y = creditSpread1Y;
    }

    public BigDecimal getCbYield1Y() {
        return cbYield1Y;
    }

    public void setCbYield1Y(BigDecimal cbYield1Y) {
        this.cbYield1Y = cbYield1Y;
    }

    public BigDecimal getCreditSpread2Y() {
        return creditSpread2Y;
    }

    public void setCreditSpread2Y(BigDecimal creditSpread2Y) {
        this.creditSpread2Y = creditSpread2Y;
    }

    public BigDecimal getCbYield2Y() {
        return cbYield2Y;
    }

    public void setCbYield2Y(BigDecimal cbYield2Y) {
        this.cbYield2Y = cbYield2Y;
    }

    public BigDecimal getCreditSpread3Y() {
        return creditSpread3Y;
    }

    public void setCreditSpread3Y(BigDecimal creditSpread3Y) {
        this.creditSpread3Y = creditSpread3Y;
    }

    public BigDecimal getCbYield3Y() {
        return cbYield3Y;
    }

    public void setCbYield3Y(BigDecimal cbYield3Y) {
        this.cbYield3Y = cbYield3Y;
    }

    public BigDecimal getCreditSpread5Y() {
        return creditSpread5Y;
    }

    public void setCreditSpread5Y(BigDecimal creditSpread5Y) {
        this.creditSpread5Y = creditSpread5Y;
    }

    public BigDecimal getCbYield5Y() {
        return cbYield5Y;
    }

    public void setCbYield5Y(BigDecimal cbYield5Y) {
        this.cbYield5Y = cbYield5Y;
    }

    public BigDecimal getCreditSpread7Y() {
        return creditSpread7Y;
    }

    public void setCreditSpread7Y(BigDecimal creditSpread7Y) {
        this.creditSpread7Y = creditSpread7Y;
    }

    public BigDecimal getCbYield7Y() {
        return cbYield7Y;
    }

    public void setCbYield7Y(BigDecimal cbYield7Y) {
        this.cbYield7Y = cbYield7Y;
    }

    public BigDecimal getCreditSpread10Y() {
        return creditSpread10Y;
    }

    public void setCreditSpread10Y(BigDecimal creditSpread10Y) {
        this.creditSpread10Y = creditSpread10Y;
    }

    public BigDecimal getCbYield10Y() {
        return cbYield10Y;
    }

    public void setCbYield10Y(BigDecimal cbYield10Y) {
        this.cbYield10Y = cbYield10Y;
    }

    public BigDecimal getCreditSpread15Y() {
        return creditSpread15Y;
    }

    public void setCreditSpread15Y(BigDecimal creditSpread15Y) {
        this.creditSpread15Y = creditSpread15Y;
    }

    public BigDecimal getCbYield15Y() {
        return cbYield15Y;
    }

    public void setCbYield15Y(BigDecimal cbYield15Y) {
        this.cbYield15Y = cbYield15Y;
    }

    public BigDecimal getCreditSpread20Y() {
        return creditSpread20Y;
    }

    public void setCreditSpread20Y(BigDecimal creditSpread20Y) {
        this.creditSpread20Y = creditSpread20Y;
    }

    public BigDecimal getCbYield20Y() {
        return cbYield20Y;
    }

    public void setCbYield20Y(BigDecimal cbYield20Y) {
        this.cbYield20Y = cbYield20Y;
    }

    public BigDecimal getCreditSpread30Y() {
        return creditSpread30Y;
    }

    public void setCreditSpread30Y(BigDecimal creditSpread30Y) {
        this.creditSpread30Y = creditSpread30Y;
    }

    public BigDecimal getCbYield30Y() {
        return cbYield30Y;
    }

    public void setCbYield30Y(BigDecimal cbYield30Y) {
        this.cbYield30Y = cbYield30Y;
    }

    public BigDecimal getCreditSpreadTb1M() {
        return creditSpreadTb1M;
    }

    public void setCreditSpreadTb1M(BigDecimal creditSpreadTb1M) {
        this.creditSpreadTb1M = creditSpreadTb1M;
    }

    public BigDecimal getCreditSpreadTb3M() {
        return creditSpreadTb3M;
    }

    public void setCreditSpreadTb3M(BigDecimal creditSpreadTb3M) {
        this.creditSpreadTb3M = creditSpreadTb3M;
    }

    public BigDecimal getCreditSpreadTb6M() {
        return creditSpreadTb6M;
    }

    public void setCreditSpreadTb6M(BigDecimal creditSpreadTb6M) {
        this.creditSpreadTb6M = creditSpreadTb6M;
    }

    public BigDecimal getCreditSpreadTb9M() {
        return creditSpreadTb9M;
    }

    public void setCreditSpreadTb9M(BigDecimal creditSpreadTb9M) {
        this.creditSpreadTb9M = creditSpreadTb9M;
    }

    public BigDecimal getCreditSpreadTb1Y() {
        return creditSpreadTb1Y;
    }

    public void setCreditSpreadTb1Y(BigDecimal creditSpreadTb1Y) {
        this.creditSpreadTb1Y = creditSpreadTb1Y;
    }

    public BigDecimal getCreditSpreadTb2Y() {
        return creditSpreadTb2Y;
    }

    public void setCreditSpreadTb2Y(BigDecimal creditSpreadTb2Y) {
        this.creditSpreadTb2Y = creditSpreadTb2Y;
    }

    public BigDecimal getCreditSpreadTb3Y() {
        return creditSpreadTb3Y;
    }

    public void setCreditSpreadTb3Y(BigDecimal creditSpreadTb3Y) {
        this.creditSpreadTb3Y = creditSpreadTb3Y;
    }

    public BigDecimal getCreditSpreadTb5Y() {
        return creditSpreadTb5Y;
    }

    public void setCreditSpreadTb5Y(BigDecimal creditSpreadTb5Y) {
        this.creditSpreadTb5Y = creditSpreadTb5Y;
    }

    public BigDecimal getCreditSpreadTb7Y() {
        return creditSpreadTb7Y;
    }

    public void setCreditSpreadTb7Y(BigDecimal creditSpreadTb7Y) {
        this.creditSpreadTb7Y = creditSpreadTb7Y;
    }

    public BigDecimal getCreditSpreadTb10Y() {
        return creditSpreadTb10Y;
    }

    public void setCreditSpreadTb10Y(BigDecimal creditSpreadTb10Y) {
        this.creditSpreadTb10Y = creditSpreadTb10Y;
    }

    public BigDecimal getCreditSpreadTb15Y() {
        return creditSpreadTb15Y;
    }

    public void setCreditSpreadTb15Y(BigDecimal creditSpreadTb15Y) {
        this.creditSpreadTb15Y = creditSpreadTb15Y;
    }

    public BigDecimal getCreditSpreadTb20Y() {
        return creditSpreadTb20Y;
    }

    public void setCreditSpreadTb20Y(BigDecimal creditSpreadTb20Y) {
        this.creditSpreadTb20Y = creditSpreadTb20Y;
    }

    public BigDecimal getCreditSpreadTb30Y() {
        return creditSpreadTb30Y;
    }

    public void setCreditSpreadTb30Y(BigDecimal creditSpreadTb30Y) {
        this.creditSpreadTb30Y = creditSpreadTb30Y;
    }
}
