package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 单券利差债券列表响应DTO
 *
 * <AUTHOR>
 */
public class BondYieldSpreadListResponseDTO {

    @ApiModelProperty("产业债")
    private List<InduBondYieldSpreadResponseDTO> indus;
    @ApiModelProperty("城投债")
    private List<UdicBondYieldSpreadResponseDTO> udics;
    @ApiModelProperty("银行债")
    private List<BankSingleBondYieldSpreadResDTO> banks;
    @ApiModelProperty("证券债")
    private List<SecuSingleBondYieldSpreadResDTO> secus;
    @ApiModelProperty("自选债")
    private List<CustomSingleBondYieldSpreadResDTO> customBonds;

    @ApiModelProperty("保险债")
    private List<InsuSingleBondYieldSpreadResDTO> insus;

    public List<InduBondYieldSpreadResponseDTO> getIndus() {
        return Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public void setIndus(List<InduBondYieldSpreadResponseDTO> indus) {
        this.indus = Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public List<UdicBondYieldSpreadResponseDTO> getUdics() {
        return Objects.isNull(udics) ? new ArrayList<>() : new ArrayList<>(udics);
    }

    public void setUdics(List<UdicBondYieldSpreadResponseDTO> udics) {
        this.udics = Objects.isNull(udics) ? new ArrayList<>() : new ArrayList<>(udics);
    }

    public List<BankSingleBondYieldSpreadResDTO> getBanks() {
        return Objects.isNull(banks) ? new ArrayList<>() : new ArrayList<>(banks);
    }

    public void setBanks(List<BankSingleBondYieldSpreadResDTO> banks) {
        this.banks = Objects.isNull(banks) ? new ArrayList<>() : new ArrayList<>(banks);
    }

    public List<SecuSingleBondYieldSpreadResDTO> getSecus() {
        return Objects.isNull(secus) ? new ArrayList<>() : new ArrayList<>(secus);
    }

    public void setSecus(List<SecuSingleBondYieldSpreadResDTO> secus) {
        this.secus = Objects.isNull(secus) ? new ArrayList<>() : new ArrayList<>(secus);
    }

    public List<CustomSingleBondYieldSpreadResDTO> getCustomBonds() {
        return Objects.isNull(customBonds) ? new ArrayList<>() : new ArrayList<>(customBonds);
    }

    public void setCustomBonds(List<CustomSingleBondYieldSpreadResDTO> customBonds) {
        this.customBonds = Objects.isNull(customBonds) ? new ArrayList<>() : new ArrayList<>(customBonds);
    }

    public List<InsuSingleBondYieldSpreadResDTO> getInsus() {
        return Objects.isNull(insus) ? new ArrayList<>() : new ArrayList<>(insus);
    }

    public void setInsus(List<InsuSingleBondYieldSpreadResDTO> insus) {
        this.insus = Objects.isNull(insus) ? new ArrayList<>() : new ArrayList<>(insus);
    }
}
