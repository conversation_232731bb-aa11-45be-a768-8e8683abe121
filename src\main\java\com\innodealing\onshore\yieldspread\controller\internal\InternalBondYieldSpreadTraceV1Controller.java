package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceLineChartPeriodResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceLineChartRatingResponseDTO;
import com.innodealing.onshore.yieldspread.service.BondYieldSpreadTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.Date;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (内部)利差追踪
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 利差追踪-v1版本")
@RestController
@RequestMapping("internal/v1/bond/yield/spread/trace")
public class InternalBondYieldSpreadTraceV1Controller {
    @Resource
    private BondYieldSpreadTraceService bondYieldSpreadTraceService;
    @Resource
    private HttpServletResponse httpServletResponse;

    @ApiOperation(value = "同步利差追踪(历史)")
    @GetMapping("/sync/history")
    public int syncHistBondYieldSpreadTrace(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate) {
        return bondYieldSpreadTraceService.syncHistBondYieldSpreadTrace(startDate);
    }

    @ApiOperation(value = "按曲线代码同步同步利差追踪(历史)")
    @PostMapping("/sync/history/by-curve-codes")
    public int syncHistBondYieldSpreadTraceByCurveCodes(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "curveCodes", value = "曲线代码列表，逗号分隔，为空表示所有曲线") @RequestParam(required = false) String curveCodes) {

        List<Integer> curveCodeList = null;
        if (curveCodes != null && !curveCodes.trim().isEmpty()) {
            curveCodeList = Arrays.stream(curveCodes.split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        return bondYieldSpreadTraceService.syncBondYieldSpreadTraceByCurveCodes(startDate, endDate, curveCodeList);
    }
}
