package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.enums.SpreadQuantileTypeEnum;
import com.innodealing.onshore.yieldspread.model.dto.SpreadDateDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondRatingResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTimeRangeDTO;
import org.springframework.lang.NonNull;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 利差公共服务
 *
 * <AUTHOR>
 */
public interface YieldSpreadCommonService {

    /**
     * 获取利差日期，三个月，六个月
     *
     * @param newSpreadDate 最新利差日期
     * @return {@link SpreadDateDTO} 利差日期对象，包含最新利差日期，三个月，六个月利差日期
     */
    SpreadDateDTO getSpreadDate(Date newSpreadDate);

    /**
     * 债券评级列表
     *
     * @param ratingNames 评级名称集合
     * @return {@link List}<{@link BondRatingResponseDTO}> 债券评级列表响应数据集
     */
    List<BondRatingResponseDTO> listBondRatings(String[] ratingNames);

    /**
     * 获取时间范围列表
     * @param queryDate     查询日期
     * @return 时间范围列表
     */
    List<YieldSpreadTimeRangeDTO> listTimeRange(LocalDate queryDate);

    /**
     * 获取利差日期集合，三个月，六个月
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link Map}<{@link Date 利差日期}, {@link SpreadDateDTO 三个月，六个月日期对象}>
     */
    Map<Date, SpreadDateDTO> getSpreadDateMap(@NonNull Date startDate, @NonNull Date endDate);

    /**
     * 获取指定历史分位的开始时间
     *
     * @param issueDate              利差时间
     * @param spreadQuantileTypeEnum 分位枚举
     * @return 起始日期
     */
    Date getQuantileStartDate(Date issueDate, SpreadQuantileTypeEnum spreadQuantileTypeEnum);

    /**
     * 获取工作日起始/结束的时间范围列表
     * 前闭后开 [start,queryDate)
     * @param queryDate   查询日期
     * @return  时间范围列表
     */
    List<YieldSpreadTimeRangeDTO> getTimeRangeWithWorkDay(LocalDate queryDate);
}
