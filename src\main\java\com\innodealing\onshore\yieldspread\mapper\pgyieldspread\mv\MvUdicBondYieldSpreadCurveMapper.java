package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvUdicBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvUdicBondYieldSpreadCurveDO;
import org.apache.ibatis.annotations.Param;

/**
 * 城投利差曲线-物化视图
 *
 * <AUTHOR>
 */
public interface MvUdicBondYieldSpreadCurveMapper extends PgBaseMapper<MvUdicBondYieldSpreadCurveParameter>, DynamicQueryMapper<MvUdicBondYieldSpreadCurveDO> {

    /**
     * 创建or刷新物化视图定义(评级分片)
     *
     * @param parameter 创建条件
     */
    void createMvRatingRouter(@Param("parameter") MvUdicBondYieldSpreadCurveParameter parameter);

    /**
     * 刷新城投利差曲线物化视图(不包含省，市)
     */
    void refreshMvUdicBondYieldSpreadCurveAll();

    /**
     * 刷新城投利差曲线物化视图(包含省)
     */
    void refreshMvUdicBondYieldSpreadCurveProvince();

    /**
     * 刷新城投利差曲线物化视图(包含市)
     */
    void refreshMvUdicBondYieldSpreadCurveCity();

    /**
     * 刷新城投利差曲线物化视图(区县)
     */
    void refreshMvUdicBondYieldSpreadCurveDistrict();

    /**
     * 刷新城投利差曲线物化视图(不包含省，市),上一天
     */
    void refreshMvUdicBondYieldSpreadCurveAllYesterday();

    /**
     * 刷新城投利差曲线物化视图(包含省),上一天
     */
    void refreshMvUdicBondYieldSpreadCurveProvinceYesterday();

    /**
     * 刷新城投利差曲线物化视图(包含市)，上一天
     */
    void refreshMvUdicBondYieldSpreadCurveCityYesterday();

    /**
     * 刷新城投利差曲线物化视图(区县)，上一天
     */
    void refreshMvUdicBondYieldSpreadCurveDistrictYesterday();

}
