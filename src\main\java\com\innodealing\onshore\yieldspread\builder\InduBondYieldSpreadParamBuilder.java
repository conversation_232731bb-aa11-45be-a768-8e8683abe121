package com.innodealing.onshore.yieldspread.builder;

import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadInduBondImpliedRatingMappingTagEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadCodeTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadComYyRatingMappingTagEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadCurveTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.CurveBondSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.CurveComSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.CurveFavoriteGroupDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.InduCurveCompositionConditionDTO;
import org.apache.commons.lang3.ArrayUtils;

import java.sql.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.util.Objects.isNull;

/**
 * 行业债券利差参数构造器
 *
 * <AUTHOR>
 */
public class InduBondYieldSpreadParamBuilder {
    private static final Integer DEFAULT_PAGE_SIZE = 50;
    private final InduBondYieldSpreadParamDTO induBondYieldSpread;

    /**
     * 构造函数
     */
    public InduBondYieldSpreadParamBuilder() {
        induBondYieldSpread = new InduBondYieldSpreadParamDTO();
    }

    /**
     * 关注组条件请求参数
     *
     * @param favoriteGroup 关注组请求参数
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder favoriteGroup(CurveFavoriteGroupDTO favoriteGroup) {
        if (Objects.isNull(favoriteGroup)) {
            return this;
        }
        induBondYieldSpread.setComUniCodes(favoriteGroup.getComUniCodes());
        induBondYieldSpread.setBondUniCodes(favoriteGroup.getBondUniCodes());
        return this;
    }

    /**
     * 组合条件请求参数（行业，债券类型，外部债项评级，隐含评级，yy评级，企业性质，期限类型，担保状态）
     *
     * @param compositionCondition 组合条件请求参数
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder compositionCondition(InduCurveCompositionConditionDTO compositionCondition) {
        if (Objects.isNull(compositionCondition)) {
            return this;
        }
        induBondYieldSpread.setBondExtRatingMapping(compositionCondition.getBondExtRatingMapping());
        induBondYieldSpread.setSpreadBondType(compositionCondition.getSpreadBondType());

        if (Objects.nonNull(compositionCondition.getBondImpliedRatingMappingTag())) {
            SpreadInduBondImpliedRatingMappingTagEnum bondImpliedRatingMappingTag =
                    ITextValueEnum.getEnum(SpreadInduBondImpliedRatingMappingTagEnum.class, compositionCondition.getBondImpliedRatingMappingTag());
            induBondYieldSpread.setBondImpliedRatingMappings(bondImpliedRatingMappingTag.getMapping());
        }
        Integer[] bondImpliedRatingMappings = compositionCondition.getBondImpliedRatingMappings();
        if (ArrayUtils.isNotEmpty(bondImpliedRatingMappings)){
            induBondYieldSpread.setBondImpliedRatingMappings(bondImpliedRatingMappings);
        }
        induBondYieldSpread.setBondImpliedRatingMappingTag(compositionCondition.getBondImpliedRatingMappingTag());
        induBondYieldSpread.setBusinessFilterNatures(compositionCondition.getBusinessFilterNatures());
        List<Long> indu2UnicodeList = YieldSpreadHelper.getIndu2UnicodeList();
        Long industryCode = compositionCondition.getIndustryCode();
        if (Objects.nonNull(industryCode) && indu2UnicodeList.contains(industryCode)) {
            induBondYieldSpread.setIndustryCode2(industryCode);
        } else {
            induBondYieldSpread.setIndustryCode1(compositionCondition.getIndustryCode());
        }
        induBondYieldSpread.setGuaranteeStatus(compositionCondition.getGuaranteeStatus());
        if (Objects.nonNull(compositionCondition.getComYyRatingMappingTag())) {
            SpreadComYyRatingMappingTagEnum comYyRatingMappingTag = ITextValueEnum.getEnum(SpreadComYyRatingMappingTagEnum.class, compositionCondition.getComYyRatingMappingTag());
            induBondYieldSpread.setComYyRatingMappings(comYyRatingMappingTag.getMapping());
        }
        Integer[] comYyRatingMappings = compositionCondition.getComYyRatingMappings();
        if (ArrayUtils.isNotEmpty(comYyRatingMappings)){
            induBondYieldSpread.setComYyRatingMappings(comYyRatingMappings);
        }
        induBondYieldSpread.setComYyRatingMappingTag(compositionCondition.getComYyRatingMappingTag());
        induBondYieldSpread.setSpreadRemainingTenorTag(compositionCondition.getSpreadRemainingTenorTag());
        return this;
    }

    /**
     * 主体利差请求参数
     *
     * @param comSpread 主体利差请求参数
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder comSpread(CurveComSpreadDTO comSpread) {
        if (Objects.isNull(comSpread)) {
            return this;
        }
        induBondYieldSpread.setComUniCode(comSpread.getComUniCode());
        induBondYieldSpread.setSpreadBondType(comSpread.getSpreadBondType());
        return this;
    }

    /**
     * 单券利差请求参数
     *
     * @param bondSpread 单券利差请求参数
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder bondSpread(CurveBondSpreadDTO bondSpread) {
        if (Objects.isNull(bondSpread)) {
            return this;
        }
        induBondYieldSpread.setBondUniCode(bondSpread.getBondUniCode());
        return this;
    }

    /**
     * 排序
     *
     * @param sort        排序DTO
     * @param defaultSort 默认排序
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder sortOrDefault(SortDTO sort, SortDTO defaultSort) {
        induBondYieldSpread.setSort(ObjectExtensionUtils.getOrDefault(sort, defaultSort));
        return this;
    }


    /**
     * 利差日期
     *
     * @param spreadDate  利差日期
     * @param defaultDate 默认利差日期
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder spreadDateOrDefault(Date spreadDate, Date defaultDate) {
        induBondYieldSpread.setSpreadDate(ObjectExtensionUtils.getOrDefault(spreadDate, defaultDate));
        return this;
    }

    /**
     * 设置搜索的发行人唯一编码|债券唯一编码
     *
     * @param codeType   编码类型 1主体编码,2债券编码
     * @param searchCode 唯一编码
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder searchCode(Integer codeType, Long searchCode) {
        if (Objects.isNull(codeType) || Objects.isNull(searchCode)) {
            return this;
        }
        SpreadCodeTypeEnum codeTypeEnum = ITextValueEnum.getEnum(SpreadCodeTypeEnum.class, codeType);
        if (codeTypeEnum.equals(SpreadCodeTypeEnum.BOND_CODE)) {
            induBondYieldSpread.setBondUniCode(searchCode);
        } else {
            induBondYieldSpread.setComUniCode(searchCode);
        }
        return this;
    }

    /**
     * 设置分页参数
     *
     * @param pageNum  页码
     * @param pageSize 每页数据量
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder page(Integer pageNum, Integer pageSize) {
        induBondYieldSpread.setPageNum(isNull(pageNum) || pageNum <= 0 ? 1 : pageNum);
        induBondYieldSpread.setPageSize(ObjectExtensionUtils.getOrDefault(pageSize, DEFAULT_PAGE_SIZE));
        return this;
    }

    /**
     * 利差曲线属性设置
     *
     * @param spreadCurveType          利差曲线类型 1. 信用利差，2. 超额利差，3. 估值收益率
     * @param displayCdbBenchmarkCurve 是否展示国开基准曲线，是|true, 否|false
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder spreadCurve(Integer spreadCurveType, Boolean displayCdbBenchmarkCurve) {
        SpreadCurveTypeEnum spreadCurveTypeEnum = Optional.ofNullable(spreadCurveType)
                .map(curveType -> ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, spreadCurveType))
                .orElse(SpreadCurveTypeEnum.CREDIT_SPREAD);
        induBondYieldSpread.setSpreadCurveType(spreadCurveTypeEnum);
        induBondYieldSpread.setDisplayCdbBenchmarkCurve(displayCdbBenchmarkCurve);
        return this;
    }

    /**
     * 设置发行人唯一编码集合
     *
     * @param comUniCodes 发行人唯一编码
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder comUniCodes(Long[] comUniCodes) {
        induBondYieldSpread.setComUniCodes(comUniCodes);
        return this;
    }

    /**
     * 设置债券唯一编码集合
     *
     * @param bondUniCodes 债券唯一编码
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder bondUniCodes(Long[] bondUniCodes) {
        induBondYieldSpread.setBondUniCodes(bondUniCodes);
        return this;
    }

    /**
     * 利差日期跨度
     *
     * @param startSpreadDate 开始利差日期
     * @param endSpreadDate   结束利差日期
     * @return {@link InduBondYieldSpreadParamBuilder} 行业债券利差参数构造器
     */
    public InduBondYieldSpreadParamBuilder spanSpreadDate(Date startSpreadDate, Date endSpreadDate) {
        induBondYieldSpread.setStartSpreadDate(startSpreadDate);
        induBondYieldSpread.setEndSpreadDate(endSpreadDate);
        return this;
    }

    /**
     * 构建行业债券利差参数对象
     *
     * @return <{@link InduBondYieldSpreadParamDTO}> 行业债券利差参数对象
     */
    public InduBondYieldSpreadParamDTO build() {
        return induBondYieldSpread;
    }
}