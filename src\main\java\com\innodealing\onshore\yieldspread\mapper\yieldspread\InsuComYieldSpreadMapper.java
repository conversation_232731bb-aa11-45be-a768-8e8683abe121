package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.bo.InsuComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.InsuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;

/**
 * 保险主体利差Mapper
 *
 * <AUTHOR>
 **/
public interface InsuComYieldSpreadMapper extends DynamicQueryMapper<InsuComYieldSpreadDO> {
    /**
     * 获取保险债券主体利差
     *
     * @param isNewest 是否最新数据
     * @param param    查询参数
     * @return 主体利差
     */
    List<InsuComYieldSpreadBO> listComYieldSpreads(@Param("isNewest") boolean isNewest, @Param("params") InsuYieldSearchParam param);

    /**
     * 获取主体数量
     *
     * @param param 查询参数
     * @return 主体数量
     */
    Long countComYieldSpread(@Param("params") InsuYieldSearchParam param);

    /**
     * 获取指定时间范围内的某个发行时间 对应的分位统计的数据
     *
     * @param startDate      时间范围开始
     * @param endDate        时间范围结束
     * @param issueDate      发行时间
     * @param comUniCodeList 指定主体列表
     * @return 分位 信用利差(全部) 超额利差(全部)统计的数据
     */
    List<ComYieldSpreadQuantileViewDO> listComYieldQuantileStatisticsViews(@Param("startDate") Date startDate,
                                                                           @Param("endDate") Date endDate,
                                                                           @Param("issueDate") Date issueDate,
                                                                           @Param("comUniCodeList") List<Long> comUniCodeList);
}
