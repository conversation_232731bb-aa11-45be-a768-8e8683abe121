package com.innodealing.onshore.yieldspread.dao.pgshard.partition;

import com.github.wz2cool.dynamic.BaseFilterDescriptor;
import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.UdicShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondShardYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.UdicShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 城投利差曲线分区
 *
 * <AUTHOR>
 */
@Repository
public class UdicBondShardYieldSpreadCurveRepository {
    @Resource
    private UdicShardBondYieldSpreadCurveMapper udicShardBondYieldSpreadCurveMapper;


    /**
     * 城投利差曲线评级分片查询
     * @param searchParameter 查询参数
     * @param router 路由
     * @return list
     */
    public List<UdicShardBondYieldSpreadCurveDO> listUdicShardBondYieldSpreadCurves(UdicBondShardYieldSpreadParamDTO searchParameter, AbstractRatingRouter router) {
        BaseFilterDescriptor<UdicShardBondYieldSpreadCurveDO>[] baseFilterDescriptors = this.listFilters(searchParameter).getFilters();
        DynamicQuery<UdicShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(UdicShardBondYieldSpreadCurveDO.class)
                .and(baseFilterDescriptors);
        return udicShardBondYieldSpreadCurveMapper.selectByDynamicQueryRouter(query, router);
    }


    /**
     * 城投利差曲线评级分片查询
     * @param searchParameter 查询参数
     * @param router 路由
     * @return optional
     */
    public Optional<UdicShardBondYieldSpreadCurveDO> firstUdicShardBondYieldSpreadCurve(UdicBondShardYieldSpreadParamDTO searchParameter, AbstractRatingRouter router) {
        BaseFilterDescriptor<UdicShardBondYieldSpreadCurveDO>[] baseFilterDescriptors = this.listFilters(searchParameter).getFilters();
        DynamicQuery<UdicShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(UdicShardBondYieldSpreadCurveDO.class)
                .and(baseFilterDescriptors);
        return udicShardBondYieldSpreadCurveMapper.selectFirstByDynamicQueryRouter(query, router);
    }

    private FilterGroupDescriptor<UdicShardBondYieldSpreadCurveDO> listFilters(UdicBondShardYieldSpreadParamDTO request) {
        Optional.ofNullable(request.getSpreadRemainingTenorTag()).ifPresent(request::setRemainingTenor);
        Optional.ofNullable(request.getGuaranteeStatus()).ifPresent(request::setGuaranteedStatus);
        return FilterGroupDescriptor.create(UdicShardBondYieldSpreadCurveDO.class)
                .and(nonNull(request.getSpreadBondType()), UdicShardBondYieldSpreadCurveDO::getSpreadBondType, isEqual(request.getSpreadBondType()))
                .and(isNull(request.getSpreadBondType()), UdicShardBondYieldSpreadCurveDO::getUsingSpreadBondType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getBondExtRatingMapping()), UdicShardBondYieldSpreadCurveDO::getBondExtRatingMapping, isEqual(request.getBondExtRatingMapping()))
                .and(isNull(request.getBondExtRatingMapping()), UdicShardBondYieldSpreadCurveDO::getUsingBondExtRatingMapping, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getGuaranteedStatus()), UdicShardBondYieldSpreadCurveDO::getGuaranteedStatus, isEqual(request.getGuaranteedStatus()))
                .and(isNull(request.getGuaranteedStatus()), UdicShardBondYieldSpreadCurveDO::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getRemainingTenor()), UdicShardBondYieldSpreadCurveDO::getSpreadRemainingTenorTag, isEqual(request.getRemainingTenor()))
                .and(isNull(request.getRemainingTenor()), UdicShardBondYieldSpreadCurveDO::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getAdministrativeDivision()), UdicShardBondYieldSpreadCurveDO::getAdministrativeDivision, isEqual(request.getAdministrativeDivision()))
                .and(isNull(request.getAdministrativeDivision()), UdicShardBondYieldSpreadCurveDO::getUsingAdministrativeDivision, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getStartSpreadDate()), UdicShardBondYieldSpreadCurveDO::getSpreadDate, greaterThanOrEqual(request.getStartSpreadDate()))
                .and(nonNull(request.getEndSpreadDate()), UdicShardBondYieldSpreadCurveDO::getSpreadDate, lessThanOrEqual(request.getEndSpreadDate()))
                .and(nonNull(request.getSpreadDate()), UdicShardBondYieldSpreadCurveDO::getSpreadDate, isEqual(request.getSpreadDate()))
                .and(nonNull(request.getProvinceUniCode()), UdicShardBondYieldSpreadCurveDO::getOperatorLevel, isEqual(request.getProvinceUniCode()))
                .and(nonNull(request.getCityUniCode()), UdicShardBondYieldSpreadCurveDO::getOperatorLevel, isEqual(request.getCityUniCode()))
                ;
    }
}
