package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.enums.SpreadCurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadQuantileTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgLgBondYieldSpreadQuantileMapper;
import com.innodealing.onshore.yieldspread.model.bo.YieldLgDataBO;
import com.innodealing.onshore.yieldspread.model.dto.request.LgSpreadBaseRequestDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadQuantileDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 地方债区域利差历史分位 DAO
 *
 * <AUTHOR>
 * @create: 2024-11-05
 */
@Repository
public class PgLgBondYieldSpreadQuantileDAO {
    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d:%s:%s:%s:%d:%d:%d";
    private static final String LG_BOND_YIELD_SPREAD_QUANTILE_PK = "yieldSpread:lgBondYieldSpreadQuantilePk";
    private static final String PLACEHOLDER = "-";
    @Resource
    private PgLgBondYieldSpreadQuantileMapper pgLgBondYieldSpreadQuantileMapper;
    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;
    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;

    /**
     * 保存地方债区域利差追踪-历史分位数据集
     *
     * @param spreadDate              利差日期
     * @param yieldSpreadQuantileList 地方债区域利差追踪-历史分位数据集
     * @return int 保存行数
     */
    public int saveLgBondYieldSpreadQuantileList(@NonNull Date spreadDate, List<PgLgBondYieldSpreadQuantileDO> yieldSpreadQuantileList) {
        if (CollectionUtils.isEmpty(yieldSpreadQuantileList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        DynamicQuery<PgLgBondYieldSpreadQuantileDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadQuantileDO.class)
                .and(PgLgBondYieldSpreadQuantileDO::getSpreadDate, isEqual(spreadDate));
        Map<String, PgLgBondYieldSpreadQuantileDO> curveCodeIssueDateMap = pgLgBondYieldSpreadQuantileMapper.selectByDynamicQuery(query).stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        List<PgLgBondYieldSpreadQuantileDO> insertList = new ArrayList<>();
        List<PgLgBondYieldSpreadQuantileDO> updateList = new ArrayList<>();
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        for (PgLgBondYieldSpreadQuantileDO lgBondYieldSpreadQuantileDO : yieldSpreadQuantileList) {
            String key = this.getKey(lgBondYieldSpreadQuantileDO);
            if (curveCodeIssueDateMap.containsKey(key)) {
                PgLgBondYieldSpreadQuantileDO existLgBondYieldQuantile = curveCodeIssueDateMap.get(key);
                lgBondYieldSpreadQuantileDO.setId(existLgBondYieldQuantile.getId());
                lgBondYieldSpreadQuantileDO.setCreateTime(null);
                lgBondYieldSpreadQuantileDO.setUpdateTime(now);
                updateList.add(lgBondYieldSpreadQuantileDO);
            } else {
                lgBondYieldSpreadQuantileDO.setId(redisService.generatePk(LG_BOND_YIELD_SPREAD_QUANTILE_PK, lgBondYieldSpreadQuantileDO.getSpreadDate()));
                lgBondYieldSpreadQuantileDO.setCreateTime(now);
                lgBondYieldSpreadQuantileDO.setUpdateTime(now);
                insertList.add(lgBondYieldSpreadQuantileDO);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param pgLgBondYieldSpreadQuantileDO 地方债区域利差追踪-历史分位数据集
     * @return {@link String} key值
     */
    private String getKey(PgLgBondYieldSpreadQuantileDO pgLgBondYieldSpreadQuantileDO) {
        Integer quantileType = pgLgBondYieldSpreadQuantileDO.getQuantileType();
        Date spreadDate = pgLgBondYieldSpreadQuantileDO.getSpreadDate();
        Long comUniCode = pgLgBondYieldSpreadQuantileDO.getComUniCode();
        String lgBondType = Optional.ofNullable(pgLgBondYieldSpreadQuantileDO.getLgBondType()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        String prepaymentStatus = Optional.ofNullable(pgLgBondYieldSpreadQuantileDO.getPrepaymentStatus()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        String fundUseType = Optional.ofNullable(pgLgBondYieldSpreadQuantileDO.getFundUseType()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        Integer usingLgBondType = pgLgBondYieldSpreadQuantileDO.getUsingLgBondType();
        Integer usingPrepaymentStatus = pgLgBondYieldSpreadQuantileDO.getUsingPrepaymentStatus();
        Integer usingFundUseType = pgLgBondYieldSpreadQuantileDO.getUsingFundUseType();
        return String.format(NAMESPACE_KEY_PLACEHOLDER, quantileType, spreadDate.getTime(), comUniCode, lgBondType, prepaymentStatus, fundUseType,
                usingLgBondType, usingPrepaymentStatus, usingFundUseType);
    }

    /**
     * 批量更新
     *
     * @param updateList 利差追踪-历史分位数据集
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgLgBondYieldSpreadQuantileDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgLgBondYieldSpreadQuantileMapper> updateBatchAction =
                MapperBatchAction.create(PgLgBondYieldSpreadQuantileMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgLgBondYieldSpreadQuantileDO quantile : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgLgBondYieldSpreadQuantileDO> updateQuery = DynamicQuery.createQuery(PgLgBondYieldSpreadQuantileDO.class)
                        .and(PgLgBondYieldSpreadQuantileDO::getId, isEqual(quantile.getId()));
                mapper.updateSelectiveByDynamicQuery(quantile, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 利差追踪-历史分位数据集
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgLgBondYieldSpreadQuantileDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgLgBondYieldSpreadQuantileMapper> insertBatchAction =
                MapperBatchAction.create(PgLgBondYieldSpreadQuantileMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgLgBondYieldSpreadQuantileDO quantile : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(quantile));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 获取地方债区域利差历史分位
     *
     * @param areaConfigList   区域列表
     * @param requestDTO       请求参数
     * @param quantileTypeEnum 分位类型 {@link SpreadQuantileTypeEnum}
     * @return 数据
     */
    public List<YieldLgDataBO> listYieldSpreadLgQuantile(@NotEmpty List<Long> areaConfigList, @NotNull LgSpreadBaseRequestDTO requestDTO,
                                                         @NotNull SpreadQuantileTypeEnum quantileTypeEnum) {
        FilterGroupDescriptor<PgLgBondYieldSpreadQuantileDO> descriptor = requestDTO.listLgSpreadBaseFilters(PgLgBondYieldSpreadQuantileDO.class);
        DynamicQuery<PgLgBondYieldSpreadQuantileDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadQuantileDO.class).ignore(PgLgBondYieldSpreadQuantileDO::getId,
                        PgLgBondYieldSpreadQuantileDO::getDeleted, PgLgBondYieldSpreadQuantileDO::getCreateTime, PgLgBondYieldSpreadQuantileDO::getUpdateTime)
                .and(PgLgBondYieldSpreadQuantileDO::getComUniCode, in(areaConfigList))
                .and(PgLgBondYieldSpreadQuantileDO::getQuantileType, isEqual(quantileTypeEnum.getValue()))
                .and(descriptor);
        List<PgLgBondYieldSpreadQuantileDO> pgLgBondYieldSpreadQuantileDOs = pgLgBondYieldSpreadQuantileMapper.selectByDynamicQuery(query);
        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, requestDTO.getSpreadDataType());
        return pgLgBondYieldSpreadQuantileDOs.stream()
                .map(baseDO -> baseDO.convertToBO(spreadCurveTypeEnum).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgLgBondYieldSpreadQuantileDO> dynamicQuery = DynamicQuery.createQuery(PgLgBondYieldSpreadQuantileDO.class)
                .orderBy(PgLgBondYieldSpreadQuantileDO::getSpreadDate, SortDirections::desc);
        return pgLgBondYieldSpreadQuantileMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgLgBondYieldSpreadQuantileDO::getSpreadDate);
    }
}
