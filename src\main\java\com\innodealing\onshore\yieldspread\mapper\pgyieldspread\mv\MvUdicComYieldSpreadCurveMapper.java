package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvUdicBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvUdicComYieldSpreadCurveDO;

/**
 * 城投利差曲线-物化视图
 *
 * <AUTHOR>
 */
public interface MvUdicComYieldSpreadCurveMapper extends PgBaseMapper<MvUdicBondYieldSpreadCurveParameter>, DynamicQueryMapper<MvUdicComYieldSpreadCurveDO> {
}
