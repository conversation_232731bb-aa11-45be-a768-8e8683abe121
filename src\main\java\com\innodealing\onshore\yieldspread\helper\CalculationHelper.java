package com.innodealing.onshore.yieldspread.helper;

import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.model.bo.CurveMaturityStructureBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.ONE_HUNDRED;

/**
 * 计算工具类
 *
 * <AUTHOR>
 */
public final class CalculationHelper {

    private CalculationHelper() {
    }

    private static final BigDecimal MV_SPREAD_BP_WEIGHT = BigDecimal.valueOf(100_000);

    public static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");

    private static final int DEFAULT_SCALE = 2;

    private static final int TWO_NUMBER = 2;

    /**
     * 相减
     *
     * @param minuend    被减数
     * @param subtracted 减数
     * @return {@link Optional}<{@link BigDecimal}> 相减之后的值
     */
    public static Optional<BigDecimal> subtract(BigDecimal minuend, BigDecimal subtracted) {
        if (Objects.isNull(minuend) || Objects.isNull(subtracted)) {
            return Optional.empty();
        }
        return Optional.of(minuend.subtract(subtracted));
    }

    /**
     * 除以1w
     *
     * @param sourceData 原始数据
     * @return 转换后数据
     */
    public static Optional<BigDecimal> divideTenThousand(BigDecimal sourceData) {
        if (Objects.isNull(sourceData)) {
            return Optional.empty();
        }
        return Optional.of(sourceData.divide(TEN_THOUSAND, DEFAULT_SCALE, RoundingMode.HALF_UP));
    }

    /**
     * 除以10w
     *
     * @param sourceData 原始数据
     * @return 转换后数据
     */
    public static Optional<BigDecimal> divideHundredThousand(BigDecimal sourceData) {
        return divideHundredThousand(sourceData, DEFAULT_SCALE);
    }

    /**
     * 除以10w
     *
     * @param sourceData 原始数据
     * @return 转换后数据
     */
    public static Optional<BigDecimal> divideHundredThousand(Long sourceData) {
        return divideHundredThousand(sourceData, DEFAULT_SCALE);
    }

    /**
     * 除以10w
     *
     * @param sourceData 原始数据
     * @param scale      保留精度
     * @return 转换后数据
     */
    public static Optional<BigDecimal> divideHundredThousand(BigDecimal sourceData, int scale) {
        if (Objects.isNull(sourceData)) {
            return Optional.empty();
        }
        return Optional.of(sourceData.divide(MV_SPREAD_BP_WEIGHT, scale, RoundingMode.HALF_UP));
    }

    /**
     * 除以10w
     *
     * @param sourceData 原始数据
     * @param scale      保留精度
     * @return 转换后数据
     */
    public static Optional<BigDecimal> divideHundredThousand(Long sourceData, int scale) {
        if (Objects.isNull(sourceData)) {
            return Optional.empty();
        }
        return Optional.of(BigDecimal.valueOf(sourceData).divide(MV_SPREAD_BP_WEIGHT, scale, RoundingMode.HALF_UP));
    }


    /**
     * 安全计算百分排位 ,去掉百分号 的历史分位值 (数值*100)
     *
     * @param lessCount 小于要计算值的数量
     * @param count     要计算的值 对应的区间总数
     * @param scale     相除结果小数点位数
     * @return 结果
     */
    public static Optional<BigDecimal> safeCalPercentRankIgnore(Integer lessCount, Integer count, int scale) {
        if (Objects.isNull(lessCount) || Objects.isNull(count)) {
            return Optional.empty();
        }
        if (count.equals(1)) {
            return Optional.of(BigDecimal.valueOf(1));
        }
        int dividend = count - 1;
        return Optional.of(BigDecimal.valueOf(lessCount)
                .divide(BigDecimal.valueOf(dividend), scale + TWO_NUMBER, RoundingMode.HALF_UP)
                .multiply(ONE_HUNDRED)
                .setScale(scale, RoundingMode.HALF_UP));
    }

    /**
     * 小数相除
     *
     * @param divider  除数
     * @param dividend 被除数
     * @param scale    相除结果小数点位数
     * @return 结果
     */
    public static Optional<BigDecimal> safeDivide(BigDecimal divider, BigDecimal dividend, int scale) {
        if (Objects.isNull(divider) || Objects.isNull(dividend)) {
            return Optional.empty();
        }
        return Optional.of(divider.divide(dividend, scale, RoundingMode.HALF_UP));
    }


    /**
     * 计算中位数
     *
     * @param values 值
     * @return {@link Optional}<{@link BigDecimal}> 中位数
     */
    public static Optional<BigDecimal> calcMedian(BigDecimal[] values) {
        if (ArrayUtils.isEmpty(values)) {
            return Optional.empty();
        }
        values = Stream.of(values).filter(Objects::nonNull).sorted().toArray(BigDecimal[]::new);
        if (ArrayUtils.isEmpty(values)) {
            return Optional.empty();
        }
        int length = values.length;
        BigDecimal median;
        if (length % TWO_NUMBER == 0) {
            median = (values[(length / TWO_NUMBER) - 1].add(values[length / TWO_NUMBER])).divide(BigDecimal.valueOf(TWO_NUMBER), DEFAULT_SCALE, RoundingMode.HALF_UP);
        } else {
            median = values[length / TWO_NUMBER];
        }
        return Optional.of(median);
    }

    /**
     * 计算插值收益率
     *
     * @param remainingTenorDay    剩余期限天数
     * @param structures           曲线期限结构
     * @param sortByRemainingTenor 是否要根据RemainingTenor来排序structures，计算必须要保证有序
     * @return 插值收益率
     */
    @Nullable
    @SuppressWarnings("squid:MethodCyclomaticComplexity")
    public static BigDecimal calcInterpolationYield(Integer remainingTenorDay, List<CurveMaturityStructureBO> structures, Boolean sortByRemainingTenor) {
        if (Objects.isNull(remainingTenorDay) || remainingTenorDay < 0 || CollectionUtils.isEmpty(structures)) {
            return null;
        }
        //过滤掉剩余期限为空的
        List<CurveMaturityStructureBO> compliantStructures = structures.stream().filter(v -> Objects.nonNull(v.getRemainingTenor())).collect(Collectors.toList());
        compliantStructures = Objects.equals(sortByRemainingTenor, Boolean.TRUE) ?
                compliantStructures.stream().sorted(Comparator.comparing(CurveMaturityStructureBO::getRemainingTenor)).collect(Collectors.toList()) : compliantStructures;
        BigDecimal remainingTenorYear = convertRemainingTenorDayToYear(remainingTenorDay);
        //超过上限
        BigDecimal upRemainingTenor = compliantStructures.get(compliantStructures.size() - 1).getRemainingTenor();
        if (Objects.nonNull(upRemainingTenor) && Objects.nonNull(remainingTenorYear) && remainingTenorYear.compareTo(upRemainingTenor) > 0) {
            return null;
        }
        IndexData indexData = indexedBinarySearch(compliantStructures, remainingTenorYear, false);
        if (Objects.nonNull(indexData.getTargetIndex())) {
            return compliantStructures.get(indexData.getTargetIndex()).getYield();
        }
        CurveMaturityStructureBO down = compliantStructures.get(indexData.getNearestTargetDownIndex());
        BigDecimal yieldLow = Objects.isNull(down) ? BigDecimal.ZERO : down.getYield();
        BigDecimal remainingTenorLow = Objects.isNull(down) ? BigDecimal.ZERO : down.getRemainingTenor();
        CurveMaturityStructureBO up = compliantStructures.get(indexData.getNearestTargetUpIndex());
        return doCalcInterpolationYield(Objects.isNull(up) ? null : up.getYield(), yieldLow,
                remainingTenorYear, Objects.isNull(up) ? null : up.getRemainingTenor(), remainingTenorLow);
    }

    /**
     * 计算插值收益率 y1+(y2-y1)(3.5-3)/(5-3) 计算规则
     *
     * @param yieldUp            收益率上限
     * @param yieldLow           收益率下限
     * @param remainingTenorYear 剩余期限(年)
     * @param remainingTenorUp   剩余期限上限(年)
     * @param remainingTenorLow  剩余期限下限(年)
     * @return 插值收益率
     */
    @SuppressWarnings("squid:S1067")
    private static BigDecimal doCalcInterpolationYield(BigDecimal yieldUp, BigDecimal yieldLow,
                                                       BigDecimal remainingTenorYear, BigDecimal remainingTenorUp,
                                                       BigDecimal remainingTenorLow) {
        if (Objects.isNull(yieldUp) || Objects.isNull(yieldLow) || Objects.isNull(remainingTenorYear) || Objects.isNull(remainingTenorUp) || Objects.isNull(remainingTenorLow)) {
            return null;
        }
        BigDecimal remainingTenorUpSubRemainingTenorLow = remainingTenorUp.subtract(remainingTenorLow);
        if (BigDecimal.ZERO.equals(remainingTenorUpSubRemainingTenorLow)) {
            return null;
        }
        BigDecimal remainingTenorSubRemainingTenorLow = remainingTenorYear.subtract(remainingTenorLow);
        BigDecimal yieldUpSubYieldLow = yieldUp.subtract(yieldLow);
        BigDecimal yieldDiffMclRemainingTenorDiff = yieldUpSubYieldLow.multiply(remainingTenorSubRemainingTenorLow);
        BigDecimal yieldDiff = yieldDiffMclRemainingTenorDiff.divide(remainingTenorUpSubRemainingTenorLow, YieldSpreadConst.SIX_DECIMAL_PLACE, RoundingMode.HALF_UP);
        return yieldLow.add(yieldDiff).setScale(YieldSpreadConst.SIX_DECIMAL_PLACE, RoundingMode.HALF_UP);
    }

    /**
     * 债券剩余期限 天数 转化为 年
     *
     * @param remainingTenorDay 剩余天数
     * @return 债券剩余期限(年)
     */
    public static BigDecimal convertRemainingTenorDayToYear(Integer remainingTenorDay) {
        if (Objects.isNull(remainingTenorDay)) {
            return null;
        }
        return BigDecimal.valueOf(remainingTenorDay).divide(BigDecimal.valueOf(YieldSpreadConst.ONE_YEAR), YieldSpreadConst.TWO_DECIMAL_PLACE, RoundingMode.HALF_UP);
    }

    /**
     * 二分查找targetValue在list中的索引，如果没找到就返回相邻索引
     *
     * @param list        查找列表
     * @param targetValue 目标值
     * @param needSort    是否需要排序，如果传入的list是无序的，这里必须指定为true
     * @param <T>         extends Comparable<? super T>
     * @return IndexData
     */
    @SuppressWarnings("squid:S109")
    public static <T> IndexData indexedBinarySearch(List<? extends Comparable<? super T>> list, T targetValue, Boolean needSort) {
        if (CollectionUtils.isEmpty(list) || Objects.isNull(targetValue)) {
            return new IndexData();
        }
        list = Objects.nonNull(needSort) && needSort ? list.stream().sorted().collect(Collectors.toList()) : list;
        int low = 0;
        int high = list.size() - 1;
        IndexData indexData = new IndexData();
        while (low <= high) {
            int mid = (low + high) / 2;
            Comparable<? super T> midVal = list.get(mid);
            int cmp = midVal.compareTo(targetValue);
            if (cmp < 0) {
                indexData.setNearestTargetDownIndex(mid);
                low = mid + 1;
            } else if (cmp > 0) {
                indexData.setNearestTargetUpIndex(mid);
                high = mid - 1;
            } else {
                indexData.setTargetIndex(mid);
                return indexData;
            }
        }
        return indexData;
    }

    static class IndexData {

        /**
         * 目标值索引
         */
        private Integer targetIndex;

        /**
         * 接近目标值小一点的索引（数组中不存在目标值的情况）
         */
        private Integer nearestTargetDownIndex;

        /**
         * 接近目标值大一点的索引（数组中不存在目标值的情况）
         */
        private Integer nearestTargetUpIndex;

        public Integer getTargetIndex() {
            return targetIndex;
        }

        public void setTargetIndex(Integer targetIndex) {
            this.targetIndex = targetIndex;
        }

        public Integer getNearestTargetDownIndex() {
            return nearestTargetDownIndex;
        }

        public void setNearestTargetDownIndex(Integer nearestTargetDownIndex) {
            this.nearestTargetDownIndex = nearestTargetDownIndex;
        }

        public Integer getNearestTargetUpIndex() {
            return nearestTargetUpIndex;
        }

        public void setNearestTargetUpIndex(Integer nearestTargetUpIndex) {
            this.nearestTargetUpIndex = nearestTargetUpIndex;
        }

    }

}
