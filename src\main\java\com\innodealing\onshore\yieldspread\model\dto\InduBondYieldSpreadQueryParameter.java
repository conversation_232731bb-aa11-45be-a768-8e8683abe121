package com.innodealing.onshore.yieldspread.model.dto;

import java.io.Serializable;
import java.sql.Date;
import java.util.Objects;

/**
 * 行业债券利差查询参数DTO
 *
 * <AUTHOR>
 */
public class InduBondYieldSpreadQueryParameter implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主体唯一编码集合
     */
    private Long[] comUniCodes;
    /**
     * 债券唯一编码集合
     */
    private Long[] bondUniCodes;
    /**
     * 一级行业编码
     */
    private Long industryCode1;
    /**
     * 二级行业编码
     */
    private Long industryCode2;
    /**
     * 债券类型: 1公募; 0私募; 2永续债
     */
    private Integer spreadBondType;
    /**
     * 债项评级 AAA,AA+,AA)
     */
    private Integer bondExtRatingMapping;
    /**
     * 隐含评级(AAA+,AAA,AAA-,AA+,AA,AA-,A+,A,A-)
     */
    private Integer[] bondImpliedRatingMappings;
    /**
     * yy评级(投资级:1、2、3、4、5,投机级:6、7、8、风险级:9、10)
     */
    private Integer[] comYyRatingMappings;
    /**
     * 隐含评级标签
     */
    private Integer bondImpliedRatingMappingTag;
    /**
     * yy评级标签
     */
    private Integer comYyRatingMappingTag;
    /**
     * 企业性质 1央企,2国企,6民企
     */
    private Integer[] businessFilterNatures;
    /**
     * 剩余期限(1,2,3,4,5)
     */
    private Integer spreadRemainingTenorTag;
    /**
     * 担保状态: 0: 无, 1: 有
     */
    protected Integer guaranteeStatus;
    /**
     * 主体唯一编码
     */
    private Long comUniCode;
    /**
     * 债券唯一编码
     */
    private Long bondUniCode;
    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 排序字段
     */
    private String propertyName;
    /**
     * 排序方向
     */
    private String sortDirection;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数据量
     */
    private Integer pageSize;

    /**
     * 分页查询开始索引
     */
    private Integer startIndex;
    /**
     * 年
     */
    private Integer year;

    public Long[] getComUniCodes() {
        return Objects.isNull(comUniCodes) ? new Long[0] : comUniCodes.clone();
    }

    public void setComUniCodes(Long[] comUniCodes) {
        this.comUniCodes = Objects.isNull(comUniCodes) ? new Long[0] : comUniCodes.clone();
    }

    public Long[] getBondUniCodes() {
        return Objects.isNull(bondUniCodes) ? new Long[0] : bondUniCodes.clone();
    }

    public void setBondUniCodes(Long[] bondUniCodes) {
        this.bondUniCodes = Objects.isNull(bondUniCodes) ? new Long[0] : bondUniCodes.clone();
    }

    public Long getIndustryCode1() {
        return industryCode1;
    }

    public void setIndustryCode1(Long industryCode1) {
        this.industryCode1 = industryCode1;
    }

    public Long getIndustryCode2() {
        return industryCode2;
    }

    public void setIndustryCode2(Long industryCode2) {
        this.industryCode2 = industryCode2;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer[] getBondImpliedRatingMappings() {
        return Objects.isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public void setBondImpliedRatingMappings(Integer[] bondImpliedRatingMappings) {
        this.bondImpliedRatingMappings = Objects.isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public Integer[] getComYyRatingMappings() {
        return Objects.isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public void setComYyRatingMappings(Integer[] comYyRatingMappings) {
        this.comYyRatingMappings = Objects.isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public Integer getBondImpliedRatingMappingTag() {
        return bondImpliedRatingMappingTag;
    }

    public void setBondImpliedRatingMappingTag(Integer bondImpliedRatingMappingTag) {
        this.bondImpliedRatingMappingTag = bondImpliedRatingMappingTag;
    }

    public Integer getComYyRatingMappingTag() {
        return comYyRatingMappingTag;
    }

    public void setComYyRatingMappingTag(Integer comYyRatingMappingTag) {
        this.comYyRatingMappingTag = comYyRatingMappingTag;
    }

    public Integer[] getBusinessFilterNatures() {
        return Objects.isNull(businessFilterNatures) ? new Integer[0] : businessFilterNatures.clone();
    }

    public void setBusinessFilterNatures(Integer[] businessFilterNatures) {
        this.businessFilterNatures = Objects.isNull(businessFilterNatures) ? new Integer[0] : businessFilterNatures.clone();
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getGuaranteeStatus() {
        return guaranteeStatus;
    }

    public void setGuaranteeStatus(Integer guaranteeStatus) {
        this.guaranteeStatus = guaranteeStatus;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(Integer startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }
}
