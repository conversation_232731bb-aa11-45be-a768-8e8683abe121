package com.innodealing.onshore.yieldspread.mapper.dmdc;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.ComInterestInduHistDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.group.ComInterestInduHistGroupDO;

/**
 * 行业主体利差(老表)
 *
 * <AUTHOR>
 **/
public interface ComInterestInduHistMapper extends SelectByGroupedQueryMapper<ComInterestInduHistGroupDO, ComInterestInduHistDO> {
}
