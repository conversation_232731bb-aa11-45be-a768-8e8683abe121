package com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvInduBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.repository.PgInduBondYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.InduShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.<PERSON>YieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.InduSpreadPanoramaBO;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.InduYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.InduShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.BasePgInduBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveAllDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu1DO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu2DO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.between;
import static com.innodealing.commons.object.ObjectExtensionUtils.isAllEmpty;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.divideHundredThousand;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE;

/**
 * 行业利差曲线分区DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgInduBondYieldSpreadCurveDAO {

    @Resource
    private PgInduBondYieldSpreadCurveRepository pgInduBondYieldSpreadCurveRepository;

    @Resource
    private MvInduBondYieldSpreadCurveDAO mvInduBondYieldSpreadCurveDAO;

    @Resource
    private InduShardBondYieldSpreadCurveMapper induShardBondYieldSpreadCurveMapper;

    /**
     * 查询行业利差曲线-物化视图
     *
     * @param searchParameter 搜索参数DTO
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 物化视图中行业利差曲线数据响应集
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurves(InduBondYieldSpreadParamDTO searchParameter) {
        List<? extends BasePgInduBondYieldSpreadCurveDO> induBondYieldSpreadCurveList;
        if (isAllEmpty(searchParameter.getIndustryCode1(), searchParameter.getIndustryCode2())) {
            induBondYieldSpreadCurveList = pgInduBondYieldSpreadCurveRepository.listYieldSpreadCurvesForAlls(searchParameter);
        } else if (Objects.nonNull(searchParameter.getIndustryCode1())) {
            induBondYieldSpreadCurveList = pgInduBondYieldSpreadCurveRepository.listYieldSpreadCurvesForIndu1s(searchParameter);
        } else {
            induBondYieldSpreadCurveList = pgInduBondYieldSpreadCurveRepository.listYieldSpreadCurvesForIndu2s(searchParameter);
        }
        if (CollectionUtils.isEmpty(induBondYieldSpreadCurveList)) {
            return Collections.emptyList();
        }
        return divideYieldSpread(induBondYieldSpreadCurveList);
    }

    private List<BondYieldSpreadCurveBO> divideYieldSpread(List<? extends BasePgInduBondYieldSpreadCurveDO> induBondYieldSpreadCurveList) {
        return induBondYieldSpreadCurveList.stream().map(curve -> {
            BondYieldSpreadCurveBO clone = BeanCopyUtils.copyProperties(curve, BondYieldSpreadCurveBO.class);
            // 物化视图中的数据乘以100_000,需要除以100_000
            ObjectExtensionUtils.ifNonNull(curve.getBondCreditSpread(), v -> divideHundredThousand(BigDecimal.valueOf(v)).ifPresent(clone::setBondCreditSpread));
            ObjectExtensionUtils.ifNonNull(curve.getBondExcessSpread(), v -> divideHundredThousand(BigDecimal.valueOf(v)).ifPresent(clone::setBondExcessSpread));
            ObjectExtensionUtils.ifNonNull(curve.getCbYield(), v -> divideHundredThousand(BigDecimal.valueOf(v), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCbYield));
            return clone;
        }).collect(Collectors.toList());
    }

    /**
     * 从物化视图同步昨日利差曲线数据
     */
    public void syncCurveIncrFromMV() {
        pgInduBondYieldSpreadCurveRepository.syncCurveIncrFromMV();
    }

    /**
     * 同步物化视图数据
     *
     * @param router 路由
     * @param param  创建条件
     */
    public void syncCurveShardInduForMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        InduBondYieldSpreadCurveParameter parameter = builderParameter(router);
        if (param.isTableRefresh()) {
            pgInduBondYieldSpreadCurveRepository.refreshTable(parameter.getTableName(), parameter);
        }
        DynamicQuery<InduShardBondYieldSpreadCurveDO> query = builderDynamicQuery(router);
        int count = induShardBondYieldSpreadCurveMapper.selectCountByDynamicQuery(query, router);
        if (count > 0) {
            induShardBondYieldSpreadCurveMapper.deleteByDynamicQueryRouter(query, router);
        }
        //同步最新数据
        pgInduBondYieldSpreadCurveRepository.syncCurveIncrFromMV(parameter.getTableName(), mvInduBondYieldSpreadCurveDAO.getMvName(router));
    }

    private InduBondYieldSpreadCurveParameter builderParameter(AbstractRatingRouter router) {
        InduBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, InduBondYieldSpreadCurveParameter.class);
        parameter.setInduLevel(router.getLevel());
        parameter.setTableName(getTableName(router));
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (spreadDateRange != null) {
            parameter.setStartDate(spreadDateRange.getStartDate());
            parameter.setEndDate(spreadDateRange.getEndDate());
        }
        return parameter;
    }

    private DynamicQuery<InduShardBondYieldSpreadCurveDO> builderDynamicQuery(AbstractRatingRouter router) {
        DynamicQuery<InduShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(InduShardBondYieldSpreadCurveDO.class);
        if (Objects.nonNull(router.getSpreadDateRange()) &&
                Objects.nonNull(router.getSpreadDateRange().getStartDate()) &&
                Objects.nonNull(router.getSpreadDateRange().getEndDate())) {
            query.and(Objects.nonNull(router.getSpreadDateRange()), InduShardBondYieldSpreadCurveDO::getSpreadDate,
                    between(router.getSpreadDateRange().getStartDate(), router.getSpreadDateRange().getEndDate()));
        }
        return query;
    }

    /**
     * 从物化视图刷新行业利差曲线数据
     */
    public void refreshMvInduBondYieldSpreadCurveFromMV() {
        pgInduBondYieldSpreadCurveRepository.refreshMvInduBondYieldSpreadCurveFromMV();
    }

    /**
     * 查询行业1全景图数据集，因为全景图数据可以和曲线的数据复用，因此也采用从这里查询
     *
     * @param searchParameter 查询行业1全景图请求参数
     * @return {@link List}<{@link InduSpreadPanoramaBO}> 查询行业1全景图数据响应集
     */
    public List<InduSpreadPanoramaBO> listYieldSpreadPanoramasForIndu1(InduBondYieldSpreadParamDTO searchParameter) {
        List<PgInduBondYieldSpreadCurveIndu1DO> curveIndu1List = pgInduBondYieldSpreadCurveRepository.pgListYieldSpreadPanoramasForIndu1s(searchParameter);
        if (CollectionUtils.isEmpty(curveIndu1List)) {
            return Collections.emptyList();
        }
        List<InduSpreadPanoramaBO> responseList = Lists.newArrayListWithExpectedSize(curveIndu1List.size());
        for (PgInduBondYieldSpreadCurveIndu1DO indu1 : curveIndu1List) {
            InduSpreadPanoramaBO response = BeanCopyUtils.copyProperties(indu1, InduSpreadPanoramaBO.class);
            ObjectExtensionUtils.ifNonNull(indu1.getBondCreditSpread(), credit -> divideHundredThousand(BigDecimal.valueOf(credit)).ifPresent(response::setBondCreditSpread));
            ObjectExtensionUtils.ifNonNull(indu1.getBondExcessSpread(), excess -> divideHundredThousand(BigDecimal.valueOf(excess)).ifPresent(response::setBondExcessSpread));
            responseList.add(response);
        }
        return responseList;
    }

    /**
     * 查询行业2全景图数据集，因为全景图数据可以和曲线的数据复用，因此也采用从这里查询
     *
     * @param searchParameter 查询行业2全景图请求参数
     * @param indu2Codes      行业2唯一编码集合
     * @return {@link List}<{@link InduSpreadPanoramaBO}> 查询行业2全景图数据响应集
     */
    public List<InduSpreadPanoramaBO> listYieldSpreadPanoramasForIndu2(InduBondYieldSpreadParamDTO searchParameter, Long[] indu2Codes) {
        List<PgInduBondYieldSpreadCurveIndu2DO> curveIndu2List = pgInduBondYieldSpreadCurveRepository.pgListYieldSpreadPanoramasForIndu2s(searchParameter, indu2Codes);
        if (CollectionUtils.isEmpty(curveIndu2List)) {
            return Collections.emptyList();
        }
        List<InduSpreadPanoramaBO> responseList = Lists.newArrayListWithExpectedSize(curveIndu2List.size());
        for (PgInduBondYieldSpreadCurveIndu2DO indu2 : curveIndu2List) {
            InduSpreadPanoramaBO response = BeanCopyUtils.copyProperties(indu2, InduSpreadPanoramaBO.class);
            ObjectExtensionUtils.ifNonNull(indu2.getBondCreditSpread(), credit -> divideHundredThousand(BigDecimal.valueOf(credit)).ifPresent(response::setBondCreditSpread));
            ObjectExtensionUtils.ifNonNull(indu2.getBondExcessSpread(), excess -> divideHundredThousand(BigDecimal.valueOf(excess)).ifPresent(response::setBondExcessSpread));
            responseList.add(response);
        }
        return responseList;
    }

    private String getTableName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()), induShardBondYieldSpreadCurveMapper.getLogicTable(),
                RatingCombinationHelper.getTableNameSuffix(router));
    }

    /**
     * 查询产业利差
     *
     * @param searchParam 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listBondYieldSpreads(InduYieldSearchParam searchParam) {
        final List<BondYieldSpreadBO> yieldSpreadList = new ArrayList<>();
        if (Objects.nonNull(searchParam.getIndustryCode1())) {
            List<PgInduBondYieldSpreadCurveIndu1DO> yieldSpreads = pgInduBondYieldSpreadCurveRepository.listYieldSpreadsFromIndu1(searchParam);
            yieldSpreads.forEach(ys -> yieldSpreadList.add(convertToBondYieldSpreadBO(ys)));
        } else if (Objects.nonNull(searchParam.getIndustryCode2())) {
            List<PgInduBondYieldSpreadCurveIndu2DO> yieldSpreads = pgInduBondYieldSpreadCurveRepository.listYieldSpreadsFromIndu2(searchParam);
            yieldSpreads.forEach(ys -> yieldSpreadList.add(convertToBondYieldSpreadBO(ys)));
        } else {
            List<PgInduBondYieldSpreadCurveAllDO> yieldSpreads = pgInduBondYieldSpreadCurveRepository.listYieldSpreadsFromAll(searchParam);
            yieldSpreads.forEach(ys -> yieldSpreadList.add(convertToBondYieldSpreadBO(ys)));
        }
        return yieldSpreadList;
    }

    private BondYieldSpreadBO convertToBondYieldSpreadBO(BasePgInduBondYieldSpreadCurveDO baseInduBondYieldSpreadDO) {
        BondYieldSpreadBO bondYieldSpreadBO = BeanCopyUtils.copyProperties(baseInduBondYieldSpreadDO, BondYieldSpreadBO.class);
        bondYieldSpreadBO.setBondCreditSpread(CalculationHelper.divideHundredThousand(baseInduBondYieldSpreadDO.getBondCreditSpread()).orElse(null));
        bondYieldSpreadBO.setBondExcessSpread(CalculationHelper.divideHundredThousand(baseInduBondYieldSpreadDO.getBondExcessSpread()).orElse(null));
        bondYieldSpreadBO.setCbYield(CalculationHelper.divideHundredThousand(baseInduBondYieldSpreadDO.getCbYield(), YIELD_SPREAD_KEEP_SCALE).orElse(null));
        bondYieldSpreadBO.setAvgBondCreditSpread(CalculationHelper.divideHundredThousand(baseInduBondYieldSpreadDO.getAvgBondCreditSpread()).orElse(null));
        bondYieldSpreadBO.setAvgBondExcessSpread(CalculationHelper.divideHundredThousand(baseInduBondYieldSpreadDO.getAvgBondExcessSpread()).orElse(null));
        bondYieldSpreadBO.setAvgCbYield(CalculationHelper.divideHundredThousand(baseInduBondYieldSpreadDO.getAvgCbYield(), YIELD_SPREAD_KEEP_SCALE).orElse(null));
        return bondYieldSpreadBO;
    }

}
