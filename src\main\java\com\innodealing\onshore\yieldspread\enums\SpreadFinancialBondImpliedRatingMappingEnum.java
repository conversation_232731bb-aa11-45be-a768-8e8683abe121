package com.innodealing.onshore.yieldspread.enums;

import java.util.Objects;

/**
 * 金融债券隐含评级映射
 *
 * <AUTHOR>
 */
public enum SpreadFinancialBondImpliedRatingMappingEnum implements IBondImpliedRating {
    /**
     * AAA级
     */
    AAA(1, new Integer[]{10, 20, 30}, "AAA级"),
    /**
     * AA级
     */
    AA(2, new Integer[]{40, 50, 60}, "AA级");

    private final Integer tag;

    private final String text;

    private final Integer[] mapping;

    SpreadFinancialBondImpliedRatingMappingEnum(Integer tag, Integer[] mapping, String text) {
        this.tag = tag;
        this.mapping = mapping.clone();
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return tag;
    }

    @Override
    public Integer[] getMapping() {
        return Objects.isNull(mapping) ? new Integer[0] : mapping.clone();
    }

}
