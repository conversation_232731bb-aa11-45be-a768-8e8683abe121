package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.enums.BankSeniorityRankingEnum;
import com.innodealing.onshore.bondmetadata.enums.SecuritySeniorityRankingEnum;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.AbstractMvComYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.YieldSpreadBondDAO;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.InsuSeniorityRankingEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.ValidationUtil;
import com.innodealing.onshore.yieldspread.model.bo.ShortComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.ComYieldSpreadItemListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.ComYieldSpreadListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.TrendReplayComYieldSpreadRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.TrendReplayYieldSpreadRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import com.innodealing.onshore.yieldspread.service.*;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.innodealing.commons.object.ObjectExtensionUtils.isAllEmpty;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.NOT_HAS_BONDS_MSG;
import static com.innodealing.onshore.yieldspread.enums.CurveTypeEnum.*;

/**
 * 主体利差服务
 *
 * <AUTHOR>
 */
@Service
public class ComYieldSpreadServiceImpl implements ComYieldSpreadService, InitializingBean {

    private final Map<CurveTypeEnum, CurveQueryRunner> queryRunnerMap = new ConcurrentHashMap<>();
    private final Table<CurveTypeEnum, Integer, Integer> spreadBondCodeTable = HashBasedTable.create();
    private static final Integer BOND_CODE_0 = 0;
    private static final Integer BOND_CODE_1 = 1;
    private static final Integer BOND_CODE_2 = 2;
    private static final int MAX_SELECTED_COM_SIZE = 4;

    private static final int POOL_SIZE = 20;

    @Resource
    private UdicComYieldSpreadService udicComYieldSpreadService;
    @Resource
    private InduComYieldSpreadService induComYieldSpreadService;
    @Resource
    private BankComYieldSpreadService bankComYieldSpreadService;
    @Resource
    private SecuComYieldSpreadService secuComYieldSpreadService;

    @Resource
    private InsuComYieldSpreadService insuComYieldSpreadService;

    @Autowired
    private List<AbstractMvComYieldSpreadCurveDAO<?>> mvComYieldSpreadCurveDAOList;
    @Resource
    private HolidayService holidayService;

    @Resource
    private YieldSpreadBondDAO yieldSpreadBondDAO;

    private static final ExecutorService EXECUTOR_SERVICE = new ThreadPoolExecutor(POOL_SIZE, POOL_SIZE, 0,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("TrendReplayService-pool-%d").build());


    @Override
    public void afterPropertiesSet() {
        queryRunnerMap.put(INDU, (comUniCode, spreadBondType, startDate, endDate)
                -> induComYieldSpreadService.curves(comUniCode, spreadBondType, startDate, endDate));
        queryRunnerMap.put(UDIC, (comUniCode, spreadBondType, startDate, endDate)
                -> udicComYieldSpreadService.curves(comUniCode, spreadBondType, startDate, endDate));
        queryRunnerMap.put(SECURITY, (comUniCode, seniorityRanking, startDate, endDate)
                -> secuComYieldSpreadService.curves(comUniCode, seniorityRanking, startDate, endDate));
        queryRunnerMap.put(BANK, (comUniCode, seniorityRanking, startDate, endDate)
                -> bankComYieldSpreadService.curves(comUniCode, seniorityRanking, startDate, endDate));
        queryRunnerMap.put(CUSTOMIZATION, this::listCustomCurves);
        queryRunnerMap.put(INSURANCE, (comUniCode, seniorityRanking, startDate, endDate)
                -> insuComYieldSpreadService.curves(comUniCode, seniorityRanking, startDate, endDate));
        spreadBondCodeTable.put(INDU, BOND_CODE_0, SpreadBondTypeEnum.SPREAD_NOT_PUBLIC_OFFERING.getValue());
        spreadBondCodeTable.put(INDU, BOND_CODE_1, SpreadBondTypeEnum.SPREAD_IS_PUBLIC_OFFERING.getValue());
        spreadBondCodeTable.put(INDU, BOND_CODE_2, SpreadBondTypeEnum.SPREAD_PERPETUAL.getValue());
        spreadBondCodeTable.put(UDIC, BOND_CODE_0, SpreadBondTypeEnum.SPREAD_NOT_PUBLIC_OFFERING.getValue());
        spreadBondCodeTable.put(UDIC, BOND_CODE_1, SpreadBondTypeEnum.SPREAD_IS_PUBLIC_OFFERING.getValue());
        spreadBondCodeTable.put(UDIC, BOND_CODE_2, SpreadBondTypeEnum.SPREAD_PERPETUAL.getValue());
        spreadBondCodeTable.put(BANK, BOND_CODE_0, BankSeniorityRankingEnum.NORMAL.getValue());
        spreadBondCodeTable.put(BANK, BOND_CODE_1, BankSeniorityRankingEnum.TIER_2_BOND.getValue());
        spreadBondCodeTable.put(BANK, BOND_CODE_2, BankSeniorityRankingEnum.PERPETUA.getValue());
        spreadBondCodeTable.put(SECURITY, BOND_CODE_0, SecuritySeniorityRankingEnum.NORMAL.getValue());
        spreadBondCodeTable.put(SECURITY, BOND_CODE_1, SecuritySeniorityRankingEnum.SUBORDINATED.getValue());
        spreadBondCodeTable.put(SECURITY, BOND_CODE_2, SecuritySeniorityRankingEnum.PERPETUA.getValue());
        spreadBondCodeTable.put(INSURANCE, BOND_CODE_1, InsuSeniorityRankingEnum.TIER2.getValue());
        spreadBondCodeTable.put(INSURANCE, BOND_CODE_2, InsuSeniorityRankingEnum.PERPETUAL.getValue());
    }

    private List<ComYieldSpreadCurveDTO> listCustomCurves(Long comUniCode, Integer spreadBondValue, Date startDate, Date endDate) {
        // 自选债的时候，不知道是城投还是产业，银行，证券，城投，产业，证券，城投的spreadBondValue枚举值是不同的，因此spreadBondValue都为null
        // 1. udic
        List<ComYieldSpreadCurveDTO> curves = udicComYieldSpreadService.curves(comUniCode, null, startDate, endDate);
        if (CollectionUtils.isNotEmpty(curves)) {
            return curves;
        }
        // 2. bank
        curves = bankComYieldSpreadService.curves(comUniCode, null, startDate, endDate);
        if (CollectionUtils.isNotEmpty(curves)) {
            return curves;
        }
        // 3. secu
        curves = secuComYieldSpreadService.curves(comUniCode, null, startDate, endDate);
        if (CollectionUtils.isNotEmpty(curves)) {
            return curves;
        }
        //7 insu 保险
        curves = insuComYieldSpreadService.curves(comUniCode, null, startDate, endDate);
        if (CollectionUtils.isNotEmpty(curves)) {
            return curves;
        }
        // 4. indu
        return induComYieldSpreadService.curves(comUniCode, null, startDate, endDate);
    }

    @Override
    public List<ComYieldSpreadCurveResponseDTO> listCurves(Long comUniCode, Integer curveType, Integer spreadBondRanking, Date startDate,
                                                           Date endDate) {
        Optional<CurveTypeEnum> curveTypeEnum = EnumUtils.ofNullable(CurveTypeEnum.class, curveType);
        if (!curveTypeEnum.isPresent()) {
            return Collections.emptyList();
        }
        // 这里产业债、城投使用的是spreadBondType，银行和证券 保险使用的是seniorityRanking，方法名没想好怎么取，取名太难啦
        Integer spreadBondValue = this.getSpreadBondValue(curveTypeEnum.get(), spreadBondRanking);
        CurveQueryRunner curveQueryRunner = queryRunnerMap.get(curveTypeEnum.get());
        List<ComYieldSpreadCurveDTO> comYieldSpreadCurveList = Optional.ofNullable(curveQueryRunner)
                .map(runner -> runner.run(comUniCode, spreadBondValue, startDate, endDate)).orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(comYieldSpreadCurveList)) {
            throw new TipsException(NOT_HAS_BONDS_MSG);
        }
        Predicate<ComYieldSpreadCurveDTO> anyOneNotEmptyPre =
                curve -> !isAllEmpty(curve.getBondCreditSpread(), curve.getBondExcessSpread(), curve.getCbYield(),
                        curve.getAvgBondExcessSpread(), curve.getAvgBondCreditSpread(), curve.getAvgCbYield());
        List<ComYieldSpreadCurveResponseDTO> responses = comYieldSpreadCurveList.stream()
                .filter(anyOneNotEmptyPre)
                .map(curve -> BeanCopyUtils.copyProperties(curve, ComYieldSpreadCurveResponseDTO.class))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(responses)) {
            throw new TipsException(NOT_HAS_BONDS_MSG);
        }
        return responses;
    }

    private Integer getSpreadBondValue(CurveTypeEnum curveTypeEnum, Integer spreadBondCode) {
        if (Objects.isNull(spreadBondCode)) {
            return null;
        }
        Integer spreadBondValue = spreadBondCodeTable.get(curveTypeEnum, spreadBondCode);
        if (Objects.isNull(spreadBondValue)) {
            throw new BusinessException("利差债券代码不正确，请输入正确债券代码");
        }
        return spreadBondValue;
    }

    private List<CustomComYieldSpreadResDTO> listCustomComs(ComYieldSpreadListRequestDTO request) {
        final Date spreadDate = request.getSpreadDate();
        final List<ComYieldSpreadItemListRequestDTO> customComs = request.getCustomComs();
        if (CollectionUtils.isEmpty(customComs)) {
            return Collections.emptyList();
        }
        final Set<Long> comUniCodes = this.collectComs(customComs);
        List<UdicComYieldSpreadResponseDTO> udicResponseList = udicComYieldSpreadService.listComs(spreadDate, comUniCodes);
        final Set<Long> existUdicComs = udicResponseList.stream().map(UdicComYieldSpreadResponseDTO::getComUniCode).collect(Collectors.toSet());
        comUniCodes.removeIf(existUdicComs::contains);
        // 2. bank
        List<BankComYieldSpreadResDTO> bankResponseList = bankComYieldSpreadService.listComs(spreadDate, comUniCodes);
        final Set<Long> existBankComs = bankResponseList.stream().map(BankComYieldSpreadResDTO::getComUniCode).collect(Collectors.toSet());
        comUniCodes.removeIf(existBankComs::contains);
        // 3. secu
        List<SecuComYieldSpreadResDTO> secuResponseList = secuComYieldSpreadService.listComs(spreadDate, comUniCodes);
        final Set<Long> existSecuComs = secuResponseList.stream().map(SecuComYieldSpreadResDTO::getComUniCode).collect(Collectors.toSet());
        comUniCodes.removeIf(existSecuComs::contains);
        // 7 insu
        List<InsuComYieldSpreadResDTO> insuResponseList = insuComYieldSpreadService.listComs(spreadDate, comUniCodes);
        final Set<Long> existInsuComs = insuResponseList.stream().map(InsuComYieldSpreadResDTO::getComUniCode).collect(Collectors.toSet());
        comUniCodes.removeIf(existInsuComs::contains);
        // 4. indu
        List<InduComYieldSpreadResponseDTO> induResponseList = induComYieldSpreadService.listComs(spreadDate, comUniCodes);
        // 自选债有先后顺序，如果某支债在城投，产业，银行，证券中都有，则按照 1.udic 2.bank 3.secu 7 insu 4.indu 先后顺序获取
        final int customComSize = udicResponseList.size() + bankResponseList.size() + secuResponseList.size() + induResponseList.size();
        List<CustomComYieldSpreadResDTO> customComResponseList = Lists.newArrayListWithExpectedSize(customComSize);
        customComResponseList.addAll(udicResponseList.stream().map(udic -> {
            CustomComYieldSpreadResDTO custom = BeanCopyUtils.copyProperties(udic, CustomComYieldSpreadResDTO.class);
            custom.setComExtRatingMappingStr(udic.getComExtRating());
            return custom;
        }).collect(Collectors.toList()));
        customComResponseList.addAll(BeanCopyUtils.copyList(bankResponseList, CustomComYieldSpreadResDTO.class));
        customComResponseList.addAll(BeanCopyUtils.copyList(secuResponseList, CustomComYieldSpreadResDTO.class));
        customComResponseList.addAll(induResponseList.stream().map(indu -> {
            CustomComYieldSpreadResDTO custom = BeanCopyUtils.copyProperties(indu, CustomComYieldSpreadResDTO.class);
            custom.setComExtRatingMappingStr(indu.getComExtRating());
            return custom;
        }).collect(Collectors.toList()));
        return this.bindingCustomCurveId(request, customComResponseList);
    }

    private void checkCodeSize(ComYieldSpreadListRequestDTO request) {
        int size = this.size(request.getCustomComs()) + this.size(request.getBanks()) + this.size(request.getUdics())
                + this.size(request.getIndus()) + this.size(request.getSecus());
        if (size > MAX_SELECTED_COM_SIZE) {
            throw new TipsException(String.format("最大只能查询%d个主体", MAX_SELECTED_COM_SIZE));
        }
    }

    private List<CustomComYieldSpreadResDTO> bindingCustomCurveId(ComYieldSpreadListRequestDTO request,
                                                                  List<CustomComYieldSpreadResDTO> customComResponseList) {
        List<CustomComYieldSpreadResDTO> totalCustomList = Lists.newArrayListWithExpectedSize(customComResponseList.size());
        for (ComYieldSpreadItemListRequestDTO customRequest : request.getCustomComs()) {
            customComResponseList.stream().filter(custom -> Objects.equals(customRequest.getComUniCode(), custom.getComUniCode())).findFirst()
                    .ifPresent(custom -> {
                        CustomComYieldSpreadResDTO customResponse = BeanCopyUtils.copyProperties(custom, CustomComYieldSpreadResDTO.class);
                        customResponse.setCurveId(customRequest.getCurveId());
                        totalCustomList.add(customResponse);
                    });
        }
        return totalCustomList;
    }

    private List<SecuComYieldSpreadResDTO> bindingSecuCurveId(ComYieldSpreadListRequestDTO request, List<SecuComYieldSpreadResDTO> secuResponseList) {
        List<SecuComYieldSpreadResDTO> totalSecuList = Lists.newArrayListWithExpectedSize(secuResponseList.size());
        for (ComYieldSpreadItemListRequestDTO secuRequest : request.getSecus()) {
            secuResponseList.stream().filter(secu -> Objects.equals(secuRequest.getComUniCode(), secu.getComUniCode())).findFirst()
                    .ifPresent(secu -> {
                        SecuComYieldSpreadResDTO secuResponse = BeanCopyUtils.copyProperties(secu, SecuComYieldSpreadResDTO.class);
                        secuResponse.setCurveId(secuRequest.getCurveId());
                        totalSecuList.add(secuResponse);
                    });
        }
        return totalSecuList;
    }

    private List<BankComYieldSpreadResDTO> bindingBankCurveId(ComYieldSpreadListRequestDTO request, List<BankComYieldSpreadResDTO> bankResponseList) {
        List<BankComYieldSpreadResDTO> totalBankList = Lists.newArrayListWithExpectedSize(bankResponseList.size());
        for (ComYieldSpreadItemListRequestDTO bankRequest : request.getBanks()) {
            bankResponseList.stream().filter(bank -> Objects.equals(bankRequest.getComUniCode(), bank.getComUniCode())).findFirst()
                    .ifPresent(bank -> {
                        BankComYieldSpreadResDTO bankResponse = BeanCopyUtils.copyProperties(bank, BankComYieldSpreadResDTO.class);
                        bankResponse.setCurveId(bankRequest.getCurveId());
                        totalBankList.add(bankResponse);
                    });
        }
        return totalBankList;
    }

    private List<InsuComYieldSpreadResDTO> bindingInsuCurveId(ComYieldSpreadListRequestDTO request, List<InsuComYieldSpreadResDTO> insuResponseList) {
        List<InsuComYieldSpreadResDTO> totalInsuList = Lists.newArrayListWithExpectedSize(insuResponseList.size());
        for (ComYieldSpreadItemListRequestDTO insuRequest : request.getInsus()) {
            insuResponseList.stream().filter(insu -> Objects.equals(insuRequest.getComUniCode(), insu.getComUniCode())).findFirst().ifPresent(insu -> {
                InsuComYieldSpreadResDTO insuResponse = BeanCopyUtils.copyProperties(insu, InsuComYieldSpreadResDTO.class);
                insuResponse.setCurveId(insuRequest.getCurveId());
                totalInsuList.add(insuResponse);
            });
        }
        return totalInsuList;
    }

    private List<UdicComYieldSpreadResponseDTO> bindingUdicCurveId(ComYieldSpreadListRequestDTO request,
                                                                   List<UdicComYieldSpreadResponseDTO> udicResponseList) {
        List<UdicComYieldSpreadResponseDTO> totalUdicList = Lists.newArrayListWithExpectedSize(udicResponseList.size());
        for (ComYieldSpreadItemListRequestDTO udicRequest : request.getUdics()) {
            udicResponseList.stream().filter(udic -> Objects.equals(udicRequest.getComUniCode(), udic.getComUniCode())).findFirst()
                    .ifPresent(udic -> {
                        UdicComYieldSpreadResponseDTO udicResponse = BeanCopyUtils.copyProperties(udic, UdicComYieldSpreadResponseDTO.class);
                        udicResponse.setCurveId(udicRequest.getCurveId());
                        totalUdicList.add(udicResponse);
                    });
        }
        return totalUdicList;
    }

    private List<InduComYieldSpreadResponseDTO> bindingInduCurveId(ComYieldSpreadListRequestDTO request,
                                                                   List<InduComYieldSpreadResponseDTO> induResponseList) {
        List<InduComYieldSpreadResponseDTO> totalInduList = Lists.newArrayListWithExpectedSize(induResponseList.size());
        for (ComYieldSpreadItemListRequestDTO induRequest : request.getIndus()) {
            induResponseList.stream().filter(indu -> Objects.equals(induRequest.getComUniCode(), indu.getComUniCode())).findFirst()
                    .ifPresent(indu -> {
                        InduComYieldSpreadResponseDTO induResponse = BeanCopyUtils.copyProperties(indu, InduComYieldSpreadResponseDTO.class);
                        induResponse.setCurveId(induRequest.getCurveId());
                        totalInduList.add(induResponse);
                    });
        }
        return totalInduList;
    }

    private int size(List<ComYieldSpreadItemListRequestDTO> comRequests) {
        return CollectionUtils.isEmpty(comRequests) ? 0 : comRequests.size();
    }

    @Override
    public ComYieldSpreadListResponseDTO listComs(ComYieldSpreadListRequestDTO request) {
        this.checkCodeSize(request);
        final Date spreadDate = request.getSpreadDate();
        // 1. udic
        List<UdicComYieldSpreadResponseDTO> udicResponseList = udicComYieldSpreadService.listComs(request.getSpreadDate(),
                this.collectComs(request.getUdics()));
        // 2. bank
        List<BankComYieldSpreadResDTO> bankResponseList = bankComYieldSpreadService.listComs(request.getSpreadDate(),
                this.collectComs(request.getBanks()));
        // 3. secu
        List<SecuComYieldSpreadResDTO> secuResponseList = secuComYieldSpreadService.listComs(request.getSpreadDate(),
                this.collectComs(request.getSecus()));
        //7. insu
        List<InsuComYieldSpreadResDTO> insuResponseList = insuComYieldSpreadService.listComs(spreadDate, this.collectComs(request.getInsus()));
        // 4. indu
        List<InduComYieldSpreadResponseDTO> induResponseList = induComYieldSpreadService.listComs(spreadDate, this.collectComs(request.getIndus()));
        ComYieldSpreadListResponseDTO response = new ComYieldSpreadListResponseDTO();
        response.setIndus(this.bindingInduCurveId(request, induResponseList));
        response.setUdics(this.bindingUdicCurveId(request, udicResponseList));
        response.setBanks(this.bindingBankCurveId(request, bankResponseList));
        response.setSecus(this.bindingSecuCurveId(request, secuResponseList));
        response.setInsus(this.bindingInsuCurveId(request, insuResponseList));
        response.setCustomComs(this.listCustomComs(request));
        return response;
    }

    @Override
    public void refreshMv() {
        if (Boolean.TRUE.equals(holidayService.isHoliday(Date.valueOf(LocalDate.now())))) {
            return;
        }
        mvComYieldSpreadCurveDAOList.forEach(AbstractMvComYieldSpreadCurveDAO::refresh);
    }

    private Set<Long> collectComs(List<ComYieldSpreadItemListRequestDTO> itemListRequests) {
        if (CollectionUtils.isEmpty(itemListRequests)) {
            return Collections.emptySet();
        }
        return itemListRequests.stream().map(ComYieldSpreadItemListRequestDTO::getComUniCode).collect(Collectors.toSet());
    }

    /**
     * 曲线查询运行器
     *
     * <AUTHOR>
     */
    @FunctionalInterface
    private interface CurveQueryRunner {

        /**
         * 查询曲线数据
         *
         * @param bondUniCode    债券唯一编码
         * @param spreadBondType 债券类型
         * @param startDate      开始日期
         * @param endDate        结束日期
         * @return {@link List}<{@link ComYieldSpreadCurveDTO}>
         */
        List<ComYieldSpreadCurveDTO> run(Long bondUniCode, Integer spreadBondType, Date startDate, Date endDate);
    }

    @Override
    public List<ComCreditSpreadDTO> trendReplayCurves(TrendReplayYieldSpreadRequestDTO requestDTO) {
        for (TrendReplayComYieldSpreadRequestDTO dto : requestDTO.getComYieldSpreadRequestDTOList()) {
            ValidationUtil.valid(dto);
        }
        // 过滤
        filterOrPopulateSectorIfNecessary(requestDTO);
        List<ComCreditSpreadDTO> comCreditSpreadDTOList = new CopyOnWriteArrayList<>();
        List<TrendReplayComYieldSpreadRequestDTO> comYieldSpreadRequestDTOList = requestDTO.getComYieldSpreadRequestDTOList();
        // 按照行业分组
        Map<Integer, List<TrendReplayComYieldSpreadRequestDTO>> collect = comYieldSpreadRequestDTOList.stream()
                .collect(Collectors.groupingBy(TrendReplayComYieldSpreadRequestDTO::getComSpreadSector));
        SwThreadPoolWorker<ShortComYieldSpreadBO> swThreadPoolWorker = SwThreadPoolWorker.of(EXECUTOR_SERVICE);
        for (Entry<Integer, List<TrendReplayComYieldSpreadRequestDTO>> integerListEntry : collect.entrySet()) {
            Optional<ComYieldSpreadSectorEnum> curveTypeEnum = EnumUtils.ofNullable(ComYieldSpreadSectorEnum.class, integerListEntry.getKey());
            if (!curveTypeEnum.isPresent()) {
                continue;
            }
            // 1
            if (ComYieldSpreadSectorEnum.UDIC.equals(curveTypeEnum.get())) {
                swThreadPoolWorker.addWork(() -> comCreditSpreadDTOList.addAll(
                        udicComYieldSpreadService.listTrendReplayComYieldSpreads(requestDTO.getSpreadStartDate(), requestDTO.getSpreadEndDate(),
                                integerListEntry.getValue())));
                //2
            } else if (ComYieldSpreadSectorEnum.INDU.equals(curveTypeEnum.get())) {
                swThreadPoolWorker.addWork(() -> comCreditSpreadDTOList.addAll(
                        induComYieldSpreadService.listTrendReplayComYieldSpreads(requestDTO.getSpreadStartDate(), requestDTO.getSpreadEndDate(),
                                integerListEntry.getValue())));

                // 3
            } else if (ComYieldSpreadSectorEnum.SECU.equals(curveTypeEnum.get())) {
                swThreadPoolWorker.addWork(() -> comCreditSpreadDTOList.addAll(
                        secuComYieldSpreadService.listTrendReplayComYieldSpreads(requestDTO.getSpreadStartDate(), requestDTO.getSpreadEndDate(),
                                integerListEntry.getValue())));
                // 4
            } else if (ComYieldSpreadSectorEnum.BANK.equals(curveTypeEnum.get())) {
                swThreadPoolWorker.addWork(() -> comCreditSpreadDTOList.addAll(
                        bankComYieldSpreadService.listTrendReplayComYieldSpreads(requestDTO.getSpreadStartDate(), requestDTO.getSpreadEndDate(),
                                integerListEntry.getValue())));
            }
        }
        swThreadPoolWorker.doWorks();
        if (CollectionUtils.isEmpty(comCreditSpreadDTOList)) {
            return Collections.emptyList();
        }
        return comCreditSpreadDTOList;
    }

    private void filterOrPopulateSectorIfNecessary(TrendReplayYieldSpreadRequestDTO requestDTO) {
        List<Long> noSectorComUniCodes = requestDTO.getComYieldSpreadRequestDTOList().stream().filter(e -> Objects.isNull(e.getComSpreadSector()))
                .map(TrendReplayComYieldSpreadRequestDTO::getComUniCode).collect(
                        Collectors.toList());
        if (CollectionUtils.isEmpty(noSectorComUniCodes)) {
            return;
        }
        List<YieldSpreadBondDO> induInfoBOList = yieldSpreadBondDAO.findComSpreadSector(noSectorComUniCodes);
        if (!CollectionUtils.isEmpty(induInfoBOList)) {
            Map<Long, Integer> tmpComSpreadSectorMap = induInfoBOList.stream()
                    .collect(Collectors.toMap(YieldSpreadBondDO::getComUniCode, YieldSpreadBondDO::getComSpreadSector, (e1, e2) -> e1));
            for (TrendReplayComYieldSpreadRequestDTO dto : requestDTO.getComYieldSpreadRequestDTOList()) {
                Integer value = tmpComSpreadSectorMap.get(dto.getComUniCode());
                if (Objects.isNull(value)) {
                    continue;
                }
                dto.setComSpreadSector(value);
            }
        }
        // 重新set 一下list ，防止 不存在行业的主体数据
        requestDTO.setComYieldSpreadRequestDTOList(
                requestDTO.getComYieldSpreadRequestDTOList().stream().filter(e -> Objects.nonNull(e.getComSpreadSector()))
                        .collect(Collectors.toList()));
    }
}