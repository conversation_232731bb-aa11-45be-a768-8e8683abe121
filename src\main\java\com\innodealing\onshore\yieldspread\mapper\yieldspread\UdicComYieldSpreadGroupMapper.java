package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.UdicComYieldSpreadGroupMaxDO;

/**
 * 信用主体利差 group mapper
 *
 * <AUTHOR>
 * @date 2024/4/3 14:55
 **/
public interface UdicComYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<UdicComYieldSpreadDO
        , UdicComYieldSpreadGroupMaxDO> {
}
