package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.dto.area.AreaInfoResponseDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 区域服务
 *
 * <AUTHOR>
 **/
@FeignClient(name = "areaService", url = "${area.service.api.url}", path = "/internal")
public interface AreaService {
    /**
     * 根据区域编码返回区域信息
     *
     * @param areaUniCodes 区域集合
     * @return 城投主体信息
     */
    @PostMapping("area/areaInfo/listAreaInfos")
    List<AreaInfoResponseDTO> listAreaInfos(@RequestBody List<Long> areaUniCodes);

    /**
     * 根据区域编码返回区域信息
     *
     * @param areaUniCodes 区域编码
     * @return key 区域编码,value 区域信息 DTO
     */
    default Map<Long, AreaInfoResponseDTO> getAreaInfoMap(Set<Long> areaUniCodes) {
        if (CollectionUtils.isEmpty(areaUniCodes)) {
            return Collections.emptyMap();
        }
        return listAreaInfos(new ArrayList<>(areaUniCodes)).stream()
                .collect(Collectors.toMap(AreaInfoResponseDTO::getAreaUniCode, Function.identity(), (x1, x2) -> x2));
    }
}
