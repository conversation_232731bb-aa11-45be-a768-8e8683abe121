package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceQuantileDO;


/**
 * 债券利差追踪-历史分位Mapper
 *
 * <AUTHOR>
 */
public interface PgBondYieldSpreadTraceQuantileMapper extends DynamicQueryMapper<PgBondYieldSpreadTraceQuantileDO> {


}
