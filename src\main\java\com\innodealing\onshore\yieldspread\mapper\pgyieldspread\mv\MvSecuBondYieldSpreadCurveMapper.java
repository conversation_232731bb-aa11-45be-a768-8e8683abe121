package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvSecuBondYieldSpreadCurveParameter;
import org.apache.ibatis.annotations.Param;

/**
 * 证券利差曲线mapper
 * <AUTHOR>
 */
public interface MvSecuBondYieldSpreadCurveMapper extends PgBaseMapper<MvSecuBondYieldSpreadCurveParameter> {

    /**
     * 创建分片视图
     * @param parameter 创建条件
     */
    void createMvRatingRouter(@Param("parameter") MvSecuBondYieldSpreadCurveParameter parameter);


}
