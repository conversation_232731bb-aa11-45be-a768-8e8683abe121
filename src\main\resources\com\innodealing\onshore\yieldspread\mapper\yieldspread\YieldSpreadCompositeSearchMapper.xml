<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.yieldspread.YieldSpreadCompositeSearchMapper">
    <sql id="bondYieldSpreadClums">
        bond_uni_code
        ,
        spread_date,
        remaining_tenor,
        remaining_tenor_day,
        latest_coupon_rate,
        bond_balance,
        com_ext_rating_mapping,
        bond_ext_rating_mapping,
        bond_implied_rating_mapping,
        bond_credit_spread,
        bond_excess_spread,
        cb_yield,
        cdb_lerp_yield
    </sql>

    <sql id="comYieldSpreadClums">
        temp
        .
        com_uni_code
        ,
        spread_date,
        com_ext_rating_mapping,
        total_assets,
        com_credit_spread,
        com_excess_spread,
        com_cb_yield
    </sql>

    <sql id="foreachSql">
        <if test="params.bondUniCodes != null and params.bondUniCodes.size()>0">
            and bond_uni_code in
            <foreach collection="params.bondUniCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="listSingleBondYieldSpreads"
            resultType="com.innodealing.onshore.yieldspread.model.bo.CustomSingleBondYieldSpreadBO">
        select * from (
        <trim suffixOverrides="UNION ALL">
            <if test="params.udicBondUniCodes != null and params.udicBondUniCodes.size()>0">
                SELECT
                <include refid="bondYieldSpreadClums"/>
                FROM udic_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.udicBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
            <if test="params.bankBondUniCodes != null and params.bankBondUniCodes.size()>0">
                SELECT
                <include refid="bondYieldSpreadClums"/>
                FROM bank_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.bankBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
            <if test="params.secuBondUniCodes != null and params.secuBondUniCodes.size()>0">
                SELECT
                <include refid="bondYieldSpreadClums"/>
                FROM secu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.secuBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
            <if test="params.induBondUniCodes != null and params.induBondUniCodes.size()>0">
                SELECT
                <include refid="bondYieldSpreadClums"/>
                FROM indu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.induBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
            <if test="params.insuBondUniCodes != null and params.insuBondUniCodes.size()>0">
                SELECT
                <include refid="bondYieldSpreadClums"/>
                FROM insu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.insuBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
        </trim>
        ) t
        group by bond_uni_code
        <if test="params.sort != null">
            ORDER BY ${params.sort.propertyName} ${params.sort.sortDirection}
        </if>
        LIMIT #{params.startIndex}, #{params.pageSize}
    </select>


    <select id="countSingleBondYieldSpread" resultType="long">
        select count(1) from (
        select * from (
        <trim suffixOverrides="UNION ALL">
            <if test="params.udicBondUniCodes != null and params.udicBondUniCodes.size()>0">
                SELECT
                bond_uni_code
                FROM udic_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.udicBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
            <if test="params.bankBondUniCodes != null and params.bankBondUniCodes.size()>0">
                SELECT
                bond_uni_code
                FROM bank_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.bankBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
            <if test="params.secuBondUniCodes != null and params.secuBondUniCodes.size()>0">
                SELECT
                bond_uni_code
                FROM secu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.secuBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
            <if test="params.induBondUniCodes != null and params.induBondUniCodes.size()>0">
                SELECT
                bond_uni_code
                FROM indu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.induBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
            <if test="params.insuBondUniCodes != null and params.insuBondUniCodes.size()>0">
                SELECT
                bond_uni_code
                FROM insu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.insuBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                UNION ALL
            </if>
        </trim>
        ) t
        group by bond_uni_code order by null
        )temp
    </select>

    <select id="listComYieldSpreads" resultType="com.innodealing.onshore.yieldspread.model.bo.CustomComYieldSpreadBO">
        select * from (
        <trim suffixOverrides="union all">
            <if test="params.udicBondUniCodes != null and params.udicBondUniCodes.size()>0">
                select
                <include refid="comYieldSpreadClums"/>
                from udic_com_yield_spread udic
                inner join (
                select distinct com_uni_code from udic_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.udicBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on udic.com_uni_code = temp.com_uni_code
                where udic.spread_date = #{params.spreadDate}
                union all
            </if>
            <if test="params.bankBondUniCodes != null and params.bankBondUniCodes.size()>0">
                select
                <include refid="comYieldSpreadClums"/>
                from bank_com_yield_spread bank
                inner join (
                select distinct com_uni_code from bank_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.bankBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on bank.com_uni_code = temp.com_uni_code
                where bank.spread_date = #{params.spreadDate}
                union all
            </if>
            <if test="params.secuBondUniCodes != null and params.secuBondUniCodes.size()>0">
                select
                <include refid="comYieldSpreadClums"/>
                from secu_com_yield_spread secu
                inner join (
                select distinct com_uni_code from secu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.secuBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on secu.com_uni_code = temp.com_uni_code
                where secu.spread_date = #{params.spreadDate}
                union all
            </if>
            <if test="params.induBondUniCodes != null and params.induBondUniCodes.size()>0">
                select
                <include refid="comYieldSpreadClums"/>
                from indu_com_yield_spread indu
                inner join (
                select distinct com_uni_code from indu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.induBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on indu.com_uni_code = temp.com_uni_code
                where indu.spread_date = #{params.spreadDate}
                union all
            </if>
            <if test="params.insuBondUniCodes != null and params.insuBondUniCodes.size()>0">
                select
                <include refid="comYieldSpreadClums"/>
                from insu_com_yield_spread insu
                inner join (
                select distinct com_uni_code from insu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.insuBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on insu.com_uni_code = temp.com_uni_code
                where insu.spread_date = #{params.spreadDate}
                union all
            </if>
        </trim>
        ) t
        group by com_uni_code
        <if test="params.sort != null">
            order by ${params.sort.propertyName} ${params.sort.sortDirection}
        </if>
        limit #{params.startIndex},#{params.pageSize}
    </select>

    <select id="countComYieldSpread" resultType="java.lang.Long">
        select count(1) from (
        select com_uni_code from (
        <trim suffixOverrides="union all">
            <if test="params.udicBondUniCodes != null and params.udicBondUniCodes.size()>0">
                select
                udic.com_uni_code
                from udic_com_yield_spread udic
                inner join (
                select distinct com_uni_code from udic_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.udicBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on udic.com_uni_code = temp.com_uni_code
                where udic.spread_date = #{params.spreadDate}
                union all
            </if>
            <if test="params.bankBondUniCodes != null and params.bankBondUniCodes.size()>0">
                select
                bank.com_uni_code
                from bank_com_yield_spread bank
                inner join (
                select distinct com_uni_code from bank_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.bankBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on bank.com_uni_code = temp.com_uni_code
                where bank.spread_date = #{params.spreadDate}
                union all
            </if>
            <if test="params.secuBondUniCodes != null and params.secuBondUniCodes.size()>0">
                select
                secu.com_uni_code
                from secu_com_yield_spread secu
                inner join (
                select distinct com_uni_code from secu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.secuBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on secu.com_uni_code = temp.com_uni_code
                where secu.spread_date = #{params.spreadDate}
                union all
            </if>
            <if test="params.induBondUniCodes != null and params.induBondUniCodes.size()>0">
                select
                indu.com_uni_code
                from indu_com_yield_spread indu
                inner join (
                select distinct com_uni_code from indu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.induBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on indu.com_uni_code = temp.com_uni_code
                where indu.spread_date = #{params.spreadDate}
                union all
            </if>
            <if test="params.insuBondUniCodes != null and params.insuBondUniCodes.size()>0">
                select
                insu.com_uni_code
                from insu_com_yield_spread insu
                inner join (
                select distinct com_uni_code from insu_bond_yield_spread_${params.year}
                <where>
                    <if test="params.comUniCode != null">
                        com_uni_code = #{params.comUniCode}
                    </if>
                    and bond_uni_code in
                    <foreach collection="params.insuBondUniCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND spread_date = #{params.spreadDate}
                </where>
                ) temp on insu.com_uni_code = temp.com_uni_code
                where insu.spread_date = #{params.spreadDate}
                union all
            </if>
        </trim>
        ) t
        group by com_uni_code order by null
        )temp
    </select>

</mapper>