package com.innodealing.onshore.yieldspread.config.datasource;

import com.innodealing.onshore.yieldspread.config.datasource.interceptor.DynamicTableNameInterceptor;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;

import javax.sql.DataSource;

/**
 * Create by
 *
 * <AUTHOR> wz2cool
 * @date : 2019/5/14
 */
class BaseSourceConfig {

    SqlSessionFactory getSessionFactory(DataSource dataSource, String[] aliasPackages) throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();

        sessionFactory.setDataSource(dataSource);
        sessionFactory.setTypeAliasesPackage(String.join(",", aliasPackages));
        // 创建并添加拦截器
        Interceptor interceptor = new DynamicTableNameInterceptor();
        sessionFactory.setPlugins(new Interceptor[]{interceptor});
        SqlSessionFactory factory = sessionFactory.getObject();
        Configuration configuration = factory.getConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        return factory;
    }
}
