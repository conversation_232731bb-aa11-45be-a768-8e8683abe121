package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 主体利差列表响应DTO
 *
 * <AUTHOR>
 */
public class ComYieldSpreadListRequestDTO {

    @ApiModelProperty(value = "产业主体唯一编码")
    @Size(message = "产业主体编码最大为{max}个", max = 4)
    private List<ComYieldSpreadItemListRequestDTO> indus;
    @ApiModelProperty(value = "城投主体唯一编码")
    @Size(message = "城投主体编码最大为{max}个", max = 4)
    private List<ComYieldSpreadItemListRequestDTO> udics;
    @ApiModelProperty(value = "银行主体唯一编码")
    @Size(message = "银行主体编码最大为{max}个", max = 4)
    private List<ComYieldSpreadItemListRequestDTO> banks;
    @ApiModelProperty(value = "证券主体唯一编码")
    @Size(message = "证券主体编码最大为{max}个", max = 4)
    private List<ComYieldSpreadItemListRequestDTO> secus;

    @ApiModelProperty(value = "保险主体唯一编码")
    @Size(message = "保险主体编码最大为{max}个", max = 4)
    private List<ComYieldSpreadItemListRequestDTO> insus;
    @ApiModelProperty(value = "自选主体唯一编码")
    @Size(message = "自选主体编码最大为{max}个", max = 4)
    private List<ComYieldSpreadItemListRequestDTO> customComs;
    @ApiModelProperty(value = "利差日期")
    @NotNull(message = "利差日期不能为空")
    private Date spreadDate;

    public List<ComYieldSpreadItemListRequestDTO> getIndus() {
        return Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public void setIndus(List<ComYieldSpreadItemListRequestDTO> indus) {
        this.indus = Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public List<ComYieldSpreadItemListRequestDTO> getUdics() {
        return Objects.isNull(udics) ? new ArrayList<>() : new ArrayList<>(udics);
    }

    public void setUdics(List<ComYieldSpreadItemListRequestDTO> udics) {
        this.udics = Objects.isNull(udics) ? new ArrayList<>() : new ArrayList<>(udics);
    }

    public List<ComYieldSpreadItemListRequestDTO> getBanks() {
        return Objects.isNull(banks) ? new ArrayList<>() : new ArrayList<>(banks);
    }

    public void setBanks(List<ComYieldSpreadItemListRequestDTO> banks) {
        this.banks = Objects.isNull(banks) ? new ArrayList<>() : new ArrayList<>(banks);
    }

    public List<ComYieldSpreadItemListRequestDTO> getSecus() {
        return Objects.isNull(secus) ? new ArrayList<>() : new ArrayList<>(secus);
    }

    public void setSecus(List<ComYieldSpreadItemListRequestDTO> secus) {
        this.secus = Objects.isNull(secus) ? new ArrayList<>() : new ArrayList<>(secus);
    }

    public List<ComYieldSpreadItemListRequestDTO> getInsus() {
        return Objects.isNull(insus) ? new ArrayList<>() : new ArrayList<>(insus);
    }

    public void setInsus(List<ComYieldSpreadItemListRequestDTO> insus) {
        this.insus = Objects.isNull(insus) ? new ArrayList<>() : new ArrayList<>(insus);
    }

    public List<ComYieldSpreadItemListRequestDTO> getCustomComs() {
        return Objects.isNull(customComs) ? new ArrayList<>() : new ArrayList<>(customComs);
    }

    public void setCustomComs(List<ComYieldSpreadItemListRequestDTO> customComs) {
        this.customComs = Objects.isNull(customComs) ? new ArrayList<>() : new ArrayList<>(customComs);
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = new Date(spreadDate.getTime());
    }
}
