package com.innodealing.onshore.yieldspread.service;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 利差计算 Service
 *
 * <AUTHOR>
 **/
public interface CalcYieldSpreadService {

    /**
     * 产业利差计算(主体,债券) 只刷新补充的债券类型
     *
     * @param startDate       开始日期
     * @param endDate         结束日期
     * @param bondUniCodeList 债券唯一代码
     * @param isEnableOldData 是否启用老数据
     * @return 影响行数
     */
    int supplementCalcInduBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodeList, Boolean isEnableOldData);

    /**
     * 产业利差计算(主体,债券)
     *
     * @param startDate       开始日期
     * @param endDate         结束日期
     * @param bondUniCodeList 债券唯一代码
     * @param isEnableOldData 是否启用老数据
     * @return 影响行数
     */
    int calcInduBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodeList, Boolean isEnableOldData);

    /**
     * 城投利差计算(主体,债券)
     *
     * @param startDate       开始日期
     * @param endDate         结束日期
     * @param bondUniCodeList 债券唯一代码
     * @param isEnableOldData 是否启用老数据
     * @return 影响行数
     */
    int calcUdicBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodeList, Boolean isEnableOldData);

    /**
     * 证券利差计算(主体,债券)
     *
     * @param startDate       开始日期
     * @param endDate         结束日期
     * @param bondUniCodeList 债券唯一代码
     * @return 影响行数
     */
    int calcSecuBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodeList);

    /**
     * 地方债利差计算
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param bondUniCodes  债券唯一代码
     * @return 影响行数
     */
    int calcLgBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodes);

    /**
     * 保险利差计算(主体,债券)
     *
     * @param startDate       开始日期
     * @param endDate         结束日期
     * @param bondUniCodeList 债券唯一代码
     * @return 影响行数
     */
    int calcInsuBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodeList);


    /**
     * 计算主体利差变动
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param comUniCodeList 主体唯一代码
     * @return 影响行数
     */
    int calcComYieldSpreadChangeBySpreadDate(Date startDate, Date endDate, Set<Long> comUniCodeList);

    /**
     * 根据日期计算保留最新一天主体利差变动
     */
    void calcRecentComYieldSpreadChange();

    /**
     * 计算证券主体利差变动
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param comUniCodeList 主体唯一代码
     * @return 影响行数
     */
    int calcSecuComYieldSpreadChangeBySpreadDate(Date startDate, Date endDate, Set<Long> comUniCodeList);

    /**
     * 银行利差计算(主体,债券)
     *
     * @param startDate       开始日期
     * @param endDate         结束日期
     * @param bondUniCodeList 债券唯一代码
     * @return 影响行数
     */
    int calcBankBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodeList);

    /**
     * 根据日期计算债券利差(不传日期默认跑前一天的数据)
     *
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param bondUniCodes 债券code
     * @return 处理条数
     */
    int calcBondYieldSpread(Date startDate, Date endDate, List<Long> bondUniCodes);

}
