package com.innodealing.onshore.yieldspread.mapper.yieldspread.view;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.github.wz2cool.dynamic.mybatis.mapper.SelectViewByDynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.view.InduComYieldSpreadDynamicView;


/**
 * 行业主体利差视图
 *
 * <AUTHOR>
 */
public interface InduComYieldSpreadViewMapper extends SelectViewByDynamicQueryMapper<InduComYieldSpreadDynamicView>,
        DynamicQueryMapper<InduComYieldSpreadDynamicView> {
}
