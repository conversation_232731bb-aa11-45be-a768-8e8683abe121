package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition;

import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.SecuBondYieldSpreadCurveParameter;
import org.apache.ibatis.annotations.Param;

/**
 * 证券利差曲线mapper
 * <AUTHOR>
 */
public interface PgSecuBondYieldSpreadCurveMapper extends PgBaseMapper<SecuBondYieldSpreadCurveParameter>{

    /**
     * 创建实体表(评级分片)
     * @param parameter 创建条件
     */
    void createTableRatingRouter(@Param("parameter") SecuBondYieldSpreadCurveParameter parameter);

    /**
     * 同步数据
     * @param tableName 表名称
     * @param mvTableName 视图名称
     */
    void syncCurveIncrFromMV(@Param("tableName") String tableName, @Param("mvTableName") String mvTableName);

}
