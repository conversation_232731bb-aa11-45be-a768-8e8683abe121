package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * 用户利差展示设置表实体对象
 *
 * <AUTHOR>
 */
@Table(name = "user_spread_config")
public class UserSpreadConfigDO {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column
    private Long id;
    /**
     * 用户id
     */
    @Column
    private Long userId;
    /**
     * 配置id
     */
    @Column
    private Long tabConfigId;
    /**
     * json 配置
     */
    @Column
    private String configJson;
    /**
     * 是否删除
     */
    @Column
    private Integer deleted;
    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }


    public Long getTabConfigId() {
        return tabConfigId;
    }

    public void setTabConfigId(Long tabConfigId) {
        this.tabConfigId = tabConfigId;
    }


    public String getConfigJson() {
        return configJson;
    }

    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }


    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }


    public Timestamp getCreateTime() {
        return java.util.Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = java.util.Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }


    public Timestamp getUpdateTime() {
        return java.util.Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = java.util.Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }
}

