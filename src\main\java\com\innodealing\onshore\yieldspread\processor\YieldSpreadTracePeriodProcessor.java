package com.innodealing.onshore.yieldspread.processor;

import com.google.common.collect.Lists;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.enums.ITracePeriodCommonEnum;
import com.innodealing.onshore.yieldspread.enums.PeriodEnum;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.*;

/**
 * 利差追踪-期限利差处理器
 *
 * <AUTHOR>
 */
@Component
public class YieldSpreadTracePeriodProcessor implements YieldSpreadTraceProcessor {
    private static final Logger logger = LoggerFactory.getLogger(YieldSpreadTracePeriodProcessor.class);

    private static final Set<YieldPanoramaBondTypeEnum> SUPPORT_BOND_TYPES = EnumSet.of(CHINA_BOND, CD_BOND, INDUSTRIAL_BOND,
            URBAN_BOND, MEDIUM_AND_SHORT_TERMS_NOTE, GENERAL_BANK_BOND, BANK_SECONDARY_CAPITAL_BOND,
            BANK_PERPETUAL_BOND, SECURITIES_BOND, SECURITIES_SUB_BOND, SECURITIES_PERPETUAL_BOND,
            INSU_CAPITAL_SUPPLEMENT, INTEREST_RATE_BOND);

    @Override
    public Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum) {
        return SUPPORT_BOND_TYPES.contains(traceBondTypeEnum);
    }

    @Override
    public List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context) {
        YieldPanoramaBondTypeEnum bondTypeEnum = context.getBondTypeEnum();
        List<PgBondYieldPanoramaAbsDO> absBondYieldPanoramas = context.getAbsBondYieldPanoramas();
        List<PgBondYieldSpreadTraceAbsDO> dataList = Lists.newArrayListWithCapacity(absBondYieldPanoramas.size());
        Optional<Class<? extends ITracePeriodCommonEnum>> tracePeriodEnumOpt = bondTypeEnum.getTracePeriodEnum();
        if (!tracePeriodEnumOpt.isPresent()) {
            return dataList;
        }
        Class<? extends ITracePeriodCommonEnum> tracePeriodCommonEnumClass = tracePeriodEnumOpt.get();
        List<? extends ITracePeriodCommonEnum> tracePeriodCommonEnums = ITracePeriodCommonEnum.listAllEnums(tracePeriodCommonEnumClass);
        for (PgBondYieldPanoramaAbsDO absBondYieldPanorama : absBondYieldPanoramas) {
            PgBondYieldSpreadTraceAbsDO pgBondYieldSpreadTraceAbsDO = new PgBondYieldSpreadTraceAbsDO();
            pgBondYieldSpreadTraceAbsDO.setBondType(bondTypeEnum.getValue());
            pgBondYieldSpreadTraceAbsDO.setChartType(YieldSpreadChartTypeEnum.TENOR_SPREAD.getValue());
            pgBondYieldSpreadTraceAbsDO.setCurveCode(absBondYieldPanorama.getCurveCode());
            for (ITracePeriodCommonEnum tracePeriodCommonEnum : tracePeriodCommonEnums) {
                try {
                    Object subtrahendValue = YieldSpreadHelper.getValueByGetMethod(
                            getPeriodFieldName(tracePeriodCommonEnum.getSubtrahendEnum()).orElse(null)
                            , absBondYieldPanorama).orElse(null);
                    Object minuendValue = YieldSpreadHelper.getValueByGetMethod(
                            getPeriodFieldName(tracePeriodCommonEnum.getMinuendEnum()).orElse(null)
                            , absBondYieldPanorama).orElse(null);
                    YieldSpreadHelper.setValueBySetMethod(pgBondYieldSpreadTraceAbsDO,
                            Objects.requireNonNull(getPeriodFieldName(tracePeriodCommonEnum.getPeriodEnum()).orElse(null)),
                            this.safeSubtract((BigDecimal) subtrahendValue, (BigDecimal) minuendValue), BigDecimal.class);
                } catch (Exception e) {
                    logger.error("通过反射设置期限利差时出错,message: {}", e.getMessage());
                }
            }
            pgBondYieldSpreadTraceAbsDO.setIssueDate(context.getIssueDate());
            pgBondYieldSpreadTraceAbsDO.setDeleted(Deleted.NO_DELETED.getValue());
            dataList.add(pgBondYieldSpreadTraceAbsDO);
        }
        return dataList;
    }

    /**
     * 只在此处适用,关联实体对象字段名固定格式 {@link PgBondYieldSpreadTraceAbsDO} {@link PgBondYieldPanoramaAbsDO}
     *
     * @param periodEnum 期限枚举
     * @return 字段名
     */
    private Optional<String> getPeriodFieldName(PeriodEnum periodEnum) {
        if (Objects.isNull(periodEnum)) {
            return Optional.empty();
        }
        return Optional.of("ytm" + periodEnum.getText());
    }
}
