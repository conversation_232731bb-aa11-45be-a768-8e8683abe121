package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.dto.financialinstitution.BankFinIndicatorResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.financialinstitution.FinancialIndicatorResponseDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 金融服务
 *
 * <AUTHOR>
 **/
@FeignClient(name = "financialInstitutionService", url = "${bond.financial.institution.api.url}", path = "/internal")
public interface FinancialInstitutionService {
    /**
     * 获取采集数据
     *
     * @param comUniCodeList 发行人编码
     * @param reportDate     报告时间
     * @return 财报数据
     */
    @GetMapping("financial/supplement/indicator")
    List<FinancialIndicatorResponseDTO> listFinancialIndicatorResponseDTO(@RequestParam List<Long> comUniCodeList,
                                                                          @RequestParam Date reportDate);

    /**
     * 获取金融采集数据
     *
     * @param comUniCodeList 发行人编码
     * @param reportDate     报告时间
     * @return 财报数据
     */
    @GetMapping("financial/supplement/indicator/bank")
    List<BankFinIndicatorResponseDTO> listBankFinancialIndicatorResponseDTO(@RequestParam List<Long> comUniCodeList,
                                                                            @RequestParam Date reportDate);

    /**
     * 根据主体编码获取采集数据
     *
     * @param comUniCodes 发行人唯一代码
     * @param spreadDate  报告时间
     * @return key 发行人唯一代码,value 城投主体信息
     */
    default Map<Long, FinancialIndicatorResponseDTO> getFinancialIndicatorMap(Set<Long> comUniCodes, Date spreadDate) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return Collections.emptyMap();
        }
        return listFinancialIndicatorResponseDTO(new ArrayList<>(comUniCodes), spreadDate).stream()
                .collect(Collectors.toMap(FinancialIndicatorResponseDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 根据主体编码获取采集数据
     *
     * @param comUniCodes 发行人唯一代码
     * @param spreadDate  报告时间
     * @return key 发行人唯一代码,value 城投主体信息
     */
    default Map<Long, BankFinIndicatorResponseDTO> getBankFinancialIndicatorMap(Set<Long> comUniCodes, Date spreadDate) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return Collections.emptyMap();
        }
        return listBankFinancialIndicatorResponseDTO(new ArrayList<>(comUniCodes), spreadDate).stream()
                .collect(Collectors.toMap(BankFinIndicatorResponseDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }




}
