package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 曲线池列表
 *
 * <AUTHOR>
 */
public class CurvePoolListResDTO {

    @ApiModelProperty("我的曲线组")
    private List<CurveGroupResDTO> myCurveGroups;

    @ApiModelProperty("基准曲线组")
    private List<CurveGroupResDTO> benchmarkCurveGroups;

    public CurvePoolListResDTO() {
    }

    /**
     * 构造方法
     *
     * @param myCurveGroups        我的曲线
     * @param benchmarkCurveGroups 基准曲线
     */
    @SuppressWarnings("squid:S2384")
    public CurvePoolListResDTO(List<CurveGroupResDTO> myCurveGroups, List<CurveGroupResDTO> benchmarkCurveGroups) {
        this.myCurveGroups = myCurveGroups;
        this.benchmarkCurveGroups = benchmarkCurveGroups;
    }

    public List<CurveGroupResDTO> getMyCurveGroups() {
        return Objects.isNull(myCurveGroups) ? new ArrayList<>() : new ArrayList<>(myCurveGroups);
    }

    public void setMyCurveGroups(List<CurveGroupResDTO> myCurveGroups) {
        this.myCurveGroups = Objects.isNull(myCurveGroups) ? new ArrayList<>() : new ArrayList<>(myCurveGroups);
    }

    public List<CurveGroupResDTO> getBenchmarkCurveGroups() {
        return Objects.isNull(benchmarkCurveGroups) ? new ArrayList<>() : new ArrayList<>(benchmarkCurveGroups);
    }

    public void setBenchmarkCurveGroups(List<CurveGroupResDTO> benchmarkCurveGroups) {
        this.benchmarkCurveGroups = Objects.isNull(benchmarkCurveGroups) ? new ArrayList<>() : new ArrayList<>(benchmarkCurveGroups);
    }

    @Override
    public String toString() {
        return "CurvePoolListResDTO{" +
                "myCurveGroups=" + myCurveGroups +
                ", benchmarkCurveGroups=" + benchmarkCurveGroups +
                '}';
    }

}
