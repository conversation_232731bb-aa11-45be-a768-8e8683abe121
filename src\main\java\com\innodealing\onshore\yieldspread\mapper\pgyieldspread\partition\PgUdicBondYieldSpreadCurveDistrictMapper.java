package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgUdicBondYieldSpreadCurveDistrictDO;

/**
 * pg城投债利差曲线-区县Mapper
 *
 * <AUTHOR>
 **/
public interface PgUdicBondYieldSpreadCurveDistrictMapper extends DynamicQueryMapper<PgUdicBondYieldSpreadCurveDistrictDO> {

    /**
     * 从物化视图刷新城投利差曲线数据
     */
    void refreshUdicBondYieldSpreadCurveFromMV();

    /**
     * 从物化视图中同步昨日利差曲线数据
     */
    void syncCurveIncrFromMV();

}
