package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayStringJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 城投利差全景响应DTO
 *
 * <AUTHOR>
 */
public class UdicSpreadPanoramaResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 省份编码
     */
    @ApiModelProperty("省份编码")
    private Long provinceUniCode;
    /**
     * 省份名称
     */
    @ApiModelProperty("省份名称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String provinceName;
    /**
     * 地级市编码
     */
    @ApiModelProperty("地级市编码")
    private Long cityUniCode;
    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String cityName;
    /**
     * 行政区域
     */
    @ApiModelProperty("行政区域")
    private Integer administrativeRegion;

    /**
     * 利差日期
     */
    @ApiModelProperty("利差日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date spreadDate;
    /**
     * 信用利差
     */
    @ApiModelProperty("债券信用利差")
    private BigDecimal bondCreditSpread;

    /**
     * 90天前信用利差变动
     */
    @ApiModelProperty("90天前债券信用利差变动")
    private BigDecimal bondCreditSpreadChange90;

    /**
     * 180天前信用利差变动
     */
    @ApiModelProperty("180天前债券信用利差变动")
    private BigDecimal bondCreditSpreadChange180;

    /**
     * 超额利差
     */
    @ApiModelProperty("债券超额利差")
    private BigDecimal bondExcessSpread;

    /**
     * 90天前超额利差变动
     */
    @ApiModelProperty("90天前债券超额利差变动")
    private BigDecimal bondExcessSpreadChange90;

    /**
     * 180天前超额利差变动
     */
    @ApiModelProperty("180天前债券超额利差变动")
    private BigDecimal bondExcessSpreadChange180;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getAdministrativeRegion() {
        return administrativeRegion;
    }

    public void setAdministrativeRegion(Integer administrativeRegion) {
        this.administrativeRegion = administrativeRegion;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }


    public BigDecimal getBondCreditSpreadChange90() {
        return bondCreditSpreadChange90;
    }

    public void setBondCreditSpreadChange90(BigDecimal bondCreditSpreadChange90) {
        this.bondCreditSpreadChange90 = bondCreditSpreadChange90;
    }

    public BigDecimal getBondCreditSpreadChange180() {
        return bondCreditSpreadChange180;
    }

    public void setBondCreditSpreadChange180(BigDecimal bondCreditSpreadChange180) {
        this.bondCreditSpreadChange180 = bondCreditSpreadChange180;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getBondExcessSpreadChange90() {
        return bondExcessSpreadChange90;
    }

    public void setBondExcessSpreadChange90(BigDecimal bondExcessSpreadChange90) {
        this.bondExcessSpreadChange90 = bondExcessSpreadChange90;
    }

    public BigDecimal getBondExcessSpreadChange180() {
        return bondExcessSpreadChange180;
    }

    public void setBondExcessSpreadChange180(BigDecimal bondExcessSpreadChange180) {
        this.bondExcessSpreadChange180 = bondExcessSpreadChange180;
    }
}
