package com.innodealing.onshore.yieldspread.dao.yieldspread.view;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.view.BankComYieldSpreadViewMapper;
import com.innodealing.onshore.yieldspread.model.view.BankComYieldSpreadDynamicView;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.Collection;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;


/**
 * 行业主体利差视图
 *
 * <AUTHOR>
 */
@Repository
public class BankComYieldSpreadViewDAO {
    @Resource
    private BankComYieldSpreadViewMapper bankComYieldSpreadViewMapper;


    /**
     * 查询主体利差数据集
     *
     * @param spreadDate 利差日期
     * @param udicComs   主体唯一编码集合
     * @return {@link List}<{@link BankComYieldSpreadDynamicView}>
     */
    public List<BankComYieldSpreadDynamicView> listComYieldSpreads(@NonNull Date spreadDate, @NonNull Collection<Long> udicComs) {
        DynamicQuery<BankComYieldSpreadDynamicView> query = DynamicQuery.createQuery(BankComYieldSpreadDynamicView.class)
                .and(BankComYieldSpreadDynamicView::getSpreadDate, isEqual(spreadDate))
                .and(BankComYieldSpreadDynamicView::getComUniCode, in(udicComs));
        return bankComYieldSpreadViewMapper.selectByDynamicQuery(query);
    }
}
