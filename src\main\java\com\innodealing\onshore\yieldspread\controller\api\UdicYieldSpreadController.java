package com.innodealing.onshore.yieldspread.controller.api;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicBondYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicComYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicSpreadPanoramaResponseDTO;
import com.innodealing.onshore.yieldspread.service.UdicBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.UdicComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 城投利差分析接口
 *
 * <AUTHOR>
 * @date 2022/08/12
 **/
@Api(tags = "(API)利差分析-城投")
@RestController
@RequestMapping("api/udic/yield-spread")
public class UdicYieldSpreadController {

    @Resource
    private UdicBondYieldSpreadService udicBondYieldSpreadService;

    @Resource
    private UdicComYieldSpreadService udicComYieldSpreadService;

    @ApiOperation("城投利差分析-全景图")
    @PostMapping("panoramas")
    public RestResponse<List<UdicSpreadPanoramaResponseDTO>> listPanoramas(
            @ApiParam(name = "requestDTO", value = "城投利差-全景请求参数", required = true)
            @RequestBody UdicPanoramaRequestDTO requestDTO) {
        return RestResponse.Success(udicBondYieldSpreadService.listUdicPanoramas(requestDTO));
    }

    @ApiOperation(value = "城投利差分析-利差曲线")
    @PostMapping(value = "/list/curves")
    public RestResponse<List<UdicCurveResponseDTO>> listCurves(@RequestBody UdicCurveRequestDTO request) {
        return RestResponse.Success(udicBondYieldSpreadService.listCurves(request));
    }

    @ApiOperation(value = "城投利差分析-主体利差")
    @PostMapping(value = "/paging/com-yield-spread")
    public RestResponse<NormPagingResult<UdicComYieldSpreadResponseDTO>> getComYieldSpreadPaging(
            @RequestBody UdicListRequestDTO request) {
        return RestResponse.Success(udicComYieldSpreadService.getComYieldSpreadPaging(request));
    }

    @ApiOperation(value = "城投利差分析-查询主体利差总数")
    @PostMapping(value = "/paging/com-yield-spread/count")
    public RestResponse<Long> getComYieldSpreadPagingCount(@RequestBody UdicListRequestDTO request) {
        return RestResponse.Success(udicComYieldSpreadService.getComYieldSpreadPagingCount(request));
    }

    @ApiOperation(value = "城投利差分析-单券利差")
    @PostMapping(value = "/paging/bond-yield-spread")
    public RestResponse<NormPagingResult<UdicBondYieldSpreadResponseDTO>> getBondYieldSpreadPaging(
            @RequestBody UdicListRequestDTO request) {
        return RestResponse.Success(udicBondYieldSpreadService.getBondYieldSpreadPaging(request));
    }

    @PostMapping("curve-save")
    @ApiOperation(value = "保存曲线")
    public RestResponse<Boolean> saveCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated UdicCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(udicBondYieldSpreadService.saveCurve(userid, curveGroupId, request));
    }

    @PostMapping("curve-update")
    @ApiOperation(value = "更新曲线")
    public RestResponse<Boolean> updateCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线名称") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Validated UdicCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(udicBondYieldSpreadService.updateCurve(userid, curveId, request));
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public RestResponse<NormPagingResult<UdicBondYieldSpreadResponseDTO>> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(udicBondYieldSpreadService.pagingSingleBondYieldSpreads(userid, request));
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差 ")
    public RestResponse<List<UdicComYieldSpreadResponseDTO>> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(udicComYieldSpreadService.listComYieldSpreads(userid, request));
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public RestResponse<Long> countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(udicComYieldSpreadService.countComYieldSpread(userid, request));
    }

}
