package com.innodealing.onshore.yieldspread.dao.yieldspread.view;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.view.UdicComYieldSpreadViewMapper;
import com.innodealing.onshore.yieldspread.model.view.UdicComYieldSpreadDynamicView;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.Collection;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;


/**
 * 城投主体利差视图
 *
 * <AUTHOR>
 */
@Repository
public class UdicComYieldSpreadViewDAO {
    @Resource
    private UdicComYieldSpreadViewMapper udicComYieldSpreadViewMapper;

    /**
     * 查询主体利差数据集
     *
     * @param spreadDate 利差日期
     * @param udicComs   主体唯一编码集合
     * @return {@link List}<{@link UdicComYieldSpreadDynamicView}>
     */
    public List<UdicComYieldSpreadDynamicView> listComYieldSpreads(@NonNull Date spreadDate, @NonNull Collection<Long> udicComs) {
        DynamicQuery<UdicComYieldSpreadDynamicView> query = DynamicQuery.createQuery(UdicComYieldSpreadDynamicView.class)
                .and(UdicComYieldSpreadDynamicView::getSpreadDate, isEqual(spreadDate))
                .and(UdicComYieldSpreadDynamicView::getComUniCode, in(udicComs));
        return udicComYieldSpreadViewMapper.selectByDynamicQuery(query);
    }
}
