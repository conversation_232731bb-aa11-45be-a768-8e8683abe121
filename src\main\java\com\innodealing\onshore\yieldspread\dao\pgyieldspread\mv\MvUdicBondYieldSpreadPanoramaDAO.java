package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvUdicBondYieldSpreadPanoramaMapper;
import com.innodealing.onshore.yieldspread.model.bo.UdicSpreadPanoramaBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvUdicBondYieldSpreadPanoramaDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.notEqual;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.divideHundredThousand;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 城投债利差全景DAO
 *
 * <AUTHOR>
 **/
@Repository
public class MvUdicBondYieldSpreadPanoramaDAO {

    @Resource
    private MvUdicBondYieldSpreadPanoramaMapper mvUdicBondYieldSpreadPanoramaMapper;

    /**
     * 刷新城投利差全景物化视图
     */
    public void refreshMvUdicBondYieldSpreadPanorama() {
        mvUdicBondYieldSpreadPanoramaMapper.refreshMvUdicBondYieldSpreadPanorama();
    }

    /**
     * 查询城投利差全景-物化视图
     *
     * @param spreadDate                  利差日期
     * @param administrativeDivision      行政区划
     * @param spreadBondType              债券类型
     * @param spreadRemainingTenorTag     剩余期限
     * @param guaranteeStatus             担保状态
     * @param provinceUniCode             省份编码
     * @param bondExtRatingMapping        债券外部评级映射
     * @param bondImpliedRatingMappingTag 债券隐含评级
     * @param comYyRatingMappingTag       yy评级
     * @return UdicSpreadPanoramaBO
     */
    public List<UdicSpreadPanoramaBO> listMvUdicBondYieldSpreadPanoramas(Date spreadDate, Integer administrativeDivision,
                                                                         Integer spreadBondType, Integer spreadRemainingTenorTag,
                                                                         Integer guaranteeStatus, Long provinceUniCode,
                                                                         Integer bondExtRatingMapping, Integer bondImpliedRatingMappingTag, Integer comYyRatingMappingTag) {
        DynamicQuery<MvUdicBondYieldSpreadPanoramaDO> groupQuery = DynamicQuery.createQuery(MvUdicBondYieldSpreadPanoramaDO.class)
                .select(MvUdicBondYieldSpreadPanoramaDO::getProvinceUniCode,
                        MvUdicBondYieldSpreadPanoramaDO::getCityUniCode,
                        MvUdicBondYieldSpreadPanoramaDO::getSpreadDate,
                        MvUdicBondYieldSpreadPanoramaDO::getBondCreditSpreadCount,
                        MvUdicBondYieldSpreadPanoramaDO::getBondExcessSpreadCount,
                        MvUdicBondYieldSpreadPanoramaDO::getBondCreditSpread,
                        MvUdicBondYieldSpreadPanoramaDO::getBondExcessSpread)
                .and(MvUdicBondYieldSpreadPanoramaDO::getSpreadDate, isEqual(spreadDate))
                .and(nonNull(administrativeDivision), MvUdicBondYieldSpreadPanoramaDO::getAdministrativeDivision, isEqual(administrativeDivision))
                .and(isNull(administrativeDivision), MvUdicBondYieldSpreadPanoramaDO::getUsingAdministrativeDivision, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(bondExtRatingMapping), MvUdicBondYieldSpreadPanoramaDO::getBondExtRatingMapping, isEqual(bondExtRatingMapping))
                .and(isNull(bondExtRatingMapping), MvUdicBondYieldSpreadPanoramaDO::getUsingBondExtRatingMapping, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(spreadBondType), MvUdicBondYieldSpreadPanoramaDO::getSpreadBondType, isEqual(spreadBondType))
                .and(isNull(spreadBondType), MvUdicBondYieldSpreadPanoramaDO::getUsingSpreadBondType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(guaranteeStatus), MvUdicBondYieldSpreadPanoramaDO::getGuaranteedStatus, isEqual(guaranteeStatus))
                .and(isNull(guaranteeStatus), MvUdicBondYieldSpreadPanoramaDO::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(spreadRemainingTenorTag), MvUdicBondYieldSpreadPanoramaDO::getSpreadRemainingTenorTag, isEqual(spreadRemainingTenorTag))
                .and(isNull(spreadRemainingTenorTag), MvUdicBondYieldSpreadPanoramaDO::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(bondImpliedRatingMappingTag), MvUdicBondYieldSpreadPanoramaDO::getBondImpliedRatingMappingTag, isEqual(bondImpliedRatingMappingTag))
                .and(isNull(bondImpliedRatingMappingTag), MvUdicBondYieldSpreadPanoramaDO::getUsingBondImpliedRatingMappingTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(comYyRatingMappingTag), MvUdicBondYieldSpreadPanoramaDO::getComYyRatingMappingTag, isEqual(comYyRatingMappingTag))
                .and(isNull(comYyRatingMappingTag), MvUdicBondYieldSpreadPanoramaDO::getUsingComYyRatingMappingTag, isEqual(UNUSED_FIELD_GROUP.getValue()));
        if (Objects.isNull(provinceUniCode)) {
            groupQuery.and(MvUdicBondYieldSpreadPanoramaDO::getProvinceUniCode, notEqual(null))
                    .and(MvUdicBondYieldSpreadPanoramaDO::getUsingCityUniCode,
                            isEqual(UNUSED_FIELD_GROUP.getValue()));
        } else {
            groupQuery.and(MvUdicBondYieldSpreadPanoramaDO::getProvinceUniCode, isEqual(provinceUniCode))
                    .and(MvUdicBondYieldSpreadPanoramaDO::getCityUniCode, notEqual(null));
        }
        List<MvUdicBondYieldSpreadPanoramaDO> mvUdicBondYieldSpreadPanoramaDOs = mvUdicBondYieldSpreadPanoramaMapper
                .selectByDynamicQuery(groupQuery);
        if (CollectionUtils.isEmpty(mvUdicBondYieldSpreadPanoramaDOs)) {
            return Collections.emptyList();
        }
        return mvUdicBondYieldSpreadPanoramaDOs.stream().map(x -> {
            UdicSpreadPanoramaBO result = BeanCopyUtils.copyProperties(x, UdicSpreadPanoramaBO.class);
            // 物化视图中的数据乘以100_000,需要除以100_000
            ObjectExtensionUtils.ifNonNull(x.getBondCreditSpread(), v -> divideHundredThousand(BigDecimal.valueOf(v)).ifPresent(result::setBondCreditSpread));
            ObjectExtensionUtils.ifNonNull(x.getBondExcessSpread(), v -> divideHundredThousand(BigDecimal.valueOf(v)).ifPresent(result::setBondExcessSpread));
            return result;
        }).collect(Collectors.toList());
    }
}
