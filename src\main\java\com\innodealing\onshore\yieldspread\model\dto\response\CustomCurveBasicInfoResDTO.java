package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * 自选曲线基础信息
 *
 * <AUTHOR>
 */
public class CustomCurveBasicInfoResDTO {

    @ApiModelProperty("曲线id")
    private Long id;

    @ApiModelProperty("曲线名称")
    private String spreadCurveName;

    @ApiModelProperty("曲线生成状态：待生成=1，生成中=2，已生成=3，生成失败=4")
    private Integer generateStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSpreadCurveName() {
        return spreadCurveName;
    }

    public void setSpreadCurveName(String spreadCurveName) {
        this.spreadCurveName = spreadCurveName;
    }

    public Integer getGenerateStatus() {
        return generateStatus;
    }

    public void setGenerateStatus(Integer generateStatus) {
        this.generateStatus = generateStatus;
    }

}
