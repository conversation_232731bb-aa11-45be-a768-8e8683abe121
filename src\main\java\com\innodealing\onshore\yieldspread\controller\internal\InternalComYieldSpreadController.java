package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.ComYieldSpreadListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.TrendReplayYieldSpreadRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.ComYieldSpreadCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.ComYieldSpreadListResponseDTO;
import com.innodealing.onshore.yieldspread.service.ComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;

/**
 * 主体利差控制器
 *
 * <AUTHOR>
 */
@Api(tags = "(内部)主体利差")
@RestController
@Validated
@RequestMapping("internal/com/yield-spread")
public class InternalComYieldSpreadController {

    @Resource
    private ComYieldSpreadService comYieldSpreadService;

    @ApiOperation(value = "曲线")
    @GetMapping(value = "/curves")
    public List<ComYieldSpreadCurveResponseDTO> listCurves(
            @ApiParam(value = "主体唯一编码", name = "comUniCode", required = true)
            @RequestParam Long comUniCode,
            @ApiParam(value = "曲线类型: 1:产业，2:城投，3:银行，4:证券，5:自定义", name = "curveType", required = true)
            @RequestParam Integer curveType,
            @ApiParam(value = "利差债券类型、求偿顺序 0:私募债(产业、城投)|普通债(银行、证券), 1:公募债(产业、城投)|次级债(银行、证券), 2:永续债(产业、城投、银行、证券)",
                    name = "spreadBondRanking")
            @RequestParam(required = false) Integer spreadBondRanking,
            @ApiParam(value = "开始日期", name = "startDate", required = true)
            @RequestParam Date startDate,
            @ApiParam(value = "结束日期", name = "endDate", required = true)
            @RequestParam Date endDate) {
        return comYieldSpreadService.listCurves(comUniCode, curveType, spreadBondRanking, startDate, endDate);
    }

    @ApiOperation(value = "主体列表数据")
    @PostMapping(value = "/list-coms")
    public ComYieldSpreadListResponseDTO listComs(@RequestBody @Validated ComYieldSpreadListRequestDTO request) {
        return comYieldSpreadService.listComs(request);
    }

    @ApiOperation(value = "刷新主体利差物化视图")
    @PostMapping(value = "/refresh")
    public void refresh() {
        comYieldSpreadService.refreshMv();
    }

    @ApiOperation(value = "查询主体利差利差")
    @PostMapping("/trend/replay/curves")
    public List<ComCreditSpreadDTO> trendReplayCurves(@RequestBody @Validated TrendReplayYieldSpreadRequestDTO requestDTO) {
        return comYieldSpreadService.trendReplayCurves(requestDTO);
    }



}
