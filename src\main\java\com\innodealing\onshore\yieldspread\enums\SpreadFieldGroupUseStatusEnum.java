package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * Greenplum中的cube函数可以对多个字段进行分组，
 * 通过grouping(column)可以判断是否使用该字段进行分组，1(未使用)，0(使用)
 * 利差分组字段使用状态枚举
 *
 * <AUTHOR>
 */
public enum SpreadFieldGroupUseStatusEnum implements ITextValueEnum {
    /**
     * 使用列进行分组
     */
    USE_FIELD_GROUP(0, "使用"),
    /**
     * 未使用列进行分组
     */
    UNUSED_FIELD_GROUP(1, "未使用");

    private Integer code;
    private String text;

    SpreadFieldGroupUseStatusEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }
}
