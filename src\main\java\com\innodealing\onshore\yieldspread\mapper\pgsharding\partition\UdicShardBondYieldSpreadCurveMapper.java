package com.innodealing.onshore.yieldspread.mapper.pgsharding.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.UdicShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.ibatis.annotations.Param;
import org.apache.shardingsphere.api.hint.HintManager;

import javax.persistence.Table;
import java.util.List;
import java.util.Optional;

/**
 * 城投利差曲线-物化视图
 *
 * <AUTHOR>
 */
public interface UdicShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<UdicShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     * @return tableName
     */
    default String getLogicTable() {
        return UdicShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }

    /**
     * 城投利差曲线查询
     *
     * @param dynamicQuery dynamicQuery
     * @param router       路由
     * @return count
     */
    default int selectCountByDynamicQuery(@Param("dynamicQuery") DynamicQuery<UdicShardBondYieldSpreadCurveDO> dynamicQuery, AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectCountByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 城投利差曲线查询
     *
     * @param dynamicQuery dynamicQuery
     * @param router       路由
     * @return list
     */
    default List<UdicShardBondYieldSpreadCurveDO> selectByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<UdicShardBondYieldSpreadCurveDO> dynamicQuery,
                                                                             AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 城投利差曲线查询
     *
     * @param dynamicQuery dynamicQuery
     * @param router       路由
     * @return optional
     */
    default Optional<UdicShardBondYieldSpreadCurveDO> selectFirstByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<UdicShardBondYieldSpreadCurveDO> dynamicQuery,
                                                                                      AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectFirstByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 城投利差曲线删除
     *
     * @param dynamicQuery dynamicQuery
     * @param router       路由
     * @return count
     */
    default int deleteByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<UdicShardBondYieldSpreadCurveDO> dynamicQuery, AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return deleteByDynamicQuery(dynamicQuery);
        }
    }
}
