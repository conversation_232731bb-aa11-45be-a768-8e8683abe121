package com.innodealing.onshore.yieldspread.service.impl;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.enums.BusinessNatureEnum;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.InsuComYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InsuComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.view.InsuComYieldSpreadViewDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.model.bo.ComCreditSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.InsuComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.InsuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.InsuCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InsuComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.view.InsuComYieldSpreadDynamicView;
import com.innodealing.onshore.yieldspread.service.InsuComYieldSpreadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;

/**
 * 保险主体利差 Service
 *
 * <AUTHOR>
 **/
@SuppressWarnings({"squid:S1854", "squid:S1481", "squid:S138"})
@Service
public class InsuComYieldSpreadServiceImpl extends AbstractComYieldSpreadService implements InsuComYieldSpreadService {

    private static final int COM_MAX_LIMIT = 200;

    @Resource
    private InsuComYieldSpreadDAO insuComYieldSpreadDAO;

    @Resource
    private InsuComYieldSpreadRedisDAO insuComYieldSpreadRedisDAO;

    @Resource
    private InsuComYieldSpreadViewDAO insuComYieldSpreadViewDAO;


    @Override
    public List<ComCreditSpreadDTO> listCreditSpreadByComUniCodes(Set<Long> comUniCodes) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return Collections.emptyList();
        }
        Set<Long> notNullComUniCodes = comUniCodes.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (notNullComUniCodes.size() > COM_MAX_LIMIT) {
            logger.warn("超过单次获取最大条数200，实际请求条数:{}", notNullComUniCodes.size());
            throw new BusinessException("超过单次获取最大条数200, 实际请求条数:" + notNullComUniCodes.size());
        }

        List<ComCreditSpreadBO> insuComYieldSpreadBOList = insuComYieldSpreadDAO.listComCreditSpreads(comUniCodes);
        return BeanCopyUtils.copyList(insuComYieldSpreadBOList, ComCreditSpreadDTO.class);
    }

    /**
     * 获取主体利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体利差
     */
    @Override
    public List<InsuComYieldSpreadResDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        InsuCurveGenerateConditionReqDTO curveGenerateCondition = super.getCurveGenerateCondition(
                userid, request.getCurveId(), InsuCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), curveGenerateCondition.getComOrBondCondition())) {
            return Collections.emptyList();
        }
        boolean isToday = super.isToday(request.getSpreadDate());
        InsuYieldSearchParam param = super.buildComYieldSearchParam(request, curveGenerateCondition, InsuYieldSearchParam.class, InsuComYieldSpreadDO.class);
        List<InsuComYieldSpreadBO> insuComYieldSpreads = insuComYieldSpreadDAO.listComYieldSpreads(isToday, param);
        if (CollectionUtils.isEmpty(insuComYieldSpreads)) {
            return Collections.emptyList();
        }
        Set<Long> comUniCodes = insuComYieldSpreads.stream().map(InsuComYieldSpreadBO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return insuComYieldSpreads.stream().map(com -> {
            InsuComYieldSpreadResDTO response = BeanCopyUtils.copyProperties(com, InsuComYieldSpreadResDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRatingMappingStr(RatingUtils.getRating(response.getComExtRatingMapping()));
            EnumUtils.getEnumByValue(response.getBusinessNature(), BusinessNatureEnum.class).ifPresent(v -> response.setBusinessNatureStr(v.getText()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getNetProfit()).ifPresent(response::setNetProfit);
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 获取主体利差条数
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体利差条数
     */
    @Override
    public Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request) {
        InsuCurveGenerateConditionReqDTO curveGenerateCondition = super.getCurveGenerateCondition(
                userid, request.getCurveId(), InsuCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), curveGenerateCondition.getComOrBondCondition())) {
            return 0L;
        }
        return super.getComCountFromRedis(request, SPREAD_INSU_COM_SPREAD_COUNT_KEY, () -> {
            InsuYieldSearchParam param = super.buildComYieldSearchParam(request, curveGenerateCondition, InsuYieldSearchParam.class, null);
            return insuComYieldSpreadDAO.countComYieldSpread(param);
        });
    }

    /**
     * 查询利差曲线数据集
     *
     * @param comUniCode                主体唯一编码
     * @param insuranceSeniorityRanking 银行求偿顺序
     * @param startDate                 开始日期
     * @param endDate                   结束日期
     * @return {@link List}<{@link ComYieldSpreadCurveDTO}>
     * @see com.innodealing.onshore.yieldspread.enums.InsuSeniorityRankingEnum
     */
    @Override
    public List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer insuranceSeniorityRanking, Date startDate, Date endDate) {
        return insuComYieldSpreadRedisDAO.listCurves(comUniCode, insuranceSeniorityRanking, startDate, endDate);
    }

    /**
     * 获取保险主体利差数据集
     *
     * @param spreadDate 利差日期
     * @param insuComs   主体唯一编码集合
     * @return {@link List}<{@link InsuComYieldSpreadResDTO}>
     */
    @Override
    public List<InsuComYieldSpreadResDTO> listComs(Date spreadDate, Set<Long> insuComs) {
        if (org.springframework.util.CollectionUtils.isEmpty(insuComs)) {
            return Collections.emptyList();
        }
        // 今天的话要加上变动数据
        List<InsuComYieldSpreadResDTO> insuResponseList;
        if (super.isToday(spreadDate)) {
            spreadDate = this.getMaxSpreadDate();
            List<InsuComYieldSpreadDynamicView> insuComYieldSpreads = insuComYieldSpreadViewDAO.listComYieldSpreads(spreadDate, insuComs);
            insuResponseList = BeanCopyUtils.copyList(insuComYieldSpreads, InsuComYieldSpreadResDTO.class);
        } else {
            List<InsuComYieldSpreadDO> insuComYieldSpreads = insuComYieldSpreadDAO.listComYieldSpreads(spreadDate, insuComs);
            insuResponseList = BeanCopyUtils.copyList(insuComYieldSpreads, InsuComYieldSpreadResDTO.class);
        }
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(insuComs);
        return insuResponseList.stream().map(com -> {
            InsuComYieldSpreadResDTO response = BeanCopyUtils.copyProperties(com, InsuComYieldSpreadResDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRatingMappingStr(RatingUtils.getRating(response.getComExtRatingMapping()));
            EnumUtils.getEnumByValue(response.getBusinessNature(), BusinessNatureEnum.class).ifPresent(v -> response.setBusinessNatureStr(v.getText()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getNetProfit()).ifPresent(response::setNetProfit);
            return response;
        }).collect(Collectors.toList());
    }

    /**
     * 获取曲线类型
     *
     * @return 曲线类型
     */
    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.INSURANCE;
    }

    /**
     * 获取最新利差日期
     *
     * @return 最新利差日期
     */
    @Override
    protected Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_INSU_COM_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = insuComYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_INSU_COM_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }
}
