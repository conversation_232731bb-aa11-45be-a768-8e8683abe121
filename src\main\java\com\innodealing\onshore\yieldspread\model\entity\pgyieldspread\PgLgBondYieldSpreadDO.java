package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * 地方债利差表 pg
 *
 * <AUTHOR>
 */
@Table(name = "lg_bond_yield_spread")
public class PgLgBondYieldSpreadDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;
    /**
     * 债券统一编码
     */
    @Column
    private Long bondUniCode;
    /**
     * 债券编码
     */
    @Column
    private String bondCode;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 债券信用利差;单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpread;
    /**
     * 债券超额利差;单位(BP)
     */
    @Column
    private BigDecimal bondExcessSpread;
    /**
     * 中债收益率;单位(%)
     */
    @Column
    private BigDecimal cbYield;
    /**
     * 国开插值收益率;单位(%)
     */
    @Column
    private BigDecimal cdbLerpYield;
    /**
     * 国债插值收益率;单位(%)
     */
    @Column
    private BigDecimal tbLerpYield;
    /**
     * 国债-信用利差;单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpreadTb;
    /**
     * 地方债类型： 1 一般债; 2 专项债;  99 其他
     */
    @Column
    private Integer lgBondType;
    /**
     * 提前还本状态 0: 不提前还本 1: 提前还本
     */
    @Column
    private Integer prepaymentStatus;
    /**
     * 资金用途性质: 1 新增; 2 再融资; 3 置换;  99 其他
     */
    @Column
    private Integer fundUseType;
    /**
     * 债券外部评级映射
     */
    @Column
    private Integer bondExtRatingMapping;
    /**
     * 主体外部评级映射
     */
    @Column
    private Integer comExtRatingMapping;
    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;
    /**
     * 剩余期限天数
     */
    @Column
    private Integer remainingTenorDay;

    /**
     * 期限档位
     */
    @Column
    private Integer lgRemainingGrade;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(BigDecimal cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

    public BigDecimal getTbLerpYield() {
        return tbLerpYield;
    }

    public void setTbLerpYield(BigDecimal tbLerpYield) {
        this.tbLerpYield = tbLerpYield;
    }

    public BigDecimal getBondCreditSpreadTb() {
        return bondCreditSpreadTb;
    }

    public void setBondCreditSpreadTb(BigDecimal bondCreditSpreadTb) {
        this.bondCreditSpreadTb = bondCreditSpreadTb;
    }

    public Integer getLgBondType() {
        return lgBondType;
    }

    public void setLgBondType(Integer lgBondType) {
        this.lgBondType = lgBondType;
    }

    public Integer getPrepaymentStatus() {
        return prepaymentStatus;
    }

    public void setPrepaymentStatus(Integer prepaymentStatus) {
        this.prepaymentStatus = prepaymentStatus;
    }

    public Integer getFundUseType() {
        return fundUseType;
    }

    public void setFundUseType(Integer fundUseType) {
        this.fundUseType = fundUseType;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getRemainingTenorDay() {
        return remainingTenorDay;
    }

    public void setRemainingTenorDay(Integer remainingTenorDay) {
        this.remainingTenorDay = remainingTenorDay;
    }

    public Integer getLgRemainingGrade() {
        return lgRemainingGrade;
    }

    public void setLgRemainingGrade(Integer lgRemainingGrade) {
        this.lgRemainingGrade = lgRemainingGrade;
    }
}