package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadQueryParameter;
import com.innodealing.onshore.yieldspread.model.dto.UdicYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import com.innodealing.onshore.yieldspread.model.view.UdicComYieldSpreadView;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;

/**
 * 城投主体利差Mapper
 *
 * <AUTHOR>
 **/
public interface UdicComYieldSpreadMapper extends DynamicQueryMapper<UdicComYieldSpreadDO> {

    /**
     * 分页查询主体利差数据
     *
     * @param parameter 分页查询主体利差数据查询参数
     * @return {@link List}<{@link UdicComYieldSpreadDO}> 分页查询主体利差响应数据
     */
    List<UdicComYieldSpreadDO> getComYieldSpreadPagingByJoin(@Param("parameter") UdicBondYieldSpreadQueryParameter parameter);

    /**
     * 分页查询主体利差数据-包含变动数据
     *
     * @param parameter 分页查询主体利差数据请求参数
     * @return {@link List}<{@link UdicComYieldSpreadView}> 分页查询主体利差响应数据
     */
    List<UdicComYieldSpreadView> getComYieldSpreadChangePagingByJoin(@Param("parameter") UdicBondYieldSpreadQueryParameter parameter);

    /**
     * 分页查询主体利差数据总条数
     *
     * @param parameter 分页查询主体利差数据总条数请求参数
     * @return {@link Long} 总条数
     */
    Long getComYieldSpreadPagingCount(@Param("parameter") UdicBondYieldSpreadQueryParameter parameter);

    /**
     * 分页查询主体利差数据
     *
     * @param parameter 分页查询主体利差数据查询参数
     * @return {@link List}<{@link UdicComYieldSpreadDO}> 分页查询主体利差响应数据
     */
    List<UdicComYieldSpreadDO> getComYieldSpreadPagingByExists(@Param("parameter") UdicBondYieldSpreadQueryParameter parameter);

    /**
     * 分页查询主体利差数据-包含变动数据
     *
     * @param parameter 分页查询主体利差数据请求参数
     * @return {@link List}<{@link UdicComYieldSpreadView}> 分页查询主体利差响应数据
     */
    List<UdicComYieldSpreadView> getComYieldSpreadChangePagingByExists(@Param("parameter") UdicBondYieldSpreadQueryParameter parameter);

    /**
     * 获取债券主体利差
     *
     * @param isNewest  是否最新数据
     * @param parameter 查询参数
     * @return 主体利差
     */
    List<UdicComYieldSpreadView> listComYieldSpreads(@Param("isNewest") boolean isNewest, @Param("parameter") UdicYieldSearchParam parameter);

    /**
     * 获取主体数量
     *
     * @param param 查询参数
     * @return 主体数量
     */
    Long countComYieldSpread(@Param("parameter") UdicYieldSearchParam param);

    /**
     * 获取指定时间范围内的某个发行时间 对应的分位统计的数据
     *
     * @param startDate      时间范围开始
     * @param endDate        时间范围结束
     * @param issueDate      发行时间
     * @param comUniCodeList 指定主体列表
     * @return 分位 信用利差(全部) 超额利差(全部)统计的数据
     */
    List<ComYieldSpreadQuantileViewDO> listComYieldQuantileStatisticsViews(@Param("startDate") Date startDate,
                                                                           @Param("endDate") Date endDate,
                                                                           @Param("issueDate") Date issueDate,
                                                                           @Param("comUniCodeList") List<Long> comUniCodeList);

}
