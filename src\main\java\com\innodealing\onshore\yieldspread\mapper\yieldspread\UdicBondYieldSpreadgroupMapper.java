package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.UdicBondYieldSpreadGroupDO;

/**
 * 城投债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface UdicBondYieldSpreadgroupMapper extends SelectByGroupedQueryMapper<UdicBondYieldSpreadDO,
        UdicBondYieldSpreadGroupDO> {

}
