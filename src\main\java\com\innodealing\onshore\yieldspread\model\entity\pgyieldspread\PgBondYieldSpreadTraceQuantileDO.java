package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;
import java.util.Objects;

/**
 * 债券收益率追踪分位数
 *
 * <AUTHOR>
 */
@Table(name = "bond_yield_spread_trace_quantile")
public class PgBondYieldSpreadTraceQuantileDO extends PgBaseBondYieldSpreadTraceDO {

    /**
     * 分位数类型, 1: 3年 2: 5年
     */
    @Column
    private Integer quantileType;
    /**
     * 开始日期
     */
    @Column
    private Date startDate;

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public Integer getQuantileType() {
        return quantileType;
    }

    public void setQuantileType(Integer quantileType) {
        this.quantileType = quantileType;
    }
}