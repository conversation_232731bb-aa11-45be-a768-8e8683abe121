package com.innodealing.onshore.yieldspread.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.github.wz2cool.dynamic.SortDirection;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.commons.zip.ZipUtils;
import com.innodealing.international.common.template.utils.ExcelUtils;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationShortInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.enums.*;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.builder.BondYieldSpreadBuilder;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgLgBondYieldSpreadBaseDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgLgBondYieldSpreadChangeDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgLgBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgLgBondYieldSpreadQuantileDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.view.PgLgBondYieldSpreadAreaViewDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.view.PgLgBondYieldSpreadQuantileViewDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.LgBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserSpreadConfigDAO;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.CommonUtils;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.*;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldPanoramaTraceSpreadDateDTO;
import com.innodealing.onshore.yieldspread.model.dto.PgBondSpreadLgDTO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldLgExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadBaseDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadChangeDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadQuantileDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgBondYieldSpreadAreaViewDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgBondYieldSpreadQuantileViewDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.LgBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.service.LgYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.RedisService;
import com.innodealing.onshore.yieldspread.service.YieldSpreadCommonService;
import com.innodealing.onshore.yieldspread.service.internal.BondPriceService;
import com.innodealing.onshore.yieldspread.service.internal.BondRatingService;
import com.innodealing.onshore.yieldspread.service.internal.ComService;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.*;


/**
 * 计算地方债利差
 *
 * <AUTHOR>
 * @create: 2024-10-24
 */
@Service
public class LgYieldSpreadServiceImpl implements LgYieldSpreadService, InitializingBean {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final ExecutorService executorService;
    public static final String YIELD_SPREAD_LG_BOND_YIELD_SPREAD_FLOW_ID = "yieldSpread:lgBondYieldSpreadFlowId";
    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%s:%s:%s:%d:%d:%d";

    private static final String LG_LINE_CHART_KEY = "yield-spread:spread_lg:line_chart:%d:%d:%s";
    private static final String PLACEHOLDER = "-";
    private static final int TRACE_INTERVAL_CHANGE_SCALE = 2;


    /**
     * 地方债区域利差导出文件名称
     */
    private static final String LG_FILE_NAME = "地方债区域利差-%s";

    private final Map<PeriodEnum, Function<YieldLgDataBO, BigDecimal>> periodLgFunctionMap = Maps.newHashMap();

    /**
     * 地方债利差所有选项
     */
    private static final List<LgSpreadSelectedRequestDTO> LG_SELECTED_ALL_ITEM = Lists.newArrayList();

    /**
     * 地方区域利差导出文件填充
     */
    private static final String LG_EXPORT_FILL = "%sFill";


    protected LgYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("LgBondYieldSpreadServiceImpl-pool-").build());
    }

    @Value("${sharding.yield.spread}")
    private Date initStartDate;
    @Resource
    private ComService comService;
    @Resource
    private BondPriceService bondPriceService;
    @Resource
    private BondRatingService bondRatingService;
    @Resource
    private RedisService redisService;
    @Resource
    private LgBondYieldSpreadDAO lgBondYieldSpreadDAO;
    @Resource
    private PgLgBondYieldSpreadDAO pgLgBondYieldSpreadDAO;
    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;
    @Resource
    private PgLgBondYieldSpreadAreaViewDAO pgLgBondYieldSpreadAreaViewDAO;
    @Resource
    private PgLgBondYieldSpreadBaseDAO pgLgBondYieldSpreadBaseDAO;
    @Resource
    private PgLgBondYieldSpreadQuantileViewDAO pgLgBondYieldSpreadQuantileViewDAO;
    @Resource
    private PgLgBondYieldSpreadQuantileDAO pgLgBondYieldSpreadQuantileDAO;
    @Resource
    private HolidayService holidayService;
    @Resource
    private PgLgBondYieldSpreadChangeDAO pgLgBondYieldSpreadChangeDAO;

    @Resource
    private UserSpreadConfigDAO userSpreadConfigDAO;

    @Override
    public void afterPropertiesSet() throws Exception {
        List<Integer> lgBondTypeList = Arrays.stream(LgBondTypeEnum.values()).map(LgBondTypeEnum::getValue).collect(Collectors.toList());
        lgBondTypeList.add(null);
        List<Integer> prepaymentStatusList = Arrays.stream(PrepaymentStatusEnum.values()).map(PrepaymentStatusEnum::getValue).collect(Collectors.toList());
        prepaymentStatusList.add(null);
        List<Integer> fundUseTypeList = Arrays.stream(FundUseTypeEnum.values()).map(FundUseTypeEnum::getValue).collect(Collectors.toList());
        fundUseTypeList.add(null);
        for (Integer lgBondType : lgBondTypeList) {
            for (Integer prepaymentStatus : prepaymentStatusList) {
                for (Integer fundUseType : fundUseTypeList) {
                    LG_SELECTED_ALL_ITEM.add(LgSpreadSelectedRequestDTO.builder()
                            .lgBondType(lgBondType)
                            .prepaymentStatus(prepaymentStatus)
                            .fundUseType(fundUseType).build());
                }
            }
        }
        periodLgFunctionMap.put(PeriodEnum.ONE_MONTH, YieldLgDataBO::getYield1M);
        periodLgFunctionMap.put(PeriodEnum.THREE_MONTHS, YieldLgDataBO::getYield3M);
        periodLgFunctionMap.put(PeriodEnum.SIX_MONTHS, YieldLgDataBO::getYield6M);
        periodLgFunctionMap.put(PeriodEnum.NINE_MONTHS, YieldLgDataBO::getYield9M);
        periodLgFunctionMap.put(PeriodEnum.ONE_YEAR, YieldLgDataBO::getYield1Y);
        periodLgFunctionMap.put(PeriodEnum.TWO_YEARS, YieldLgDataBO::getYield2Y);
        periodLgFunctionMap.put(PeriodEnum.THREE_YEARS, YieldLgDataBO::getYield3Y);
        periodLgFunctionMap.put(PeriodEnum.FIVE_YEARS, YieldLgDataBO::getYield5Y);
        periodLgFunctionMap.put(PeriodEnum.SEVEN_YEARS, YieldLgDataBO::getYield7Y);
        periodLgFunctionMap.put(PeriodEnum.TEN_YEARS, YieldLgDataBO::getYield10Y);
        periodLgFunctionMap.put(PeriodEnum.FIFTEEN_YEARS, YieldLgDataBO::getYield15Y);
        periodLgFunctionMap.put(PeriodEnum.TWENTY_YEARS, YieldLgDataBO::getYield20Y);
        periodLgFunctionMap.put(PeriodEnum.THIRTY_YEARS, YieldLgDataBO::getYield30Y);
    }

    /**
     * 地方债区域利差 最大时间 redis key值
     */
    private static final String LG_AREA_SPREAD_DATE_KEY = "yield-spread:bond_yield_lg_area:%s";

    /**
     * 检查分片表
     */
    @Scheduled(initialDelay = 1000, fixedDelay = 3_600_000)
    public void checkShardingTables() {
        Date currentDate = new Date(System.currentTimeMillis());
        Date endDate = new Date(DateUtils.addDays(currentDate, 1).getTime());
        createShardingTables(initStartDate, endDate);
    }

    private void createShardingTables(Date startDate, Date endDate) {
        Collection<String> shardingTableNames = ShardingUtils.getYearShardingTableNames(LG_BOND_TABLE_NAME, startDate, endDate);
        for (String shardingTableName : shardingTableNames) {
            lgBondYieldSpreadDAO.createShardingTable(shardingTableName);
        }
    }

    public int syncHisLgBondYieldSpread(Date startDate) {
        Date endDate = Date.valueOf(LocalDate.now());
        return this.syncLgAreaBondYieldSpread(startDate, endDate);
    }

    @Override
    public int syncLgAreaBondYieldSpread(Date startDate, Date endDate) {
        AtomicInteger effectRows = new AtomicInteger();
        startDate = Objects.isNull(startDate) ? Date.valueOf(LocalDate.now().minusDays(1)) : startDate;
        endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
        for (LocalDate localStartDate = startDate.toLocalDate(); !localStartDate.isAfter(endDate.toLocalDate()); localStartDate = localStartDate.plusDays(1)) {
            Date spreadDate = Date.valueOf(localStartDate);
            effectRows.addAndGet(this.calLgBondYieldSpreadBase(spreadDate));
            effectRows.addAndGet(this.calLgBondYieldSpreadQuantile(spreadDate));
            effectRows.addAndGet(this.calLgBondYieldSpreadChange(spreadDate));
            logger.info("syncLgBondYieldSpread 同步地方债区域利差日期:{}", spreadDate);
        }
        // 将最大的日期缓存起来，当查询当天的时候，需要从缓存中查询DB中最大的日期
        String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String redisKey = String.format(LG_AREA_SPREAD_DATE_KEY, now);
        BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO = new BondYieldPanoramaTraceSpreadDateDTO();
        pgLgBondYieldSpreadBaseDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
        pgLgBondYieldSpreadQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
        pgLgBondYieldSpreadChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
        if (ObjectUtils.anyNotNull(spreadDateDTO.getAbsSpreadDate(), spreadDateDTO.getChangeSpreadDate(), spreadDateDTO.getQuantileSpreadDate())) {
            redisService.set(redisKey, JSON.toJSONString(spreadDateDTO), YieldSpreadConst.TWO_DAYS, TimeUnit.DAYS);
        }
        this.cacheLgLineChart();
        return effectRows.get();
    }

    @Override
    public int syncLgBondYieldSpreadBase(Date startDate, Date endDate) {
        AtomicInteger effectRows = new AtomicInteger();
        endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            effectRows.addAndGet(this.calLgBondYieldSpreadBase(Date.valueOf(beginDate)));
            logger.info("syncLgBondYieldSpreadBase 同步地方债区域利差开始日期:{},结束日期{}", beginDate, endDate);
        }
        return effectRows.get();
    }

    @Override
    public int syncLgBondYieldSpreadQuantile(Date startDate, Date endDate) {
        AtomicInteger effectRows = new AtomicInteger();
        endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            effectRows.addAndGet(this.calLgBondYieldSpreadQuantile(Date.valueOf(beginDate)));
            logger.info("syncLgBondYieldSpreadQuantile_all 同步地方债区域利差分位开始日期:{},结束日期{}", beginDate, endDate);
        }
        return effectRows.get();
    }

    @Override
    public int syncLgBondYieldSpreadQuantile(Date startDate, Date endDate, SpreadQuantileTypeEnum quantileTypeEnum) {
        AtomicInteger effectRows = new AtomicInteger();
        endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            effectRows.addAndGet(calLgBondYieldSpreadQuantile(Date.valueOf(beginDate), quantileTypeEnum));
            logger.info("syncLgBondYieldSpreadQuantile_single year:{} 同步地方债区域利差分位开始日期:{},结束日期{}", quantileTypeEnum.getYear(), beginDate, endDate);
        }
        return effectRows.get();
    }

    @Override
    public int syncLgBondYieldSpreadChange(Date startDate, Date endDate) {
        AtomicInteger effectRows = new AtomicInteger();
        endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            effectRows.addAndGet(this.calLgBondYieldSpreadChange(Date.valueOf(beginDate)));
            logger.info("syncLgBondYieldSpreadChange 同步地方债区域利差变动开始日期:{},结束日期{}", beginDate, endDate);
        }
        return effectRows.get();
    }

    private int calLgBondYieldSpreadBase(Date spreadDate) {
        List<PgLgBondYieldSpreadBaseDO> lgBondYieldSpreadBaseDOList = Lists.newArrayList();
        // 地方债 全国利差视图
        pgLgBondYieldSpreadAreaViewDAO.createLgBondYieldSpreadAreaView(spreadDate, spreadDate, (long) SpreadComAreaEnum.NATION.getValue());
        List<PgLgBondYieldSpreadAreaViewDO> pgLgBondYieldSpreadAreaViewDOs = pgLgBondYieldSpreadAreaViewDAO.listPgLgBondYieldSpreadArea(spreadDate, spreadDate, spreadDate);
        pgLgBondYieldSpreadAreaViewDAO.dropLgBondYieldSpreadAreaMv(spreadDate, spreadDate);

        pgLgBondYieldSpreadAreaViewDAO.createLgBondYieldSpreadAreaView(spreadDate, spreadDate, null);
        pgLgBondYieldSpreadAreaViewDOs.addAll(pgLgBondYieldSpreadAreaViewDAO.listPgLgBondYieldSpreadArea(spreadDate, spreadDate, spreadDate));
        pgLgBondYieldSpreadAreaViewDAO.dropLgBondYieldSpreadAreaMv(spreadDate, spreadDate);

        for (PgLgBondYieldSpreadAreaViewDO areaViewDO : pgLgBondYieldSpreadAreaViewDOs) {
            PgLgBondYieldSpreadBaseDO lgBondYieldSpreadBaseDO = BeanCopyUtils.copyProperties(areaViewDO, PgLgBondYieldSpreadBaseDO.class);
            lgBondYieldSpreadBaseDO.setDeleted(Deleted.NO_DELETED.getValue());
            Optional.ofNullable(lgBondYieldSpreadBaseDO.getComUniCode())
                    .flatMap(comUniCode -> EnumUtils.getEnumByValue(lgBondYieldSpreadBaseDO.getComUniCode().intValue(), SpreadComAreaEnum.class))
                    .ifPresent(spreadComAreaEnum -> lgBondYieldSpreadBaseDO.setLgAreaName(spreadComAreaEnum.getText()));
            lgBondYieldSpreadBaseDOList.add(lgBondYieldSpreadBaseDO);
        }
        return pgLgBondYieldSpreadBaseDAO.saveLgBondYieldSpreadBaseDOList(spreadDate, lgBondYieldSpreadBaseDOList);
    }

    private int calLgBondYieldSpreadQuantile(Date spreadDate) {
        int effectRows = 0;
        for (SpreadQuantileTypeEnum quantileTypeEnum : SpreadQuantileTypeEnum.values()) {
            effectRows += calLgBondYieldSpreadQuantile(spreadDate, quantileTypeEnum);
        }
        return effectRows;
    }

    private int calLgBondYieldSpreadQuantile(Date spreadDate, SpreadQuantileTypeEnum quantileTypeEnum) {
        List<PgLgBondYieldSpreadQuantileDO> quantileList = Lists.newArrayList();
        Date startDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, quantileTypeEnum);
        pgLgBondYieldSpreadQuantileViewDAO.createLgBondYieldSpreadQuantileView(startDate, spreadDate);
        List<PgLgBondYieldSpreadQuantileViewDO> pgLgBondYieldSpreadQuantileViewList =
                pgLgBondYieldSpreadQuantileViewDAO.listLgBondYieldSpreadQuantiles(startDate, spreadDate, spreadDate);
        pgLgBondYieldSpreadQuantileViewDAO.dropLgBondYieldSpreadQuantileView(startDate, spreadDate);

        for (PgLgBondYieldSpreadQuantileViewDO pgLgBondYieldSpreadQuantileViewDO : pgLgBondYieldSpreadQuantileViewList) {
            PgLgBondYieldSpreadQuantileDO lgBondYieldSpreadQuantile = BeanCopyUtils.copyProperties(pgLgBondYieldSpreadQuantileViewDO, PgLgBondYieldSpreadQuantileDO.class);
            lgBondYieldSpreadQuantile.setStartDate(startDate);
            lgBondYieldSpreadQuantile.setQuantileType(quantileTypeEnum.getValue());
            lgBondYieldSpreadQuantile.setDeleted(Deleted.NO_DELETED.getValue());
            quantileList.add(lgBondYieldSpreadQuantile);
        }
        return pgLgBondYieldSpreadQuantileDAO.saveLgBondYieldSpreadQuantileList(spreadDate, quantileList);
    }

    private int calLgBondYieldSpreadChange(Date spreadDate) {
        List<PgLgBondYieldSpreadChangeDO> changeList = Lists.newArrayList();
        // 查询 LgBondYieldSpreadBase
        List<PgLgBondYieldSpreadBaseDO> curLgBondYieldSpreadBaseDOList = pgLgBondYieldSpreadBaseDAO.listLgBondYieldSpreadBase(spreadDate);
        Map<String, PgLgBondYieldSpreadBaseDO> currentBaseMap =
                curLgBondYieldSpreadBaseDOList.stream().collect(Collectors.toMap(this::getKey, x -> x, (k1, k2) -> k1));
        for (BondYieldIntervalChangeTypeEnum changeTypeEnum : BondYieldIntervalChangeTypeEnum.values()) {
            // 获取最近一天的工作日，包含自己
            Date startDate = this.getChangeStartDate(spreadDate, changeTypeEnum);
            List<PgLgBondYieldSpreadBaseDO> beforeLgBondYieldSpreadBaseDOList = pgLgBondYieldSpreadBaseDAO.listLgBondYieldSpreadBase(startDate);
            if (CollectionUtils.isEmpty(beforeLgBondYieldSpreadBaseDOList)) {
                continue;
            }
            Map<String, PgLgBondYieldSpreadBaseDO> beforeBaseMap =
                    beforeLgBondYieldSpreadBaseDOList.stream().collect(Collectors.toMap(this::getKey, x -> x, (k1, k2) -> k1));
            for (Map.Entry<String, PgLgBondYieldSpreadBaseDO> curentEntry : currentBaseMap.entrySet()) {
                PgLgBondYieldSpreadBaseDO beforeBase = beforeBaseMap.get(curentEntry.getKey());
                Optional<PgLgBondYieldSpreadChangeDO> change = buildLgBondYieldSpreadChange(curentEntry.getValue(), beforeBase, changeTypeEnum.getValue(), startDate);
                change.ifPresent(changeList::add);
            }
        }
        return pgLgBondYieldSpreadChangeDAO.saveLgBondYieldSpreadChangeList(spreadDate, changeList);
    }

    private Optional<PgLgBondYieldSpreadChangeDO> buildLgBondYieldSpreadChange(PgLgBondYieldSpreadBaseDO currentBase, PgLgBondYieldSpreadBaseDO beforeBase,
                                                                               Integer changeType, Date startDate) {
        if (Objects.isNull(currentBase) || Objects.isNull(beforeBase)) {
            return Optional.empty();
        }
        Date spreadDate = currentBase.getSpreadDate();
        PgLgBondYieldSpreadChangeDO pgLgBondYieldSpreadChangeDO = new PgLgBondYieldSpreadChangeDO();
        pgLgBondYieldSpreadChangeDO.setChangeType(changeType);
        pgLgBondYieldSpreadChangeDO.setStartDate(startDate);
        pgLgBondYieldSpreadChangeDO.setSpreadDate(spreadDate);

        pgLgBondYieldSpreadChangeDO.setLgAreaName(currentBase.getLgAreaName());
        pgLgBondYieldSpreadChangeDO.setComUniCode(currentBase.getComUniCode());
        pgLgBondYieldSpreadChangeDO.setLgBondType(currentBase.getLgBondType());
        pgLgBondYieldSpreadChangeDO.setPrepaymentStatus(currentBase.getPrepaymentStatus());
        pgLgBondYieldSpreadChangeDO.setFundUseType(currentBase.getFundUseType());
        pgLgBondYieldSpreadChangeDO.setUsingLgBondType(currentBase.getUsingLgBondType());
        pgLgBondYieldSpreadChangeDO.setUsingPrepaymentStatus(currentBase.getUsingPrepaymentStatus());
        pgLgBondYieldSpreadChangeDO.setUsingFundUseType(currentBase.getUsingFundUseType());

        pgLgBondYieldSpreadChangeDO.setCreditSpread1M(safeSubtract(currentBase.getCreditSpread1M(), beforeBase.getCreditSpread1M()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb1M(safeSubtract(currentBase.getCreditSpreadTb1M(), beforeBase.getCreditSpreadTb1M()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield1M(safeSubtractToBp(currentBase.getCbYield1M(), beforeBase.getCbYield1M()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread3M(safeSubtract(currentBase.getCreditSpread3M(), beforeBase.getCreditSpread3M()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb3M(safeSubtract(currentBase.getCreditSpreadTb3M(), beforeBase.getCreditSpreadTb3M()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield3M(safeSubtractToBp(currentBase.getCbYield3M(), beforeBase.getCbYield3M()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread6M(safeSubtract(currentBase.getCreditSpread6M(), beforeBase.getCreditSpread6M()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb6M(safeSubtract(currentBase.getCreditSpreadTb6M(), beforeBase.getCreditSpreadTb6M()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield6M(safeSubtractToBp(currentBase.getCbYield6M(), beforeBase.getCbYield6M()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread9M(safeSubtract(currentBase.getCreditSpread9M(), beforeBase.getCreditSpread9M()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb9M(safeSubtract(currentBase.getCreditSpreadTb9M(), beforeBase.getCreditSpreadTb9M()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield9M(safeSubtractToBp(currentBase.getCbYield9M(), beforeBase.getCbYield9M()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread1Y(safeSubtract(currentBase.getCreditSpread1Y(), beforeBase.getCreditSpread1Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb1Y(safeSubtract(currentBase.getCreditSpreadTb1Y(), beforeBase.getCreditSpreadTb1Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield1Y(safeSubtractToBp(currentBase.getCbYield1Y(), beforeBase.getCbYield1Y()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread2Y(safeSubtract(currentBase.getCreditSpread2Y(), beforeBase.getCreditSpread2Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb2Y(safeSubtract(currentBase.getCreditSpreadTb2Y(), beforeBase.getCreditSpreadTb2Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield2Y(safeSubtractToBp(currentBase.getCbYield2Y(), beforeBase.getCbYield2Y()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread3Y(safeSubtract(currentBase.getCreditSpread3Y(), beforeBase.getCreditSpread3Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb3Y(safeSubtract(currentBase.getCreditSpreadTb3Y(), beforeBase.getCreditSpreadTb3Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield3Y(safeSubtractToBp(currentBase.getCbYield3Y(), beforeBase.getCbYield3Y()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread5Y(safeSubtract(currentBase.getCreditSpread5Y(), beforeBase.getCreditSpread5Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb5Y(safeSubtract(currentBase.getCreditSpreadTb5Y(), beforeBase.getCreditSpreadTb5Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield5Y(safeSubtractToBp(currentBase.getCbYield5Y(), beforeBase.getCbYield5Y()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread7Y(safeSubtract(currentBase.getCreditSpread7Y(), beforeBase.getCreditSpread7Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb7Y(safeSubtract(currentBase.getCreditSpreadTb7Y(), beforeBase.getCreditSpreadTb7Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield7Y(safeSubtractToBp(currentBase.getCbYield7Y(), beforeBase.getCbYield7Y()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread10Y(safeSubtract(currentBase.getCreditSpread10Y(), beforeBase.getCreditSpread10Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb10Y(safeSubtract(currentBase.getCreditSpreadTb10Y(), beforeBase.getCreditSpreadTb10Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield10Y(safeSubtractToBp(currentBase.getCbYield10Y(), beforeBase.getCbYield10Y()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread15Y(safeSubtract(currentBase.getCreditSpread15Y(), beforeBase.getCreditSpread15Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb15Y(safeSubtract(currentBase.getCreditSpreadTb15Y(), beforeBase.getCreditSpreadTb15Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield15Y(safeSubtractToBp(currentBase.getCbYield15Y(), beforeBase.getCbYield15Y()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread20Y(safeSubtract(currentBase.getCreditSpread20Y(), beforeBase.getCreditSpread20Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb20Y(safeSubtract(currentBase.getCreditSpreadTb20Y(), beforeBase.getCreditSpreadTb20Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield20Y(safeSubtractToBp(currentBase.getCbYield20Y(), beforeBase.getCbYield20Y()).orElse(null));

        pgLgBondYieldSpreadChangeDO.setCreditSpread30Y(safeSubtract(currentBase.getCreditSpread30Y(), beforeBase.getCreditSpread30Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCreditSpreadTb30Y(safeSubtract(currentBase.getCreditSpreadTb30Y(), beforeBase.getCreditSpreadTb30Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setCbYield30Y(safeSubtractToBp(currentBase.getCbYield30Y(), beforeBase.getCbYield30Y()).orElse(null));
        pgLgBondYieldSpreadChangeDO.setDeleted(Deleted.NO_DELETED.getValue());
        return Optional.of(pgLgBondYieldSpreadChangeDO);
    }

    private Optional<YieldLgDataBO> buildLgBondSpreadChangeByBO(YieldLgDataBO currentBase, YieldLgDataBO beforeBase,
                                                                SpreadCurveTypeEnum spreadCurveTypeEnum) {
        if (Objects.isNull(currentBase) || Objects.isNull(beforeBase)) {
            return Optional.empty();
        }
        YieldLgDataBO yieldLgDataBO = new YieldLgDataBO();
        yieldLgDataBO.setLgAreaName(currentBase.getLgAreaName());
        yieldLgDataBO.setComUniCode(currentBase.getComUniCode());

        yieldLgDataBO.setYield1M(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield1M(), beforeBase.getYield1M()).orElse(null));
        yieldLgDataBO.setYield3M(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield3M(), beforeBase.getYield3M()).orElse(null));
        yieldLgDataBO.setYield6M(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield6M(), beforeBase.getYield6M()).orElse(null));
        yieldLgDataBO.setYield9M(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield9M(), beforeBase.getYield9M()).orElse(null));
        yieldLgDataBO.setYield1Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield1Y(), beforeBase.getYield1Y()).orElse(null));
        yieldLgDataBO.setYield2Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield2Y(), beforeBase.getYield2Y()).orElse(null));
        yieldLgDataBO.setYield3Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield3Y(), beforeBase.getYield3Y()).orElse(null));
        yieldLgDataBO.setYield5Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield5Y(), beforeBase.getYield5Y()).orElse(null));
        yieldLgDataBO.setYield7Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield7Y(), beforeBase.getYield7Y()).orElse(null));
        yieldLgDataBO.setYield10Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield10Y(), beforeBase.getYield10Y()).orElse(null));
        yieldLgDataBO.setYield15Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield15Y(), beforeBase.getYield15Y()).orElse(null));
        yieldLgDataBO.setYield20Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield20Y(), beforeBase.getYield20Y()).orElse(null));
        yieldLgDataBO.setYield30Y(getSafeSubtractFunction(spreadCurveTypeEnum).apply(currentBase.getYield30Y(), beforeBase.getYield30Y()).orElse(null));
        return Optional.of(yieldLgDataBO);
    }

    private BiFunction<BigDecimal, BigDecimal, Optional<BigDecimal>> getSafeSubtractFunction(SpreadCurveTypeEnum spreadCurveTypeEnum) {
        if (SpreadCurveTypeEnum.CB_YIELD_SPREAD.equals(spreadCurveTypeEnum)) {
            return this::safeSubtractToBp;
        } else {
            return this::safeSubtract;
        }
    }

    private Optional<BigDecimal> safeSubtract(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return Optional.empty();
        }
        return Optional.of(value1.subtract(value2).setScale(TRACE_INTERVAL_CHANGE_SCALE, RoundingMode.HALF_UP));
    }

    private Optional<BigDecimal> safeSubtractToBp(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return Optional.empty();
        }
        return Optional.of(value1.subtract(value2).multiply(ONE_HUNDRED).setScale(TRACE_INTERVAL_CHANGE_SCALE, RoundingMode.HALF_UP));
    }

    private Date getChangeStartDate(Date issueDate, BondYieldIntervalChangeTypeEnum changeType) {
        LocalDate startDate = changeType.getIntervalStartDate(issueDate.toLocalDate());
        // 获取最近一天的工作日，包含自己
        Date workDate = holidayService.latestWorkDay(Date.valueOf(startDate), 0);
        Date minStartDate = changeType.getIntervalMinStartDate(startDate);
        return workDate.before(minStartDate) ? minStartDate : workDate;
    }

    private String getKey(PgLgBondYieldSpreadBaseDO baseDO) {
        Long comUniCode = baseDO.getComUniCode();
        String lgBondType = Optional.ofNullable(baseDO.getLgBondType()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        String prepaymentStatus = Optional.ofNullable(baseDO.getPrepaymentStatus()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        String fundUseType = Optional.ofNullable(baseDO.getFundUseType()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        Integer usingLgBondType = baseDO.getUsingLgBondType();
        Integer usingPrepaymentStatus = baseDO.getUsingPrepaymentStatus();
        Integer usingFundUseType = baseDO.getUsingFundUseType();
        return String.format(NAMESPACE_KEY_PLACEHOLDER, comUniCode, lgBondType, prepaymentStatus, fundUseType,
                usingLgBondType, usingPrepaymentStatus, usingFundUseType);
    }

    private String getKeyByLgSelectedRequestDTO(LgSpreadSelectedRequestDTO requestDTO, Long comUniCode) {
        String lgBondType = Optional.ofNullable(requestDTO.getLgBondType()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        String prepaymentStatus = Optional.ofNullable(requestDTO.getPrepaymentStatus()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        String fundUseType = Optional.ofNullable(requestDTO.getFundUseType()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        Integer usingLgBondType = Objects.isNull(requestDTO.getLgBondType()) ? SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP.getValue() :
                SpreadFieldGroupUseStatusEnum.USE_FIELD_GROUP.getValue();
        Integer usingPrepaymentStatus = Objects.isNull(requestDTO.getPrepaymentStatus()) ? SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP.getValue() :
                SpreadFieldGroupUseStatusEnum.USE_FIELD_GROUP.getValue();
        Integer usingFundUseType = Objects.isNull(requestDTO.getFundUseType()) ? SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP.getValue() :
                SpreadFieldGroupUseStatusEnum.USE_FIELD_GROUP.getValue();
        return String.format(NAMESPACE_KEY_PLACEHOLDER, comUniCode, lgBondType, prepaymentStatus, fundUseType,
                usingLgBondType, usingPrepaymentStatus, usingFundUseType);
    }


    @Override
    public int calcLgBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs, Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                                  Date spreadDate) {
        if (CollectionUtils.isEmpty(onshoreBondInfoDTOs)) {
            return 0;
        }
        Set<Long> bondUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getBondUniCode).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> comUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getComUniCode).filter(Objects::nonNull).collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        CompletableFuture<Map<Long, CbValuationShortInfoResponseDTO>> submitCbValuationShortInfo =
                of.submit(() -> bondPriceService.getCbValuationShortInfoMap(spreadDate, bondUniCodes));
        CompletableFuture<Map<Long, BondExternalCreditRatingDTO>> submitBondExternalCreditRatingDTO =
                of.submit(() -> bondRatingService.getBondExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, ComExternalCreditRatingDTO>> submitComExternalCreditRatingDTO =
                of.submit(() -> bondRatingService.getComExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        of.doWorks(submitCbValuationShortInfo, submitBondExternalCreditRatingDTO, submitComExternalCreditRatingDTO);
        // 获取中债估值信息
        Map<Long, CbValuationShortInfoResponseDTO> cbMap = submitCbValuationShortInfo.join();
        // 债券评级数据
        Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap = submitBondExternalCreditRatingDTO.join();
        // 主体评级数据
        Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap = submitComExternalCreditRatingDTO.join();
        return parseLgBondYieldSpreadDO(onshoreBondInfoDTOs, cbMap, bondExternalCreditRatingMap, comExternalCreditRatingMap, bondYieldCurveMap, spreadDate);
    }

    private int parseLgBondYieldSpreadDO(List<OnshoreBondInfoDTO> onshoreBondInfos, Map<Long, CbValuationShortInfoResponseDTO> cbMap,
                                         Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap, Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap,
                                         Map<Integer, BondYieldCurveDTO> bondYieldCurveMap, Date spreadDate) {
        List<LgBondYieldSpreadDO> lgBondYieldSpreadDOs = new ArrayList<>();
        for (OnshoreBondInfoDTO onshoreBondInfoDTO : onshoreBondInfos) {
            LgBondYieldSpreadDO lgBondYieldSpreadDO = BeanCopyUtils.copyProperties(onshoreBondInfoDTO, LgBondYieldSpreadDO.class);
            lgBondYieldSpreadDO.setSpreadDate(spreadDate);
            if (!DateExtensionUtils.isSameDay(spreadDate, YieldSpreadHelper.getYesterDay())) {
                lgBondYieldSpreadDO.setBondBalance(null);
            }
            // 利差剩余期限标签
            lgBondYieldSpreadDO.setSpreadRemainingTenorTag(YieldSpreadHelper.getSpreadRemainingTenorTag(lgBondYieldSpreadDO.getRemainingTenorDay()));
            lgBondYieldSpreadDO = fillRatingColumn(lgBondYieldSpreadDO, bondExternalCreditRatingMap, comExternalCreditRatingMap);
            // 估值收益率
            lgBondYieldSpreadDO = fillCbColumn(lgBondYieldSpreadDO, cbMap);
            // 插值收益率
            lgBondYieldSpreadDO = fillLerpYieldColumn(lgBondYieldSpreadDO, bondYieldCurveMap);
            // 利差
            lgBondYieldSpreadDO = fillSpreadColumn(lgBondYieldSpreadDO, bondYieldCurveMap);
            lgBondYieldSpreadDO = fillMappingColumn(lgBondYieldSpreadDO);
            if (Objects.nonNull(lgBondYieldSpreadDO.getBondCreditSpreadTb()) ||
                    Objects.nonNull(lgBondYieldSpreadDO.getBondCreditSpread()) || Objects.nonNull(lgBondYieldSpreadDO.getBondExcessSpread())) {
                lgBondYieldSpreadDO.setId(redisService.generatePk(YIELD_SPREAD_LG_BOND_YIELD_SPREAD_FLOW_ID, lgBondYieldSpreadDO.getSpreadDate()));
                lgBondYieldSpreadDO.setDeleted(Deleted.NO_DELETED.getValue());
                lgBondYieldSpreadDOs.add(lgBondYieldSpreadDO);
            }
        }
        lgBondYieldSpreadDAO.saveLgBondYieldSpreadDOList(spreadDate, spreadDate, lgBondYieldSpreadDOs);
        return savePgLgBondYieldSpreadDOLists(spreadDate, lgBondYieldSpreadDOs);
    }

    private int savePgLgBondYieldSpreadDOLists(Date spreadDate, List<LgBondYieldSpreadDO> lgBondYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(lgBondYieldSpreadDOs)) {
            return 0;
        }
        List<PgLgBondYieldSpreadDO> pgLgBondYieldSpreadDOs = BeanCopyUtils.copyList(lgBondYieldSpreadDOs, PgLgBondYieldSpreadDO.class);
        return pgLgBondYieldSpreadDAO.savePgLgBondYieldSpreadDOList(spreadDate, pgLgBondYieldSpreadDOs);
    }

    /**
     * 填充评级相关信息
     *
     * @param lgBondYieldSpreadDO         产业债利差
     * @param bondExternalCreditRatingMap 主体外部评级
     * @param comExternalCreditRatingMap  主体YY评级
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.LgBondYieldSpreadDO
     * <AUTHOR>
     */
    private LgBondYieldSpreadDO fillRatingColumn(LgBondYieldSpreadDO lgBondYieldSpreadDO, Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                                 Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap) {
        LgBondYieldSpreadDO result = BeanCopyUtils.copyProperties(lgBondYieldSpreadDO, LgBondYieldSpreadDO.class);
        if (ObjectUtils.isNotEmpty(bondExternalCreditRatingMap)) {
            BondExternalCreditRatingDTO bondExternalCreditRatingDTO = bondExternalCreditRatingMap.get(lgBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondExternalCreditRatingDTO)) {
                result.setBondExtRatingMapping(bondExternalCreditRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(comExternalCreditRatingMap)) {
            ComExternalCreditRatingDTO comExternalCreditRatingDTO = comExternalCreditRatingMap.get(lgBondYieldSpreadDO.getComUniCode());
            if (Objects.nonNull(comExternalCreditRatingDTO)) {
                result.setComExtRatingMapping(comExternalCreditRatingDTO.getRatingMapping());
            }
        }
        return result;
    }

    /**
     * 填充中债估值相关信息
     *
     * @param lgBondYieldSpreadDO 地方债利差
     * @param cbMap               中债估值
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.LgBondYieldSpreadDO
     * <AUTHOR>
     */
    private LgBondYieldSpreadDO fillCbColumn(LgBondYieldSpreadDO lgBondYieldSpreadDO, Map<Long, CbValuationShortInfoResponseDTO> cbMap) {
        LgBondYieldSpreadDO result = BeanCopyUtils.copyProperties(lgBondYieldSpreadDO, LgBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(cbMap)) {
            return result;
        }
        CbValuationShortInfoResponseDTO cb = cbMap.get(lgBondYieldSpreadDO.getBondUniCode());
        if (Objects.nonNull(cb)) {
            result.setCbYield(cb.getYield());
        }
        return result;
    }

    /**
     * 填充插值收益率
     *
     * @param lgBondYieldSpreadDO 地方债利差
     * @param bondYieldCurveMap   收益率曲线信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.LgBondYieldSpreadDO
     * <AUTHOR>
     */
    private LgBondYieldSpreadDO fillLerpYieldColumn(LgBondYieldSpreadDO lgBondYieldSpreadDO, Map<Integer, BondYieldCurveDTO> bondYieldCurveMap) {
        LgBondYieldSpreadDO result = BeanCopyUtils.copyProperties(lgBondYieldSpreadDO, LgBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(bondYieldCurveMap)) {
            return result;
        }
        //国开插值收益率;单位(%)
        BondYieldCurveDTO bondYieldCurveDTO = bondYieldCurveMap.get(YieldSpreadHelper.CDB_YIELD_CURVE);
        if (Objects.nonNull(bondYieldCurveDTO)) {
            BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(bondYieldCurveDTO, BondYieldCurveBO.class);
            result.setCdbLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
        }
        // 国债插值收益率;单位(%)
        BondYieldCurveDTO bondYieldCurveTb = bondYieldCurveMap.get(CurveCode.CHINA_BOND.getValue());
        if (Objects.nonNull(bondYieldCurveTb)) {
            BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(bondYieldCurveTb, BondYieldCurveBO.class);
            result.setTbLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
        }
        return result;
    }

    /**
     * 填充利差数据
     *
     * @param lgBondYieldSpreadDO 地方债利差
     * @param bondYieldCurveMap   收益率曲线信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.LgBondYieldSpreadDO
     */
    private LgBondYieldSpreadDO fillSpreadColumn(LgBondYieldSpreadDO lgBondYieldSpreadDO, Map<Integer, BondYieldCurveDTO> bondYieldCurveMap) {
        LgBondYieldSpreadDO result = BeanCopyUtils.copyProperties(lgBondYieldSpreadDO, LgBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(bondYieldCurveMap)) {
            return result;
        }
        if (Objects.nonNull(result.getCbYield())) {
            // 信用利差(减国开)
            if (Objects.nonNull(result.getCdbLerpYield()) && result.getCdbLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondCreditSpread(result.getCbYield().subtract(result.getCdbLerpYield()).multiply(YieldSpreadHelper.BP_WEIGHT)
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            // 信用利差(减国债)
            if (Objects.nonNull(result.getTbLerpYield()) && result.getTbLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondCreditSpreadTb(result.getCbYield().subtract(result.getTbLerpYield()).multiply(YieldSpreadHelper.BP_WEIGHT)
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            BondYieldCurveDTO bondYieldCurveDTO = bondYieldCurveMap.get(CurveCode.CHINA_BOND_CITY.getValue());
            if (Objects.nonNull(bondYieldCurveDTO)) {
                BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(bondYieldCurveDTO, BondYieldCurveBO.class);
                BigDecimal lerpYield = YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO);
                if (Objects.nonNull(lerpYield) && lerpYield.compareTo(BigDecimal.ZERO) > 0) {
                    result.setBondExcessSpread(result.getCbYield().subtract(lerpYield).multiply(YieldSpreadHelper.BP_WEIGHT)
                            .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                    result.setExcessSpreadStatus(0);
                } else {
                    result.setExcessSpreadStatus(1);
                }
            }
        }
        return result;
    }

    private LgBondYieldSpreadDO fillMappingColumn(LgBondYieldSpreadDO lgBondYieldSpreadDO) {
        LgBondYieldSpreadDO result = BeanCopyUtils.copyProperties(lgBondYieldSpreadDO, LgBondYieldSpreadDO.class);
        String areaName = YieldSpreadHelper.getLgComAreaNameMap().get(lgBondYieldSpreadDO.getComUniCode());
        result.setLgAreaName(areaName);
        // 入参剩余期限, 返回对应挡位
        Integer remainingTenorDay = lgBondYieldSpreadDO.getRemainingTenorDay();
        if (Objects.nonNull(remainingTenorDay)) {
            PeriodEnum periodEnum = LgBondYieldSpreadTenorEnum.getLgBondYieldSpreadTenor(remainingTenorDay).getPeriodEnum();
            result.setLgRemainingGrade(periodEnum.getValue());
            result.setLgRemainingGradeName(periodEnum.getText());
        }
        return result;
    }

    /**
     * 缓存利地方债区域利差折线图
     */
    @Override
    public void cacheLgLineChart() {
        logger.info("【LgYieldSpreadService#cacheLgLineChart】 ===> start....");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final LocalDate localEndDate = LocalDate.now().minusDays(YieldSpreadConst.ONE_DAY);
        final Date startDate = Date.valueOf(localEndDate.minusYears(YieldSpreadConst.FIVE_YEARS));
        final Date endDate = Date.valueOf(localEndDate);
        for (LgSpreadSelectedRequestDTO lgSelectedDTO : LG_SELECTED_ALL_ITEM) {
            List<PgLgBondYieldSpreadBaseDO> yieldLgDataDOList = pgLgBondYieldSpreadBaseDAO.listSpreadLgLineData(startDate, endDate, lgSelectedDTO, null);
            //收益率列表
            List<YieldLgDataBO> cbYieldDataBOList = yieldLgDataDOList.stream()
                    .map(baseDO -> baseDO.convertToBO(SpreadCurveTypeEnum.CB_YIELD_SPREAD).orElse(null))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            LinkedHashMap<Date, List<YieldLgDataBO>> cbYieldDataMap = cbYieldDataBOList.stream()
                    .collect(Collectors.groupingBy(YieldLgDataBO::getSpreadDate, LinkedHashMap::new, Collectors.toList()));
            cacheLgLineData(cbYieldDataMap, SpreadCurveTypeEnum.CB_YIELD_SPREAD, lgSelectedDTO);
            //信用利差(减国开)列表
            List<YieldLgDataBO> creditDataBOList = yieldLgDataDOList.stream()
                    .map(baseDO -> baseDO.convertToBO(SpreadCurveTypeEnum.CREDIT_SPREAD).orElse(null))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            LinkedHashMap<Date, List<YieldLgDataBO>> creditDataMap = creditDataBOList.stream()
                    .collect(Collectors.groupingBy(YieldLgDataBO::getSpreadDate, LinkedHashMap::new, Collectors.toList()));
            cacheLgLineData(creditDataMap, SpreadCurveTypeEnum.CREDIT_SPREAD, lgSelectedDTO);
            //信用利差(减国债)列表
            List<YieldLgDataBO> creditDataTbBOList = yieldLgDataDOList.stream()
                    .map(baseDO -> baseDO.convertToBO(SpreadCurveTypeEnum.CREDIT_SPREAD_TB).orElse(null))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            LinkedHashMap<Date, List<YieldLgDataBO>> creditTbDataMap = creditDataTbBOList.stream()
                    .collect(Collectors.groupingBy(YieldLgDataBO::getSpreadDate, LinkedHashMap::new, Collectors.toList()));
            cacheLgLineData(creditTbDataMap, SpreadCurveTypeEnum.CREDIT_SPREAD_TB, lgSelectedDTO);
        }
        stopWatch.stop();
        logger.info("【LgYieldSpreadService#cacheLgLineChart】 ===> end....spend time: {} ms", stopWatch.getTotalTimeMillis());
    }

    private void cacheLgLineData(LinkedHashMap<Date, List<YieldLgDataBO>> cbYieldDataMap,
                                 SpreadCurveTypeEnum spreadCurveTypeEnum,
                                 LgSpreadSelectedRequestDTO lgSelectedDTO) {
        final Map<String, String> cacheMap = Maps.newHashMap();

        List<Date> issueDateList = new ArrayList<>(cbYieldDataMap.size());
        Map<SpreadComAreaEnum, Map<PeriodEnum, List<BigDecimal>>> spreadComAreaMap = Maps.newHashMap();
        for (SpreadComAreaEnum spreadComAreaEnum : SpreadComAreaEnum.values()) {
            spreadComAreaMap.put(spreadComAreaEnum, periodLgFunctionMap.keySet().stream().collect(Collectors.toMap(key -> key, value -> new ArrayList<>())));
        }
        fillLgLineCacheData(cbYieldDataMap, issueDateList, spreadComAreaMap, spreadCurveTypeEnum);
        Date[] issueDateArray = issueDateList.toArray(new Date[0]);
        for (Map.Entry<SpreadComAreaEnum, Map<PeriodEnum, List<BigDecimal>>> comAreaEnumEntry : spreadComAreaMap.entrySet()) {
            Map<PeriodEnum, List<BigDecimal>> periodMap = comAreaEnumEntry.getValue();
            for (Map.Entry<PeriodEnum, List<BigDecimal>> periodDataEntry : periodMap.entrySet()) {
                String redisKey = getLgLineDataRedisKey(comAreaEnumEntry.getKey(), periodDataEntry.getKey(), spreadCurveTypeEnum, lgSelectedDTO).orElse(null);
                YieldLgLineCacheDataBO yieldLgLineCacheDataBO = new YieldLgLineCacheDataBO();
                yieldLgLineCacheDataBO.setIssueDates(issueDateArray);
                yieldLgLineCacheDataBO.setYields(periodDataEntry.getValue().toArray(new BigDecimal[0]));
                cacheMap.put(redisKey, ZipUtils.zipBase64(JSON.toJSONString(yieldLgLineCacheDataBO)));
            }
        }

        redisService.multiSet(cacheMap);
    }

    private void fillLgLineCacheData(LinkedHashMap<Date, List<YieldLgDataBO>> cbYieldDataMap, List<Date> issueDateList,
                                     Map<SpreadComAreaEnum, Map<PeriodEnum, List<BigDecimal>>> spreadComAreaMap,
                                     SpreadCurveTypeEnum spreadCurveTypeEnum) {
        Integer scale = YieldSpreadConst.TWO_DECIMAL_PLACE;
        if (SpreadCurveTypeEnum.CB_YIELD_SPREAD.equals(spreadCurveTypeEnum)) {
            scale = YieldSpreadConst.FOUR_DECIMAL_PLACE;
        }
        for (Map.Entry<Date, List<YieldLgDataBO>> entry : cbYieldDataMap.entrySet()) {
            issueDateList.add(entry.getKey());
            for (SpreadComAreaEnum spreadComAreaEnum : SpreadComAreaEnum.values()) {
                Map<PeriodEnum, List<BigDecimal>> periodEnumListMap = spreadComAreaMap.get(spreadComAreaEnum);
                for (Map.Entry<PeriodEnum, Function<YieldLgDataBO, BigDecimal>> functionEntry : periodLgFunctionMap.entrySet()) {
                    List<BigDecimal> dataList = periodEnumListMap.get(functionEntry.getKey());
                    Optional<YieldLgDataBO> pgLgDataBOopt = entry.getValue().stream()
                            .filter(yieldLgDataBO -> Long.valueOf(spreadComAreaEnum.getValue()).equals(yieldLgDataBO.getComUniCode())).findAny();
                    if (pgLgDataBOopt.isPresent()) {
                        dataList.add(CommonUtils.safeSetScale(functionEntry.getValue().apply(pgLgDataBOopt.get()), scale).orElse(null));
                    } else {
                        dataList.add(null);
                    }
                }

            }
        }
    }

    private Optional<String> getLgLineDataRedisKey(SpreadComAreaEnum spreadComAreaEnum, PeriodEnum periodEnum,
                                                   SpreadCurveTypeEnum spreadCurveTypeEnum, LgSpreadSelectedRequestDTO lgSelectedDTO) {
        if (!ObjectUtils.allNotNull(spreadComAreaEnum, periodEnum, spreadCurveTypeEnum, lgSelectedDTO)) {
            return Optional.empty();
        }
        return Optional.of(String.format(LG_LINE_CHART_KEY, periodEnum.getValue(), spreadCurveTypeEnum.getValue(),
                this.getKeyByLgSelectedRequestDTO(lgSelectedDTO, (long) spreadComAreaEnum.getValue())));
    }

    /**
     * 获取地方债区域利差基础信息
     *
     * @param userId     用户id
     * @param requestDTO 请求参数
     * @return 数据值
     */
    @Override
    public YieldLgResponseDTO getYieldSpreadLgBase(Long userId, LgSpreadBaseRequestDTO requestDTO) {
        List<Long> areaConfigList = getLgUserConfig(userId, (long) UserConfigEnum.CONFIG_30103.getValue(), Long.class);
        PgBondSpreadLgDTO pgBondSpreadLgDTO = listYieldLgDataBOs(areaConfigList, requestDTO, BondYieldTableTypeEnum.ABS, null, null, null);
        List<YieldLgDataBO> yieldLgDataBOList = pgBondSpreadLgDTO.getYieldLgDataBOList();
        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, requestDTO.getSpreadDataType());

        YieldLgResponseDTO yieldLgResponseDTO = convertToLgResponse(userId, yieldLgDataBOList, null, null, spreadCurveTypeEnum, BondYieldTableTypeEnum.ABS, areaConfigList);
        sort(requestDTO.getSort(), yieldLgResponseDTO);
        return yieldLgResponseDTO;
    }

    private PgBondSpreadLgDTO listYieldLgDataBOs(List<Long> areaConfigList, LgSpreadBaseRequestDTO requestDTO,
                                                 BondYieldTableTypeEnum bondYieldTableTypeEnum,
                                                 Integer tableTypeParam, Date startDate, Date endDate) {
        List<YieldLgDataBO> yieldLgDataBOList;
        if (Objects.equals(bondYieldTableTypeEnum, BondYieldTableTypeEnum.ABS)) {
            yieldLgDataBOList = pgLgBondYieldSpreadBaseDAO.listYieldSpreadLgBase(areaConfigList, requestDTO);
        } else if (Objects.equals(bondYieldTableTypeEnum, BondYieldTableTypeEnum.HIST_QUANTILE)) {
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                yieldLgDataBOList = pgLgBondYieldSpreadBaseDAO.listStatisticsLgQuantileBO(requestDTO, areaConfigList, startDate, endDate);
            } else {
                SpreadQuantileTypeEnum quantileTypeEnum = EnumUtils.ofNullable(SpreadQuantileTypeEnum.class, tableTypeParam).orElse(SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE);
                yieldLgDataBOList = pgLgBondYieldSpreadQuantileDAO.listYieldSpreadLgQuantile(areaConfigList, requestDTO, quantileTypeEnum);
                startDate = yieldSpreadCommonService.getQuantileStartDate(requestDTO.getSpreadDate(), quantileTypeEnum);
                endDate = requestDTO.getSpreadDate();
            }
        } else {
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                yieldLgDataBOList = listLgBondYieldSpreadOfChange(areaConfigList, requestDTO, startDate, endDate);
            } else {
                BondYieldIntervalChangeTypeEnum changeTypeEnum = EnumUtils.ofNullable(BondYieldIntervalChangeTypeEnum.class, tableTypeParam)
                        .orElse(BondYieldIntervalChangeTypeEnum.ONE_DAY_CHANGE);
                yieldLgDataBOList = pgLgBondYieldSpreadChangeDAO.listYieldSpreadLgChange(areaConfigList, requestDTO, changeTypeEnum);
                startDate = getChangeStartDate(requestDTO.getSpreadDate(), changeTypeEnum);
                endDate = requestDTO.getSpreadDate();
            }
        }
        PgBondSpreadLgDTO pgBondSpreadLgDTO = new PgBondSpreadLgDTO();
        pgBondSpreadLgDTO.setYieldLgDataBOList(yieldLgDataBOList);
        pgBondSpreadLgDTO.setStartDate(startDate);
        pgBondSpreadLgDTO.setEndDate(endDate);
        return pgBondSpreadLgDTO;

    }

    private YieldLgResponseDTO convertToLgResponse(Long userId, List<YieldLgDataBO> yieldLgDataBOList,
                                                   Date startDate, Date endDate,
                                                   SpreadCurveTypeEnum spreadCurveTypeEnum,
                                                   BondYieldTableTypeEnum bondYieldTableTypeEnum,
                                                   List<Long> areaConfigList) {
        YieldLgResponseDTO resultDTO = new YieldLgResponseDTO();
        Map<Long, YieldLgDataResponseDTO> dataResponseMap = yieldLgDataBOList.stream()
                .map(yieldLgDataBO -> BondYieldSpreadBuilder.builder(yieldLgDataBO, spreadCurveTypeEnum, bondYieldTableTypeEnum))
                .collect(Collectors.toMap(YieldLgDataResponseDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));

        yieldLgDataBOList.sort(Comparator.comparing(yieldLgDataBO -> areaConfigList.indexOf(yieldLgDataBO.getComUniCode())));

        List<YieldLgDataResponseDTO> dataResponseDTOList = areaConfigList.stream()
                .map(comUniCode -> {
                    YieldLgDataResponseDTO defaultResponseDTO = new YieldLgDataResponseDTO();
                    defaultResponseDTO.setComUniCode(comUniCode);
                    EnumUtils.getEnumByValue(comUniCode.intValue(), SpreadComAreaEnum.class)
                            .ifPresent(spreadComAreaEnum -> defaultResponseDTO.setLgAreaName(spreadComAreaEnum.getText()));
                    return dataResponseMap.getOrDefault(comUniCode, defaultResponseDTO);
                }).collect(Collectors.toList());


        List<Integer> periodConfigList = getLgUserConfig(userId, (long) UserConfigEnum.CONFIG_30102.getValue(), Integer.class);
        SpreadStatisticsBO spreadStatisticsBO = YieldSpreadHelper.statisticsSpreadByPeriod(yieldLgDataBOList, periodConfigList);

        //处理最大最小和中位数 为了页面颜色展示
        resultDTO.setMinYield(spreadStatisticsBO.getMinYield());
        resultDTO.setMaxYield(spreadStatisticsBO.getMaxYield());
        resultDTO.setMedianYield(spreadStatisticsBO.getMedianYield());
        resultDTO.setYieldLgDataList(dataResponseDTOList);
        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            resultDTO.setStatisticalDateStart(startDate.toLocalDate().format(DateTimeFormatter.ISO_DATE));
            resultDTO.setStatisticalDateEnd(endDate.toLocalDate().format(DateTimeFormatter.ISO_DATE));
            resultDTO.setStatisticalDateRange(String.format(BondYieldSpreadBuilder.STATISTICAL_DATE_RANGE, startDate.toLocalDate().format(DateTimeFormatter.ISO_DATE),
                    endDate.toLocalDate().format(DateTimeFormatter.ISO_DATE)));
        }
        return resultDTO;
    }

    private void sort(SortDTO sort, YieldLgResponseDTO yieldLgResponseDTO) {
        if (Objects.isNull(sort)) {
            return;
        }
        String propertyName = sort.getPropertyName();
        SortDirection sortDirection = sort.getSortDirection();
        List<YieldLgDataResponseDTO> yieldLgDataList = yieldLgResponseDTO.getYieldLgDataList();
        if (StringUtils.isBlank(propertyName) || Objects.isNull(sortDirection) || CollectionUtils.isEmpty(yieldLgDataList)) {
            return;
        }
        if (Objects.equals(sortDirection, SortDirection.ASC)) {
            yieldLgDataList.sort(Comparator.comparing(x -> getNeedSortVal(propertyName, x), Comparator.nullsLast(BigDecimal::compareTo)));
        }else {
            yieldLgDataList.sort(Comparator.comparing(x -> getNeedSortVal(propertyName, (YieldLgDataResponseDTO) x), Comparator.nullsFirst(BigDecimal::compareTo)).reversed());
        }
        yieldLgResponseDTO.setYieldLgDataList(yieldLgDataList);
    }

    @SuppressWarnings("java:S1612")
    private BigDecimal getNeedSortVal(String propertyName, YieldLgDataResponseDTO yieldLgDataResponseDTO) {
        try {
            Optional<Object> sortBValOptional = YieldSpreadHelper.getValueByGetMethod(propertyName, yieldLgDataResponseDTO);
            return sortBValOptional
                    .map(x -> (String) x)
                    .filter(StringUtils::isNotBlank)
                    .map(BigDecimal::new)
                    .orElse(null);
        } catch (NoSuchMethodException e) {
            logger.error("getNeedSortVal,message: {}", e.getMessage());
        }
        return null;
    }


    /**
     * 根据用户配置获取地方债利差配置列表
     *
     * @param userId   用户id
     * @param configId 配置id
     * @param clazz    类型
     * @param <T>      泛型
     * @return 返回值
     */
    private <T> List<T> getLgUserConfig(Long userId, Long configId, Class<T> clazz) {
        if (!ObjectUtils.allNotNull(userId, configId, clazz)) {
            return Lists.newArrayList();
        }
        Optional<UserSpreadConfigBO<T>> userConfigOpt = userSpreadConfigDAO.getUserSpreadConfigByConfigId(userId, configId, clazz);
        if (!userConfigOpt.isPresent()) {
            throw new BusinessException(String.format("用户userId:%s,配置id:%s找不到配置信息", userId, configId));
        }
        UserSpreadConfigBO<T> longUserSpreadConfigBO = userConfigOpt.get();
        return longUserSpreadConfigBO.getConfigDetails();
    }

    /**
     * 获取地方债区域利差历史分位信息
     *
     * @param userId     用户id
     * @param requestDTO 请求参数
     * @return 数据值
     */
    @Override
    public YieldLgResponseDTO getYieldSpreadLgQuantile(Long userId, LgSpreadQuantileRequestDTO requestDTO) {
        List<Long> areaConfigList = getLgUserConfig(userId, (long) UserConfigEnum.CONFIG_30103.getValue(), Long.class);
        PgBondSpreadLgDTO pgBondSpreadLgDTO = listYieldLgDataBOs(areaConfigList, requestDTO, BondYieldTableTypeEnum.HIST_QUANTILE,
                requestDTO.getQuantileType(), requestDTO.getStartDate(), requestDTO.getEndDate());

        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, requestDTO.getSpreadDataType());
        YieldLgResponseDTO yieldLgResponseDTO = convertToLgResponse(userId, pgBondSpreadLgDTO.getYieldLgDataBOList(), pgBondSpreadLgDTO.getStartDate(),
                pgBondSpreadLgDTO.getEndDate(), spreadCurveTypeEnum, BondYieldTableTypeEnum.HIST_QUANTILE, areaConfigList);
        sort(requestDTO.getSort(), yieldLgResponseDTO);
        return yieldLgResponseDTO;
    }

    /**
     * 获取地方债区域利差区间变动信息
     *
     * @param userId     用户id
     * @param requestDTO 请求参数
     * @return 数据值
     */
    @Override
    public YieldLgResponseDTO listYieldSpreadLgChange(Long userId, LgSpreadChangeRequestDTO requestDTO) {
        List<Long> areaConfigList = getLgUserConfig(userId, (long) UserConfigEnum.CONFIG_30103.getValue(), Long.class);
        PgBondSpreadLgDTO pgBondSpreadLgDTO = listYieldLgDataBOs(areaConfigList, requestDTO, BondYieldTableTypeEnum.INTERVAL_CHANGE,
                requestDTO.getChangeType(), requestDTO.getStartDate(), requestDTO.getEndDate());

        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, requestDTO.getSpreadDataType());
        YieldLgResponseDTO yieldLgResponseDTO = convertToLgResponse(userId, pgBondSpreadLgDTO.getYieldLgDataBOList(), pgBondSpreadLgDTO.getStartDate(),
                pgBondSpreadLgDTO.getEndDate(), spreadCurveTypeEnum, BondYieldTableTypeEnum.INTERVAL_CHANGE, areaConfigList);
        sort(requestDTO.getSort(), yieldLgResponseDTO);
        return yieldLgResponseDTO;
    }


    private List<YieldLgDataBO> listLgBondYieldSpreadOfChange(List<Long> areaConfigList, LgSpreadBaseRequestDTO requestDTO, Date startDate, Date endDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Lists.newArrayList();
        }
        if (startDate.toLocalDate().isAfter(startDate.toLocalDate())) {
            throw new TipsException("开始时间不能大于结束时间");
        }
        List<YieldLgDataBO> changeList = Lists.newArrayList();
        // 查询 end的LgBondYieldSpreadBase
        requestDTO.setSpreadDate(endDate);
        List<YieldLgDataBO> endYieldLgDataBOList = pgLgBondYieldSpreadBaseDAO.listYieldSpreadLgBase(areaConfigList, requestDTO);
        Map<String, YieldLgDataBO> currentBaseMap =
                endYieldLgDataBOList.stream().collect(Collectors.toMap(x -> getKeyByLgSelectedRequestDTO(requestDTO, x.getComUniCode()), x -> x, (k1, k2) -> k1));

        // 查询 before的LgBondYieldSpreadBase
        requestDTO.setSpreadDate(startDate);
        List<YieldLgDataBO> startYieldLgDataBOList = pgLgBondYieldSpreadBaseDAO.listYieldSpreadLgBase(areaConfigList, requestDTO);
        Map<String, YieldLgDataBO> startBaseMap =
                startYieldLgDataBOList.stream().collect(Collectors.toMap(x -> getKeyByLgSelectedRequestDTO(requestDTO, x.getComUniCode()), x -> x, (k1, k2) -> k1));
        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, requestDTO.getSpreadDataType());
        for (Map.Entry<String, YieldLgDataBO> curentEntry : currentBaseMap.entrySet()) {
            YieldLgDataBO beforeBase = startBaseMap.get(curentEntry.getKey());
            Optional<YieldLgDataBO> change = buildLgBondSpreadChangeByBO(curentEntry.getValue(), beforeBase, spreadCurveTypeEnum);
            change.ifPresent(changeList::add);
        }

        return changeList;
    }

    /**
     * 地方债区域利差导出
     *
     * @param httpServletResponse response对象
     * @param userId              用户id
     * @param requestDTO          请求对象
     */
    @Override
    public void exportYieldSpreadLg(HttpServletResponse httpServletResponse, Long userId, BondYieldLgExportReqDTO requestDTO) throws IOException {
        ServletOutputStream outputStream = httpServletResponse.getOutputStream();
        ExcelWriter excelWriter = null;
        try {
            Date spreadDate = requestDTO.getSpreadDate();
            // 通用利差曲线类型(1,2,3,4), 地方债利差曲线类型(1,3,4), excel导出需要获取 地方债利差曲线类型的专有描述
            SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, requestDTO.getSpreadDataType());
            LgSpreadCurveTypeEnum lgSpreadCurveTypeEnum = spreadCurveTypeEnum.getLgSpreadCurveTypeEnum();
            List<Integer> periodCodes = userSpreadConfigDAO.getUserSpreadConfigByConfigId(userId, (long) UserConfigEnum.CONFIG_30102.getValue(), Integer.class)
                    .map(UserSpreadConfigBO::getConfigDetails).orElseGet(Lists::newArrayList);
            periodCodes.sort(Integer::compareTo);
            List<PeriodEnum> periodEnums = PeriodEnum.getPeriodEnumByValues(periodCodes);

            List<Long> areaConfigList = getLgUserConfig(userId, (long) UserConfigEnum.CONFIG_30103.getValue(), Long.class);
            Map<BondYieldTableTypeEnum, List<YieldLgDataBO>> dataMap = Maps.newHashMap();
            //获取base数据
            PgBondSpreadLgDTO pgBondSpreadLgBaseDTO = this.listYieldLgDataBOs(areaConfigList, requestDTO, BondYieldTableTypeEnum.ABS, null, null, null);
            dataMap.put(BondYieldTableTypeEnum.ABS, pgBondSpreadLgBaseDTO.getYieldLgDataBOList());
            //历史分位参数
            Date quantileStartDate = requestDTO.getQuantileStartDate();
            Date quantileEndDate = requestDTO.getQuantileEndDate();
            Integer quantileType = requestDTO.getQuantileType();
            PgBondSpreadLgDTO pgBondSpreadLgQuantileDTO = this.listYieldLgDataBOs(areaConfigList, requestDTO, BondYieldTableTypeEnum.HIST_QUANTILE,
                    quantileType, quantileStartDate, quantileEndDate);
            quantileStartDate = pgBondSpreadLgQuantileDTO.getStartDate();
            quantileEndDate = pgBondSpreadLgQuantileDTO.getEndDate();
            dataMap.put(BondYieldTableTypeEnum.HIST_QUANTILE, pgBondSpreadLgQuantileDTO.getYieldLgDataBOList());
            //区间变动参数
            //获取区间变动数据
            Date changeStartDate = requestDTO.getChangeStartDate();
            Date changeEndDate = requestDTO.getChangeEndDate();
            Integer changeType = requestDTO.getChangeType();
            PgBondSpreadLgDTO pgBondSpreadLgChangeDTO = this.listYieldLgDataBOs(areaConfigList, requestDTO, BondYieldTableTypeEnum.INTERVAL_CHANGE,
                    changeType, changeStartDate, changeEndDate);
            changeStartDate = pgBondSpreadLgChangeDTO.getStartDate();
            changeEndDate = pgBondSpreadLgChangeDTO.getEndDate();
            dataMap.put(BondYieldTableTypeEnum.INTERVAL_CHANGE, pgBondSpreadLgChangeDTO.getYieldLgDataBOList());


            InputStream inputStream = new ClassPathResource("/static/template/excel/地方债区域利差模板.xlsx").getInputStream();
            excelWriter = EasyExcelFactory.write(outputStream).withTemplate(inputStream).excelType(ExcelTypeEnum.XLSX).build();
            ExcelUtils.setExportResponse(httpServletResponse,
                    String.format(LG_FILE_NAME, spreadDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE)));
            WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
            // 获取填充excel标题数据
            Map<String, Object> excelShow = Maps.newHashMap();
            ExcelWriter finalExcelWriter = excelWriter;
            dataMap.forEach((k, v) -> dealLgExcelWriter(finalExcelWriter, writeSheet, k, v, periodEnums, areaConfigList));

            excelShow.put("threeDateRange", String.format(BondYieldSpreadBuilder.STATISTICAL_DATE_RANGE,
                    quantileStartDate.toLocalDate().format(DateTimeFormatter.ISO_DATE), quantileEndDate.toLocalDate().format(DateTimeFormatter.ISO_DATE)));
            excelShow.put("changeDateRange", String.format(BondYieldSpreadBuilder.STATISTICAL_DATE_RANGE,
                    changeStartDate.toLocalDate().format(DateTimeFormatter.ISO_DATE), changeEndDate.toLocalDate().format(DateTimeFormatter.ISO_DATE)));
            excelShow.put("selectedStr", getExcelSelectedShow(requestDTO));
            String lgDataName = lgSpreadCurveTypeEnum.getExcelFieldName();
            excelShow.put("lgDataName", lgDataName);
            FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();
            excelWriter.fill(excelShow, fillConfig, writeSheet);
        } catch (RuntimeException e) {
            logger.error(e.getMessage());
            throw e;
        } finally {
            Optional.ofNullable(excelWriter).ifPresent(ExcelWriter::finish);
            outputStream.flush();
        }

    }

    private String getExcelSelectedShow(LgSpreadSelectedRequestDTO requestDTO) {
        StringBuilder selectedStrBuilder = new StringBuilder();
        if (Objects.nonNull(requestDTO)) {
            EnumUtils.getEnumByValue(requestDTO.getLgBondType(), LgBondTypeEnum.class)
                    .ifPresent(lgBondTypeEnum -> selectedStrBuilder.append(", ").append(lgBondTypeEnum.getText()));
            EnumUtils.getEnumByValue(requestDTO.getPrepaymentStatus(), YieldPrepaymentStatusEnum.class)
                    .ifPresent(yieldPrepaymentStatusEnum -> selectedStrBuilder.append(", ").append(yieldPrepaymentStatusEnum.getText()));
            EnumUtils.getEnumByValue(requestDTO.getFundUseType(), FundUseTypeEnum.class)
                    .ifPresent(fundUseTypeEnum -> selectedStrBuilder.append(", ").append(fundUseTypeEnum.getText()));
        }
        return selectedStrBuilder.toString();
    }

    private void dealLgExcelWriter(ExcelWriter excelWriter, WriteSheet writeSheet,
                                   BondYieldTableTypeEnum bondYieldTableTypeEnum, List<YieldLgDataBO> yieldLgDataBOs,
                                   List<PeriodEnum> periodEnums, List<Long> areaConfigList) {
        if (CollectionUtils.isEmpty(yieldLgDataBOs)) {
            return;
        }
        List<PeriodExcelFillDataBO> periodExcelFillDataBOList = listPeriodLgExcelFillData(bondYieldTableTypeEnum, periodEnums);
        Map<Long, YieldLgDataBO> dataResponseMap = yieldLgDataBOs.stream()
                .collect(Collectors.toMap(YieldLgDataBO::getComUniCode, Function.identity(), (x1, x2) -> x2));
        List<YieldSpreadTraceDataExportExcelDTO> dataResponseDTOList = areaConfigList.stream()
                .map(comUniCode -> {
                    YieldSpreadTraceDataExportExcelDTO defaultResponseDTO = new YieldSpreadTraceDataExportExcelDTO();
                    EnumUtils.getEnumByValue(comUniCode.intValue(), SpreadComAreaEnum.class)
                            .ifPresent(spreadComAreaEnum -> defaultResponseDTO.setTypeName(spreadComAreaEnum.getText()));
                    YieldLgDataBO yieldLgDataBO = dataResponseMap.get(comUniCode);
                    if (Objects.nonNull(yieldLgDataBO)) {
                        BeanCopyUtils.copyProperties(yieldLgDataBO, defaultResponseDTO);
                    }
                    return defaultResponseDTO;
                }).collect(Collectors.toList());
        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
        if (BondYieldTableTypeEnum.ABS.equals(bondYieldTableTypeEnum)) {
            excelWriter.fill(new FillWrapper(String.format(LG_EXPORT_FILL, ""),
                    periodExcelFillDataBOList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper("lg" + bondYieldTableTypeEnum.getExcelPlaceholder(), dataResponseDTOList), writeSheet);
            excelWriter.fill(new FillWrapper(bondYieldTableTypeEnum.getExcelPlaceholder() + "Type", dataResponseDTOList), writeSheet);
        } else if (BondYieldTableTypeEnum.HIST_QUANTILE.equals(bondYieldTableTypeEnum)) {
            String excelPlaceholder = BondYieldTableTypeEnum.HIST_QUANTILE.getExcelPlaceholder();
            excelWriter.fill(new FillWrapper(String.format(LG_EXPORT_FILL, excelPlaceholder),
                    periodExcelFillDataBOList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper("lg" + excelPlaceholder, dataResponseDTOList), writeSheet);
            excelWriter.fill(new FillWrapper(excelPlaceholder + "Type", dataResponseDTOList), writeSheet);
        } else {
            String excelPlaceholder = BondYieldTableTypeEnum.INTERVAL_CHANGE.getExcelPlaceholder();
            excelWriter.fill(new FillWrapper(String.format(LG_EXPORT_FILL, excelPlaceholder),
                    periodExcelFillDataBOList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper("lg" + excelPlaceholder, dataResponseDTOList), writeSheet);
            excelWriter.fill(new FillWrapper(excelPlaceholder + "Type", dataResponseDTOList), writeSheet);
        }

    }

    private List<PeriodExcelFillDataBO> listPeriodLgExcelFillData(BondYieldTableTypeEnum bondYieldTableTypeEnum,
                                                                  List<PeriodEnum> periodEnums) {
        if (Objects.isNull(bondYieldTableTypeEnum) || CollectionUtils.isEmpty(periodEnums)) {
            return Lists.newArrayList();
        }
        return periodEnums.stream().map(periodEnum -> {
            PeriodExcelFillDataBO periodExcelFillDataBO = new PeriodExcelFillDataBO();
            periodExcelFillDataBO.setPeriod(periodEnum.getText());
            periodExcelFillDataBO.setYieldPeriodFill(String.format(periodEnum.getPeriodExcelFill(), "lg" + bondYieldTableTypeEnum.getExcelPlaceholder()));
            return periodExcelFillDataBO;
        }).collect(Collectors.toList());
    }

    @Override
    public Date maxSpreadDate() {
        BondYieldPanoramaTraceSpreadDateDTO maxSpreadDate = getMaxSpreadDate();
        return maxSpreadDate.maxSpreadDate().orElseGet(() -> {
            logger.warn("[地方债利差] maxSpreadDate_empty");
            return Date.valueOf(LocalDate.now().minusDays(1));
        });
    }

    private BondYieldPanoramaTraceSpreadDateDTO getMaxSpreadDate() {
        String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String redisKey = String.format(LG_AREA_SPREAD_DATE_KEY, now);
        Optional<String> spreadDateJsonOpt = redisService.get(redisKey);
        if (spreadDateJsonOpt.isPresent()) {
            return JSON.parseObject(spreadDateJsonOpt.get(), BondYieldPanoramaTraceSpreadDateDTO.class);
        }
        BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO = new BondYieldPanoramaTraceSpreadDateDTO();
        pgLgBondYieldSpreadBaseDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
        pgLgBondYieldSpreadQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
        pgLgBondYieldSpreadChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
        if (ObjectUtils.anyNotNull(spreadDateDTO.getAbsSpreadDate(), spreadDateDTO.getChangeSpreadDate(), spreadDateDTO.getQuantileSpreadDate())) {
            redisService.set(redisKey, JSON.toJSONString(spreadDateDTO), YieldSpreadConst.TWO_DAYS, TimeUnit.DAYS);
        }
        return spreadDateDTO;
    }

    /**
     * 根据参数获取地区对比图
     *
     * @param userId     用户id
     * @param requestDTO 请求值
     * @return 数据
     */
    @Override
    public YieldTraceLineCommonResponseDTO lgLineChartWithPeriod(Long userId, LgSpreadLinePeriodRequestDTO requestDTO) {
        List<Long> comUniCodeList = requestDTO.getComUniCodeList();
        Integer periodType = requestDTO.getPeriodType();
        Integer spreadDataType = requestDTO.getSpreadDataType();
        List<SpreadComAreaEnum> spreadComAreaEnumList = Arrays.stream(SpreadComAreaEnum.values())
                .filter(spreadComAreaEnum -> comUniCodeList.contains((long) spreadComAreaEnum.getValue()))
                .collect(Collectors.toList());
        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, spreadDataType);
        PeriodEnum periodEnum = ITextValueEnum.getEnum(PeriodEnum.class, periodType);

        LinkedHashMap<Integer, YieldLgLineCacheDataBO> areaDataMap = Maps.newLinkedHashMap();
        for (SpreadComAreaEnum spreadComAreaEnum : spreadComAreaEnumList) {
            String redisKey = getLgLineDataRedisKey(spreadComAreaEnum, periodEnum, spreadCurveTypeEnum, requestDTO).orElse(null);
            Optional<String> valueOpt = redisService.get(redisKey);
            if (!valueOpt.isPresent()) {
                continue;
            }
            String value = valueOpt.get();
            areaDataMap.put(spreadComAreaEnum.getValue(), JSON.parseObject(ZipUtils.unzipBase64(value), YieldLgLineCacheDataBO.class));
        }

        return convertLgLineResponseDTO(areaDataMap, requestDTO.getStartDate(), requestDTO.getEndDate(), SpreadComAreaEnum.class);
    }

    private <T extends ITextValueEnum> YieldTraceLineCommonResponseDTO convertLgLineResponseDTO(LinkedHashMap<Integer, YieldLgLineCacheDataBO> dataMap
            , Date reqStartDate, Date reqEndDate, Class<T> clazz) {
        Date minDate = null;
        Date maxDate = null;
        //获取所有数据中的最小日期和最大日期
        for (Map.Entry<Integer, YieldLgLineCacheDataBO> lgLineCacheDataBOEntry : dataMap.entrySet()) {
            YieldLgLineCacheDataBO value = lgLineCacheDataBOEntry.getValue();
            Date[] issueDates = value.getIssueDates();
            if (issueDates.length > 0) {
                if (Objects.isNull(minDate) || issueDates[0].before(minDate)) {
                    minDate = issueDates[0];
                }
                if (Objects.isNull(maxDate) || issueDates[issueDates.length - 1].after(maxDate)) {
                    maxDate = issueDates[issueDates.length - 1];
                }
            }
        }
        if (!ObjectUtils.allNotNull(minDate, maxDate)) {
            return new YieldTraceLineCommonResponseDTO();
        }
        //处理东八区问题
        reqStartDate = Date.valueOf(reqStartDate.toLocalDate());
        reqEndDate = Date.valueOf(reqEndDate.toLocalDate());
        //获取实际的最大日期和最小日期
        if (reqStartDate.after(minDate)) {
            minDate = reqStartDate;
        }
        if (reqEndDate.before(maxDate)) {
            maxDate = reqEndDate;
        }

        YieldTraceLineCommonResponseDTO yieldTraceLineCommonResponseDTO = new YieldTraceLineCommonResponseDTO();
        List<Date> issueDates = Lists.newArrayList();
        //响应的string数据
        LinkedHashMap<Integer, LgBondLineChartTempBO> responseDataMap = Maps.newLinkedHashMap();
        dataMap.keySet().forEach(key -> {
            LgBondLineChartTempBO lgBondLineChartTempBO = new LgBondLineChartTempBO();
            lgBondLineChartTempBO.setTypeCode(key);
            lgBondLineChartTempBO.setCursor(0);
            lgBondLineChartTempBO.setDataList(new ArrayList<>());
            responseDataMap.put(key, lgBondLineChartTempBO);
        });
        fillLgLineResponseData(dataMap, minDate, maxDate, issueDates, responseDataMap);
        List<YieldTraceLineDataResponseDTO> reponseDataList = responseDataMap.entrySet().stream().map(entry -> {
            YieldTraceLineDataResponseDTO yieldTraceLineDataResponseDTO = new YieldTraceLineDataResponseDTO();
            yieldTraceLineDataResponseDTO.setTypeCode(entry.getKey());
            EnumUtils.getEnumByValue(entry.getKey(), clazz).map(ITextValueEnum::getText).ifPresent(yieldTraceLineDataResponseDTO::setTypeName);
            yieldTraceLineDataResponseDTO.setYields(entry.getValue().getDataList());
            return yieldTraceLineDataResponseDTO;
        }).collect(Collectors.toList());
        yieldTraceLineCommonResponseDTO.setDataDTOLists(reponseDataList);
        yieldTraceLineCommonResponseDTO.setIssueDates(issueDates);
        return yieldTraceLineCommonResponseDTO;
    }

    private void fillLgLineResponseData(LinkedHashMap<Integer, YieldLgLineCacheDataBO> dataMap,
                                        Date minDate, Date maxDate,
                                        List<Date> issueDates, LinkedHashMap<Integer, LgBondLineChartTempBO> responseDataMap) {
        boolean isDateCursorStart = false;
        while (!minDate.after(maxDate)) {
            boolean isAnyNotNull = false;
            for (Map.Entry<Integer, YieldLgLineCacheDataBO> lgLineCacheDataBOEntry : dataMap.entrySet()) {
                Integer typeCode = lgLineCacheDataBOEntry.getKey();
                LgBondLineChartTempBO lgBondLineChartTempBO = responseDataMap.get(typeCode);
                Integer cursor = lgBondLineChartTempBO.getCursor();
                YieldLgLineCacheDataBO lgLineCacheDataBO = lgLineCacheDataBOEntry.getValue();
                Date[] currentIssueDates = lgLineCacheDataBO.getIssueDates();
                BigDecimal[] currentYieldDatas = lgLineCacheDataBO.getYields();
                List<String> dataList = lgBondLineChartTempBO.getDataList();
                if (cursor > currentIssueDates.length - 1 || minDate.before(currentIssueDates[cursor])) {
                    dataList.add(null);
                    isDateCursorStart = true;
                } else if (minDate.after(currentIssueDates[cursor])) {
                    lgBondLineChartTempBO.setCursor(cursor + 1);
                } else {
                    dataList.add(bigDecimalToStringWithNull(currentYieldDatas[cursor]));
                    lgBondLineChartTempBO.setCursor(cursor + 1);
                    isDateCursorStart = true;
                    isAnyNotNull = true;
                }
            }
            //都是null,说明当前日期都没有数据,那 issueDateArray不用加
            if (isAnyNotNull) {
                issueDates.add(minDate);
            } else if (isDateCursorStart) {
                responseDataMap.values().forEach(responseData -> responseData.getDataList().remove(responseData.getDataList().size() - 1));
            }
            if (isDateCursorStart) {
                minDate = Date.valueOf(minDate.toLocalDate().plusDays(1L));
            }
        }
    }

    private String bigDecimalToStringWithNull(BigDecimal value) {
        return Objects.isNull(value) ? null : value.toPlainString();
    }

    /**
     * 根据参数获取同区域分层数据
     *
     * @param userId     用户id
     * @param requestDTO 请求参数
     * @return 数据
     */
    @Override
    public YieldTraceLineCommonResponseDTO lgLineChartWithArea(Long userId, LgSpreadLineAreaRequestDTO requestDTO) {
        Long comUniCode = requestDTO.getComUniCode();
        SpreadComAreaEnum areaEnum = ITextValueEnum.getEnum(SpreadComAreaEnum.class, comUniCode.intValue());
        Integer spreadDataType = requestDTO.getSpreadDataType();
        List<Integer> periodConfigList = getLgUserConfig(userId, (long) UserConfigEnum.CONFIG_30102.getValue(), Integer.class);

        List<PeriodEnum> periodEnumList = Arrays.stream(PeriodEnum.values())
                .filter(periodEnum -> periodConfigList.contains(periodEnum.getValue()))
                .collect(Collectors.toList());
        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, spreadDataType);

        LinkedHashMap<Integer, YieldLgLineCacheDataBO> areaDataMap = Maps.newLinkedHashMap();
        for (PeriodEnum periodEnum : periodEnumList) {
            String redisKey = getLgLineDataRedisKey(areaEnum, periodEnum, spreadCurveTypeEnum, requestDTO).orElse(null);
            Optional<String> valueOpt = redisService.get(redisKey);
            if (!valueOpt.isPresent()) {
                continue;
            }
            String value = valueOpt.get();
            areaDataMap.put(periodEnum.getValue(), JSON.parseObject(ZipUtils.unzipBase64(value), YieldLgLineCacheDataBO.class));
        }

        return convertLgLineResponseDTO(areaDataMap, requestDTO.getStartDate(), requestDTO.getEndDate(), PeriodEnum.class);
    }

    /**
     * 获取所有主体区域列表
     *
     * @return 主体区域列表
     */
    @Override
    public List<LgComAreaResponseDTO> listAllComArea() {
        return Arrays.stream(SpreadComAreaEnum.values()).map(spreadComAreaEnum -> {
            LgComAreaResponseDTO lgComAreaResponseDTO = new LgComAreaResponseDTO();
            lgComAreaResponseDTO.setComUniCode((long) spreadComAreaEnum.getValue());
            lgComAreaResponseDTO.setLgAreaName(spreadComAreaEnum.getText());
            return lgComAreaResponseDTO;
        }).collect(Collectors.toList());
    }
}
