package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.util.Objects;

/**
 * 表区利差查询
 *
 * <AUTHOR>
 */
public class YieldSpreadSearchReqDTO extends BaseRequest {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "利差日期", example = "2020-04-07", required = true)
    protected Date spreadDate;

    @ApiModelProperty("曲线id")
    @NotNull(message = "曲线id不能为空")
    private Long curveId;

    @ApiModelProperty("排序字段")
    private SortDTO sort;

    @ApiModelProperty("主体或债券的uniCode")
    private Long uniCode;

    /**
     * @see com.innodealing.onshore.yieldspread.enums.SpreadCodeTypeEnum
     */
    @ApiModelProperty("条件类型 1：主体利差，2：单券利差")
    private Integer uniCodeType;

    public Integer getUniCodeType() {
        return uniCodeType;
    }

    public void setUniCodeType(Integer uniCodeType) {
        this.uniCodeType = uniCodeType;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getCurveId() {
        return curveId;
    }

    public void setCurveId(Long curveId) {
        this.curveId = curveId;
    }

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }

    public Long getUniCode() {
        return uniCode;
    }

    public void setUniCode(Long uniCode) {
        this.uniCode = uniCode;
    }

}
