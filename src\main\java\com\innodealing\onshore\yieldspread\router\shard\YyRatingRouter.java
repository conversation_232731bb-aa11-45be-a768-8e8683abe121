package com.innodealing.onshore.yieldspread.router.shard;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * yy评级路由
 *
 * <AUTHOR>
 */
@JSONType(serialzeFeatures = SerializerFeature.SortField)
@SuppressWarnings({"squid:S2162"})
public class YyRatingRouter extends AbstractRatingRouter {

    @JSONField(ordinal = 1)
    private Integer level1;
    @JSONField(ordinal = 2)
    private Integer level2;
    @JSONField(ordinal = 3)
    private Integer level3;
    @JSONField(ordinal = 4)
    private Integer level4;
    @JSONField(ordinal = 5)
    private Integer level5;
    @JSONField(ordinal = 6)
    private Integer level6;
    @JSONField(ordinal = 7)
    private Integer level7;
    @JSONField(ordinal = 8)
    private Integer level8;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof YyRatingRouter)) {
            return false;
        }
        YyRatingRouter that = (YyRatingRouter) o;
        return Objects.equals(getSpreadDateRange(), that.getSpreadDateRange()) &&
                Objects.equals(getLevel(), that.getLevel()) &&
                Objects.equals(getLevel1(), that.getLevel1()) &&
                Objects.equals(getLevel2(), that.getLevel2()) &&
                Objects.equals(getLevel3(), that.getLevel3()) &&
                Objects.equals(getLevel4(), that.getLevel4()) &&
                Objects.equals(getLevel5(), that.getLevel5()) &&
                Objects.equals(getLevel6(), that.getLevel6()) &&
                Objects.equals(getLevel7(), that.getLevel7()) &&
                Objects.equals(getLevel8(), that.getLevel8());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getSpreadDateRange(), getLevel(), getLevel1(), getLevel2(), getLevel3(), getLevel4(),
                getLevel5(), getLevel6(), getLevel7(), getLevel8());
    }

    public Integer getLevel1() {
        return level1;
    }

    public void setLevel1(Integer level1) {
        this.level1 = level1;
    }

    public Integer getLevel2() {
        return level2;
    }

    public void setLevel2(Integer level2) {
        this.level2 = level2;
    }

    public Integer getLevel3() {
        return level3;
    }

    public void setLevel3(Integer level3) {
        this.level3 = level3;
    }

    public Integer getLevel4() {
        return level4;
    }

    public void setLevel4(Integer level4) {
        this.level4 = level4;
    }

    public Integer getLevel5() {
        return level5;
    }

    public void setLevel5(Integer level5) {
        this.level5 = level5;
    }

    public Integer getLevel6() {
        return level6;
    }

    public void setLevel6(Integer level6) {
        this.level6 = level6;
    }

    public Integer getLevel7() {
        return level7;
    }

    public void setLevel7(Integer level7) {
        this.level7 = level7;
    }

    public Integer getLevel8() {
        return level8;
    }

    public void setLevel8(Integer level8) {
        this.level8 = level8;
    }


    @Override
    public List<Integer> getRatings() {
        return yyRatings();
    }

    private List<Integer> yyRatings() {
        return Stream.of(this.getLevel1(), this.getLevel2(), this.getLevel3(),
                        this.getLevel4(), this.getLevel5(), this.getLevel6(), this.getLevel7(),
                        this.getLevel8())
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public int compareTo(AbstractRatingRouter o) {
        int x = this.hashCode();
        int y = o.hashCode();
        return Integer.compare(x, y);
    }
}
