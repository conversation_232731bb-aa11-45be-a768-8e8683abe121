package com.innodealing.onshore.yieldspread.controller.api;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.request.BondYieldSpreadListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondCreditYieldCurvesResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondYieldSpreadCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondYieldSpreadListResponseDTO;
import com.innodealing.onshore.yieldspread.service.BondYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.sql.Date;
import java.util.List;

/**
 * 单券利差控制器
 *
 * <AUTHOR>
 */
@Api(tags = "(API)单券利差")
@RestController
@Validated
@RequestMapping("api/bond/yield-spread")
public class BondYieldSpreadController {

    @Resource
    private BondYieldSpreadService bondYieldSpreadService;

    @ApiOperation(value = "曲线")
    @GetMapping(value = "/curves")
    public RestResponse<List<BondYieldSpreadCurveResponseDTO>> listCurves(
            @ApiParam(value = "债券唯一编码", name = "bondUniCode", required = true)
            @RequestParam Long bondUniCode,
            @ApiParam(value = "曲线类型: 1:产业，2:城投，3:银行，4:证券，5:自定义，7：保险", name = "curveType", required = true)
            @RequestParam Integer curveType,
            @ApiParam(value = "开始日期", name = "startDate", required = true)
            @RequestParam Date startDate,
            @ApiParam(value = "结束日期", name = "endDate", required = true)
            @RequestParam Date endDate) {
        return RestResponse.Success(bondYieldSpreadService.listCurves(bondUniCode, curveType, startDate, endDate));
    }

    @ApiOperation(value = "债券列表数据")
    @PostMapping(value = "/list-bonds")
    public RestResponse<BondYieldSpreadListResponseDTO> listBonds(@RequestBody @Validated BondYieldSpreadListRequestDTO request,
                                                                  @ApiParam(name = "userid", value = "用户编号", hidden = true)
                                                                  @CookieValue("userid") Long userid) {
        return RestResponse.Success(bondYieldSpreadService.listBonds(request, userid));
    }

    @ApiOperation(value = "根据BondUniCodes获取信用利差曲线数据，不传日期默认查最近一年，最多支持3年")
    @PostMapping("credit-spread/list-curve")
    public RestResponse<BondCreditYieldCurvesResDTO> listCreditSpreadCurves(
            @ApiParam(name = "bondUniCodes", value = "债券BondUniCode", required = true)
            @NotNull(message = "查询曲线的bondUniCode不能为空") @Size(min = 1, max = 3, message = "只支持查询1-3条曲线") @RequestBody List<Long> bondUniCodes,
            @ApiParam(name = "startDate", value = "利差查询开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "利差查询结束日期") @RequestParam(required = false) Date endDate) {
        return RestResponse.Success(bondYieldSpreadService.listCreditSpreadCurves(bondUniCodes, startDate, endDate));
    }

}
