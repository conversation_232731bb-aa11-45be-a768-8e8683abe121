package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.service.internal.SyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * (内部)同步数据
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部)同步数据")
@RestController
@RequestMapping("internal/sync")
public class InternalSyncController {

    @Resource
    private SyncService service;

    @ApiOperation(value = "同步所有利差债券到yield_spread_bond表")
    @PostMapping("/all-yield-spread-bond")
    public int syncAllYieldSpreadBond() {
        return service.syncAllYieldSpreadBond();
    }

    @ApiOperation(value = "刷新所有利差债券缓存")
    @PostMapping("/refresh-local-cache/yield-spread-bond")
    public void refreshYieldSpreadBondLocalCache() {
       service.refreshYieldSpreadBondLocalCache();
    }

}
