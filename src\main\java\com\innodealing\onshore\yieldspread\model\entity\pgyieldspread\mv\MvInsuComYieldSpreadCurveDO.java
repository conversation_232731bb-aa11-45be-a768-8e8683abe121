package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 主体利差曲线-保险
 *
 * <AUTHOR>
 */
@Table(name = "mv_insu_com_yield_spread_curve")
public class MvInsuComYieldSpreadCurveDO extends BaseMvComYieldSpreadCurveDO {

    /**
     * 银行求偿顺序 1:普通;2:二级资本债;3:永续债
     */
    @Column
    private Integer insuranceSeniorityRanking;
    /**
     * 是否使用银行求偿顺序进行分组 0:是 1:否
     */
    @Column
    private Integer usingInsuranceSeniorityRanking;

    public Integer getInsuranceSeniorityRanking() {
        return insuranceSeniorityRanking;
    }

    public void setInsuranceSeniorityRanking(Integer insuranceSeniorityRanking) {
        this.insuranceSeniorityRanking = insuranceSeniorityRanking;
    }

    public Integer getUsingInsuranceSeniorityRanking() {
        return usingInsuranceSeniorityRanking;
    }

    public void setUsingInsuranceSeniorityRanking(Integer usingInsuranceSeniorityRanking) {
        this.usingInsuranceSeniorityRanking = usingInsuranceSeniorityRanking;
    }
}
