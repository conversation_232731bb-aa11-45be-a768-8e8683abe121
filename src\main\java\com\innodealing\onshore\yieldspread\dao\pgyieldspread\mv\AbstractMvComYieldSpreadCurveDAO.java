package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.BaseMvComYieldSpreadCurveDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;

import java.util.List;

import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.SPREAD_KEEP_SCALE;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE;

/**
 * 基础主体利差曲线DAO
 *
 * @param <T> PgBaseMapper使用参数
 * <AUTHOR>
 */
public abstract class AbstractMvComYieldSpreadCurveDAO<T> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final PgBaseMapper<T> pgBaseMapper;

    /**
     * 构造函数
     *
     * @param pgBaseMapper mapper
     */
    protected AbstractMvComYieldSpreadCurveDAO(PgBaseMapper<T> pgBaseMapper) {
        this.pgBaseMapper = pgBaseMapper;
    }

    /**
     * 刷新物化视图
     */
    public void refresh() {
        final String tableName = this.tableName();
        logger.info("AbstractMvComYieldSpreadCurveDAO#refresh {} start...", tableName);
        pgBaseMapper.refreshMvInduBondYieldSpreadCurve(tableName);
        logger.info("AbstractMvComYieldSpreadCurveDAO#refresh {} end...", tableName);
    }

    /**
     * 查询主体利差数据集合
     *
     * @param comUniCode      主体唯一编码
     * @param spreadBondValue 利差债券类别，证券，产业，银行，城投枚举值不同
     * @return {@link List}<{@link ComYieldSpreadCurveBO}>
     * @see com.innodealing.onshore.bondmetadata.enums.SecuritySeniorityRankingEnum
     * @see com.innodealing.onshore.bondmetadata.enums.BankSeniorityRankingEnum
     * @see com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum
     */
    public abstract List<ComYieldSpreadCurveBO> list(@NonNull Long comUniCode, Integer spreadBondValue);

    /**
     * 获取表名
     *
     * @return {@link String}
     */
    protected abstract String tableName();

    /**
     * 处理曲线精度
     *
     * @param curve 曲线对象
     * @return {@link ComYieldSpreadCurveBO}
     */
    protected <V extends BaseMvComYieldSpreadCurveDO> ComYieldSpreadCurveBO handlePrecision(V curve) {
        ComYieldSpreadCurveBO comYieldSpreadCurveBO = BeanCopyUtils.copyProperties(curve, ComYieldSpreadCurveBO.class);
        BigDecimalUtils.handlerPrecision(curve.getBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(comYieldSpreadCurveBO::setBondCreditSpread);
        BigDecimalUtils.handlerPrecision(curve.getBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(comYieldSpreadCurveBO::setBondExcessSpread);
        BigDecimalUtils.handlerPrecision(curve.getCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(comYieldSpreadCurveBO::setCbYield);
        BigDecimalUtils.handlerPrecision(curve.getAvgBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(comYieldSpreadCurveBO::setAvgBondCreditSpread);
        BigDecimalUtils.handlerPrecision(curve.getAvgBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(comYieldSpreadCurveBO::setAvgBondExcessSpread);
        BigDecimalUtils.handlerPrecision(curve.getAvgCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(comYieldSpreadCurveBO::setAvgCbYield);
        return comYieldSpreadCurveBO;
    }
}
