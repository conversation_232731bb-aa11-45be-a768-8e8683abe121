package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 利差追踪折线图(同期限)
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class YieldSpreadTraceLineChartPeriodResponseDTO {

    @ApiModelProperty("日期")
    private List<Date> issueDates;

    @ApiModelProperty("1Y 证券普通债、证券次级债才有值")
    private List<String> ytm1Ys;

    @ApiModelProperty("2Y 证券普通债、证券次级债才有值")
    private List<String> ytm2Ys;

    @ApiModelProperty("3Y 证券普通债、证券次级债才有值")
    private List<String> ytm3Ys;

    @ApiModelProperty("5Y 证券普通债、证券次级债才有值")
    private List<String> ytm5Ys;

    @ApiModelProperty("7Y 证券普通债、证券次级债才有值")
    private List<String> ytm7Ys;

    @ApiModelProperty("AAA+")
    private List<String> ratingAAAPluses;

    @ApiModelProperty("AAA")
    private List<String> ratingAAAs;

    @ApiModelProperty("AAA-")
    private List<String> ratingAAASubs;

    @ApiModelProperty("AA+")
    private List<String> ratingAAPluses;

    @ApiModelProperty("AA")
    private List<String> ratingAAs;

    @ApiModelProperty("AA(2)")
    private List<String> ratingAATwos;

    @ApiModelProperty("AA-")
    private List<String> ratingAASubs;

    public List<Date> getIssueDates() {
        return issueDates;
    }

    public void setIssueDates(List<Date> issueDates) {
        this.issueDates = Objects.isNull(issueDates) ? new ArrayList<>() : new ArrayList<>(issueDates);
    }

    public List<String> getYtm1Ys() {
        return ytm1Ys;
    }

    public void setYtm1Ys(List<String> ytm1Ys) {
        this.ytm1Ys = Objects.isNull(ytm1Ys) ? new ArrayList<>() : new ArrayList<>(ytm1Ys);
    }

    public List<String> getYtm2Ys() {
        return ytm2Ys;
    }

    public void setYtm2Ys(List<String> ytm2Ys) {
        this.ytm2Ys = Objects.isNull(ytm2Ys) ? new ArrayList<>() : new ArrayList<>(ytm2Ys);
    }

    public List<String> getYtm3Ys() {
        return ytm3Ys;
    }

    public void setYtm3Ys(List<String> ytm3Ys) {
        this.ytm3Ys = Objects.isNull(ytm3Ys) ? new ArrayList<>() : new ArrayList<>(ytm3Ys);
    }

    public List<String> getYtm5Ys() {
        return ytm5Ys;
    }

    public void setYtm5Ys(List<String> ytm5Ys) {
        this.ytm5Ys = Objects.isNull(ytm5Ys) ? new ArrayList<>() : new ArrayList<>(ytm5Ys);
    }

    public List<String> getYtm7Ys() {
        return ytm7Ys;
    }

    public void setYtm7Ys(List<String> ytm7Ys) {
        this.ytm7Ys = Objects.isNull(ytm7Ys) ? new ArrayList<>() : new ArrayList<>(ytm7Ys);
    }

    public List<String> getRatingAAAPluses() {
        return ratingAAAPluses;
    }

    public void setRatingAAAPluses(List<String> ratingAAAPluses) {
        this.ratingAAAPluses = Objects.isNull(ratingAAAPluses) ? new ArrayList<>() : new ArrayList<>(ratingAAAPluses);
    }

    public List<String> getRatingAAAs() {
        return ratingAAAs;
    }

    public void setRatingAAAs(List<String> ratingAAAs) {
        this.ratingAAAs = Objects.isNull(ratingAAAs) ? new ArrayList<>() : new ArrayList<>(ratingAAAs);
    }

    public List<String> getRatingAAASubs() {
        return ratingAAASubs;
    }

    public void setRatingAAASubs(List<String> ratingAAASubs) {
        this.ratingAAASubs = Objects.isNull(ratingAAASubs) ? new ArrayList<>() : new ArrayList<>(ratingAAASubs);
    }

    public List<String> getRatingAAPluses() {
        return ratingAAPluses;
    }

    public void setRatingAAPluses(List<String> ratingAAPluses) {
        this.ratingAAPluses = Objects.isNull(ratingAAPluses) ? new ArrayList<>() : new ArrayList<>(ratingAAPluses);
    }

    public List<String> getRatingAAs() {
        return ratingAAs;
    }

    public void setRatingAAs(List<String> ratingAAs) {
        this.ratingAAs = Objects.isNull(ratingAAs) ? new ArrayList<>() : new ArrayList<>(ratingAAs);
    }

    public List<String> getRatingAATwos() {
        return ratingAATwos;
    }

    public void setRatingAATwos(List<String> ratingAATwos) {
        this.ratingAATwos = Objects.isNull(ratingAATwos) ? new ArrayList<>() : new ArrayList<>(ratingAATwos);
    }

    public List<String> getRatingAASubs() {
        return ratingAASubs;
    }

    public void setRatingAASubs(List<String> ratingAASubs) {
        this.ratingAASubs = Objects.isNull(ratingAASubs) ? new ArrayList<>() : new ArrayList<>(ratingAASubs);
    }

}
