package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgSecuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadCreditYieldGroupDO;

/**
 * pg secu债券利差计算信用利差中位数Mapper
 *
 * <AUTHOR>
 */
public interface PgSecuBondYieldSpreadCreditYieldGroupMapper extends SelectByGroupedQueryMapper<PgSecuBondYieldSpreadDO,
        PgSecuBondYieldSpreadCreditYieldGroupDO> {
}
