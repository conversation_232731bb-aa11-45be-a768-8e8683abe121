package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.mv.MvSecuShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvSecuBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvSecuBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 证券利差曲线DAO
 * <AUTHOR>
 */
@Repository
public class MvSecuBondYieldSpreadCurveDAO {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MvSecuBondYieldSpreadCurveMapper mvSecuBondYieldSpreadCurveMapper;

    @Resource
    private MvSecuShardBondYieldSpreadCurveMapper mvSecuShardBondYieldSpreadCurveMapper;

    /**
     * 创建或刷新证券物化视图
     * @param router 路由
     * @param param 创建条件
     */
    public void createOrRefreshSecuCurveMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        String tableName = this.getMvName(router);
        logger.info("[refreshMvInduBondYieldSpreadBondCurveRouter]开始创建证券评级分片物化视图:{}", tableName);
        MvSecuBondYieldSpreadCurveParameter parameter = this.builderShardMvParam(router);
        parameter.setTableName(tableName);
        if (param.isMvRefresh()) {
            mvSecuBondYieldSpreadCurveMapper.dropMv(tableName);
            mvSecuBondYieldSpreadCurveMapper.createMvRatingRouter(parameter);
        }else {
            mvSecuBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurve(tableName);
        }
    }

    private MvSecuBondYieldSpreadCurveParameter builderShardMvParam(AbstractRatingRouter router) {
        MvSecuBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, MvSecuBondYieldSpreadCurveParameter.class);
        parameter.setImpliedRatingMappings(router.getRatings());
        return parameter;
    }


    /**
     * 时间范围不为空，删除临时表
     *
     * @param router 路由
     */
    public void droTempMv(AbstractRatingRouter router) {
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (Objects.nonNull(spreadDateRange)) {
            mvSecuBondYieldSpreadCurveMapper.dropMv(this.getMvName(router));
        }
    }


    /**
     *  获取视图表名
     * @param router 路由
     * @return 视图名称
     */
    public String getMvName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()), mvSecuShardBondYieldSpreadCurveMapper.getLogicTable(),
                RatingCombinationHelper.getMvNameSuffix(router));
    }


}
