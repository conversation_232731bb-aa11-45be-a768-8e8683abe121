package com.innodealing.onshore.yieldspread.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * 曲线唯一编码枚举类
 *
 * <AUTHOR>
 * @date 2024/4/12 16:12
 **/
public enum CurveUniCodeEnum {
    /**
     * 中债保险公司资本补充债(AA+) 对应code
     */
    INSU_CAPITAL_SUPPLY_BOND_AA_PLUS(101_747L, 200, "中债保险公司资本补充债(AA+)"),
    /**
     * 中债保险公司资本补充债(AA) 对应code
     */
    INSU_CAPITAL_SUPPLY_BOND_AA(101_748L, 201, "中债保险公司资本补充债(AA)"),
    /**
     * 中债保险公司资本补充债(AA-) 对应code
     */
    INSU_CAPITAL_SUPPLY_BOND_AA_SUB(101_749L, 202, "中债保险公司资本补充债(AA-)"),

    /**
     * 中债农发债
     */
    CHINA_BOND_NO(100_966L, 203, "中债农发债"),
    /**
     * 中债进出口行债
     */
    CHINA_BOND_IMPORT(100_988L, 204, "中债进出口行债");

    private final Long curveUniCode;
    private final Integer curveCode;
    private final String text;

    CurveUniCodeEnum(Long curveUniCode, Integer curveCode, String text) {
        this.curveUniCode = curveUniCode;
        this.curveCode = curveCode;
        this.text = text;
    }

    public String getText() {
        return this.text;
    }

    public Long getCurveUniCode() {
        return this.curveUniCode;
    }

    public Integer getCurveCode() {
        return this.curveCode;
    }

    /**
     * 根据利差定义的曲线curveCode 获取价格服务对应的唯一curveUniCode
     *
     * @param curveCode 利差定义的曲线curveCode
     * @return CurveUniCode 值
     */
    public static Optional<Long> getCurveUniCodeByCurveCode(Integer curveCode) {
        return Arrays.stream(CurveUniCodeEnum.values()).
                filter(curveUniCodeEnum -> curveUniCodeEnum.getCurveCode().equals(curveCode))
                .map(CurveUniCodeEnum::getCurveUniCode).findFirst();
    }

    /**
     * 根据价格服务对应的唯一curveUniCode 获取利差定义的曲线curveCode
     *
     * @param curveUniCode 曲线唯一编码
     * @return CurveCode值
     */
    public static Optional<Integer> getCurveCodeByCurveUniCode(Long curveUniCode) {
        return Arrays.stream(CurveUniCodeEnum.values()).
                filter(curveUniCodeEnum -> curveUniCodeEnum.getCurveUniCode().equals(curveUniCode))
                .map(CurveUniCodeEnum::getCurveCode).findFirst();
    }
}
