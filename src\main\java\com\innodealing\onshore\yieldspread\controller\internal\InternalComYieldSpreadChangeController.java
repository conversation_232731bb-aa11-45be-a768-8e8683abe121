package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.service.ComYieldSpreadChangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * (内部)利差分析
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 主体利差分析变动")
@RestController
@RequestMapping("internal/com-yield-spread/change")
public class InternalComYieldSpreadChangeController {

    @Resource
    private ComYieldSpreadChangeService comYieldSpreadChangeService;

    @ApiOperation(value = "清除主体利差分析历史变动数据")
    @GetMapping("/clear")
    public int clearOldSpreadChanges() {
        return comYieldSpreadChangeService.clearOldSpreadChanges();
    }

}
