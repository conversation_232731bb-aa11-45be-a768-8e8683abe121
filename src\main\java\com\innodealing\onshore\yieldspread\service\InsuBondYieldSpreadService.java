package com.innodealing.onshore.yieldspread.service;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondpriceapollo.CurveMaturityStructureDTO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.InsuCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InsuSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuComYieldSpreadDO;

import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 保险债利差 service
 *
 * <AUTHOR>
 * @date 2024/4/10 18:46
 **/
public interface InsuBondYieldSpreadService {
    /**
     * 保险债利差计算
     *
     * @param onshoreBondInfoDTOs 债券信息
     * @param bondYieldCurveMap   (curveUniCode,收益率曲线) 收益率曲线
     * @param spreadDate          利差日期
     * @return 影响数
     */
    int calcInsuBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs,
                                             Map<Long, List<CurveMaturityStructureDTO>> bondYieldCurveMap,
                                             Date spreadDate);

    /**
     * 保险主体利差计算
     *
     * @param insuComYieldSpreadDOs 保险主体利差数据
     * @param spreadDate            利差日期
     * @return 影响数
     */
    Integer calcInsuComYieldSpreadsBySpreadDate(List<InsuComYieldSpreadDO> insuComYieldSpreadDOs, Date spreadDate);

    /**
     * 刷新物化视图
     *
     * @param param 时间片
     */
    void refreshMvInsuBondYieldSpreadRatingCurve(RefreshYieldCurveParam param);

    /**
     * 历史物化视图
     *
     * @param isTableRefresh 是否刷新表
     */
    void refreshMvInsuBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh);

    /**
     * 存入曲线池
     *
     * @param userid       用户id
     * @param curveGroupId 曲线组id
     * @param params       保存曲线参数
     * @return 操作结果
     */
    boolean saveCurve(Long userid, Long curveGroupId, InsuCurveGenerateConditionReqDTO params);

    /**
     * 更新曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @param request 更新参数
     * @return 执行结果
     */
    boolean updateCurve(Long userid, Long curveId, InsuCurveGenerateConditionReqDTO request);

    /**
     * 查询曲线数据集
     *
     * @param bondUniCode 债券唯一编码
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveDTO}>
     */
    List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate);

    /**
     * 保险单券利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 单券利差
     */
    NormPagingResult<InsuSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 查询债券数据集合
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link InsuSingleBondYieldSpreadResDTO}>
     */
    List<InsuSingleBondYieldSpreadResDTO> listBonds(Date spreadDate, Set<Long> bondUniCodes);
}
