package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvSecuBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvSecuComYieldSpreadCurveDO;

/**
 * 证券利差曲线mapper
 *
 * <AUTHOR>
 */
public interface MvSecuComYieldSpreadCurveMapper extends PgBaseMapper<MvSecuBondYieldSpreadCurveParameter>, DynamicQueryMapper<MvSecuComYieldSpreadCurveDO> {


}
