package com.innodealing.onshore.yieldspread.model.bo;

import com.innodealing.onshore.bondmetadata.enums.CurveCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;


/**
 * 利差追踪DO
 *
 * <AUTHOR>
 */
public class PgBondYieldSpreadTraceBO {

    /**
     * 债券品种，1 城投债，2 产业债 3 中短期票据 4 银行普通债 5 银行二级资本债 6 银行永续债 7 证券普通债 8 证券次级债 9 证券永续债 10 同业存单
     */
    private Integer bondType;

    /**
     * 图类型，1 到期收益率 2 信用利差 3 等级利差 4 期限利差 5 品种利差 6 条款利差 7 减国债
     */
    private Integer chartType;

    /**
     * 表格类型，1 绝对值 2 区间变动 3 3年历史分位
     */
    private Integer tableType;

    /**
     * 收益率曲线编码
     *
     * @see CurveCode
     */
    private Integer curveCode;

    /**
     * 1月到期收益率
     */
    private BigDecimal ytm1M;
    /**
     * 3月到期收益率
     */
    private BigDecimal ytm3M;
    /**
     * 6月到期收益率
     */
    private BigDecimal ytm6M;
    /**
     * 9月到期收益率
     */
    private BigDecimal ytm9M;


    /**
     * 1年到期收益率
     */
    private BigDecimal ytm1Y;

    /**
     * 2年到期收益率
     */
    private BigDecimal ytm2Y;

    /**
     * 3年到期收益率
     */
    private BigDecimal ytm3Y;

    /**
     * 5年到期收益率
     */
    private BigDecimal ytm5Y;

    /**
     * 7年到期收益率
     */
    private BigDecimal ytm7Y;

    /**
     * 10年到期收益率
     */
    private BigDecimal ytm10Y;
    /**
     * 15年到期收益率
     */
    private BigDecimal ytm15Y;
    /**
     * 20年到期收益率
     */
    private BigDecimal ytm20Y;
    /**
     * 30年到期收益率
     */
    private BigDecimal ytm30Y;
    /**
     * 50年到期收益率
     */
    private BigDecimal ytm50Y;

    /**
     * 发布日期
     */
    private Date issueDate;

    /**
     * 开始日期
     */
    private java.sql.Date startDate;

    public Integer getBondType() {
        return bondType;
    }

    public void setBondType(Integer bondType) {
        this.bondType = bondType;
    }

    public Integer getChartType() {
        return chartType;
    }

    public void setChartType(Integer chartType) {
        this.chartType = chartType;
    }

    public Integer getTableType() {
        return tableType;
    }

    public void setTableType(Integer tableType) {
        this.tableType = tableType;
    }

    public Integer getCurveCode() {
        return curveCode;
    }

    public void setCurveCode(Integer curveCode) {
        this.curveCode = curveCode;
    }

    public BigDecimal getYtm1Y() {
        return ytm1Y;
    }

    public void setYtm1Y(BigDecimal ytm1Y) {
        this.ytm1Y = ytm1Y;
    }

    public BigDecimal getYtm2Y() {
        return ytm2Y;
    }

    public void setYtm2Y(BigDecimal ytm2Y) {
        this.ytm2Y = ytm2Y;
    }

    public BigDecimal getYtm3Y() {
        return ytm3Y;
    }

    public void setYtm3Y(BigDecimal ytm3Y) {
        this.ytm3Y = ytm3Y;
    }

    public BigDecimal getYtm5Y() {
        return ytm5Y;
    }

    public void setYtm5Y(BigDecimal ytm5Y) {
        this.ytm5Y = ytm5Y;
    }

    public BigDecimal getYtm7Y() {
        return ytm7Y;
    }

    public void setYtm7Y(BigDecimal ytm7Y) {
        this.ytm7Y = ytm7Y;
    }

    public java.sql.Date getStartDate() {
        return Objects.isNull(startDate) ? null : new java.sql.Date(startDate.getTime());
    }

    public void setStartDate(java.sql.Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new java.sql.Date(startDate.getTime());
    }

    public Date getIssueDate() {
        return Objects.isNull(issueDate) ? null : new Date(issueDate.getTime());
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = Objects.isNull(issueDate) ? null : new Date(issueDate.getTime());
    }

    public BigDecimal getYtm1M() {
        return ytm1M;
    }

    public void setYtm1M(BigDecimal ytm1M) {
        this.ytm1M = ytm1M;
    }

    public BigDecimal getYtm3M() {
        return ytm3M;
    }

    public void setYtm3M(BigDecimal ytm3M) {
        this.ytm3M = ytm3M;
    }

    public BigDecimal getYtm6M() {
        return ytm6M;
    }

    public void setYtm6M(BigDecimal ytm6M) {
        this.ytm6M = ytm6M;
    }

    public BigDecimal getYtm9M() {
        return ytm9M;
    }

    public void setYtm9M(BigDecimal ytm9M) {
        this.ytm9M = ytm9M;
    }

    public BigDecimal getYtm10Y() {
        return ytm10Y;
    }

    public void setYtm10Y(BigDecimal ytm10Y) {
        this.ytm10Y = ytm10Y;
    }

    public BigDecimal getYtm15Y() {
        return ytm15Y;
    }

    public void setYtm15Y(BigDecimal ytm15Y) {
        this.ytm15Y = ytm15Y;
    }

    public BigDecimal getYtm20Y() {
        return ytm20Y;
    }

    public void setYtm20Y(BigDecimal ytm20Y) {
        this.ytm20Y = ytm20Y;
    }

    public BigDecimal getYtm30Y() {
        return ytm30Y;
    }

    public void setYtm30Y(BigDecimal ytm30Y) {
        this.ytm30Y = ytm30Y;
    }

    public BigDecimal getYtm50Y() {
        return ytm50Y;
    }

    public void setYtm50Y(BigDecimal ytm50Y) {
        this.ytm50Y = ytm50Y;
    }
}