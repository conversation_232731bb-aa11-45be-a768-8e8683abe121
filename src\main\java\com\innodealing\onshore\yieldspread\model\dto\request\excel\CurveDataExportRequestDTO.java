package com.innodealing.onshore.yieldspread.model.dto.request.excel;

import com.innodealing.onshore.yieldspread.model.dto.request.CurveBondRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.CurveComRequestDTO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 曲线数据导出请求DTO
 *
 * <AUTHOR>
 */
public class CurveDataExportRequestDTO {

    @ApiModelProperty("曲线id")
    @NotEmpty(message = "请选择曲线")
    private List<Long> curveIds;
    @ApiModelProperty("曲线算法类型 1：中位数,2：平均数")
    @NotNull(message = "请选择曲线算法类型")
    private Integer arithmeticType;
    @ApiModelProperty("起始日期")
    @NotNull(message = "请选择起始时间")
    private Date startDate;
    @ApiModelProperty("结束日期")
    @NotNull(message = "请选择结束时间")
    private Date endDate;
    @ApiModelProperty("主体请求")
    @Valid
    private List<CurveComRequestDTO> comSpreads;
    @ApiModelProperty("单券请求")
    @Valid
    private List<CurveBondRequestDTO> bondSpreads;

    public List<Long> getCurveIds() {
        return Objects.isNull(curveIds) ? new ArrayList<>() : new ArrayList<>(curveIds);
    }

    public void setCurveIds(List<Long> curveIds) {
        this.curveIds = Objects.isNull(curveIds) ? new ArrayList<>() : new ArrayList<>(curveIds);
    }

    public Integer getArithmeticType() {
        return arithmeticType;
    }

    public void setArithmeticType(Integer arithmeticType) {
        this.arithmeticType = arithmeticType;
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = new Date(startDate.getTime());
    }

    public Date getEndDate() {
        return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public void setEndDate(Date endDate) {
        this.endDate = new Date(endDate.getTime());
    }

    public List<CurveComRequestDTO> getComSpreads() {
        return Objects.isNull(comSpreads) ? new ArrayList<>() : new ArrayList<>(comSpreads);
    }

    public void setComSpreads(List<CurveComRequestDTO> comSpreads) {
        this.comSpreads = Objects.isNull(comSpreads) ? new ArrayList<>() : new ArrayList<>(comSpreads);
    }

    public List<CurveBondRequestDTO> getBondSpreads() {
        return Objects.isNull(bondSpreads) ? new ArrayList<>() : new ArrayList<>(bondSpreads);
    }

    public void setBondSpreads(List<CurveBondRequestDTO> bondSpreads) {
        this.bondSpreads = Objects.isNull(bondSpreads) ? new ArrayList<>() : new ArrayList<>(bondSpreads);
    }
}
