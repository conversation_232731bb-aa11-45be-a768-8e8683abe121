package com.innodealing.onshore.yieldspread.model.dto.response.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.CURVE_EXPORT_TITLE;

/**
 * 导出数据基类
 *
 * <AUTHOR>
 */
public class BaseCurveYieldSpreadExcelDTO {

    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "曲线"}, order = 1)
    @ColumnWidth(25)
    private String curveName;

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

}
