package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.UserSpreadConfigDTO;
import com.innodealing.onshore.yieldspread.service.UserSpreadConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * management 用户利差展示设置后台管理
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部)用户利差展示设置")
@RestController
@RequestMapping("/internal/config/user-spread-config")
public class InternalUserSpreadConfigController {

    @Resource
    private UserSpreadConfigService userSpreadConfigService;

    @ApiOperation("用户利差展示设置-保存用户利差设置")
    @PostMapping(value = "config/user/save")
    public RestResponse<Void> saveUserSpreadConfig(
            @ApiParam(value = "用户id") @RequestParam(value = "userid", required = false) Long userId,
            @RequestBody List<UserSpreadConfigDTO> userSpreadConfigDTO) {
        userSpreadConfigService.saveUserSpreadConfig(userId, userSpreadConfigDTO);
        return RestResponse.Success(null);
    }

    @ApiOperation("用户利差展示设置-查询用户利差设置")
    @GetMapping(value = "config/user/list")
    public RestResponse<List<UserSpreadConfigDTO>> listUserSpreadConfigByConfigIds(@ApiParam(value = "用户id") @RequestParam(value = "userid", required = false) Long userId,
                                                                                   @ApiParam(name = "configIds", value = "配置id列表") @RequestParam List<Long> configIds) {
        return RestResponse.Success(userSpreadConfigService.listUserSpreadConfigByConfigIds(userId, configIds));
    }

    @ApiOperation("用户利差展示设置-查询系统默认利差设置")
    @GetMapping(value = "config/default/list")
    public RestResponse<List<UserSpreadConfigDTO>> listDefaultSpreadConfigByConfigIds(@ApiParam(name = "configIds", value = "配置id列表") @RequestParam List<Long> configIds) {
        return RestResponse.Success(userSpreadConfigService.listDefaultSpreadConfigByConfigIds(configIds));
    }

}

