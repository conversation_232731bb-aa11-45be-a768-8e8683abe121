package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.bo.PgUdicAreaYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgUdicBondYieldSpreadDO;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;

/**
 * pg城投债利差Mapper
 *
 * <AUTHOR>
 **/
public interface PgUdicBondYieldSpreadMapper extends DynamicQueryMapper<PgUdicBondYieldSpreadDO> {

    /**
     * 根据日期查询城投区域利差
     *
     * @param spreadDate 利差日期
     * @return 城投区域利差
     */
    List<PgUdicAreaYieldSpreadBO> listAreaBondYieldSpreads(@Param("spreadDate") Date spreadDate);
}
