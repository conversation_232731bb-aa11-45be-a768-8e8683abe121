package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * 城投利差 RequestDTO
 *
 * <AUTHOR>
 */
public class UdicPanoramaRequestDTO extends BasePanoramaRequestDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("行政区划")
    protected Integer administrativeDivision;
    @ApiModelProperty("省份编码")
    protected Long provinceUniCode;

    public Integer getAdministrativeDivision() {
        return administrativeDivision;
    }

    public void setAdministrativeDivision(Integer administrativeDivision) {
        this.administrativeDivision = administrativeDivision;
    }

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }

    @Override
    public String toString() {
        return "UdicPanoramaRequestDTO{" +
                "administrativeDivision=" + administrativeDivision +
                ", provinceUniCode=" + provinceUniCode +
                ", bondExtRatingMapping=" + bondExtRatingMapping +
                ", bondImpliedRatingMappingTag=" + bondImpliedRatingMappingTag +
                ", comYyRatingMappingTag=" + comYyRatingMappingTag +
                ", spreadRemainingTenorTag=" + spreadRemainingTenorTag +
                ", spreadBondType=" + spreadBondType +
                ", guaranteeStatus=" + guaranteeStatus +
                ", spreadDate=" + spreadDate +
                '}';
    }
}
