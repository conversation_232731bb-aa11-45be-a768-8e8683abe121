package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.repository.PgUdicBondYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgUdicBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgUdicBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.PgUdicAreaYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.UdicSpreadPanoramaBO;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.UdicPanoramaRequestDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgUdicBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgUdicBondYieldSpreadGroupDO;
import com.innodealing.onshore.yieldspread.router.annotation.DynamicTableNameParam;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.*;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 城投债利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
@SuppressWarnings({"squid:S00107"})
public class PgUdicBondYieldSpreadDAO {

    @Resource
    private PgUdicBondYieldSpreadMapper pgUdicBondYieldSpreadMapper;

    @Resource
    private PgUdicBondYieldSpreadGroupMapper pgUdicBondYieldSpreadGroupMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private PgUdicBondYieldSpreadCurveRepository pgUdicBondYieldSpreadCurveRepository;

    /**
     * 获取城投利差分析计算中位数后的数据
     *
     * @param request 全景图查询参数
     * @return UdicSpreadPanoramaBO
     */
    public List<UdicSpreadPanoramaBO> listUdicBondYieldInduSpreads(UdicPanoramaRequestDTO request) {
        List<PgUdicBondYieldSpreadGroupDO> pgUdicBondYieldSpreadGroupDOList = pgUdicBondYieldSpreadCurveRepository.listUdicBondYieldInduSpreads(request);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadGroupDOList)) {
            return Collections.emptyList();
        }
        return pgUdicBondYieldSpreadGroupDOList.stream().map(x -> {
            UdicSpreadPanoramaBO result = BeanCopyUtils.copyProperties(x, UdicSpreadPanoramaBO.class);
            if (Objects.nonNull(x.getBondCreditSpread())) {
                result.setBondCreditSpread(x.getBondCreditSpread()
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.nonNull(x.getBondExcessSpread())) {
                result.setBondExcessSpread(x.getBondExcessSpread()
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 获取城投利差分析计算中位数后的数据
     *
     * @param startDate                   利差开始日期
     * @param endDate                     利差结束日期
     * @param bondExtRatingMapping        债券外部评级映射
     * @param bondImpliedRatingMappingTag 债项隐含评级映射
     * @param comYyRatingMappingTag       yy评级
     * @param spreadBondType              债券类型
     * @param spreadRemainingTenorTag     剩余期限
     * @param guaranteeStatus             担保状态
     * @param administrativeDivision      行政区划
     * @param bondImpliedRatingMappings   隐含
     * @param comYyRatingMappings         yy
     * @return {@link List}<{@link UdicSpreadPanoramaBO}> 城投利差分析计算中位数后的数据集
     */
    public List<UdicSpreadPanoramaBO> listUdicBondYieldInduSpreadsForProvince(Date startDate, Date endDate, Integer bondExtRatingMapping,
                                                                              Integer bondImpliedRatingMappingTag,
                                                                              Integer comYyRatingMappingTag, Integer spreadBondType,
                                                                              Integer spreadRemainingTenorTag, Integer guaranteeStatus,
                                                                              Integer administrativeDivision, Integer[] bondImpliedRatingMappings, Integer[] comYyRatingMappings) {
        GroupByQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> and = GroupByQuery.createQuery(PgUdicBondYieldSpreadDO.class, PgUdicBondYieldSpreadGroupDO.class)
                .select(PgUdicBondYieldSpreadGroupDO::getSpreadDate, PgUdicBondYieldSpreadGroupDO::getProvinceUniCode,
                        PgUdicBondYieldSpreadGroupDO::getBondCreditSpread, PgUdicBondYieldSpreadGroupDO::getBondExcessSpread,
                        PgUdicBondYieldSpreadGroupDO::getBondCreditSpreadCount, PgUdicBondYieldSpreadGroupDO::getBondExcessSpreadCount)
                .and(PgUdicBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startDate))
                .and(PgUdicBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(endDate))
                .and(Objects.nonNull(bondExtRatingMapping), PgUdicBondYieldSpreadDO::getBondExtRatingMapping, isEqual(bondExtRatingMapping))
                .and(Objects.nonNull(bondImpliedRatingMappingTag), PgUdicBondYieldSpreadDO::getBondImpliedRatingMappingTag, isEqual(bondImpliedRatingMappingTag))
                .and(Objects.nonNull(comYyRatingMappingTag), PgUdicBondYieldSpreadDO::getComYyRatingMappingTag, isEqual(comYyRatingMappingTag))
                .and(Objects.nonNull(spreadBondType), PgUdicBondYieldSpreadDO::getSpreadBondType, isEqual(spreadBondType))
                .and(Objects.nonNull(spreadRemainingTenorTag), PgUdicBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(spreadRemainingTenorTag))
                .and(Objects.nonNull(guaranteeStatus), PgUdicBondYieldSpreadDO::getGuaranteedStatus, isEqual(guaranteeStatus))
                .and(Objects.nonNull(administrativeDivision), PgUdicBondYieldSpreadDO::getAdministrativeDivision, isEqual(administrativeDivision))
                .and(ArrayUtils.isNotEmpty(bondImpliedRatingMappings), PgUdicBondYieldSpreadDO::getBondImpliedRatingMapping, in(bondImpliedRatingMappings))
                .and(ArrayUtils.isNotEmpty(comYyRatingMappings), PgUdicBondYieldSpreadDO::getComYyRatingMapping, in(comYyRatingMappings))
                .and(PgUdicBondYieldSpreadDO::getProvinceUniCode, notEqual(null));
        GroupedQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> groupedQuery =
                and.groupBy(PgUdicBondYieldSpreadDO::getSpreadDate, PgUdicBondYieldSpreadDO::getProvinceUniCode)
                        .orderBy(PgUdicBondYieldSpreadGroupDO::getSpreadDate, desc());
        groupedQuery.and(PgUdicBondYieldSpreadGroupDO::getSpreadCount, greaterThanOrEqual(MIN_BOND_SIZE));
        List<PgUdicBondYieldSpreadGroupDO> pgUdicBondYieldSpreadGroupDOList = pgUdicBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadGroupDOList)) {
            return Collections.emptyList();
        }
        return pgUdicBondYieldSpreadGroupDOList.stream().map(x -> {
            UdicSpreadPanoramaBO result = BeanCopyUtils.copyProperties(x, UdicSpreadPanoramaBO.class);
            ObjectExtensionUtils.ifNonNull(x.getBondCreditSpread(), creditSpread ->
                    result.setBondCreditSpread(creditSpread.setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP)));
            ObjectExtensionUtils.ifNonNull(x.getBondExcessSpread(), excessSpread ->
                    result.setBondExcessSpread(excessSpread.setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP)));
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 获取城投利差分析计算中位数后的数据
     *
     * @param startDate                   利差开始日期
     * @param endDate                     利差结束日期
     * @param bondExtRatingMapping        债券外部评级映射
     * @param bondImpliedRatingMappingTag 债项隐含评级映射
     * @param comYyRatingMappingTag       yy评级
     * @param spreadBondType              债券类型
     * @param spreadRemainingTenorTag     剩余期限
     * @param guaranteeStatus             担保状态
     * @param administrativeDivision      行政区划
     * @return {@link List}<{@link UdicSpreadPanoramaBO}> 城投利差分析计算中位数后的数据集
     */
    public List<UdicSpreadPanoramaBO> listUdicBondYieldInduSpreadsForCity(Date startDate, Date endDate, Integer bondExtRatingMapping,
                                                                          Integer bondImpliedRatingMappingTag,
                                                                          Integer comYyRatingMappingTag, Integer spreadBondType,
                                                                          Integer spreadRemainingTenorTag, Integer guaranteeStatus,
                                                                          Integer administrativeDivision) {
        GroupByQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> and = GroupByQuery.createQuery(PgUdicBondYieldSpreadDO.class, PgUdicBondYieldSpreadGroupDO.class)
                .select(PgUdicBondYieldSpreadGroupDO::getSpreadDate, PgUdicBondYieldSpreadGroupDO::getProvinceUniCode, PgUdicBondYieldSpreadGroupDO::getCityUniCode,
                        PgUdicBondYieldSpreadGroupDO::getBondCreditSpread, PgUdicBondYieldSpreadGroupDO::getBondExcessSpread,
                        PgUdicBondYieldSpreadGroupDO::getBondCreditSpreadCount, PgUdicBondYieldSpreadGroupDO::getBondExcessSpreadCount)
                .and(PgUdicBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startDate))
                .and(PgUdicBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(endDate))
                .and(Objects.nonNull(bondExtRatingMapping), PgUdicBondYieldSpreadDO::getBondExtRatingMapping, isEqual(bondExtRatingMapping))
                .and(Objects.nonNull(bondImpliedRatingMappingTag), PgUdicBondYieldSpreadDO::getBondImpliedRatingMappingTag, isEqual(bondImpliedRatingMappingTag))
                .and(Objects.nonNull(comYyRatingMappingTag), PgUdicBondYieldSpreadDO::getComYyRatingMappingTag, isEqual(comYyRatingMappingTag))
                .and(Objects.nonNull(spreadBondType), PgUdicBondYieldSpreadDO::getSpreadBondType, isEqual(spreadBondType))
                .and(Objects.nonNull(spreadRemainingTenorTag), PgUdicBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(spreadRemainingTenorTag))
                .and(Objects.nonNull(guaranteeStatus), PgUdicBondYieldSpreadDO::getGuaranteedStatus, isEqual(guaranteeStatus))
                .and(Objects.nonNull(administrativeDivision), PgUdicBondYieldSpreadDO::getAdministrativeDivision, isEqual(administrativeDivision))
                .and(PgUdicBondYieldSpreadDO::getProvinceUniCode, notEqual(null))
                .and(PgUdicBondYieldSpreadDO::getCityUniCode, notEqual(null));
        GroupedQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> groupedQuery =
                and.groupBy(PgUdicBondYieldSpreadDO::getCityUniCode, PgUdicBondYieldSpreadDO::getProvinceUniCode, PgUdicBondYieldSpreadDO::getSpreadDate);
        groupedQuery.and(PgUdicBondYieldSpreadGroupDO::getSpreadCount, greaterThanOrEqual(MIN_BOND_SIZE))
                .orderBy(PgUdicBondYieldSpreadGroupDO::getSpreadDate, desc());
        List<PgUdicBondYieldSpreadGroupDO> pgUdicBondYieldSpreadGroupDOList = pgUdicBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadGroupDOList)) {
            return Collections.emptyList();
        }
        return pgUdicBondYieldSpreadGroupDOList.stream().map(x -> {
            UdicSpreadPanoramaBO result = BeanCopyUtils.copyProperties(x, UdicSpreadPanoramaBO.class);
            ObjectExtensionUtils.ifNonNull(x.getBondCreditSpread(), creditSpread ->
                    result.setBondCreditSpread(creditSpread.setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP)));
            ObjectExtensionUtils.ifNonNull(x.getBondExcessSpread(), excessSpread ->
                    result.setBondExcessSpread(excessSpread.setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP)));
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 计算中位数
     *
     * @param comUniCodes    发行人代码
     * @param spreadBondType 利差债券类型
     * @param spreadDate     利差日期
     * @return key 发行人代码,城投债利差
     */
    @DynamicTableNameParam(logicTableName = YieldSpreadConst.UDIC_TABLE_NAME)
    public Map<Long, PgUdicBondYieldSpreadGroupDO> getUdicBondYieldSpreadMap(Set<Long> comUniCodes, Integer spreadBondType,
                                                                             Date spreadDate) {
        GroupedQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgUdicBondYieldSpreadDO.class, PgUdicBondYieldSpreadGroupDO.class)
                        .select(PgUdicBondYieldSpreadGroupDO::getComUniCode, PgUdicBondYieldSpreadGroupDO::getCbYield,
                                PgUdicBondYieldSpreadGroupDO::getBondExcessSpread, PgUdicBondYieldSpreadGroupDO::getBondCreditSpread)
                        .and(PgUdicBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(PgUdicBondYieldSpreadDO::getComUniCode, in(comUniCodes))
                        .and(Objects.nonNull(spreadBondType), PgUdicBondYieldSpreadDO::getSpreadBondType, isEqual(spreadBondType))
                        .groupBy(PgUdicBondYieldSpreadDO::getComUniCode);
        List<PgUdicBondYieldSpreadGroupDO> pgUdicBondYieldSpreadGroupDs = pgUdicBondYieldSpreadGroupMapper.
                selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadGroupDs)) {
            return Collections.emptyMap();
        }
        return pgUdicBondYieldSpreadGroupDs.stream()
                .filter(Objects::nonNull)
                .map(x -> {
                    PgUdicBondYieldSpreadGroupDO result = BeanCopyUtils.copyProperties(x, PgUdicBondYieldSpreadGroupDO.class);
                    if (Objects.nonNull(x.getCbYield())) {
                        result.setCbYield(x.getCbYield().setScale(YIELD_SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondCreditSpread())) {
                        result.setBondCreditSpread(x.getBondCreditSpread()
                                .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondExcessSpread())) {
                        result.setBondExcessSpread(x.getBondExcessSpread()
                                .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                    }
                    return result;
                }).collect(Collectors.toMap(PgUdicBondYieldSpreadGroupDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 批量更新
     *
     * @param pgUdicBondYieldSpreadDOList 城投债利差列表
     * @param spreadDate                  利差日期
     * @return 受影响的行数
     */
    @DynamicTableNameParam(logicTableName = YieldSpreadConst.UDIC_TABLE_NAME)
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int savePgUdicBondYieldSpreadDOList(Date spreadDate, List<PgUdicBondYieldSpreadDO> pgUdicBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> bondUniCodes = pgUdicBondYieldSpreadDOList.stream().map(PgUdicBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<PgUdicBondYieldSpreadDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadDO.class)
                .select(PgUdicBondYieldSpreadDO::getId, PgUdicBondYieldSpreadDO::getBondUniCode, PgUdicBondYieldSpreadDO::getSpreadDate)
                .and(PgUdicBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(PgUdicBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<PgUdicBondYieldSpreadDO> existDataList = pgUdicBondYieldSpreadMapper.selectByDynamicQuery(query);
        Map<String, PgUdicBondYieldSpreadDO> existPgUdicBondYieldSpreadDO = existDataList.stream().collect(Collectors.toMap(x ->
                        String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                Function.identity(), (x1, x2) -> x2));
        MapperBatchAction<PgUdicBondYieldSpreadMapper> batchAction = MapperBatchAction.
                create(PgUdicBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgUdicBondYieldSpreadDO pgUdicBondYieldSpreadDO : pgUdicBondYieldSpreadDOList) {
            PgUdicBondYieldSpreadDO existData = existPgUdicBondYieldSpreadDO.get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                    pgUdicBondYieldSpreadDO.getBondUniCode(),
                    pgUdicBondYieldSpreadDO.getSpreadDate().getTime()));
            batchAction.addAction(mapper -> {
                if (Objects.nonNull(existData)) {
                    pgUdicBondYieldSpreadDO.setId(existData.getId());
                    mapper.updateByPrimaryKey(pgUdicBondYieldSpreadDO);
                } else {
                    mapper.insert(pgUdicBondYieldSpreadDO);
                }
            });
        }
        List<BatchResult> batchResults = batchAction.doBatchActionWithResults();
        return batchResults.stream().mapToInt(x -> {
            int effectRows = Arrays.stream(x.getUpdateCounts()).sum();
            if (effectRows > 0) {
                return effectRows;
            } else {
                return x.getParameterObjects().size();
            }
        }).sum();
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgUdicBondYieldSpreadDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadDO.class);
        Optional<java.util.Date> maxDateOpt =
                pgUdicBondYieldSpreadMapper.selectMaxByDynamicQuery(PgUdicBondYieldSpreadDO::getSpreadDate, query);
        return maxDateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 获取所有利差日期
     *
     * @return {@link List}<{@link Date}> 利差日期集合
     */
    public List<Date> listAllSpreadDates() {
        DynamicQuery<PgUdicBondYieldSpreadDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadDO.class)
                .selectDistinct(PgUdicBondYieldSpreadDO::getSpreadDate);
        List<PgUdicBondYieldSpreadDO> spreadList = pgUdicBondYieldSpreadMapper.selectByDynamicQuery(query);
        return spreadList.stream().map(PgUdicBondYieldSpreadDO::getSpreadDate).collect(Collectors.toList());
    }

    /**
     * 查询并计算利差曲线数据-组合条件方式
     *
     * @param searchParameter 查询参数
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 查询利差曲线分组响应数据
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurvesByMultiCondition(UdicBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadDO>[] baseFilterDescriptors = this.listCommonFilters(searchParameter);
        GroupByQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> query = GroupByQuery.createQuery(PgUdicBondYieldSpreadDO.class, PgUdicBondYieldSpreadGroupDO.class)
                .select(PgUdicBondYieldSpreadGroupDO::getSpreadDate, PgUdicBondYieldSpreadGroupDO::getBondCreditSpread, PgUdicBondYieldSpreadGroupDO::getBondCreditSpreadCount,
                        PgUdicBondYieldSpreadGroupDO::getBondExcessSpread, PgUdicBondYieldSpreadGroupDO::getBondExcessSpreadCount,
                        PgUdicBondYieldSpreadGroupDO::getCbYield, PgUdicBondYieldSpreadGroupDO::getCbYieldCount,
                        PgUdicBondYieldSpreadGroupDO::getCdbLerpYield, PgUdicBondYieldSpreadGroupDO::getCdbLerpYieldCount).and(baseFilterDescriptors);
        GroupedQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> groupQuery = query.groupBy(PgUdicBondYieldSpreadDO::getSpreadDate);
        groupQuery.and(PgUdicBondYieldSpreadGroupDO::getSpreadCount, greaterThanOrEqual(MIN_BOND_SIZE));
        List<PgUdicBondYieldSpreadGroupDO> pgUdicBondYieldSpreadGroupList = pgUdicBondYieldSpreadGroupMapper.selectByGroupedQuery(groupQuery);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadGroupList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> copyList = Lists.newArrayListWithExpectedSize(pgUdicBondYieldSpreadGroupList.size());
        for (PgUdicBondYieldSpreadGroupDO pgUdicBondYieldSpreadGroupDO : pgUdicBondYieldSpreadGroupList) {
            BondYieldSpreadCurveBO clone = BeanCopyUtils.copyProperties(pgUdicBondYieldSpreadGroupDO, BondYieldSpreadCurveBO.class);
            BigDecimalUtils.handlerPrecision(clone.getBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondCreditSpread);
            BigDecimalUtils.handlerPrecision(clone.getBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondExcessSpread);
            BigDecimalUtils.handlerPrecision(clone.getCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCbYield);
            BigDecimalUtils.handlerPrecision(clone.getCdbLerpYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCdbLerpYield);
            copyList.add(clone);
        }
        return copyList;
    }

    /**
     * 查询并计算利差曲线数据-单券利差方式
     *
     * @param bondUniCode     债券唯一编码
     * @param startSpreadDate 利差开始日期
     * @param endSpreadDate   利差结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 债券每天中位数数据集合
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurvesByBond(Long bondUniCode, Date startSpreadDate, Date endSpreadDate) {
        if (Objects.isNull(bondUniCode)) {
            return Collections.emptyList();
        }
        DynamicQuery<PgUdicBondYieldSpreadDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadDO.class)
                .select(PgUdicBondYieldSpreadDO::getSpreadDate, PgUdicBondYieldSpreadDO::getBondCreditSpread,
                        PgUdicBondYieldSpreadDO::getBondExcessSpread, PgUdicBondYieldSpreadDO::getCbYield)
                .and(PgUdicBondYieldSpreadDO::getBondUniCode, isEqual(bondUniCode))
                .and(nonNull(startSpreadDate), PgUdicBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startSpreadDate))
                .and(nonNull(endSpreadDate), PgUdicBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(endSpreadDate))
                .orderBy(PgUdicBondYieldSpreadDO::getSpreadDate, SortDirections::asc);
        List<PgUdicBondYieldSpreadDO> pgUdicBondYieldSpreadList = pgUdicBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> copyList = Lists.newArrayListWithExpectedSize(pgUdicBondYieldSpreadList.size());
        for (PgUdicBondYieldSpreadDO pgUdicBondYieldSpreadDO : pgUdicBondYieldSpreadList) {
            BondYieldSpreadCurveBO clone = BeanCopyUtils.copyProperties(pgUdicBondYieldSpreadDO, BondYieldSpreadCurveBO.class);
            BigDecimalUtils.handlerPrecision(clone.getBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondCreditSpread);
            BigDecimalUtils.handlerPrecision(clone.getBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondExcessSpread);
            BigDecimalUtils.handlerPrecision(clone.getCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCbYield);
            BigDecimalUtils.handlerPrecision(clone.getCdbLerpYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCdbLerpYield);
            copyList.add(clone);
        }
        return copyList;
    }

    private BaseFilterDescriptor<PgUdicBondYieldSpreadDO>[] listCommonFilters(UdicBondYieldSpreadParamDTO request) {
        FilterGroupDescriptor<PgUdicBondYieldSpreadDO> filterGroup = FilterGroupDescriptor.create(PgUdicBondYieldSpreadDO.class)
                .and(nonNull(request.getProvinceUniCode()), PgUdicBondYieldSpreadDO::getProvinceUniCode, isEqual(request.getProvinceUniCode()))
                .and(nonNull(request.getCityUniCode()), PgUdicBondYieldSpreadDO::getCityUniCode, isEqual(request.getCityUniCode()))
                .and(nonNull(request.getSpreadBondType()), PgUdicBondYieldSpreadDO::getSpreadBondType, isEqual(request.getSpreadBondType()))
                .and(nonNull(request.getBondExtRatingMapping()), PgUdicBondYieldSpreadDO::getBondExtRatingMapping, isEqual(request.getBondExtRatingMapping()))
                .and(nonNull(request.getBondImpliedRatingMappingTag()), PgUdicBondYieldSpreadDO::getBondImpliedRatingMappingTag, isEqual(request.getBondImpliedRatingMappingTag()))
                .and(nonNull(request.getComYyRatingMappingTag()), PgUdicBondYieldSpreadDO::getComYyRatingMappingTag, isEqual(request.getComYyRatingMappingTag()))
                .and(nonNull(request.getAdministrativeDivision()), PgUdicBondYieldSpreadDO::getAdministrativeDivision, isEqual(request.getAdministrativeDivision()))
                .and(nonNull(request.getSpreadRemainingTenorTag()), PgUdicBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(request.getSpreadRemainingTenorTag()))
                .and(nonNull(request.getGuaranteeStatus()), PgUdicBondYieldSpreadDO::getGuaranteedStatus, isEqual(request.getGuaranteeStatus()))
                .and(nonNull(request.getComUniCode()), PgUdicBondYieldSpreadDO::getComUniCode, isEqual(request.getComUniCode()))
                .and(nonNull(request.getBondUniCode()), PgUdicBondYieldSpreadDO::getBondUniCode, isEqual(request.getBondUniCode()))
                .and(isNotEmpty(request.getComUniCodes()), PgUdicBondYieldSpreadDO::getComUniCode, in(request.getComUniCodes()))
                .and(isNotEmpty(request.getBondUniCodes()), PgUdicBondYieldSpreadDO::getBondUniCode, in(request.getBondUniCodes()))
                .and(nonNull(request.getStartSpreadDate()), PgUdicBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(request.getStartSpreadDate()))
                .and(nonNull(request.getEndSpreadDate()), PgUdicBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(request.getEndSpreadDate()));
        return filterGroup.getFilters();
    }

    /**
     * 查询城投利差
     *
     * @param params 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listBondYieldSpreads(UdicYieldSearchParam params) {
        GroupedQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgUdicBondYieldSpreadDO.class, PgUdicBondYieldSpreadGroupDO.class)
                        .select(PgUdicBondYieldSpreadGroupDO::getSpreadDate,
                                PgUdicBondYieldSpreadGroupDO::getBondCreditSpread,
                                PgUdicBondYieldSpreadGroupDO::getBondExcessSpread,
                                PgUdicBondYieldSpreadGroupDO::getCbYield,
                                PgUdicBondYieldSpreadGroupDO::getAvgBondCreditSpread,
                                PgUdicBondYieldSpreadGroupDO::getAvgBondExcessSpread,
                                PgUdicBondYieldSpreadGroupDO::getAvgCbYield,
                                PgUdicBondYieldSpreadGroupDO::getBondCreditSpreadCount,
                                PgUdicBondYieldSpreadGroupDO::getBondExcessSpreadCount,
                                PgUdicBondYieldSpreadGroupDO::getCbYieldCount)
                        .and(Objects.nonNull(params.getSpreadBondType()), PgUdicBondYieldSpreadDO::getSpreadBondType, isEqual(params.getSpreadBondType()))
                        .and(Objects.nonNull(params.getProvinceUniCode()), PgUdicBondYieldSpreadDO::getProvinceUniCode, isEqual(params.getProvinceUniCode()))
                        .and(Objects.nonNull(params.getCityUniCode()), PgUdicBondYieldSpreadDO::getCityUniCode, isEqual(params.getCityUniCode()))
                        .and(Objects.nonNull(params.getDistrictUniCode()), PgUdicBondYieldSpreadDO::getDistrictUniCode, isEqual(params.getDistrictUniCode()))
                        .and(Objects.nonNull(params.getAdministrativeDivision()), PgUdicBondYieldSpreadDO::getAdministrativeDivision, isEqual(params.getAdministrativeDivision()))
                        .and(Objects.nonNull(params.getGuaranteedStatus()), PgUdicBondYieldSpreadDO::getGuaranteedStatus, isEqual(params.getGuaranteedStatus()))
                        .and(Objects.nonNull(params.getBondExtRatingMapping()), PgUdicBondYieldSpreadDO::getBondExtRatingMapping, isEqual(params.getBondExtRatingMapping()))
                        .and(Objects.nonNull(params.getRemainingTenor()), PgUdicBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(params.getRemainingTenor()))
                        .and(ArrayUtils.isNotEmpty(params.getBondImpliedRatingMappings()),
                                PgUdicBondYieldSpreadDO::getBondImpliedRatingMapping, in(params.getBondImpliedRatingMappings()))
                        .and(ArrayUtils.isNotEmpty(params.getComYyRatingMappings()), PgUdicBondYieldSpreadDO::getComYyRatingMapping, in(params.getComYyRatingMappings()))
                        .and(Objects.nonNull(params.getComUniCode()), PgUdicBondYieldSpreadDO::getComUniCode, isEqual(params.getComUniCode()))
                        .and(Objects.nonNull(params.getBondUniCode()), PgUdicBondYieldSpreadDO::getBondUniCode, isEqual(params.getBondUniCode()))
                        .groupBy(PgUdicBondYieldSpreadDO::getSpreadDate)
                        .orderBy(PgUdicBondYieldSpreadGroupDO::getSpreadDate, asc());
        List<PgUdicBondYieldSpreadGroupDO> bondYieldSpreads = pgUdicBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(bondYieldSpreads)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(bondYieldSpreads, BondYieldSpreadBO.class);
    }

    /**
     * 根据利差日期 获取 区域利差数据
     *
     * @param spreadDate 利差日期
     * @return 区域利差数据
     */
    @DynamicTableNameParam(logicTableName = YieldSpreadConst.UDIC_TABLE_NAME)
    public List<PgUdicAreaYieldSpreadBO> listAreaBondYieldSpreads(@NotNull Date spreadDate) {
        return pgUdicBondYieldSpreadMapper.listAreaBondYieldSpreads(spreadDate);
    }

}
