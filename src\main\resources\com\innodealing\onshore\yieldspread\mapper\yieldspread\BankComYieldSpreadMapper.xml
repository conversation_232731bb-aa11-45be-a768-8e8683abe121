<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.yieldspread.BankComYieldSpreadMapper">
    <select id="listComYieldSpreads"
            resultType="com.innodealing.onshore.yieldspread.model.bo.BankComYieldSpreadBO">
        SELECT
        <if test="isNewest">
            com_change.credit_spread_change_3m,
            com_change.credit_spread_change_6m,
            com_change.excess_spread_change_3m,
            com_change.excess_spread_change_6m,
            com_change.credit_spread_quantile_3y,
            com_change.credit_spread_quantile_5y,
            com_change.excess_spread_quantile_3y,
            com_change.excess_spread_quantile_5y,
        </if>
        bank.com_uni_code,
        bank.bank_type ,
        bank.spread_date,
        bank.npl_ratio,
        bank.leverage_ratio,
        bank.car,
        bank.loan_provis_ratio,
        bank.com_tier2_credit_spread,
        bank.com_tier2_excess_spread,
        bank.com_tier2_cb_yield,
        bank.com_ext_rating_mapping,
        bank.indu_level2_name,
        bank.total_assets,
        bank.net_profit,
        bank.com_senior_credit_spread,
        bank.com_senior_excess_spread,
        bank.com_credit_spread,
        bank.com_perpetual_credit_spread,
        bank.com_excess_spread,
        bank.com_perpetual_excess_spread,
        bank.com_senior_cb_yield,
        bank.com_cb_yield,
        bank.com_perpetual_cb_yield
        FROM bank_com_yield_spread bank
        INNER JOIN (
        select distinct com_uni_code from bank_bond_yield_spread_${params.year}
        where spread_date = #{params.spreadDate}
        <if test="params.spreadBondType != null">
            and bank_seniority_ranking = #{params.spreadBondType}
        </if>
        <if test="params.bankTypes != null and params.bankTypes.size() > 0">
            and bank_type in
            <foreach collection="params.bankTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.remainingTenor != null">
            and spread_remaining_tenor_tag = #{params.remainingTenor}
        </if>
        <if test="params.bondImpliedRatingMappings != null and params.bondImpliedRatingMappings.length >0">
            and bond_implied_rating_mapping in
            <foreach collection="params.bondImpliedRatingMappings" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.comUniCode != null">
            and com_uni_code = #{params.comUniCode}
        </if>
        <if test="params.bondUniCode != null">
            and bond_uni_code = #{params.bondUniCode}
        </if>
        ) tmp
        on bank.com_uni_code = tmp.com_uni_code
        <if test="isNewest">
            LEFT JOIN com_yield_spread_change com_change ON bank.com_uni_code =com_change.com_uni_code
            and bank.spread_date = com_change.spread_date
            AND com_change.deleted = 0
            and com_change.com_spread_sector = 4
        </if>
        WHERE bank.spread_date = #{params.spreadDate} AND bank.deleted = 0
        <if test="params.sort != null">
            order by ${params.sort.propertyName} ${params.sort.sortDirection}
        </if>
        limit #{params.startIndex},#{params.pageSize}
    </select>

    <select id="countComYieldSpread" resultType="long">
        SELECT COUNT(*)
        FROM bank_com_yield_spread
        INNER JOIN (
        select distinct com_uni_code from bank_bond_yield_spread_${params.year}
        where spread_date = #{params.spreadDate}
        <if test="params.spreadBondType != null">
            and bank_seniority_ranking = #{params.spreadBondType}
        </if>
        <if test="params.bankTypes != null and params.bankTypes.size() > 0">
            and bank_type in
            <foreach collection="params.bankTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.remainingTenor != null">
            and spread_remaining_tenor_tag = #{params.remainingTenor}
        </if>
        <if test="params.bondImpliedRatingMappings != null and params.bondImpliedRatingMappings.length >0">
            and bond_implied_rating_mapping in
            <foreach collection="params.bondImpliedRatingMappings" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.comUniCode != null">
            and com_uni_code = #{params.comUniCode}
        </if>
        <if test="params.bondUniCode != null">
            and bond_uni_code = #{params.bondUniCode}
        </if>
        ) tmp
        on bank_com_yield_spread.com_uni_code = tmp.com_uni_code
        WHERE (bank_com_yield_spread.spread_date = #{params.spreadDate} AND bank_com_yield_spread.deleted = 0)
    </select>
    <select id="listComYieldQuantileStatisticsViews"
            resultType="com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO">
        SELECT
        t1.com_uni_code,
        CASE WHEN t2.com_credit_spread is not null THEN COUNT(t1.com_credit_spread &lt; t2.com_credit_spread or null)
        ELSE NULL END comCreditSpreadLessIssueCount,
        CASE WHEN t2.com_credit_spread is not null THEN COUNT(t1.com_credit_spread) ELSE NULL END comCreditSpreadCount,
        CASE WHEN t2.com_excess_spread is not null THEN COUNT(t1.com_excess_spread &lt; t2.com_excess_spread or null)
        ELSE NULL END comExcessSpreadLessIssueCount,
        CASE WHEN t2.com_excess_spread is not null THEN COUNT(t1.com_excess_spread) ELSE NULL END comExcessSpreadCount
        FROM
        bank_com_yield_spread AS t1
        INNER JOIN bank_com_yield_spread t2 ON t1.com_uni_code = t2.com_uni_code
        WHERE
        t1.spread_date >= #{startDate}
        AND t1.spread_date &lt;= #{endDate}
        AND t1.deleted = 0
        AND t2.spread_date = #{issueDate}
        AND t2.deleted = 0
        <if test="comUniCodeList != null and comUniCodeList.size >0">
            AND t2.com_uni_code IN
            <foreach collection="comUniCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        t1.com_uni_code
    </select>
</mapper>