package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.mv.MvInduShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvInduBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvInduBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.YyRatingRouter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 行业利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvInduBondYieldSpreadCurveDAO {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private MvInduBondYieldSpreadCurveMapper mvInduBondYieldSpreadCurveMapper;
    @Resource
    private MvInduShardBondYieldSpreadCurveMapper mvShardingInduBondYieldSpreadCurveMapper;

    /**
     * 刷新物化视图
     */
    public void refreshMvInduBondYieldSpreadCurve() {
        mvInduBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurveAll();
        mvInduBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurveIndu1();
        mvInduBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurveIndu2();
    }

    /**
     * 刷新昨日的曲线物化视图
     */
    public void refreshCurveYesterday() {
        mvInduBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurveAllYesterday();
        mvInduBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurveIndu1Yesterday();
        mvInduBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurveIndu2Yesterday();
    }

    /**
     * 创建或刷新产业利差曲线物化视图
     * @param router 路由
     * @param param 参数
     */
    public void createOrRefreshInduCurveMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        MvInduBondYieldSpreadCurveParameter parameter = this.builderShardMvParam(router);
        logger.info("[refreshMvInduBondYieldSpreadBondCurveRouter]开始创建产业评级分片物化视图:{}", parameter.getTableName());
        parameter.setTableName(parameter.getTableName());
        if (param.isMvRefresh()) {
            mvInduBondYieldSpreadCurveMapper.dropMv(parameter.getTableName());
            mvInduBondYieldSpreadCurveMapper.createMvRatingRouter(parameter);
        } else {
            mvInduBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurve(parameter.getTableName());
        }
    }


    /**
     * 时间范围不为空，删除临时表
     *
     * @param router 路由
     */
    public void droTempMv(AbstractRatingRouter router) {
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (Objects.nonNull(spreadDateRange)) {
            mvInduBondYieldSpreadCurveMapper.dropMv(this.getMvName(router));
        }
    }

    private MvInduBondYieldSpreadCurveParameter builderShardMvParam(AbstractRatingRouter router) {
        MvInduBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, MvInduBondYieldSpreadCurveParameter.class);
        parameter.setTableName(this.getMvName(router));
        parameter.setInduLevel(router.getLevel());
        if (router instanceof ImplicitRatingRouter) {
            ImplicitRatingRouter implicitRatingRouter = (ImplicitRatingRouter) router;
            parameter.setImpliedRatingMappings(implicitRatingRouter.getRatings());
        }
        if (router instanceof YyRatingRouter) {
            YyRatingRouter yyRatingRouter = (YyRatingRouter) router;
            parameter.setYyRatingMappings(yyRatingRouter.getRatings());
        }
        return parameter;
    }

    /**
     * 获取行业利差曲线物化视图名称
     * @param router 路由
     * @return mvName
     */
    public String getMvName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()),
                mvShardingInduBondYieldSpreadCurveMapper.getLogicTable(), RatingCombinationHelper.getMvNameSuffix(router));
    }


}
