package com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis;

import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvInduComYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.LIST_COM_INDU_SPREAD_CURVES_KEY;

/**
 * 产业单券利差缓存DAO
 *
 * <AUTHOR>
 */
@Repository
public class InduComYieldSpreadRedisDAO extends AbstractComYieldSpreadCache {

    @Resource
    private MvInduComYieldSpreadCurveDAO mvInduComYieldSpreadCurveDAO;
    @Resource
    private HolidayService holidayService;

    /**
     * 构造函数
     *
     * @param stringRedisTemplate 操作redis对象
     */
    public InduComYieldSpreadRedisDAO(StringRedisTemplate stringRedisTemplate) {
        super(stringRedisTemplate);
    }

    @Override
    protected String cacheKey(@NonNull Long comUniCode, Integer spreadBondType) {
        return String.format(LIST_COM_INDU_SPREAD_CURVES_KEY, comUniCode, spreadBondType);
    }

    @Override
    protected List<ComYieldSpreadCurveBO> fromDB(@NonNull Long comUniCode, Integer spreadBondType) {
        return mvInduComYieldSpreadCurveDAO.list(comUniCode, spreadBondType);
    }


    @Override
    protected long expiredTime() {
        // 4-8点的时候缓存时间为10分钟一次，防止这个时间点增量数据进来以后，没有缓存进去
        int hour = LocalDateTime.now().getHour();
        if (hour >= EARLY_EXPIRED_TIME_START_HOUR && hour <= EARLY_EXPIRED_TIME_END_HOUR) {
            return EARLY_EXPIRED_TIME;
        }
        LocalDateTime midnight = LocalDateTime.of(holidayService.getNextWorkDay(Date.valueOf(LocalDate.now())).toLocalDate(),
                LocalTime.now().withHour(FOUR_HOURS).withMinute(ZERO_MINUTE).withSecond(ZERO_SECOND).withNano(ZERO_NANO));
        return ChronoUnit.MILLIS.between(LocalDateTime.now(), midnight);
    }

}