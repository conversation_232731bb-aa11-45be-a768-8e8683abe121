package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.BondCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.BondYieldSpreadDTO;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.model.dto.request.BondYieldSpreadListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondCreditYieldCurvesResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondYieldSpreadCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondYieldSpreadListResponseDTO;
import com.innodealing.onshore.yieldspread.service.BondYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * (内部)单券利差
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 单券利差")
@RestController
@Validated
@RequestMapping("internal/bond-yield-spread")
public class InternalBondYieldSpreadController {

    @Resource
    private BondYieldSpreadService bondYieldSpreadService;

    @ApiOperation(value = "查询单券利差信用利差(全部)数据")
    @PostMapping("/list/bond-credit-spreads")
    public List<BondCreditSpreadDTO> listBondCreditSpreads(
            @ApiParam(name = "spreadDate", value = "利差日期") @RequestParam Date spreadDate,
            @ApiParam(name = "bondUniCodes", value = "单券唯一代码集合")
            @RequestBody Set<Long> bondUniCodes) {
        return bondYieldSpreadService.listBondCreditSpreads(spreadDate, bondUniCodes);
    }

    @ApiOperation(value = "曲线")
    @GetMapping(value = "/curves")
    public List<BondYieldSpreadCurveResponseDTO> listCurves(
            @ApiParam(value = "债券唯一编码", name = "bondUniCode", required = true)
            @RequestParam Long bondUniCode,
            @ApiParam(value = "曲线类型: 1:产业，2:城投，3:银行，4:证券，5:自定义", name = "curveType", required = true)
            @RequestParam Integer curveType,
            @ApiParam(value = "开始日期", name = "startDate", required = true)
            @RequestParam Date startDate,
            @ApiParam(value = "结束日期", name = "endDate", required = true)
            @RequestParam Date endDate) {
        return bondYieldSpreadService.listCurves(bondUniCode, curveType, startDate, endDate);
    }

    @ApiOperation(value = "债券列表数据")
    @PostMapping(value = "/list-bonds")
    public BondYieldSpreadListResponseDTO listBonds(@RequestBody @Validated BondYieldSpreadListRequestDTO request,
                                                    @ApiParam(name = "userid", value = "用户编号", hidden = true)
                                                    @CookieValue("userid") Long userid) {
        return bondYieldSpreadService.listBonds(request, userid);
    }

    @ApiOperation(value = "根据日期和BondUniCode(最多支持" + YieldSpreadConst.BATCH_SEARCH_BOND_YIELD_SPREAD_UPPER_LIMIT + "只债)获取利差数据，不传日期默认是最近工作日 (超额利差暂无数据)。" +
            "这个接口提供后又不需要了，所以还未经过测试")
    @PostMapping("/batch-yield-spread")
    public List<BondYieldSpreadDTO> batchYieldSpreadByBondUniCodes(
            @ApiParam(name = "spreadDate", value = "利差日期，不传值是最近工作日") @RequestParam(required = false) Date spreadDate,
            @Size(message = "批量获取债券利差数据的债券数量不能超过 {max} 只", max = YieldSpreadConst.BATCH_SEARCH_BOND_YIELD_SPREAD_UPPER_LIMIT)
            @ApiParam(name = "bondUniCodes", value = "债券唯一代码集合,一次最多查" + YieldSpreadConst.BATCH_SEARCH_BOND_YIELD_SPREAD_UPPER_LIMIT + "只", required = true)
            @RequestBody List<Long> bondUniCodes) {
        return bondYieldSpreadService.batchYieldSpreadByBondUniCodes(spreadDate, bondUniCodes);
    }

    @ApiOperation(value = "根据BondUniCodes获取信用利差曲线数据，不传日期默认查最近一年，最多支持3年")
    @PostMapping("credit-spread/list-curve")
    public BondCreditYieldCurvesResDTO listCreditSpreadCurves(
            @ApiParam(name = "bondUniCodes", value = "债券BondUniCode", required = true)
            @NotNull(message = "查询曲线的bondUniCode不能为空") @Size(min = 1, max = 3, message = "只支持查询1-3条曲线") @RequestBody List<Long> bondUniCodes,
            @ApiParam(name = "startDate", value = "利差查询开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "利差查询结束日期") @RequestParam(required = false) Date endDate) {
        return bondYieldSpreadService.listCreditSpreadCurves(bondUniCodes, startDate, endDate);
    }

}
