package com.innodealing.onshore.yieldspread.executor;

import com.innodealing.onshore.yieldspread.config.SyncConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * 并行同步执行器
 * 支持按日期分片并行执行同步任务
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ParallelSyncExecutor {
    
    /**
     * 线程池
     */
    private final ExecutorService executorService;
    
    public ParallelSyncExecutor() {
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        this.executorService = Executors.newFixedThreadPool(corePoolSize * 2);
        log.info("初始化并行同步执行器，线程池大小: {}", corePoolSize * 2);
    }
    
    /**
     * 并行执行同步任务
     * 
     * @param config 同步配置
     * @param syncFunction 同步函数，接收日期参数，返回影响行数
     * @return 总影响行数
     */
    public int executeParallelSync(SyncConfiguration config, Function<Date, Integer> syncFunction) {
        if (!config.isParallel()) {
            return executeSerialSync(config, syncFunction);
        }
        
        List<Date> dates = generateDateList(config.getStartDate(), config.getEndDate());
        if (dates.isEmpty()) {
            log.warn("没有需要同步的日期");
            return 0;
        }
        
        log.info("开始并行同步，日期范围: {} - {}, 总天数: {}, 并行度: {}", 
                config.getStartDate(), config.getEndDate(), dates.size(), config.getParallelism());
        
        AtomicInteger totalEffectRows = new AtomicInteger(0);
        List<List<Date>> batches = createBatches(dates, config.getBatchSize());
        
        List<CompletableFuture<Integer>> futures = new ArrayList<>();
        
        for (List<Date> batch : batches) {
            CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                int batchEffectRows = 0;
                for (Date date : batch) {
                    try {
                        int effectRows = syncFunction.apply(date);
                        batchEffectRows += effectRows;
                        log.debug("同步日期: {}, 影响行数: {}", date, effectRows);
                    } catch (Exception e) {
                        log.error("同步日期失败: {}", date, e);
                    }
                }
                return batchEffectRows;
            }, executorService);
            
            futures.add(future);
        }
        
        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );
        
        try {
            allFutures.join();
            
            // 汇总结果
            for (CompletableFuture<Integer> future : futures) {
                totalEffectRows.addAndGet(future.get());
            }
            
            log.info("并行同步完成，总影响行数: {}", totalEffectRows.get());
            
        } catch (Exception e) {
            log.error("并行同步执行失败", e);
        }
        
        return totalEffectRows.get();
    }
    
    /**
     * 串行执行同步任务
     */
    private int executeSerialSync(SyncConfiguration config, Function<Date, Integer> syncFunction) {
        List<Date> dates = generateDateList(config.getStartDate(), config.getEndDate());
        if (dates.isEmpty()) {
            return 0;
        }
        
        log.info("开始串行同步，日期范围: {} - {}, 总天数: {}", 
                config.getStartDate(), config.getEndDate(), dates.size());
        
        int totalEffectRows = 0;
        for (Date date : dates) {
            try {
                int effectRows = syncFunction.apply(date);
                totalEffectRows += effectRows;
                log.debug("同步日期: {}, 影响行数: {}", date, effectRows);
            } catch (Exception e) {
                log.error("同步日期失败: {}", date, e);
            }
        }
        
        log.info("串行同步完成，总影响行数: {}", totalEffectRows);
        return totalEffectRows;
    }
    
    /**
     * 生成日期列表
     */
    private List<Date> generateDateList(Date startDate, Date endDate) {
        List<Date> dates = new ArrayList<>();
        LocalDate start = startDate.toLocalDate();
        LocalDate end = endDate.toLocalDate();
        
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            dates.add(Date.valueOf(date));
        }
        
        return dates;
    }
    
    /**
     * 创建批次
     */
    private List<List<Date>> createBatches(List<Date> dates, int batchSize) {
        List<List<Date>> batches = new ArrayList<>();
        
        for (int i = 0; i < dates.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, dates.size());
            batches.add(dates.subList(i, endIndex));
        }
        
        return batches;
    }
    
    /**
     * 关闭线程池
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            log.info("并行同步执行器已关闭");
        }
    }
}
