package com.innodealing.onshore.yieldspread.dao.decorator;

import com.innodealing.onshore.yieldspread.controller.MaterializedViewRefreshController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 装饰器模式的DAO包装类
 * 用于控制物化视图的刷新行为
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefreshControlledDAO {
    
    @Resource
    private MaterializedViewRefreshController materializedViewRefreshController;
    
    /**
     * 执行带物化视图刷新控制的操作
     * 
     * @param viewName 物化视图名称
     * @param operation 需要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeWithRefreshControl(String viewName, RefreshControlledOperation<T> operation) {
        boolean shouldRefresh = materializedViewRefreshController.shouldRefreshMaterializedView(viewName);
        
        if (shouldRefresh) {
            log.debug("执行物化视图刷新控制操作: {}", viewName);
            T result = operation.execute(true);
            materializedViewRefreshController.markMaterializedViewRefreshed(viewName);
            return result;
        } else {
            log.debug("跳过物化视图刷新: {}", viewName);
            return operation.execute(false);
        }
    }
    
    /**
     * 批量执行带物化视图刷新控制的操作
     * 
     * @param viewNames 物化视图名称列表
     * @param operation 需要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeBatchWithRefreshControl(java.util.List<String> viewNames, BatchRefreshControlledOperation<T> operation) {
        java.util.List<String> viewsToRefresh = materializedViewRefreshController.shouldRefreshMaterializedViews(viewNames);
        
        log.debug("批量物化视图刷新控制，总数: {}, 需要刷新: {}", viewNames.size(), viewsToRefresh.size());
        
        T result = operation.execute(viewsToRefresh);
        
        // 标记已刷新的视图
        for (String viewName : viewsToRefresh) {
            materializedViewRefreshController.markMaterializedViewRefreshed(viewName);
        }
        
        return result;
    }
    
    /**
     * 强制执行物化视图刷新
     * 
     * @param viewName 物化视图名称
     * @param operation 需要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeWithForceRefresh(String viewName, RefreshControlledOperation<T> operation) {
        log.info("强制执行物化视图刷新: {}", viewName);
        T result = operation.execute(true);
        materializedViewRefreshController.markMaterializedViewRefreshed(viewName);
        return result;
    }
    
    /**
     * 跳过物化视图刷新执行操作
     * 
     * @param operation 需要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> T executeWithoutRefresh(RefreshControlledOperation<T> operation) {
        log.debug("跳过物化视图刷新执行操作");
        return operation.execute(false);
    }
    
    /**
     * 物化视图刷新控制操作接口
     */
    @FunctionalInterface
    public interface RefreshControlledOperation<T> {
        /**
         * 执行操作
         * 
         * @param shouldRefresh 是否应该刷新物化视图
         * @return 操作结果
         */
        T execute(boolean shouldRefresh);
    }
    
    /**
     * 批量物化视图刷新控制操作接口
     */
    @FunctionalInterface
    public interface BatchRefreshControlledOperation<T> {
        /**
         * 执行批量操作
         * 
         * @param viewsToRefresh 需要刷新的视图列表
         * @return 操作结果
         */
        T execute(java.util.List<String> viewsToRefresh);
    }
}
