package com.innodealing.onshore.yieldspread.controller.api;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.service.InduBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.InduComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 行业利差分析接口
 *
 * <AUTHOR>
 **/
@Api(tags = "(API)利差分析-行业")
@RestController
@RequestMapping("api/indu/yield-spread")
public class InduYieldSpreadController {

    @Resource
    private InduBondYieldSpreadService induBondYieldSpreadService;

    @Resource
    private InduComYieldSpreadService induComYieldSpreadService;

    @ApiOperation(value = "行业利差分析-全景图")
    @PostMapping("/list/panoramas")
    public RestResponse<List<InduPanoramaResponseDTO>> listPanoramas(@RequestBody InduPanoramaRequestDTO request) {
        return RestResponse.Success(induBondYieldSpreadService.listPanoramas(request));
    }

    @ApiOperation(value = "行业利差分析-行业列表")
    @GetMapping("/list/industries")
    public RestResponse<List<IndustryResponseDTO>> listIndustries() {
        return RestResponse.Success(induBondYieldSpreadService.listIndustries());
    }

    @ApiOperation(value = "行业利差分析-利差曲线")
    @PostMapping(value = "/list/curves")
    public RestResponse<List<InduCurveResponseDTO>> listCurves(@RequestBody InduCurveRequestDTO request) {
        return RestResponse.Success(induBondYieldSpreadService.listCurves(request));
    }

    @ApiOperation(value = "行业利差分析-主体利差")
    @PostMapping(value = "/paging/com-yield-spread")
    public RestResponse<NormPagingResult<InduComYieldSpreadResponseDTO>> getComYieldSpreadPaging(
            @RequestBody InduListRequestDTO request) {
        return RestResponse.Success(induComYieldSpreadService.getComYieldSpreadPaging(request));
    }

    @ApiOperation(value = "行业利差分析-查询主体利差总数")
    @PostMapping(value = "/paging/com-yield-spread/count")
    public RestResponse<Long> getComYieldSpreadPagingCount(@RequestBody InduListRequestDTO request) {
        return RestResponse.Success(induComYieldSpreadService.getComYieldSpreadPagingCount(request));
    }

    @ApiOperation(value = "行业利差分析-单券利差")
    @PostMapping(value = "/paging/bond-yield-spread")
    public RestResponse<NormPagingResult<InduBondYieldSpreadResponseDTO>> getBondYieldSpreadPaging(
            @RequestBody InduListRequestDTO request) {
        return RestResponse.Success(induBondYieldSpreadService.getBondYieldSpreadPaging(request));
    }

    @PostMapping("curve-save")
    @ApiOperation(value = "保存曲线")
    public RestResponse<Boolean> saveCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated InduCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(induBondYieldSpreadService.saveCurve(userid, curveGroupId, request));
    }

    @PostMapping("curve-update")
    @ApiOperation(value = "更新曲线")
    public RestResponse<Boolean> updateCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线名称") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Validated InduCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(induBondYieldSpreadService.updateCurve(userid, curveId, request));
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public RestResponse<NormPagingResult<InduBondYieldSpreadResponseDTO>> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(induBondYieldSpreadService.pagingSingleBondYieldSpreads(userid, request));
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差")
    public RestResponse<List<InduComYieldSpreadResponseDTO>> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(induComYieldSpreadService.listComYieldSpreads(userid, request));
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public RestResponse<Long> countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(induComYieldSpreadService.countComYieldSpread(userid, request));
    }

}
