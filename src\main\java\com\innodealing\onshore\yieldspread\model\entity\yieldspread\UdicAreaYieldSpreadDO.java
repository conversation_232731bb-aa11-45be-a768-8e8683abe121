package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * dm城投口径区域利差表实体对象
 *
 * <AUTHOR>
 */
@Table(name = "udic_area_yield_spread")
public class UdicAreaYieldSpreadDO {
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column
    private Long id;
    /**
     * 区域编码
     */
    @Column
    private Long areaUniCode;
    /**
     * 区域名称
     */
    @Column
    private String areaName;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpread;
    /**
     * 是否删除
     */
    @Column
    private Integer deleted;
    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Long getAreaUniCode() {
        return areaUniCode;
    }

    public void setAreaUniCode(Long areaUniCode) {
        this.areaUniCode = areaUniCode;
    }


    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }


    public Date getSpreadDate() {
        return java.util.Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = java.util.Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }


    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }


    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }


    public Timestamp getCreateTime() {
        return java.util.Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = java.util.Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }


    public Timestamp getUpdateTime() {
        return java.util.Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = java.util.Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }
}

