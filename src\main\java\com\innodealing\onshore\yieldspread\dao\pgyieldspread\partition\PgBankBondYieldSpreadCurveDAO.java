package com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.yieldspread.dao.pgshard.partition.BankBondShardYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvBankBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.BankShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgBankBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.BankBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.BankYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.BankShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.EmptyRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.between;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.divideHundredThousand;

/**
 * 银行利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgBankBondYieldSpreadCurveDAO {

    @Resource
    private PgBankBondYieldSpreadCurveMapper pgBankBondYieldSpreadCurveMapper;

    @Resource
    private BankShardBondYieldSpreadCurveMapper bankShardBondYieldSpreadCurveMapper;

    @Resource
    private MvBankBondYieldSpreadCurveDAO mvBankBondYieldSpreadCurveDAO;

    @Resource
    private BankBondShardYieldSpreadCurveRepository bankYieldSpreadCurveRepository;

    @Resource
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;

    /**
     * 同步物化视图到pg
     *
     * @param router 路由
     * @param param  pg创建参数
     */
    public void syncCurveShardBankForMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        BankBondYieldSpreadCurveParameter parameter = builderParameter(router);
        if (param.isTableRefresh()) {
            pgBankBondYieldSpreadCurveMapper.refreshTable(parameter.getTableName(), parameter);
        }
        DynamicQuery<BankShardBondYieldSpreadCurveDO> query = builderDynamicQuery(router);
        int count = bankShardBondYieldSpreadCurveMapper.selectCountByDynamicQueryRouter(query, router);
        if (count > 0) {
            bankShardBondYieldSpreadCurveMapper.deleteByDynamicQueryRouter(query, router);
        }
        //同步最新数据
        pgBankBondYieldSpreadCurveMapper.syncCurveIncrFromMV(parameter.getTableName(), mvBankBondYieldSpreadCurveDAO.getMvName(router));
    }

    private BankBondYieldSpreadCurveParameter builderParameter(AbstractRatingRouter router) {
        BankBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, BankBondYieldSpreadCurveParameter.class);
        parameter.setTableName(getTableName(router));
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (spreadDateRange != null) {
            parameter.setStartDate(spreadDateRange.getStartDate());
            parameter.setEndDate(spreadDateRange.getEndDate());
        }
        return parameter;
    }

    private DynamicQuery<BankShardBondYieldSpreadCurveDO> builderDynamicQuery(AbstractRatingRouter router) {
        DynamicQuery<BankShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(BankShardBondYieldSpreadCurveDO.class);
        if (Objects.nonNull(router.getSpreadDateRange()) &&
                Objects.nonNull(router.getSpreadDateRange().getStartDate()) &&
                Objects.nonNull(router.getSpreadDateRange().getEndDate())) {
            query.and(BankShardBondYieldSpreadCurveDO::getSpreadDate,
                    between(router.getSpreadDateRange().getStartDate(), router.getSpreadDateRange().getEndDate()));
        }
        return query;
    }

    private String getTableName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()), bankShardBondYieldSpreadCurveMapper.getLogicTable(),
                RatingCombinationHelper.getTableNameSuffix(router));
    }

    /**
     * 查询银行利差
     *
     * @param params 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listBankYieldSpreads(BankYieldSearchParam params) {
        List<BankShardBondYieldSpreadCurveDO> yieldSpreads = bankYieldSpreadCurveRepository
                .listBankYieldSpreads(params, this.getRatingRouter(params.getBondImpliedRatingMappings()));
        List<BondYieldSpreadBO> bondYieldSpreadBOList = BeanCopyUtils.copyList(yieldSpreads, BondYieldSpreadBO.class);
        if (CollectionUtils.isNotEmpty(bondYieldSpreadBOList)) {
            bondYieldSpreadBOList.forEach(ys -> {
                ObjectExtensionUtils.ifNonNull(ys.getBondCreditSpread(), v -> divideHundredThousand(v).ifPresent(ys::setBondCreditSpread));
                ObjectExtensionUtils.ifNonNull(ys.getBondExcessSpread(), v -> divideHundredThousand(v).ifPresent(ys::setBondExcessSpread));
                ObjectExtensionUtils.ifNonNull(ys.getCbYield(), v -> divideHundredThousand(v, YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE).ifPresent(ys::setCbYield));
                ObjectExtensionUtils.ifNonNull(ys.getAvgBondCreditSpread(), v -> divideHundredThousand(v).ifPresent(ys::setAvgBondCreditSpread));
                ObjectExtensionUtils.ifNonNull(ys.getAvgBondExcessSpread(), v -> divideHundredThousand(v).ifPresent(ys::setAvgBondExcessSpread));
                ObjectExtensionUtils.ifNonNull(ys.getAvgCbYield(), v -> divideHundredThousand(v, YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE).ifPresent(ys::setAvgCbYield));
            });
        }
        return bondYieldSpreadBOList;
    }

    private AbstractRatingRouter getRatingRouter(Integer[] bondImpliedRatingMappings) {
        return ArrayUtils.isEmpty(bondImpliedRatingMappings) ? new EmptyRouter() : implicitRatingRouterFactory.newRatingRouter(bondImpliedRatingMappings);
    }

}
