package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;
import java.util.Objects;

/**
 * 行业债利差
 *
 * <AUTHOR>
 **/
@Table(name = "mv_indu_bond_yield_spread_panorama")
public class MvInduBondYieldSpreadPanoramaDO {
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 一级行业编码
     */
    @Column
    private Long induLevel1Code;
    /**
     * 二级行业编码
     */
    @Column
    private Long induLevel2Code;
    /**
     * 信用利差;单位(BP)
     */
    @Column
    private Long bondCreditSpread;
    /**
     * 超额利差;单位(BP)
     */
    @Column
    private Long bondExcessSpread;
    /**
     * 债券外部评级映射
     */
    @Column
    private Integer bondExtRatingMapping;

    /**
     * 利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer spreadBondType;
    /**
     * 隐含评级标签
     */
    @Column
    private Integer bondImpliedRatingMappingTag;
    /**
     * yy评级标签
     */
    @Column
    private Integer comYyRatingMappingTag;
    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;
    /**
     * 担保状态: 0: 无; 1: 有
     */
    @Column
    private Integer guaranteedStatus;
    /**
     * 信用利差不为空的债券样本数量
     */
    @Column
    private Integer bondCreditSpreadCount;
    /**
     * 超额利差不为空的债券样本数量
     */
    @Column
    private Integer bondExcessSpreadCount;
    /**
     * 是否根据债券外部评级映射进行分组: 0：是，1：否
     */
    @Column
    private Integer usingBondExtRatingMapping;
    /**
     * 是否根据利差债券类别进行分组: 0：是，1：否
     */
    @Column
    private Integer usingSpreadBondType;
    /**
     * 是否根据隐含评级标签进行分组，0：是，1：否
     */
    @Column
    private Integer usingBondImpliedRatingMappingTag;
    /**
     * 是否根据yy评级标签进行分组，0：是，1：否
     */
    @Column
    private Integer usingComYyRatingMappingTag;
    /**
     * 是否根据利差剩余期限标签进行分组: 0：是，1：否
     */
    @Column
    private Integer usingSpreadRemainingTenorTag;
    /**
     * 是否根据担保状态进行分组: 0：是，1：否
     */
    @Column
    private Integer usingGuaranteedStatus;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }

    public Long getInduLevel2Code() {
        return induLevel2Code;
    }

    public void setInduLevel2Code(Long induLevel2Code) {
        this.induLevel2Code = induLevel2Code;
    }

    public Long getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(Long bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public Long getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(Long bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }

    public Integer getUsingBondExtRatingMapping() {
        return usingBondExtRatingMapping;
    }

    public void setUsingBondExtRatingMapping(Integer usingBondExtRatingMapping) {
        this.usingBondExtRatingMapping = usingBondExtRatingMapping;
    }

    public Integer getUsingSpreadBondType() {
        return usingSpreadBondType;
    }

    public void setUsingSpreadBondType(Integer usingSpreadBondType) {
        this.usingSpreadBondType = usingSpreadBondType;
    }

    public Integer getUsingSpreadRemainingTenorTag() {
        return usingSpreadRemainingTenorTag;
    }

    public void setUsingSpreadRemainingTenorTag(Integer usingSpreadRemainingTenorTag) {
        this.usingSpreadRemainingTenorTag = usingSpreadRemainingTenorTag;
    }

    public Integer getUsingGuaranteedStatus() {
        return usingGuaranteedStatus;
    }

    public void setUsingGuaranteedStatus(Integer usingGuaranteedStatus) {
        this.usingGuaranteedStatus = usingGuaranteedStatus;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getUsingBondImpliedRatingMappingTag() {
        return usingBondImpliedRatingMappingTag;
    }

    public void setUsingBondImpliedRatingMappingTag(Integer usingBondImpliedRatingMappingTag) {
        this.usingBondImpliedRatingMappingTag = usingBondImpliedRatingMappingTag;
    }

    public Integer getBondImpliedRatingMappingTag() {
        return bondImpliedRatingMappingTag;
    }

    public void setBondImpliedRatingMappingTag(Integer bondImpliedRatingMappingTag) {
        this.bondImpliedRatingMappingTag = bondImpliedRatingMappingTag;
    }

    public Integer getComYyRatingMappingTag() {
        return comYyRatingMappingTag;
    }

    public void setComYyRatingMappingTag(Integer comYyRatingMappingTag) {
        this.comYyRatingMappingTag = comYyRatingMappingTag;
    }

    public Integer getUsingComYyRatingMappingTag() {
        return usingComYyRatingMappingTag;
    }

    public void setUsingComYyRatingMappingTag(Integer usingComYyRatingMappingTag) {
        this.usingComYyRatingMappingTag = usingComYyRatingMappingTag;
    }
}