package com.innodealing.onshore.yieldspread.helper;

import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 通用工具类
 *
 * <AUTHOR>
 */
public final class CommonUtils {

    private CommonUtils() {
    }

    /**
     * 脱敏
     *
     * @param value          value
     * @param hasPermissions 是否有权限
     * @return 权限校验后的值
     */
    public static String desensitized(String value, Boolean... hasPermissions) {
        if (ArrayUtils.isEmpty(hasPermissions) || !Arrays.stream(hasPermissions).allMatch(Boolean.TRUE::equals)) {
            return YieldSpreadConst.NO_PERMISSIONS_PLACEHOLDER;
        }
        return StringUtils.isBlank(value) ? YieldSpreadConst.EMPTY_PLACEHOLDER : value;
    }

    /**
     * 脱敏
     *
     * @param value          value
     * @param hasPermissions 是否有权限
     * @return 权限校验后的值
     */
    public static String desensitized(BigDecimal value, Boolean... hasPermissions) {
        if (ArrayUtils.isEmpty(hasPermissions) || !Arrays.stream(hasPermissions).allMatch(Boolean.TRUE::equals)) {
            return YieldSpreadConst.NO_PERMISSIONS_PLACEHOLDER;
        }
        return Objects.isNull(value) ? YieldSpreadConst.EMPTY_PLACEHOLDER : value.toString();
    }

    /**
     * 格式化或脱敏
     *
     * @param value          value
     * @param decimalPlace   保留的小数位
     * @param hasPermissions 是否有权限  需都满足
     * @return 格式化或脱敏后的值
     */
    public static String formatOrDesensitized(BigDecimal value, Integer decimalPlace, Boolean... hasPermissions) {
        if (ArrayUtils.isEmpty(hasPermissions)) {
            return doFormatOrDesensitized(value, decimalPlace, false);
        }
        return doFormatOrDesensitized(value, decimalPlace, Arrays.stream(hasPermissions).allMatch(Boolean.TRUE::equals));
    }

    /**
     * 格式化或脱敏
     *
     * @param value         value
     * @param decimalPlace  保留的小数位
     * @param hasPermission 是否有权限
     * @return 格式化或脱敏后的值
     */
    public static String formatOrDesensitized(BigDecimal value, Integer decimalPlace, Boolean hasPermission) {
        return doFormatOrDesensitized(value, decimalPlace, hasPermission);
    }

    /**
     * 格式化 小数格式化string
     *
     * @param value        value
     * @param decimalPlace 保留的小数位
     * @return 格式化或脱敏后的值
     */
    public static String formatDecimal(BigDecimal value, Integer decimalPlace) {
        return doFormatOrDesensitized(value, decimalPlace, true);
    }

    /**
     * 设置小数位数
     *
     * @param value 小数
     * @param scale 位数
     * @return 数值
     */
    public static Optional<BigDecimal> safeSetScale(BigDecimal value, Integer scale) {
        return Optional.ofNullable(value).map(data -> {
            if (Objects.nonNull(scale)) {
                data = data.setScale(scale, RoundingMode.HALF_UP);
            }
            return data;
        });
    }

    /**
     * 格式化或脱敏
     *
     * @param value         value
     * @param decimalPlace  保留的小数位
     * @param hasPermission 是否有权限
     * @return 格式化或脱敏后的值
     */
    private static String doFormatOrDesensitized(BigDecimal value, Integer decimalPlace, Boolean hasPermission) {
        if (Objects.equals(Boolean.FALSE, hasPermission)) {
            return YieldSpreadConst.NO_PERMISSIONS_PLACEHOLDER;
        }
        if (Objects.isNull(value)) {
            return YieldSpreadConst.EMPTY_PLACEHOLDER;
        }
        Optional<String> decimalFormatOptional = getDecimalFormat(decimalPlace);
        if (decimalFormatOptional.isPresent()) {
            DecimalFormat df = new DecimalFormat(decimalFormatOptional.get());
            df.setRoundingMode(RoundingMode.HALF_UP);
            return df.format(value);
        }
        return String.valueOf(value);
    }

    /**
     * 获取格式
     *
     * @param decimalPlace 保留小数位
     * @return 格式
     */
    private static Optional<String> getDecimalFormat(Integer decimalPlace) {
        if (Objects.isNull(decimalPlace)) {
            return Optional.empty();
        }
        if (decimalPlace <= 0) {
            return Optional.of("0");
        }
        String format = "%0" + decimalPlace + "d";
        return Optional.of("0." + String.format(format, 0));
    }

}
