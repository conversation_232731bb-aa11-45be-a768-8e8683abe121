package com.innodealing.onshore.yieldspread.service;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.InduListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.TrendReplayComYieldSpreadRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InduComYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 产业主体利差 Service
 *
 * <AUTHOR>
 **/
public interface InduComYieldSpreadService {

    /**
     * 产业主体利差计算
     *
     * @param comYieldSpreadDOs 产业主体利差数据
     * @param spreadDate        利差日期
     * @param isEnableOldData   是否启用老数据
     * @return InduComYieldSpreadDO
     */
    Integer calcInduComYieldSpreadsBySpreadDate(List<InduComYieldSpreadDO> comYieldSpreadDOs,
                                                Date spreadDate, Boolean isEnableOldData);

    /**
     * 行业主体利差分页查询
     *
     * @param request 行业主体利差分页查询请求参数
     * @return {@link NormPagingResult}<{@link InduComYieldSpreadResponseDTO}> 行业主体利差分页查询响应DTO
     */
    NormPagingResult<InduComYieldSpreadResponseDTO> getComYieldSpreadPaging(InduListRequestDTO request);

    /**
     * 行业主体利差分页查询(测试)
     *
     * @param request 行业主体利差分页查询请求参数
     * @return {@link NormPagingResult}<{@link InduComYieldSpreadResponseDTO}> 行业主体利差分页查询响应DTO
     */
    NormPagingResult<InduComYieldSpreadResponseDTO> getComYieldSpreadPagingByExists(InduListRequestDTO request);

    /**
     * 获取最大利差日期
     *
     * @return {@link String} 利差日期
     */
    Date getMaxSpreadDate();

    /**
     * 查询行业主体利差总数
     *
     * @param request 查询行业主体利差总数请求参数
     * @return {@link Long} 总数
     */
    Long getComYieldSpreadPagingCount(InduListRequestDTO request);

    /**
     * 获取主体利差
     *
     * @param userid  用户ID
     * @param request 请求参数
     * @return 主体利差
     */
    List<InduComYieldSpreadResponseDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 主体数量
     *
     * @param userid  用户ID
     * @param request 请求参数
     * @return 主体数量
     */
    Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 获取证券主体利差数据集
     *
     * @param spreadDate 利差日期
     * @param induComs   主体唯一编码集合
     * @return {@link List}<{@link InduComYieldSpreadResponseDTO}>
     */
    List<InduComYieldSpreadResponseDTO> listComs(Date spreadDate, Set<Long> induComs);

    /**
     * 查询利差曲线数据集
     *
     * @param comUniCode     主体唯一编码
     * @param spreadBondType 利差债券类型
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @return {@link List}<{@link ComYieldSpreadCurveDTO}>
     */
    List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer spreadBondType, Date startDate, Date endDate);

    /**
     * listTrendReplayComYieldSpreads
     * @param spreadStartDate 开始日期
     * @param spreadEndDate 结束日期
     * @param value 请求参数的列表
     * @return list
     */
    List<ComCreditSpreadDTO> listTrendReplayComYieldSpreads(Date spreadStartDate, Date spreadEndDate,
                                                            List<TrendReplayComYieldSpreadRequestDTO> value);
}
