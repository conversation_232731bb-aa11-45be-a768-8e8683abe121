package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 主体利差曲线-城投
 *
 * <AUTHOR>
 */
@Table(name = "mv_udic_com_yield_spread_curve")
public class MvUdicComYieldSpreadCurveDO extends BaseMvComYieldSpreadCurveDO {


    /**
     * 利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer spreadBondType;
    @Column
    private Integer usingSpreadBondType;

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getUsingSpreadBondType() {
        return usingSpreadBondType;
    }

    public void setUsingSpreadBondType(Integer usingSpreadBondType) {
        this.usingSpreadBondType = usingSpreadBondType;
    }
}
