package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition;

import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.BankBondYieldSpreadCurveParameter;
import org.apache.ibatis.annotations.Param;

/**
 * 银行利差曲线mapper
 * <AUTHOR>
 */
public interface PgBankBondYieldSpreadCurveMapper extends PgBaseMapper<BankBondYieldSpreadCurveParameter> {

    /**
     * 创建实体表(评级分片)
     * @param parameter 创建参数
     */
    void createTableRatingRouter(@Param("parameter") BankBondYieldSpreadCurveParameter parameter);

    /**
     * 同步视图数据
     * @param tableName 表名
     * @param mvTableName 视图名称
     */
    void syncCurveIncrFromMV(@Param("tableName") String tableName, @Param("mvTableName") String mvTableName);

}
