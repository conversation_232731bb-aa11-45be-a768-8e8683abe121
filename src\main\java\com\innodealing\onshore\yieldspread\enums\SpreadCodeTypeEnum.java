package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 利差编码类型
 *
 * <AUTHOR>
 */
public enum SpreadCodeTypeEnum implements ITextValueEnum {
    /**
     * 主体编码类型
     */
    COM_CODE(1, "主体编码"),
    /**
     * 债券编码类型
     */
    BOND_CODE(2, "债券编码");

    private Integer code;
    private String text;

    SpreadCodeTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }
}
