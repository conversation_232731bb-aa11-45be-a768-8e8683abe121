package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.*;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.request.OnshoreBondFilterRequestDTO;
import com.innodealing.onshore.bondmetadata.enums.CouponRateTypeEnum;
import com.innodealing.onshore.bondmetadata.enums.UrbanInvestEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.enums.UrbanInvestCaliberEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.*;

/**
 * 债券基础信息远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "bondInfoService", url = "${bond.basic.api.url}", path = "/internal")
public interface BondInfoService {

    /**
     * 获取债券主体编码信息
     *
     * @param startBondUniCode            查询最小 bondUniCode
     * @param fetchCount                  获取数量
     * @param onshoreBondFilterRequestDTO 债券筛选请求DTO
     * @return 债券剩余日期
     */
    @PostMapping("bond/filter/onshore/list/bondComUniCode/fetch")
    List<BondComUniCodeDTO> listBondComUniCodeDTOs(
            @RequestParam Long startBondUniCode,
            @RequestParam Integer fetchCount,
            @RequestBody OnshoreBondFilterRequestDTO onshoreBondFilterRequestDTO);

    /**
     * 批量获取债券信息
     *
     * @param bondUniCodes 债券唯一编码列表
     * @return 境内债券信息
     */
    @PostMapping("bond/onshore/info/listByUniCodes")
    List<OnshoreBondInfoDTO> listOnshoreBondInfoDTOs(@RequestBody Collection<Long> bondUniCodes);

    /**
     * 获取主体存续信息
     *
     * @param comUniCodes 主体编码
     * @return 发行人存续
     */
    @PostMapping("com/current/bonds/balance/getByComUniCodes")
    List<ComCurrentBondsBalanceDTO> listComCurrentBondsBalanceByComUniCodes(@RequestBody Collection<Long> comUniCodes);

    /**
     * 获取指定计算日期的债券剩余期限
     *
     * @param bondUniCodes  债券编码
     * @param calculateDate 计算日期
     * @return 债券剩余日期
     */
    @PostMapping("bond/calculate/remainingTenor")
    List<BondRemainingTenorDTO> listBondRemainingTenorDTOs(@RequestBody Long[] bondUniCodes,
                                                           @RequestParam Date calculateDate);

    /**
     * 批量获取债券精简信息
     *
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link BondShortInfoDTO}> 债券基础信息集合
     */
    @PostMapping("/bond/info/short/getByUniCode")
    List<BondShortInfoDTO> listBondShortInfoListByUniCodes(@RequestBody Long[] bondUniCodes);

    /**
     * 批量获取债券过滤信息
     *
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link OnshoreBondFilterV3DTO}> 债券过滤信息集合
     */
    @ApiOperation("批量获取债券过滤信息")
    @PostMapping("/bond/filter/v3/onshore/batch")
    List<OnshoreBondFilterV3DTO> listOnshoreFilterBatchByUniCodes(@RequestBody Long[] bondUniCodes);

    /**
     * 获取产业债券主体编码信息
     *
     * @param isUdic            城投: 1 是 0 否
     * @param shardingHindParam 分片参数
     * @return BondComUniCodeDTO
     */
    default List<BondComUniCodeDTO> listBondComUniCodeDTOs(Integer isUdic, String shardingHindParam) {
        OnshoreBondFilterRequestDTO onshoreBondFilterRequestDTO = new OnshoreBondFilterRequestDTO();
        List<Integer> couponRateFilterTypeList = new ArrayList<>();
        couponRateFilterTypeList.add(CouponRateTypeEnum.FIXED_RATE.getValue());
        couponRateFilterTypeList.add(CouponRateTypeEnum.FLOAT_RATE.getValue());
        if (Objects.equals(isUdic, UrbanInvestEnum.NOT_URBAN_INVEST.getValue())) {
            onshoreBondFilterRequestDTO.setUdicStatus(isUdic);
            onshoreBondFilterRequestDTO.setInduUniCodeList(new ArrayList<>(YieldSpreadHelper.getInduUnicodeMap().keySet()));
            onshoreBondFilterRequestDTO.setBondTypeList(YieldSpreadHelper.getInduSpreadBondTypeList());
        }
        if (Objects.equals(isUdic, UrbanInvestEnum.URBAN_INVEST.getValue())) {
            UrbanInvestCaliberEnum urbanInvestCaliberEnum = EnumUtils.getEnumByTextValue(shardingHindParam, UrbanInvestCaliberEnum.class).orElse(UrbanInvestCaliberEnum.UDIC);
            if (Objects.equals(urbanInvestCaliberEnum, UrbanInvestCaliberEnum.UDIC)) {
                onshoreBondFilterRequestDTO.setUdicStatus(isUdic);
            }
            onshoreBondFilterRequestDTO.setBondTypeList(YieldSpreadHelper.getUdicSpreadBondTypeList());
        }
        onshoreBondFilterRequestDTO.setCouponRateFilterTypeList(couponRateFilterTypeList);
        return getBondComUniCodeDTOs(onshoreBondFilterRequestDTO);
    }

    /**
     * 获取证券行业类型的债券主体编码信息
     *
     * @param udicStatus 城投: 1 是 0 否
     * @return BondComUniCodeDTO
     */
    default List<BondComUniCodeDTO> listSecuBondComUniCodeDTOs(Integer udicStatus) {
        OnshoreBondFilterRequestDTO onshoreBondFilterRequestDTO = new OnshoreBondFilterRequestDTO();
        onshoreBondFilterRequestDTO.setUdicStatus(udicStatus);
        List<Long> induUniCodeList = new ArrayList<>();
        induUniCodeList.add(SECU_INDU_CODE);
        onshoreBondFilterRequestDTO.setInduUniCodeList(induUniCodeList);
        onshoreBondFilterRequestDTO.setBondTypeList(YieldSpreadHelper.getSecuSpreadBondTypeList());
        return getBondComUniCodeDTOs(onshoreBondFilterRequestDTO);
    }

    /**
     * 获取地方债的债券主体编码信息
     *
     * @return BondComUniCodeDTO
     */
    default List<BondComUniCodeDTO> listLgBondComUniCodeDTOs() {
        OnshoreBondFilterRequestDTO onshoreBondFilterRequestDTO = new OnshoreBondFilterRequestDTO();
        onshoreBondFilterRequestDTO.setBondTypeList(YieldSpreadHelper.getLgBondSpreadTypeList());
        return getBondComUniCodeDTOs(onshoreBondFilterRequestDTO);
    }

    /**
     * 获取保险行业类型的债券主体编码信息
     *
     * @param udicStatus 城投: 1 是 0 否
     * @return BondComUniCodeDTO
     */
    default List<BondComUniCodeDTO> listInsuBondComUniCodeDTOs(Integer udicStatus) {
        OnshoreBondFilterRequestDTO onshoreBondFilterRequestDTO = new OnshoreBondFilterRequestDTO();
        onshoreBondFilterRequestDTO.setUdicStatus(udicStatus);
        List<Long> induUniCodeList = new ArrayList<>();
        induUniCodeList.add(INSU_INDU_CODE);
        onshoreBondFilterRequestDTO.setInduUniCodeList(induUniCodeList);
        // bond_type_par in (4,5,8,9,14,15,21,22,23,24,26,31,33,35,36)
        onshoreBondFilterRequestDTO.setBondTypeList(YieldSpreadHelper.getInsuSpreadBondTypeList());
        return getBondComUniCodeDTOs(onshoreBondFilterRequestDTO);
    }

    /**
     * 获取银行的债券主体编码信息
     *
     * @param udicStatus 城投: 1 是 0 否
     * @return BondComUniCodeDTO
     */
    default List<BondComUniCodeDTO> listBankBondComUniCodeDTOs(Integer udicStatus) {
        OnshoreBondFilterRequestDTO onshoreBondFilterRequestDTO = new OnshoreBondFilterRequestDTO();
        onshoreBondFilterRequestDTO.setUdicStatus(udicStatus);
        List<Long> induUniCodeList = new ArrayList<>();
        induUniCodeList.add(BANK_INDU_CODE);
        onshoreBondFilterRequestDTO.setInduUniCodeList(induUniCodeList);
        onshoreBondFilterRequestDTO.setBondTypeList(YieldSpreadHelper.getBankSpreadBondTypeList());
        return getBondComUniCodeDTOs(onshoreBondFilterRequestDTO);
    }

    /**
     * 获取债券主体编码信息
     *
     * @param onshoreBondFilterRequestDTO 债券筛选请求
     * @return BondComUniCodeDTO
     */
    default List<BondComUniCodeDTO> getBondComUniCodeDTOs(OnshoreBondFilterRequestDTO onshoreBondFilterRequestDTO) {
        Long startBondUniCode = 0L;
        Integer fetchCount = null;
        List<BondComUniCodeDTO> bondComUniCodeDTOAllList = new ArrayList<>();
        while (Objects.isNull(fetchCount) || fetchCount == YieldSpreadHelper.BATCH_SIZE) {
            List<BondComUniCodeDTO> bondComUniCodeDTOs = listBondComUniCodeDTOs(startBondUniCode,
                    YieldSpreadHelper.BATCH_SIZE, onshoreBondFilterRequestDTO);
            if (CollectionUtils.isEmpty(bondComUniCodeDTOs)) {
                break;
            }
            fetchCount = bondComUniCodeDTOs.size();
            startBondUniCode = bondComUniCodeDTOs.stream()
                    .mapToLong(BondComUniCodeDTO::getBondUniCode).max().orElse(startBondUniCode);
            bondComUniCodeDTOAllList.addAll(bondComUniCodeDTOs);
        }
        return bondComUniCodeDTOAllList;
    }

    /**
     * 批量获取债券信息
     *
     * @param bondUniCodes 债券唯一编码列表
     * @param spreadDate   利差日期
     * @return 境内债券信息
     */
    default List<OnshoreBondInfoDTO> listOnshoreBondInfoDTOs(Set<Long> bondUniCodes, Date spreadDate) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        Map<Long, BondRemainingTenorDTO> bondRemainingTenorDTOMap = getValidBondRemainingTenorDTOMap(bondUniCodes, spreadDate);
        if (ObjectUtils.isEmpty(bondRemainingTenorDTOMap)) {
            return Collections.emptyList();
        }
        bondUniCodes = bondRemainingTenorDTOMap.keySet();
        //批量获取债券信息
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = listOnshoreBondInfoDTOs(bondUniCodes);
        if (CollectionUtils.isEmpty(onshoreBondInfoDTOs)) {
            return Collections.emptyList();
        }
        return onshoreBondInfoDTOs.stream().map(x -> {
            OnshoreBondInfoDTO result = BeanCopyUtils.copyProperties(x, OnshoreBondInfoDTO.class);
            result.setRemainingTenor(bondRemainingTenorDTOMap.get(result.getBondUniCode()).getRemainTenor());
            result.setRemainingTenorDay(bondRemainingTenorDTOMap.get(result.getBondUniCode()).getRemainTenorDays());
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 批量获取债券信息,没有剩余期限的不会返回
     *
     * @param bondUniCodes 债券唯一编码列表
     * @param spreadDate   利差日期
     * @return 境内债券信息
     */
    default List<OnshoreBondInfoDTO> listOnshoreBondInfos(Set<Long> bondUniCodes, Date spreadDate) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        List<BondRemainingTenorDTO> bondRemainingTenors = listBondRemainingTenorDTOs(bondUniCodes.stream().toArray(Long[]::new), spreadDate);
        if (CollectionUtils.isEmpty(bondRemainingTenors)) {
            return Collections.emptyList();
        }
        List<BondRemainingTenorDTO> validRemainingTenors = new ArrayList<>();
        for (BondRemainingTenorDTO bondRemainingTenorDTO : bondRemainingTenors) {
            if (Objects.nonNull(bondRemainingTenorDTO.getRemainTenorDays())) {
                validRemainingTenors.add(bondRemainingTenorDTO);
            }
        }
        if (CollectionUtils.isEmpty(validRemainingTenors)) {
            return Collections.emptyList();
        }
        Map<Long, BondRemainingTenorDTO> remainingTenorMap = validRemainingTenors.stream()
                .collect(Collectors.toMap(BondRemainingTenorDTO::getBondUniCode, Function.identity(), (x1, x2) -> x2));
        bondUniCodes = remainingTenorMap.keySet();
        //批量获取债券信息
        List<OnshoreBondInfoDTO> onshoreBondInfos = listOnshoreBondInfoDTOs(bondUniCodes);
        if (CollectionUtils.isEmpty(onshoreBondInfos)) {
            return Collections.emptyList();
        }
        return onshoreBondInfos.stream().map(x -> {
            OnshoreBondInfoDTO result = BeanCopyUtils.copyProperties(x, OnshoreBondInfoDTO.class);
            result.setRemainingTenor(remainingTenorMap.get(result.getBondUniCode()).getRemainTenor());
            result.setRemainingTenorDay(remainingTenorMap.get(result.getBondUniCode()).getRemainTenorDays());
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 获取指定计算日期的利差债券有效剩余期限
     *
     * @param bondUniCodes  债券编码
     * @param calculateDate 计算日期
     * @return key 债券编码,value 债券剩余期限
     */
    default Map<Long, BondRemainingTenorDTO> getValidBondRemainingTenorDTOMap(Set<Long> bondUniCodes, Date calculateDate) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyMap();
        }
        List<BondRemainingTenorDTO> bondRemainingTenorDTOs = listBondRemainingTenorDTOs(bondUniCodes.stream().toArray(Long[]::new),
                calculateDate);
        if (CollectionUtils.isEmpty(bondRemainingTenorDTOs)) {
            return Collections.emptyMap();
        }
        List<BondRemainingTenorDTO> validDataList = new ArrayList<>();
        for (BondRemainingTenorDTO bondRemainingTenorDTO : bondRemainingTenorDTOs) {
            if (YieldSpreadHelper.spreadRemainingTenorIsValid(bondRemainingTenorDTO.getRemainTenorDays())) {
                validDataList.add(bondRemainingTenorDTO);
            }
        }
        if (CollectionUtils.isEmpty(validDataList)) {
            return Collections.emptyMap();
        }
        return validDataList.stream()
                .collect(Collectors.toMap(BondRemainingTenorDTO::getBondUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 获取主体存续信息
     *
     * @param comUniCodes 主体编码
     * @return key 主体编码,value 债券存续金额DTO
     */
    default Map<Long, ComCurrentBondsBalanceDTO> getComCurrentBondsBalanceDTOMap(Collection<Long> comUniCodes) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return Collections.emptyMap();
        }
        return listComCurrentBondsBalanceByComUniCodes(comUniCodes).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ComCurrentBondsBalanceDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 获取债券精简信息
     *
     * @param comUnicode 债券代码
     * @return 债券精简信息
     */
    @PostMapping("/bond/info/short/getNotDeletedByCom")
    List<BondShortInfoDTO> listBondShortInfoByCom(@RequestBody Long comUnicode);

    /**
     * 债券名称转代码
     *
     * @param nameMd5s 债券名称
     * @return 债券名称转代码
     */
    @PostMapping("/bond/unicode/getByName")
    List<BondNameToCodeDTO> getBondNameToCodeList(@RequestBody String... nameMd5s);

    /**
     * 批量获取债券代码
     *
     * @param bondCodes 债券代码(不带后缀)
     * @return 债券代码列表
     */
    @PostMapping("/bond/bondCode/getByCode")
    List<BondCodeDTO> getBondCodeList(@RequestBody String[] bondCodes);

    /**
     * 批量获取债券精简信息
     *
     * @param bondCodes 债券代码
     * @return 债券精简列表
     */
    @PostMapping("/bond/info/short/getByCode")
    List<BondShortInfoDTO> getBondShortInfoListByCodes(@RequestBody String[] bondCodes);

    /**
     * 获取债券筛选信息V3-fetchCount大小不能超过500
     *
     * @param circulationStatus            是否流通中 0:否 1:是
     * @param fetchCount                   获取数量
     * @param lastUpdateTime               最新更新时间
     * @param lastUpdateTimeMaxBondUniCode 最新更新时间里面最大的bondUniCode
     * @return List<OnshoreBondFilterV3DTO>
     */
    @GetMapping
    List<OnshoreBondFilterV3DTO> listBondFilters(@RequestParam Integer circulationStatus,
                                                 @RequestParam Integer fetchCount,
                                                 @RequestParam Timestamp lastUpdateTime,
                                                 @RequestParam Long lastUpdateTimeMaxBondUniCode);

}
