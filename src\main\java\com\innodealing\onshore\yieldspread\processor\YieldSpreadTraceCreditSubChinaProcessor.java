package com.innodealing.onshore.yieldspread.processor;

import com.google.common.collect.Lists;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.CD_BOND;

/**
 * 利差追踪-信用利差 减国债 处理器
 *
 * <AUTHOR>
 */
@Component
public class YieldSpreadTraceCreditSubChinaProcessor implements YieldSpreadTraceProcessor {

    private static final Set<YieldPanoramaBondTypeEnum> SUPPORT_BOND_TYPES = EnumSet.of(CD_BOND);

    @Override
    public Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum) {
        return SUPPORT_BOND_TYPES.contains(traceBondTypeEnum);
    }

    @Override
    public List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context) {
        if (Objects.isNull(context.getChinaBondYieldPanorama())) {
            return Collections.emptyList();
        }
        YieldPanoramaBondTypeEnum bondTypeEnum = context.getBondTypeEnum();

        // 减去同期限国债
        List<PgBondYieldPanoramaAbsDO> absBondYieldPanoramas = context.getAbsBondYieldPanoramas();
        PgBondYieldPanoramaAbsDO chinaBond = context.getChinaBondYieldPanorama();
        Map<Integer, PgBondYieldPanoramaAbsDO> absCurveCodeMap =
                absBondYieldPanoramas.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        List<PgBondYieldSpreadTraceAbsDO> dataList = Lists.newArrayList();
        for (Map.Entry<Integer, PgBondYieldPanoramaAbsDO> absEntry : absCurveCodeMap.entrySet()) {
            PgBondYieldPanoramaAbsDO abs = absEntry.getValue();

            PgBondYieldSpreadTraceAbsDO bondYieldSpreadTraceAbsDO = convertAbsSubtractToTrace(abs, chinaBond, bondTypeEnum,
                    context.getIssueDate(), absEntry.getKey(), YieldSpreadChartTypeEnum.CREDIT_SUB_CB_SPREAD);
            dataList.add(bondYieldSpreadTraceAbsDO);
        }
        return dataList;
    }
}
