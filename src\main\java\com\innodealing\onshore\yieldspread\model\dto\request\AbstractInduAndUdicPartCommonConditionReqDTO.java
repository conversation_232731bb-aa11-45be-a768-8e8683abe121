package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.ExtRatingFilterMappingEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.enums.GuaranteedStatusEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

/**
 * 产业和城投部分公共筛选项
 *
 * <AUTHOR>
 */
public abstract class AbstractInduAndUdicPartCommonConditionReqDTO extends AbstractCurveGenerateConditionReqDTO {

    /**
     * @see com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum
     */
    @ApiModelProperty("债券类型 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)")
    protected Integer spreadBondType;

    /**
     * @see com.innodealing.onshore.yieldspread.enums.GuaranteedStatusEnum
     */
    @ApiModelProperty("担保状态: 0: 无, 1: 有")
    protected Integer guaranteedStatus;

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.ExtRatingFilterMappingEnum
     */
    @ApiModelProperty("债券外部评级  20:AAA,40:AA+,50:AA")
    protected Integer bondExtRatingMapping;

    /**
     * yy评级(投资级:1、2、3、4、5,投机级:6、7、8、风险级:9、10)
     */
    @ApiModelProperty("yy评级 1,2,3,4,5  6,7,8")
    protected Integer[] comYyRatingMappings;

    protected String jointSpreadBondTypeName() {
        return Objects.nonNull(this.spreadBondType) ? (SEPARATOR + EnumUtils.getEnum(SpreadBondTypeEnum.class, this.spreadBondType).getText()) : "";
    }

    protected String jointGuaranteeStatusName() {
        return Objects.nonNull(this.guaranteedStatus) ?
                (SEPARATOR + EnumUtils.getEnum(GuaranteedStatusEnum.class, this.guaranteedStatus).getDesc()) : "";
    }

    protected String jointExtRatingName() {
        if (Objects.isNull(this.bondExtRatingMapping)) {
            return "";
        }
        for (ExtRatingFilterMappingEnum value : ExtRatingFilterMappingEnum.values()) {
            if (this.bondExtRatingMapping.equals(value.getOriginRatingMapping())) {
                return SEPARATOR + "外评" + value.getText();
            }
        }
        return "";
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer[] getComYyRatingMappings() {
        return Objects.isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public void setComYyRatingMappings(Integer[] comYyRatingMappings) {
        this.comYyRatingMappings = Objects.isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

}
