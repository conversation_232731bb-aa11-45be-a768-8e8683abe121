package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayStringJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 单券利差通用字段
 *
 * <AUTHOR>
 */
public class BaseSingleBondYieldSpreadResDTO {

    @ApiModelProperty("利差日期")
    private Date spreadDate;

    @ApiModelProperty("债券code")
    private Long bondUniCode;

    @ApiModelProperty("债券简称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String bondShortName;

    @ApiModelProperty("债券编码")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String bondCode;

    @ApiModelProperty("剩余期限")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String remainingTenor;

    @ApiModelProperty("发行人名称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String comUniName;

    @ApiModelProperty("所属行业")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String induLevel2Name;

    @ApiModelProperty("票面利率(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal latestCouponRate;

    @ApiModelProperty("剩余规模(亿)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondBalance;

    @ApiModelProperty("主/债")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String ratingStr;

    @ApiModelProperty("隐含评级")
    private Integer bondImpliedRatingMapping;

    @ApiModelProperty("隐含评级")
    private String bondImpliedRatingMappingStr;

    @ApiModelProperty("债券信用利差;单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondCreditSpread;

    @ApiModelProperty("债券超额利差;单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondExcessSpread;

    @ApiModelProperty("中债估值;单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal cbYield;

    @ApiModelProperty("中债估值;单位(%)")
    private String cbYieldStr;

    @ApiModelProperty("国开收益率;单位(%)")
    private String cdbLerpYield;

    @ApiModelProperty("曲线id")
    private Long curveId;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }

    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public String getRatingStr() {
        return ratingStr;
    }

    public void setRatingStr(String ratingStr) {
        this.ratingStr = ratingStr;
    }

    public Integer getBondImpliedRatingMapping() {
        return bondImpliedRatingMapping;
    }

    public void setBondImpliedRatingMapping(Integer bondImpliedRatingMapping) {
        this.bondImpliedRatingMapping = bondImpliedRatingMapping;
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public String getCbYieldStr() {
        return cbYieldStr;
    }

    public void setCbYieldStr(String cbYieldStr) {
        this.cbYieldStr = cbYieldStr;
    }

    public String getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(String cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

    public String getBondImpliedRatingMappingStr() {
        return bondImpliedRatingMappingStr;
    }

    public void setBondImpliedRatingMappingStr(String bondImpliedRatingMappingStr) {
        this.bondImpliedRatingMappingStr = bondImpliedRatingMappingStr;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Long getCurveId() {
        return curveId;
    }

    public void setCurveId(Long curveId) {
        this.curveId = curveId;
    }

}
