package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondrating.request.BondExternalCreditRatingRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.request.BondImpliedRatingRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.request.ComExternalCreditRatingRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.request.ComYyRatingRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 评级服务
 *
 * <AUTHOR>
 **/
@FeignClient(name = "bondRatingService", url = "${bond.rating.api.url}", path = "/internal/rating/latest")
public interface BondRatingService {
    /**
     * 批量获取债券隐含评级
     *
     * @param bondImpliedRatingRequestDTO 隐含评级请求参数
     * @return 债券隐含评级列表
     */
    @PostMapping("bondImpliedRating/list")
    BondImpliedRatingResponseDTO listBondImpliedRatings(@RequestBody BondImpliedRatingRequestDTO bondImpliedRatingRequestDTO);

    /**
     * 获取债券外部评级数据
     *
     * @param bondExternalCreditRatingRequestDTO 债券外部评级请求DTO
     * @return 债券外部评级响应DTO
     */
    @PostMapping("bondExternalCreditRating/list")
    BondExternalCreditRatingResponseDTO listBondExternalCreditRatings(
            @RequestBody BondExternalCreditRatingRequestDTO bondExternalCreditRatingRequestDTO);

    /**
     * 获取主体外部评级数据
     *
     * @param comExternalCreditRatingRequestDTO 主体外部评级请求DTO
     * @return 主体外部评级响应DTO
     */
    @PostMapping("comExternalCreditRating/onshore/list")
    List<ComExternalCreditRatingDTO> listComExternalCreditRatings(
            @RequestBody ComExternalCreditRatingRequestDTO comExternalCreditRatingRequestDTO);

    /**
     * 主体YY评级数据
     *
     * @param comYyRatingRequestDTO 主体YY评级请求DTO
     * @return 主体YY评级响应DTO
     */
    @PostMapping("comYyRating/list")
    ComYyRatingResponseDTO listComYyRatings(@RequestBody ComYyRatingRequestDTO comYyRatingRequestDTO);

    /**
     * 批量获取债券隐含评级
     *
     * @param issueTime       发布日期
     * @param bondUniCodeList 债券列表
     * @return key 债券唯一代码,value 债券隐含评级
     */
    default Map<Long, BondImpliedRatingDTO> getBondImpliedRatingMap(Timestamp issueTime, Set<Long> bondUniCodeList) {
        if (CollectionUtils.isEmpty(bondUniCodeList)) {
            return Collections.emptyMap();
        }
        BondImpliedRatingRequestDTO requestDTO = new BondImpliedRatingRequestDTO();
        requestDTO.setBondUniCodeList(new ArrayList<>(bondUniCodeList));
        requestDTO.setIssueTime(issueTime);
        return listBondImpliedRatings(requestDTO).getBondImpliedRatingDTOList().stream()
                .collect(Collectors.toMap(BondImpliedRatingDTO::getBondUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 获取债券外部评级数据
     *
     * @param issueTime       发布日期
     * @param bondUniCodeList 债券列表
     * @return key 债券唯一代码,value 债券外部评级
     */
    default Map<Long, BondExternalCreditRatingDTO> getBondExternalCreditRatingMap(Timestamp issueTime, Set<Long> bondUniCodeList) {
        if (CollectionUtils.isEmpty(bondUniCodeList)) {
            return Collections.emptyMap();
        }
        BondExternalCreditRatingRequestDTO requestDTO = new BondExternalCreditRatingRequestDTO();
        requestDTO.setBondUniCodeList(new ArrayList<>(bondUniCodeList));
        requestDTO.setIssueTime(issueTime);
        return listBondExternalCreditRatings(requestDTO).getBondExternalCreditRatingDTOList().stream()
                .collect(Collectors.toMap(BondExternalCreditRatingDTO::getBondUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 获取主体外部评级数据
     *
     * @param issueTime      发布日期
     * @param comUniCodeList 主体列表
     * @return key 发行人唯一代码,value 主体外部评级
     */
    default Map<Long, ComExternalCreditRatingDTO> getComExternalCreditRatingMap(Timestamp issueTime, Set<Long> comUniCodeList) {
        if (CollectionUtils.isEmpty(comUniCodeList)) {
            return Collections.emptyMap();
        }
        ComExternalCreditRatingRequestDTO requestDTO = new ComExternalCreditRatingRequestDTO();
        requestDTO.setComUniCodeList(new ArrayList<>(comUniCodeList));
        requestDTO.setIssueTime(issueTime);
        return listComExternalCreditRatings(requestDTO).stream()
                .collect(Collectors.toMap(ComExternalCreditRatingDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }


    /**
     * 获取主体YY评级数据
     *
     * @param issueTime      发布日期
     * @param comUniCodeList 主体列表
     * @return key 发行人唯一代码,value 主体YY评级
     */
    default Map<Long, ComYyRatingDTO> getComYyRatingMap(Timestamp issueTime, Set<Long> comUniCodeList) {
        if (CollectionUtils.isEmpty(comUniCodeList)) {
            return Collections.emptyMap();
        }
        ComYyRatingRequestDTO requestDTO = new ComYyRatingRequestDTO();
        requestDTO.setComUniCodeList(new ArrayList<>(comUniCodeList));
        requestDTO.setIssueTime(issueTime);
        return listComYyRatings(requestDTO).getComYyRatingDTOList().stream()
                .collect(Collectors.toMap(ComYyRatingDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }
}
