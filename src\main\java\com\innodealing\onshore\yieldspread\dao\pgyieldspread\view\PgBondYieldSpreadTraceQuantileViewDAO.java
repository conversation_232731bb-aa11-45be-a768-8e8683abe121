package com.innodealing.onshore.yieldspread.dao.pgyieldspread.view;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view.PgBondYieldSpreadTraceQuantileViewMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgBondYieldSpreadTraceQuantileViewDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 债券利差追踪分位数视图DAO
 *
 * <AUTHOR>
 */
@Component
public class PgBondYieldSpreadTraceQuantileViewDAO {

    @Resource
    private PgBondYieldSpreadTraceQuantileViewMapper pgBondYieldSpreadTraceQuantileViewMapper;

    /**
     * 创建利差追踪历史分位视图
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    public void createTraceQuantileView(Date startDate, Date endDate) {
        pgBondYieldSpreadTraceQuantileViewMapper.createTraceQuantileView(startDate.toString(), endDate.toString());
    }

    /**
     * 查询收益率全景分位数据
     *
     * @param issueDate 发行日期
     * @return {@link List}<{@link PgBondYieldSpreadTraceQuantileViewDO}> 利差追踪历史分位数据集
     */
    public List<PgBondYieldSpreadTraceQuantileViewDO> listBondYieldSpreadTraceQuantiles(Date issueDate) {
        DynamicQuery<PgBondYieldSpreadTraceQuantileViewDO> query = DynamicQuery.createQuery(PgBondYieldSpreadTraceQuantileViewDO.class)
                .and(PgBondYieldSpreadTraceQuantileViewDO::getIssueDate, isEqual(issueDate));
        return pgBondYieldSpreadTraceQuantileViewMapper.selectByDynamicQuery(query);
    }
}