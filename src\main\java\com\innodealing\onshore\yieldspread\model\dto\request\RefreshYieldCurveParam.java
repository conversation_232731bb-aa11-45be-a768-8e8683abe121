package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.yieldspread.builder.RefreshYieldCurveParamBuilder;

import java.sql.Date;
import java.util.Objects;

/**
 * 数据刷新参数
 *
 * <AUTHOR>
 */
public class RefreshYieldCurveParam {

    private Date startDate;
    private Date endDate;
    private Boolean mvRefresh;
    private Boolean tableRefresh;

    public boolean isMvRefresh() {
        return Boolean.TRUE.equals(mvRefresh);
    }

    public boolean isTableRefresh() {
        return Boolean.TRUE.equals(tableRefresh);
    }

    public void setTableRefresh(Boolean tableRefresh) {
        this.tableRefresh = tableRefresh;
    }

    public void setMvRefresh(Boolean mvRefresh) {
        this.mvRefresh = mvRefresh;
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public Date getEndDate() {
        return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public void setEndDate(Date endDate) {
        this.endDate = Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    /**
     * 获取物化视图参数构造器
     *
     * @return {@link com.innodealing.onshore.yieldspread.builder.RefreshYieldCurveParamBuilder} 物化视图参数构造器
     */
    public static RefreshYieldCurveParamBuilder builder() {
        return new RefreshYieldCurveParamBuilder();
    }

}
