package com.innodealing.onshore.yieldspread.processor;

import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.ONE_HUNDRED;

/**
 * 抽象利差追踪踪模板
 *
 * <AUTHOR>
 */
public interface YieldSpreadTraceProcessor {

    /**
     * 支持债券类型
     *
     * @param traceBondTypeEnum 利差追踪债券类型枚举类
     * @return {@link Boolean} 是否支持
     */
    Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum);

    /**
     * 处理利差追踪到期收益率
     *
     * @param context 上下文 利差追踪上下文
     * @return {@link List}<{@link PgBondYieldSpreadTraceAbsDO}> 利差追踪-绝对值数据
     */
    List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context);

    /**
     * 安全减法
     *
     * @param value1 value1
     * @param value2 value2
     * @return {@link BigDecimal}
     */
    default BigDecimal safeSubtract(BigDecimal value1, BigDecimal value2) {
        if (!ObjectUtils.allNotNull(value1, value2)) {
            return null;
        }
        return value1.subtract(value2).multiply(ONE_HUNDRED);
    }

    /**
     * 全景数据转换为等级利差数据对象
     *
     * @param sourceAbs     全景对象
     * @param compareAbs    最高评级对象
     * @param bondTypeEnum  债券类型
     * @param issueDate     发行时间
     * @param curveCode     曲线code
     * @param chartTypeEnum 图表类型
     * @return 等级利差数据值
     */
    default PgBondYieldSpreadTraceAbsDO convertAbsSubtractToTrace(PgBondYieldPanoramaAbsDO sourceAbs, PgBondYieldPanoramaAbsDO compareAbs,
                                                                  YieldPanoramaBondTypeEnum bondTypeEnum, Date issueDate,
                                                                  Integer curveCode, YieldSpreadChartTypeEnum chartTypeEnum) {
        PgBondYieldSpreadTraceAbsDO pgBondYieldSpreadTraceAbsDO = new PgBondYieldSpreadTraceAbsDO();
        pgBondYieldSpreadTraceAbsDO.setBondType(bondTypeEnum.getValue());
        pgBondYieldSpreadTraceAbsDO.setChartType(chartTypeEnum.getValue());
        pgBondYieldSpreadTraceAbsDO.setCurveCode(curveCode);
        pgBondYieldSpreadTraceAbsDO.setIssueDate(issueDate);
        pgBondYieldSpreadTraceAbsDO.setDeleted(Deleted.NO_DELETED.getValue());
        if (Objects.isNull(sourceAbs) || Objects.isNull(compareAbs)) {
            return pgBondYieldSpreadTraceAbsDO;
        }
        pgBondYieldSpreadTraceAbsDO.setYtm1M(this.safeSubtract(sourceAbs.getYtm1M(), compareAbs.getYtm1M()));
        pgBondYieldSpreadTraceAbsDO.setYtm3M(this.safeSubtract(sourceAbs.getYtm3M(), compareAbs.getYtm3M()));
        pgBondYieldSpreadTraceAbsDO.setYtm6M(this.safeSubtract(sourceAbs.getYtm6M(), compareAbs.getYtm6M()));
        pgBondYieldSpreadTraceAbsDO.setYtm9M(this.safeSubtract(sourceAbs.getYtm9M(), compareAbs.getYtm9M()));
        pgBondYieldSpreadTraceAbsDO.setYtm1Y(this.safeSubtract(sourceAbs.getYtm1Y(), compareAbs.getYtm1Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm2Y(this.safeSubtract(sourceAbs.getYtm2Y(), compareAbs.getYtm2Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm3Y(this.safeSubtract(sourceAbs.getYtm3Y(), compareAbs.getYtm3Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm5Y(this.safeSubtract(sourceAbs.getYtm5Y(), compareAbs.getYtm5Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm7Y(this.safeSubtract(sourceAbs.getYtm7Y(), compareAbs.getYtm7Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm10Y(this.safeSubtract(sourceAbs.getYtm10Y(), compareAbs.getYtm10Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm15Y(this.safeSubtract(sourceAbs.getYtm15Y(), compareAbs.getYtm15Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm20Y(this.safeSubtract(sourceAbs.getYtm20Y(), compareAbs.getYtm20Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm30Y(this.safeSubtract(sourceAbs.getYtm30Y(), compareAbs.getYtm30Y()));
        pgBondYieldSpreadTraceAbsDO.setYtm50Y(this.safeSubtract(sourceAbs.getYtm50Y(), compareAbs.getYtm50Y()));
        return pgBondYieldSpreadTraceAbsDO;
    }
}
