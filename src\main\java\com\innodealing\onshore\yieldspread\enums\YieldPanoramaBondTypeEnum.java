package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.innodealing.onshore.bondmetadata.enums.CurveCode.*;

/**
 * 利差全景债券类型
 *
 * <AUTHOR>
 */
@SuppressWarnings("squid:S2972")
public enum YieldPanoramaBondTypeEnum implements ITextValueEnum {
    /**
     * 债券品种  1 国债, 2 国开债, 3 地方政府债, 4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险
     */
    CHINA_BOND(1, "国债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getChinaBonds());
        }

        @Override
        public Integer getTopRating() {
            return null;
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getChinaBond());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-国债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.empty();
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return null;
        }
    },
    CD_BOND(2, "国开债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getChinaBondKais());
        }

        @Override
        public Integer getTopRating() {
            return null;
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getChinaKaiBond());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-国开债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.empty();
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return null;
        }
    },
    LOCAL_TREASURY_BOND(3, "地方政府债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getChinaBondLgbs());
        }

        @Override
        public Integer getTopRating() {
            return null;
        }

        @Override
        public Set<Integer> getChartTypes() {
            return Collections.emptySet();
        }

        @Override
        public String getTemplatePath() {
            return StringUtils.EMPTY;
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.empty();
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return null;
        }

    },
    MEDIUM_AND_SHORT_TERMS_NOTE(4, "中短期票据") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getMediumAndShortTermsNotes());
        }

        @Override
        public Integer getTopRating() {
            return CHINA_BOND_MID_AAA_PLUS.getValue();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getMediumAndShortTermsNotes());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-中短期票据模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodCbmEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_20402;
        }
    },
    INDUSTRIAL_BOND(5, "产业债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getIndustrialBonds());
        }

        @Override
        public Integer getTopRating() {
            return YieldSpreadCurveCodeEnum.CHINA_INDUSTRIAL_AAA.getValue();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getIndustrialBonds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-产业债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodInduEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_20502;
        }
    },
    URBAN_BOND(6, "城投") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getUrbanBonds());
        }

        @Override
        public Integer getTopRating() {
            return CHINA_CT_AAA.getValue();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getUrbanBonds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-城投债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodCtEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_20602;
        }
    },
    GENERAL_BANK_BOND(7, "银行普通债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getGeneralBankBonds());
        }

        @Override
        public Integer getTopRating() {
            return CHINA_BOND_ORD.getValue();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getGeneralBankBonds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-银行普通债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodBoEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_20702;
        }
    },
    BANK_SECONDARY_CAPITAL_BOND(8, "银行二级资本债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getBankSecondaryCapitalBonds());
        }

        @Override
        public Integer getTopRating() {
            return BANK_SECONDARY_CAPITAL_BOND_AAA_SUB.getValue();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getBankSecondaryCapitalBonds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-银行二级资本债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodBt2Enum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_20802;
        }
    },
    BANK_PERPETUAL_BOND(9, "银行永续债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getBankPerpetualBonds());
        }

        @Override
        public Integer getTopRating() {
            return BANK_PERPETUAL_BOND_AAA_SUB.getValue();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getBankPerpetualBonds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-银行永续债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodCtEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_20902;
        }
    },
    SECURITIES_BOND(10, "证券公司债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getSecuritiesBonds());
        }

        @Override
        public Integer getTopRating() {
            return SECURITIES_BOND_AAA.getValue();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getSecuritiesBonds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-证券公司债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodScEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_21002;
        }
    },
    SECURITIES_SUB_BOND(11, "证券次级债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getSecuritiesSubBonds());
        }

        @Override
        public Integer getTopRating() {
            return null;
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getSecuritiesSubBonds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-证券次级债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodSsEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_21102;
        }
    },
    SECURITIES_PERPETUAL_BOND(12, "证券永续债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getSecuritiesPerpetualBonds());
        }

        @Override
        public Integer getTopRating() {
            return null;
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getSecuritiesPerpetualBonds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-证券永续债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodSpEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_21202;
        }
    },
    NCD(13, "同业存单") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getNcds());
        }

        @Override
        public Integer getTopRating() {
            return NCD_AAA.getValue();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getNcds());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-同业存单模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.empty();
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return null;
        }
    },
    INSU_CAPITAL_SUPPLEMENT(14, "保险资本补充") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getInsuCapitalSupplements());
        }

        @Override
        public Integer getTopRating() {
            return CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA_PLUS.getCurveCode();
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>(YieldSpreadChartTypeEnum.getInsuCapitalSupplements());
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-保险资本补充模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodIcsEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_21402;
        }
    },
    CHINA_BOND_IMPORT(15, "进出口行债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(Collections.singletonList(YieldSpreadCurveCodeEnum.CHINA_BOND_IMPORT.getValue()));
        }

        @Override
        public Integer getTopRating() {
            return Integer.MAX_VALUE;
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>();
        }

        @Override
        public String getTemplatePath() {
            return "";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.empty();
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return null;
        }
    },
    CHINA_BOND_NO(16, "农发债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(Collections.singletonList(YieldSpreadCurveCodeEnum.CHINA_BOND_NO.getValue()));
        }

        @Override
        public Integer getTopRating() {
            return Integer.MAX_VALUE;
        }

        @Override
        public Set<Integer> getChartTypes() {
            return new HashSet<>();
        }

        @Override
        public String getTemplatePath() {
            return "";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.empty();
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return null;
        }
    },
    INTEREST_RATE_BOND(17, "利率债") {
        @Override
        public Set<Integer> getCurveCodes() {
            return new HashSet<>(YieldSpreadCurveCodeEnum.getInterestRateBonds());
        }

        @Override
        public Integer getTopRating() {
            return Integer.MAX_VALUE;
        }

        @Override
        public Set<Integer> getChartTypes() {
            return YieldSpreadChartTypeEnum.getInterestRateBond();
        }

        @Override
        public String getTemplatePath() {
            return "/static/template/excel/利差追踪-利率债模板.xlsx";
        }

        @Override
        public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
            return Optional.of(TracePeriodIrEnum.class);
        }

        @Override
        public UserConfigEnum getConfigEnum() {
            return UserConfigEnum.CONFIG_21702;
        }
    };

    private final Integer code;
    private final String text;

    private static final EnumSet<YieldPanoramaBondTypeEnum> TRACE_BOND_TYPE_ENUMS;
    private static final EnumSet<YieldPanoramaBondTypeEnum> HAS_PERMISSION_BOND_TYPE_ENUMS;

    static {
        TRACE_BOND_TYPE_ENUMS = EnumSet.of(CHINA_BOND, CD_BOND, MEDIUM_AND_SHORT_TERMS_NOTE, INDUSTRIAL_BOND, URBAN_BOND,
                GENERAL_BANK_BOND, BANK_SECONDARY_CAPITAL_BOND, BANK_PERPETUAL_BOND, SECURITIES_BOND, SECURITIES_SUB_BOND,
                SECURITIES_PERPETUAL_BOND, NCD, INSU_CAPITAL_SUPPLEMENT, INTEREST_RATE_BOND);
        HAS_PERMISSION_BOND_TYPE_ENUMS = EnumSet.of(INDUSTRIAL_BOND);
    }

    YieldPanoramaBondTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }

    public Set<Integer> getCurveCodes() {
        throw new AbstractMethodError();
    }

    public Integer getTopRating() {
        throw new AbstractMethodError();
    }

    public Set<Integer> getChartTypes() {
        throw new AbstractMethodError();
    }

    public String getTemplatePath() {
        throw new AbstractMethodError();
    }

    public UserConfigEnum getConfigEnum() {
        throw new AbstractMethodError();
    }

    public Optional<Class<? extends ITracePeriodCommonEnum>> getTracePeriodEnum() {
        throw new AbstractMethodError();
    }

    /**
     * 获取利差追踪所需要的债券类型
     *
     * @return {@link Set}<{@link YieldPanoramaBondTypeEnum}> 债券类型枚举
     */
    public static Set<YieldPanoramaBondTypeEnum> getTraceBondTypeEnums() {
        return EnumSet.copyOf(TRACE_BOND_TYPE_ENUMS);
    }

    /**
     * 获取有数据权限 的债券类型
     *
     * @return {@link Set}<{@link YieldPanoramaBondTypeEnum}> 债券类型枚举
     */
    public static Set<YieldPanoramaBondTypeEnum> getHasPermissionBondTypeEnums() {
        return EnumSet.copyOf(HAS_PERMISSION_BOND_TYPE_ENUMS);
    }


}
