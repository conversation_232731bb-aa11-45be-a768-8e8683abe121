package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.service.CalcYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.dmdc.InterestDataMigrateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;

/**
 * (内部)利差老数据迁移
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 利差老数据迁移")
@RestController
@RequestMapping("internal/interest")
public class InternalYieldSpreadMigrateController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private InterestDataMigrateService interestDataMigrateService;

    @ApiOperation(value = "迁移所有利差数据")
    @PostMapping("interestDataMigrate")
    public int interestDataMigrate(@ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
                                   @ApiParam(name = "endDate", value = "截止日期", required = true) @RequestParam Date endDate) {
        int effectRows = 0;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        effectRows += interestDataMigrateService.comInterestInduHistMigrate(startDate, endDate);
        effectRows += interestDataMigrateService.comInterestCtzHistMigrate(startDate, endDate);
        effectRows += interestDataMigrateService.bondInterestInduMigrate(startDate, endDate);
        effectRows += interestDataMigrateService.bondInterestCtzMigrate(startDate, endDate);
        logger.info("[interestDataMigrate] effectRows:{} stopWatch: {}", effectRows, stopWatch.getTime());
        return effectRows;
    }

    @ApiOperation(value = "迁移城投债利差数据")
    @PostMapping("bondInterestCtzMigrate")
    public int bondInterestCtzMigrate(@ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
                                      @ApiParam(name = "endDate", value = "截止日期", required = true) @RequestParam Date endDate) {
        return interestDataMigrateService.bondInterestCtzMigrate(startDate, endDate);
    }

    @ApiOperation(value = "迁移行业债利差数据")
    @PostMapping("bondInterestInduMigrate")
    public int bondInterestInduMigrate(@ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
                                       @ApiParam(name = "endDate", value = "截止日期", required = true) @RequestParam Date endDate) {
        return interestDataMigrateService.bondInterestInduMigrate(startDate, endDate);
    }

    @ApiOperation(value = "迁移行业主体利差数据")
    @PostMapping("comInterestInduHistMigrate")
    public int comInterestInduHistMigrate(@ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
                                          @ApiParam(name = "endDate", value = "截止日期", required = true) @RequestParam Date endDate) {
        return interestDataMigrateService.comInterestInduHistMigrate(startDate, endDate);
    }

    @ApiOperation(value = "迁移城投主体利差数据")
    @PostMapping("comInterestCtzHistMigrate")
    public int comInterestCtzHistMigrate(@ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
                                         @ApiParam(name = "endDate", value = "截止日期", required = true) @RequestParam Date endDate) {
        return interestDataMigrateService.comInterestCtzHistMigrate(startDate, endDate);
    }

    @Resource
    private CalcYieldSpreadService calcYieldSpreadService;

    @ApiOperation(value = "根据日期计算行业利差(债券,主体) (不传日期默认跑前一天的数据)")
    @PostMapping("/calcInduBondYieldSpreadsBySpreadDate")
    public int calcInduBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList,
            @ApiParam(name = "isEnableOldData", value = "isEnableOldData 是否启用老数据")
            @RequestParam(defaultValue = "false") Boolean isEnableOldData) {
        return calcYieldSpreadService.calcInduBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList, isEnableOldData);
    }

    @ApiOperation(value = "根据日期计算城投利差(债券,主体)(不传日期默认跑前一天的数据)")
    @PostMapping("/calcUdicBondYieldSpreadsBySpreadDate")
    public int calcUdicBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList,
            @ApiParam(name = "isEnableOldData", value = "isEnableOldData 是否启用老数据")
            @RequestParam(defaultValue = "false") Boolean isEnableOldData) {
        return calcYieldSpreadService.calcUdicBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList, isEnableOldData);
    }
}
