<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvSecuBondYieldSpreadCurveMapper">


    <sql id="ratingRouterQuerySql">
        SELECT max(secu_bond_yield_spread.id) AS id,
        MEDIAN((secu_bond_yield_spread.bond_credit_spread * 100000::numeric)::bigint) AS bond_credit_spread,
        MEDIAN((secu_bond_yield_spread.bond_excess_spread * 100000::numeric)::bigint) AS bond_excess_spread,
        MEDIAN((secu_bond_yield_spread.cb_yield * 100000::numeric)::bigint) AS cb_yield,
        AVG(((secu_bond_yield_spread.bond_credit_spread * (100000)::numeric))::bigint) AS avg_bond_credit_spread,
        AVG(((secu_bond_yield_spread.bond_excess_spread * (100000)::numeric))::bigint) AS avg_bond_excess_spread,
        AVG(((secu_bond_yield_spread.cb_yield * (100000)::numeric))::bigint) AS avg_cb_yield,
        count(secu_bond_yield_spread.bond_credit_spread) AS bond_credit_spread_count,
        count(secu_bond_yield_spread.bond_excess_spread) AS bond_excess_spread_count,
        count(secu_bond_yield_spread.cb_yield) AS cb_yield_count,
        secu_bond_yield_spread.spread_date,
        secu_bond_yield_spread.security_seniority_ranking,
        secu_bond_yield_spread.spread_remaining_tenor_tag,
        grouping(secu_bond_yield_spread.security_seniority_ranking) AS using_security_seniority_ranking,
        grouping(secu_bond_yield_spread.spread_remaining_tenor_tag) AS using_spread_remaining_tenor_tag
        FROM yield_spread.secu_bond_yield_spread
        where 1=1
        <if test="parameter.spreadDateRange == null">
            AND secu_bond_yield_spread.spread_date = (('now'::text)::date - 1)
        </if>
        <if test="parameter.spreadDateRange != null">
            AND secu_bond_yield_spread.spread_date BETWEEN '${parameter.spreadDateRange.startDate}' and
            '${parameter.spreadDateRange.endDate}'
        </if>
        <if test="parameter.impliedRatingMappings != null and parameter.impliedRatingMappings.size() > 0">
            AND secu_bond_yield_spread.bond_implied_rating_mapping in
            <foreach collection="parameter.impliedRatingMappings" item="item" open="(" close=")" separator=",">
                '${item}'
            </foreach>
        </if>
        GROUP BY secu_bond_yield_spread.spread_date, CUBE ( secu_bond_yield_spread.security_seniority_ranking,
        secu_bond_yield_spread.spread_remaining_tenor_tag)
    </sql>

    <update id="createMvRatingRouter">
        create MATERIALIZED view yield_spread.${parameter.tableName}
        WITH (appendoptimized= true, orientation = column)
        as
        <include refid="ratingRouterQuerySql"></include>
    </update>


</mapper>