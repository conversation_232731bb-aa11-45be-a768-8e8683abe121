package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgBondYieldPanoramaQuantileViewDO;

import java.util.List;


/**
 * 债券收益率全景分位数视图Mapper
 *
 * <AUTHOR>
 */
public interface PgBondYieldPanoramaQuantileViewMapper extends DynamicQueryMapper<PgBondYieldPanoramaQuantileViewDO> {

    /**
     * 创建或替换视图
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    void createPanoramaQuantileView(String startDate, String endDate, List<Integer> curveCodeList);

}
