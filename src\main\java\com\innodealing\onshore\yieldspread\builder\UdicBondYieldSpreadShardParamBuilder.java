package com.innodealing.onshore.yieldspread.builder;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveShardEnum;
import com.alibaba.fastjson.JSONObject;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondShardYieldSpreadParamDTO;

import java.util.Objects;

/**
 * 城投利差曲线分片查询参数构造器
 *
 * <AUTHOR>
 */
public class UdicBondYieldSpreadShardParamBuilder implements ShardParamBuilder {

    protected final UdicBondShardYieldSpreadParamDTO paramDTO;

    /**
     * 构造方法
     */
    public UdicBondYieldSpreadShardParamBuilder() {
        paramDTO = new UdicBondShardYieldSpreadParamDTO();
    }

    @Override
    public UdicBondYieldSpreadShardParamBuilder extra(JSONObject jsonObject) {
        return this;
    }

    @Override
    public UdicBondYieldSpreadShardParamBuilder searchQueryParam(Object dto) {
        if (Objects.isNull(dto)) {
            return this;
        }
        BeanCopyUtils.copyProperties(dto, paramDTO);
        return this;
    }

    @Override
    public UdicBondYieldSpreadShardParamBuilder level(YieldSpreadCurveShardEnum shardEnum) {
        this.paramDTO.setLevel(shardEnum.getText());
        return this;
    }

    @Override
    public UdicBondShardYieldSpreadParamDTO build() {
        return paramDTO;
    }
}
