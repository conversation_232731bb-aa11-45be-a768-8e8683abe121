package com.innodealing.onshore.yieldspread.mapper.pgsharding.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.InduShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.ibatis.annotations.Param;
import org.apache.shardingsphere.api.hint.HintManager;

import javax.persistence.Table;
import java.util.List;

/**
 * 行业利差曲线-物化视图
 *
 * <AUTHOR>
 */

public interface InduShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<InduShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     * @return tableName
     */
    default String getLogicTable() {
        return InduShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }

    /**
     * 产业利差曲线数量查询
     * @param dynamicQuery 查询条件
     * @param router 路由
     * @return count
     */
    default int selectCountByDynamicQuery(@Param("dynamicQuery") DynamicQuery<InduShardBondYieldSpreadCurveDO> dynamicQuery, AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectCountByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 产业利差曲线查询
     * @param dynamicQuery 查询条件
     * @param router 路由
     * @return list
     */
    default List<InduShardBondYieldSpreadCurveDO> selectByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<InduShardBondYieldSpreadCurveDO> dynamicQuery,
                                                                             AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 产业利差曲线删除
     * @param dynamicQuery 匹配条件
     * @param router 路由
     * @return 数量
     */
    default int deleteByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<InduShardBondYieldSpreadCurveDO> dynamicQuery, AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return deleteByDynamicQuery(dynamicQuery);
        }
    }
}
