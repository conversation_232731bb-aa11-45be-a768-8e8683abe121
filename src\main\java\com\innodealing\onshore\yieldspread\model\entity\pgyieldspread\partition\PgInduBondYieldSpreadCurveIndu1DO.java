package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 行业债利差曲线分区表(包含行业1)
 *
 * <AUTHOR>
 */
@Table(name = "indu_bond_yield_spread_curve_indu1")
public class PgInduBondYieldSpreadCurveIndu1DO extends BasePgInduBondYieldSpreadCurveDO {
    /**
     * 一级行业编码
     */
    @Column
    private Long induLevel1Code;

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }
}