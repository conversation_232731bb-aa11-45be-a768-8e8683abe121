package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * yy评级标签标签
 *
 * <AUTHOR>
 */
public enum ComYyRatingMappingTagTypeEnum implements ITextValueEnum {
    /**
     * 投资级
     */
    INVEST_LEVEL(1, "投资级"),
    /**
     * 投机级
     */
    SPECULATES_LEVEL(2, "投机级"),
    /**
     * 其他
     */
    OTHER(99, "其他");

    private Integer code;
    private String text;

    ComYyRatingMappingTagTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }
}
