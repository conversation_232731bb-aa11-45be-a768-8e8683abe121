package com.innodealing.onshore.yieldspread.model.dto.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import java.math.BigDecimal;

/**
 * 利差变动数据
 *
 * <AUTHOR>
 */
public class YieldSpreadTraceDataExportExcelDTO {

    @ExcelProperty("typeName")
    private String typeName;

    @ExcelProperty("1M")
    private BigDecimal yield1M;
    @ExcelProperty("3M")
    private BigDecimal yield3M;
    @ExcelProperty("6M")
    private BigDecimal yield6M;
    @ExcelProperty("9M")
    private BigDecimal yield9M;
    @ExcelProperty("1Y")
    private BigDecimal yield1Y;
    @ExcelProperty("2Y")
    private BigDecimal yield2Y;
    @ExcelProperty("3Y")
    private BigDecimal yield3Y;
    @ExcelProperty("5Y")
    private BigDecimal yield5Y;
    @ExcelProperty("7Y")
    private BigDecimal yield7Y;
    @ExcelProperty("10Y")
    private BigDecimal yield10Y;
    @ExcelProperty("15Y")
    private BigDecimal yield15Y;
    @ExcelProperty("20Y")
    private BigDecimal yield20Y;
    @ExcelProperty("30Y")
    private BigDecimal yield30Y;
    @ExcelProperty("50Y")
    private BigDecimal yield50Y;
    @ExcelIgnore
    private Integer sort;


    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public BigDecimal getYield1Y() {
        return yield1Y;
    }

    public void setYield1Y(BigDecimal yield1Y) {
        this.yield1Y = yield1Y;
    }

    public BigDecimal getYield2Y() {
        return yield2Y;
    }

    public void setYield2Y(BigDecimal yield2Y) {
        this.yield2Y = yield2Y;
    }

    public BigDecimal getYield3Y() {
        return yield3Y;
    }

    public void setYield3Y(BigDecimal yield3Y) {
        this.yield3Y = yield3Y;
    }

    public BigDecimal getYield5Y() {
        return yield5Y;
    }

    public void setYield5Y(BigDecimal yield5Y) {
        this.yield5Y = yield5Y;
    }

    public BigDecimal getYield7Y() {
        return yield7Y;
    }

    public void setYield7Y(BigDecimal yield7Y) {
        this.yield7Y = yield7Y;
    }

    public BigDecimal getYield1M() {
        return yield1M;
    }

    public void setYield1M(BigDecimal yield1M) {
        this.yield1M = yield1M;
    }

    public BigDecimal getYield3M() {
        return yield3M;
    }

    public void setYield3M(BigDecimal yield3M) {
        this.yield3M = yield3M;
    }

    public BigDecimal getYield6M() {
        return yield6M;
    }

    public void setYield6M(BigDecimal yield6M) {
        this.yield6M = yield6M;
    }

    public BigDecimal getYield9M() {
        return yield9M;
    }

    public void setYield9M(BigDecimal yield9M) {
        this.yield9M = yield9M;
    }

    public BigDecimal getYield10Y() {
        return yield10Y;
    }

    public void setYield10Y(BigDecimal yield10Y) {
        this.yield10Y = yield10Y;
    }

    public BigDecimal getYield15Y() {
        return yield15Y;
    }

    public void setYield15Y(BigDecimal yield15Y) {
        this.yield15Y = yield15Y;
    }

    public BigDecimal getYield20Y() {
        return yield20Y;
    }

    public void setYield20Y(BigDecimal yield20Y) {
        this.yield20Y = yield20Y;
    }

    public BigDecimal getYield30Y() {
        return yield30Y;
    }

    public void setYield30Y(BigDecimal yield30Y) {
        this.yield30Y = yield30Y;
    }

    public BigDecimal getYield50Y() {
        return yield50Y;
    }

    public void setYield50Y(BigDecimal yield50Y) {
        this.yield50Y = yield50Y;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
