package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.sql.Date;
import java.util.Objects;

/**
 * 地方债区域利差日期范围参数的参数
 *
 * <AUTHOR>
 */
public class LgSpreadDateRangeRequestDTO extends LgSpreadBaseRequestDTO {

    @ApiModelProperty(value = "自定义范围开始时间")
    private Date startDate;

    @ApiModelProperty(value = "自定义范围结束时间")
    private Date endDate;


    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public Date getEndDate() {
        return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public void setEndDate(Date endDate) {
        this.endDate = Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }
}
