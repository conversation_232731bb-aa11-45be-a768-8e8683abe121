package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.yieldspread.model.dto.response.CcsCfetsRepurchaseFlowResponseDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Date;
import java.util.List;

/**
 * 资金面服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "capitalSideService", url = "${onshore.capital.side.url}", path = "/internal")
public interface CapitalSideService {

    /**
     * 查询收盘数据
     *
     * @param repurchaseSource 回购来源
     * @param startDate        开始日期
     * @param endDate          结束日期
     * @return {@link List}<{@link CcsCfetsRepurchaseFlowResponseDTO}>
     */
    @ApiOperation(("按照时间范围获取资金综合屏-cfets回购收盘历史数据"))
    @PostMapping("/ccsCfets/repurchase/list/history")
     List<CcsCfetsRepurchaseFlowResponseDTO> listBetweenDateHistoryCloses(
            @ApiParam(name = "startDate", value = "开始时间", required = true)
            @RequestParam(value = "startDate", defaultValue = "2023-06-09") Date startDate,
            @ApiParam(name = "endDate", value = "结束时间", required = true)
            @RequestParam(value = "endDate", defaultValue = "2024-12-31") Date endDate,
            @ApiParam(name = "repurchaseSource", value = "回购来源(默认 银行间(R)=5)", required = true)
            @RequestParam(value = "repurchaseSource", defaultValue = "5") Integer repurchaseSource);

}
