<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBondYieldSpreadTraceAbsMapper">
    <select id="listTraceQuantileStatisticsViews"
            resultType="com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgSpreadTraceQuantileStatisticsViewDO">
        SELECT t1.chart_type,
               t1.curve_code,
               CASE
                   WHEN COUNT(t2.ytm_1M) > 0 THEN COUNT(t1.ytm_1M &lt; t2.ytm_1M or null)
                   ELSE NULL END                                                    ytm1MLessIssueCount,
               CASE WHEN COUNT(t2.ytm_1M) > 0 THEN COUNT(t1.ytm_1M) ELSE NULL END   ytm1MCount,
               CASE
                   WHEN COUNT(t2.ytm_3M) > 0 THEN COUNT(t1.ytm_3M &lt; t2.ytm_3M or null)
                   ELSE NULL END                                                    ytm3MLessIssueCount,
               CASE WHEN COUNT(t2.ytm_3M) > 0 THEN COUNT(t1.ytm_3M) ELSE NULL END   ytm3MCount,
               CASE
                   WHEN COUNT(t2.ytm_6M) > 0 THEN COUNT(t1.ytm_6M &lt; t2.ytm_6M or null)
                   ELSE NULL END                                                    ytm6MLessIssueCount,
               CASE WHEN COUNT(t2.ytm_6M) > 0 THEN COUNT(t1.ytm_6M) ELSE NULL END   ytm6MCount,
               CASE
                   WHEN COUNT(t2.ytm_9M) > 0 THEN COUNT(t1.ytm_9M &lt; t2.ytm_9M or null)
                   ELSE NULL END                                                    ytm9MLessIssueCount,
               CASE WHEN COUNT(t2.ytm_9M) > 0 THEN COUNT(t1.ytm_9M) ELSE NULL END   ytm9MCount,
               CASE
                   WHEN COUNT(t2.ytm_1y) > 0 THEN COUNT(t1.ytm_1y &lt; t2.ytm_1y or null)
                   ELSE NULL END                                                    ytm1YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_1y) > 0 THEN COUNT(t1.ytm_1y) ELSE NULL END   ytm1YCount,
               CASE
                   WHEN COUNT(t2.ytm_2y) > 0 THEN COUNT(t1.ytm_2y &lt; t2.ytm_2y or null)
                   ELSE NULL END                                                    ytm2YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_2y) > 0 THEN COUNT(t1.ytm_2y) ELSE NULL END   ytm2YCount,
               CASE
                   WHEN COUNT(t2.ytm_3y) > 0 THEN COUNT(t1.ytm_3y &lt; t2.ytm_3y or null)
                   ELSE NULL END                                                    ytm3YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_3y) > 0 THEN COUNT(t1.ytm_3y) ELSE NULL END   ytm3YCount,
               CASE
                   WHEN COUNT(t2.ytm_5y) > 0 THEN COUNT(t1.ytm_5y &lt; t2.ytm_5y or null)
                   ELSE NULL END                                                    ytm5YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_5y) > 0 THEN COUNT(t1.ytm_5y) ELSE NULL END   ytm5YCount,
               CASE
                   WHEN COUNT(t2.ytm_7y) > 0 THEN COUNT(t1.ytm_7y &lt; t2.ytm_7y or null)
                   ELSE NULL END                                                    ytm7YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_7y) > 0 THEN COUNT(t1.ytm_7y) ELSE NULL END   ytm7YCount,
               CASE
                   WHEN COUNT(t2.ytm_10y) > 0 THEN COUNT(t1.ytm_10y &lt; t2.ytm_10y or null)
                   ELSE NULL END                                                    ytm10YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_10y) > 0 THEN COUNT(t1.ytm_10y) ELSE NULL END ytm10YCount,
               CASE
                   WHEN COUNT(t2.ytm_15y) > 0 THEN COUNT(t1.ytm_15y &lt; t2.ytm_15y or null)
                   ELSE NULL END                                                    ytm15YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_15y) > 0 THEN COUNT(t1.ytm_15y) ELSE NULL END ytm15YCount,
               CASE
                   WHEN COUNT(t2.ytm_20y) > 0 THEN COUNT(t1.ytm_20y &lt; t2.ytm_20y or null)
                   ELSE NULL END                                                    ytm20YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_20y) > 0 THEN COUNT(t1.ytm_20y) ELSE NULL END ytm20YCount,
               CASE
                   WHEN COUNT(t2.ytm_30y) > 0 THEN COUNT(t1.ytm_30y &lt; t2.ytm_30y or null)
                   ELSE NULL END                                                    ytm30YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_30y) > 0 THEN COUNT(t1.ytm_30y) ELSE NULL END ytm30YCount,
               CASE
                   WHEN COUNT(t2.ytm_50y) > 0 THEN COUNT(t1.ytm_50y &lt; t2.ytm_50y or null)
                   ELSE NULL END                                                    ytm50YLessIssueCount,
               CASE WHEN COUNT(t2.ytm_50y) > 0 THEN COUNT(t1.ytm_50y) ELSE NULL END ytm50YCount
        FROM bond_yield_spread_trace_abs as t1
                 INNER JOIN bond_yield_spread_trace_abs t2
                            ON t1.bond_type = t2.bond_type AND t1.curve_code = t2.curve_code AND
                               t1.chart_type = t2.chart_type AND t2.issue_date = #{issueDate} AND t2.deleted = 0
        WHERE t1.issue_date >= #{startDate}
          AND t1.issue_date &lt;= #{endDate}
          AND t1.bond_type = #{bondType}
          AND t1.deleted = 0
        GROUP BY t1.chart_type, t1.curve_code
    </select>
</mapper>