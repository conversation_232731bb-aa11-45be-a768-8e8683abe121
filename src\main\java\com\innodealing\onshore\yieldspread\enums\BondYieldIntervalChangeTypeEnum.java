package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import org.springframework.lang.NonNull;

import java.sql.Date;
import java.time.LocalDate;

/**
 * 债券收益率区间变动 类型 1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动
 *
 * <AUTHOR>
 */
public enum BondYieldIntervalChangeTypeEnum implements ITextValueEnum {
    /**
     * 债券收益率区间变动 类型 1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动
     */
    ONE_WEEK_CHANGE(1, "一周", Constants.THREE_DAYS) {
        @Override
        public LocalDate getIntervalStartDate(@NonNull LocalDate issueDate) {
            return issueDate.minusDays(Constants.SIX_DAYS);
        }
    },
    ONE_MONTH_CHANGE(2, "一月", Constants.SEVEN_DAYS) {
        @Override
        public LocalDate getIntervalStartDate(@NonNull LocalDate issueDate) {
            return issueDate.minusMonths(Constants.ONE_MONTH);
        }
    },
    THREE_MONTH_CHANGE(3, "三月", Constants.SEVEN_DAYS) {
        @Override
        public LocalDate getIntervalStartDate(@NonNull LocalDate issueDate) {
            return issueDate.minusMonths(Constants.THREE_MONTHS);
        }
    },
    SIX_MONTH_CHANGE(4, "六月", Constants.SEVEN_DAYS) {
        @Override
        public LocalDate getIntervalStartDate(@NonNull LocalDate issueDate) {
            return issueDate.minusMonths(Constants.SIX_MONTHS);
        }
    },
    ONE_YEAR_CHANGE(5, "一年", Constants.SEVEN_DAYS) {
        @Override
        public LocalDate getIntervalStartDate(@NonNull LocalDate issueDate) {
            return issueDate.minusYears(Constants.ONE_YEAR);
        }
    },
    ONE_DAY_CHANGE(6, "一日", Constants.THREE_DAYS) {
        @Override
        public LocalDate getIntervalStartDate(@NonNull LocalDate issueDate) {
            return issueDate.minusDays(Constants.ONE_DAY);
        }
    },
    TWO_WEEK_CHANGE(7, "两周", Constants.THREE_DAYS) {
        @Override
        public LocalDate getIntervalStartDate(@NonNull LocalDate issueDate) {
            return issueDate.minusDays(Constants.THIRTEEN_DAYS);
        }
    };

    private final int value;
    private final String text;
    private final int minusDays;

    BondYieldIntervalChangeTypeEnum(Integer value, String text, int minusDays) {
        this.value = value;
        this.text = text;
        this.minusDays = minusDays;
    }

    /**
     * 获取区间变动最小的开始日期
     *
     * @param issueDate 发行日期
     * @return {@link Date} 最小的开始日期
     */
    public Date getIntervalMinStartDate(@NonNull LocalDate issueDate) {
        return Date.valueOf(issueDate.minusDays(minusDays));
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }

    /**
     * 得到区间开始日期
     *
     * @param issueDate 发行日期
     * @return {@link LocalDate} 区间的开始日期
     */
    public abstract LocalDate getIntervalStartDate(@NonNull LocalDate issueDate);

    /**
     * 获取默认变动类型
     *
     * @return {@link BondYieldIntervalChangeTypeEnum} 变动类型
     */
    public static BondYieldIntervalChangeTypeEnum getDefaultChangeType() {
        return ONE_DAY_CHANGE;
    }

    /**
     * 常量
     *
     * <AUTHOR>
     */
    private static class Constants {
        public static final int THREE_DAYS = 3;
        public static final int SEVEN_DAYS = 7;
        public static final int SIX_DAYS = 6;
        public static final int ONE_MONTH = 1;
        public static final int THREE_MONTHS = 3;
        public static final int SIX_MONTHS = 6;
        public static final int ONE_YEAR = 1;
        public static final int ONE_DAY = 1;
        public static final int THIRTEEN_DAYS = 13;
    }
}
