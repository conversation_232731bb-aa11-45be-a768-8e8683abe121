package com.innodealing.onshore.yieldspread.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.helper.CommonsHelper;
import com.google.common.collect.Sets;
import com.innodealing.commons.encrypt.MD5Utils;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.constant.MqTags;
import com.innodealing.onshore.bondmetadata.constant.MqTopics;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondCodeDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondNameToCodeDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondShortInfoDTO;
import com.innodealing.onshore.bondmetadata.enums.ImplicitRatingTagEnum;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.consts.TipsConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgYieldSpreadCompositeSearchDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserCurveDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.YieldSpreadCompositeSearchDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.redis.UserCurveRedisDAO;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.enums.CurveGenerateStatusEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadCodeTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.CommonUtils;
import com.innodealing.onshore.yieldspread.helper.LocalCache;
import com.innodealing.onshore.yieldspread.model.bo.*;
import com.innodealing.onshore.yieldspread.model.dto.CustomYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.CustomCurveGenerateReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.ICurveGenerateReq;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserCurveDO;
import com.innodealing.onshore.yieldspread.service.CustomCurveService;
import com.innodealing.onshore.yieldspread.service.internal.BondInfoService;
import com.innodealing.onshore.yieldspread.service.internal.ComService;
import com.innodealing.onshore.yieldspread.service.internal.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RPermitExpirableSemaphore;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.FOUR_DECIMAL_PLACE;

/**
 * 自定义曲线
 *
 * <AUTHOR>
 */
@Service
public class CustomCurveServiceImpl extends AbstractBondCurveService implements CustomCurveService, InitializingBean {

    private static final String DESTINATION = String.format("%s:%s", MqTopics.ONSHORE_YIELD_SPREAD, MqTags.ONSHORE_YIELD_SPREAD_CUSTOM_CURVE_GENERATE);

    private static final SortDTO DEFAULT_SORT = new SortDTO(CommonsHelper.getPropertyName(BankBondYieldSpreadDO::getBondCreditSpread), SortDirection.DESC);

    @Resource
    private UserCurveDAO userCurveDAO;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private UserCurveRedisDAO userCurveRedisDAO;

    @Resource
    private PgYieldSpreadCompositeSearchDAO pgYieldSpreadCompositeSearchDAO;

    @Resource
    private YieldSpreadCompositeSearchDAO yieldSpreadCompositeSearchDAO;

    @Resource
    protected ComService comService;

    @Resource
    private LocalCache localCache;

    @Resource
    private UserService userService;

    @Override
    public boolean curveNameIsDuplicate(Long userid, String curveName) {
        return userCurveDAO.isExist(userid, curveName);
    }

    @Override
    public Boolean saveCurve(Long userid, Long curveGroupId, CustomCurveGenerateReqDTO customCurveSaveReq) {
        return super.generalSaveCurve(userid, curveGroupId, customCurveSaveReq);

    }

    @Override
    protected void specialCheckBeforeSave(Long userid, ICurveGenerateReq params) {
        int generatingCurveNum = userCurveDAO.countUserCurve(userid, Arrays.asList(CurveGenerateStatusEnum.WAITING, CurveGenerateStatusEnum.GENERATING));
        if (generatingCurveNum >= YieldSpreadConst.GENERATING_CURVE_UPPER_LIMIT) {
            throw new TipsException(TipsConst.GENERATING_CURVE_UPPER_LIMIT_REMIND);
        }
    }

    @Override
    protected void modifyProperty(ICurveGenerateReq params, UserCurveDO userCurveDO) {
        CustomCurveGenerateReqDTO saveReqDTO = (CustomCurveGenerateReqDTO) params;
        userCurveDO.setFilterCondition("");
        userCurveDO.setSpreadCurveData("");
        userCurveDO.setGenerateStatus(CurveGenerateStatusEnum.WAITING.getValue());
        userCurveDO.setImportedBond(JSON.toJSONString(saveReqDTO.getBondUniCodes()));
    }

    @Override
    protected void doOtherBusinessAfterSave(UserCurveDO userCurveDO) {
        sendMessageForGenerate(userCurveDO);
    }

    private void sendMessageForGenerate(UserCurveDO userCurveDO) {
        SendStatus sendStatus = rocketMQTemplate.syncSend(DESTINATION, userCurveDO.getId()).getSendStatus();
        if (!SendStatus.SEND_OK.equals(sendStatus)) {
            logger.error("custom curve sync send to rocketMQ error.curveId:{},sendStatus is:{}", userCurveDO.getId(), sendStatus);
            UserCurveDO newUserCurveDO = new UserCurveDO();
            newUserCurveDO.setId(userCurveDO.getId());
            newUserCurveDO.setGenerateStatus(CurveGenerateStatusEnum.FAILED.getValue());
            userCurveDAO.updateByPrimaryKeySelective(newUserCurveDO);
        }
    }

    @Override
    public List<CustomCurveBasicInfoResDTO> listCustomCurves(Long userid, Set<Integer> generateStatus) {
        Set<CurveGenerateStatusEnum> generateStatusSet = null;
        if (CollectionUtils.isNotEmpty(generateStatus)) {
            generateStatusSet = generateStatus.stream().map(s -> EnumUtils.getEnumNullable(CurveGenerateStatusEnum.class, s))
                    .filter(Objects::nonNull).collect(Collectors.toSet());
        }
        return BeanCopyUtils.copyList(userCurveDAO.listCustomCurves(userid, generateStatusSet), CustomCurveBasicInfoResDTO.class);
    }

    @Override
    public boolean regenerate(Long userid, Long curveId) {
        CurveDefinitionBO curveBO = userCurveDAO.getCurveDefinitionBO(curveId).orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        super.checkBelongAndCurveType(userid, curveBO.getUserId(), curveBO.getSpreadCurveType());
        if (!Objects.equals(CurveTypeEnum.CUSTOMIZATION.getValue(), curveBO.getSpreadCurveType())) {
            throw new TipsException("不是自定义曲线不能重新生成!");
        }
        if (!curveBO.getGenerateStatus().equals(CurveGenerateStatusEnum.FAILED.getValue())) {
            throw new TipsException("曲线处于" + EnumUtils.getEnum(CurveGenerateStatusEnum.class, curveBO.getGenerateStatus()).getText() + "状态,不能进行重新生成");
        }
        UserCurveDO userCurve = new UserCurveDO();
        userCurve.setId(curveId);
        userCurve.setGenerateStatus(CurveGenerateStatusEnum.WAITING.getValue());
        boolean flag = userCurveDAO.updateByPrimaryKeySelective(userCurve);
        if (flag) {
            SendStatus sendStatus = rocketMQTemplate.syncSend(DESTINATION, curveId).getSendStatus();
            if (!SendStatus.SEND_OK.equals(sendStatus)) {
                logger.error("Regenerate_{}, custom curve sync send to rocketMQ error.sendStatus is:{}", curveId, sendStatus);
                userCurve.setGenerateStatus(CurveGenerateStatusEnum.FAILED.getValue());
                userCurveDAO.updateByPrimaryKeySelective(userCurve);
                return Boolean.FALSE;
            }
        }
        return true;
    }

    @Override
    public boolean cancelGenerate(Long userid, Long curveId) {
        return curvePoolService.removeCurve(userid, curveId);
    }

    @Override
    public boolean update(Long userid, Long curveId, CustomCurveGenerateReqDTO param) {
        return super.generalUpdateCurve(userid, curveId, param);
    }

    @Override
    protected boolean isSameCondition(UserCurveDO oldUserCurve, ICurveGenerateReq params) {
        CustomCurveGenerateReqDTO curveParams = (CustomCurveGenerateReqDTO) params;
        String newBondUniCodesJsonStr = JSON.toJSONString(curveParams.getBondUniCodes());
        String newMd5String = MD5Utils.getMD5String(newBondUniCodesJsonStr);
        String oldBondUniCodesJsonStr = oldUserCurve.getImportedBond();
        String oldMd5String = MD5Utils.getMD5String(oldBondUniCodesJsonStr);
        return newMd5String.equals(oldMd5String);
    }

    @Override
    protected Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_CUSTOM_BOND_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        return Date.valueOf(LocalDate.now());
    }

    @Override
    public void generateCurve(Long curveId) throws InterruptedException {
        Optional<CurveDefinitionWithImportedBondBO> userCurveOptional = userCurveDAO.getCurveDefinitionWithImportedBondBO(curveId);
        if (!checkCustomCurve(curveId, userCurveOptional.orElse(null))) {
            return;
        }
        boolean isSuccess = false;
        CurveDefinitionWithImportedBondBO curveBaseData = userCurveOptional.get();
        RPermitExpirableSemaphore curveGenerateSemaphore = this.getCurveGenerateSemaphore();
        logger.info("GenerateCurve_{}.try acquire.", curveId);
        String permitId = curveGenerateSemaphore.acquire(YieldSpreadConst.GENERATE_CURVE_PERMIT_LEASE_TIME, TimeUnit.MILLISECONDS);
        logger.info("GenerateCurve_{}.acquire success.permitId is:{}", curveId, permitId);
        try {
            if (!this.startGenerate(curveId)) {
                logger.info("GenerateCurve_{}.Start generate curve fail,Maybe generateStatus wrong.curveMetadata is:{}", curveId, userCurveDAO.getCurveDefinitionBO(curveId));
                return;
            }
            List<BondYieldSpreadBO> bondYieldSpreads = pgYieldSpreadCompositeSearchDAO
                    .listBondYieldSpreads(new HashSet<>(JSON.parseArray(curveBaseData.getImportedBond(), Long.class)), null);
            if (!isGeneratingStatus(curveId)) {
                logger.info("GenerateCurve_{}.Generate curve termination,GenerateStatus isn't generating.curveMetadata is:{}", curveId, userCurveDAO.getCurveDefinitionBO(curveId));
                return;
            }
            List<CurveDataResDTO> curveDataResList = BeanCopyUtils.copyList(bondYieldSpreads, CurveDataResDTO.class);
            String curveDataJsonStr = JSONObject.toJSONString(curveDataResList);
            if (!this.endGenerate(curveId, curveDataJsonStr)) {
                logger.info("GenerateCurve_{}.End generate curve fail,Maybe generateStatus wrong.curveMetadata is:{}", curveId, userCurveDAO.getCurveDefinitionBO(curveId));
                return;
            }
            userCurveRedisDAO.cacheCurve(curveId, curveDataResList);
            isSuccess = true;
            logger.info("GenerateCurve_{}.generate curve success.", curveId);
        } catch (Exception e) {
            if (!userCurveDAO.casGenerateStatus(curveId, CurveGenerateStatusEnum.FAILED, CurveGenerateStatusEnum.GENERATING)) {
                logger.info("GenerateCurve_{}.casGenerateStatus fail,Maybe generateStatus wrong.curveMetadata is:{}", curveId, userCurveDAO.getCurveDefinitionBO(curveId));
            }
            logger.error("GenerateCurve_{}.Generate curve error.", curveId, e);
        } finally {
            curveGenerateSemaphore.tryRelease(permitId);
        }
        try {
            if (isSuccess) {
                super.selectedThisCurve(curveBaseData.getUserId(), curveId);
            }
        } catch (Exception e) {
            logger.error("GenerateCurve_{}.selectedThisCurve error.", curveId, e);
        }
    }

    @Override
    public BondAnalysisResDTO analysisBonds(Set<String> bondNameOrCodeList) {
        List<BondAnalysisBO> bondAnalysisList = this.convertToBondAnalysisBO(bondNameOrCodeList);
        Set<String> bondCodes = new HashSet<>();
        Set<Long> bondUniCodes = new HashSet<>();
        for (BondAnalysisBO bondAnalysisBO : bondAnalysisList) {
            if (Objects.nonNull(bondAnalysisBO.getBondUniCode())) {
                bondUniCodes.add(bondAnalysisBO.getBondUniCode());
            } else {
                bondCodes.add(bondAnalysisBO.getBondCode());
            }
        }
        // 利差债券code
        Set<Long> yieldSpreadBondUniCodes = new HashSet<>();
        yieldSpreadBondUniCodes.addAll(localCache.listExistsBondUniCodes(bondUniCodes));
        yieldSpreadBondUniCodes.addAll(localCache.listExistsBondUniCodesByBondCodes(bondCodes));
        Map<Long, BondShortInfoDTO> bondInfoBondUniCodeMap = new HashMap<>(bondNameOrCodeList.size());
        Map<String, BondShortInfoDTO> bondInfoBondCodeMap = new HashMap<>(bondNameOrCodeList.size());
        if (CollectionUtils.isNotEmpty(yieldSpreadBondUniCodes)) {
            List<BondShortInfoDTO> bondInfos = bondInfoService.listBondShortInfoListByUniCodes(yieldSpreadBondUniCodes.toArray(new Long[0]));
            bondInfoBondUniCodeMap.putAll(bondInfos.stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (k1, k2) -> k1)));
            bondInfoBondCodeMap.putAll(bondInfos.stream().collect(Collectors.toMap(BondShortInfoDTO::getBondCode, Function.identity(), (k1, k2) -> k1)));
        }
        List<BondBasicInfoResDTO> normalBons = new ArrayList<>();
        List<String> invalidBondCodes = new ArrayList<>();
        for (BondAnalysisBO bondAnalysisBO : bondAnalysisList) {
            BondShortInfoDTO bondShortInfoDTO = bondInfoBondUniCodeMap.get(bondAnalysisBO.getBondUniCode());
            bondShortInfoDTO = Objects.isNull(bondShortInfoDTO) ? bondInfoBondCodeMap.get(bondAnalysisBO.getBondCode()) : bondShortInfoDTO;
            if (Objects.nonNull(bondShortInfoDTO)) {
                BondBasicInfoResDTO bondBasicInfoResDTO = new BondBasicInfoResDTO();
                bondBasicInfoResDTO.setBondCode(bondShortInfoDTO.getBondCode());
                bondBasicInfoResDTO.setBondUniCode(bondShortInfoDTO.getBondUniCode());
                bondBasicInfoResDTO.setBondShortName(bondShortInfoDTO.getBondShortName());
                normalBons.add(bondBasicInfoResDTO);
            } else {
                invalidBondCodes.add(bondAnalysisBO.getOriginalBondKey());
            }
        }
        BondAnalysisResDTO bondAnalysisResDTO = new BondAnalysisResDTO();
        bondAnalysisResDTO.setInvalidBonds(invalidBondCodes);
        bondAnalysisResDTO.setNormalBonds(normalBons);
        return bondAnalysisResDTO;
    }

    @Override
    public List<CustomSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        Optional<CurveDefinitionWithImportedBondBO> curveOptional = userCurveDAO.getCurveDefinitionWithImportedBondBO(userid, request.getCurveId());
        CurveDefinitionWithImportedBondBO curve = curveOptional.orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        Set<Long> bondUniCodes = new HashSet<>(JSON.parseArray(curve.getImportedBond(), Long.class));
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        Long searchBondUnicode = request.getUniCode();
        Integer uniCodeType = request.getUniCodeType();
        if (Objects.nonNull(searchBondUnicode) && Objects.nonNull(uniCodeType) && uniCodeType.equals(SpreadCodeTypeEnum.BOND_CODE.getValue())) {
            if (bondUniCodes.contains(searchBondUnicode)) {
                bondUniCodes = Sets.newHashSet(searchBondUnicode);
            } else {
                return Collections.emptyList();
            }
        }
        List<CustomSingleBondYieldSpreadBO> customSingleBondYieldSpreads = yieldSpreadCompositeSearchDAO
                .listSingleBondYieldSpreads(this.buildTableAreaSearchParam(bondUniCodes, request));
        if (CollectionUtils.isEmpty(customSingleBondYieldSpreads)) {
            return Collections.emptyList();
        }
        Long[] bondUniCodeResult = customSingleBondYieldSpreads.stream().map(CustomSingleBondYieldSpreadBO::getBondUniCode).distinct().toArray(Long[]::new);
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodeResult).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        List<CustomSingleBondYieldSpreadResDTO> result = customSingleBondYieldSpreads.stream().map(bond -> {
            CustomSingleBondYieldSpreadResDTO response = BeanCopyUtils.copyProperties(bond, CustomSingleBondYieldSpreadResDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
                response.setBondCode(bondShortInfo.getBondCode());
            }
            response.setCdbLerpYield(CommonUtils.formatDecimal(bond.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            String ratingStr = RatingUtils.getRating(bond.getComExtRatingMapping()) + "/" + RatingUtils.getRating(bond.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            EnumUtils.getEnumByValue(bond.getBondImpliedRatingMapping(), ImplicitRatingTagEnum.class)
                    .ifPresent(v -> response.setBondImpliedRatingMappingStr(v.getText()));
            return response;
        }).collect(Collectors.toList());
        // 权限控制
        this.permissionProcessing(userid, request.getSpreadDate(), result);
        return result;
    }

    private void permissionProcessing(Long userid, Date spreadDate, List<CustomSingleBondYieldSpreadResDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(CustomSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toList()));
            for (CustomSingleBondYieldSpreadResDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRatingMappingStr(CommonUtils.desensitized(yieldSpread.getBondImpliedRatingMappingStr(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();

        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (CustomSingleBondYieldSpreadResDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
        }
    }

    @Override
    public Long countSingleBondYieldSpread(Long userid, YieldSpreadSearchReqDTO request) {
        String redisKey = getComCountKey(request);
        String countStr = stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(countStr)) {
            return Long.parseLong(countStr);
        }
        Optional<CurveDefinitionWithImportedBondBO> curveOptional = userCurveDAO.getCurveDefinitionWithImportedBondBO(userid, request.getCurveId());
        CurveDefinitionWithImportedBondBO curve = curveOptional.orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        Set<Long> bondUniCodes = new HashSet<>(JSON.parseArray(curve.getImportedBond(), Long.class));
        Long searchBondUnicode = request.getUniCode();
        Integer uniCodeType = request.getUniCodeType();
        if (Objects.nonNull(searchBondUnicode) && Objects.nonNull(uniCodeType) && uniCodeType.equals(SpreadCodeTypeEnum.BOND_CODE.getValue())) {
            if (bondUniCodes.contains(searchBondUnicode)) {
                bondUniCodes = Sets.newHashSet(searchBondUnicode);
            } else {
                return 0L;
            }
        }
        Long count = yieldSpreadCompositeSearchDAO.countSingleBondYieldSpread(this.buildTableAreaSearchParam(bondUniCodes, request));
        stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(count), getExpirationTimeMillis(), TimeUnit.MILLISECONDS);
        return count;
    }

    private String getComCountKey(YieldSpreadSearchReqDTO request) {
        request.setSort(null);
        request.setPageNum(0);
        request.setPageSize(0);
        return String.format(COUNT_CUSTOM_BOND_SPREAD_KEY, MD5Utils.getMD5String(JSON.toJSONString(request)));
    }

    private long getExpirationTimeMillis() {
        LocalDateTime midnight = LocalDateTime.now().plusDays(1).withHour(EXPIRATION_HOUR).withMinute(0).withSecond(0).withNano(0);
        return ChronoUnit.MILLIS.between(LocalDateTime.now(), midnight);
    }

    private CustomYieldSearchParam buildTableAreaSearchParam(Set<Long> bondUniCodes, YieldSpreadSearchReqDTO request) {
        CustomYieldSearchParam searchParam = new CustomYieldSearchParam();
        searchParam.setStartIndex((request.getPageNum() - 1) * request.getPageSize());
        searchParam.setPageSize(request.getPageSize());
        searchParam.setPageNum(request.getPageNum());
        SortDTO sort = request.getSort();
        searchParam.setSort(Objects.isNull(sort) ? DEFAULT_SORT : sort);
        searchParam.setBondUniCodes(bondUniCodes);
        Map<Integer, Set<Long>> sectorBondUniCodesMap = localCache.convertToSectorBondUniCodesMap(bondUniCodes);
        searchParam.setUdicBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.UDIC.getValue()));
        searchParam.setInduBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.INDU.getValue()));
        searchParam.setBankBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.BANK.getValue()));
        searchParam.setSecuBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.SECU.getValue()));
        searchParam.setInsuBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.INSU.getValue()));
        if (Objects.isNull(request.getSpreadDate()) || DateExtensionUtils.isSameDay(request.getSpreadDate(), new Date(System.currentTimeMillis()))) {
            searchParam.setSpreadDate(this.getMaxSpreadDate());
        } else {
            searchParam.setSpreadDate(request.getSpreadDate());
        }
        if (Objects.nonNull(request.getUniCodeType()) && Objects.nonNull(request.getUniCode()) && request.getUniCodeType().equals(SpreadCodeTypeEnum.COM_CODE.getValue())) {
            searchParam.setComUniCode(request.getUniCode());
        }
        searchParam.setYear(searchParam.getSpreadDate().toLocalDate().getYear());
        return searchParam;
    }

    @Override
    public GenerateDetailResDTO generateDetail() {
        GenerateDetailResDTO generateDetailResDTO = new GenerateDetailResDTO();
        List<CurveDefinitionBO> curveDefinitions = userCurveDAO.listCustomCurves(null, Sets.newHashSet(CurveGenerateStatusEnum.GENERATING));
        generateDetailResDTO.setCustomCurves(BeanCopyUtils.copyList(curveDefinitions, CustomCurveGenerateDetailResDTO.class));
        RPermitExpirableSemaphore semaphore = getCurveGenerateSemaphore();
        generateDetailResDTO.setAvailablePermit(semaphore.availablePermits());
        generateDetailResDTO.setPermit(semaphore.getPermits());
        return generateDetailResDTO;
    }

    @Override
    public void addPermits(Integer permitCount) {
        RPermitExpirableSemaphore semaphore = getCurveGenerateSemaphore();
        semaphore.addPermits(permitCount);
    }

    @Override
    public void setPermits(Integer permitCount) {
        RPermitExpirableSemaphore semaphore = getCurveGenerateSemaphore();
        semaphore.setPermits(permitCount);
    }

    /**
     * 生成曲线数据
     *
     * @param spreadDate 日期
     */
    @Override
    public void generateCurveData(Date spreadDate) {
        List<CurveDefinitionBO> curves = userCurveDAO.listCustomCurves(null, Sets.newHashSet(CurveGenerateStatusEnum.SUCCEED));
        if (CollectionUtils.isEmpty(curves)) {
            return;
        }
        if (Objects.isNull(spreadDate)) {
            spreadDate = Date.valueOf(LocalDate.now().minusDays(1));
        }
        final Date currentSpreadDate = spreadDate;
        for (CurveDefinitionBO curve : curves) {
            Optional<UserCurveDO> userCurveOptional = userCurveDAO.get(curve.getId());
            userCurveOptional.ifPresent(c -> {
                List<CurveDataResDTO> curveDataList = JSONObject.parseArray(c.getSpreadCurveData(), CurveDataResDTO.class);
                if (CollectionUtils.isNotEmpty(curveDataList)) {
                    curveDataList = curveDataList.stream().filter(curveDataResDTO -> !currentSpreadDate.equals(curveDataResDTO.getSpreadDate())).collect(Collectors.toList());
                }
                List<BondYieldSpreadBO> bondYieldSpreads = pgYieldSpreadCompositeSearchDAO
                        .listBondYieldSpreads(new HashSet<>(JSON.parseArray(c.getImportedBond(), Long.class)), currentSpreadDate);
                if (CollectionUtils.isEmpty(bondYieldSpreads)) {
                    return;
                }
                List<CurveDataResDTO> currentDataList = BeanCopyUtils.copyList(bondYieldSpreads, CurveDataResDTO.class);
                curveDataList.addAll(currentDataList);
                curveDataList.sort(Comparator.comparing(CurveDataResDTO::getSpreadDate));
                String curveDataJsonStr = JSONObject.toJSONString(curveDataList);
                c.setSpreadCurveData(curveDataJsonStr);
                userCurveDAO.updateByPrimaryKeySelective(c);
            });
        }
    }

    @Override
    public void generateYesterdayCurveData() {
        generateCurveData(null);
    }

    private List<BondAnalysisBO> convertToBondAnalysisBO(Set<String> bondNameOrCodes) {
        List<BondAnalysisBO> bondAnalysisBOs = new ArrayList<>(bondNameOrCodes.size());
        Map<String, BondAnalysisBO> bondAnalysisMap = new HashMap<>(bondNameOrCodes.size());
        for (String bondNameOrCode : bondNameOrCodes) {
            String nameMd5 = MD5Utils.getMD5String(bondNameOrCode);
            BondAnalysisBO bondAnalysisBO = new BondAnalysisBO();
            bondAnalysisBO.setOriginalBondKey(bondNameOrCode);
            bondAnalysisBO.setOriginalBondKeyMd5(nameMd5);
            bondAnalysisBO.setBondCode(bondNameOrCode);
            bondAnalysisBO.setBondUniCode(localCache.getBondUniCodeByBondCode(bondNameOrCode).orElse(null));
            bondAnalysisBOs.add(bondAnalysisBO);
            bondAnalysisMap.put(nameMd5, bondAnalysisBO);
        }
        String[] nameMd5Array = bondAnalysisBOs.stream().filter(v -> Objects.isNull(v.getBondUniCode())).map(BondAnalysisBO::getOriginalBondKeyMd5).toArray(String[]::new);
        if (ArrayUtils.isEmpty(nameMd5Array)) {
            return bondAnalysisBOs;
        }
        List<BondNameToCodeDTO> bondInfos = bondInfoService.getBondNameToCodeList(nameMd5Array);
        for (BondNameToCodeDTO bondInfo : bondInfos) {
            BondAnalysisBO bondAnalysisBO = bondAnalysisMap.get(bondInfo.getNameMd5());
            if (Objects.isNull(bondAnalysisBO)) {
                continue;
            }
            bondAnalysisBO.setBondUniCode(bondInfo.getBondUniCode());
        }
        //补全债券代码
        String[] incompleteBondCodes = bondAnalysisBOs.stream().filter(bo -> bo.getBondUniCode() == null
                && StringUtils.isNotBlank(bo.getBondCode())
                && !bo.getBondCode().contains(".")).map(BondAnalysisBO::getBondCode).toArray(String[]::new);
        if (incompleteBondCodes.length == 0) {
            return bondAnalysisBOs;
        }
        List<BondCodeDTO> bondCodeDTOList = bondInfoService.getBondCodeList(incompleteBondCodes);
        if (CollectionUtils.isEmpty(bondCodeDTOList)) {
            return bondAnalysisBOs;
        }
        Map<String, List<String>> bondCodeDTOMap = bondCodeDTOList.stream()
                .collect(Collectors.toMap(BondCodeDTO::getBondCode, BondCodeDTO::getBondCodeList));
        for (BondAnalysisBO bondAnalysisBO : bondAnalysisBOs) {
            List<String> bondCodeList = bondCodeDTOMap.get(bondAnalysisBO.getOriginalBondKey());
            if (!CollectionUtils.isEmpty(bondCodeList) && bondCodeList.size() == 1) {
                bondAnalysisBO.setBondCode(bondCodeList.get(0));
            }
        }
        return bondAnalysisBOs;
    }

    @Override
    public void afterPropertiesSet() {
        RPermitExpirableSemaphore semaphore = this.getCurveGenerateSemaphore();
        semaphore.setPermits(YieldSpreadConst.GENERATE_CURVE_MAX_CONCURRENCY);
    }

    @Override
    public List<CurveDataResDTO> listCurveData(Long curveId) {
        Optional<String> spreadCurveDataOptional = userCurveDAO.getSpreadCurveData(curveId);
        return spreadCurveDataOptional.isPresent() ?
                JSONObject.parseArray(spreadCurveDataOptional.get(), CurveDataResDTO.class) : Collections.emptyList();
    }

    @Override
    protected List<CurveDataResDTO> listCurveData(ICurveGenerateReq curveGenerateParam) {
        return Collections.emptyList();
    }

    private boolean checkCustomCurve(Long curveId, CurveDefinitionWithImportedBondBO userCurve) {
        if (Objects.isNull(userCurve)) {
            logger.info("GenerateCurve_{}.custom curve data is null.", curveId);
            return Boolean.FALSE;
        }
        if (!userCurve.getSpreadCurveType().equals(CurveTypeEnum.CUSTOMIZATION.getValue())) {
            logger.info("GenerateCurve_{}.curve type isn't CUSTOMIZATION.", curveId);
            return Boolean.FALSE;
        }
        if (!userCurve.getGenerateStatus().equals(CurveGenerateStatusEnum.WAITING.getValue())) {
            logger.info("GenerateCurve_{}.custom curve GenerateStatus isn't WAITING.", curveId);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean isGeneratingStatus(Long curveId) {
        Optional<CurveDefinitionBO> userCurveOptional = userCurveDAO.getCurveDefinitionBO(curveId);
        if (!userCurveOptional.isPresent()) {
            logger.info("custom curve data is null.curve id is:{} ", curveId);
            return Boolean.FALSE;
        }
        CurveDefinitionBO userCurve = userCurveOptional.get();
        if (!userCurve.getGenerateStatus().equals(CurveGenerateStatusEnum.GENERATING.getValue())) {
            logger.info("custom curve GenerateStatus isn't WAITING.curve id is:{} ", curveId);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private RPermitExpirableSemaphore getCurveGenerateSemaphore() {
        return redissonClient.getPermitExpirableSemaphore(YieldSpreadCacheConst.SEMAPHORE_GENERATE_CURVE);
    }

    private boolean startGenerate(Long curveId) {
        UserCurveDO userCurveDO = new UserCurveDO();
        userCurveDO.setGenerateStatus(CurveGenerateStatusEnum.GENERATING.getValue());
        userCurveDO.setGenerateStartTime(new Timestamp(System.currentTimeMillis()));
        return userCurveDAO.casByGenerateStatus(userCurveDO, curveId, CurveGenerateStatusEnum.WAITING);
    }

    private boolean endGenerate(Long curveId, String curveDataJsonStr) {
        UserCurveDO userCurveDO = new UserCurveDO();
        userCurveDO.setSpreadCurveData(curveDataJsonStr);
        userCurveDO.setGenerateStatus(CurveGenerateStatusEnum.SUCCEED.getValue());
        userCurveDO.setGenerateEndTime(new Timestamp(System.currentTimeMillis()));
        return userCurveDAO.casByGenerateStatus(userCurveDO, curveId, CurveGenerateStatusEnum.GENERATING);
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.CUSTOMIZATION;
    }

}
