package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.notify.service.NotificationSender;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondComUniCodeDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondFilterV3DTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.request.OnshoreBondFilterRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.bondpriceapollo.CurveMaturityStructureDTO;
import com.innodealing.onshore.bondmetadata.dto.common.DateRangeDTO;
import com.innodealing.onshore.bondmetadata.enums.*;
import com.innodealing.onshore.yieldspread.consts.NotifyConst;
import com.innodealing.onshore.yieldspread.consts.RedisLockConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.*;
import com.innodealing.onshore.yieldspread.dao.yieldspread.*;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadQuantileTypeEnum;
import com.innodealing.onshore.yieldspread.enums.UrbanInvestCaliberEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.ShardingHindStrParamUtil;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadChangeBO;
import com.innodealing.onshore.yieldspread.model.dto.SpreadDateDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.*;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.*;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import com.innodealing.onshore.yieldspread.service.*;
import com.innodealing.onshore.yieldspread.service.internal.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.subtract;

/**
 * 利差计算 Service
 *
 * <AUTHOR>
 **/
@SuppressWarnings({"squid:S3776"})
@Service
public class CalcYieldSpreadServiceImpl implements CalcYieldSpreadService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 主体分位统计单次最大主体数
     */
    private static final int COM_BATCH_SIZE = 20;

    /**
     * 主体利差分位 小数点位数
     */
    private static final int COM_QUANTILE_SCALE = 2;

    @Resource
    private HolidayService holidayService;

    @Resource
    private InduBondYieldSpreadService induBondYieldSpreadService;

    @Resource
    private InduComYieldSpreadService induComYieldSpreadService;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private UdicInfoService udicInfoService;

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private BondPriceApolloService bondPriceApolloService;

    @Resource
    private UdicBondYieldSpreadService udicBondYieldSpreadService;

    @Resource
    private UdicComYieldSpreadService udicComYieldSpreadService;

    @Resource
    private UdicBondYieldSpreadDAO udicBondYieldSpreadDAO;

    @Resource
    private InduBondYieldSpreadDAO induBondYieldSpreadDAO;

    @Resource
    private SecuBondYieldSpreadDAO secuBondYieldSpreadDAO;

    @Resource
    private InsuBondYieldSpreadDAO insuBondYieldSpreadDAO;

    @Resource
    private BankBondYieldSpreadDAO bankBondYieldSpreadDAO;

    @Resource
    private InduComYieldSpreadDAO induComYieldSpreadDAO;

    @Resource
    private UdicComYieldSpreadDAO udicComYieldSpreadDAO;

    @Resource
    private SecuComYieldSpreadDAO secuComYieldSpreadDAO;

    @Resource
    private BankComYieldSpreadDAO bankComYieldSpreadDAO;

    @Resource
    private InsuComYieldSpreadDAO insuComYieldSpreadDAO;

    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;

    @Resource
    private ComYieldSpreadChangeDAO comYieldSpreadChangeDAO;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private PgInduBondYieldSpreadDAO pgInduBondYieldSpreadDAO;

    @Resource
    private PgUdicBondYieldSpreadDAO pgUdicBondYieldSpreadDAO;

    @Resource
    private SecuBondYieldSpreadService secuBondYieldSpreadService;

    @Resource
    private LgYieldSpreadService calcLgYieldSpreadService;

    @Resource
    private InsuBondYieldSpreadService insuBondYieldSpreadService;

    @Resource
    private SecuComYieldSpreadService secuComYieldSpreadService;

    @Resource
    private BankBondYieldSpreadService bankBondYieldSpreadService;

    @Resource
    private BankComYieldSpreadService bankComYieldSpreadService;

    @Resource
    private PgBankBondYieldSpreadDAO pgBankBondYieldSpreadDAO;

    @Resource
    private PgSecuBondYieldSpreadDAO pgSecuBondYieldSpreadDAO;

    @Resource
    private PgInsuBondYieldSpreadDAO pgInsuBondYieldSpreadDAO;

    @Resource
    private NotificationSender notificationSender;

    @Resource
    private BondYieldSpreadService bondYieldSpreadService;

    @Resource
    private RedissonClient redissonClient;

    public int supplementCalcInduBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodes, Boolean isEnableOldData) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            List<Integer> bondTypeList = Arrays.asList(BondType.COLLECTIVE_NOTES.getValue(), BondType.ORIENTATION_TOOL.getValue(),
                    BondType.SUPER_SHORT_TERM_FINANCING_BOND.getValue(), BondType.PROJECT_INCOME_NOTES.getValue());
            List<BondComUniCodeDTO> bondComUniCodeDTOList = listSupplementBondComUniCodeDTOs(bondTypeList);
            if (CollectionUtils.isEmpty(bondComUniCodeDTOList)) {
                return 0;
            }
            bondUniCodes = bondComUniCodeDTOList.stream().map(BondComUniCodeDTO::getBondUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        return calcInduBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodes, isEnableOldData);
    }

    /**
     * 查询补充的债券
     *
     * @param bondTypeList 补充的债券
     * @return BondComUniCodeDTO
     */
    private List<BondComUniCodeDTO> listSupplementBondComUniCodeDTOs(List<Integer> bondTypeList) {
        OnshoreBondFilterRequestDTO onshoreBondFilterRequestDTO = new OnshoreBondFilterRequestDTO();
        List<Integer> couponRateFilterTypeList = new ArrayList<>();
        couponRateFilterTypeList.add(CouponRateTypeEnum.FIXED_RATE.getValue());
        couponRateFilterTypeList.add(CouponRateTypeEnum.FLOAT_RATE.getValue());
        onshoreBondFilterRequestDTO.setUdicStatus(UrbanInvestEnum.NOT_URBAN_INVEST.getValue());
        onshoreBondFilterRequestDTO.setInduUniCodeList(new ArrayList<>(YieldSpreadHelper.getInduUnicodeMap().keySet()));
        onshoreBondFilterRequestDTO.setBondTypeList(bondTypeList);
        onshoreBondFilterRequestDTO.setCouponRateFilterTypeList(couponRateFilterTypeList);
        return bondInfoService.getBondComUniCodeDTOs(onshoreBondFilterRequestDTO);
    }

    @Override
    public int calcInduBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodes, Boolean isEnableOldData) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        int effectRows = 0;
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            List<BondComUniCodeDTO> bondComUniCodeDTOList = bondInfoService.
                    listBondComUniCodeDTOs(UrbanInvestEnum.NOT_URBAN_INVEST.getValue(), null);
            if (CollectionUtils.isEmpty(bondComUniCodeDTOList)) {
                return effectRows;
            }
            bondUniCodes = bondComUniCodeDTOList.stream().map(BondComUniCodeDTO::getBondUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatchSpreadDate = new StopWatch();
            stopWatchSpreadDate.start();
            try {
                // 利差计算
                effectRows += calcInduBondYieldSpread(spreadDate, bondUniCodes, isEnableOldData);
            } catch (Exception e) {
                logger.error(String.format("can't handle calcInduBondYieldSpread spreadDate: %d", spreadDate.getTime()), e);
            }
            logger.info("[calcInduBondYieldSpread]  spreadDate:{}  stopWatch: {} 已处理债券条数:{}",
                    spreadDate, stopWatchSpreadDate.getTime(), effectRows);
        }
        // 更新缓存中最大日期
        pgInduBondYieldSpreadDAO.getMaxSpreadDate().ifPresent(date -> {
            stringRedisTemplate.opsForValue().set(MAX_INDU_BOND_SPREAD_DATE_KEY, date.toString());
            this.setCustomBondMaxSpreadDate(date);
        });
        induComYieldSpreadDAO.getMaxSpreadDateForMaster().ifPresent(date -> {
            stringRedisTemplate.opsForValue().set(MAX_INDU_COM_SPREAD_DATE_KEY, date.toString());
            this.setCustomComMaxSpreadDate(date);
        });
        return effectRows;
    }

    @Override
    public int calcUdicBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodes, Boolean isEnableOldData) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        int effectRows = 0;
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        String hindStrParam = ShardingHindStrParamUtil.getHindStrParam();
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            List<BondComUniCodeDTO> bondComUniCodeDTOList = bondInfoService.
                    listBondComUniCodeDTOs(UrbanInvestEnum.URBAN_INVEST.getValue(), hindStrParam);
            if (UrbanInvestCaliberEnum.DM_UDIC.getText().equals(hindStrParam)) {
                Set<Long> dmComUniCodeList = udicInfoService.getDmComUniCodeList();
                bondComUniCodeDTOList = bondComUniCodeDTOList.stream()
                        .filter(bondComUniCodeDTO -> dmComUniCodeList.contains(bondComUniCodeDTO.getComUniCode()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(bondComUniCodeDTOList)) {
                return effectRows;
            }
            bondUniCodes = bondComUniCodeDTOList.stream().map(BondComUniCodeDTO::getBondUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatchSpreadDate = new StopWatch();
            stopWatchSpreadDate.start();
            try {
                // 利差计算
                effectRows += calcUdicBondYieldSpread(spreadDate, bondUniCodes, isEnableOldData);
            } catch (Exception e) {
                logger.error("can't handle calcUdicBondYieldSpread spreadDate: {}", spreadDate, e);
            }
            logger.info("[calcUdicBondYieldSpread]  spreadDate:{}  stopWatch: {} 已处理债券条数:{}",
                    spreadDate, stopWatchSpreadDate.getTime(), effectRows);
        }
        if (StringUtils.isBlank(hindStrParam)) {
            // 更新缓存中最大日期
            pgUdicBondYieldSpreadDAO.getMaxSpreadDate().ifPresent(date -> {
                stringRedisTemplate.opsForValue().set(MAX_UDIC_BOND_SPREAD_DATE_KEY, date.toString());
                this.setCustomBondMaxSpreadDate(date);
            });
            udicComYieldSpreadDAO.getMaxSpreadDateForMaster().ifPresent(date -> {
                stringRedisTemplate.opsForValue().set(MAX_UDIC_COM_SPREAD_DATE_KEY, date.toString());
                this.setCustomComMaxSpreadDate(date);
            });
        }

        return effectRows;
    }

    @Override
    public int calcBankBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodes) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        int effectRows = 0;
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            List<BondComUniCodeDTO> bondComUniCodeDTOList = bondInfoService.
                    listBankBondComUniCodeDTOs(UrbanInvestEnum.NOT_URBAN_INVEST.getValue());
            if (CollectionUtils.isEmpty(bondComUniCodeDTOList)) {
                return effectRows;
            }
            bondUniCodes = bondComUniCodeDTOList.stream().map(BondComUniCodeDTO::getBondUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }

        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatchSpreadDate = new StopWatch();
            stopWatchSpreadDate.start();
            try {
                // 利差计算
                effectRows += calcBankBondYieldSpread(spreadDate, bondUniCodes);
            } catch (Exception e) {
                logger.error(String.format("can't handle calcBankBondYieldSpread spreadDate: %d", spreadDate.getTime()), e);
            }
            logger.info("[calcBankBondYieldSpread]  spreadDate:{}  stopWatch: {} 已处理债券条数:{}",
                    spreadDate, stopWatchSpreadDate.getTime(), effectRows);
        }
        pgBankBondYieldSpreadDAO.getMaxSpreadDate().ifPresent(date -> {
            stringRedisTemplate.opsForValue().set(MAX_BANK_BOND_SPREAD_DATE_KEY, date.toString());
            this.setCustomBondMaxSpreadDate(date);
        });
        bankComYieldSpreadDAO.getMaxSpreadDateForMaster().ifPresent(date -> {
            stringRedisTemplate.opsForValue().set(MAX_BANK_COM_SPREAD_DATE_KEY, date.toString());
            this.setCustomComMaxSpreadDate(date);
        });
        return effectRows;
    }

    @Override
    public int calcSecuBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodes) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        int effectRows = 0;
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            List<BondComUniCodeDTO> bondComUniCodeDTOList = bondInfoService.
                    listSecuBondComUniCodeDTOs(UrbanInvestEnum.NOT_URBAN_INVEST.getValue());
            if (CollectionUtils.isEmpty(bondComUniCodeDTOList)) {
                return effectRows;
            }
            bondUniCodes = bondComUniCodeDTOList.stream().map(BondComUniCodeDTO::getBondUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatchSpreadDate = new StopWatch();
            stopWatchSpreadDate.start();
            try {
                // 利差计算
                effectRows += calcSecuBondYieldSpread(spreadDate, bondUniCodes);
            } catch (Exception e) {
                logger.error(String.format("can't handle calcSecuBondYieldSpread spreadDate: %d", spreadDate.getTime()), e);
            }
            logger.info("[calcSecuBondYieldSpread]  spreadDate:{}  stopWatch: {} 已处理债券条数:{}",
                    spreadDate, stopWatchSpreadDate.getTime(), effectRows);
        }
        pgSecuBondYieldSpreadDAO.getMaxSpreadDate().ifPresent(date -> {
            stringRedisTemplate.opsForValue().set(MAX_SECU_BOND_SPREAD_DATE_KEY, date.toString());
            this.setCustomBondMaxSpreadDate(date);
        });
        secuComYieldSpreadDAO.getMaxSpreadDateForMaster().ifPresent(date -> {
            stringRedisTemplate.opsForValue().set(MAX_SECU_COM_SPREAD_DATE_KEY, date.toString());
            this.setCustomComMaxSpreadDate(date);
        });
        return 0;
    }

    @Override
    public int calcLgBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodes) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        int effectRows = 0;
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            List<BondComUniCodeDTO> bondComUniCodeDTOList = bondInfoService.listLgBondComUniCodeDTOs();
            if (CollectionUtils.isEmpty(bondComUniCodeDTOList)) {
                return effectRows;
            }
            bondUniCodes = bondComUniCodeDTOList.stream().map(BondComUniCodeDTO::getBondUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        logger.info("【calcLgBondYieldSpread】 bondUniCodes count={}", bondUniCodes.size());
        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatchSpreadDate = new StopWatch();
            stopWatchSpreadDate.start();
            try {
                // 利差计算
                effectRows = calcLgBondYieldSpread(spreadDate, bondUniCodes);
            } catch (Exception e) {
                logger.error(String.format("can't handle calcLgBondYieldSpread spreadDate: %d", spreadDate.getTime()), e);
            }
            logger.info("[calcLgBondYieldSpread]  spreadDate:{}  stopWatch: {} 已处理债券条数:{}",
                    spreadDate, stopWatchSpreadDate.getTime(), effectRows);
        }

        return effectRows;
    }

    /**
     * 保险利差计算(主体,债券)
     *
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param bondUniCodes 债券唯一代码
     * @return 影响行数
     */
    @Override
    public int calcInsuBondYieldSpreadsBySpreadDate(Date startDate, Date endDate, List<Long> bondUniCodes) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        int effectRows = 0;
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            List<BondComUniCodeDTO> bondComUniCodeDTOList = bondInfoService.
                    listInsuBondComUniCodeDTOs(UrbanInvestEnum.NOT_URBAN_INVEST.getValue());
            if (CollectionUtils.isEmpty(bondComUniCodeDTOList)) {
                return effectRows;
            }
            bondUniCodes = bondComUniCodeDTOList.stream().map(BondComUniCodeDTO::getBondUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatchSpreadDate = new StopWatch();
            stopWatchSpreadDate.start();
            try {
                // 利差计算 并更新相应表值
                effectRows += calcInsuBondYieldSpread(spreadDate, bondUniCodes);
            } catch (Exception e) {
                logger.error(String.format("can't handle calcInsuBondYieldSpread spreadDate: %d", spreadDate.getTime()), e);
            }
            logger.info("[calcInsuBondYieldSpread]  spreadDate:{}  stopWatch: {} 已处理债券条数:{}",
                    spreadDate, stopWatchSpreadDate.getTime(), effectRows);
        }
        pgInsuBondYieldSpreadDAO.getMaxSpreadDate().ifPresent(date -> {
            stringRedisTemplate.opsForValue().set(MAX_INSU_BOND_SPREAD_DATE_KEY, date.toString());
            this.setCustomBondMaxSpreadDate(date);
        });
        insuComYieldSpreadDAO.getMaxSpreadDateForMaster().ifPresent(date -> {
            stringRedisTemplate.opsForValue().set(MAX_INSU_COM_SPREAD_DATE_KEY, date.toString());
            this.setCustomComMaxSpreadDate(date);
        });
        return effectRows;
    }

    private int calcInduBondYieldSpread(Date spreadDate, List<Long> bondUniCodes, Boolean isEnableOldData) {
        int effectRows = 0;
        // 获取收益率曲线信息
        List<Integer> bondYieldCurveInduList = new ArrayList<>(YieldSpreadHelper.getBondYieldCurveInduMap().values());
        bondYieldCurveInduList.add(YieldSpreadHelper.CDB_YIELD_CURVE);
        bondYieldCurveInduList.add(CurveCode.CHINA_BOND_ORD.getValue());
        Map<Integer, BondYieldCurveDTO> bondYieldCurveMap = bondPriceService.
                getBondYieldCurveMap(spreadDate, bondYieldCurveInduList);
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = listOnshoreBondInfos(bondUniCodes, spreadDate);

        if (CollectionUtils.isNotEmpty(onshoreBondInfoDTOs)) {
            List<List<OnshoreBondInfoDTO>> onshoreBondInfoDTOsList = Lists.
                    partition(onshoreBondInfoDTOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<OnshoreBondInfoDTO> onshoreBondInfoDTOS : onshoreBondInfoDTOsList) {
                effectRows += induBondYieldSpreadService.
                        calcInduBondYieldSpreadsBySpreadDate(onshoreBondInfoDTOS, bondYieldCurveMap, spreadDate, isEnableOldData);
            }
            // 刷完一天债券数据后 再计算主体数据
            List<InduBondYieldSpreadGroupDO> induBondYieldSpreadGroupDOList = induBondYieldSpreadDAO.
                    listInduBondYieldSpreadGroupDOs(spreadDate);
            if (CollectionUtils.isEmpty(induBondYieldSpreadGroupDOList)) {
                return effectRows;
            }
            List<InduComYieldSpreadDO> comYieldSpreadDOs = BeanCopyUtils.copyList(induBondYieldSpreadGroupDOList,
                    InduComYieldSpreadDO.class);
            List<List<InduComYieldSpreadDO>> comYieldSpreadDOsList = Lists.partition(comYieldSpreadDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<InduComYieldSpreadDO> induComYieldSpreadDOs : comYieldSpreadDOsList) {
                induComYieldSpreadService.calcInduComYieldSpreadsBySpreadDate(induComYieldSpreadDOs, spreadDate, isEnableOldData);
            }
        }
        return effectRows;
    }

    private int calcUdicBondYieldSpread(Date spreadDate, List<Long> bondUniCodes, Boolean isEnableOldData) {
        int effectRows = 0;
        if (CollectionUtils.isEmpty(bondUniCodes) || Objects.isNull(spreadDate)) {
            return effectRows;
        }
        // 获取收益率曲线信息
        List<Integer> bondYieldCurveUdicList = new ArrayList<>(YieldSpreadHelper.getBondYieldCurveUdicMap().values());
        bondYieldCurveUdicList.add(YieldSpreadHelper.CDB_YIELD_CURVE);
        Map<Integer, BondYieldCurveDTO> bondYieldCurveMap = bondPriceService.
                getBondYieldCurveMap(spreadDate, bondYieldCurveUdicList);
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = listOnshoreBondInfos(bondUniCodes, spreadDate);
        if (CollectionUtils.isNotEmpty(onshoreBondInfoDTOs)) {
            List<List<OnshoreBondInfoDTO>> onshoreBondInfoDTOsList = Lists.partition(onshoreBondInfoDTOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<OnshoreBondInfoDTO> onshoreBondInfoDTOList : onshoreBondInfoDTOsList) {
                effectRows += udicBondYieldSpreadService.
                        calcUdicBondYieldSpreadsBySpreadDate(onshoreBondInfoDTOList, bondYieldCurveMap, spreadDate, isEnableOldData);
            }
            // 刷完一天债券数据后 再计算主体数据
            List<UdicBondYieldSpreadGroupDO> yieldSpreadGroupDOList = udicBondYieldSpreadDAO.listUdicBondYieldSpreadGroupDOs(spreadDate);
            List<UdicComYieldSpreadDO> comYieldSpreadDOs = BeanCopyUtils.copyList(yieldSpreadGroupDOList, UdicComYieldSpreadDO.class);
            List<List<UdicComYieldSpreadDO>> comYieldSpreadDOsList = Lists.partition(comYieldSpreadDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<UdicComYieldSpreadDO> udicComYieldSpreadDOs : comYieldSpreadDOsList) {
                udicComYieldSpreadService.calcUdicComYieldSpreadsBySpreadDate(udicComYieldSpreadDOs, spreadDate, isEnableOldData);
            }
        }
        return effectRows;
    }

    private int calcBankBondYieldSpread(Date spreadDate, List<Long> bondUniCodes) {
        int effectRows = 0;
        // 获取收益率曲线信息
        List<Integer> bondYieldCurveBankList = new ArrayList<>(YieldSpreadHelper.getBondYieldCurveBankMap().values());
        bondYieldCurveBankList.add(YieldSpreadHelper.CDB_YIELD_CURVE);
        Map<Integer, BondYieldCurveDTO> bondYieldCurveMap = bondPriceService.getBondYieldCurveMap(spreadDate, bondYieldCurveBankList);
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = listOnshoreBondInfos(bondUniCodes, spreadDate);
        if (CollectionUtils.isNotEmpty(onshoreBondInfoDTOs)) {
            List<Long> spreadDateBondUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getBondUniCode).collect(Collectors.toList());
            List<OnshoreBondFilterV3DTO> onshoreBondFilterV3DTOS = listOnshoreBondFilters(spreadDateBondUniCodes);
            Map<Long, OnshoreBondFilterV3DTO> onshoreBondFilterMap = onshoreBondFilterV3DTOS.stream()
                    .collect(Collectors.toMap(OnshoreBondFilterV3DTO::getBondUniCode, Function.identity(), (v1, v2) -> v1));
            List<List<OnshoreBondInfoDTO>> onshoreBondInfoDTOsList =
                    Lists.partition(onshoreBondInfoDTOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<OnshoreBondInfoDTO> onshoreBondInfoDTOS : onshoreBondInfoDTOsList) {
                Map<Long, OnshoreBondFilterV3DTO> onshoreBondInfoFilterDTOS = onshoreBondInfoDTOS.stream()
                        .map(onshoreBondInfoDTO -> onshoreBondFilterMap.get(onshoreBondInfoDTO.getBondUniCode()))
                        .collect(Collectors.toMap(OnshoreBondFilterV3DTO::getBondUniCode, Function.identity(), (v1, v2) -> v1));
                effectRows += bankBondYieldSpreadService.
                        calcBankBondYieldSpreadsBySpreadDate(onshoreBondInfoDTOS, onshoreBondInfoFilterDTOS, bondYieldCurveMap, spreadDate);
            }
            // 刷完一天债券数据后 再计算主体数据
            List<BankBondYieldSpreadGroupDO> bankBondYieldSpreadGroupDOList = bankBondYieldSpreadDAO.
                    listBankBondYieldSpreadGroupDOs(spreadDate);
            if (CollectionUtils.isEmpty(bankBondYieldSpreadGroupDOList)) {
                return effectRows;
            }
            List<BankComYieldSpreadDO> comYieldSpreadDOs = BeanCopyUtils.copyList(bankBondYieldSpreadGroupDOList,
                    BankComYieldSpreadDO.class);
            List<List<BankComYieldSpreadDO>> comYieldSpreadDOsList = Lists.partition(comYieldSpreadDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<BankComYieldSpreadDO> bankComYieldSpreadDOs : comYieldSpreadDOsList) {
                bankComYieldSpreadService.calcBankComYieldSpreadsBySpreadDate(bankComYieldSpreadDOs, spreadDate);
            }
        }
        return effectRows;

    }

    private int calcSecuBondYieldSpread(Date spreadDate, List<Long> bondUniCodes) {
        int effectRows = 0;
        // 获取收益率曲线信息
        List<Integer> bondYieldCurveSecuList = new ArrayList<>(YieldSpreadHelper.getBondYieldCurveSecuMap().values());
        bondYieldCurveSecuList.add(YieldSpreadHelper.CDB_YIELD_CURVE);
        // 获取收益率曲线
        Map<Integer, BondYieldCurveDTO> bondYieldCurveMap = bondPriceService.getBondYieldCurveMap(spreadDate, bondYieldCurveSecuList);
        // 境内债券基础信息 | 获取 剩余期限大于等于6个月小于6年 的 有效债券信息
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = listOnshoreBondInfos(bondUniCodes, spreadDate);
        if (CollectionUtils.isNotEmpty(onshoreBondInfoDTOs)) {
            List<List<OnshoreBondInfoDTO>> onshoreBondInfoDTOsList = Lists.
                    partition(onshoreBondInfoDTOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<OnshoreBondInfoDTO> onshoreBondInfoDTOS : onshoreBondInfoDTOsList) {
                effectRows += secuBondYieldSpreadService.
                        calcSecuBondYieldSpreadsBySpreadDate(onshoreBondInfoDTOS, bondYieldCurveMap, spreadDate);
            }
            // 刷完一天债券数据后 再计算主体数据
            List<SecuBondYieldSpreadGroupDO> secuBondYieldSpreadGroupDOList = secuBondYieldSpreadDAO.
                    listSecuBondYieldSpreadGroupDOs(spreadDate);
            if (CollectionUtils.isEmpty(secuBondYieldSpreadGroupDOList)) {
                return effectRows;
            }
            List<SecuComYieldSpreadDO> comYieldSpreadDOs = BeanCopyUtils.copyList(secuBondYieldSpreadGroupDOList,
                    SecuComYieldSpreadDO.class);
            List<List<SecuComYieldSpreadDO>> comYieldSpreadDOsList = Lists.partition(comYieldSpreadDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<SecuComYieldSpreadDO> secuComYieldSpreadDOs : comYieldSpreadDOsList) {
                secuComYieldSpreadService.calcSecuComYieldSpreadsBySpreadDate(secuComYieldSpreadDOs, spreadDate);
            }
        }
        return effectRows;
    }

    private int calcLgBondYieldSpread(Date spreadDate, List<Long> bondUniCodes) {
        int effectRows = 0;
        // 获取收益率曲线信息
        // 曲线 code , 地方债 + 国开 + 国债
        List<Integer> bondYieldCurveList = new ArrayList<>(Arrays.asList(CurveCode.CHINA_BOND_CITY.getValue(), YieldSpreadHelper.CDB_YIELD_CURVE, CurveCode.CHINA_BOND.getValue()));
        // 曲线收益率
        Map<Integer, BondYieldCurveDTO> bondYieldCurveMap = bondPriceService.getBondYieldCurveMap(spreadDate, bondYieldCurveList);
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = listAllOnshoreBondInfos(bondUniCodes, spreadDate);
        if (CollectionUtils.isEmpty(onshoreBondInfoDTOs)) {
            return effectRows;
        }
        List<List<OnshoreBondInfoDTO>> onshoreBondInfoDTOsList = Lists.partition(onshoreBondInfoDTOs, YieldSpreadHelper.BATCH_SIZE);
        for (List<OnshoreBondInfoDTO> onshoreBondInfoDTOS : onshoreBondInfoDTOsList) {
            effectRows += calcLgYieldSpreadService.calcLgBondYieldSpreadsBySpreadDate(onshoreBondInfoDTOS, bondYieldCurveMap, spreadDate);
        }
        return effectRows;
    }

    private int calcInsuBondYieldSpread(Date spreadDate, List<Long> bondUniCodes) {
        int effectRows = 0;
        // 获取收益率曲线信息
        List<Long> curveUniCodeList = new ArrayList<>(YieldSpreadHelper.getBondYieldCurveInsuMap().values());
        curveUniCodeList.add(YieldSpreadConst.CDB_CURVE_UNI_CODE);
        Map<Long, List<CurveMaturityStructureDTO>> curveMaturityStructureMap = bondPriceApolloService.getCurveMaturityStructureMap(curveUniCodeList, spreadDate);
        //剩余期限大于等于6个月小于6年 的 有效债券信息
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = listOnshoreBondInfos(bondUniCodes, spreadDate);
        if (CollectionUtils.isNotEmpty(onshoreBondInfoDTOs)) {
            List<List<OnshoreBondInfoDTO>> onshoreBondInfoDTOsList = Lists.
                    partition(onshoreBondInfoDTOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<OnshoreBondInfoDTO> onshoreBondInfoDTOS : onshoreBondInfoDTOsList) {
                //保险利差计算,结果落到  mysql insu_bond_yield_spread(分表)  yield_spread_bond(未完成)  pgmysql  insu_bond_yield_spread
                effectRows += insuBondYieldSpreadService.
                        calcInsuBondYieldSpreadsBySpreadDate(onshoreBondInfoDTOS, curveMaturityStructureMap, spreadDate);
            }
            // 刷完一天债券数据后 再计算主体数据
            //获取保险利差
            List<InsuBondYieldSpreadGroupDO> insuBondYieldSpreadGroupDOList = insuBondYieldSpreadDAO.
                    listInsuBondYieldSpreadGroupDOs(spreadDate);
            if (CollectionUtils.isEmpty(insuBondYieldSpreadGroupDOList)) {
                return effectRows;
            }
            List<InsuComYieldSpreadDO> comYieldSpreadDOs = BeanCopyUtils.copyList(insuBondYieldSpreadGroupDOList,
                    InsuComYieldSpreadDO.class);
            List<List<InsuComYieldSpreadDO>> comYieldSpreadDOsList = Lists.partition(comYieldSpreadDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<InsuComYieldSpreadDO> insuComYieldSpreadDOs : comYieldSpreadDOsList) {
                // 主体利差落库
                insuBondYieldSpreadService.calcInsuComYieldSpreadsBySpreadDate(insuComYieldSpreadDOs, spreadDate);
            }
        }
        return effectRows;
    }

    /**
     * 获取 剩余期限大于等于6个月小于6年 的 有效债券信息
     *
     * @param bondUniCodes
     * @param spreadDate
     * @return
     */
    private List<OnshoreBondInfoDTO> listOnshoreBondInfos(List<Long> bondUniCodes, Date spreadDate) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        List<List<Long>> lists = Lists.partition(bondUniCodes, YieldSpreadHelper.BATCH_SIZE);
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOAllList = new ArrayList<>();
        StopWatch stopWatch1 = new StopWatch();
        stopWatch1.start();
        for (List<Long> list : lists) {
            List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = bondInfoService.listOnshoreBondInfoDTOs(new HashSet<>(list), spreadDate);
            onshoreBondInfoDTOAllList.addAll(onshoreBondInfoDTOs);
        }
        logger.info("onshoreBondInfoDTOAllList:{}  stopWatch: {}  bondUniCodes:{}",
                onshoreBondInfoDTOAllList.size(), stopWatch1.getTime(), bondUniCodes.size());
        return onshoreBondInfoDTOAllList;
    }

    /**
     * 批量获取债券信息,没有剩余期限的不会返回
     *
     * @param bondUniCodes 债券唯一编码列表
     * @param spreadDate   利差日期
     * @return 境内债券信息
     */
    private List<OnshoreBondInfoDTO> listAllOnshoreBondInfos(List<Long> bondUniCodes, Date spreadDate) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        List<List<Long>> lists = Lists.partition(bondUniCodes, YieldSpreadHelper.BATCH_SIZE);
        List<OnshoreBondInfoDTO> onshoreBondInfoDTOAllList = new ArrayList<>();
        StopWatch stopWatch1 = new StopWatch();
        stopWatch1.start();
        for (List<Long> list : lists) {
            List<OnshoreBondInfoDTO> onshoreBondInfoDTOs = bondInfoService.listOnshoreBondInfos(new HashSet<>(list), spreadDate);
            onshoreBondInfoDTOAllList.addAll(onshoreBondInfoDTOs);
        }
        logger.info("listAllOnshoreBondInfos:{}  stopWatch: {}  bondUniCodes:{}",
                onshoreBondInfoDTOAllList.size(), stopWatch1.getTime(), bondUniCodes.size());
        return onshoreBondInfoDTOAllList;
    }

    private List<OnshoreBondFilterV3DTO> listOnshoreBondFilters(List<Long> bondUniCodes) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        List<List<Long>> lists = Lists.partition(bondUniCodes, YieldSpreadHelper.BATCH_SIZE);
        List<OnshoreBondFilterV3DTO> onshoreBondFilterDTOAllList = new ArrayList<>();
        StopWatch stopWatch1 = new StopWatch();
        stopWatch1.start();
        for (List<Long> list : lists) {
            List<OnshoreBondFilterV3DTO> onshoreBondFilterV3DTOS = bondInfoService.listOnshoreFilterBatchByUniCodes(list.toArray(new Long[0]));
            onshoreBondFilterDTOAllList.addAll(onshoreBondFilterV3DTOS);
        }
        logger.info("onshoreBondFilterDTOAllList:{}  stopWatch: {}  bondUniCodes:{}",
                onshoreBondFilterDTOAllList.size(), stopWatch1.getTime(), bondUniCodes.size());
        return onshoreBondFilterDTOAllList;
    }

    @Override
    public int calcComYieldSpreadChangeBySpreadDate(Date startDate, Date endDate, Set<Long> comUniCodeList) {
        int effectRows = 0;
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            try {
                effectRows += calcInduComYieldSpreadChange(spreadDate, comUniCodeList);
                effectRows += calcUdicComYieldSpreadChange(spreadDate, comUniCodeList);
                effectRows += calcSecuComYieldSpreadChange(spreadDate, comUniCodeList);
                effectRows += calcBankComYieldSpreadChange(spreadDate, comUniCodeList);
                effectRows += calcInsuComYieldSpreadChange(spreadDate, comUniCodeList);
            } catch (Exception e) {
                logger.error(String.format("can't handle calcComYieldSpreadChangeBySpreadDate spreadDate: %d", spreadDate.getTime()), e);
            }
            logger.info("[calcComYieldSpreadChangeBySpreadDate]  spreadDate:{}  stopWatch: {} 已处理主体条数:{}",
                    spreadDate, stopWatch.getTime(), effectRows);
        }
        return effectRows;
    }

    /**
     * 根据日期计算保留最新一天主体利差变动
     */
    @Override
    public void calcRecentComYieldSpreadChange() {
        Date spreadDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        if (holidayService.isHoliday(spreadDate)) {
            return;
        }
        int effectRows = 0;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        effectRows += calcInduComYieldSpreadChange(spreadDate, null);
        effectRows += calcUdicComYieldSpreadChange(spreadDate, null);
        effectRows += calcSecuComYieldSpreadChange(spreadDate, null);
        effectRows += calcBankComYieldSpreadChange(spreadDate, null);
        effectRows += calcInsuComYieldSpreadChange(spreadDate, null);
        if (effectRows > 0) {
            comYieldSpreadChangeDAO.clearOldSpreadChanges(spreadDate);
        }
        logger.info("[calcRecentComYieldSpreadChange]  spreadDate:{}  stopWatch: {} 已处理主体条数:{}",
                spreadDate, stopWatch.getTime(), effectRows);
    }

    @Override
    public int calcSecuComYieldSpreadChangeBySpreadDate(Date startDate, Date endDate, Set<Long> comUniCodeList) {
        int effectRows = 0;
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            try {
                effectRows += calcSecuComYieldSpreadChange(spreadDate, comUniCodeList);
            } catch (Exception e) {
                logger.error(String.format("can't handle calcSecuComYieldSpreadChangeBySpreadDate spreadDate: %d", spreadDate.getTime()), e);
            }
            logger.info("[calcSecuComYieldSpreadChangeBySpreadDate]  spreadDate:{}  stopWatch: {} 已处理主体条数:{}",
                    spreadDate, stopWatch.getTime(), effectRows);
        }
        return effectRows;
    }

    private int calcInduComYieldSpreadChange(Date spreadDate, Set<Long> comUniCodeList) {
        int effectRows = 0;
        if (Objects.isNull(spreadDate)) {
            return effectRows;
        }
        // 1. 根据所传日期计算3个月前、6个月前的日期,需要过滤掉假期，如果是假期，则往前推最近的工作日
        SpreadDateDTO spreadDateDTO = yieldSpreadCommonService.getSpreadDate(spreadDate);
        List<ComYieldSpreadChangeBO> comYieldSpreadChangeBOList = induComYieldSpreadDAO.listInduComYieldSpreads(spreadDate, comUniCodeList);
        if (CollectionUtils.isEmpty(comYieldSpreadChangeBOList)) {
            return effectRows;
        }

        Date threeYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE);
        Date fiveYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.FIVE_YEARS_QUANTILE);

        //取历史分位 聚合运算查询范围数据量可能很大,需要分批查
        List<List<ComYieldSpreadChangeBO>> comYieldSpreadChangeBOLists = Lists.partition(comYieldSpreadChangeBOList, COM_BATCH_SIZE);
        for (List<ComYieldSpreadChangeBO> partitionYieldSpreadChangeBOList : comYieldSpreadChangeBOLists) {
            // 填充主体利差变动
            Set<Long> partitionComUniCodeList = partitionYieldSpreadChangeBOList.stream().map(ComYieldSpreadChangeBO::getComUniCode).collect(Collectors.toSet());
            List<ComYieldSpreadChangeBO> before3MonthComYieldSpreadChangeBOList = induComYieldSpreadDAO.
                    listInduComYieldSpreads(spreadDateDTO.getBefore90Date(), partitionComUniCodeList);
            List<ComYieldSpreadChangeBO> before6MonthComYieldSpreadChangeBOList = induComYieldSpreadDAO.
                    listInduComYieldSpreads(spreadDateDTO.getBefore180Date(), partitionComUniCodeList);
            List<ComYieldSpreadQuantileViewDO> threeYearQuantileViewDOs =
                    induComYieldSpreadDAO.listInduComYieldQuantileStatistics(threeYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            List<ComYieldSpreadQuantileViewDO> fiveYearQuantileViewDOs =
                    induComYieldSpreadDAO.listInduComYieldQuantileStatistics(fiveYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            List<ComYieldSpreadChangeDO> comYieldSpreadChangeDOList = fillSpreadChange(partitionYieldSpreadChangeBOList,
                    before3MonthComYieldSpreadChangeBOList, before6MonthComYieldSpreadChangeBOList,
                    threeYearQuantileViewDOs, fiveYearQuantileViewDOs,
                    UrbanInvestEnum.NOT_URBAN_INVEST.getValue(), ComYieldSpreadSectorEnum.INDU.getValue());
            if (CollectionUtils.isNotEmpty(comYieldSpreadChangeDOList)) {
                effectRows += comYieldSpreadChangeDAO.saveComYieldSpreadChanges(spreadDate, comYieldSpreadChangeDOList, ComYieldSpreadSectorEnum.INDU.getValue());
            }
        }
        return effectRows;
    }

    private int calcUdicComYieldSpreadChange(Date spreadDate, Set<Long> comUniCodeList) {
        int effectRows = 0;
        if (Objects.isNull(spreadDate) || StringUtils.isNotBlank(ShardingHindStrParamUtil.getHindStrParam())) {
            return effectRows;
        }
        // 1. 根据所传日期计算3个月前、6个月前的日期,需要过滤掉假期，如果是假期，则往前推最近的工作日
        SpreadDateDTO spreadDateDTO = yieldSpreadCommonService.getSpreadDate(spreadDate);
        List<ComYieldSpreadChangeBO> comYieldSpreadChangeBOList = udicComYieldSpreadDAO.listUdicComYieldSpreads(spreadDate, comUniCodeList);
        if (CollectionUtils.isEmpty(comYieldSpreadChangeBOList)) {
            return effectRows;
        }

        Date threeYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE);
        Date fiveYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.FIVE_YEARS_QUANTILE);

        //取历史分位 聚合运算查询范围数据量可能很大,需要分批查
        List<List<ComYieldSpreadChangeBO>> comYieldSpreadChangeBOLists = Lists.partition(comYieldSpreadChangeBOList, COM_BATCH_SIZE);
        for (List<ComYieldSpreadChangeBO> partitionYieldSpreadChangeBOList : comYieldSpreadChangeBOLists) {
            Set<Long> partitionComUniCodeList = partitionYieldSpreadChangeBOList.stream().map(ComYieldSpreadChangeBO::getComUniCode).collect(Collectors.toSet());
            List<ComYieldSpreadChangeBO> before3MonthComYieldSpreadChangeBOList = udicComYieldSpreadDAO.
                    listUdicComYieldSpreads(spreadDateDTO.getBefore90Date(), partitionComUniCodeList);
            List<ComYieldSpreadChangeBO> before6MonthComYieldSpreadChangeBOList = udicComYieldSpreadDAO.
                    listUdicComYieldSpreads(spreadDateDTO.getBefore180Date(), partitionComUniCodeList);
            List<ComYieldSpreadQuantileViewDO> threeYearQuantileViewDOs =
                    udicComYieldSpreadDAO.listUdicComYieldQuantileStatistics(threeYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            List<ComYieldSpreadQuantileViewDO> fiveYearQuantileViewDOs =
                    udicComYieldSpreadDAO.listUdicComYieldQuantileStatistics(fiveYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            List<ComYieldSpreadChangeDO> comYieldSpreadChangeDOList = fillSpreadChange(partitionYieldSpreadChangeBOList,
                    before3MonthComYieldSpreadChangeBOList, before6MonthComYieldSpreadChangeBOList,
                    threeYearQuantileViewDOs, fiveYearQuantileViewDOs,
                    UrbanInvestEnum.NOT_URBAN_INVEST.getValue(), ComYieldSpreadSectorEnum.UDIC.getValue());
            if (CollectionUtils.isNotEmpty(comYieldSpreadChangeDOList)) {
                effectRows += comYieldSpreadChangeDAO.saveComYieldSpreadChanges(spreadDate, comYieldSpreadChangeDOList, ComYieldSpreadSectorEnum.UDIC.getValue());
            }
        }
        return effectRows;
    }

    private int calcSecuComYieldSpreadChange(Date spreadDate, Set<Long> comUniCodeList) {
        int effectRows = 0;
        if (Objects.isNull(spreadDate)) {
            return effectRows;
        }
        // 1. 根据所传日期计算3个月前、6个月前的日期,需要过滤掉假期，如果是假期，则往前推最近的工作日
        SpreadDateDTO spreadDateDTO = yieldSpreadCommonService.getSpreadDate(spreadDate);
        List<ComYieldSpreadChangeBO> comYieldSpreadChangeBOList = secuComYieldSpreadDAO.listSecuComYieldSpreads(spreadDate, comUniCodeList);
        if (CollectionUtils.isEmpty(comYieldSpreadChangeBOList)) {
            return effectRows;
        }

        Date threeYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE);
        Date fiveYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.FIVE_YEARS_QUANTILE);

        //取历史分位 聚合运算查询范围数据量可能很大,需要分批查
        List<List<ComYieldSpreadChangeBO>> comYieldSpreadChangeBOLists = Lists.partition(comYieldSpreadChangeBOList, COM_BATCH_SIZE);
        for (List<ComYieldSpreadChangeBO> partitionYieldSpreadChangeBOList : comYieldSpreadChangeBOLists) {
            // 填充主体利差变动
            Set<Long> partitionComUniCodeList = partitionYieldSpreadChangeBOList.stream().map(ComYieldSpreadChangeBO::getComUniCode).collect(Collectors.toSet());
            List<ComYieldSpreadChangeBO> before3MonthComYieldSpreadChangeBOList = secuComYieldSpreadDAO.
                    listSecuComYieldSpreads(spreadDateDTO.getBefore90Date(), partitionComUniCodeList);
            List<ComYieldSpreadChangeBO> before6MonthComYieldSpreadChangeBOList = secuComYieldSpreadDAO.
                    listSecuComYieldSpreads(spreadDateDTO.getBefore180Date(), partitionComUniCodeList);
            List<ComYieldSpreadQuantileViewDO> threeYearQuantileViewDOs =
                    secuComYieldSpreadDAO.listSecuComYieldQuantileStatistics(threeYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            List<ComYieldSpreadQuantileViewDO> fiveYearQuantileViewDOs =
                    secuComYieldSpreadDAO.listSecuComYieldQuantileStatistics(fiveYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            // 填充主体利差变动
            List<ComYieldSpreadChangeDO> comYieldSpreadChangeDOList = fillSpreadChange(partitionYieldSpreadChangeBOList,
                    before3MonthComYieldSpreadChangeBOList, before6MonthComYieldSpreadChangeBOList,
                    threeYearQuantileViewDOs, fiveYearQuantileViewDOs,
                    UrbanInvestEnum.NOT_URBAN_INVEST.getValue(), ComYieldSpreadSectorEnum.SECU.getValue());
            if (CollectionUtils.isNotEmpty(comYieldSpreadChangeDOList)) {
                effectRows += comYieldSpreadChangeDAO.saveComYieldSpreadChanges(spreadDate, comYieldSpreadChangeDOList, ComYieldSpreadSectorEnum.SECU.getValue());
            }
        }
        return effectRows;
    }

    private int calcInsuComYieldSpreadChange(Date spreadDate, Set<Long> comUniCodeList) {
        int effectRows = 0;
        if (Objects.isNull(spreadDate)) {
            return effectRows;
        }
        // 1. 根据所传日期计算3个月前、6个月前的日期,需要过滤掉假期，如果是假期，则往前推最近的工作日
        SpreadDateDTO spreadDateDTO = yieldSpreadCommonService.getSpreadDate(spreadDate);
        List<ComYieldSpreadChangeBO> comYieldSpreadChangeBOList = insuComYieldSpreadDAO.listInsuComYieldSpreads(spreadDate, comUniCodeList);
        if (CollectionUtils.isEmpty(comYieldSpreadChangeBOList)) {
            return effectRows;
        }

        Date threeYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE);
        Date fiveYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.FIVE_YEARS_QUANTILE);

        //取历史分位 聚合运算查询范围数据量可能很大,需要分批查
        List<List<ComYieldSpreadChangeBO>> comYieldSpreadChangeBOLists = Lists.partition(comYieldSpreadChangeBOList, COM_BATCH_SIZE);
        for (List<ComYieldSpreadChangeBO> partitionYieldSpreadChangeBOList : comYieldSpreadChangeBOLists) {
            // 填充主体利差变动
            Set<Long> partitionComUniCodeList = partitionYieldSpreadChangeBOList.stream().map(ComYieldSpreadChangeBO::getComUniCode).collect(Collectors.toSet());
            List<ComYieldSpreadChangeBO> before3MonthComYieldSpreadChangeBOList = insuComYieldSpreadDAO.
                    listInsuComYieldSpreads(spreadDateDTO.getBefore90Date(), partitionComUniCodeList);
            List<ComYieldSpreadChangeBO> before6MonthComYieldSpreadChangeBOList = insuComYieldSpreadDAO.
                    listInsuComYieldSpreads(spreadDateDTO.getBefore180Date(), partitionComUniCodeList);
            List<ComYieldSpreadQuantileViewDO> threeYearQuantileViewDOs =
                    insuComYieldSpreadDAO.listInsuComYieldQuantileStatistics(threeYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            List<ComYieldSpreadQuantileViewDO> fiveYearQuantileViewDOs =
                    insuComYieldSpreadDAO.listInsuComYieldQuantileStatistics(fiveYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            // 填充主体利差变动
            List<ComYieldSpreadChangeDO> comYieldSpreadChangeDOList = fillSpreadChange(partitionYieldSpreadChangeBOList,
                    before3MonthComYieldSpreadChangeBOList, before6MonthComYieldSpreadChangeBOList,
                    threeYearQuantileViewDOs, fiveYearQuantileViewDOs,
                    UrbanInvestEnum.NOT_URBAN_INVEST.getValue(), ComYieldSpreadSectorEnum.INSU.getValue());
            if (CollectionUtils.isNotEmpty(comYieldSpreadChangeDOList)) {
                effectRows += comYieldSpreadChangeDAO.saveComYieldSpreadChanges(spreadDate, comYieldSpreadChangeDOList, ComYieldSpreadSectorEnum.INSU.getValue());
            }
        }
        return effectRows;
    }

    private int calcBankComYieldSpreadChange(Date spreadDate, Set<Long> comUniCodeList) {
        int effectRows = 0;
        if (Objects.isNull(spreadDate)) {
            return effectRows;
        }
        // 1. 根据所传日期计算3个月前、6个月前的日期,需要过滤掉假期，如果是假期，则往前推最近的工作日
        SpreadDateDTO spreadDateDTO = yieldSpreadCommonService.getSpreadDate(spreadDate);
        List<ComYieldSpreadChangeBO> comYieldSpreadChangeBOList = bankComYieldSpreadDAO.listBankComYieldSpreads(spreadDate, comUniCodeList);
        if (CollectionUtils.isEmpty(comYieldSpreadChangeBOList)) {
            return effectRows;
        }

        Date threeYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE);
        Date fiveYearStartDate = yieldSpreadCommonService.getQuantileStartDate(spreadDate, SpreadQuantileTypeEnum.FIVE_YEARS_QUANTILE);

        //取历史分位 聚合运算查询范围数据量可能很大,需要分批查
        List<List<ComYieldSpreadChangeBO>> comYieldSpreadChangeBOLists = Lists.partition(comYieldSpreadChangeBOList, COM_BATCH_SIZE);
        for (List<ComYieldSpreadChangeBO> partitionYieldSpreadChangeBOList : comYieldSpreadChangeBOLists) {
            // 填充主体利差变动
            Set<Long> partitionComUniCodeList = partitionYieldSpreadChangeBOList.stream().map(ComYieldSpreadChangeBO::getComUniCode).collect(Collectors.toSet());
            List<ComYieldSpreadChangeBO> before3MonthComYieldSpreadChangeBOList = bankComYieldSpreadDAO.
                    listBankComYieldSpreads(spreadDateDTO.getBefore90Date(), partitionComUniCodeList);
            List<ComYieldSpreadChangeBO> before6MonthComYieldSpreadChangeBOList = bankComYieldSpreadDAO.
                    listBankComYieldSpreads(spreadDateDTO.getBefore180Date(), partitionComUniCodeList);
            List<ComYieldSpreadQuantileViewDO> threeYearQuantileViewDOs =
                    bankComYieldSpreadDAO.listBankComYieldQuantileStatistics(threeYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            List<ComYieldSpreadQuantileViewDO> fiveYearQuantileViewDOs =
                    bankComYieldSpreadDAO.listBankComYieldQuantileStatistics(fiveYearStartDate, spreadDate, spreadDate, Lists.newArrayList(partitionComUniCodeList));
            // 填充主体利差变动
            List<ComYieldSpreadChangeDO> comYieldSpreadChangeDOList = fillSpreadChange(partitionYieldSpreadChangeBOList,
                    before3MonthComYieldSpreadChangeBOList, before6MonthComYieldSpreadChangeBOList,
                    threeYearQuantileViewDOs, fiveYearQuantileViewDOs,
                    UrbanInvestEnum.NOT_URBAN_INVEST.getValue(), ComYieldSpreadSectorEnum.BANK.getValue());
            if (CollectionUtils.isNotEmpty(comYieldSpreadChangeDOList)) {
                effectRows += comYieldSpreadChangeDAO.saveComYieldSpreadChanges(spreadDate, comYieldSpreadChangeDOList, ComYieldSpreadSectorEnum.BANK.getValue());
            }
        }
        return effectRows;
    }

    private List<ComYieldSpreadChangeDO> fillSpreadChange(List<ComYieldSpreadChangeBO> udicSpreadPanoramaBOList,
                                                          List<ComYieldSpreadChangeBO> before3MonthComYieldSpreadChangeBOList,
                                                          List<ComYieldSpreadChangeBO> before6MonthComYieldSpreadChangeBOList,
                                                          List<ComYieldSpreadQuantileViewDO> threeYearQuantileViewDOs,
                                                          List<ComYieldSpreadQuantileViewDO> fiveYearQuantileViewDOs,
                                                          Integer udicStatus, Integer yieldSpreadSector) {
        if (CollectionUtils.isEmpty(udicSpreadPanoramaBOList)) {
            return Collections.emptyList();
        }
        //变动比较数据
        Map<Long, ComYieldSpreadChangeBO> before3monthComYieldSpreadBOMap = before3MonthComYieldSpreadChangeBOList.stream()
                .collect(Collectors.toMap(ComYieldSpreadChangeBO::getComUniCode, Function.identity(), (x1, x2) -> x2));
        Map<Long, ComYieldSpreadChangeBO> before6monthComYieldSpreadBOMap = before6MonthComYieldSpreadChangeBOList.stream()
                .collect(Collectors.toMap(ComYieldSpreadChangeBO::getComUniCode, Function.identity(), (x1, x2) -> x2));
        //分为计算数据
        Map<Long, ComYieldSpreadQuantileViewDO> threeYearQuantileMap = threeYearQuantileViewDOs.stream()
                .collect(Collectors.toMap(ComYieldSpreadQuantileViewDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
        Map<Long, ComYieldSpreadQuantileViewDO> fiveYearQuantileMap = fiveYearQuantileViewDOs.stream()
                .collect(Collectors.toMap(ComYieldSpreadQuantileViewDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
        List<ComYieldSpreadChangeDO> comYieldSpreadChangeDOList = new ArrayList<>();
        for (ComYieldSpreadChangeBO comYieldSpreadChangeBO : udicSpreadPanoramaBOList) {
            Optional<ComYieldSpreadChangeDO> optionalComYieldSpreadChangeDO = calcSpreadChange(comYieldSpreadChangeBO,
                    before3monthComYieldSpreadBOMap,
                    before6monthComYieldSpreadBOMap, threeYearQuantileMap, fiveYearQuantileMap, udicStatus, yieldSpreadSector);
            optionalComYieldSpreadChangeDO.ifPresent(comYieldSpreadChangeDOList::add);
        }
        return comYieldSpreadChangeDOList;
    }

    private Optional<ComYieldSpreadChangeDO> calcSpreadChange(ComYieldSpreadChangeBO comYieldSpreadChangeBO,
                                                              Map<Long, ComYieldSpreadChangeBO> before3monthComYieldSpreadBOMap,
                                                              Map<Long, ComYieldSpreadChangeBO> before6monthComYieldSpreadBOMap,
                                                              Map<Long, ComYieldSpreadQuantileViewDO> threeYearQuantileMap,
                                                              Map<Long, ComYieldSpreadQuantileViewDO> fiveYearQuantileMap,
                                                              Integer udicStatus, Integer yieldSpreadSector) {
        if (Objects.isNull(comYieldSpreadChangeBO)) {
            return Optional.empty();
        }
        ComYieldSpreadChangeDO result = BeanCopyUtils.copyProperties(comYieldSpreadChangeBO, ComYieldSpreadChangeDO.class);
        ComYieldSpreadChangeBO before3MonthComYieldSpreadChangeBO = before3monthComYieldSpreadBOMap
                .get(comYieldSpreadChangeBO.getComUniCode());
        ComYieldSpreadChangeBO before6MonthComYieldSpreadChangeBO = before6monthComYieldSpreadBOMap
                .get(comYieldSpreadChangeBO.getComUniCode());

        ComYieldSpreadQuantileViewDO threeYearQuantileViewDO = threeYearQuantileMap.get(comYieldSpreadChangeBO.getComUniCode());
        ComYieldSpreadQuantileViewDO fiveYearQuantileViewDO = fiveYearQuantileMap.get(comYieldSpreadChangeBO.getComUniCode());

        if (Objects.isNull(before3MonthComYieldSpreadChangeBO) && Objects.isNull(before6MonthComYieldSpreadChangeBO) &&
                Objects.isNull(threeYearQuantileViewDO) && Objects.isNull(fiveYearQuantileViewDO)) {
            return Optional.empty();
        }
        if (Objects.nonNull(before3MonthComYieldSpreadChangeBO)) {
            BigDecimal bondCreditSpreadChange3M =
                    subtract(comYieldSpreadChangeBO.getComCreditSpread(),
                            before3MonthComYieldSpreadChangeBO.getComCreditSpread()).orElse(null);
            result.setCreditSpreadChange3M(bondCreditSpreadChange3M);
            BigDecimal bondExcessSpreadChange3M =
                    subtract(comYieldSpreadChangeBO.getComExcessSpread(),
                            before3MonthComYieldSpreadChangeBO.getComExcessSpread()).orElse(null);
            result.setExcessSpreadChange3M(bondExcessSpreadChange3M);
        }
        if (Objects.nonNull(before6MonthComYieldSpreadChangeBO)) {
            BigDecimal bondCreditSpreadChange6M =
                    subtract(comYieldSpreadChangeBO.getComCreditSpread(),
                            before6MonthComYieldSpreadChangeBO.getComCreditSpread()).orElse(null);
            result.setCreditSpreadChange6M(bondCreditSpreadChange6M);
            BigDecimal bondExcessSpreadChange6M =
                    subtract(comYieldSpreadChangeBO.getComExcessSpread(),
                            before6MonthComYieldSpreadChangeBO.getComExcessSpread()).orElse(null);
            result.setExcessSpreadChange6M(bondExcessSpreadChange6M);
        }
        if (Objects.nonNull(threeYearQuantileViewDO)) {
            Integer comCreditSpreadLessIssueCount = threeYearQuantileViewDO.getComCreditSpreadLessIssueCount();
            Integer comCreditSpreadCount = threeYearQuantileViewDO.getComCreditSpreadCount();
            Integer comExcessSpreadLessIssueCount = threeYearQuantileViewDO.getComExcessSpreadLessIssueCount();
            Integer comExcessSpreadCount = threeYearQuantileViewDO.getComExcessSpreadCount();
            result.setCreditSpreadQuantile3Y(CalculationHelper.safeCalPercentRankIgnore(comCreditSpreadLessIssueCount, comCreditSpreadCount, COM_QUANTILE_SCALE).orElse(null));
            result.setExcessSpreadQuantile3Y(CalculationHelper.safeCalPercentRankIgnore(comExcessSpreadLessIssueCount, comExcessSpreadCount, COM_QUANTILE_SCALE).orElse(null));
        }
        if (Objects.nonNull(fiveYearQuantileViewDO)) {
            Integer comCreditSpreadLessIssueCount = fiveYearQuantileViewDO.getComCreditSpreadLessIssueCount();
            Integer comCreditSpreadCount = fiveYearQuantileViewDO.getComCreditSpreadCount();
            Integer comExcessSpreadLessIssueCount = fiveYearQuantileViewDO.getComExcessSpreadLessIssueCount();
            Integer comExcessSpreadCount = fiveYearQuantileViewDO.getComExcessSpreadCount();
            result.setCreditSpreadQuantile5Y(CalculationHelper.safeCalPercentRankIgnore(comCreditSpreadLessIssueCount, comCreditSpreadCount, COM_QUANTILE_SCALE).orElse(null));
            result.setExcessSpreadQuantile5Y(CalculationHelper.safeCalPercentRankIgnore(comExcessSpreadLessIssueCount, comExcessSpreadCount, COM_QUANTILE_SCALE).orElse(null));
        }

        result.setUdicStatus(udicStatus);
        result.setComSpreadSector(yieldSpreadSector);
        result.setDeleted(Deleted.NO_DELETED.getValue());
        return Optional.of(result);
    }

    private void setCustomComMaxSpreadDate(Date spreadDate) {
        String oldDate = stringRedisTemplate.opsForValue().get(MAX_CUSTOM_COM_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(oldDate) && spreadDate.compareTo(Date.valueOf(oldDate)) <= 0) {
            return;
        }
        stringRedisTemplate.opsForValue().set(MAX_CUSTOM_COM_SPREAD_DATE_KEY, spreadDate.toString());
    }

    private void setCustomBondMaxSpreadDate(Date spreadDate) {
        String oldDate = stringRedisTemplate.opsForValue().get(MAX_CUSTOM_BOND_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(oldDate) && spreadDate.compareTo(Date.valueOf(oldDate)) <= 0) {
            return;
        }
        stringRedisTemplate.opsForValue().set(MAX_CUSTOM_BOND_SPREAD_DATE_KEY, spreadDate.toString());
    }

    @Override
    public int calcBondYieldSpread(Date startDate, Date endDate, List<Long> bondUniCodes) {
        logger.info("[calcBondYieldSpread] begin.start time:{}.", LocalDateTime.now());
        DateRangeDTO dateRange = redressCalcDate(startDate, endDate);
        logger.info("[calcBondYieldSpread] practical date ==> startDate:{},endDate:{}.", startDate, endDate);
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(dateRange.getStartDate(), dateRange.getEndDate());
        int effectRows = 0;
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        for (Date spreadDate : spreadDates) {
            try {
                effectRows += calcBondYieldSpreadByDay(spreadDate, bondUniCodes);
            } catch (Exception e) {
                logger.error("[calcBondYieldSpread] error. spreadDate is {}.", spreadDate, e);
                notificationSender.sendMarkdown(NotifyConst.TagKey.CALC_YIELD_SPREAD, "[calcBondYieldSpread]计算" + spreadDate + "债券利差异常:" + e.getMessage());
            }
        }
        logger.info("[calcBondYieldSpread] end.end time:{}.total number:{}", LocalDateTime.now(), effectRows);
        return effectRows;
    }

    private DateRangeDTO redressCalcDate(Date startDate, Date endDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        } else {
            if (startDate.compareTo(endDate) > 0) {
                Date temp = startDate;
                startDate = endDate;
                endDate = temp;
            }
            if (startDate.before(YieldSpreadConst.BOND_YIELD_SPREAD_START_DATE)) {
                startDate = YieldSpreadConst.BOND_YIELD_SPREAD_START_DATE;
            }
            Date yesterday = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            if (endDate.after(yesterday)) {
                endDate = yesterday;
            }
        }
        return new DateRangeDTO(startDate, endDate);
    }

    private int calcBondYieldSpreadByDay(Date spreadDate, List<Long> bondUniCodes) {
        RLock lock = redissonClient.getLock(String.format(RedisLockConst.CALC_BOND_YIELD_SPREAD, spreadDate));
        int effectRows = 0;
        if (!lock.tryLock()) {
            logger.info("[calcBondYieldSpread] {}债券利差正在计算，请勿重复执行！", spreadDate);
            return effectRows;
        }
        try {
            if (holidayService.isHoliday(spreadDate)) {
                return effectRows;
            }
            long startTime = System.currentTimeMillis();
            effectRows = bondYieldSpreadService.calcBondYieldSpread(spreadDate, bondUniCodes);
            long elapsedTime = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis() - startTime);
            logger.info("[calcBondYieldSpread] spreadDate:{}.elapsed seconds:{}.dispose bond number:{}.", spreadDate, elapsedTime, effectRows);
            return effectRows;
        } finally {
            lock.unlock();
        }
    }

}
