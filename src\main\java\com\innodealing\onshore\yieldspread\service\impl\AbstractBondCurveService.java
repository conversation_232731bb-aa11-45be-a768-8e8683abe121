package com.innodealing.onshore.yieldspread.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.innodealing.commons.encrypt.MD5Utils;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.consts.RedisLockConst;
import com.innodealing.onshore.yieldspread.consts.TipsConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserCurveDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserSelectedCurveDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.YieldSpreadBondDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.redis.UserCurveRedisDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadCodeTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionBO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.UniversalYieldSpreadSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.AbstractCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.ComOrBondConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.ICurveGenerateReq;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveDataResDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserSelectedCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import com.innodealing.onshore.yieldspread.service.CurvePoolService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.Nullable;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;

/**
 * 曲线基类
 *
 * <AUTHOR>
 */
public abstract class AbstractBondCurveService extends AbstractBaseYieldSpreadService {

    protected static final int MIN_SAMPLE_BOND_SIZE = 1;

    protected static final int MIN_SAMPLE_BOND_SIZE_FOR_COM_OR_BOND = 1;

    @Resource
    private UserCurveDAO userCurveDAO;

    @Resource
    private UserSelectedCurveDAO userSelectedCurveDAO;

    @Resource
    private YieldSpreadBondDAO yieldSpreadBondDAO;

    @Resource
    private UserCurveRedisDAO userCurveRedisDAO;

    @Resource
    protected RedissonClient redissonClient;

    @Resource
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    protected CurvePoolService curvePoolService;

    /**
     * 保存曲线
     *
     * @param userid       用户id
     * @param curveGroupId 曲线组id
     * @param params       保存参数
     * @return 是否保存成功
     */
    protected boolean generalSaveCurve(Long userid, Long curveGroupId, ICurveGenerateReq params) {
        RLock lock = redissonClient.getLock(String.format(RedisLockConst.CURVE_POOL_USER, userid));
        try {
            lock.lock();
            String curveName = params.getCurveName();
            this.generalCheckBeforeSave(userid, curveName);
            this.specialCheckBeforeSave(userid, params);
            this.checkHasSampleBondSize(params);
            UserCurveDO userCurveDO = new UserCurveDO();
            userCurveDO.setUserId(userid);
            userCurveDO.setSpreadCurveName(curveName);
            userCurveDO.setSpreadCurveType(getCurveType().getValue());
            userCurveDO.setFilterCondition(JSONObject.toJSONString(params));
            Long defaultGroupId = Optional.ofNullable(curveGroupId).orElseGet(() -> curvePoolService.getDefaultGroupIdOrAddIfNotExist(userid));
            userCurveDO.setCurveGroupId(defaultGroupId);
            userCurveDO.setCurveOrder(curvePoolService.getCurveNextOrder(defaultGroupId));
            this.modifyProperty(params, userCurveDO);
            boolean result = userCurveDAO.insert(userCurveDO);
            if (result && Objects.nonNull(userCurveDO.getId())) {
                this.doOtherBusinessAfterSave(userCurveDO);
                // 自定义曲线在生成成功时选定
                if (CurveTypeEnum.CUSTOMIZATION != this.getCurveType()) {
                    this.selectedThisCurve(userid, userCurveDO.getId());
                }
            }
            return result;
        } finally {
            lock.unlock();
        }
    }

    protected void selectedThisCurve(Long userid, Long curveId) {
        Optional<UserSelectedCurveDO> selectedCurveOptional = userSelectedCurveDAO.getByUserid(userid);
        UserSelectedCurveDO userSelectedCurveDO;
        if (selectedCurveOptional.isPresent()) {
            userSelectedCurveDO = selectedCurveOptional.get();
            String selectedCurveStr = userSelectedCurveDO.getSelectedCurve();
            Set<Long> curveIds = new HashSet<>(JSON.parseArray(StringUtils.isBlank(selectedCurveStr) ? "[]" : selectedCurveStr, Long.class));
            if (curveIds.size() < YieldSpreadConst.SELECTED_CURVE_UPPER_LIMIT) {
                curveIds.add(curveId);
            } else {
                Optional<CurveDefinitionBO> curveDefinitionOptional = userCurveDAO.getOldestCurve(curveIds);
                if (curveDefinitionOptional.isPresent()) {
                    curveIds.remove(curveDefinitionOptional.get().getId());
                    curveIds.add(curveId);
                } else {
                    curveIds = Sets.newHashSet(curveId);
                }
            }
            userSelectedCurveDO.setSelectedCurve(JSON.toJSONString(curveIds));
            userSelectedCurveDAO.updateByPrimaryKeySelective(userSelectedCurveDO);
        } else {
            userSelectedCurveDO = new UserSelectedCurveDO();
            userSelectedCurveDO.setUserId(userid);
            userSelectedCurveDO.setSelectedCurve(JSON.toJSONString(Sets.newHashSet(curveId)));
            userSelectedCurveDAO.insert(userSelectedCurveDO);
        }

    }

    private void checkHasSampleBondSize(ICurveGenerateReq params) {
        if (CurveTypeEnum.CUSTOMIZATION.equals(getCurveType()) || CurveTypeEnum.CB.equals(getCurveType())) {
            return;
        }
        List<CurveDataResDTO> curveDataList = listCurveData(params);
        AbstractCurveGenerateConditionReqDTO filterCondition = (AbstractCurveGenerateConditionReqDTO) params;
        ComOrBondConditionReqDTO comOrBondCondition = filterCondition.getComOrBondCondition();
        boolean isComOrBond = Objects.nonNull(comOrBondCondition) && Objects.nonNull(comOrBondCondition.getConditionType());
        if (CollectionUtils.isEmpty(curveDataList)) {
            throw new TipsException("该方案样本券不足" + this.getMinSampleBondSize(isComOrBond) + "只无法生成利差曲线");
        }
    }

    /**
     * 更新曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @param params  更新参数
     * @return 操作结果
     */
    protected boolean generalUpdateCurve(Long userid, Long curveId, ICurveGenerateReq params) {
        RLock lock = redissonClient.getLock(String.format(RedisLockConst.CURVE_POOL_USER, userid));
        try {
            lock.lock();
            UserCurveDO oldUserCurve = userCurveDAO.get(userid, curveId).orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
            String oldName = oldUserCurve.getSpreadCurveName();
            String newName = params.getCurveName();
            boolean sameCondition = this.isSameCondition(oldUserCurve, params);
            boolean sameName = oldName.equals(newName);
            if (sameName && sameCondition) {
                return true;
            }
            if (!sameName && userCurveDAO.isExist(userid, newName)) {
                throw new TipsException("曲线名称重复");
            }
            UserCurveDO userCurveDO = new UserCurveDO();
            userCurveDO.setId(oldUserCurve.getId());
            userCurveDO.setSpreadCurveName(newName);
            if (sameCondition) {
                return userCurveDAO.updateByPrimaryKeySelective(userCurveDO);
            }
            this.checkHasSampleBondSize(params);
            userCurveDO.setSpreadCurveType(getCurveType().getValue());
            userCurveDO.setFilterCondition(JSONObject.toJSONString(params));
            this.modifyProperty(params, userCurveDO);
            boolean result = userCurveDAO.updateByPrimaryKeySelective(userCurveDO);
            if (result) {
                userCurveRedisDAO.removeCurveCache(curveId);
                this.doOtherBusinessAfterSave(userCurveDO);
            }
            return result;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 是否是相同的条件，有特殊判断逻辑子类可重写
     *
     * @param oldUserCurve 旧的曲线定义数据
     * @param params       更新曲线参数
     * @return 是否是相同的条件
     */
    protected boolean isSameCondition(UserCurveDO oldUserCurve, ICurveGenerateReq params) {
        String oldConditionMd5 = MD5Utils.getMD5String(oldUserCurve.getFilterCondition());
        String newConditionMd5 = MD5Utils.getMD5String(JSON.toJSONString(params));
        return oldConditionMd5.equals(newConditionMd5);
    }

    protected <R extends UniversalYieldSpreadSearchParam> R buildBondYieldSearchParam(
            YieldSpreadSearchReqDTO request, AbstractCurveGenerateConditionReqDTO generateRequest, Class<R> clazz,
            SortDTO sort) {
        R searchParam = BeanCopyUtils.copyProperties(generateRequest, clazz);
        searchParam.setSpreadDate(request.getSpreadDate());
        if (Objects.isNull(request.getSpreadDate()) || DateExtensionUtils.isSameDay(request.getSpreadDate(), new Date(System.currentTimeMillis()))) {
            searchParam.setSpreadDate(this.getMaxSpreadDate());
        }
        searchParam.setSort(Objects.isNull(sort) ? request.getSort() : sort);
        ComOrBondConditionReqDTO comOrBondCondition = generateRequest.getComOrBondCondition();
        if (Objects.nonNull(comOrBondCondition)) {
            searchParam.setComOrBondUniCode(comOrBondCondition.getConditionType(), comOrBondCondition.getUniCode());
        }
        if (Objects.nonNull(request.getUniCodeType()) && Objects.nonNull(request.getUniCode())) {
            if (request.getUniCodeType().equals(SpreadCodeTypeEnum.COM_CODE.getValue())) {
                searchParam.setComUniCode(request.getUniCode());
            } else {
                searchParam.setBondUniCode(request.getUniCode());
            }
        }
        searchParam.setPageNum(request.getPageNum());
        searchParam.setPageSize(request.getPageSize());
        return searchParam;
    }

    protected <R extends AbstractCurveGenerateConditionReqDTO> R getCurveGenerateCondition(Long curveId, Class<R> clazz) {
        return getCurveGenerateCondition(null, curveId, clazz);
    }

    protected <R extends AbstractCurveGenerateConditionReqDTO> R getCurveGenerateCondition(@Nullable Long userid, Long curveId, Class<R> clazz) {
        Optional<CurveDefinitionBO> curveOptional = userCurveDAO.getCurveDefinitionBO(curveId);
        CurveDefinitionBO curveBO = curveOptional.orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        if (!Objects.isNull(userid)) {
            super.checkBelongAndCurveType(userid, curveBO.getUserId(), curveBO.getSpreadCurveType());
        }
        String filterCondition = curveBO.getFilterCondition();
        try {
            return StringUtils.isBlank(filterCondition) ? clazz.newInstance() : JSON.parseObject(filterCondition, clazz);
        } catch (Exception e) {
            logger.error("newInstance CurveGenerateConditionReqDTO error,filterCondition is :{}", filterCondition, e);
            throw new BusinessException("实例化曲线生成条件对象失败");
        }
    }

    /**
     * 判断是否超出搜索范围，如果曲线筛选条件选了单券，表区筛选条件不是这条单券，就会返回true
     *
     * @param filterUniCode      表区筛选条件债券code
     * @param filterUniCodeType  筛选的code类型
     * @param comOrBondCondition 曲线筛选条件
     * @return 是否超出搜索范围
     */
    protected boolean isOutOfSearchRange(Long filterUniCode, Integer filterUniCodeType, ComOrBondConditionReqDTO comOrBondCondition) {
        SpreadCodeTypeEnum codeType = EnumUtils.getEnumNullable(SpreadCodeTypeEnum.class, filterUniCodeType);
        if (Objects.isNull(filterUniCode) || Objects.isNull(codeType) || Objects.isNull(comOrBondCondition)) {
            return false;
        }
        Integer storeConditionType = comOrBondCondition.getConditionType();
        Long storeUniCode = comOrBondCondition.getUniCode();
        if (Objects.isNull(storeConditionType) || Objects.isNull(storeUniCode)) {
            return false;
        }
        return filterUniCodeType.equals(storeConditionType) && !filterUniCode.equals(storeUniCode);
    }

    /**
     * 曲线数据
     *
     * @param curveId 曲线id
     * @return 曲线数据
     */
    protected abstract List<CurveDataResDTO> listCurveData(Long curveId);

    /**
     * 曲线数据
     *
     * @param curveGenerateParam 曲线生成参数
     * @return 曲线数据
     */
    protected abstract List<CurveDataResDTO> listCurveData(ICurveGenerateReq curveGenerateParam);

    /**
     * 保存前的特殊校验
     *
     * @param userid 用户ID
     * @param params 曲线参数
     */
    protected void specialCheckBeforeSave(Long userid, ICurveGenerateReq params) {
    }

    /**
     * 调整DO属性
     *
     * @param params      曲线保存参数
     * @param userCurveDO 保存实体对象
     */
    protected void modifyProperty(ICurveGenerateReq params, UserCurveDO userCurveDO) {
    }

    /**
     * 保存后做其他操作
     *
     * @param userCurveDO 曲线数据
     */
    protected void doOtherBusinessAfterSave(UserCurveDO userCurveDO) {
    }

    /**
     * 把BondYieldSpreadBO转换为CurveDataResDTO，并过滤掉不满足债券统计数的数据
     *
     * @param yieldSpreads      利差数据
     * @param minSampleBondSize 最少的样本债数量
     * @return CurveDataResDTO
     */
    protected List<CurveDataResDTO> convertToCurveDataResDTOsAndFilterData(List<BondYieldSpreadBO> yieldSpreads, int minSampleBondSize) {
        List<CurveDataResDTO> curveDataList = new ArrayList<>();
        if (CollectionUtils.isEmpty(yieldSpreads)) {
            return curveDataList;
        }
        for (BondYieldSpreadBO yieldSpread : yieldSpreads) {
            CurveDataResDTO curveDataResDTO = new CurveDataResDTO();
            boolean cscExceedMinSize = yieldSpread.getBondCreditSpreadCount() >= minSampleBondSize;
            boolean escExceedMinSize = yieldSpread.getBondExcessSpreadCount() >= minSampleBondSize;
            boolean cbycExceedMinSize = yieldSpread.getCbYieldCount() >= minSampleBondSize;
            curveDataResDTO.setSpreadDate(yieldSpread.getSpreadDate());
            curveDataResDTO.setBondCreditSpread(cscExceedMinSize ? yieldSpread.getBondCreditSpread() : null);
            curveDataResDTO.setBondExcessSpread(escExceedMinSize ? yieldSpread.getBondExcessSpread() : null);
            curveDataResDTO.setCbYield(cbycExceedMinSize ? yieldSpread.getCbYield() : null);
            curveDataResDTO.setAvgBondCreditSpread(cscExceedMinSize ? yieldSpread.getAvgBondCreditSpread() : null);
            curveDataResDTO.setAvgBondExcessSpread(escExceedMinSize ? yieldSpread.getAvgBondExcessSpread() : null);
            curveDataResDTO.setAvgCbYield(cbycExceedMinSize ? yieldSpread.getAvgCbYield() : null);
            curveDataList.add(curveDataResDTO);
        }
        return curveDataList;
    }

    protected Integer getMinSampleBondSize(boolean isComOrBond) {
        return isComOrBond ? MIN_SAMPLE_BOND_SIZE_FOR_COM_OR_BOND : MIN_SAMPLE_BOND_SIZE;
    }

    private void generalCheckBeforeSave(Long userid, String spreadCurveName) {
        if (userCurveDAO.isExist(userid, spreadCurveName)) {
            throw new TipsException("曲线已存在，请勿重复添加");
        }
        if (userCurveDAO.countUserCurve(userid) >= YieldSpreadConst.SAVE_CURVE_UPPER_LIMIT) {
            throw new TipsException(TipsConst.SAVE_CURVE_UPPER_LIMIT_REMIND);
        }
    }

    protected void saveYieldSpreadBonds(List<YieldSpreadBondDO> yieldSpreadBonds) {
        yieldSpreadBondDAO.saveYieldSpreadBonds(yieldSpreadBonds);
    }

    /**
     * 判断是否是今天
     *
     * @param spreadDate 利差日期
     * @return boolean
     */
    protected boolean isToday(Date spreadDate) {
        return spreadDate.toLocalDate().equals(LocalDate.now());
    }
}
