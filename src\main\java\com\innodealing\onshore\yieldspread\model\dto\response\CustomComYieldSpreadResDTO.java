package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayStringJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 自定义曲线主体利差
 *
 * <AUTHOR>
 */
public class CustomComYieldSpreadResDTO {

    @ApiModelProperty("发行人代码")
    private Long comUniCode;

    @ApiModelProperty("发行人名称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String comUniName;

    @ApiModelProperty("利差日期")
    private Date spreadDate;

    @ApiModelProperty("主体评级")
    private Integer comExtRatingMapping;

    @ApiModelProperty("主体评级")
    private String comExtRatingMappingStr;

    @ApiModelProperty("总资产(亿)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal totalAssets;

    @ApiModelProperty("主体信用利差(全部债券);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comCreditSpread;

    @ApiModelProperty("主体超额利差(全部债券);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comExcessSpread;

    @ApiModelProperty("主体估值收益率(全部债券);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comCbYield;

    @ApiModelProperty("曲线id")
    private Long curveId;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public String getComExtRatingMappingStr() {
        return comExtRatingMappingStr;
    }

    public void setComExtRatingMappingStr(String comExtRatingMappingStr) {
        this.comExtRatingMappingStr = comExtRatingMappingStr;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public Long getCurveId() {
        return curveId;
    }

    public void setCurveId(Long curveId) {
        this.curveId = curveId;
    }
}
