package com.innodealing.onshore.yieldspread.router.factory;


import com.google.common.collect.Maps;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.enums.YyRatingTagEnum;
import com.innodealing.onshore.yieldspread.router.shard.YyRatingRouter;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;

/**
 * YY评级 分片路由创建工厂
 *
 * <AUTHOR>
 */
@Component
public class YyRatingRouterFactory implements RatingRouterFactory<YyRatingRouter> {

    private static final Map<Integer, BiConsumer<YyRatingRouter, Integer>> PROPERTY_MAP = Maps.newConcurrentMap();

    static {
        PROPERTY_MAP.put(YyRatingTagEnum.NO_1.getValue(), YyRatingRouter::setLevel1);
        PROPERTY_MAP.put(YyRatingTagEnum.NO_2.getValue(), YyRatingRouter::setLevel2);
        PROPERTY_MAP.put(YyRatingTagEnum.NO_3.getValue(), YyRatingRouter::setLevel3);
        PROPERTY_MAP.put(YyRatingTagEnum.NO_4.getValue(), YyRatingRouter::setLevel4);
        PROPERTY_MAP.put(YyRatingTagEnum.NO_5.getValue(), YyRatingRouter::setLevel5);
        PROPERTY_MAP.put(YyRatingTagEnum.NO_6.getValue(), YyRatingRouter::setLevel6);
        PROPERTY_MAP.put(YyRatingTagEnum.NO_7.getValue(), YyRatingRouter::setLevel7);
        PROPERTY_MAP.put(YyRatingTagEnum.NO_8.getValue(), YyRatingRouter::setLevel8);
    }

    @Override
    public YyRatingRouter newRatingRouter(Integer... combinations) {
        YyRatingRouter router = new YyRatingRouter();
        Optional.ofNullable(combinations).ifPresent(cbs -> {
            for (Integer rating : cbs) {
                if (ITextValueEnum.containsEnum(YyRatingTagEnum.class, rating)) {
                    PROPERTY_MAP.get(rating).accept(router, rating);
                }
            }
        });
        return router;
    }

}
