package com.innodealing.onshore.yieldspread.builder;

import com.alibaba.fastjson.JSONObject;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveShardEnum;
import com.innodealing.onshore.yieldspread.model.dto.InduBondShardYieldSpreadParamDTO;

import java.util.Objects;
import java.util.Optional;

/**
 * 行业利差曲线分片查询参数构造器
 *
 * <AUTHOR>
 */
public class InduBondYieldSpreadShardParamBuilder implements ShardParamBuilder {

    protected final InduBondShardYieldSpreadParamDTO paramDTO;

    /**
     * 构造方法，初始化实例
     */
    public InduBondYieldSpreadShardParamBuilder() {
        paramDTO = new InduBondShardYieldSpreadParamDTO();
    }

    /**
     * 设置induCodes参数
     *
     * @param jsonObject json
     * @return 构造器
     */
    public InduBondYieldSpreadShardParamBuilder induCodes(JSONObject jsonObject) {
        String induLevel = paramDTO.getLevel();
        if (YieldSpreadCurveShardEnum.INDU_LEVEL_1.getText().equals(induLevel)) {
            Long[] indu1Codes = jsonObject.getObject("indu1Codes", Long[].class);
            Optional.ofNullable(indu1Codes).ifPresent(paramDTO::setIndu1Codes);
        }
        if (YieldSpreadCurveShardEnum.INDU_LEVEL_2.getText().equals(induLevel)) {
            Long[] indu2Codes = jsonObject.getObject("indu2Codes", Long[].class);
            Optional.ofNullable(indu2Codes).ifPresent(paramDTO::setIndu2Codes);
        }
        return this;
    }

    @Override
    public InduBondYieldSpreadShardParamBuilder searchQueryParam(Object dto) {
        if (Objects.isNull(dto)) {
            return this;
        }
        BeanCopyUtils.copyProperties(dto, paramDTO);
        return this;
    }

    @Override
    public InduBondYieldSpreadShardParamBuilder level(YieldSpreadCurveShardEnum shardEnum) {
        this.paramDTO.setLevel(shardEnum.getText());
        return this;
    }

    @Override
    public InduBondYieldSpreadShardParamBuilder extra(JSONObject jsonObject) {
        return induCodes(jsonObject);
    }

    @Override
    public InduBondShardYieldSpreadParamDTO build() {
        return paramDTO;
    }
}
