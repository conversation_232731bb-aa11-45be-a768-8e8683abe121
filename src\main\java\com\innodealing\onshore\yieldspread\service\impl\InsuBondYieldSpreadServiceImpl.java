package com.innodealing.onshore.yieldspread.service.impl;

import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.helper.CommonsHelper;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondfinance.response.ComFinanceSheetResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationShortInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondpriceapollo.CurveMaturityStructureDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondImpliedRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.enums.BusinessNatureEnum;
import com.innodealing.onshore.bondmetadata.enums.EmbeddedOption;
import com.innodealing.onshore.bondmetadata.enums.ImplicitRatingTagEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgInsuBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvInsuBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition.PgInsuBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.InsuBondYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InsuBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InsuComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.InsuSeniorityRankingEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.CommonUtils;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.InsuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveDataResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InsuSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgInsuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInsuBondYieldSpreadGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.EmptyRouter;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import com.innodealing.onshore.yieldspread.service.InsuBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.RedisService;
import com.innodealing.onshore.yieldspread.service.internal.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.CACHE_ONE_DAYS;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.MAX_INSU_BOND_SPREAD_DATE_KEY;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.*;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.splitDateRange;

/**
 * 保险债利差 service 实现类
 *
 * <AUTHOR>
 * @date 2024/4/10 18:47
 **/
@Service
public class InsuBondYieldSpreadServiceImpl extends AbstractBondCurveService implements InsuBondYieldSpreadService {

    private final ExecutorService executorService;

    private static final SortDTO DEFAULT_SORT = new SortDTO(CommonsHelper.getPropertyName(InsuBondYieldSpreadDO::getBondCreditSpread), SortDirection.DESC);


    protected InsuBondYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("InsuBondYieldSpreadServiceImpl-pool-").build());
    }

    public static final String YIELD_SPREAD_INSU_BOND_YIELD_SPREAD_FLOW_ID = "yieldSpread:insuBondYieldSpreadFlowId";

    @Value("${sharding.yield.spread}")
    private Date initStartDate;

    @Resource
    private RedisService redisService;

    @Resource
    private InsuBondYieldSpreadRedisDAO insuBondYieldSpreadRedisDAO;

    @Resource
    private InsuBondYieldSpreadDAO insuBondYieldSpreadDAO;

    @Resource
    private InsuComYieldSpreadDAO insuComYieldSpreadDAO;

    @Resource
    private ComService comService;

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private BondRatingService bondRatingService;

    @Resource
    private PgInsuBondYieldSpreadDAO pgInsuBondYieldSpreadDAO;

    @Resource
    private BondFinanceService bondFinanceService;

    @Autowired
    private MvInsuBondYieldSpreadCurveDAO mvInsuBondYieldSpreadCurveDAO;

    @Autowired
    private PgInsuBondYieldSpreadCurveDAO pgInsuBondYieldSpreadCurveDAO;

    @Autowired
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private UserService userService;


    /**
     * 检查保险分片表
     */
    //@Scheduled(initialDelay = 1000, fixedDelay = 3_600_000)
    public void checkShardingTables() {
        Date currentDate = new Date(System.currentTimeMillis());
        Date endDate = new Date(DateUtils.addDays(currentDate, 1).getTime());
        createShardingTables(initStartDate, endDate);
    }

    private void createShardingTables(Date startDate, Date endDate) {
        Collection<String> shardingTableNames = ShardingUtils.getYearShardingTableNames(INSU_TABLE_NAME, startDate, endDate);
        for (String shardingTableName : shardingTableNames) {
            insuBondYieldSpreadDAO.createShardingTable(shardingTableName);
        }
    }

    /**
     * 保险债利差计算
     *
     * @param onshoreBondInfoDTOs 债券信息
     * @param bondYieldCurveMap   (CurveUniCode,收益率曲线)
     * @param spreadDate          利差日期
     * @return 影响数
     */
    @Override
    public int calcInsuBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs, Map<Long, List<CurveMaturityStructureDTO>> bondYieldCurveMap, Date spreadDate) {
        if (CollectionUtils.isEmpty(onshoreBondInfoDTOs)) {
            return 0;
        }
        Set<Long> bondUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getBondUniCode).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> comUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getComUniCode).filter(Objects::nonNull).collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        // (comUniCode,主体精简信息)
        CompletableFuture<Map<Long, ComShortInfoDTO>> submitComShortInfoDTO = of.submit(() -> comService.getComShortInfoByUniCodeMap(comUniCodes));
        // (bondUniCode,中债估值信息)
        CompletableFuture<Map<Long, CbValuationShortInfoResponseDTO>> submitCbValuationShortInfo =
                of.submit(() -> bondPriceService.getCbValuationShortInfoMap(spreadDate, bondUniCodes));
        // (bondUniCode,隐含评级信息)
        CompletableFuture<Map<Long, BondImpliedRatingDTO>> submitBondImpliedRatingDTO =
                of.submit(() -> bondRatingService.getBondImpliedRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        // (bondUniCode,债券外部评级)
        CompletableFuture<Map<Long, BondExternalCreditRatingDTO>> submitBondExternalCreditRatingDTO =
                of.submit(() -> bondRatingService.getBondExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        // (comUniCode,主体外部评级信息)
        CompletableFuture<Map<Long, ComExternalCreditRatingDTO>> submitComExternalCreditRatingDTO =
                of.submit(() -> bondRatingService.getComExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        of.doWorks(submitComShortInfoDTO, submitCbValuationShortInfo, submitBondImpliedRatingDTO,
                submitBondExternalCreditRatingDTO, submitComExternalCreditRatingDTO);
        // 获取主体信息
        Map<Long, ComShortInfoDTO> comShortInfoDTOMap = submitComShortInfoDTO.join();
        // 获取中债估值信息
        Map<Long, CbValuationShortInfoResponseDTO> cbMap = submitCbValuationShortInfo.join();
        // 获取隐含评级信息
        Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap = submitBondImpliedRatingDTO.join();
        //债券外部评级
        Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap = submitBondExternalCreditRatingDTO.join();
        //主体外部评级
        Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap = submitComExternalCreditRatingDTO.join();
        return parseInsuBondYieldSpreadDO(onshoreBondInfoDTOs, comShortInfoDTOMap, cbMap, bondImpliedRatingMap, bondExternalCreditRatingMap,
                comExternalCreditRatingMap, bondYieldCurveMap, spreadDate);
    }


    /**
     * 解析数据并落库
     *
     * @param onshoreBondInfos            保险相关的债券信息
     * @param comShortInfoMap             (ComUniCode,主体信息)
     * @param cbMap                       (BondUniCode,中债估值信息)
     * @param bondImpliedRatingMap        (BondUniCode,隐含评级信息)
     * @param bondExternalCreditRatingMap (BondUniCode,债券外部评级)
     * @param comExternalCreditRatingMap  (ComUniCode,主体外部评级信息)
     * @param bondYieldCurveMap           (CurveUniCode,收益率曲线)
     * @param spreadDate                  利差日期
     * @return
     */
    private int parseInsuBondYieldSpreadDO(List<OnshoreBondInfoDTO> onshoreBondInfos, Map<Long, ComShortInfoDTO> comShortInfoMap, Map<Long, CbValuationShortInfoResponseDTO> cbMap,
                                           Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap, Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                           Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap,
                                           Map<Long, List<CurveMaturityStructureDTO>> bondYieldCurveMap, Date spreadDate) {
        List<InsuBondYieldSpreadDO> insuBondYieldSpreadDOs = new ArrayList<>();
        List<YieldSpreadBondDO> yieldSpreadBonds = Lists.newArrayList();
        for (OnshoreBondInfoDTO onshoreBondInfoDTO : onshoreBondInfos) {
            InsuBondYieldSpreadDO insuBondYieldSpreadDO = BeanCopyUtils.copyProperties(onshoreBondInfoDTO, InsuBondYieldSpreadDO.class);
            insuBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getPublicOffering());
            insuBondYieldSpreadDO.setSpreadDate(spreadDate);
            if (!DateExtensionUtils.isSameDay(spreadDate, YieldSpreadHelper.getYesterDay())) {
                insuBondYieldSpreadDO.setLatestCouponRate(null);
                insuBondYieldSpreadDO.setBondBalance(null);
            }
            if (Objects.equals(onshoreBondInfoDTO.getEmbeddedOption(), EmbeddedOption.PERPETUAL.getValue())) {
                insuBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getEmbeddedOption());
            }
            // 利差剩余期限标签
            insuBondYieldSpreadDO.setSpreadRemainingTenorTag(YieldSpreadHelper.getSpreadRemainingTenorTag(insuBondYieldSpreadDO.getRemainingTenorDay()));
            insuBondYieldSpreadDO = fillComInfoColumn(insuBondYieldSpreadDO, comShortInfoMap);
            insuBondYieldSpreadDO = fillRatingColumn(insuBondYieldSpreadDO, bondImpliedRatingMap, bondExternalCreditRatingMap, comExternalCreditRatingMap);
            insuBondYieldSpreadDO = fillCbColumn(insuBondYieldSpreadDO, cbMap);
            insuBondYieldSpreadDO = fillLerpYieldColumn(insuBondYieldSpreadDO, bondYieldCurveMap);
            insuBondYieldSpreadDO = fillSpreadColumn(insuBondYieldSpreadDO);
            //这里如果超额利差和信用利差都为空的情况下 过滤掉
            if (Objects.nonNull(insuBondYieldSpreadDO.getBondCreditSpread()) || Objects.nonNull(insuBondYieldSpreadDO.getBondExcessSpread())) {
                insuBondYieldSpreadDO.setId(redisService.generatePk(YIELD_SPREAD_INSU_BOND_YIELD_SPREAD_FLOW_ID, insuBondYieldSpreadDO.getSpreadDate()));
                insuBondYieldSpreadDO.setDeleted(0);
                insuBondYieldSpreadDOs.add(insuBondYieldSpreadDO);
                YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(insuBondYieldSpreadDO, YieldSpreadBondDO.class);
                yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.INSU.getValue());
                yieldSpreadBonds.add(yieldSpreadBondDO);
            }
        }
        insuBondYieldSpreadDAO.saveInsuBondYieldSpreadDOList(spreadDate, spreadDate, insuBondYieldSpreadDOs);
        super.saveYieldSpreadBonds(yieldSpreadBonds);
        return savePgInsuBondYieldSpreadDOLists(spreadDate, insuBondYieldSpreadDOs);
    }

    private int savePgInsuBondYieldSpreadDOLists(Date spreadDate, List<InsuBondYieldSpreadDO> insuBondYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(insuBondYieldSpreadDOs)) {
            return 0;
        }
        List<PgInsuBondYieldSpreadDO> pgInsuBondYieldSpreadDOs = insuBondYieldSpreadDOs.stream()
                .map(x -> BeanCopyUtils.copyProperties(x, PgInsuBondYieldSpreadDO.class))
                .collect(Collectors.toList());
        return pgInsuBondYieldSpreadDAO.savePgInsuBondYieldSpreadDOList(spreadDate, pgInsuBondYieldSpreadDOs);

    }

    /**
     * 填充发行人信息
     *
     * @param insuBondYieldSpreadDO 保险债利差
     * @param comShortInfoMap       主体信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuBondYieldSpreadDO
     * <AUTHOR>
     */
    private InsuBondYieldSpreadDO fillComInfoColumn(InsuBondYieldSpreadDO insuBondYieldSpreadDO, Map<Long, ComShortInfoDTO> comShortInfoMap) {
        InsuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(insuBondYieldSpreadDO, InsuBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comShortInfoMap)) {
            return result;
        }
        ComShortInfoDTO comShortInfoDTO = comShortInfoMap.get(result.getComUniCode());
        if (Objects.nonNull(comShortInfoDTO)) {
            //主体基础信息
            result.setBusinessNature(comShortInfoDTO.getBusinessNature());
            result.setBusinessFilterNature(BusinessNatureEnum.getBusinessFilterNatureEnum(comShortInfoDTO.getBusinessNature()).getValue());
            result.setInduLevel1Code(comShortInfoDTO.getInduLevel1Code());
            result.setInduLevel1Name(comShortInfoDTO.getInduLevel1Name());
            result.setInduLevel2Code(comShortInfoDTO.getInduLevel2Code());
            result.setInduLevel2Name(comShortInfoDTO.getInduLevel2Name());
        }
        return result;
    }

    /**
     * 填充中债估值相关信息
     *
     * @param insuBondYieldSpreadDO 产业债利差
     * @param cbMap                 中债估值
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO
     * <AUTHOR>
     */
    private InsuBondYieldSpreadDO fillCbColumn(InsuBondYieldSpreadDO insuBondYieldSpreadDO, Map<Long, CbValuationShortInfoResponseDTO> cbMap) {
        InsuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(insuBondYieldSpreadDO, InsuBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(cbMap)) {
            return result;
        }
        CbValuationShortInfoResponseDTO cb = cbMap.get(insuBondYieldSpreadDO.getBondUniCode());
        if (Objects.nonNull(cb)) {
            result.setCbYield(cb.getYield());
        }
        return result;
    }

    /**
     * 填充评级相关信息
     *
     * @param insuBondYieldSpreadDO       产业债利差
     * @param bondImpliedRatingMap        债券隐含评级
     * @param bondExternalCreditRatingMap 债券外部评级
     * @param comExternalCreditRatingMap  主体外部评级
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO
     * <AUTHOR>
     */
    private InsuBondYieldSpreadDO fillRatingColumn(InsuBondYieldSpreadDO insuBondYieldSpreadDO, Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap,
                                                   Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                                   Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap) {
        InsuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(insuBondYieldSpreadDO, InsuBondYieldSpreadDO.class);
        if (ObjectUtils.isNotEmpty(bondImpliedRatingMap)) {
            BondImpliedRatingDTO bondImpliedRatingDTO = bondImpliedRatingMap.get(insuBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondImpliedRatingDTO)) {
                result.setBondImpliedRatingMapping(bondImpliedRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(bondExternalCreditRatingMap)) {
            BondExternalCreditRatingDTO bondExternalCreditRatingDTO = bondExternalCreditRatingMap.get(insuBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondExternalCreditRatingDTO)) {
                result.setBondExtRatingMapping(bondExternalCreditRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(comExternalCreditRatingMap)) {
            ComExternalCreditRatingDTO comExternalCreditRatingDTO = comExternalCreditRatingMap.get(insuBondYieldSpreadDO.getComUniCode());
            if (Objects.nonNull(comExternalCreditRatingDTO)) {
                result.setComExtRatingMapping(comExternalCreditRatingDTO.getRatingMapping());
            }
        }
        return result;
    }

    /**
     * 填充插值收益率
     *
     * @param insuBondYieldSpreadDO 产业债利差
     * @param bondYieldCurveMap     收益率曲线信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuBondYieldSpreadDO
     * <AUTHOR>
     */
    private InsuBondYieldSpreadDO fillLerpYieldColumn(InsuBondYieldSpreadDO insuBondYieldSpreadDO, Map<Long, List<CurveMaturityStructureDTO>> bondYieldCurveMap) {
        InsuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(insuBondYieldSpreadDO, InsuBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(bondYieldCurveMap)) {
            return result;
        }
        //国开插值收益率;单位(%)
        List<CurveMaturityStructureDTO> curveMaturityStructureDTOS = bondYieldCurveMap.get(YieldSpreadConst.CDB_CURVE_UNI_CODE);
        if (CollectionUtils.isNotEmpty(curveMaturityStructureDTOS)) {
            result.setCdbLerpYield(YieldSpreadHelper.curveMaturityStructureDTOs(result.getRemainingTenorDay(), curveMaturityStructureDTOS));
        }
        //隐含评级对应曲线插值收益率;单位(%)
        if (Objects.nonNull(result.getBondImpliedRatingMapping())) {
            List<CurveMaturityStructureDTO> impliedRatingLerpYieldCurveDTOList
                    = bondYieldCurveMap.get(YieldSpreadHelper.getBondYieldCurveInsuMap().get(result.getBondImpliedRatingMapping()));
            if (CollectionUtils.isNotEmpty(impliedRatingLerpYieldCurveDTOList)) {
                result.setImpliedRatingLerpYield(YieldSpreadHelper.curveMaturityStructureDTOs(result.getRemainingTenorDay(), impliedRatingLerpYieldCurveDTOList));
            }
        }
        return result;
    }

    /**
     * 填充利差数据
     *
     * @param insuBondYieldSpreadDO 产业债利差
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuBondYieldSpreadDO
     * <AUTHOR>
     */
    private InsuBondYieldSpreadDO fillSpreadColumn(InsuBondYieldSpreadDO insuBondYieldSpreadDO) {
        InsuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(insuBondYieldSpreadDO, InsuBondYieldSpreadDO.class);
        if (Objects.nonNull(result.getCbYield())) {
            if (Objects.nonNull(result.getCdbLerpYield()) && result.getCdbLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondCreditSpread(result.getCbYield().subtract(result.getCdbLerpYield()).multiply(YieldSpreadHelper.BP_WEIGHT)
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.nonNull(result.getImpliedRatingLerpYield()) && result.getImpliedRatingLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondExcessSpread(result.getCbYield().subtract(result.getImpliedRatingLerpYield()).multiply(YieldSpreadHelper.BP_WEIGHT)
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                result.setExcessSpreadStatus(0);
            } else {
                result.setExcessSpreadStatus(1);
            }
        }
        return result;
    }

    /**
     * 保险主体利差计算
     *
     * @param comYieldSpreadDOs 保险主体利差数据
     * @param spreadDate        利差日期
     * @return 影响数
     */
    @Override
    public Integer calcInsuComYieldSpreadsBySpreadDate(List<InsuComYieldSpreadDO> comYieldSpreadDOs, Date spreadDate) {
        if (CollectionUtils.isEmpty(comYieldSpreadDOs)) {
            return 0;
        }
        Set<Long> comUniCodes = comYieldSpreadDOs.stream().map(InsuComYieldSpreadDO::getComUniCode).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        CompletableFuture<Map<Long, ComFinanceSheetResponseDTO>> submitComFinanceSheetResponseDTO = of.submit(() ->
                bondFinanceService.getComFinanceLatestYearReportMap(spreadDate, comUniCodes));
        // 计算中位数(全部)  key=comUnicode value=行业债利差
        CompletableFuture<Map<Long, PgInsuBondYieldSpreadGroupDO>> submitYieldSpread = of.submit(() ->
                pgInsuBondYieldSpreadDAO.getInsuBondYieldSpreadMap(comUniCodes, null, spreadDate));
        // 计算中位数(资本补充) key=comUnicode value=行业债利差
        CompletableFuture<Map<Long, PgInsuBondYieldSpreadGroupDO>> submitYieldSpreadSenior = of
                .submit(() -> pgInsuBondYieldSpreadDAO.getInsuBondYieldSpreadMap(comUniCodes,
                        InsuSeniorityRankingEnum.TIER2.getValue(), spreadDate));
        // 计算中位数(永续) key=comUnicode value=行业债利差
        CompletableFuture<Map<Long, PgInsuBondYieldSpreadGroupDO>> submitYieldSpreadPerpetual = of
                .submit(() -> pgInsuBondYieldSpreadDAO.getInsuBondYieldSpreadMap(comUniCodes,
                        InsuSeniorityRankingEnum.PERPETUAL.getValue(), spreadDate));

        of.doWorks(submitComFinanceSheetResponseDTO, submitYieldSpread, submitYieldSpreadSenior, submitYieldSpreadPerpetual);
        // 获取主体财报
        Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap = submitComFinanceSheetResponseDTO.join();
        // 获取估值中位数(全部、资本补充、永续)
        Map<Long, PgInsuBondYieldSpreadGroupDO> yieldSpreadMap = submitYieldSpread.join();
        Map<Long, PgInsuBondYieldSpreadGroupDO> yieldSpreadTier2Map = submitYieldSpreadSenior.join();
        Map<Long, PgInsuBondYieldSpreadGroupDO> yieldSpreadPerpetualMap = submitYieldSpreadPerpetual.join();

        List<InsuComYieldSpreadDO> comYieldSpreadDOSaves = new ArrayList<>();
        for (InsuComYieldSpreadDO comYieldSpreadDO : comYieldSpreadDOs) {
            comYieldSpreadDO = fillFinanceColumn(comYieldSpreadDO, comFinanceSheetResponseDTOMap);
            comYieldSpreadDO = fillComSpreadColumn(comYieldSpreadDO, yieldSpreadMap, yieldSpreadTier2Map, yieldSpreadPerpetualMap);
            comYieldSpreadDO.setDeleted(0);
            comYieldSpreadDOSaves.add(comYieldSpreadDO);
        }
        return insuComYieldSpreadDAO.saveInsuComYieldSpreadDOList(spreadDate, comYieldSpreadDOSaves);
    }

    private InsuComYieldSpreadDO fillFinanceColumn(InsuComYieldSpreadDO insuComYieldSpreadDO,
                                                   Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap) {
        InsuComYieldSpreadDO result = BeanCopyUtils.copyProperties(insuComYieldSpreadDO, InsuComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comFinanceSheetResponseDTOMap)) {
            return result;
        }
        ComFinanceSheetResponseDTO comFinanceSheetResponseDTO = comFinanceSheetResponseDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(comFinanceSheetResponseDTO)) {
            //赋值 总资产和总利润
            result.setTotalAssets(comFinanceSheetResponseDTO.getTotalAssets());
            result.setNetProfit(comFinanceSheetResponseDTO.getNetProfit());
        }
        return result;
    }

    private InsuComYieldSpreadDO fillComSpreadColumn(InsuComYieldSpreadDO insuComYieldSpreadDO,
                                                     Map<Long, PgInsuBondYieldSpreadGroupDO> yieldSpreadMap,
                                                     Map<Long, PgInsuBondYieldSpreadGroupDO> yieldSpreadTier2Map,
                                                     Map<Long, PgInsuBondYieldSpreadGroupDO> yieldSpreadPerpetualMap) {
        InsuComYieldSpreadDO result = BeanCopyUtils.copyProperties(insuComYieldSpreadDO, InsuComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(result)) {
            return result;
        }
        PgInsuBondYieldSpreadGroupDO yieldSpread = yieldSpreadMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpread)) {
            result.setComCbYield(yieldSpread.getCbYield());
            result.setComCreditSpread(yieldSpread.getBondCreditSpread());
            result.setComExcessSpread(yieldSpread.getBondExcessSpread());
        }
        PgInsuBondYieldSpreadGroupDO yieldSpreadTier2 = yieldSpreadTier2Map.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadTier2)) {
            result.setComTier2CbYield(yieldSpreadTier2.getCbYield());
            result.setComTier2CreditSpread(yieldSpreadTier2.getBondCreditSpread());
            result.setComTier2ExcessSpread(yieldSpreadTier2.getBondExcessSpread());
        }
        PgInsuBondYieldSpreadGroupDO yieldSpreadPerpetual = yieldSpreadPerpetualMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadPerpetual)) {
            result.setComPerpetualCbYield(yieldSpreadPerpetual.getCbYield());
            result.setComPerpetualCreditSpread(yieldSpreadPerpetual.getBondCreditSpread());
            result.setComPerpetualExcessSpread(yieldSpreadPerpetual.getBondExcessSpread());
        }
        return result;
    }

    /**
     * 刷新物化视图
     *
     * @param param 时间片
     */
    @Override
    public void refreshMvInsuBondYieldSpreadRatingCurve(RefreshYieldCurveParam param) {
        this.refreshMvInsuBondYieldSpreadBondAllCurve(param);
        this.refreshMvInsuBondYieldSpreadBondImpliedRatingMappingCurve(param);
    }

    /**
     * 历史物化视图
     *
     * @param isTableRefresh 是否刷新表
     */
    @Override
    public void refreshMvInsuBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh) {
        final List<AbstractRatingRouter.SpreadDateRange> dateRangeList = splitDateRange(LocalDate.parse("2019-06-01"), LocalDate.now().minusDays(1));
        for (AbstractRatingRouter.SpreadDateRange dateRange : dateRangeList) {
            final RefreshYieldCurveParam param = RefreshYieldCurveParam.builder().dateRange(dateRange).initRefresh(true, isTableRefresh).build();
            this.refreshMvInsuBondYieldSpreadBondAllCurve(param);
            this.refreshMvInsuBondYieldSpreadBondImpliedRatingMappingCurve(param);
        }
    }

    private void refreshMvInsuBondYieldSpreadBondImpliedRatingMappingCurve(RefreshYieldCurveParam param) {
        Set<String> insuImplicitRatingCombination = RatingCombinationHelper.getInsuImplicitRatingCombination();
        Set<ImplicitRatingRouter> implicitRatingRouters = implicitRatingRouterFactory.newRatingRouterList(insuImplicitRatingCombination);
        for (ImplicitRatingRouter implicitRatingRouter : implicitRatingRouters) {
            Optional.ofNullable(param)
                    .ifPresent(p -> implicitRatingRouter.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
            mvInsuBondYieldSpreadCurveDAO.createOrRefreshInsuCurveMv(implicitRatingRouter, param);
            pgInsuBondYieldSpreadCurveDAO.syncCurveShardInsuForMv(implicitRatingRouter, param);
            mvInsuBondYieldSpreadCurveDAO.droTempMv(implicitRatingRouter);

        }
    }

    private void refreshMvInsuBondYieldSpreadBondAllCurve(RefreshYieldCurveParam param) {
        EmptyRouter router = new EmptyRouter();
        try {
            Optional.ofNullable(param)
                    .ifPresent(p -> router.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
            mvInsuBondYieldSpreadCurveDAO.createOrRefreshInsuCurveMv(router, param);
            pgInsuBondYieldSpreadCurveDAO.syncCurveShardInsuForMv(router, param);
        } finally {
            mvInsuBondYieldSpreadCurveDAO.droTempMv(router);
        }
    }

    /**
     * 存入曲线池
     *
     * @param userid       用户id
     * @param curveGroupId 曲线组id
     * @param params       保存曲线参数
     * @return 操作结果
     */
    @Override
    public boolean saveCurve(Long userid, Long curveGroupId, InsuCurveGenerateConditionReqDTO params) {
        return this.generalSaveCurve(userid, curveGroupId, params);
    }

    /**
     * 更新曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @param request 更新参数
     * @return 执行结果
     */
    @Override
    public boolean updateCurve(Long userid, Long curveId, InsuCurveGenerateConditionReqDTO request) {
        return super.generalUpdateCurve(userid, curveId, request);
    }

    /**
     * 获取曲线类型
     *
     * @return 曲线类型
     */
    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.INSURANCE;
    }

    /**
     * 获取最新利差日期
     *
     * @return 最新利差日期
     */
    @Override
    protected Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_INSU_BOND_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = pgInsuBondYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_INSU_BOND_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }

    /**
     * 曲线数据
     *
     * @param curveId 曲线id
     * @return 曲线数据
     */
    @Override
    protected List<CurveDataResDTO> listCurveData(Long curveId) {
        InsuCurveGenerateConditionReqDTO conditionDTO = super.getCurveGenerateCondition(curveId, InsuCurveGenerateConditionReqDTO.class);
        return listCurveData(conditionDTO);
    }

    /**
     * 曲线数据
     *
     * @param curveGenerateParam 曲线生成参数
     * @return 曲线数据
     */
    @Override
    protected List<CurveDataResDTO> listCurveData(ICurveGenerateReq curveGenerateParam) {
        InsuCurveGenerateConditionReqDTO conditionDTO = (InsuCurveGenerateConditionReqDTO) curveGenerateParam;
        ComOrBondConditionReqDTO comOrBondCondition = conditionDTO.getComOrBondCondition();
        boolean isComOrBond = Objects.nonNull(comOrBondCondition) && Objects.nonNull(comOrBondCondition.getConditionType());
        InsuYieldSearchParam params = BeanCopyUtils.copyProperties(conditionDTO, InsuYieldSearchParam.class);
        List<BondYieldSpreadBO> yieldSpreads;
        boolean isRealTimeCalculate = isComOrBond || CollectionUtils.isNotEmpty(conditionDTO.getBusinessFilterNatures());
        if (isRealTimeCalculate) {
            if (isComOrBond) {
                params.setComOrBondUniCode(comOrBondCondition.getConditionType(), comOrBondCondition.getUniCode());
            }
            yieldSpreads = pgInsuBondYieldSpreadDAO.listBondYieldSpreads(params);
        } else {
            yieldSpreads = pgInsuBondYieldSpreadCurveDAO.listInsuYieldSpreads(params);
        }
        return super.convertToCurveDataResDTOsAndFilterData(yieldSpreads, super.getMinSampleBondSize(isComOrBond));
    }

    /**
     * 查询曲线数据集
     *
     * @param bondUniCode 债券唯一编码
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveDTO}>
     */
    @Override
    public List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate) {
        return insuBondYieldSpreadRedisDAO.listCurves(bondUniCode, startDate, endDate);
    }

    /**
     * 保险单券利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 单券利差
     */
    @Override
    public NormPagingResult<InsuSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        InsuCurveGenerateConditionReqDTO generateRequest = super.getCurveGenerateCondition(userid, request.getCurveId(), InsuCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return new NormPagingResult<>();
        }
        InsuYieldSearchParam param = super
                .buildBondYieldSearchParam(request, generateRequest, InsuYieldSearchParam.class, ObjectExtensionUtils.getOrDefault(request.getSort(), DEFAULT_SORT));
        NormPagingResult<InsuBondYieldSpreadDO> pagingResult = insuBondYieldSpreadDAO.listInsuSingleBondYieldSpreads(param);
        if (CollectionUtils.isEmpty(pagingResult.getList())) {
            return pagingResult.convert(bond -> BeanCopyUtils.copyProperties(bond, InsuSingleBondYieldSpreadResDTO.class));
        }
        Long[] bondUniCodes = pagingResult.getList().stream().map(InsuBondYieldSpreadDO::getBondUniCode).distinct().toArray(Long[]::new);
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        NormPagingResult<InsuSingleBondYieldSpreadResDTO> result = pagingResult.convert(bond -> {
            InsuSingleBondYieldSpreadResDTO response = BeanCopyUtils.copyProperties(bond, InsuSingleBondYieldSpreadResDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            response.setCdbLerpYield(CommonUtils.formatDecimal(bond.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            String ratingStr = RatingUtils.getRating(bond.getComExtRatingMapping()) + "/" + RatingUtils.getRating(bond.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            EnumUtils.getEnumByValue(bond.getBondImpliedRatingMapping(), ImplicitRatingTagEnum.class)
                    .ifPresent(v -> response.setBondImpliedRatingMappingStr(v.getText()));
            EnumUtils.getEnumByValue(bond.getBusinessNature(), BusinessNatureEnum.class).ifPresent(v -> response.setBusinessNatureStr(v.getText()));
            response.setInsuranceSeniorityRankingStr(InsuSeniorityRankingEnum.getTextByValue(bond.getInsuranceSeniorityRanking()).orElse(null));
            CalculationHelper.divideTenThousand(bond.getBondBalance()).ifPresent(response::setBondBalance);
            return response;
        });
        // 权限控制
        if (DateExtensionUtils.isSameDay(request.getSpreadDate(), Date.valueOf(LocalDate.now()))) {
            result.setList(this.permissionProcessing(userid, request.getSpreadDate(), result.getList()));
        }
        return result;
    }

    private List<InsuSingleBondYieldSpreadResDTO> permissionProcessing(Long userid, Date spreadDate, List<InsuSingleBondYieldSpreadResDTO> list) {

        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(InsuSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toList()));
            for (InsuSingleBondYieldSpreadResDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRatingMappingStr(CommonUtils.desensitized(yieldSpread.getBondImpliedRatingMappingStr(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (InsuSingleBondYieldSpreadResDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
        }
        return list;
    }

    /**
     * 查询债券数据集合
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link InsuSingleBondYieldSpreadResDTO}>
     */
    @Override
    public List<InsuSingleBondYieldSpreadResDTO> listBonds(Date spreadDate, Set<Long> bondUniCodes) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        spreadDate = super.isToday(spreadDate) ? this.getMaxSpreadDate() : spreadDate;
        List<InsuBondYieldSpreadDO> insuBondYieldSpreadList = insuBondYieldSpreadDAO.listInsuBondYieldSpreads(spreadDate, bondUniCodes);
        if (CollectionUtils.isEmpty(insuBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes.toArray(new Long[0])).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        List<InsuSingleBondYieldSpreadResDTO> responseList = Lists.newArrayListWithExpectedSize(insuBondYieldSpreadList.size());
        for (InsuBondYieldSpreadDO insuBondYieldSpread : insuBondYieldSpreadList) {
            InsuSingleBondYieldSpreadResDTO response = BeanCopyUtils.copyProperties(insuBondYieldSpread, InsuSingleBondYieldSpreadResDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            String ratingStr = RatingUtils.getRating(insuBondYieldSpread.getComExtRatingMapping()) + "/" + RatingUtils.getRating(insuBondYieldSpread.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            EnumUtils.getEnumByValue(insuBondYieldSpread.getBondImpliedRatingMapping(), ImplicitRatingTagEnum.class)
                    .ifPresent(v -> response.setBondImpliedRatingMappingStr(v.getText()));
            EnumUtils.getEnumByValue(insuBondYieldSpread.getBusinessNature(), BusinessNatureEnum.class).ifPresent(v -> response.setBusinessNatureStr(v.getText()));
            response.setInsuranceSeniorityRankingStr(InsuSeniorityRankingEnum.getTextByValue(insuBondYieldSpread.getInsuranceSeniorityRanking()).orElse(null));
            CalculationHelper.divideTenThousand(insuBondYieldSpread.getBondBalance()).ifPresent(response::setBondBalance);
            responseList.add(response);
        }
        return responseList;
    }
}
