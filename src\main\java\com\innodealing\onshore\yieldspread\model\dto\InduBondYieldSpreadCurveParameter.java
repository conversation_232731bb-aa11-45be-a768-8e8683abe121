package com.innodealing.onshore.yieldspread.model.dto;

import java.sql.Date;
import java.util.Objects;

/**
 * 产业利差曲线表创建参数
 * <AUTHOR>
 */
public class InduBondYieldSpreadCurveParameter {

    private String tableName;
    private Date startDate;
    private Date endDate;
    private String induLevel;

    public String getInduLevel() {
        return induLevel;
    }

    public void setInduLevel(String induLevel) {
        this.induLevel = induLevel;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public Date getEndDate() {
        return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public void setEndDate(Date endDate) {
        this.endDate = Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }
}
