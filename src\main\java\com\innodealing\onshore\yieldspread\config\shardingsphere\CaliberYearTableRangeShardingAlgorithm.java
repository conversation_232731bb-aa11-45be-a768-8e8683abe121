package com.innodealing.onshore.yieldspread.config.shardingsphere;

import com.google.common.collect.Range;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.helper.ShardingHindStrParamUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingValue;

import java.security.InvalidParameterException;
import java.sql.Date;
import java.util.Collection;

/**
 * 自定义Range分片算法,支持线程本地内存传递分片参数
 *
 * <AUTHOR>
 * @date 2024/6/12 9:53
 **/
public class CaliberYearTableRangeShardingAlgorithm implements RangeShardingAlgorithm<Long> {

    public Collection<String> doSharding(Collection<String> collection, RangeShardingValue<Long> rangeShardingValue) {
        String tableName = rangeShardingValue.getLogicTableName();
        Range<Long> idRange = rangeShardingValue.getValueRange();
        boolean hasLowerBound = idRange.hasLowerBound();
        boolean hasUpperBound = idRange.hasUpperBound();
        Long minPk = hasLowerBound ? idRange.lowerEndpoint() : null;
        Long maxPk = hasUpperBound ? idRange.upperEndpoint() : null;
        String shardingHindStrParam = ShardingHindStrParamUtil.getHindStrParam();
        if (StringUtils.isNotBlank(shardingHindStrParam)) {
            tableName = String.format("%s_%s", tableName, shardingHindStrParam);
        }
        return this.getTables(tableName, minPk, maxPk);
    }

    private Collection<String> getTables(String tableName, Long minPk, Long maxPk) {
        if (minPk == null) {
            throw new InvalidParameterException("minPk cannot be empty!");
        } else {
            Date startDate = ShardingUtils.getPkDate(minPk);
            Date endDate = maxPk == null ? new Date(System.currentTimeMillis()) : ShardingUtils.getPkDate(maxPk);
            return ShardingUtils.getYearShardingTableNames(tableName, startDate, endDate);
        }
    }
}
