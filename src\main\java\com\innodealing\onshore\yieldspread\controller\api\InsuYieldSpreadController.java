package com.innodealing.onshore.yieldspread.controller.api;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.request.InsuCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InsuComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InsuSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.service.InsuBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.InsuComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 保险利差
 *
 * <AUTHOR>
 */
@Api(tags = "(API)保险利差")
@RestController
@Validated
@RequestMapping("api/insu/yield-spread")
public class InsuYieldSpreadController {

    @Resource
    private InsuBondYieldSpreadService insuBondYieldSpreadService;

    @Resource
    private InsuComYieldSpreadService insuComYieldSpreadService;

    @PostMapping("curve-save")
    @ApiOperation(value = "保存曲线")
    public RestResponse<Boolean> saveCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated InsuCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(insuBondYieldSpreadService.saveCurve(userid, curveGroupId, request));
    }

    @PostMapping("curve-update")
    @ApiOperation(value = "更新曲线")
    public RestResponse<Boolean> updateCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Validated InsuCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(insuBondYieldSpreadService.updateCurve(userid, curveId, request));
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public RestResponse<NormPagingResult<InsuSingleBondYieldSpreadResDTO>> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(insuBondYieldSpreadService.pagingSingleBondYieldSpreads(userid, request));
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差  分页")
    public RestResponse<List<InsuComYieldSpreadResDTO>> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(insuComYieldSpreadService.listComYieldSpreads(userid, request));
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public RestResponse<Long> countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(insuComYieldSpreadService.countComYieldSpread(userid, request));
    }

}
