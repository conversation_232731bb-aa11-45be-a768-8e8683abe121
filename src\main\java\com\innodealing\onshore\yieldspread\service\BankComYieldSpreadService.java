package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.TrendReplayComYieldSpreadRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BankComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankComYieldSpreadDO;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 银行主体利差 Service
 *
 * <AUTHOR>
 **/
public interface BankComYieldSpreadService {

    /**
     * 银行主体利差计算
     *
     * @param comYieldSpreadDOs 证券主体利差数据
     * @param spreadDate        利差日期
     * @return 影响数
     */
    Integer calcBankComYieldSpreadsBySpreadDate(List<BankComYieldSpreadDO> comYieldSpreadDOs, Date spreadDate);

    /**
     * 获取主体利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体利差
     */
    List<BankComYieldSpreadResDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 获取主体利差条数
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体利差条数
     */
    Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 获取银行主体利差数据集
     *
     * @param spreadDate 利差日期
     * @param bankComs   主体唯一编码集合
     * @return {@link List}<{@link BankComYieldSpreadResDTO}>
     */
    List<BankComYieldSpreadResDTO> listComs(Date spreadDate, Set<Long> bankComs);

    /**
     * 查询利差曲线数据集
     *
     * @param comUniCode           主体唯一编码
     * @param bankSeniorityRanking 银行求偿顺序
     * @param startDate            开始日期
     * @param endDate              结束日期
     * @return {@link List}<{@link ComYieldSpreadCurveDTO}>
     * @see com.innodealing.onshore.bondmetadata.enums.BankSeniorityRankingEnum
     */
    List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer bankSeniorityRanking, Date startDate, Date endDate);

    /**
     * 主体利差走势复盘 列表
     * @param spreadStartDate 开始时间
     * @param spreadEndDate 结束时间
     * @param replayComYieldSpreadRequestDTOs 主体列表请求体
     * @return 利差曲线列表
     */
    List<ComCreditSpreadDTO> listTrendReplayComYieldSpreads(Date spreadStartDate, Date spreadEndDate, List<TrendReplayComYieldSpreadRequestDTO> replayComYieldSpreadRequestDTOs);
}
