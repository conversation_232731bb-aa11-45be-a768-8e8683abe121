package com.innodealing.onshore.yieldspread.helper;

import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * sharding hint 强制路由分片 参数 线程本地存储
 *
 * <AUTHOR>
 */
public final class ShardingHindStrParamUtil {

    private ShardingHindStrParamUtil() {
    }

    private static final TransmittableThreadLocal<String> HIND_STR_PARAM_LOCAL = new TransmittableThreadLocal<>();

    /**
     * 设置 分片参数
     */
    public static void setHindStrParamLocal(String hindStrParam) {
        HIND_STR_PARAM_LOCAL.set(hindStrParam);
    }

    /**
     * 移除 分片参数
     */
    public static void removeHindStrParam() {
        HIND_STR_PARAM_LOCAL.remove();
    }

    /**
     * 获取 分片参数
     */
    public static String getHindStrParam() {
        return HIND_STR_PARAM_LOCAL.get();
    }
}
