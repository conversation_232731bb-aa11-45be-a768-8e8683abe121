package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * PgBaseMapper
 * <AUTHOR>
 * @param <T> 参数
 */
@SuppressWarnings({"squid:S1860"})
public interface PgBaseMapper<T> {

    /**
     * 删除视图
     *
     * @param tableName 视图名称
     * @return boolean
     */
    @Update("DROP MATERIALIZED VIEW IF EXISTS ${tableName} cascade;")
    Boolean dropMv(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName 表名称
     * @return boolean
     */
    @Update("DROP TABLE IF EXISTS ${tableName} cascade;")
    Boolean dropTable(@Param("tableName") String tableName);

    /**
     * 判断表是否存在
     *
     * @param tableName 表名称
     * @return boolean
     */
    @Select("select true from pg_class where relname = #{tableName} limit 1 ")
    Boolean tableIsExists(@Param("tableName") String tableName);

    /**
     * 刷新视图
     *
     * @param tableName 视图名称
     */
    @Update("REFRESH MATERIALIZED VIEW ${tableName}")
    void refreshMvInduBondYieldSpreadCurve(@Param("tableName") String tableName);

    /**
     * 判断表是否存在
     *
     * @param tableName 表名称
     * @return boolean
     */
    default boolean isExists(String tableName) {
        return Boolean.TRUE.equals(this.tableIsExists(tableName));
    }


    /**
     * 创建实体表(评级分片)
     * @param parameter 参数
     */
    void createTableRatingRouter(@Param("parameter") T parameter);

    /**
     * 同步数据
     * @param tableName 表名称
     * @param mvTableName 视图形成
     */
    void syncCurveIncrFromMV(@Param("tableName") String tableName, @Param("mvTableName") String mvTableName);

    /**
     * 是否需要刷新表结构 并发更新竞争
     * @param tableName 表名
     * @param parameter 参数
     */
    default void refreshTable(String tableName, T parameter) {
        //已经刷新完成
        if (RatingCombinationHelper.isSuccess(tableName)) {
            return;
        }
        synchronized (tableName.intern()) {
            //已经刷新完成
            if (RatingCombinationHelper.isSuccess(tableName)) {
                return;
            }
            this.dropTable(tableName);
            this.createTableRatingRouter(parameter);
            RatingCombinationHelper.success(tableName);
        }
    }

}
