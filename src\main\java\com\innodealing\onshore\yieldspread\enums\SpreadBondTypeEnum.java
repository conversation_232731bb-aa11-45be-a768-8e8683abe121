package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 利差债券类型
 *
 * <AUTHOR>
 **/
public enum SpreadBondTypeEnum implements ITextValueEnum {
    /**
     * 私募
     */
    SPREAD_NOT_PUBLIC_OFFERING(0, "私募"),
    /**
     * 公募
     */
    SPREAD_IS_PUBLIC_OFFERING(1, "公募"),
    /**
     * 利差永续
     */
    SPREAD_PERPETUAL(2, "永续债");

    private final int value;
    private final String text;

    SpreadBondTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }


    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
