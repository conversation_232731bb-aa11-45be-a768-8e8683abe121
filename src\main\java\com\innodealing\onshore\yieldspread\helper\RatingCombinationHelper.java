package com.innodealing.onshore.yieldspread.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.google.common.collect.Maps;
import com.google.inject.internal.util.Sets;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.ImplicitRatingTagEnum;
import com.innodealing.onshore.bondmetadata.enums.YyRatingTagEnum;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.EmptyRouter;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.YyRatingRouter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.innodealing.international.common.template.utils.DateExtensionUtils.FORMAT_DATE_NUM;

/**
 * Rating 组合操作帮助类
 *
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S109", "squid:S3776", "squid:S00103"})
public final class RatingCombinationHelper {

    private RatingCombinationHelper() {
    }

    /**
     * 产业债利差 隐含评级分类
     * AAA AA A {@link ImplicitRatingTagEnum}
     */
    private static final List<Integer> INDU_TRIPLE_AAA_LIST = Arrays.asList(ImplicitRatingTagEnum.AAA_PLUS.getValue(), ImplicitRatingTagEnum.AAA.getValue(), ImplicitRatingTagEnum.AAA_SUB.getValue());
    private static final List<Integer> INDU_DOUBLE_AA_LIST = Arrays.asList(ImplicitRatingTagEnum.AA_PLUS.getValue(), ImplicitRatingTagEnum.AA.getValue(), ImplicitRatingTagEnum.AA_SUB.getValue());
    private static final List<Integer> INDU_ONE_A_LIST = Arrays.asList(ImplicitRatingTagEnum.A_PLUS.getValue(), ImplicitRatingTagEnum.A.getValue(), ImplicitRatingTagEnum.A_SUB.getValue());
    /**
     * 城投债利差
     * AAA AA {@link ImplicitRatingTagEnum}
     */
    private static final List<Integer> UDIC_TRIPLE_AAA_LIST = Arrays.asList(ImplicitRatingTagEnum.AAA_PLUS.getValue(), ImplicitRatingTagEnum.AAA.getValue(), ImplicitRatingTagEnum.AAA_SUB.getValue());
    private static final List<Integer> UDIC_DOUBLE_AA_LIST = Arrays.asList(ImplicitRatingTagEnum.AA_PLUS.getValue(), ImplicitRatingTagEnum.AA.getValue(), ImplicitRatingTagEnum.AA_2.getValue(), ImplicitRatingTagEnum.AA_SUB.getValue());
    private static final List<Integer> UDIC_ONE_A_LIST = Arrays.asList(ImplicitRatingTagEnum.A_PLUS.getValue(), ImplicitRatingTagEnum.A.getValue(), ImplicitRatingTagEnum.A_SUB.getValue());
    /**
     * 银行债利差 隐含评级分类
     * AAA AA {@link ImplicitRatingTagEnum}
     */
    private static final List<Integer> BANK_TRIPLE_AAA_LIST = Arrays.asList(ImplicitRatingTagEnum.AAA_PLUS.getValue(), ImplicitRatingTagEnum.AAA.getValue(), ImplicitRatingTagEnum.AAA_SUB.getValue());
    private static final List<Integer> BANK_DOUBLE_AA_LIST = Arrays.asList(ImplicitRatingTagEnum.AA_PLUS.getValue(), ImplicitRatingTagEnum.AA.getValue(), ImplicitRatingTagEnum.AA_SUB.getValue());
    /**
     * 保险债利差 隐含评级分类
     */
    private static final List<Integer> INSU_TRIPLE_AAA_LIST = Arrays.asList(ImplicitRatingTagEnum.AAA_PLUS.getValue(), ImplicitRatingTagEnum.AAA.getValue(), ImplicitRatingTagEnum.AAA_SUB.getValue());
    private static final List<Integer> INSU_DOUBLE_AA_LIST = Arrays.asList(ImplicitRatingTagEnum.AA_PLUS.getValue(), ImplicitRatingTagEnum.AA.getValue(), ImplicitRatingTagEnum.AA_SUB.getValue());
    /**
     * 证券债利差 隐含评级分类
     * AAA AA {@link ImplicitRatingTagEnum}
     */
    private static final List<Integer> SECU_TRIPLE_AAA_LIST = Arrays.asList(ImplicitRatingTagEnum.AAA_PLUS.getValue(), ImplicitRatingTagEnum.AAA.getValue(), ImplicitRatingTagEnum.AAA_SUB.getValue());
    private static final List<Integer> SECU_DOUBLE_AA_LIST = Arrays.asList(ImplicitRatingTagEnum.AA_PLUS.getValue(), ImplicitRatingTagEnum.AA.getValue(), ImplicitRatingTagEnum.AA_SUB.getValue());

    /**
     * 产业债 YY评级 {@link YyRatingTagEnum}
     * 投资:1 2 3 4 5  投机级:6 7 8
     */
    private static final List<Integer> INDU_INVESTMENT_RATING_LIST = Arrays.asList(YyRatingTagEnum.NO_1.getValue(), YyRatingTagEnum.NO_2.getValue(), YyRatingTagEnum.NO_3.getValue(), YyRatingTagEnum.NO_4.getValue(), YyRatingTagEnum.NO_5.getValue());
    private static final List<Integer> INDU_SPECULATION_RATING_LIST = Arrays.asList(YyRatingTagEnum.NO_6.getValue(), YyRatingTagEnum.NO_7.getValue(), YyRatingTagEnum.NO_8.getValue());
    /**
     * 城投债 YY评级 {@link YyRatingTagEnum}
     * 投资:1 2 3 4 5  投机级:6 7 8
     */
    private static final List<Integer> UDIC_INVESTMENT_RATING_LIST = Arrays.asList(YyRatingTagEnum.NO_1.getValue(), YyRatingTagEnum.NO_2.getValue(), YyRatingTagEnum.NO_3.getValue(), YyRatingTagEnum.NO_4.getValue(), YyRatingTagEnum.NO_5.getValue(), YyRatingTagEnum.NO_6.getValue());
    private static final List<Integer> UDIC_SPECULATION_RATING_LIST = Arrays.asList(YyRatingTagEnum.NO_6.getValue(), YyRatingTagEnum.NO_7.getValue(), YyRatingTagEnum.NO_8.getValue());
    /**
     * 产业债隐含评级组合
     */
    private static final Set<String> INDU_IMPLICIT_RATING_COMBINATION;
    /**
     * 城投隐含评级组合
     */
    private static final Set<String> UDIC_IMPLICIT_RATING_COMBINATION;
    /**
     * 银行隐含评级组合
     */
    private static final Set<String> BANK_IMPLICIT_RATING_COMBINATION;

    /**
     * 银行隐含评级组合
     */
    private static final Set<String> INSU_IMPLICIT_RATING_COMBINATION;
    /**
     * 证券隐含评级组合
     */
    private static final Set<String> SECU_IMPLICIT_RATING_COMBINATION;
    /**
     * 产业YY评级
     */
    private static final Set<String> INDU_INVESTMENT_RATING_COMBINATION;

    private static final Map<Class<? extends AbstractRatingRouter>, String> TABLE_NAME_FORMAT_MAP = Maps.newHashMap();
    /**
     * 城投YY评级
     */
    private static final Set<String> UDIC_INVESTMENT_RATING_COMBINATION;
    public static final String YY_RATING_TABLE_NAME_FORMAT = "%s_yyr_%s";
    public static final String IMPLICIT_RATING_TABLE_NAME_FORMAT = "%s_imr_%s";

    public static final String EMPTY_TABLE_NAME_FORMAT = "%s_%s";

    private static final TransmittableThreadLocal<Map<String, Integer>> REFRESH_TABLE_SUCCESS = new TransmittableThreadLocal<>();

    static {
        /**
         * 隐含评级组合初始化
         */
        INDU_IMPLICIT_RATING_COMBINATION =
                Stream.of(combination(INDU_TRIPLE_AAA_LIST), combination(INDU_DOUBLE_AA_LIST), combination(INDU_ONE_A_LIST))
                        .flatMap(Collection::stream).collect(Collectors.toSet());
        UDIC_IMPLICIT_RATING_COMBINATION =
                Stream.of(combination(UDIC_TRIPLE_AAA_LIST), combination(UDIC_DOUBLE_AA_LIST), combination(UDIC_ONE_A_LIST))
                        .flatMap(Collection::stream).collect(Collectors.toSet());
        BANK_IMPLICIT_RATING_COMBINATION =
                Stream.of(combination(BANK_TRIPLE_AAA_LIST), combination(BANK_DOUBLE_AA_LIST))
                        .flatMap(Collection::stream).collect(Collectors.toSet());
        INSU_IMPLICIT_RATING_COMBINATION =
                Stream.of(combination(INSU_TRIPLE_AAA_LIST), combination(INSU_DOUBLE_AA_LIST))
                        .flatMap(Collection::stream).collect(Collectors.toSet());
        SECU_IMPLICIT_RATING_COMBINATION =
                Stream.of(combination(SECU_TRIPLE_AAA_LIST), combination(SECU_DOUBLE_AA_LIST))
                        .flatMap(Collection::stream).collect(Collectors.toSet());
        /**
         * YY评级组合初始化
         */
        INDU_INVESTMENT_RATING_COMBINATION =
                Stream.of(combination(INDU_INVESTMENT_RATING_LIST), combination(INDU_SPECULATION_RATING_LIST))
                        .flatMap(Collection::stream).collect(Collectors.toSet());
        UDIC_INVESTMENT_RATING_COMBINATION =
                Stream.of(combination(UDIC_INVESTMENT_RATING_LIST), combination(UDIC_SPECULATION_RATING_LIST))
                        .flatMap(Collection::stream).collect(Collectors.toSet());
        /**
         * 表名格式化规则
         */
        TABLE_NAME_FORMAT_MAP.put(YyRatingRouter.class, YY_RATING_TABLE_NAME_FORMAT);
        TABLE_NAME_FORMAT_MAP.put(ImplicitRatingRouter.class, IMPLICIT_RATING_TABLE_NAME_FORMAT);
        TABLE_NAME_FORMAT_MAP.put(EmptyRouter.class, EMPTY_TABLE_NAME_FORMAT);
    }

    /**
     * table格式化
     *
     * @param clazz 表实体
     * @return tableFormat
     */
    public static String getTableFormat(Class<? extends AbstractRatingRouter> clazz) {
        return TABLE_NAME_FORMAT_MAP.get(clazz);
    }

    /**
     * 初始化
     */
    public static void init() {
        REFRESH_TABLE_SUCCESS.set(Maps.newConcurrentMap());
    }

    /**
     * 清空
     */
    public static void clear() {
        REFRESH_TABLE_SUCCESS.remove();
    }

    /**
     * 获取sharding表后缀
     *
     * @param router 路由
     * @return tableSuffix
     */
    public static String getTableNameSuffix(AbstractRatingRouter router) {
        // 父分片
        String superSuffix = Stream.of(router.getLevel()).filter(StringUtils::isNotBlank).collect(Collectors.joining("_"));
        // 评级分片
        JSONObject json = (JSONObject) JSON.toJSON(router);
        String suffix = json.values().stream()
                .filter(obj -> Objects.nonNull(obj) && obj instanceof Number)
                .map(Object::toString).collect(Collectors.joining("_"));
        return Stream.of(superSuffix, suffix).filter(StringUtils::isNotBlank).collect(Collectors.joining("_"));
    }

    /**
     * 获取sharding视图后缀
     *
     * @param router 路由
     * @return mvSuffix
     */
    public static String getMvNameSuffix(AbstractRatingRouter router) {
        String tableNameSuffix = getTableNameSuffix(router);
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (Objects.isNull(spreadDateRange)) {
            return MessageFormat.format(StringUtils.isBlank(tableNameSuffix) ? "{0}yday" : "{0}_yday", tableNameSuffix);
        }
        String startDate = DateExtensionUtils.format(spreadDateRange.getStartDate(), FORMAT_DATE_NUM);
        String endDate = DateExtensionUtils.format(spreadDateRange.getEndDate(), FORMAT_DATE_NUM);
        return String.format("%s_%s%s", tableNameSuffix, startDate, endDate);
    }

    public static Set<String> getInduImplicitRatingCombination() {
        return INDU_IMPLICIT_RATING_COMBINATION;
    }

    public static Set<String> getUdicImplicitRatingCombination() {
        return UDIC_IMPLICIT_RATING_COMBINATION;
    }

    public static Set<String> getBankImplicitRatingCombination() {
        return BANK_IMPLICIT_RATING_COMBINATION;
    }

    public static Set<String> getInsuImplicitRatingCombination() {
        return INSU_IMPLICIT_RATING_COMBINATION;
    }

    public static Set<String> getSecuImplicitRatingCombination() {
        return SECU_IMPLICIT_RATING_COMBINATION;
    }

    public static Set<String> getInduInvestmentRatingCombination() {
        return INDU_INVESTMENT_RATING_COMBINATION;
    }

    public static Set<String> getUdicInvestmentRatingCombination() {
        return UDIC_INVESTMENT_RATING_COMBINATION;
    }


    private static Set<String> combination(List<Integer> elements) {
        if (CollectionUtils.isEmpty(elements)) {
            return Collections.emptySet();
        }
        Set<String> combination = Sets.newHashSet();
        for (int i = 1; i < elements.size(); i++) {
            combination.addAll(combination(elements, i));
        }
        combination.add(elements.stream().map(String::valueOf).collect(Collectors.joining(",")));
        return combination;
    }

    private static Set<String> combination(List<Integer> elements, int k) {
        Set<String> result = Sets.newHashSet();
        // get the length of the array
        // e.g. for {'A','B','C','D'} => N = 4
        int length = elements.size();
        if (k > length) {
            return result;
        }
        // init combination index array
        int[] pointers = new int[k];
        int r = 0;
        int i = 0;
        while (r >= 0) {
            // forward step if i < (N + (r-K))
            if (i <= (length + (r - k))) {
                pointers[r] = i;
                if (r == k - 1) {
                    result.add(extracted(elements.toArray(new Object[0]), pointers));
                    i++;
                } else {
                    // if combination is not full yet, select next element
                    i = pointers[r] + 1;
                    r++;
                }
            } else {
                r--;
                if (r >= 0) {
                    i = pointers[r] + 1;
                }
            }
        }
        return result;
    }

    private static String extracted(Object[] e, int[] combination) {
        StringBuilder comb = new StringBuilder();
        for (int i1 = 0; i1 < combination.length; i1++) {
            comb.append(e[combination[i1]]);
            if (i1 < combination.length - 1) {
                comb.append(",");
            }
        }
        return comb.toString();
    }

    /**
     * 判断当前表是否被刷新过
     *
     * @param tableName 表名称
     * @return Boolean
     */
    public static Boolean isSuccess(String tableName) {
        Map<String, Integer> tableSuccessMap = REFRESH_TABLE_SUCCESS.get();
        return tableSuccessMap != null && Objects.nonNull(tableSuccessMap.get(tableName));
    }


    /**
     * 刷新成功
     *
     * @param tableName 表名称
     * @return Boolean
     */
    public static void success(String tableName) {
        Map<String, Integer> tableSuccessMap = REFRESH_TABLE_SUCCESS.get();
        if (tableSuccessMap == null) {
            tableSuccessMap = Maps.newConcurrentMap();
            REFRESH_TABLE_SUCCESS.set(tableSuccessMap);
        }
        tableSuccessMap.put(tableName, 1);
    }
}
