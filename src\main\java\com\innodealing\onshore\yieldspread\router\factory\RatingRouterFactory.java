package com.innodealing.onshore.yieldspread.router.factory;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 路由创建工厂接口
 * @param <R> router实例
 * <AUTHOR>
 */
public interface RatingRouterFactory<R> {

    /**
     * 批量构建方法
     * @param combinationList 组合list
     * @return set
     */
    default Set<R> newRatingRouterList(Set<String> combinationList) {
        if (CollectionUtils.isEmpty(combinationList)) {
            return Collections.emptySet();
        }
        return combinationList.stream().filter(Objects::nonNull)
                .map(combination -> {
                    Integer[] combinations = Arrays.stream(combination.split(",")).map(Integer::parseInt).toArray(Integer[]::new);
                    return this.newRatingRouter(combinations);
                }).collect(Collectors.toSet());

    }

    /**
     * 创建router
     * @param combinations 组合
     * @return router
     */
    R newRatingRouter(Integer... combinations);

}
