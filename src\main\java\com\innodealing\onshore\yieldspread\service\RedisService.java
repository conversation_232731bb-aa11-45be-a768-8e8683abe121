package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.model.dto.response.InduCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicCurveResponseDTO;

import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * redis 服务
 *
 * <AUTHOR>
 */
public interface RedisService {

    /**
     * 获取pk
     *
     * @param redisKey   redisKey
     * @param spreadDate 发布时间
     * @return pk
     */
    long generatePk(String redisKey, Date spreadDate);

    /**
     * 从缓存中取行业利差曲线数据
     *
     * @param cacheKey 缓存键
     * @return {@link List}<{@link InduCurveResponseDTO}> 行业利差曲线数据
     */
    List<InduCurveResponseDTO> listInduCurvesFromCache(String cacheKey);

    /**
     * 从缓存中取城投利差曲线数据
     *
     * @param cacheKey 缓存键
     * @return {@link List}<{@link UdicCurveResponseDTO}> 城投利差曲线数据
     */
    List<UdicCurveResponseDTO> listUdicCurvesFromCache(String cacheKey);

    /**
     * 设置值到缓存中
     *
     * @param key     键
     * @param value   值
     * @param timeout 超时时间
     * @param unit    超时单位
     */
    void set(String key, String value, long timeout, TimeUnit unit);

    /**
     * 批量设置值到缓存中
     *
     * @param dataMap key-value数值
     * @param timeout 超时时间
     * @param unit    超时单位
     */
    void multiSet(Map<String, String> dataMap, long timeout, TimeUnit unit);

    /**
     * 批量设置值到缓存中,不设过期时间
     *
     * @param dataMap key-value数值
     */
    void multiSet(Map<String, String> dataMap);


    /**
     * 缓存值
     *
     * @param key 键
     * @return 值
     */
    Optional<String> get(String key);

    /**
     * 根据key删除
     *
     * @param key key
     */
    void delete(String key);

    /**
     * 批量根据key删除
     *
     * @param keys key列表
     * @return 删除数
     */
    Long delete(List<String> keys);

    /**
     * 从缓存中获取所有利差日期
     *
     * @param cacheKey 缓存键
     * @return {@link List}<{@link Date}> 利差日期集合
     */
    List<Date> listAllSpreadDates(String cacheKey);
}
