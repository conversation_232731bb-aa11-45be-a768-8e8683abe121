package com.innodealing.onshore.yieldspread.model.dto;

import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceBO;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 利差追踪获取数据
 *
 * <AUTHOR>
 * @date 2024/5/7 10:35
 **/
public class PgBondYieldSpreadTraceDTO {
    /**
     * 利差追踪数据
     */
    private List<PgBondYieldSpreadTraceBO> pgBondYieldSpreadTraceBOList;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    public List<PgBondYieldSpreadTraceBO> getPgBondYieldSpreadTraceBOList() {
        return Objects.isNull(pgBondYieldSpreadTraceBOList) ? new ArrayList<>() : new ArrayList<>(pgBondYieldSpreadTraceBOList);
    }

    public void setPgBondYieldSpreadTraceBOList(List<PgBondYieldSpreadTraceBO> pgBondYieldSpreadTraceBOList) {
        this.pgBondYieldSpreadTraceBOList = Objects.isNull(pgBondYieldSpreadTraceBOList) ? new ArrayList<>() : new ArrayList<>(pgBondYieldSpreadTraceBOList);
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public Date getEndDate() {
        return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public void setEndDate(Date endDate) {
        this.endDate = Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }
}
