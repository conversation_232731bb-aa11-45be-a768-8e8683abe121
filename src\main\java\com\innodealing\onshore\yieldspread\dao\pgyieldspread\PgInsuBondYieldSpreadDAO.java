package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgInsuBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgInsuBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.InsuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgInsuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInsuBondYieldSpreadGroupDO;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.SPREAD_KEEP_SCALE;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE;
import static java.util.Objects.nonNull;

/**
 * 保险利差dao
 *
 * <AUTHOR>
 * @date 2024/4/11 15:05
 **/
@Repository
public class PgInsuBondYieldSpreadDAO {

    @Resource
    private PgInsuBondYieldSpreadMapper pgInsuBondYieldSpreadMapper;

    @Resource
    private PgInsuBondYieldSpreadGroupMapper pgInsuBondYieldSpreadGroupMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 批量更新
     *
     * @param pgInsuBondYieldSpreadDOs 保险债利差列表
     * @param spreadDate               利差日期
     * @return 受影响的行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int savePgInsuBondYieldSpreadDOList(Date spreadDate, List<PgInsuBondYieldSpreadDO> pgInsuBondYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(pgInsuBondYieldSpreadDOs)) {
            return 0;
        }
        Set<Long> bondUniCodes = pgInsuBondYieldSpreadDOs.stream().map(PgInsuBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<PgInsuBondYieldSpreadDO> query = DynamicQuery.createQuery(PgInsuBondYieldSpreadDO.class)
                .select(PgInsuBondYieldSpreadDO::getId, PgInsuBondYieldSpreadDO::getBondUniCode, PgInsuBondYieldSpreadDO::getSpreadDate)
                .and(PgInsuBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(PgInsuBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<PgInsuBondYieldSpreadDO> existDataList = pgInsuBondYieldSpreadMapper.selectByDynamicQuery(query);
        Map<String, PgInsuBondYieldSpreadDO> existPgInsuBondYieldSpreadDO = existDataList.stream().collect(Collectors.toMap(x ->
                        String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                Function.identity(), (x1, x2) -> x2));
        MapperBatchAction<PgInsuBondYieldSpreadMapper> batchAction = MapperBatchAction.
                create(PgInsuBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgInsuBondYieldSpreadDO pgInsuBondYieldSpreadDO : pgInsuBondYieldSpreadDOs) {
            PgInsuBondYieldSpreadDO existData = existPgInsuBondYieldSpreadDO.get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                    pgInsuBondYieldSpreadDO.getBondUniCode(),
                    pgInsuBondYieldSpreadDO.getSpreadDate().getTime()));
            batchAction.addAction(mapper -> {
                if (Objects.nonNull(existData)) {
                    UpdateQuery<PgInsuBondYieldSpreadDO> updateQuery = UpdateQuery.createQuery(PgInsuBondYieldSpreadDO.class)
                            .set(pgInsuBondYieldSpreadDO)
                            .and(PgInsuBondYieldSpreadDO::getId, isEqual(pgInsuBondYieldSpreadDO.getId()));
                    mapper.updateByUpdateQuery(updateQuery);
                } else {
                    mapper.insert(pgInsuBondYieldSpreadDO);
                }
            });
        }
        List<BatchResult> batchResults = batchAction.doBatchActionWithResults();
        return batchResults.stream().mapToInt(x -> {
            int effectRows = Arrays.stream(x.getUpdateCounts()).sum();
            if (effectRows > 0) {
                return effectRows;
            } else {
                return x.getParameterObjects().size();
            }
        }).sum();
    }

    /**
     * 计算中债估值中位数
     *
     * @param comUniCodes      发行人代码
     * @param seniorityRanking 保险公司债求偿顺序   1:资本补充、2:永续"
     * @param spreadDate       利差日期
     * @return key 发行人代码,行业债利差
     */
    public Map<Long, PgInsuBondYieldSpreadGroupDO> getInsuBondYieldSpreadMap(Set<Long> comUniCodes, Integer seniorityRanking, Date spreadDate) {
        GroupedQuery<PgInsuBondYieldSpreadDO, PgInsuBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgInsuBondYieldSpreadDO.class, PgInsuBondYieldSpreadGroupDO.class)
                        .select(PgInsuBondYieldSpreadGroupDO::getComUniCode, PgInsuBondYieldSpreadGroupDO::getCbYield,
                                PgInsuBondYieldSpreadGroupDO::getBondCreditSpread, PgInsuBondYieldSpreadGroupDO::getBondExcessSpread)
                        .and(PgInsuBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(PgInsuBondYieldSpreadDO::getComUniCode, in(comUniCodes))
                        .and(Objects.nonNull(seniorityRanking), PgInsuBondYieldSpreadDO::getInsuranceSeniorityRanking, isEqual(seniorityRanking))
                        .groupBy(PgInsuBondYieldSpreadDO::getComUniCode);
        List<PgInsuBondYieldSpreadGroupDO> pgInduBondYieldSpreadGroupDs = pgInsuBondYieldSpreadGroupMapper.
                selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(pgInduBondYieldSpreadGroupDs)) {
            return Collections.emptyMap();
        }
        return pgInduBondYieldSpreadGroupDs.stream()
                .filter(Objects::nonNull)
                .map(x -> {
                    PgInsuBondYieldSpreadGroupDO result = BeanCopyUtils.copyProperties(x, PgInsuBondYieldSpreadGroupDO.class);
                    if (Objects.nonNull(x.getCbYield())) {
                        result.setCbYield(x.getCbYield().setScale(YIELD_SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondCreditSpread())) {
                        result.setBondCreditSpread(x.getBondCreditSpread()
                                .setScale(SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondExcessSpread())) {
                        result.setBondExcessSpread(x.getBondExcessSpread()
                                .setScale(SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    return result;
                }).collect(Collectors.toMap(PgInsuBondYieldSpreadGroupDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 获取最新利差日期
     *
     * @return 最新利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgInsuBondYieldSpreadDO> query = DynamicQuery.createQuery(PgInsuBondYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt = pgInsuBondYieldSpreadMapper.selectMaxByDynamicQuery(PgInsuBondYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 查询保险利差
     *
     * @param params 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listBondYieldSpreads(InsuYieldSearchParam params) {
        GroupedQuery<PgInsuBondYieldSpreadDO, PgInsuBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgInsuBondYieldSpreadDO.class, PgInsuBondYieldSpreadGroupDO.class)
                        .select(PgInsuBondYieldSpreadGroupDO::getSpreadDate,
                                PgInsuBondYieldSpreadGroupDO::getBondCreditSpread,
                                PgInsuBondYieldSpreadGroupDO::getBondExcessSpread,
                                PgInsuBondYieldSpreadGroupDO::getCbYield,
                                PgInsuBondYieldSpreadGroupDO::getAvgBondCreditSpread,
                                PgInsuBondYieldSpreadGroupDO::getAvgBondExcessSpread,
                                PgInsuBondYieldSpreadGroupDO::getAvgCbYield,
                                PgInsuBondYieldSpreadGroupDO::getBondCreditSpreadCount,
                                PgInsuBondYieldSpreadGroupDO::getBondExcessSpreadCount,
                                PgInsuBondYieldSpreadGroupDO::getCbYieldCount)
                        .and(Objects.nonNull(params.getSpreadBondType()), PgInsuBondYieldSpreadDO::getInsuranceSeniorityRanking, isEqual(params.getSpreadBondType()))
                        .and(!CollectionUtils.isEmpty(params.getBusinessFilterNatures()), PgInsuBondYieldSpreadDO::getBusinessFilterNature, in(params.getBusinessFilterNatures()))
                        .and(Objects.nonNull(params.getRemainingTenor()), PgInsuBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(params.getRemainingTenor()))
                        .and(ArrayUtils.isNotEmpty(params.getBondImpliedRatingMappings()),
                                PgInsuBondYieldSpreadDO::getBondImpliedRatingMapping, in(params.getBondImpliedRatingMappings()))
                        .and(Objects.nonNull(params.getComUniCode()), PgInsuBondYieldSpreadDO::getComUniCode, isEqual(params.getComUniCode()))
                        .and(Objects.nonNull(params.getBondUniCode()), PgInsuBondYieldSpreadDO::getBondUniCode, isEqual(params.getBondUniCode()))
                        .groupBy(PgInsuBondYieldSpreadDO::getSpreadDate)
                        .orderBy(PgInsuBondYieldSpreadGroupDO::getSpreadDate, asc());
        List<PgInsuBondYieldSpreadGroupDO> bondYieldSpreads = pgInsuBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(bondYieldSpreads)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(bondYieldSpreads, BondYieldSpreadBO.class);
    }

    /**
     * 查询并计算利差曲线数据-单券利差方式
     *
     * @param bondUniCode     债券唯一编码
     * @param startSpreadDate 利差开始日期
     * @param endSpreadDate   利差结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 债券每天中位数数据集合
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurvesByBond(Long bondUniCode, Date startSpreadDate, Date endSpreadDate) {
        if (Objects.isNull(bondUniCode)) {
            return Collections.emptyList();
        }
        DynamicQuery<PgInsuBondYieldSpreadDO> query = DynamicQuery.createQuery(PgInsuBondYieldSpreadDO.class)
                .select(PgInsuBondYieldSpreadDO::getSpreadDate, PgInsuBondYieldSpreadDO::getBondCreditSpread,
                        PgInsuBondYieldSpreadDO::getBondExcessSpread, PgInsuBondYieldSpreadDO::getCbYield)
                .and(PgInsuBondYieldSpreadDO::getBondUniCode, isEqual(bondUniCode))
                .and(nonNull(startSpreadDate), PgInsuBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startSpreadDate))
                .and(nonNull(endSpreadDate), PgInsuBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(endSpreadDate))
                .orderBy(PgInsuBondYieldSpreadDO::getSpreadDate, SortDirections::asc);
        List<PgInsuBondYieldSpreadDO> pgUdicBondYieldSpreadList = pgInsuBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> copyList = Lists.newArrayListWithExpectedSize(pgUdicBondYieldSpreadList.size());
        for (PgInsuBondYieldSpreadDO pgInsuBondYieldSpreadDO : pgUdicBondYieldSpreadList) {
            BondYieldSpreadCurveBO clone = BeanCopyUtils.copyProperties(pgInsuBondYieldSpreadDO, BondYieldSpreadCurveBO.class);
            BigDecimalUtils.handlerPrecision(clone.getBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondCreditSpread);
            BigDecimalUtils.handlerPrecision(clone.getBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondExcessSpread);
            BigDecimalUtils.handlerPrecision(clone.getCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCbYield);
            BigDecimalUtils.handlerPrecision(clone.getCdbLerpYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCdbLerpYield);
            copyList.add(clone);
        }
        return copyList;
    }
}
