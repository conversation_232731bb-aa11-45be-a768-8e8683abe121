# syncHistBondYieldSpreadTrace 优化方案使用说明

## 概述

本优化方案通过设计模式和并行处理技术，解决了原有 `syncHistBondYieldSpreadTrace` 方法的三个主要性能问题：

1. **参数灵活性问题** - 使用建造者模式和策略模式，支持按债券类型和曲线代码过滤
2. **串行化性能问题** - 使用并行执行器，支持多线程并行处理
3. **频繁物化视图刷新问题** - 使用Redis控制物化视图刷新频率

## 核心设计模式

### 1. 建造者模式 (Builder Pattern)
- **类**: `SyncConfiguration`
- **用途**: 灵活构建同步配置参数
- **优势**: 支持可选参数，代码可读性强

### 2. 策略模式 (Strategy Pattern)
- **类**: `OptimizedBondYieldSpreadTraceService`
- **用途**: 根据不同配置选择不同的同步策略
- **优势**: 易于扩展新的同步策略

### 3. 装饰器模式 (Decorator Pattern)
- **类**: `RefreshControlledDAO`
- **用途**: 为现有DAO添加物化视图刷新控制功能
- **优势**: 不修改原有代码，功能可插拔

## 使用方式

### 1. 基本使用

```java
// 注入优化服务
@Resource
private OptimizedBondYieldSpreadTraceService optimizedService;

// 使用默认配置同步
SyncConfiguration config = SyncConfiguration.defaultConfig(startDate, endDate);
int effectRows = optimizedService.syncBondYieldSpreadTraceWithConfig(config);
```

### 2. 按债券类型同步

```java
// 同步特定债券类型
List<YieldPanoramaBondTypeEnum> bondTypes = Arrays.asList(
    YieldPanoramaBondTypeEnum.INDUSTRIAL_BOND,
    YieldPanoramaBondTypeEnum.URBAN_BOND
);
int effectRows = optimizedService.syncBondYieldSpreadTraceByBondTypes(startDate, endDate, bondTypes);

// 或者使用配置对象
SyncConfiguration config = SyncConfiguration.builder()
    .startDate(startDate)
    .endDate(endDate)
    .bondTypes(bondTypes)
    .parallel(true)
    .parallelism(8)
    .build();
int effectRows = optimizedService.syncBondYieldSpreadTraceWithConfig(config);
```

### 3. 按曲线代码同步

```java
// 同步特定曲线代码
List<Integer> curveCodes = Arrays.asList(101, 102, 103);
int effectRows = optimizedService.syncBondYieldSpreadTraceByCurveCodes(startDate, endDate, curveCodes);

// 或者使用配置对象
SyncConfiguration config = SyncConfiguration.forCurveCode(startDate, endDate, 101);
int effectRows = optimizedService.syncBondYieldSpreadTraceWithConfig(config);
```

### 4. 并行同步

```java
// 使用指定并行度同步
int parallelism = 16; // 16个线程
int effectRows = optimizedService.syncBondYieldSpreadTraceParallel(startDate, endDate, parallelism);

// 或者使用配置对象
SyncConfiguration config = SyncConfiguration.builder()
    .startDate(startDate)
    .endDate(endDate)
    .parallel(true)
    .parallelism(parallelism)
    .batchSize(5) // 每批处理5天
    .build();
int effectRows = optimizedService.syncBondYieldSpreadTraceWithConfig(config);
```

### 5. 分类型同步

```java
// 仅同步绝对值数据
SyncConfiguration config = SyncConfiguration.builder()
    .startDate(startDate)
    .endDate(endDate)
    .syncAbs(true)
    .syncQuantile(false)
    .syncChange(false)
    .build();
int effectRows = optimizedService.syncBondYieldSpreadTraceAbsOnly(config);

// 仅同步分位数据
int effectRows = optimizedService.syncBondYieldSpreadTraceQuantileOnly(config);

// 仅同步变动数据
int effectRows = optimizedService.syncBondYieldSpreadTraceChangeOnly(config);
```

## REST API 使用

### 1. 按债券类型同步
```bash
POST /optimized/bond/yield/spread/trace/sync/by-bond-types
参数:
- startDate: 2024-01-01
- endDate: 2024-01-31
- bondTypes: 5,6,7 (可选，逗号分隔的债券类型值)
```

### 2. 按曲线代码同步
```bash
POST /optimized/bond/yield/spread/trace/sync/by-curve-codes
参数:
- startDate: 2024-01-01
- endDate: 2024-01-31
- curveCodes: 101,102,103 (可选，逗号分隔的曲线代码)
```

### 3. 并行同步
```bash
POST /optimized/bond/yield/spread/trace/sync/parallel
参数:
- startDate: 2024-01-01
- endDate: 2024-01-31
- parallelism: 8 (可选，默认为CPU核心数)
```

### 4. 物化视图控制
```bash
# 禁用物化视图刷新
POST /optimized/bond/yield/spread/trace/mv/disable-refresh
参数:
- viewName: bond_yield_spread_trace
- ttlSeconds: 3600

# 启用物化视图刷新
POST /optimized/bond/yield/spread/trace/mv/enable-refresh
参数:
- viewName: bond_yield_spread_trace
- ttlSeconds: 3600

# 查看刷新状态
GET /optimized/bond/yield/spread/trace/mv/refresh-status?viewName=bond_yield_spread_trace
```

## 物化视图刷新控制

### 1. 自动控制
```java
@Resource
private MaterializedViewRefreshController mvController;

// 检查是否应该刷新
boolean shouldRefresh = mvController.shouldRefreshMaterializedView("bond_yield_spread_trace");

// 标记已刷新
mvController.markMaterializedViewRefreshed("bond_yield_spread_trace");
```

### 2. 手动控制
```java
// 禁用1小时
mvController.disableMaterializedViewRefresh("bond_yield_spread_trace", 3600);

// 启用1小时
mvController.enableMaterializedViewRefresh("bond_yield_spread_trace", 3600);

// 清除控制
mvController.clearMaterializedViewRefreshControl("bond_yield_spread_trace");
```

### 3. 装饰器模式使用
```java
@Resource
private RefreshControlledDAO refreshControlledDAO;

// 执行带刷新控制的操作
int result = refreshControlledDAO.executeWithRefreshControl("bond_yield_spread_trace", 
    (shouldRefresh) -> {
        if (shouldRefresh) {
            // 执行包含物化视图刷新的操作
            return dao.syncWithRefresh();
        } else {
            // 执行不包含物化视图刷新的操作
            return dao.syncWithoutRefresh();
        }
    });
```

## 性能优化效果

### 1. 参数过滤优化
- **原来**: 无法按债券类型或曲线代码过滤，总是处理所有数据
- **现在**: 支持精确过滤，减少不必要的计算

### 2. 并行处理优化
- **原来**: 单线程串行处理，处理100天数据需要约100分钟
- **现在**: 16线程并行处理，处理100天数据约需要6-8分钟

### 3. 物化视图刷新优化
- **原来**: 每次同步都刷新物化视图，频繁的I/O操作
- **现在**: 1小时内最多刷新一次，减少90%的刷新操作

## 注意事项

1. **线程安全**: 并行执行时注意数据库连接池大小配置
2. **内存使用**: 大批量数据处理时注意JVM内存配置
3. **Redis依赖**: 物化视图控制功能依赖Redis，确保Redis可用
4. **兼容性**: 新接口完全独立，不影响原有功能
5. **监控**: 建议添加监控指标，观察优化效果

## 扩展建议

1. **添加更多过滤条件**: 如按日期范围、按评级等
2. **支持异步执行**: 对于大批量数据，支持异步处理并返回任务ID
3. **添加进度回调**: 支持进度监控和中断机制
4. **数据校验**: 添加同步前后的数据一致性校验
5. **性能监控**: 集成APM工具，监控执行性能
