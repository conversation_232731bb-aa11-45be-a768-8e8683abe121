package com.innodealing.onshore.yieldspread.controller.api;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.request.BankCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BankComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BankSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.service.BankBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.BankComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 银行利差
 *
 * <AUTHOR>
 */
@Api(tags = "(API)银行利差")
@RestController
@Validated
@RequestMapping("api/bank/yield-spread")
public class BankYieldSpreadController {

    @Resource
    private BankBondYieldSpreadService bankBondYieldSpreadService;

    @Resource
    private BankComYieldSpreadService bankComYieldSpreadService;

    @PostMapping("curve-save")
    @ApiOperation(value = "保存曲线")
    public RestResponse<Boolean> saveCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated BankCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(bankBondYieldSpreadService.saveCurve(userid, curveGroupId, request));
    }

    @PostMapping("curve-update")
    @ApiOperation(value = "更新曲线")
    public RestResponse<Boolean> updateCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Validated BankCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(bankBondYieldSpreadService.updateCurve(userid, curveId, request));
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public RestResponse<NormPagingResult<BankSingleBondYieldSpreadResDTO>> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(bankBondYieldSpreadService.pagingSingleBondYieldSpreads(userid, request));
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差  分页")
    public RestResponse<List<BankComYieldSpreadResDTO>> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(bankComYieldSpreadService.listComYieldSpreads(userid, request));
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public RestResponse<Long> countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(bankComYieldSpreadService.countComYieldSpread(userid, request));
    }

}
