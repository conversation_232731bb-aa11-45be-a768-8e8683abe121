<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgInduBondYieldSpreadCurveAllMapper">

    <update id="refreshMvInduBondYieldSpreadCurveFromMV">
        truncate yield_spread.indu_bond_yield_spread_curve_all;
        INSERT INTO yield_spread.indu_bond_yield_spread_curve_all
        SELECT * from yield_spread.mv_indu_bond_yield_spread_curve_all;
    </update>

    <insert id="syncCurveIncrFromMV">
        insert into yield_spread.indu_bond_yield_spread_curve_all
        select *
        from yield_spread.mv_indu_bond_yield_spread_curve_all_yesterday;
    </insert>
</mapper>