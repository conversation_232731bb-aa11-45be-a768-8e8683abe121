package com.innodealing.onshore.yieldspread.model.dto;

import com.innodealing.onshore.bondmetadata.enums.AreaTypeEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 城投利差查询参数
 *
 * <AUTHOR>
 */
public class UdicYieldSearchParam extends UniversalYieldSpreadSearchParam {

    @ApiModelProperty("省份编码")
    private Long provinceUniCode;

    @ApiModelProperty("地级市编码")
    private Long cityUniCode;

    /**
     * 区县编码
     */
    private Long districtUniCode;

    /**
     * @see AreaTypeEnum
     */
    @ApiModelProperty("行政区划")
    private Integer administrativeDivision;

    /**
     * 担保状态: 0: 无, 1: 有
     */
    private Integer guaranteedStatus;

    /**
     * 债券外部评级  1:AAA,2:AA+,3:AA
     */
    private Integer bondExtRatingMapping;

    public Long getDistrictUniCode() {
        return districtUniCode;
    }

    public void setDistrictUniCode(Long districtUniCode) {
        this.districtUniCode = districtUniCode;
    }

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }

    public Integer getAdministrativeDivision() {
        return administrativeDivision;
    }

    public void setAdministrativeDivision(Integer administrativeDivision) {
        this.administrativeDivision = administrativeDivision;
    }

    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

}
