package com.innodealing.onshore.yieldspread.model.entity.pgsharding.table;


import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 保险债券利差曲线 分片DO
 *
 * <AUTHOR>
 */
@Table(name = "insu_curve")
public class InsuShardBondYieldSpreadCurveDO {
    @Id
    @Column
    private Long id;
    /**
     * 保险公司债求偿顺序   求偿顺序 1资本补充债、2永续"
     */
    @Column
    private Integer insuranceSeniorityRanking;

    /**
     * 银行类型
     */
    @Column
    private Integer businessFilterNature;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 债券信用利差;单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpread;
    /**
     * 债券超额利差;单位(BP)
     */
    @Column
    private BigDecimal bondExcessSpread;
    /**
     * 中债收益率;单位(%)
     */
    @Column
    private BigDecimal cbYield;

    @Column
    private BigDecimal avgBondCreditSpread;
    /**
     * 超额利差;单位(BP)
     */
    @Column
    private BigDecimal avgBondExcessSpread;
    /**
     * 中债收益率;单位(BP)
     */
    @Column
    private BigDecimal avgCbYield;

    @Column
    private Integer bondCreditSpreadCount;
    /**
     * 超额利差;单位(BP)
     */
    @Column
    private Integer bondExcessSpreadCount;
    /**
     * 中债收益率;单位(BP)
     */
    @Column
    private Integer cbYieldCount;

    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;

    /**
     * 是否根据保险企业性质分组
     */
    @Column
    private Integer usingBusinessFilterNature;

    /**
     * 是否根据利差债券类别进行分组: 0：是，1：否
     */
    @Column
    private Integer usingInsuranceSeniorityRanking;

    /**
     * 是否根据利差剩余期限标签进行分组: 0：是，1：否
     */
    @Column
    private Integer usingSpreadRemainingTenorTag;


    public Integer getUsingSpreadRemainingTenorTag() {
        return usingSpreadRemainingTenorTag;
    }

    public void setUsingSpreadRemainingTenorTag(Integer usingSpreadRemainingTenorTag) {
        this.usingSpreadRemainingTenorTag = usingSpreadRemainingTenorTag;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getInsuranceSeniorityRanking() {
        return insuranceSeniorityRanking;
    }

    public void setInsuranceSeniorityRanking(Integer insuranceSeniorityRanking) {
        this.insuranceSeniorityRanking = insuranceSeniorityRanking;
    }

    public Integer getBusinessFilterNature() {
        return businessFilterNature;
    }

    public void setBusinessFilterNature(Integer businessFilterNature) {
        this.businessFilterNature = businessFilterNature;
    }

    public Integer getUsingBusinessFilterNature() {
        return usingBusinessFilterNature;
    }

    public void setUsingBusinessFilterNature(Integer usingBusinessFilterNature) {
        this.usingBusinessFilterNature = usingBusinessFilterNature;
    }

    public Integer getUsingInsuranceSeniorityRanking() {
        return usingInsuranceSeniorityRanking;
    }

    public void setUsingInsuranceSeniorityRanking(Integer usingInsuranceSeniorityRanking) {
        this.usingInsuranceSeniorityRanking = usingInsuranceSeniorityRanking;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getCbYieldCount() {
        return cbYieldCount;
    }

    public void setCbYieldCount(Integer cbYieldCount) {
        this.cbYieldCount = cbYieldCount;
    }
}
