package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.collect.Sets;
import com.innodealing.onshore.bondmetadata.dto.area.AreaInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.enums.AreaTypeEnum;
import com.innodealing.onshore.yieldspread.consts.TipsConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.model.dto.UdicYieldSearchParam;
import com.innodealing.onshore.yieldspread.service.internal.AreaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.util.Objects;

/**
 * 利差基类
 *
 * <AUTHOR>
 */
public abstract class AbstractBaseYieldSpreadService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    protected AreaService areaService;

    protected UdicYieldSearchParam buildUdicYieldSearchParamForArea(UdicYieldSearchParam searchParam, Long areaCode) {
        if (Objects.isNull(areaCode)) {
            return searchParam;
        }
        AreaInfoResponseDTO areaInfo = areaService.getAreaInfoMap(Sets.newHashSet(areaCode)).get(areaCode);
        if (Objects.isNull(areaInfo)) {
            logger.info("buildAreaCodeForSearch is null.area code is:{}", areaCode);
            setAreaAbnormalValue(searchParam);
            return searchParam;
        }
        AreaTypeEnum areaType = convertToAreaType(areaInfo);
        if (Objects.equals(areaType, AreaTypeEnum.PROVINCE)) {
            searchParam.setProvinceUniCode(areaInfo.getProvinceUniCode());
        } else if (Objects.equals(areaType, AreaTypeEnum.CITY)) {
            searchParam.setCityUniCode(areaInfo.getCityUniCode());
        } else if (Objects.equals(areaType, AreaTypeEnum.DISTRICT)) {
            searchParam.setDistrictUniCode(areaInfo.getDistrictUniCode());
        } else {
            setAreaAbnormalValue(searchParam);
        }
        return searchParam;
    }

    protected AreaTypeEnum convertToAreaType(@NotNull AreaInfoResponseDTO areaInfo) {
        //是区县或者园区
        if (Objects.nonNull(areaInfo.getDistrictUniCode())) {
            if (Objects.equals(areaInfo.getAreaUniCode(), areaInfo.getDistrictUniCode())) {
                return AreaTypeEnum.DISTRICT;
            } else {
                return AreaTypeEnum.ZONE;
            }
        }
        if (Objects.nonNull(areaInfo.getCityUniCode())) {
            return AreaTypeEnum.CITY;
        }
        if (Objects.nonNull(areaInfo.getProvinceUniCode())) {
            return AreaTypeEnum.PROVINCE;
        }
        return AreaTypeEnum.COUNTRY;
    }

    /**
     * area异常时设置0值，不返回数据给前端
     *
     * @param searchParam 查询参数
     */
    private void setAreaAbnormalValue(UdicYieldSearchParam searchParam) {
        searchParam.setProvinceUniCode(0L);
        searchParam.setCityUniCode(0L);
        searchParam.setDistrictUniCode(0L);
    }

    /**
     * 校验曲线所属和曲线类型
     *
     * @param searchUserId 查询用户id
     * @param curveUserId  曲线中保存的用户id
     * @param curveType    曲线类型
     */
    protected void checkBelongAndCurveType(Long searchUserId, Long curveUserId, Integer curveType) {
        if (!Objects.equals(curveUserId, YieldSpreadConst.BENCHMARK_CURVE_USER_ID) && !Objects.equals(searchUserId, curveUserId)) {
            throw new TipsException(TipsConst.CURVE_NOT_EXIST);
        }
        if (!Objects.equals(curveType, getCurveType().getValue())) {
            throw new TipsException("曲线类型不匹配");
        }
    }

    /**
     * 获取曲线类型
     *
     * @return 曲线类型
     */
    @NotNull
    protected abstract CurveTypeEnum getCurveType();

    /**
     * 获取最新利差日期
     *
     * @return 最新利差日期
     */
    protected abstract Date getMaxSpreadDate();

}
