package com.innodealing.onshore.yieldspread.router.shard;

import com.alibaba.fastjson.annotation.JSONField;

import java.sql.Date;
import java.util.List;
import java.util.Objects;

/**
 * 路由
 *
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S00118", "squid:S2162"})
public abstract class AbstractRatingRouter implements Comparable<AbstractRatingRouter> {
    @JSONField(serialize = false)
    private String level;
    @JSONField(serialize = false)
    private SpreadDateRange spreadDateRange;

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    /**
     * 路由评级集合
     *
     * @return list
     */
    public abstract List<Integer> getRatings();

    public SpreadDateRange getSpreadDateRange() {
        return spreadDateRange;
    }

    /**
     * 时间范围
     *
     * @param startDate startDate
     * @param endDate   endDate
     */
    public void setSpreadDateRange(Date startDate, Date endDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)){
            return;
        }
        spreadDateRange = new SpreadDateRange(startDate, endDate);
    }

    /**
     * 时间范围内部类
     */
    @SuppressWarnings({"squid:S2972"})
    public static class SpreadDateRange implements Comparable<SpreadDateRange> {
        private Date startDate;

        private Date endDate;

        /**
         * 构造方法
         *
         * @param startDate startDate
         * @param endDate   endDate
         */
        public SpreadDateRange(Date startDate, Date endDate) {
            this.startDate = new Date(startDate.getTime());
            this.endDate = new Date(endDate.getTime());
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof SpreadDateRange)) {
                return false;
            }
            SpreadDateRange that = (SpreadDateRange) o;
            return Objects.equals(getStartDate(), that.getStartDate()) && Objects.equals(getEndDate(), that.getEndDate());
        }

        @Override
        public int hashCode() {
            return Objects.hash(getStartDate(), getEndDate());
        }

        public Date getStartDate() {
            return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
        }

        public void setStartDate(Date startDate) {
            this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
        }

        public Date getEndDate() {
            return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
        }

        public void setEndDate(Date endDate) {
            this.endDate = Objects.isNull(endDate) ? null : new Date(endDate.getTime());
        }

        @Override
        public String toString() {
            return "[" + startDate.toString() + "-" + endDate.toString() + "]";
        }

        @Override
        public int compareTo(SpreadDateRange o) {
            return getStartDate().compareTo(o.getStartDate());
        }

    }
}
