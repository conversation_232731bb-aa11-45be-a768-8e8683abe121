package com.innodealing.onshore.yieldspread.handler;

import com.alibaba.excel.metadata.property.OnceAbsoluteMergeProperty;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;
import java.util.Objects;


/**
 * 自定义单元格合并处理器
 *
 * <AUTHOR>
 */
public class CustomManyMergeHandler implements SheetWriteHandler {

    /**
     * 合并列表
     */
    private List<OnceAbsoluteMergeProperty> onceAbsoluteMergeProperties;


    /**
     * 添加 单词合并对象
     *
     * @param firstRowIndex    First row
     * @param lastRowIndex     Last row
     * @param firstColumnIndex First column
     * @param lastColumnIndex  Last row
     */
    public void addOnceAbsoluteMerge(int firstRowIndex, int lastRowIndex, int firstColumnIndex, int lastColumnIndex) {
        if (firstRowIndex < 0 || lastRowIndex < 0 || firstColumnIndex < 0 || lastColumnIndex < 0) {
            throw new IllegalArgumentException("All parameters must be greater than 0");
        }
        if (Objects.isNull(onceAbsoluteMergeProperties)) {
            this.onceAbsoluteMergeProperties = Lists.newArrayList();
        }
        OnceAbsoluteMergeProperty onceAbsoluteMergeProperty
                = new OnceAbsoluteMergeProperty(firstRowIndex, lastRowIndex, firstColumnIndex, lastColumnIndex);
        onceAbsoluteMergeProperties.add(onceAbsoluteMergeProperty);
    }

    /**
     * 功能：单元格合并
     * sheet创建完成后调用
     *
     * @param writeWorkbookHolder workbook持有器
     * @param writeSheetHolder    sheet持有器
     */
    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if (CollectionUtils.isEmpty(onceAbsoluteMergeProperties)) {
            return;
        }
        for (OnceAbsoluteMergeProperty onceAbsoluteMergeProperty : onceAbsoluteMergeProperties) {
            if (onceAbsoluteMergeProperty.getFirstRowIndex() < 0 || onceAbsoluteMergeProperty.getLastRowIndex() < 0
                    || onceAbsoluteMergeProperty.getFirstColumnIndex() < 0 || onceAbsoluteMergeProperty.getLastColumnIndex() < 0) {
                throw new IllegalArgumentException("All parameters must be greater than 0");
            }
            CellRangeAddress cellRangeAddress =
                    new CellRangeAddress(onceAbsoluteMergeProperty.getFirstRowIndex(), onceAbsoluteMergeProperty.getLastRowIndex(),
                            onceAbsoluteMergeProperty.getFirstColumnIndex(), onceAbsoluteMergeProperty.getLastColumnIndex());
            writeSheetHolder.getSheet().addMergedRegionUnsafe(cellRangeAddress);
        }
    }
}
