package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import static java.util.Objects.isNull;


/**
 * 曲线请求DTO-组合条件请求参数（城投|行业公用）
 *
 * <AUTHOR>
 * @date 2022-09-26
 */
public class CurveCompositionConditionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("利差债券类型 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)")
    protected Integer spreadBondType;
    @ApiModelProperty("债券外部评级")
    protected Integer bondExtRatingMapping;
    @ApiModelProperty("隐含评级标签 1(AAA+,AAA,AAA-)  2(AA+,AA,AA-)  3(A+,A,A-)")
    protected Integer bondImpliedRatingMappingTag;
    @ApiModelProperty("yy评级标签 1(1,2,3,4,5)  2(6,7,8)")
    protected Integer comYyRatingMappingTag;
    @ApiModelProperty("剩余期限(1,2,3,4,5)")
    protected Integer spreadRemainingTenorTag;
    @ApiModelProperty("担保状态: 0: 无, 1: 有")
    protected Integer guaranteeStatus;
    @ApiModelProperty("曲线名称(导出时前端出入)")
    protected String curveName;
    /**
     * 隐含评级(AAA+,AAA,AAA-,AA+,AA,AA-,A+,A,A-)
     */
    @ApiModelProperty("隐含评级 10 AAA+,20 AAA 30 AAA-,40 AA+,50 AA, 60 AA-,  70 A+,80 A, 90 A-)")
    protected Integer[] bondImpliedRatingMappings;
    /**
     * yy评级(投资级:1、2、3、4、5,投机级:6、7、8、风险级:9、10)
     */
    @ApiModelProperty("yy评级 1,2,3,4,5  6,7,8")
    protected Integer[] comYyRatingMappings;

    public Integer[] getBondImpliedRatingMappings() {
        return isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public void setBondImpliedRatingMappings(Integer[] bondImpliedRatingMappings) {
        this.bondImpliedRatingMappings = isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public Integer[] getComYyRatingMappings() {
        return isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public void setComYyRatingMappings(Integer[] comYyRatingMappings) {
        this.comYyRatingMappings = isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer getBondImpliedRatingMappingTag() {
        return bondImpliedRatingMappingTag;
    }

    public void setBondImpliedRatingMappingTag(Integer bondImpliedRatingMappingTag) {
        this.bondImpliedRatingMappingTag = bondImpliedRatingMappingTag;
    }

    public Integer getComYyRatingMappingTag() {
        return comYyRatingMappingTag;
    }

    public void setComYyRatingMappingTag(Integer comYyRatingMappingTag) {
        this.comYyRatingMappingTag = comYyRatingMappingTag;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getGuaranteeStatus() {
        return guaranteeStatus;
    }

    public void setGuaranteeStatus(Integer guaranteeStatus) {
        this.guaranteeStatus = guaranteeStatus;
    }

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }
}
