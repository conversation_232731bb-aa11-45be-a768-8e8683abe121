package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceLineChartPeriodResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceLineChartRatingResponseDTO;
import com.innodealing.onshore.yieldspread.service.BondYieldSpreadTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.Date;

/**
 * (内部)利差追踪
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 利差追踪")
@RestController
@RequestMapping("internal/bond/yield/spread/trace")
public class InternalBondYieldSpreadTraceController {
    @Resource
    private BondYieldSpreadTraceService bondYieldSpreadTraceService;
    @Resource
    private HttpServletResponse httpServletResponse;

    @ApiOperation(value = "同步利差追踪(历史)")
    @GetMapping("/sync/history")
    public int syncHistBondYieldSpreadTrace(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate) {
        return bondYieldSpreadTraceService.syncHistBondYieldSpreadTrace(startDate);
    }

    @ApiOperation(value = "同步利差追踪(增量),不传日期默认同步上一天数据")
    @GetMapping("/sync/incr")
    public int syncBondYieldSpreadTrace(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam(required = false) Date endDate) {
        return bondYieldSpreadTraceService.syncBondYieldSpreadTrace(startDate, endDate);
    }

    @ApiOperation(value = "同步利差追踪-历史分位(历史)")
    @GetMapping("/sync/history/quantile")
    public int syncBondYieldSpreadTraceQuantile(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate) {
        return bondYieldSpreadTraceService.syncBondYieldSpreadTraceQuantile(startDate);
    }

    @ApiOperation(value = "同步利差追踪-区间变动(历史)")
    @GetMapping("/sync/history/interval-change")
    public int syncBondYieldSpreadTraceChange(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate) {
        return bondYieldSpreadTraceService.syncBondYieldSpreadTraceChange(startDate);
    }

    @ApiOperation("缓存利差追踪-折线图")
    @GetMapping("/cache/line-chart")
    public void cacheLineChart() {
        bondYieldSpreadTraceService.cacheLineChart();
    }

    @ApiOperation(value = "利差追踪-折线图(同评级)")
    @GetMapping(value = "/line-chart/rating")
    public YieldSpreadTraceLineChartRatingResponseDTO lineChartWithRating(
            @ApiParam(value = "债券类型(4:中短期票据,  5:产业债, 6:城投, 7:银行普通债, 8:银行二级资本债, 9:银行永续债, 10:证券公司债, 11:证券次级债, 12:证券永续债, 13:同业存单)",
                    name = "bondType", required = true) @RequestParam("bondType") Integer bondType,
            @ApiParam(value = "图类型，1:到期收益率 2:信用利差(债券类型-国开) 3:等级利差 4:期限利差 5:品种利差(债券类型-中短期票据) 6:条款利差(债券类型-普通债)",
                    name = "chartType", required = true) @RequestParam("chartType") Integer chartType,
            @ApiParam(value = "评级类型，1:AAA+ 2:AAA 3:AAA- 4:AA+ 5:AA 6:AA(2) 7:AA- 0:证券次级债，证券永续债时，评级类型为0,因为这两种没有评级",
                    name = "ratingType", required = true) @RequestParam(value = "ratingType") Integer ratingType,
            @ApiParam(name = "startDate", value = "起始日期") @RequestParam("startDate") Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam("endDate") Date endDate,
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid) {
        return bondYieldSpreadTraceService.lineChartWithRating(bondType, chartType, ratingType, startDate, endDate, userid);
    }

    @ApiOperation(value = "利差追踪-折线图(同期限)")
    @GetMapping(value = "/line-chart/period")
    public YieldSpreadTraceLineChartPeriodResponseDTO lineChartWithPeriod(
            @ApiParam(value = "债券类型(4:中短期票据,  5:产业债, 6:城投, 7:银行普通债, 8:银行二级资本债, 9:银行永续债, 10:证券公司债, 11:证券次级债, 12:证券永续债, 13:同业存单)",
                    name = "bondType", required = true) @RequestParam("bondType") Integer bondType,
            @ApiParam(value = "图类型，1:到期收益率 2:信用利差(债券类型-国开) 3:等级利差 4:期限利差 5:品种利差(债券类型-中短期票据) 6:条款利差(债券类型-普通债)",
                    name = "chartType", required = true) @RequestParam("chartType") Integer chartType,
            @ApiParam(value = "期限类型，1:1Y 2:2Y 3:3Y 5:5Y 7:7Y 0:证券次级债，证券永续债时，期限类型为0,因为只有一个评级，展示所有期限",
                    name = "periodType", required = true) @RequestParam(value = "periodType", required = false) Integer periodType,
            @ApiParam(name = "startDate", value = "起始日期") @RequestParam("startDate") Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam("endDate") Date endDate,
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid) {
        return bondYieldSpreadTraceService.lineChartWithPeriod(bondType, chartType, periodType, startDate, endDate, userid);
    }
}
