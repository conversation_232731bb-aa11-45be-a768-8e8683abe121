package com.innodealing.onshore.yieldspread.model.dto.response.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.innodealing.international.common.template.source.excel.convert.ExcelDateConvert;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.CURVE_EXPORT_TITLE;

/**
 * 曲线导出数据DTO
 *
 * <AUTHOR>
 */
public class CurveExportExcelDTO {

    @ApiModelProperty("曲线")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "曲线"})
    @ColumnWidth(25)
    private String curveName;

    @ApiModelProperty("利差日期")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "利差日期"}, converter = ExcelDateConvert.class)
    @ColumnWidth(25)
    private Date spreadDate;

    @ApiModelProperty("信用利差")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)"})
    @ColumnWidth(25)
    private BigDecimal creditSpread;

    @ApiModelProperty("超额利差")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)"})
    @ColumnWidth(25)
    private BigDecimal excessSpread;

    @ApiModelProperty("收益率")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率(%)"})
    @ColumnWidth(25)
    private BigDecimal cbYield;

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getCreditSpread() {
        return creditSpread;
    }

    public void setCreditSpread(BigDecimal creditSpread) {
        this.creditSpread = creditSpread;
    }

    public BigDecimal getExcessSpread() {
        return excessSpread;
    }

    public void setExcessSpread(BigDecimal excessSpread) {
        this.excessSpread = excessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

}
