package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BondYieldSpreadDO;
import org.apache.ibatis.annotations.Param;

/**
 * 债券利差
 *
 * <AUTHOR>
 */
public interface BondYieldSpreadMapper extends DynamicQueryMapper<BondYieldSpreadDO> {

    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    void createShardingTable(@Param("tableName") String tableName);

}
