package com.innodealing.onshore.yieldspread.controller.api;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.response.BondRatingResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTimeRangeDTO;
import com.innodealing.onshore.yieldspread.service.YieldSpreadCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 利差公共接口
 *
 * <AUTHOR>
 */
@Api(tags = "利差分析-公共接口")
@RestController
@RequestMapping("api/common/yield-spread")
public class CommonController {

    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;

    @ApiOperation(value = "利差分析-债券评级")
    @GetMapping("/list/bond-ratings")
    public RestResponse<List<BondRatingResponseDTO>> listBondRatings(
            @ApiParam(name = "ratingNames", value = "评级名称")
            @RequestParam(value = "ratingNames", required = false) String[] ratingNames) {
        return RestResponse.Success(yieldSpreadCommonService.listBondRatings(ratingNames));
    }

    @ApiOperation(value = "利差-时间范围")
    @GetMapping("/list/time-ranges")
    public RestResponse<List<YieldSpreadTimeRangeDTO>> getTimeRanges(
            @ApiParam(name = "date", value = "日期, 不传默认当日", required = false)
            @RequestParam(value = "date", required = false) Date date
    ){
        LocalDate queryDate = Optional.ofNullable(date).map(Date::toLocalDate).orElse(LocalDate.now());
        return RestResponse.Success(yieldSpreadCommonService.listTimeRange(queryDate));
    }

}
