package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadQuantileDO;

/**
 * 地方债区域利差历史分位 Mapper
 *
 * <AUTHOR>
 * @create: 2024-11-05
 */
public interface PgLgBondYieldSpreadQuantileMapper extends DynamicQueryMapper<PgLgBondYieldSpreadQuantileDO> {
}
