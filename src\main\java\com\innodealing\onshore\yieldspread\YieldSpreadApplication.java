package com.innodealing.onshore.yieldspread;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * 利差分析
 *
 * <AUTHOR>
 */
@EnableSwagger2
@SpringBootApplication
@EnableFeignClients
@EnableScheduling
public class YieldSpreadApplication {

    /**
     * 主方法
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        SpringApplication.run(YieldSpreadApplication.class, args);
    }

}
