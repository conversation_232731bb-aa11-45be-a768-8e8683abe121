package com.innodealing.onshore.yieldspread.model.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 证券主体利差
 *
 * <AUTHOR>
 */
public class SecuComYieldSpreadBO extends BaseFinanceComYieldSpreadBO {

    @ApiModelProperty("资本杠杆率(%)")
    private BigDecimal capitalLeverageRatio;

    @ApiModelProperty("主体信用利差(二级资本);单位(BP)")
    private BigDecimal comSubordinatedCreditSpread;

    @ApiModelProperty("主体超额利差(二级资本);单位(BP)")
    private BigDecimal comSubordinatedExcessSpread;

    @ApiModelProperty("主体估值收益率(二级资本);单位(%)")
    private BigDecimal comSubordinatedCbYield;

    public BigDecimal getCapitalLeverageRatio() {
        return capitalLeverageRatio;
    }

    public void setCapitalLeverageRatio(BigDecimal capitalLeverageRatio) {
        this.capitalLeverageRatio = capitalLeverageRatio;
    }

    public BigDecimal getComSubordinatedCreditSpread() {
        return comSubordinatedCreditSpread;
    }

    public void setComSubordinatedCreditSpread(BigDecimal comSubordinatedCreditSpread) {
        this.comSubordinatedCreditSpread = comSubordinatedCreditSpread;
    }

    public BigDecimal getComSubordinatedExcessSpread() {
        return comSubordinatedExcessSpread;
    }

    public void setComSubordinatedExcessSpread(BigDecimal comSubordinatedExcessSpread) {
        this.comSubordinatedExcessSpread = comSubordinatedExcessSpread;
    }

    public BigDecimal getComSubordinatedCbYield() {
        return comSubordinatedCbYield;
    }

    public void setComSubordinatedCbYield(BigDecimal comSubordinatedCbYield) {
        this.comSubordinatedCbYield = comSubordinatedCbYield;
    }

}
