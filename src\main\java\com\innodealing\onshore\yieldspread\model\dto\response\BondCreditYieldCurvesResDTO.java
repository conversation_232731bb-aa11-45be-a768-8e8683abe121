package com.innodealing.onshore.yieldspread.model.dto.response;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 债券信用利差曲线
 *
 * <AUTHOR>
 */
public class BondCreditYieldCurvesResDTO {

    /**
     * 利差日期
     */
    private List<Date> spreadDates;

    /**
     * 信用利差
     * 外层list跟spreadDate对应，内层list是放的同一天的多个BondUniCode的信用利差数据，具体是几个，看前端传几个
     */
    private List<List<String>> creditYieldSpreads;

    public List<Date> getSpreadDates() {
        return Objects.isNull(spreadDates) ? new ArrayList<>() : new ArrayList<>(spreadDates);
    }

    public void setSpreadDates(List<Date> spreadDates) {
        this.spreadDates = Objects.isNull(spreadDates) ? new ArrayList<>() : new ArrayList<>(spreadDates);
    }

    public List<List<String>> getCreditYieldSpreads() {
        return Objects.isNull(creditYieldSpreads) ? new ArrayList<>() : new ArrayList<>(creditYieldSpreads);
    }

    public void setCreditYieldSpreads(List<List<String>> creditYieldSpreads) {
        this.creditYieldSpreads = Objects.isNull(creditYieldSpreads) ? new ArrayList<>() : new ArrayList<>(creditYieldSpreads);
    }

}
