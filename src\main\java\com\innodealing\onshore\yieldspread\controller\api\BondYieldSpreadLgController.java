package com.innodealing.onshore.yieldspread.controller.api;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.commons.json.JSON;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldLgExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.LgComAreaResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldLgResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldTraceLineCommonResponseDTO;
import com.innodealing.onshore.yieldspread.service.LgYieldSpreadService;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 利差地方债区域利差
 *
 * <AUTHOR>
 * @date 2023/4/26 11:32
 */
@Api(tags = "(API)地方债区域利差")
@RestController
@RequestMapping("api/bond/yield/spread/lg")
public class BondYieldSpreadLgController {

    @Resource
    private LgYieldSpreadService bondYieldSpreadLgService;

    @Resource
    private HttpServletResponse httpServletResponse;

    @ApiImplicitParams({
            @ApiImplicitParam(name = "lgSpreadBaseRequestDTO", required = true,
                    dataType = "LgSpreadBaseRequestDTO", paramType = "body")
    })
    @ApiOperation(value = "地方债区域利差展示-绝对值")
    @PostMapping(value = "/chart/base", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<YieldLgResponseDTO> getYieldSpreadLgBase(
            @RequestBody String lgSpreadBaseRequestDTO,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        LgSpreadBaseRequestDTO requestDTO = JSON.parseObject(lgSpreadBaseRequestDTO, LgSpreadBaseRequestDTO.class);
        return RestResponse.Success(bondYieldSpreadLgService.getYieldSpreadLgBase(userId, requestDTO));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "lgSpreadQuantileRequestDTO", required = true,
                    dataType = "LgSpreadQuantileRequestDTO", paramType = "body")
    })
    @ApiOperation(value = "地方债区域利差展示-历史分位")
    @PostMapping(value = "/chart/hist-quantile", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<YieldLgResponseDTO> getYieldSpreadLgQuantile(
            @RequestBody String lgSpreadQuantileRequestDTO,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        LgSpreadQuantileRequestDTO requestDTO = JSON.parseObject(lgSpreadQuantileRequestDTO, LgSpreadQuantileRequestDTO.class);
        return RestResponse.Success(bondYieldSpreadLgService.getYieldSpreadLgQuantile(userId, requestDTO));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "lgSpreadChangeRequestDTO", required = true,
                    dataType = "LgSpreadChangeRequestDTO", paramType = "body")
    })
    @ApiOperation(value = "地方债区域利差展示-区间变动")
    @PostMapping(value = "/chart/interval-change", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<YieldLgResponseDTO> listYieldSpreadLgChange(
            @RequestBody String lgSpreadChangeRequestDTO,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        LgSpreadChangeRequestDTO requestDTO = JSON.parseObject(lgSpreadChangeRequestDTO, LgSpreadChangeRequestDTO.class);
        return RestResponse.Success(bondYieldSpreadLgService.listYieldSpreadLgChange(userId, requestDTO));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "lgSpreadLinePeriodRequestDTO", required = true,
                    dataType = "LgSpreadLinePeriodRequestDTO", paramType = "body")
    })
    @ApiOperation(value = "地方债区域利差图区-地区对比(同一期限)")
    @PostMapping(value = "/lg-line-chart/period", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<YieldTraceLineCommonResponseDTO> lgLineChartWithPeriod(
            @RequestBody String lgSpreadLinePeriodRequestDTO,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        LgSpreadLinePeriodRequestDTO requestDTO = JSON.parseObject(lgSpreadLinePeriodRequestDTO, LgSpreadLinePeriodRequestDTO.class);
        return RestResponse.Success(bondYieldSpreadLgService.lgLineChartWithPeriod(userId, requestDTO));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "lgSpreadLineAreaRequestDTO", required = true,
                    dataType = "LgSpreadLineAreaRequestDTO", paramType = "body")
    })
    @ApiOperation(value = "地方债区域利差图区-同地区")
    @PostMapping(value = "/lg-line-chart/area", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<YieldTraceLineCommonResponseDTO> lgLineChartWithArea(
            @RequestBody String lgSpreadLineAreaRequestDTO,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        LgSpreadLineAreaRequestDTO requestDTO = JSON.parseObject(lgSpreadLineAreaRequestDTO, LgSpreadLineAreaRequestDTO.class);
        return RestResponse.Success(bondYieldSpreadLgService.lgLineChartWithArea(userId, requestDTO));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "bondYieldLgExportReqDTO", required = true,
                    dataType = "BondYieldLgExportReqDTO", paramType = "body")
    })
    @ApiOperation(value = "地方债区域利差展示-导出")
    @PostMapping(value = "/chart/export", consumes = MediaType.TEXT_PLAIN_VALUE)
    public void exportYieldSpreadLg(@Validated @RequestBody String bondYieldLgExportReqDTO,
                                    @ApiParam(name = "userid", value = "用户编号", hidden = true)
                                    @CookieValue("userid") Long userId) throws IOException {
        BondYieldLgExportReqDTO requestDTO = JSON.parseObject(bondYieldLgExportReqDTO, BondYieldLgExportReqDTO.class);
        bondYieldSpreadLgService.exportYieldSpreadLg(httpServletResponse, userId, requestDTO);
    }

    @ApiOperation(value = "地方债区域利差-区域列表")
    @GetMapping(value = "/area/list")
    public RestResponse<List<LgComAreaResponseDTO>> listAllComArea() {
        return RestResponse.Success(bondYieldSpreadLgService.listAllComArea());
    }

    @ApiOperation(value = "地方债区域利差-最新数据日期")
    @GetMapping(value = "/max-spread-date")
    public RestResponse<String> maxSpreadDate(){
        return RestResponse.Success(bondYieldSpreadLgService.maxSpreadDate().toString());
    }

}
