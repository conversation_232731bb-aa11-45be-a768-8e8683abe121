13:56:26.400 [main] INFO  c.i.o.y.YieldSpreadApplication - [TID: N/A] - message:The following profiles are active: dev
13:56:33.380 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - [TID: N/A] - message:Skipping MapperFactoryBean with name 'pgYieldSpreadCompositeSearchMapper' and 'com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgYieldSpreadCompositeSearchMapper' mapperInterface. Bean already defined with the same name!
13:56:33.380 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - [TID: N/A] - message:Skipping MapperFactoryBean with name 'yieldSpreadCompositeSearchMapper' and 'com.innodealing.onshore.yieldspread.mapper.yieldspread.YieldSpreadCompositeSearchMapper' mapperInterface. Bean already defined with the same name!
13:56:33.381 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - [TID: N/A] - message:No MyBatis mapper was found in '[com.innodealing.onshore.yieldspread]' package. Please check your configuration.
13:56:33.432 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [TID: N/A] - message:Multiple Spring Data modules found, entering strict repository configuration mode!
13:56:33.433 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [TID: N/A] - message:Bootstrapping Spring Data Redis repositories in DEFAULT mode.
13:56:33.503 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [TID: N/A] - message:Finished Spring Data repository scanning in 60ms. Found 0 Redis repository interfaces.
13:56:33.680 [main] INFO  o.s.cloud.context.scope.GenericScope - [TID: N/A] - message:BeanFactory id=1ae4855d-8510-3a45-bd9e-969de7a6a1a6
13:56:33.845 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.AreaService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.846 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondFinanceService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.847 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondInfoService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.848 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondPriceApolloService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.849 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondPriceService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.850 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondRatingService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.851 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.ComService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.852 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.FinancialInstitutionService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.852 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.HolidayService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.853 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.InternalKeyValueService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.854 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.UdicInfoService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:33.854 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.UserService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
13:56:34.305 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - [TID: N/A] - message:Tomcat initialized with port(s): 8080 (http)
13:56:34.314 [main] INFO  o.a.coyote.http11.Http11NioProtocol - [TID: N/A] - message:Initializing ProtocolHandler ["http-nio-8080"]
13:56:34.314 [main] INFO  o.a.catalina.core.StandardService - [TID: N/A] - message:Starting service [Tomcat]
13:56:34.314 [main] INFO  o.a.catalina.core.StandardEngine - [TID: N/A] - message:Starting Servlet engine: [Apache Tomcat/9.0.31]
13:56:34.446 [main] INFO  o.a.c.c.C.[.[.[/onshore-yield-spread] - [TID: N/A] - message:Initializing Spring embedded WebApplicationContext
13:56:34.446 [main] INFO  o.s.web.context.ContextLoader - [TID: N/A] - message:Root WebApplicationContext: initialization completed in 8033 ms
13:56:34.828 [main] INFO  c.i.o.y.c.d.YieldSpreadDataSourceConfig$$EnhancerBySpringCGLIB$$67e8a3de - [TID: N/A] - message:[创建分片数据源:null]
13:56:35.701 [main] INFO  c.alibaba.druid.pool.DruidDataSource - [TID: N/A] - message:{dataSource-1} inited
13:56:36.096 [main] INFO  o.a.s.c.c.log.ConfigurationLogger - [TID: N/A] - message:ShardingRuleConfiguration
tables:
  udic_area_yield_spread:
    actualDataNodes: yield_spread.udic_area_yield_spread
    logicTable: udic_area_yield_spread
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.StrParamHintShardingAlgorithm
  indu_bond_yield_spread:
    actualDataNodes: yield_spread.indu_bond_yield_spread
    logicTable: indu_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  udic_bond_yield_spread:
    actualDataNodes: yield_spread.udic_bond_yield_spread
    logicTable: udic_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.CaliberYearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.CaliberYearTableRangeShardingAlgorithm
        shardingColumn: id
  secu_bond_yield_spread:
    actualDataNodes: yield_spread.indu_bond_yield_spread
    logicTable: secu_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  lg_bond_yield_spread:
    actualDataNodes: yield_spread.indu_bond_yield_spread
    logicTable: lg_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  bank_bond_yield_spread:
    actualDataNodes: yield_spread.indu_bond_yield_spread
    logicTable: bank_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  insu_bond_yield_spread:
    actualDataNodes: yield_spread.insu_bond_yield_spread
    logicTable: insu_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  bond_yield_spread:
    actualDataNodes: yield_spread.bond_yield_spread
    logicTable: bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id

13:56:36.098 [main] INFO  o.a.s.c.c.log.ConfigurationLogger - [TID: N/A] - message:Properties
sql.show: 'true'

13:56:36.163 [main] INFO  o.a.s.c.e.m.TableMetaDataInitializer - [TID: N/A] - message:TableMetaDataInitializer => loading...
13:56:36.226 [main] INFO  o.a.s.c.e.m.TableMetaDataInitializer - [TID: N/A] - message:TableMetaDataInitializer => load success result：8 StopWatch '': running time = 62092400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
062031900  100%  loadShardingTables
000060500  000%  loadDefaultTables

13:56:37.097 [main] INFO  org.redisson.Version - [TID: N/A] - message:Redisson 3.21.3
13:56:37.467 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - [TID: N/A] - message:1 connections initialized for 192.168.9.153/192.168.9.153:6379
13:56:37.567 [redisson-netty-2-19] INFO  o.r.c.pool.MasterConnectionPool - [TID: N/A] - message:24 connections initialized for 192.168.9.153/192.168.9.153:6379
13:56:38.537 [main] INFO  c.alibaba.druid.pool.DruidDataSource - [TID: N/A] - message:{dataSource-2} inited
13:56:39.337 [main] INFO  c.alibaba.druid.pool.DruidDataSource - [TID: N/A] - message:{dataSource-3} inited
13:56:39.836 [main] INFO  c.i.o.y.c.d.PgShardingYieldSpreadDataSourceConfig$$EnhancerBySpringCGLIB$$ff95b3a3 - [TID: N/A] - message:[创建分片数据源:***************************************************************************************************************************************]
13:56:39.878 [main] INFO  o.a.s.c.c.log.ConfigurationLogger - [TID: N/A] - message:ShardingRuleConfiguration
tables:
  indu_curve:
    actualDataNodes: yield_spread.indu_curve
    logicTable: indu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  udic_curve:
    actualDataNodes: yield_spread.udic_curve
    logicTable: udic_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  secu_curve:
    actualDataNodes: yield_spread.secu_curve
    logicTable: secu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  bank_curve:
    actualDataNodes: yield_spread.bank_curve
    logicTable: bank_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  insu_curve:
    actualDataNodes: yield_spread.insu_curve
    logicTable: insu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_indu_curve:
    actualDataNodes: yield_spread.mv_indu_curve
    logicTable: mv_indu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_udic_curve:
    actualDataNodes: yield_spread.mv_udic_curve
    logicTable: mv_udic_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_secu_curve:
    actualDataNodes: yield_spread.mv_secu_curve
    logicTable: mv_secu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_bank_curve:
    actualDataNodes: yield_spread.mv_bank_curve
    logicTable: mv_bank_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_insu_curve:
    actualDataNodes: yield_spread.mv_insu_curve
    logicTable: mv_insu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm

13:56:39.878 [main] INFO  o.a.s.c.c.log.ConfigurationLogger - [TID: N/A] - message:Properties
{}

13:56:40.006 [main] INFO  o.a.s.c.e.m.TableMetaDataInitializer - [TID: N/A] - message:TableMetaDataInitializer => loading...
13:56:40.111 [main] INFO  o.a.s.c.e.m.TableMetaDataInitializer - [TID: N/A] - message:TableMetaDataInitializer => load success result：10 StopWatch '': running time = ********* ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
*********  100%  loadShardingTables
*********  000%  loadDefaultTables

13:56:42.238 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:42.250 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:42.632 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:42.632 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:42.632 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@613452e1, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@23639e5, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2404ab3a, containsSubquery=false)
13:56:42.632 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:43.031 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:43.031 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:43.063 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:43.063 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:43.063 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@140db646, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@4e5d611f, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@37b3e8b3, containsSubquery=false)
13:56:43.063 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:43.088 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:43.094 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:43.094 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:43.094 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:43.094 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:43.094 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@37bdd4b1, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@656605e7, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@29d8d7a9, containsSubquery=false)
13:56:43.094 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:43.356 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:43.356 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:43.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:43.357 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:43.357 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5c24a636, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@332e4784, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2f22049d, containsSubquery=false)
13:56:43.357 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:43.382 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:43.384 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:43.384 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:43.384 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:43.384 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:43.384 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@58f45557, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@674db438, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5e5af20c, containsSubquery=false)
13:56:43.384 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:44.191 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:44.191 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:44.191 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:44.191 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:44.191 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@e83d546, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7408c08, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@767f03c5, containsSubquery=false)
13:56:44.191 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:44.217 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:44.218 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:44.218 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:44.218 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:44.218 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:44.218 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4bc2cb11, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@452d9887, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5aa16232, containsSubquery=false)
13:56:44.219 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:44.517 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:44.518 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:44.518 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:44.518 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:44.518 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6ff08304, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3e11cc3a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1d033ae1, containsSubquery=false)
13:56:44.518 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:44.539 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:44.542 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:44.542 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:44.542 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:44.542 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:44.542 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@627ceaf, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2f738b8, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@784bea2c, containsSubquery=false)
13:56:44.542 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:45.220 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:45.220 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:45.221 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:45.221 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:45.221 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@70661538, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@575b9b92, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@de7abc9, containsSubquery=false)
13:56:45.221 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:45.244 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:45.247 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:45.248 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:45.248 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:45.248 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:45.248 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@738e057b, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@43874120, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1786e7a5, containsSubquery=false)
13:56:45.248 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:46.024 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:46.026 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:46.028 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:46.028 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:46.028 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@10e7192, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@219505d9, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@24fe3184, containsSubquery=false)
13:56:46.028 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:46.067 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:46.079 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:46.080 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:46.080 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:46.080 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:46.081 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6aff891f, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3f58e7d5, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4289feba, containsSubquery=false)
13:56:46.081 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:47.101 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:47.102 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:47.102 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:47.103 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:47.103 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@20ecbeda, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2d1df853, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@79052209, containsSubquery=false)
13:56:47.103 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:47.126 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:47.133 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:47.134 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:47.134 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:47.134 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:47.134 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@19f4bfce, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5c2fb167, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@364f0a6f, containsSubquery=false)
13:56:47.134 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:47.476 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:47.477 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:47.477 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:47.477 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:47.477 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2882a80d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@36d28b4c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6d919a4, containsSubquery=false)
13:56:47.477 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:47.505 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:47.508 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:47.508 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:47.509 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:47.509 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:47.509 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6a79a909, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@721d3a03, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@76d404ab, containsSubquery=false)
13:56:47.509 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:47.765 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:47.765 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:47.765 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:47.765 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:47.766 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2236756e, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7491ccdf, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5839306e, containsSubquery=false)
13:56:47.766 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:47.791 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:47.796 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:47.796 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:47.796 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:47.797 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:47.797 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@e0eecbe, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1fa51751, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@71a7e67, containsSubquery=false)
13:56:47.797 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:48.675 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:48.675 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:48.675 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:48.675 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:48.675 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@24c00206, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@70fda9bf, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4d682552, containsSubquery=false)
13:56:48.675 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:48.698 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:48.700 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:48.700 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:48.700 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:48.700 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:48.700 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@238bee26, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2fc51db0, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@22d178d3, containsSubquery=false)
13:56:48.700 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:49.676 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:49.676 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:49.676 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:49.676 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:49.676 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5710fca8, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7e2e1179, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@19e7aa5b, containsSubquery=false)
13:56:49.676 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:49.701 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:49.704 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:49.704 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:49.704 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:49.704 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:49.704 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6de06606, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@49f1ac93, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@8937f62, containsSubquery=false)
13:56:49.704 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:50.213 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:50.213 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:50.213 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:50.213 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:50.213 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@19ba5771, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@a41434b, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@126688a7, containsSubquery=false)
13:56:50.213 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:50.235 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:50.238 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:50.238 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:50.238 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:50.238 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:50.238 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5bf52832, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7df0a05b, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@485c31d7, containsSubquery=false)
13:56:50.238 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:50.778 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:50.778 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:50.778 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:50.778 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:50.778 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6a8e4df1, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@43f50bfe, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@35e92c45, containsSubquery=false)
13:56:50.778 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:50.803 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:50.806 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:50.806 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:50.806 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:50.806 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:50.806 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@72cdb111, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@eba3009, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2159c25e, containsSubquery=false)
13:56:50.806 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:51.618 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:51.618 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:51.618 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:51.618 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:51.618 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@1f8d3539, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@70295fa4, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3f90f77a, containsSubquery=false)
13:56:51.618 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:51.639 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:51.642 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:51.642 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:51.642 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:51.642 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:51.642 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@70bdc9df, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@482ecb95, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@79360eff, containsSubquery=false)
13:56:51.642 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:52.153 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:52.153 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:52.153 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:52.153 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:52.153 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@84fd359, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@41c77559, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@196d7c9e, containsSubquery=false)
13:56:52.153 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:52.175 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:52.179 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:52.179 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:52.179 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:52.179 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:52.179 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@155d00b5, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@de61005, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2b84d474, containsSubquery=false)
13:56:52.179 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:52.947 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:52.947 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:52.947 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:52.947 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:52.947 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6105265d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2fb90f1, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6283ac9, containsSubquery=false)
13:56:52.947 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:52.967 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:52.971 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:52.971 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:52.971 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:52.971 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:52.971 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4b969bd9, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@caf0e10, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@99c5646, containsSubquery=false)
13:56:52.971 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:53.498 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:53.498 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:53.498 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:53.498 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:53.498 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@306a875c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5bafd971, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@56418318, containsSubquery=false)
13:56:53.498 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:53.520 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:53.523 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:53.523 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:53.523 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:53.523 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:53.523 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5dcd3a19, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7ce2bfb, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5a8fb312, containsSubquery=false)
13:56:53.523 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:54.366 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:54.366 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:54.366 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:54.366 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:54.366 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@d4fe4d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@27e4ca66, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@36c6509c, containsSubquery=false)
13:56:54.366 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:54.388 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:54.390 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:54.390 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:54.390 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:54.390 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:54.390 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7abc82fa, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7e7a6f81, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@79acb200, containsSubquery=false)
13:56:54.390 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:54.965 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:54.965 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:54.965 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:54.965 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:54.965 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@67954442, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7bb5c602, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@714ab3a1, containsSubquery=false)
13:56:54.965 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:54.989 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:54.991 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:54.991 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:54.991 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:54.991 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:54.991 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5282f922, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@76f18f68, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7e5383d1, containsSubquery=false)
13:56:54.991 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:55.559 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:55.559 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:55.559 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:55.559 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:55.559 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@53bd2b00, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@319267ad, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4305b87e, containsSubquery=false)
13:56:55.559 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:55.581 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:55.582 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:55.583 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:55.583 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:55.583 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:55.583 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@198453c9, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@772ccecf, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@479a2453, containsSubquery=false)
13:56:55.583 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:56.103 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:56.103 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:56.103 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:56.103 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:56.103 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@19adcb41, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@f03dc9b, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5c24724, containsSubquery=false)
13:56:56.103 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:56.128 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:56.131 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:56.131 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:56.131 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:56.131 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:56.131 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5ada5fd3, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@4d36c8cb, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@15733aa5, containsSubquery=false)
13:56:56.131 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:56.648 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:56.648 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:56.648 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:56.648 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:56.648 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@487fc205, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@44556fbd, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@39f29540, containsSubquery=false)
13:56:56.648 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:56.675 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:56.676 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:56.676 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:56.676 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:56.676 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:56.676 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@722b4f64, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5fde8cf4, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5f708ac6, containsSubquery=false)
13:56:56.676 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:57.444 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:57.444 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:57.444 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:57.444 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:57.444 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2613a6fc, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7d6e3e42, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@103388c6, containsSubquery=false)
13:56:57.444 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:57.465 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:57.468 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:57.468 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:57.468 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:57.468 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:57.468 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@232dda63, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2f541378, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2b1cf779, containsSubquery=false)
13:56:57.468 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:57.997 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:57.997 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:57.997 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:57.997 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:57.997 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2df9b4f3, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@6112390a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5ac92082, containsSubquery=false)
13:56:57.997 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:58.021 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:58.023 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:58.023 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:58.023 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:58.023 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:58.023 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@256bfbbb, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@363502a4, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7ab66d54, containsSubquery=false)
13:56:58.023 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:58.809 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:58.809 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:58.809 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:58.809 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:58.809 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@41c96a67, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@8dc73c7, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@728c3a0e, containsSubquery=false)
13:56:58.809 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:58.831 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:58.833 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:58.833 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:58.835 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:58.835 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:58.835 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@773e8030, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@64718893, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@485c8d5e, containsSubquery=false)
13:56:58.835 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:59.623 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:56:59.623 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:59.623 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:59.623 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:59.623 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3db32d5, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2f41798d, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1602b644, containsSubquery=false)
13:56:59.623 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:56:59.644 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:56:59.648 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:56:59.648 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:56:59.648 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:56:59.648 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:56:59.648 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@33b09909, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3003b9ad, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@b7a74ac, containsSubquery=false)
13:56:59.648 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:00.181 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:00.181 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:00.181 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:00.181 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:00.181 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4e2bd0df, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@162c02a2, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@79c0e385, containsSubquery=false)
13:57:00.181 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:00.205 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:00.208 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:00.208 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:00.209 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:00.209 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:00.209 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3a410524, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@44e31522, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@55a7c43, containsSubquery=false)
13:57:00.209 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:00.998 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:00.998 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:00.998 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:00.998 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:00.998 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@561dc11f, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7317fb, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@36bf28d2, containsSubquery=false)
13:57:00.998 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:01.023 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:01.026 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:01.026 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:01.027 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:01.027 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:01.027 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@40e0213c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@fd6784a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@272ee84a, containsSubquery=false)
13:57:01.027 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:01.552 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:01.552 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:01.553 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:01.553 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:01.553 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@232b4552, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@739785c5, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@77f76656, containsSubquery=false)
13:57:01.553 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:01.592 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:01.595 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:01.595 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:01.595 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:01.595 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:01.595 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@452888c2, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@9a183e7, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@487e4b06, containsSubquery=false)
13:57:01.595 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:02.222 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:02.222 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:02.222 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:02.222 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:02.222 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@32e58546, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@879ce67, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3546439b, containsSubquery=false)
13:57:02.222 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:02.244 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:02.247 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:02.247 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:02.247 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:02.247 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:02.247 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2ef1b7f4, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@29ac632a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3e45ca81, containsSubquery=false)
13:57:02.247 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:03.284 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:03.284 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:03.284 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:03.284 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:03.284 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@55db4eea, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@45da9712, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@354880a9, containsSubquery=false)
13:57:03.284 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:03.307 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:03.309 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:03.309 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:03.310 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:03.310 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:03.310 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@125dcfe1, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@63e148cb, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@747bf7a, containsSubquery=false)
13:57:03.310 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:04.142 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:04.142 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:04.142 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:04.142 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:04.142 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@645b2ac7, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5e5df559, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7481775c, containsSubquery=false)
13:57:04.142 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:04.189 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:04.192 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:04.192 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:04.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:04.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:04.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@59a6a709, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@266200f6, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@70ccf921, containsSubquery=false)
13:57:04.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:05.015 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:05.015 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:05.015 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:05.015 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:05.015 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@41e5450f, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5677b9e3, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6a6e6278, containsSubquery=false)
13:57:05.015 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:05.040 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:05.044 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:05.044 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:05.044 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:05.044 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:05.044 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@291b3e68, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@307fe491, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6f9de548, containsSubquery=false)
13:57:05.044 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:05.889 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:05.889 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:05.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:05.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:05.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@53a15397, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@53964b0e, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@50db0b17, containsSubquery=false)
13:57:05.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:05.912 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:05.915 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:05.915 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:05.915 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:05.915 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:05.915 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5fada492, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@57268fc3, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4f4a0e4, containsSubquery=false)
13:57:05.915 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:06.731 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:06.731 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:06.732 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:06.732 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:06.732 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@79e98bd6, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@58bbe78f, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3f4e090c, containsSubquery=false)
13:57:06.732 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:06.754 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:06.757 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:06.757 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:06.757 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:06.757 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:06.757 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5a8f777, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@4a6198a7, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2e06a7e4, containsSubquery=false)
13:57:06.757 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:07.556 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:07.556 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:07.556 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:07.556 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:07.556 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6fa1dc0c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@74037f9b, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@b0dc426, containsSubquery=false)
13:57:07.556 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:07.581 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:07.583 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:07.583 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:07.583 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:07.583 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:07.583 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@32200389, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@57e9dd1f, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6e1134e1, containsSubquery=false)
13:57:07.583 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:08.586 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:08.586 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:08.586 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:08.586 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:08.586 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@1a6804d1, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7db99d41, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3e0fe68f, containsSubquery=false)
13:57:08.586 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:08.610 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:08.613 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:08.613 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:08.613 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:08.613 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:08.613 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@578f8288, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@16f52d68, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6616b9e0, containsSubquery=false)
13:57:08.613 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:09.381 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:09.381 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:09.381 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:09.381 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:09.381 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7e22f3da, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2d288c47, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5d8c9945, containsSubquery=false)
13:57:09.381 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:09.404 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:09.407 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:09.407 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:09.408 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:09.408 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:09.408 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4e74adb, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3d18bdb3, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@30336205, containsSubquery=false)
13:57:09.408 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:10.171 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:10.171 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:10.172 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:10.172 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:10.172 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3e71b1a4, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1579cf25, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@77459635, containsSubquery=false)
13:57:10.172 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:10.200 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:10.202 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:10.202 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:10.203 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:10.203 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:10.203 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3ac726af, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@61e8fbfc, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2ea55742, containsSubquery=false)
13:57:10.203 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:10.734 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:10.734 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:10.734 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:10.734 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:10.734 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@cb4999d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@8afd00e, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1bd7bdf, containsSubquery=false)
13:57:10.735 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:10.771 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:10.774 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:10.774 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:10.774 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:10.774 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:10.774 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@58178c5e, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@329e6700, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@440e28fa, containsSubquery=false)
13:57:10.774 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:11.326 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:11.326 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:11.326 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:11.326 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:11.326 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3fd9c5db, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@a256322, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6860341b, containsSubquery=false)
13:57:11.326 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:11.351 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:11.353 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:11.353 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:11.353 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:11.353 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:11.353 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3610b408, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5fb99e3d, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@61f18402, containsSubquery=false)
13:57:11.353 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:12.110 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:12.110 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:12.110 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:12.110 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:12.110 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@39911d6a, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@54ad5fce, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@63b187f, containsSubquery=false)
13:57:12.110 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:12.148 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:12.150 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:12.150 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:12.150 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:12.150 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:12.150 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@721fda59, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2cb9cc85, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1d0c7a03, containsSubquery=false)
13:57:12.150 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:12.662 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:12.663 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:12.663 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:12.663 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:12.663 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2a201aa9, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5402b18c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@670738b2, containsSubquery=false)
13:57:12.663 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:12.687 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:12.689 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:12.689 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:12.689 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:12.689 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:12.689 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3d6fe206, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@63f38fb1, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4d5d5473, containsSubquery=false)
13:57:12.689 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:13.463 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:13.463 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:13.463 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:13.463 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:13.463 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@95e8df8, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2e07ae6c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@fbafe38, containsSubquery=false)
13:57:13.463 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:13.484 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:13.486 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:13.486 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:13.486 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:13.486 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:13.486 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6fe40fa8, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@37f76752, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@48f7f8cc, containsSubquery=false)
13:57:13.486 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:14.002 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:14.002 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:14.002 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:14.002 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:14.002 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6a56a99a, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1d900d84, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3e13f2c8, containsSubquery=false)
13:57:14.002 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:14.028 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:14.030 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:14.030 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:14.030 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:14.030 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:14.030 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2685c3a2, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7a1f008, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@68bffb06, containsSubquery=false)
13:57:14.030 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:14.781 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:14.781 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:14.781 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:14.781 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:14.781 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7352cf80, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@64a27d27, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4f407424, containsSubquery=false)
13:57:14.781 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:14.803 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:14.805 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:14.805 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:14.805 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:14.805 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:14.805 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7ce78666, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@304a2c8a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5d98f014, containsSubquery=false)
13:57:14.805 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:15.342 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:15.342 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:15.342 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:15.342 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:15.342 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2b04b29d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3106efb9, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@477aa172, containsSubquery=false)
13:57:15.342 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:15.362 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:15.365 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:15.365 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:15.365 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:15.365 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:15.365 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5a0ec54c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@486d9e4e, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@702e8e67, containsSubquery=false)
13:57:15.365 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:15.887 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:15.887 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:15.887 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:15.887 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:15.887 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@383cf604, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3c650ad2, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@74bcff71, containsSubquery=false)
13:57:15.887 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:15.912 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:57:15.914 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
13:57:15.914 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:15.914 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:15.914 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:15.914 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@446717fb, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@1fce8b06, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@c1aa8f9, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@bbccb5f, containsSubquery=false)
13:57:15.914 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
13:57:16.635 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:<==      Total: 96525
13:57:16.635 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
13:57:16.636 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
13:57:16.636 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
13:57:16.636 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:16.636 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@1c8a2bec, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7b779c7b, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@16fc5679, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2b465287, containsSubquery=false)
13:57:16.636 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
13:57:16.658 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
13:59:52.722 [NettyClientSelector_1] INFO  RocketmqRemoting - [TID: N/A] - message:closeChannel: close the connection to remote address[************:9876] result: true
13:59:52.997 [NettyClientSelector_1] INFO  RocketmqRemoting - [TID: N/A] - message:closeChannel: close the connection to remote address[************:9876] result: true
14:55:43.073 [NettyClientSelector_1] INFO  RocketmqRemoting - [TID: N/A] - message:closeChannel: close the connection to remote address[************:10911] result: true
14:55:43.073 [NettyClientSelector_1] INFO  RocketmqRemoting - [TID: N/A] - message:closeChannel: close the connection to remote address[************:9876] result: true
14:55:43.073 [NettyClientSelector_1] INFO  RocketmqRemoting - [TID: N/A] - message:closeChannel: close the connection to remote address[************:9876] result: true
14:55:43.076 [NettyClientSelector_1] INFO  RocketmqRemoting - [TID: N/A] - message:closeChannel: close the connection to remote address[************:10911] result: true
14:55:52.222 [main] INFO  c.i.o.y.YieldSpreadApplication - [TID: N/A] - message:The following profiles are active: dev
14:55:53.162 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - [TID: N/A] - message:Skipping MapperFactoryBean with name 'pgYieldSpreadCompositeSearchMapper' and 'com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgYieldSpreadCompositeSearchMapper' mapperInterface. Bean already defined with the same name!
14:55:53.163 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - [TID: N/A] - message:Skipping MapperFactoryBean with name 'yieldSpreadCompositeSearchMapper' and 'com.innodealing.onshore.yieldspread.mapper.yieldspread.YieldSpreadCompositeSearchMapper' mapperInterface. Bean already defined with the same name!
14:55:53.163 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - [TID: N/A] - message:No MyBatis mapper was found in '[com.innodealing.onshore.yieldspread]' package. Please check your configuration.
14:55:53.214 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [TID: N/A] - message:Multiple Spring Data modules found, entering strict repository configuration mode!
14:55:53.216 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [TID: N/A] - message:Bootstrapping Spring Data Redis repositories in DEFAULT mode.
14:55:53.283 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [TID: N/A] - message:Finished Spring Data repository scanning in 58ms. Found 0 Redis repository interfaces.
14:55:53.445 [main] INFO  o.s.cloud.context.scope.GenericScope - [TID: N/A] - message:BeanFactory id=1ae4855d-8510-3a45-bd9e-969de7a6a1a6
14:55:53.618 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.AreaService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.620 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondFinanceService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.621 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondInfoService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondPriceApolloService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.623 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondPriceService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.623 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.BondRatingService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.624 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.ComService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.625 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.FinancialInstitutionService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.626 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.HolidayService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.627 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.InternalKeyValueService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.628 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.UdicInfoService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:53.628 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [TID: N/A] - message:Bean 'com.innodealing.onshore.yieldspread.service.internal.UserService' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
14:55:54.100 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - [TID: N/A] - message:Tomcat initialized with port(s): 8080 (http)
14:55:54.115 [main] INFO  o.a.coyote.http11.Http11NioProtocol - [TID: N/A] - message:Initializing ProtocolHandler ["http-nio-8080"]
14:55:54.115 [main] INFO  o.a.catalina.core.StandardService - [TID: N/A] - message:Starting service [Tomcat]
14:55:54.115 [main] INFO  o.a.catalina.core.StandardEngine - [TID: N/A] - message:Starting Servlet engine: [Apache Tomcat/9.0.31]
14:55:54.275 [main] INFO  o.a.c.c.C.[.[.[/onshore-yield-spread] - [TID: N/A] - message:Initializing Spring embedded WebApplicationContext
14:55:54.275 [main] INFO  o.s.web.context.ContextLoader - [TID: N/A] - message:Root WebApplicationContext: initialization completed in 2040 ms
14:55:54.687 [main] INFO  c.i.o.y.c.d.YieldSpreadDataSourceConfig$$EnhancerBySpringCGLIB$$b7a30638 - [TID: N/A] - message:[创建分片数据源:null]
14:55:55.922 [main] INFO  c.alibaba.druid.pool.DruidDataSource - [TID: N/A] - message:{dataSource-1} inited
14:55:56.147 [main] INFO  o.a.s.c.c.log.ConfigurationLogger - [TID: N/A] - message:ShardingRuleConfiguration
tables:
  udic_area_yield_spread:
    actualDataNodes: yield_spread.udic_area_yield_spread
    logicTable: udic_area_yield_spread
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.StrParamHintShardingAlgorithm
  indu_bond_yield_spread:
    actualDataNodes: yield_spread.indu_bond_yield_spread
    logicTable: indu_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  udic_bond_yield_spread:
    actualDataNodes: yield_spread.udic_bond_yield_spread
    logicTable: udic_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.CaliberYearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.CaliberYearTableRangeShardingAlgorithm
        shardingColumn: id
  secu_bond_yield_spread:
    actualDataNodes: yield_spread.indu_bond_yield_spread
    logicTable: secu_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  lg_bond_yield_spread:
    actualDataNodes: yield_spread.indu_bond_yield_spread
    logicTable: lg_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  bank_bond_yield_spread:
    actualDataNodes: yield_spread.indu_bond_yield_spread
    logicTable: bank_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  insu_bond_yield_spread:
    actualDataNodes: yield_spread.insu_bond_yield_spread
    logicTable: insu_bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id
  bond_yield_spread:
    actualDataNodes: yield_spread.bond_yield_spread
    logicTable: bond_yield_spread
    tableStrategy:
      standard:
        preciseAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm
        rangeAlgorithmClassName: com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm
        shardingColumn: id

14:55:56.149 [main] INFO  o.a.s.c.c.log.ConfigurationLogger - [TID: N/A] - message:Properties
sql.show: 'true'

14:55:56.175 [main] INFO  o.a.s.c.e.m.TableMetaDataInitializer - [TID: N/A] - message:TableMetaDataInitializer => loading...
14:55:56.209 [main] INFO  o.a.s.c.e.m.TableMetaDataInitializer - [TID: N/A] - message:TableMetaDataInitializer => load success result：8 StopWatch '': running time = 34088100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
034054400  100%  loadShardingTables
000033700  000%  loadDefaultTables

14:55:56.822 [main] INFO  org.redisson.Version - [TID: N/A] - message:Redisson 3.21.3
14:55:57.183 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - [TID: N/A] - message:1 connections initialized for 192.168.9.153/192.168.9.153:6379
14:55:57.294 [redisson-netty-2-19] INFO  o.r.c.pool.MasterConnectionPool - [TID: N/A] - message:24 connections initialized for 192.168.9.153/192.168.9.153:6379
14:55:58.303 [main] INFO  c.alibaba.druid.pool.DruidDataSource - [TID: N/A] - message:{dataSource-2} inited
14:55:59.094 [main] INFO  c.alibaba.druid.pool.DruidDataSource - [TID: N/A] - message:{dataSource-3} inited
14:55:59.650 [main] INFO  c.i.o.y.c.d.PgShardingYieldSpreadDataSourceConfig$$EnhancerBySpringCGLIB$$4f5015fd - [TID: N/A] - message:[创建分片数据源:***************************************************************************************************************************************]
14:55:59.708 [main] INFO  o.a.s.c.c.log.ConfigurationLogger - [TID: N/A] - message:ShardingRuleConfiguration
tables:
  indu_curve:
    actualDataNodes: yield_spread.indu_curve
    logicTable: indu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  udic_curve:
    actualDataNodes: yield_spread.udic_curve
    logicTable: udic_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  secu_curve:
    actualDataNodes: yield_spread.secu_curve
    logicTable: secu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  bank_curve:
    actualDataNodes: yield_spread.bank_curve
    logicTable: bank_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  insu_curve:
    actualDataNodes: yield_spread.insu_curve
    logicTable: insu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_indu_curve:
    actualDataNodes: yield_spread.mv_indu_curve
    logicTable: mv_indu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_udic_curve:
    actualDataNodes: yield_spread.mv_udic_curve
    logicTable: mv_udic_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_secu_curve:
    actualDataNodes: yield_spread.mv_secu_curve
    logicTable: mv_secu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_bank_curve:
    actualDataNodes: yield_spread.mv_bank_curve
    logicTable: mv_bank_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm
  mv_insu_curve:
    actualDataNodes: yield_spread.mv_insu_curve
    logicTable: mv_insu_curve
    tableStrategy:
      hint:
        algorithmClassName: com.innodealing.onshore.yieldspread.config.shardingsphere.RatingTableHintShardingAlgorithm

14:55:59.709 [main] INFO  o.a.s.c.c.log.ConfigurationLogger - [TID: N/A] - message:Properties
{}

14:55:59.834 [main] INFO  o.a.s.c.e.m.TableMetaDataInitializer - [TID: N/A] - message:TableMetaDataInitializer => loading...
14:55:59.917 [main] INFO  o.a.s.c.e.m.TableMetaDataInitializer - [TID: N/A] - message:TableMetaDataInitializer => load success result：10 StopWatch '': running time = ******** ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
*********  100%  loadShardingTables
*********  000%  loadDefaultTables

14:56:01.891 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:01.910 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:02.387 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:02.387 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:02.387 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@73e505d5, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1e4cf0e5, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7908e69e, containsSubquery=false)
14:56:02.387 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:02.857 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:02.857 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:02.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:02.890 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:02.890 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@219505d9, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@24fe3184, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@113d9226, containsSubquery=false)
14:56:02.890 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:02.913 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:02.919 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:02.919 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:02.919 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:02.919 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:02.919 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7e0a7b9e, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1936513a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@23cf86c0, containsSubquery=false)
14:56:02.919 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:03.495 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:03.495 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:03.495 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:03.495 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:03.495 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4c59c76c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@137fb1a1, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6cbd57fa, containsSubquery=false)
14:56:03.495 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:03.517 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:03.519 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:03.519 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:03.519 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:03.519 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:03.519 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@82da5c9, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@e2931b1, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@634fa039, containsSubquery=false)
14:56:03.519 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:05.785 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:05.785 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:05.786 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:05.786 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:05.786 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2882a80d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@36d28b4c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6d919a4, containsSubquery=false)
14:56:05.786 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:05.851 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:05.855 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:05.855 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:05.856 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:05.856 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:05.856 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7b48b933, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@35929bc7, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3ffa007c, containsSubquery=false)
14:56:05.856 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:07.302 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:07.303 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:07.304 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:07.304 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:07.304 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6f88319b, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1e25f6, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@232e1f66, containsSubquery=false)
14:56:07.304 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:07.327 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:07.331 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:07.331 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:07.332 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:07.332 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:07.332 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@1cf8d43b, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@4ffe2ded, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@187c011d, containsSubquery=false)
14:56:07.332 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:07.688 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:07.688 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:07.688 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:07.688 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:07.688 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@52294ab7, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2a25dd06, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@323b4e2a, containsSubquery=false)
14:56:07.688 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:07.711 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:07.714 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:07.715 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:07.715 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:07.715 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:07.715 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@76d404ab, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1ef64e45, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@39e08d73, containsSubquery=false)
14:56:07.715 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:07.980 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:07.980 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:07.980 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:07.980 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:07.980 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5839306e, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@650fb50f, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@748321c5, containsSubquery=false)
14:56:07.981 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:08.014 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:08.018 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:08.018 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:08.019 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:08.019 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:08.019 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@71a7e67, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@6afafa48, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@13e880b5, containsSubquery=false)
14:56:08.019 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:08.324 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:08.324 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:08.325 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:08.325 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:08.325 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4d682552, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1ad2e962, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3e26080c, containsSubquery=false)
14:56:08.325 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:08.353 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:08.356 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:08.356 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:08.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:08.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:08.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@503f5382, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5710fca8, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7e2e1179, containsSubquery=false)
14:56:08.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:08.912 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:08.913 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:08.913 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:08.914 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:08.914 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@35e92c45, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1f5d72f, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@198cf325, containsSubquery=false)
14:56:08.914 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:08.936 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:08.939 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:08.939 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:08.939 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:08.939 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:08.939 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3dd1f392, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@4faaf13a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@514ee14c, containsSubquery=false)
14:56:08.939 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:09.747 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:09.747 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:09.747 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:09.747 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:09.747 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@de61005, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2b84d474, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1d458c1e, containsSubquery=false)
14:56:09.747 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:09.772 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:09.774 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:09.774 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:09.774 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:09.774 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:09.774 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5011753d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@6105265d, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2fb90f1, containsSubquery=false)
14:56:09.775 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:10.353 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:10.353 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:10.353 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:10.353 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:10.353 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@1534fd96, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@4b969bd9, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@caf0e10, containsSubquery=false)
14:56:10.353 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:10.374 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:10.376 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:10.376 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:10.376 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:10.376 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:10.376 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@44c1ce5a, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@306a875c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5bafd971, containsSubquery=false)
14:56:10.376 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:10.889 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:10.889 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:10.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:10.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:10.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@788c0028, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5dcd3a19, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7ce2bfb, containsSubquery=false)
14:56:10.889 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:10.918 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:10.920 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:10.920 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:10.920 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:10.920 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:10.920 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@76339283, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@d4fe4d, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@27e4ca66, containsSubquery=false)
14:56:10.920 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:11.653 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:11.653 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:11.654 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:11.654 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:11.654 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7b0ad007, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7abc82fa, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7e7a6f81, containsSubquery=false)
14:56:11.654 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:11.678 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:11.681 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:11.681 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:11.682 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:11.682 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:11.682 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4a3e3ce3, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@67954442, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7bb5c602, containsSubquery=false)
14:56:11.682 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:12.190 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:12.190 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:12.190 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:12.190 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:12.190 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7d2aa0c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5282f922, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@76f18f68, containsSubquery=false)
14:56:12.190 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:12.214 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:12.217 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:12.217 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:12.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:12.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:12.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@29cd7b9e, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@53bd2b00, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@319267ad, containsSubquery=false)
14:56:12.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:12.992 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:12.992 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:12.992 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:12.993 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:12.993 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3b89f41a, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@198453c9, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@772ccecf, containsSubquery=false)
14:56:12.993 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:13.019 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:13.021 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:13.021 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:13.021 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:13.021 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:13.021 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@8662634, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@19adcb41, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@f03dc9b, containsSubquery=false)
14:56:13.021 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:13.543 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:13.543 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:13.543 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:13.543 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:13.543 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4baca539, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5ada5fd3, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4d36c8cb, containsSubquery=false)
14:56:13.543 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:13.568 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:13.572 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:13.572 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:13.572 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:13.572 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:13.572 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2bb5637a, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1decf3e3, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@722b4f64, containsSubquery=false)
14:56:13.572 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:14.087 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:14.087 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:14.088 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:14.088 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:14.088 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2613a6fc, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7d6e3e42, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@103388c6, containsSubquery=false)
14:56:14.088 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:14.109 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:14.112 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:14.112 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:14.112 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:14.112 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:14.112 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@232dda63, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2f541378, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2b1cf779, containsSubquery=false)
14:56:14.112 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:14.912 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:14.912 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:14.913 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:14.913 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:14.913 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2df9b4f3, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@6112390a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5ac92082, containsSubquery=false)
14:56:14.913 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:14.937 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:14.942 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:14.942 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:14.942 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:14.942 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:14.942 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@256bfbbb, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@363502a4, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7ab66d54, containsSubquery=false)
14:56:14.942 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:15.444 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:15.444 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:15.445 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:15.445 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:15.445 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@41c96a67, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@8dc73c7, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@728c3a0e, containsSubquery=false)
14:56:15.445 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:15.469 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:15.472 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:15.472 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:15.472 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:15.472 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:15.472 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@773e8030, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@64718893, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@485c8d5e, containsSubquery=false)
14:56:15.472 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:16.016 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:16.016 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:16.016 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:16.016 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:16.016 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3db32d5, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2f41798d, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1602b644, containsSubquery=false)
14:56:16.016 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:16.042 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:16.045 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:16.045 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:16.045 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:16.045 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:16.045 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@33b09909, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3003b9ad, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@b7a74ac, containsSubquery=false)
14:56:16.046 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:16.816 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:16.816 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:16.816 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:16.816 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:16.816 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4e2bd0df, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@162c02a2, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@79c0e385, containsSubquery=false)
14:56:16.816 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:16.839 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:16.842 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:16.842 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:16.842 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:16.842 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:16.842 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3a410524, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@44e31522, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@55a7c43, containsSubquery=false)
14:56:16.842 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:17.365 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:17.365 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:17.366 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:17.366 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:17.366 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@561dc11f, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7317fb, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@36bf28d2, containsSubquery=false)
14:56:17.366 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:17.387 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:17.389 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:17.390 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:17.390 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:17.390 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:17.390 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@40e0213c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@fd6784a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@272ee84a, containsSubquery=false)
14:56:17.390 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:18.138 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:18.138 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:18.138 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:18.139 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:18.139 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@232b4552, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@739785c5, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@77f76656, containsSubquery=false)
14:56:18.139 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:18.160 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:18.162 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:18.162 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:18.163 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:18.163 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:18.163 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@452888c2, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@9a183e7, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@487e4b06, containsSubquery=false)
14:56:18.163 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:18.677 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:18.677 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:18.678 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:18.678 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:18.678 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@32e58546, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@879ce67, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3546439b, containsSubquery=false)
14:56:18.678 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:18.698 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:18.700 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:18.700 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:18.701 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:18.701 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:18.701 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2ef1b7f4, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@29ac632a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3e45ca81, containsSubquery=false)
14:56:18.701 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:19.207 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:19.208 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:19.208 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:19.208 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:19.208 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@55db4eea, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@45da9712, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@354880a9, containsSubquery=false)
14:56:19.208 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:19.232 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:19.235 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:19.235 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:19.235 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:19.235 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:19.235 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@125dcfe1, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@63e148cb, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@747bf7a, containsSubquery=false)
14:56:19.235 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:20.089 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:20.089 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:20.089 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:20.089 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:20.089 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@645b2ac7, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5e5df559, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@7481775c, containsSubquery=false)
14:56:20.089 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:20.116 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:20.121 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:20.122 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:20.122 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:20.122 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:20.122 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@59a6a709, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@266200f6, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@70ccf921, containsSubquery=false)
14:56:20.122 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:21.197 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:21.197 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:21.197 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:21.197 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:21.197 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@41e5450f, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5677b9e3, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6a6e6278, containsSubquery=false)
14:56:21.197 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:21.238 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:21.246 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:21.246 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:21.246 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:21.246 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:21.246 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@291b3e68, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@307fe491, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6f9de548, containsSubquery=false)
14:56:21.247 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:22.152 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:22.152 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:22.152 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:22.152 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:22.152 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@53a15397, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@53964b0e, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@50db0b17, containsSubquery=false)
14:56:22.152 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:22.175 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:22.178 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:22.179 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:22.179 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:22.179 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:22.179 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5fada492, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@57268fc3, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4f4a0e4, containsSubquery=false)
14:56:22.179 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:22.568 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:22.569 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:22.569 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:22.569 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:22.569 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@79e98bd6, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@58bbe78f, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3f4e090c, containsSubquery=false)
14:56:22.569 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:22.591 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:22.594 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:22.595 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:22.595 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:22.595 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:22.595 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5a8f777, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@4a6198a7, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2e06a7e4, containsSubquery=false)
14:56:22.595 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:22.907 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:22.908 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:22.908 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:22.908 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:22.908 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6fa1dc0c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@74037f9b, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@b0dc426, containsSubquery=false)
14:56:22.908 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:22.930 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:22.934 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:22.934 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:22.934 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:22.934 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:22.934 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@32200389, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@57e9dd1f, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6e1134e1, containsSubquery=false)
14:56:22.935 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:23.233 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:23.234 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:23.234 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:23.234 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:23.234 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@1a6804d1, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7db99d41, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3e0fe68f, containsSubquery=false)
14:56:23.234 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:23.260 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:23.263 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:23.263 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:23.263 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:23.263 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:23.263 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@578f8288, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@16f52d68, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6616b9e0, containsSubquery=false)
14:56:23.263 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:23.788 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:23.788 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:23.788 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:23.788 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:23.788 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7e22f3da, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2d288c47, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5d8c9945, containsSubquery=false)
14:56:23.788 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:23.814 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:23.816 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:23.817 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:23.817 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:23.817 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:23.817 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@4e74adb, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3d18bdb3, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@30336205, containsSubquery=false)
14:56:23.817 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:24.635 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:24.635 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:24.635 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:24.635 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:24.635 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3e71b1a4, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1579cf25, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@77459635, containsSubquery=false)
14:56:24.635 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:24.660 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:24.663 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:24.663 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:24.663 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:24.663 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:24.663 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3ac726af, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@61e8fbfc, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2ea55742, containsSubquery=false)
14:56:24.663 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:25.192 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:25.192 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:25.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:25.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:25.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@cb4999d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@8afd00e, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1bd7bdf, containsSubquery=false)
14:56:25.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:25.215 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:25.217 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:25.217 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:25.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:25.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:25.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@58178c5e, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@329e6700, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@440e28fa, containsSubquery=false)
14:56:25.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:25.757 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:25.757 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:25.757 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:25.757 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:25.757 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3fd9c5db, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@a256322, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@6860341b, containsSubquery=false)
14:56:25.757 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:25.778 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:25.781 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:25.781 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:25.781 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:25.781 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:25.781 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3610b408, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5fb99e3d, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@61f18402, containsSubquery=false)
14:56:25.781 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:26.331 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:26.331 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:26.332 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:26.332 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:26.332 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@39911d6a, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@54ad5fce, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@63b187f, containsSubquery=false)
14:56:26.332 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:26.354 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:26.356 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:26.356 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:26.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:26.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:26.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@721fda59, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2cb9cc85, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@1d0c7a03, containsSubquery=false)
14:56:26.356 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:26.994 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:26.995 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:26.995 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:26.995 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:26.995 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2a201aa9, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5402b18c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@670738b2, containsSubquery=false)
14:56:26.995 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:27.021 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:27.024 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:27.024 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:27.024 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:27.024 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:27.024 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3d6fe206, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@63f38fb1, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4d5d5473, containsSubquery=false)
14:56:27.024 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:27.760 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:27.760 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:27.761 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:27.761 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:27.761 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@95e8df8, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2e07ae6c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@fbafe38, containsSubquery=false)
14:56:27.761 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:27.792 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:27.797 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:27.797 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:27.797 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:27.797 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:27.797 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6fe40fa8, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@37f76752, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@48f7f8cc, containsSubquery=false)
14:56:27.797 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:29.156 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:29.157 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:29.157 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:29.157 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:29.157 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@6a56a99a, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@1d900d84, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@3e13f2c8, containsSubquery=false)
14:56:29.157 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:29.226 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:29.232 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:29.232 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:29.233 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:29.233 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:29.233 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2685c3a2, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7a1f008, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@68bffb06, containsSubquery=false)
14:56:29.233 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:29.682 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:29.682 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:29.682 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:29.682 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:29.682 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7352cf80, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@64a27d27, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@4f407424, containsSubquery=false)
14:56:29.682 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:29.706 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:29.710 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:29.710 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:29.710 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:29.710 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:29.710 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7ce78666, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@304a2c8a, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5d98f014, containsSubquery=false)
14:56:29.710 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:29.987 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:29.987 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:29.987 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:29.987 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:29.987 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2b04b29d, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3106efb9, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@477aa172, containsSubquery=false)
14:56:29.987 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:30.010 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:30.013 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:30.013 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:30.013 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:30.013 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:30.013 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@5a0ec54c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@486d9e4e, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@702e8e67, containsSubquery=false)
14:56:30.013 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:30.281 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:30.281 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:30.281 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:30.281 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:30.282 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@383cf604, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3c650ad2, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@74bcff71, containsSubquery=false)
14:56:30.282 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:30.304 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:30.308 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:30.308 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:30.308 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:30.308 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:30.308 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@1fce8b06, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@c1aa8f9, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@bbccb5f, containsSubquery=false)
14:56:30.308 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:31.709 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:31.709 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:31.709 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:31.709 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:31.709 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@7b779c7b, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@16fc5679, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2b465287, containsSubquery=false)
14:56:31.709 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:31.731 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:31.733 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:31.733 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:31.734 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:31.734 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:31.734 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@27cfc534, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@3e046920, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@14e9458a, containsSubquery=false)
14:56:31.734 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:31.984 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:31.984 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:31.984 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:31.984 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:31.984 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@23f41ce3, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7d6fc802, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@47c89a33, containsSubquery=false)
14:56:31.986 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:32.014 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:32.018 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:32.018 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:32.020 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:32.020 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:32.020 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@58b472b4, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@5b2e7293, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@472d3c57, containsSubquery=false)
14:56:32.020 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:33.618 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:33.618 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:33.618 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:33.618 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:33.618 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@b9286ad, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2573cc0c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@2cf62699, containsSubquery=false)
14:56:33.618 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:33.642 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:33.645 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:33.645 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:33.645 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:33.645 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:33.645 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@2dd631c5, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@30090808, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@f562214, containsSubquery=false)
14:56:33.645 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:33.916 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:33.916 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:33.916 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:33.916 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:33.916 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@95aa79c, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@37a26221, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@25bb5bf9, containsSubquery=false)
14:56:33.916 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:33.939 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:33.941 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:33.942 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:33.942 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:33.942 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:33.942 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@71962018, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@e0dc940, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5979d0a5, containsSubquery=false)
14:56:33.942 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:34.192 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:34.192 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:34.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:34.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:34.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@23ccdee9, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@34a7decd, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5e04bfe5, containsSubquery=false)
14:56:34.192 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:34.214 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:34.216 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:34.216 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:34.216 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:34.216 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:34.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@8936d23, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@73131043, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@5af2bc6, containsSubquery=false)
14:56:34.217 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:34.712 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:34.712 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:34.713 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:34.713 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:34.713 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@3be22500, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@476fc8a1, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@ddf8b1d, containsSubquery=false)
14:56:34.713 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:34.736 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:34.738 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:34.738 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:34.738 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:34.738 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:34.738 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@b13c600, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@7ae5231c, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@642bfa5e, containsSubquery=false)
14:56:34.738 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:35.261 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:35.261 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:35.261 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:35.261 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:35.261 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@11514c77, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@2a93ad09, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@31ef9ac6, containsSubquery=false)
14:56:35.261 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:35.284 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
14:56:35.286 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code FROM yield_spread_bond 
14:56:35.286 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:35.286 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:35.286 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:35.286 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@6ef0a044, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=12, stopIndex=165, distinctRow=false, projections=[ColumnProjection(owner=null, name=com_spread_sector, alias=Optional.of(com_spread_sector)), ColumnProjection(owner=null, name=deleted, alias=Optional.of(deleted)), ColumnProjection(owner=null, name=bond_uni_code, alias=Optional.of(bond_uni_code)), ColumnProjection(owner=null, name=id, alias=Optional.of(id)), ColumnProjection(owner=null, name=com_uni_code, alias=Optional.of(com_uni_code)), ColumnProjection(owner=null, name=bond_code, alias=Optional.of(bond_code))], columnLabels=[com_spread_sector, deleted, bond_uni_code, id, com_uni_code, bond_code]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@249b2dd4, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@696faa2b, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@16d869de, containsSubquery=false)
14:56:35.286 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT      com_spread_sector AS com_spread_sector, deleted AS deleted, bond_uni_code AS bond_uni_code, id AS id, com_uni_code AS com_uni_code, bond_code AS bond_code  FROM yield_spread_bond
14:56:36.055 [main] DEBUG c.i.o.y.m.y.Y.selectRowBoundsByDynamicQuery - [TID: N/A] - message:<==      Total: 96525
14:56:36.055 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==>  Preparing: SELECT COUNT( id ) FROM yield_spread_bond 
14:56:36.055 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:==> Parameters: 
14:56:36.055 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Rule Type: sharding
14:56:36.055 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Logic SQL: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:36.055 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:SQLStatement: SelectSQLStatementContext(super=CommonSQLStatementContext(sqlStatement=org.apache.shardingsphere.sql.parser.sql.statement.dml.SelectStatement@10e7192, tablesContext=TablesContext(tables=[Table(name=yield_spread_bond, alias=Optional.absent())], schema=Optional.absent())), projectionsContext=ProjectionsContext(startIndex=10, stopIndex=22, distinctRow=false, projections=[AggregationProjection(type=COUNT, innerExpression=(  id  ), alias=Optional.absent(), derivedAggregationProjections=[], index=-1)], columnLabels=[COUNT(  id  )]), groupByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.groupby.GroupByContext@550d3cd7, orderByContext=org.apache.shardingsphere.sql.parser.relation.segment.select.orderby.OrderByContext@4e3931, paginationContext=org.apache.shardingsphere.sql.parser.relation.segment.select.pagination.PaginationContext@409e0e69, containsSubquery=false)
14:56:36.055 [main] INFO  ShardingSphere-SQL - [TID: N/A] - message:Actual SQL: yield_spread ::: SELECT    COUNT(  id  )  FROM yield_spread_bond
14:56:36.076 [main] DEBUG c.i.o.y.m.y.Y.selectCountByDynamicQuery - [TID: N/A] - message:<==      Total: 1
