package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvBankBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvBankComYieldSpreadCurveDO;

/**
 * 银行利差曲线物化视图
 *
 * <AUTHOR>
 */
public interface MvBankComYieldSpreadCurveMapper extends PgBaseMapper<MvBankBondYieldSpreadCurveParameter>, DynamicQueryMapper<MvBankComYieldSpreadCurveDO> {


}
