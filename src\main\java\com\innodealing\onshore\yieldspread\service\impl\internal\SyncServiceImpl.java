package com.innodealing.onshore.yieldspread.service.impl.internal;

import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.AssertUtils;
import com.innodealing.onshore.yieldspread.dao.yieldspread.*;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.helper.LocalCache;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.*;
import com.innodealing.onshore.yieldspread.service.internal.SyncService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 同步数据
 *
 * <AUTHOR>
 */
@Service
public class SyncServiceImpl implements SyncService {

    @Resource
    private UdicBondYieldSpreadDAO udicBondYieldSpreadDAO;

    @Resource
    private InduBondYieldSpreadDAO induBondYieldSpreadDAO;

    @Resource
    private BankBondYieldSpreadDAO bankBondYieldSpreadDAO;

    @Resource
    private SecuBondYieldSpreadDAO secuBondYieldSpreadDAO;

    private InsuBondYieldSpreadDAO insuBondYieldSpreadDAO;

    @Resource
    private YieldSpreadBondDAO yieldSpreadBondDAO;

    @Resource
    private LocalCache localCache;

    @Resource
    private RedissonClient redissonClient;

    private static final int BATCH_INSERT_SIZE = 1000;

    private static final String INTERNAL_KEY = "onshore-yield-spread:sync-all-yield-spread-bond";

    @Override
    public int syncAllYieldSpreadBond() {
        RLock lock = redissonClient.getLock(INTERNAL_KEY);
        AssertUtils.isTrue(lock.tryLock(), new BusinessException("正在同步所有利差债券"));
        try {
            int total = 0;
            total += syncUdicBonds();
            total += syncInduBonds();
            total += syncBankBonds();
            total += syncSecuBonds();
            total += syncInsuBonds();
            return total;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void refreshYieldSpreadBondLocalCache() {
        localCache.cacheYieldSpreadBonds();
    }

    private int syncUdicBonds() {
        int num = 0;
        List<UdicBondYieldSpreadGroupDO> udicBonds = udicBondYieldSpreadDAO.listBonds();
        List<YieldSpreadBondDO> insertList = new ArrayList<>();
        for (UdicBondYieldSpreadGroupDO udicBondYieldSpreadGroupDO : udicBonds) {
            YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(udicBondYieldSpreadGroupDO, YieldSpreadBondDO.class);
            yieldSpreadBondDO.setDeleted(Deleted.NO_DELETED.getValue());
            yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.UDIC.getValue());
            insertList.add(yieldSpreadBondDO);
        }
        List<List<YieldSpreadBondDO>> partition = Lists.partition(insertList, BATCH_INSERT_SIZE);
        for (List<YieldSpreadBondDO> bonds : partition) {
            num += yieldSpreadBondDAO.saveYieldSpreadBonds(bonds);
        }
        return num;
    }

    private int syncInduBonds() {
        int num = 0;
        List<InduBondYieldSpreadGroupDO> induBonds = induBondYieldSpreadDAO.listBonds();
        List<YieldSpreadBondDO> insertList = new ArrayList<>();
        for (InduBondYieldSpreadGroupDO induBondYieldSpreadGroupDO : induBonds) {
            YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(induBondYieldSpreadGroupDO, YieldSpreadBondDO.class);
            yieldSpreadBondDO.setDeleted(Deleted.NO_DELETED.getValue());
            yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.INDU.getValue());
            insertList.add(yieldSpreadBondDO);
        }
        List<List<YieldSpreadBondDO>> partition = Lists.partition(insertList, BATCH_INSERT_SIZE);
        for (List<YieldSpreadBondDO> bonds : partition) {
            num += yieldSpreadBondDAO.saveYieldSpreadBonds(bonds);
        }
        return num;
    }

    private int syncBankBonds() {
        int num = 0;
        List<BankBondYieldSpreadGroupDO> bankBonds = bankBondYieldSpreadDAO.listBonds();
        List<YieldSpreadBondDO> insertList = new ArrayList<>();
        for (BankBondYieldSpreadGroupDO bankBondYieldSpreadGroupDO : bankBonds) {
            YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(bankBondYieldSpreadGroupDO, YieldSpreadBondDO.class);
            yieldSpreadBondDO.setDeleted(Deleted.NO_DELETED.getValue());
            yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.BANK.getValue());
            insertList.add(yieldSpreadBondDO);
        }
        List<List<YieldSpreadBondDO>> partition = Lists.partition(insertList, BATCH_INSERT_SIZE);
        for (List<YieldSpreadBondDO> bonds : partition) {
            num += yieldSpreadBondDAO.saveYieldSpreadBonds(bonds);
        }
        return num;
    }

    private int syncSecuBonds() {
        int num = 0;
        List<SecuBondYieldSpreadGroupDO> secuBonds = secuBondYieldSpreadDAO.listBonds();
        List<YieldSpreadBondDO> insertList = new ArrayList<>();
        for (SecuBondYieldSpreadGroupDO secuBondYieldSpreadGroupDO : secuBonds) {
            YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(secuBondYieldSpreadGroupDO, YieldSpreadBondDO.class);
            yieldSpreadBondDO.setDeleted(Deleted.NO_DELETED.getValue());
            yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.SECU.getValue());
            insertList.add(yieldSpreadBondDO);
        }
        List<List<YieldSpreadBondDO>> partition = Lists.partition(insertList, BATCH_INSERT_SIZE);
        for (List<YieldSpreadBondDO> bonds : partition) {
            num += yieldSpreadBondDAO.saveYieldSpreadBonds(bonds);
        }
        return num;
    }

    private int syncInsuBonds() {
        int num = 0;
        List<InsuBondYieldSpreadGroupDO> insuBonds = insuBondYieldSpreadDAO.listBonds();
        List<YieldSpreadBondDO> insertList = new ArrayList<>();
        for (InsuBondYieldSpreadGroupDO insuBondYieldSpreadGroupDO : insuBonds) {
            YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(insuBondYieldSpreadGroupDO, YieldSpreadBondDO.class);
            yieldSpreadBondDO.setDeleted(Deleted.NO_DELETED.getValue());
            yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.INSU.getValue());
            insertList.add(yieldSpreadBondDO);
        }
        List<List<YieldSpreadBondDO>> partition = Lists.partition(insertList, BATCH_INSERT_SIZE);
        for (List<YieldSpreadBondDO> bonds : partition) {
            num += yieldSpreadBondDAO.saveYieldSpreadBonds(bonds);
        }
        return num;
    }

}
