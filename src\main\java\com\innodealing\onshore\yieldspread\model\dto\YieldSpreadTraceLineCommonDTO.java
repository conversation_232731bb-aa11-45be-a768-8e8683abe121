package com.innodealing.onshore.yieldspread.model.dto;


import io.swagger.annotations.ApiModelProperty;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 利差追踪折线图
 *
 * <AUTHOR>
 */
public class YieldSpreadTraceLineCommonDTO {

    @ApiModelProperty("日期")
    private Date[] issueDates;

    @ApiModelProperty("数据对象列表")
    private List<YieldSpreadTraceLineDataDTO> dataDTOLists;

    public Date[] getIssueDates() {
        return Objects.isNull(issueDates) ? new Date[0] : issueDates.clone();
    }

    public void setIssueDates(Date[] issueDates) {
        this.issueDates = Objects.isNull(issueDates) ? new Date[0] : issueDates.clone();
    }

    public List<YieldSpreadTraceLineDataDTO> getDataDTOLists() {
        return Objects.isNull(dataDTOLists) ? new ArrayList<>() : new ArrayList<>(dataDTOLists);
    }

    public void setDataDTOLists(List<YieldSpreadTraceLineDataDTO> dataDTOLists) {
        this.dataDTOLists = Objects.isNull(dataDTOLists) ? new ArrayList<>() : new ArrayList<>(dataDTOLists);
    }
}
