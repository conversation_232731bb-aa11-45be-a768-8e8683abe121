package com.innodealing.onshore.yieldspread.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.innodealing.onshore.yieldspread.helper.ExcelStyleUtils;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.DynCurveExportExcelDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.FixCurveExportExcelDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 导出 Controller
 * 通常而言只需要继承该类,就可以简单的实现导出excel操作了
 * <p>
 * swagger需要加入produces
 * '@ApiOperation(value = "获取excel", produces = "application/octet-stream")'
 * </p>
 *
 * <AUTHOR>
 */
public class ExportController {

    @Resource
    protected HttpServletResponse response;

    private static final List<String> ILLEGAL_NAME = Arrays.asList("?", "*", ":", "/", "\\", "|", ">", "<");

    private static final String UTF_8 = "UTF-8";

    private static final String FILE_SUFFIX_XLSX = ".xlsx";

    private static final String DEFAULT_SHEET = "sheet1";

    private static final int DEFAULT_COLUMN_WIDTH = 30;

    /**
     * export excel
     *
     * @param fileName  excel file name
     * @param sheetName sheet名
     * @param list      list
     * @param <T>       T
     * @throws IOException ex
     */

    protected <T> void exportExcel(String fileName, String sheetName, List<T> list, Class<T> headClass) throws IOException {
        for (String s : ILLEGAL_NAME) {
            fileName = fileName.replace(s, StringUtils.EMPTY);
        }
        this.doExport(fileName, list, EasyExcel.writerSheet(0, sheetName).head(headClass).build());
    }

    /**
     * export excel
     *
     * @param fileName  excel file name
     * @param sheetName sheet名
     * @param list      list
     * @param headList  header list
     * @throws IOException ex
     */
    protected <T> void exportExcel(String fileName, String sheetName, List<T> list, List<List<String>> headList) throws IOException {
        for (String s : ILLEGAL_NAME) {
            fileName = fileName.replace(s, StringUtils.EMPTY);
        }
        this.doExport(fileName, list, EasyExcel.writerSheet(0, sheetName).head(headList).build());
    }

    private WriteSheet buildWriteSheet(Integer sheetNo, String sheetName, Class<?> clazz) {
        return EasyExcel.writerSheet(sheetNo, sheetName).head(clazz).build();
    }

    /**
     * @param fileName 文件名
     * @param sheetMap 工作簿  key：sheetName，value：sheet数据
     * @param <T>      类型
     * @throws IOException
     */
    protected <T> void exportExcelMultipleSheet(String fileName, Map<String, List<T>> sheetMap) throws IOException {
        setExportResponse(fileName);
        ServletOutputStream outputStream = response.getOutputStream();
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream).registerWriteHandler(new SimpleColumnWidthStyleStrategy(DEFAULT_COLUMN_WIDTH)).build()) {
            int sheetNo = 0;
            if (MapUtils.isEmpty(sheetMap)) {
                excelWriter.write(Collections.emptyList(), EasyExcel.writerSheet(sheetNo, DEFAULT_SHEET).build());
            } else {
                for (Map.Entry<String, List<T>> writeSheetListEntry : sheetMap.entrySet()) {
                    List<T> value = writeSheetListEntry.getValue();
                    if (CollectionUtils.isEmpty(value)) {
                        continue;
                    }
                    WriteSheet writeSheet = buildWriteSheet(sheetNo, writeSheetListEntry.getKey(), value.get(0).getClass());
                    excelWriter.write(value, writeSheet);
                    sheetNo++;
                }
            }
            outputStream.flush();
        }
    }

    private <T> void doExport(String fileName, List<T> list, WriteSheet sheet) throws IOException {
        setExportResponse(fileName);
        ServletOutputStream outputStream = response.getOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(outputStream).
                registerWriteHandler(new SimpleColumnWidthStyleStrategy(DEFAULT_COLUMN_WIDTH)).build();
        excelWriter.write(list, sheet);
        outputStream.flush();
        excelWriter.finish();
    }

    protected <T> void mixExportExcelMultipleSheet(String fileName, List<FixCurveExportExcelDTO<T>> sheetFixDataList,
                                             List<DynCurveExportExcelDTO> sheetDynDataList) throws IOException {
        setExportResponse(fileName);
        ServletOutputStream outputStream = response.getOutputStream();
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(DEFAULT_COLUMN_WIDTH))
                .registerWriteHandler(new HorizontalCellStyleStrategy(ExcelStyleUtils.getHeadStyle(), ExcelStyleUtils.getContentStyle()))
                .build()
        ) {
            int sheetNo = 0;
            if (CollectionUtils.isEmpty(sheetFixDataList) && CollectionUtils.isEmpty(sheetDynDataList)) {
                excelWriter.write(Collections.emptyList(), EasyExcel.writerSheet(sheetNo, DEFAULT_SHEET).build());
                return;
            }
            for (FixCurveExportExcelDTO<T> fixCurveExportExcelDTO : sheetFixDataList) {
                List<T> list = fixCurveExportExcelDTO.getList();
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                WriteSheet writeSheet = buildWriteSheet(sheetNo, fixCurveExportExcelDTO.getSheetName(), fixCurveExportExcelDTO.getHeadClass());
                excelWriter.write(list, writeSheet);
                sheetNo++;
            }
            for (DynCurveExportExcelDTO dynCurveExportExcelDTO : sheetDynDataList) {
                List<List<Object>> dataList = dynCurveExportExcelDTO.getDataList();
                List<List<String>> headList = dynCurveExportExcelDTO.getHeadList();
                if (CollectionUtils.isEmpty(dataList)) {
                    continue;
                }
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetNo, dynCurveExportExcelDTO.getSheetName()).head(headList).build();
                excelWriter.write(dataList, writeSheet);
                sheetNo++;
            }
            outputStream.flush();
        }
    }

    protected void dynExportExcelMultipleSheet(String fileName, List<DynCurveExportExcelDTO> sheetDynDataList) throws IOException {
        setExportResponse(fileName);
        ServletOutputStream outputStream = response.getOutputStream();
        try (ExcelWriter excelWriter = EasyExcel.write(outputStream)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(DEFAULT_COLUMN_WIDTH))
                .build()
        ) {
            int sheetNo = 0;
            if (CollectionUtils.isEmpty(sheetDynDataList)) {
                excelWriter.write(Collections.emptyList(), EasyExcel.writerSheet(sheetNo, DEFAULT_SHEET).build());
            } else {
                for (DynCurveExportExcelDTO dynCurveExportExcelDTO:sheetDynDataList) {
                    List<List<Object>> dataList = dynCurveExportExcelDTO.getDataList();
                    List<List<String>> headList = dynCurveExportExcelDTO.getHeadList();
                    if (CollectionUtils.isEmpty(dataList)) {
                        continue;
                    }
                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetNo, dynCurveExportExcelDTO.getSheetName()).head(headList).build();
                    excelWriter.write(dataList, writeSheet);
                    sheetNo++;
                }
            }
            outputStream.flush();
        }
    }

    protected void dynDoExport(String fileName, String sheetName, List<List<String>> headList, List<List<Object>> dataList) throws IOException {
        setExportResponse(fileName);
        EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(DEFAULT_COLUMN_WIDTH))
                .head(headList)
                .sheet(sheetName)
                .doWrite(dataList);
    }

    private void setExportResponse(String fileName) throws IOException{
        response.setContentType("application/vnd.ms-excel");
        // 防止下载的文件名字乱码
        response.setCharacterEncoding(UTF_8);
        // 文件以附件形式下载
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, UTF_8) + FILE_SUFFIX_XLSX);
    }
}

