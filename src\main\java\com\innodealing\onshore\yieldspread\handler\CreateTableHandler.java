package com.innodealing.onshore.yieldspread.handler;

import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;

import java.sql.Date;
import java.util.Collection;
import java.util.function.Consumer;

/**
 * 创建表处理器
 *
 * <AUTHOR>
 */
public interface CreateTableHandler {

    /**
     * 检查分片表
     */
    void checkShardingTables();

    /**
     * 创建年表
     *
     * @param tableName 表名
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param consumer  消费者
     */
    default void createYearShardingTable(String tableName, Date startDate, Date endDate, Consumer<String> consumer) {
        createTable(ShardingUtils.getYearShardingTableNames(tableName, startDate, endDate), consumer);
    }

    /**
     * 创建月表
     *
     * @param tableName 表名
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param consumer  消费者
     */
    default void createMonthShardingTable(String tableName, Date startDate, Date endDate, Consumer<String> consumer) {
        createTable(ShardingUtils.getMonthShardingTableNames(tableName, startDate, endDate), consumer);
    }


    /**
     * 创建表
     *
     * @param tableNames 表名
     * @param consumer   消费者
     */
    default void createTable(Collection<String> tableNames, Consumer<String> consumer) {
        for (String tableName : tableNames) {
            consumer.accept(tableName);
        }
    }

}
