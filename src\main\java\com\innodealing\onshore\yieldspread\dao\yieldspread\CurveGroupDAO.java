package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.enums.CurveGroupCategoryEnum;
import com.innodealing.onshore.yieldspread.enums.CurveGroupTypeEnum;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.CurveGroupMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.CurveGroupDO;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 曲线组
 *
 * <AUTHOR>
 */
@Repository
public class CurveGroupDAO {

    @Resource
    private CurveGroupMapper groupMapper;

    /**
     * 统计用户的曲线组数量
     *
     * @param userid 用户ID
     * @return 曲线组数量
     */
    public int countGroup(Long userid) {
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .and(CurveGroupDO::getUserId, isEqual(userid))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return groupMapper.selectCountByDynamicQuery(query);
    }

    /**
     * 判断该组名是否存在,包含基准曲线组
     *
     * @param userid    用户id
     * @param groupName 组名
     * @return true:存在  false：不存在
     */
    public boolean isExistContainBenchmarkCurveGroup(Long userid, String groupName) {
        HashSet<Long> userIds = Sets.newHashSet(userid, YieldSpreadConst.BENCHMARK_CURVE_USER_ID);
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .select(CurveGroupDO::getId)
                .and(CurveGroupDO::getUserId, in(userIds))
                .and(CurveGroupDO::getCurveGroupName, isEqual(groupName))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return groupMapper.selectFirstByDynamicQuery(query).isPresent();
    }

    /**
     * 判断该组是否存在
     *
     * @param userid  用户id
     * @param groupId 组id
     * @return true:存在  false：不存在
     */
    public boolean isExist(Long userid, Long groupId) {
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .select(CurveGroupDO::getId)
                .and(CurveGroupDO::getUserId, isEqual(userid))
                .and(CurveGroupDO::getId, isEqual(groupId))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return groupMapper.selectFirstByDynamicQuery(query).isPresent();
    }

    /**
     * 获取最大序号
     *
     * @param userid         用户id
     * @param curveGroupType 哪中类型的曲线组
     * @return 最大序号id
     */
    public Optional<Integer> getMaxOrder(Long userid, CurveGroupTypeEnum curveGroupType) {
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .and(CurveGroupDO::getUserId, isEqual(userid))
                .and(CurveGroupDO::getCurveGroupType, isEqual(curveGroupType.getValue()))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return groupMapper.selectMaxByDynamicQuery(CurveGroupDO::getCurveGroupOrder, query);
    }

    /**
     * 新增组
     *
     * @param curveGroup CurveGroupDO
     * @return 插入结果
     */
    public boolean insert(CurveGroupDO curveGroup) {
        return groupMapper.insertSelective(curveGroup) != 0;
    }

    /**
     * 根据组id获取组
     *
     * @param id 组id
     * @return CurveGroupDO
     */
    public Optional<CurveGroupDO> get(Long id) {
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .and(CurveGroupDO::getId, isEqual(id))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return groupMapper.selectFirstByDynamicQuery(query);
    }

    /**
     * 更新组
     *
     * @param curveGroup CurveGroupDO
     * @return 更新结果结果
     */
    public boolean updateByPrimaryKeySelective(CurveGroupDO curveGroup) {
        return groupMapper.updateByPrimaryKeySelective(curveGroup) != 0;
    }

    /**
     * 删除组
     *
     * @param groupId 组id
     * @return 删除结果
     */
    public boolean delete(Long groupId) {
        CurveGroupDO group = new CurveGroupDO();
        group.setId(groupId);
        group.setDeleted(Deleted.DELETED.getValue());
        return groupMapper.updateByPrimaryKeySelective(group) != 0;
    }

    /**
     * 根据用户id获取曲线组
     *
     * @param userid             用户id
     * @param curveGroupCategory 曲线类别
     * @return 曲线组
     */
    public List<CurveGroupDO> listByUserid(Long userid, @Nullable CurveGroupCategoryEnum curveGroupCategory) {
        List<Long> userIds = Lists.newArrayList(userid);
        if (Objects.isNull(curveGroupCategory) || CurveGroupCategoryEnum.BENCHMARK_GROUP == curveGroupCategory) {
            userIds.add(YieldSpreadConst.BENCHMARK_CURVE_USER_ID);
        }
        Integer curveGroupCategoryValue = Objects.isNull(curveGroupCategory) ? null : curveGroupCategory.getValue();
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .and(CurveGroupDO::getUserId, in(userIds))
                .and(Objects.nonNull(curveGroupCategoryValue), CurveGroupDO::getCurveGroupCategory, isEqual(curveGroupCategoryValue))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                .orderBy(CurveGroupDO::getCurveGroupOrder, SortDirections::desc);
        return groupMapper.selectByDynamicQuery(query);
    }

    /**
     * 获取默认组id
     *
     * @param userid 用户ID
     * @return 是否有了默认组
     */
    public Optional<Long> getDefaultGroupId(Long userid) {
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .select(CurveGroupDO::getId)
                .and(CurveGroupDO::getUserId, isEqual(userid))
                .and(CurveGroupDO::getCurveGroupType, isEqual(CurveGroupTypeEnum.DEFAULT_GROUP.getValue()))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        Optional<CurveGroupDO> curveGroupOptional = groupMapper.selectFirstByDynamicQuery(query);
        return curveGroupOptional.map(CurveGroupDO::getId);
    }

    /**
     * 是否有了默认组
     *
     * @param userid 用户ID
     * @return 是否有了默认组
     */
    public boolean hasDefaultGroup(Long userid) {
        return getDefaultGroupId(userid).isPresent();
    }

    /**
     * 根据用户id和组名获取组
     *
     * @param userid    用户id
     * @param groupName 组名
     * @return 组
     */
    public Optional<CurveGroupDO> get(Long userid, String groupName) {
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .and(CurveGroupDO::getUserId, isEqual(userid))
                .and(CurveGroupDO::getCurveGroupName, isEqual(groupName))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return groupMapper.selectFirstByDynamicQuery(query);
    }

    /**
     * 获取比这个order小的曲线组order
     *
     * @param userid        用户id
     * @param preGroupOrder 前一条曲线组的order 大的在前
     * @return order
     */
    public Optional<Integer> getLessGroupOrder(Long userid, Integer preGroupOrder) {
        DynamicQuery<CurveGroupDO> query = DynamicQuery.createQuery(CurveGroupDO.class)
                .select(CurveGroupDO::getCurveGroupOrder)
                .and(CurveGroupDO::getUserId, isEqual(userid))
                .and(CurveGroupDO::getCurveGroupOrder, lessThan(preGroupOrder))
                .and(CurveGroupDO::getCurveGroupType, isEqual(CurveGroupTypeEnum.ORDINARY_GROUP.getValue()))
                .and(CurveGroupDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return groupMapper.selectMaxByDynamicQuery(CurveGroupDO::getCurveGroupOrder, query);
    }

}
