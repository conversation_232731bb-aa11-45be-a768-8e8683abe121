package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition;

import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.InsuBondYieldSpreadCurveParameter;
import org.apache.ibatis.annotations.Param;

/**
 * 保险利差曲线mapper
 *
 * <AUTHOR>
 */
public interface PgInsuBondYieldSpreadCurveMapper extends PgBaseMapper<InsuBondYieldSpreadCurveParameter> {

    /**
     * 创建实体表(评级分片)
     *
     * @param parameter 创建参数
     */
    void createTableRatingRouter(@Param("parameter") InsuBondYieldSpreadCurveParameter parameter);

    /**
     * 同步视图数据
     *
     * @param tableName   表名
     * @param mvTableName 视图名称
     */
    void syncCurveIncrFromMV(@Param("tableName") String tableName, @Param("mvTableName") String mvTableName);

}
