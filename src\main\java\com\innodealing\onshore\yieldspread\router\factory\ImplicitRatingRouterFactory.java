package com.innodealing.onshore.yieldspread.router.factory;


import com.google.common.collect.Maps;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.enums.ImplicitRatingTagEnum;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.BiConsumer;

/**
 * 隐含评级 分片路由创建工厂
 *
 * <AUTHOR>
 */
@Component
public class ImplicitRatingRouterFactory implements RatingRouterFactory<ImplicitRatingRouter> {

    private static final Map<Integer, BiConsumer<ImplicitRatingRouter, Integer>> PROPERTY_MAP = Maps.newConcurrentMap();

    static {
        PROPERTY_MAP.put(ImplicitRatingTagEnum.AAA_PLUS.getValue(), ImplicitRatingRouter::setTripleAPlus);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.AAA.getValue(), ImplicitRatingRouter::setTripleA);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.AAA_SUB.getValue(), ImplicitRatingRouter::setTripleASub);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.AA_PLUS.getValue(), ImplicitRatingRouter::setDoubleAPlus);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.AA.getValue(), ImplicitRatingRouter::setDoubleA);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.AA_2.getValue(), ImplicitRatingRouter::setDoubleA2);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.AA_SUB.getValue(), ImplicitRatingRouter::setDoubleASub);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.A_PLUS.getValue(), ImplicitRatingRouter::setOneAPlus);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.A.getValue(), ImplicitRatingRouter::setOneA);
        PROPERTY_MAP.put(ImplicitRatingTagEnum.A_SUB.getValue(), ImplicitRatingRouter::setOneASub);
    }

    @Override
    public ImplicitRatingRouter newRatingRouter(Integer... combinations) {
        ImplicitRatingRouter router = new ImplicitRatingRouter();
        for (Integer rating : combinations) {
            if (ITextValueEnum.containsEnum(ImplicitRatingTagEnum.class, rating)) {
                PROPERTY_MAP.get(rating).accept(router, rating);
            }
        }
        return router;
    }

}
