package com.innodealing.onshore.yieldspread.model.dto.request;

/**
 * 行业利差请求DTO
 *
 * <AUTHOR>
 */
public class InduPanoramaRequestDTO extends BasePanoramaRequestDTO {

    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return "InduPanoramaRequestDTO{" +
                "bondExtRatingMapping=" + bondExtRatingMapping +
                ", bondImpliedRatingMappingTag=" + bondImpliedRatingMappingTag +
                ", comYyRatingMappingTag=" + comYyRatingMappingTag +
                ", spreadRemainingTenorTag=" + spreadRemainingTenorTag +
                ", spreadBondType=" + spreadBondType +
                ", guaranteeStatus=" + guaranteeStatus +
                ", spreadDate=" + spreadDate +
                '}';
    }
}
