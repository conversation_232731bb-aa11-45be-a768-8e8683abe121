package com.innodealing.onshore.yieldspread.controller.api;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.model.dto.request.CustomCurveGenerateReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondAnalysisResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CustomComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CustomCurveBasicInfoResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CustomSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.service.CustomComYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.CustomCurveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * 自定义曲线
 *
 * <AUTHOR>
 */
@Api(tags = "(API)自定义曲线")
@RestController
@RequestMapping("api/custom-curve")
@Validated
public class CustomCurveController {

    @Resource
    private CustomCurveService customCurveService;

    @Resource
    private CustomComYieldSpreadService customComYieldSpreadService;

    @ApiOperation(value = "曲线名称是否重复")
    @GetMapping("curve-name/is-duplicate")
    public RestResponse<Boolean> curveNameIsDuplicate(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveName", value = "曲线名称")
            @NotBlank(message = "曲线名称不能为空") @Length(message = "自定义曲线名称不能超过 {max} 个字符", max = YieldSpreadConst.CUSTOM_CURVE_NAME_MAX_LENGTH)
            @RequestParam String curveName) {
        return RestResponse.Success(customCurveService.curveNameIsDuplicate(userid, curveName));
    }

    @ApiOperation(value = "解析债券")
    @PostMapping("analysis-bonds")
    public RestResponse<BondAnalysisResDTO> analysisBonds(
            @RequestBody
            @NotEmpty(message = "导入的债券集合不能为空")
            @Size(message = "单次解析曲线上限为 {max} 只债券，请调整后再上传", max = YieldSpreadConst.ANALYSIS_BOND_SIZE_UPPER_LIMIT)
            Set<String> bondNameOrCodeList) {
        return RestResponse.Success(customCurveService.analysisBonds(bondNameOrCodeList));
    }

    @ApiOperation(value = "保存曲线")
    @PostMapping("custom-curve-save")
    public RestResponse<Boolean> saveCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated CustomCurveGenerateReqDTO customCurveGenerateReqDTO) {
        return RestResponse.Success(customCurveService.saveCurve(userid, curveGroupId, customCurveGenerateReqDTO));
    }

    @ApiOperation(value = "获取自定义曲线列表")
    @GetMapping("custom-curve-list")
    public RestResponse<List<CustomCurveBasicInfoResDTO>> listCustomCurves(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "generateStatus", value = "自定义曲线生成状态：待生成=1，生成中=2，已生成=3，生成失败=4")
            @RequestParam(name = "generateStatus", required = false) Set<Integer> generateStatus) {
        return RestResponse.Success(customCurveService.listCustomCurves(userid, generateStatus));
    }

    @ApiOperation(value = "重新生成，生成失败后可重新生成")
    @PostMapping("custom-curve-regenerate")
    public RestResponse<Boolean> regenerate(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam(name = "curveId") Long curveId) {
        return RestResponse.Success(customCurveService.regenerate(userid, curveId));
    }

    @ApiOperation(value = "取消生成曲线")
    @GetMapping("cancel-generate")
    public RestResponse<Boolean> cancelGenerate(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam Long curveId) {
        return RestResponse.Success(customCurveService.cancelGenerate(userid, curveId));
    }

    @ApiOperation(value = "更新曲线")
    @PostMapping("custom-curve-update")
    public RestResponse<Boolean> updateCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Valid CustomCurveGenerateReqDTO generateReqDTO) {
        return RestResponse.Success(customCurveService.update(userid, curveId, generateReqDTO));
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public RestResponse<List<CustomSingleBondYieldSpreadResDTO>> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(customCurveService.pagingSingleBondYieldSpreads(userid, request));
    }

    @PostMapping("/count-single-bond")
    @ApiOperation(value = "表区-单券利差条数")
    public RestResponse<Long> countSingleBondYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(customCurveService.countSingleBondYieldSpread(userid, request));
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差  分页")
    public RestResponse<List<CustomComYieldSpreadResDTO>> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(customComYieldSpreadService.listComYieldSpreads(userid, request));
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public RestResponse<Long> countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(customComYieldSpreadService.countComYieldSpread(userid, request));
    }

}
