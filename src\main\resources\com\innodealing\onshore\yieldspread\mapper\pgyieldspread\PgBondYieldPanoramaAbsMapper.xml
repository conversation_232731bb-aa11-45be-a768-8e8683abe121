<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBondYieldPanoramaAbsMapper">
    <select id="listPanoramaQuantileStatisticsViews"
            resultType="com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgPanoramaQuantileStatisticsViewDO">
        SELECT t1.curve_code,
        CASE
        WHEN COUNT(t2.ytm_1m) > 0 THEN COUNT(t1.ytm_1m &lt; t2.ytm_1m or null)
        ELSE NULL END ytm1MLessIssueCount,
        CASE WHEN COUNT(t2.ytm_1m) > 0 THEN COUNT(t1.ytm_1m) ELSE NULL END ytm1MCount,
        CASE
        WHEN COUNT(t2.ytm_3m) > 0 THEN COUNT(t1.ytm_3m &lt; t2.ytm_3m or null)
        ELSE NULL END ytm3MLessIssueCount,
        CASE WHEN COUNT(t2.ytm_3m) > 0 THEN COUNT(t1.ytm_3m) ELSE NULL END ytm3MCount,
        CASE
        WHEN COUNT(t2.ytm_6m) > 0 THEN COUNT(t1.ytm_6m &lt; t2.ytm_6m or null)
        ELSE NULL END ytm6MLessIssueCount,
        CASE WHEN COUNT(t2.ytm_6m) > 0 THEN COUNT(t1.ytm_6m) ELSE NULL END ytm6MCount,
        CASE
        WHEN COUNT(t2.ytm_9m) > 0 THEN COUNT(t1.ytm_9m &lt; t2.ytm_9m or null)
        ELSE NULL END ytm9MLessIssueCount,
        CASE WHEN COUNT(t2.ytm_9m) > 0 THEN COUNT(t1.ytm_9m) ELSE NULL END ytm9MCount,
        CASE
        WHEN COUNT(t2.ytm_1y) > 0 THEN COUNT(t1.ytm_1y &lt; t2.ytm_1y or null)
        ELSE NULL END ytm1YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_1y) > 0 THEN COUNT(t1.ytm_1y) ELSE NULL END ytm1YCount,
        CASE
        WHEN COUNT(t2.ytm_2y) > 0 THEN COUNT(t1.ytm_2y &lt; t2.ytm_2y or null)
        ELSE NULL END ytm2YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_2y) > 0 THEN COUNT(t1.ytm_2y) ELSE NULL END ytm2YCount,
        CASE
        WHEN COUNT(t2.ytm_3y) > 0 THEN COUNT(t1.ytm_3y &lt; t2.ytm_3y or null)
        ELSE NULL END ytm3YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_3y) > 0 THEN COUNT(t1.ytm_3y) ELSE NULL END ytm3YCount,
        CASE
        WHEN COUNT(t2.ytm_4y) > 0 THEN COUNT(t1.ytm_4y &lt; t2.ytm_4y or null)
        ELSE NULL END ytm4YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_4y) > 0 THEN COUNT(t1.ytm_4y) ELSE NULL END ytm4YCount,
        CASE
        WHEN COUNT(t2.ytm_5y) > 0 THEN COUNT(t1.ytm_5y &lt; t2.ytm_5y or null)
        ELSE NULL END ytm5YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_5y) > 0 THEN COUNT(t1.ytm_5y) ELSE NULL END ytm5YCount,
        CASE
        WHEN COUNT(t2.ytm_7y) > 0 THEN COUNT(t1.ytm_7y &lt; t2.ytm_7y or null)
        ELSE NULL END ytm7YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_7y) > 0 THEN COUNT(t1.ytm_7y) ELSE NULL END ytm7YCount,
        CASE
        WHEN COUNT(t2.ytm_10y) > 0 THEN COUNT(t1.ytm_10y &lt; t2.ytm_10y or null)
        ELSE NULL END ytm10YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_10y) > 0 THEN COUNT(t1.ytm_10y) ELSE NULL END ytm10YCount,
        CASE
        WHEN COUNT(t2.ytm_15y) > 0 THEN COUNT(t1.ytm_15y &lt; t2.ytm_15y or null)
        ELSE NULL END ytm15YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_15y) > 0 THEN COUNT(t1.ytm_15y) ELSE NULL END ytm15YCount,
        CASE
        WHEN COUNT(t2.ytm_20y) > 0 THEN COUNT(t1.ytm_20y &lt; t2.ytm_20y or null)
        ELSE NULL END ytm20YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_20y) > 0 THEN COUNT(t1.ytm_20y) ELSE NULL END ytm20YCount,
        CASE
        WHEN COUNT(t2.ytm_30y) > 0 THEN COUNT(t1.ytm_30y &lt; t2.ytm_30y or null)
        ELSE NULL END ytm30YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_30y) > 0 THEN COUNT(t1.ytm_30y) ELSE NULL END ytm30YCount,
        CASE
        WHEN COUNT(t2.ytm_50y) > 0 THEN COUNT(t1.ytm_50y &lt; t2.ytm_50y or null)
        ELSE NULL END ytm50YLessIssueCount,
        CASE WHEN COUNT(t2.ytm_50y) > 0 THEN COUNT(t1.ytm_50y) ELSE NULL END ytm50YCount
        FROM bond_yield_panorama_abs as t1
        INNER JOIN bond_yield_panorama_abs t2
        ON t1.curve_code = t2.curve_code AND t2.issue_date = #{issueDate} AND t2.deleted = 0
        WHERE t1.issue_date >= #{startDate}
        AND t1.issue_date &lt;= #{endDate}
        AND t1.bond_type in
        <foreach collection="bondTypeList" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND t1.deleted = 0
        GROUP BY t1.curve_code;
    </select>
</mapper>