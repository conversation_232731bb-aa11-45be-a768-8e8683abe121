package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 债券评级响应DTO
 *
 * <AUTHOR>
 */
public class BondRatingResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("债券评级code")
    private Integer ratingCode;

    @ApiModelProperty("债券评级名称")
    private String ratingName;

    /**
     * 债券评级响应构造函数
     *
     * @param ratingCode 评级代码
     * @param ratingName 评级名字
     */
    public BondRatingResponseDTO(Integer ratingCode, String ratingName) {
        this.ratingCode = ratingCode;
        this.ratingName = ratingName;
    }

    public Integer getRatingCode() {
        return ratingCode;
    }

    public void setRatingCode(Integer ratingCode) {
        this.ratingCode = ratingCode;
    }

    public String getRatingName() {
        return ratingName;
    }

    public void setRatingName(String ratingName) {
        this.ratingName = ratingName;
    }
}
