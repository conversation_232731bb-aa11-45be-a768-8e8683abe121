package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * 用户曲线
 *
 * <AUTHOR>
 */
@Table(name = "user_curve_group")
public class CurveGroupDO {

    /**
     * 主键id
     */
    @Id
    @Column
    @GeneratedValue(generator = "JDBC", strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户id
     */
    @Column
    private Long userId;

    /**
     * 组名
     */
    @Column
    private String curveGroupName;

    /**
     * 组类别
     */
    @Column
    private Integer curveGroupCategory;

    /**
     * 组类型
     */
    @Column
    private Integer curveGroupType;

    @Column
    private Integer curveGroupOrder;

    /**
     * 是否删除
     */
    @Column
    private Integer deleted;

    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;

    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCurveGroupName() {
        return curveGroupName;
    }

    public void setCurveGroupName(String curveGroupName) {
        this.curveGroupName = curveGroupName;
    }

    public Integer getCurveGroupCategory() {
        return curveGroupCategory;
    }

    public void setCurveGroupCategory(Integer curveGroupCategory) {
        this.curveGroupCategory = curveGroupCategory;
    }

    public Integer getCurveGroupType() {
        return curveGroupType;
    }

    public void setCurveGroupType(Integer curveGroupType) {
        this.curveGroupType = curveGroupType;
    }

    public Integer getCurveGroupOrder() {
        return curveGroupOrder;
    }

    public void setCurveGroupOrder(Integer curveGroupOrder) {
        this.curveGroupOrder = curveGroupOrder;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Timestamp getCreateTime() {
        return Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public Timestamp getUpdateTime() {
        return Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

}
