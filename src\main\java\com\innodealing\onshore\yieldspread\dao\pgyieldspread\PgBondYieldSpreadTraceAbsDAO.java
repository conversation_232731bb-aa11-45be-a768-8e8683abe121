package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveCodeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBondYieldSpreadTraceAbsMapper;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceAbsBO;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgSpreadTraceQuantileStatisticsViewDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;


/**
 * 债券利差追踪-绝对值DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgBondYieldSpreadTraceAbsDAO {

    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d:%d";
    private static final String BOND_YIELD_SPREAD_TRACE_ABS_PK = "yieldSpread:bondYieldSpreadTraceAbsPk";

    @Resource
    private PgBondYieldSpreadTraceAbsMapper bondYieldSpreadTraceAbsMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;

    /**
     * 保存利差追踪绝对值列表
     *
     * @param issueDate               发行日期
     * @param yieldSpreadTraceAbsList 利差追踪-绝对值列表
     * @return int 保存行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int saveBondYieldSpreadTraceAbsList(@NonNull Date issueDate, List<PgBondYieldSpreadTraceAbsDO> yieldSpreadTraceAbsList) {
        if (CollectionUtils.isEmpty(yieldSpreadTraceAbsList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        DynamicQuery<PgBondYieldSpreadTraceAbsDO> query = DynamicQuery.createQuery(PgBondYieldSpreadTraceAbsDO.class)
                .and(PgBondYieldSpreadTraceAbsDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldSpreadTraceAbsDO::getId, lessThanOrEqual(maxPk));
        Map<String, PgBondYieldSpreadTraceAbsDO> curveCodeIssueDateMap = bondYieldSpreadTraceAbsMapper.selectByDynamicQuery(query).stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        List<PgBondYieldSpreadTraceAbsDO> insertList = new ArrayList<>();
        List<PgBondYieldSpreadTraceAbsDO> updateList = new ArrayList<>();
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        for (PgBondYieldSpreadTraceAbsDO bondYieldSpreadTraceQuantileDO : yieldSpreadTraceAbsList) {
            String key = this.getKey(bondYieldSpreadTraceQuantileDO);
            if (curveCodeIssueDateMap.containsKey(key)) {
                PgBondYieldSpreadTraceAbsDO existBondYieldPanoramaQuantile = curveCodeIssueDateMap.get(key);
                bondYieldSpreadTraceQuantileDO.setId(existBondYieldPanoramaQuantile.getId());
                bondYieldSpreadTraceQuantileDO.setCreateTime(null);
                bondYieldSpreadTraceQuantileDO.setUpdateTime(now);
                updateList.add(bondYieldSpreadTraceQuantileDO);
            } else {
                bondYieldSpreadTraceQuantileDO.setId(redisService.generatePk(BOND_YIELD_SPREAD_TRACE_ABS_PK, bondYieldSpreadTraceQuantileDO.getIssueDate()));
                bondYieldSpreadTraceQuantileDO.setCreateTime(now);
                bondYieldSpreadTraceQuantileDO.setUpdateTime(now);
                insertList.add(bondYieldSpreadTraceQuantileDO);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param abs 利差追踪-绝对值
     * @return {@link String} key值
     */
    private String getKey(PgBondYieldSpreadTraceAbsDO abs) {
        return String.format(NAMESPACE_KEY_PLACEHOLDER, abs.getBondType(), abs.getChartType(), abs.getCurveCode(),
                abs.getIssueDate().getTime());
    }

    /**
     * 批量更新
     *
     * @param updateList 利差追踪-绝对值列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgBondYieldSpreadTraceAbsDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldSpreadTraceAbsMapper> updateBatchAction =
                MapperBatchAction.create(PgBondYieldSpreadTraceAbsMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldSpreadTraceAbsDO abs : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgBondYieldSpreadTraceAbsDO> updateQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceAbsDO.class)
                        .and(PgBondYieldSpreadTraceAbsDO::getId, isEqual(abs.getId()));
                mapper.updateSelectiveByDynamicQuery(abs, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 利差追踪-绝对值列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgBondYieldSpreadTraceAbsDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldSpreadTraceAbsMapper> insertBatchAction =
                MapperBatchAction.create(PgBondYieldSpreadTraceAbsMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldSpreadTraceAbsDO abs : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(abs));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 查詢利差追踪-绝对值数据
     *
     * @param issueDate  发行日期
     * @param chartTypes 图类型
     * @param bondType   债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充
     * @return {@link List}<{@link PgBondYieldSpreadTraceAbsDO}> 利差追踪-绝对值数据集
     */
    public List<PgBondYieldSpreadTraceAbsDO> listBondYieldSpreadTraces(@NonNull Date issueDate, Collection<Integer> chartTypes, Integer bondType) {
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        DynamicQuery<PgBondYieldSpreadTraceAbsDO> query = DynamicQuery.createQuery(PgBondYieldSpreadTraceAbsDO.class)
                .and(PgBondYieldSpreadTraceAbsDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldSpreadTraceAbsDO::getId, lessThanOrEqual(maxPk))
                .and(Objects.nonNull(bondType), PgBondYieldSpreadTraceAbsDO::getBondType, isEqual(bondType))
                .and(CollectionUtils.isNotEmpty(chartTypes), PgBondYieldSpreadTraceAbsDO::getChartType, in(chartTypes))
                .and(PgBondYieldSpreadTraceAbsDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return bondYieldSpreadTraceAbsMapper.selectByDynamicQuery(query);
    }

    /**
     * 查詢利差追踪-绝对值数据
     *
     * @param bondType   债券类型
     * @param spreadDate 利率时间
     * @return 利差追踪-绝对值数据
     */
    public List<PgBondYieldSpreadTraceBO> listYieldSpreadTraces(YieldPanoramaBondTypeEnum bondType, Date spreadDate) {
        if (!ObjectUtils.allNotNull(bondType, spreadDate)) {
            return Lists.newArrayList();
        }
        Long minPk = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<PgBondYieldSpreadTraceAbsDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceAbsDO.class)
                .and(PgBondYieldSpreadTraceAbsDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldSpreadTraceAbsDO::getId, lessThanOrEqual(maxPk))
                .and(PgBondYieldSpreadTraceAbsDO::getBondType, isEqual(bondType.getValue()))
                .and(PgBondYieldSpreadTraceAbsDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<PgBondYieldSpreadTraceAbsDO> pgBondYieldPanoramaAbsDOList = bondYieldSpreadTraceAbsMapper.selectByDynamicQuery(dynamicQuery);
        return CollectionUtils.isNotEmpty(pgBondYieldPanoramaAbsDOList) ?
                BeanCopyUtils.copyList(pgBondYieldPanoramaAbsDOList, PgBondYieldSpreadTraceBO.class) : Lists.newArrayList();
    }

    /**
     * 查询利差追踪历史分位数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param issueDate 发行时间
     * @param bondType  债券类型
     * @return 计算历史分位所需的基本数据
     */
    public List<PgSpreadTraceQuantileStatisticsViewDO> listTraceQuantileViews(@NonNull Date startDate, @NonNull Date endDate,
                                                                              @NonNull Date issueDate, @NonNull Integer bondType) {
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        LocalDate localIssueDate = issueDate.toLocalDate();
        if (localStartDate.isAfter(localEndDate)) {
            throw new TipsException("开始时间不能大于结束时间");
        }
        if (localIssueDate.isBefore(localStartDate) || localIssueDate.isAfter(localEndDate)) {
            throw new TipsException("发行时间必须要在自定义时间范围内");
        }
        return bondYieldSpreadTraceAbsMapper.listTraceQuantileStatisticsViews(startDate, endDate, issueDate, bondType);
    }


    /**
     * 查詢利差追踪-绝对值数据
     *
     * @param bondTypeEnum  债券类型
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param chartTypeEnum 图类型
     * @param curveCodeEnum 曲线类型
     * @return 利差追踪-绝对值数据
     */
    public List<PgBondYieldSpreadTraceAbsBO> listYieldSpreadTraces(Date startDate, Date endDate, YieldPanoramaBondTypeEnum bondTypeEnum, YieldSpreadChartTypeEnum chartTypeEnum,
                                                                   YieldSpreadCurveCodeEnum curveCodeEnum) {
        if (!ObjectUtils.allNotNull(bondTypeEnum, startDate, endDate, chartTypeEnum, curveCodeEnum)) {
            return Collections.emptyList();
        }
        Long minPk = ShardingUtils.getMinPkOfDate(startDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(endDate);
        DynamicQuery<PgBondYieldSpreadTraceAbsDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceAbsDO.class)
                .ignore(PgBondYieldSpreadTraceAbsDO::getId, PgBondYieldSpreadTraceAbsDO::getDeleted,
                        PgBondYieldSpreadTraceAbsDO::getCreateTime, PgBondYieldSpreadTraceAbsDO::getUpdateTime)
                .and(PgBondYieldSpreadTraceAbsDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldSpreadTraceAbsDO::getId, lessThanOrEqual(maxPk))
                .and(PgBondYieldSpreadTraceAbsDO::getBondType, isEqual(bondTypeEnum.getValue()))
                .and(PgBondYieldSpreadTraceAbsDO::getChartType, isEqual(chartTypeEnum.getValue()))
                .and(PgBondYieldSpreadTraceAbsDO::getCurveCode, isEqual(curveCodeEnum.getValue()))
                .and(PgBondYieldSpreadTraceAbsDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<PgBondYieldSpreadTraceAbsDO> pgBondYieldPanoramaAbsDOList = bondYieldSpreadTraceAbsMapper.selectByDynamicQuery(dynamicQuery);
        return BeanCopyUtils.copyList(pgBondYieldPanoramaAbsDOList, PgBondYieldSpreadTraceAbsBO.class);
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgBondYieldSpreadTraceAbsDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceAbsDO.class)
                .orderBy(PgBondYieldSpreadTraceAbsDO::getIssueDate, SortDirections::desc);
        return bondYieldSpreadTraceAbsMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgBondYieldSpreadTraceAbsDO::getIssueDate);
    }
}
