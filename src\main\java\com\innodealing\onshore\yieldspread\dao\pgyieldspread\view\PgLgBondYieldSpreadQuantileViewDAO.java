package com.innodealing.onshore.yieldspread.dao.pgyieldspread.view;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view.PgLgBondYieldSpreadQuantileViewMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgBondYieldSpreadQuantileViewDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 地方债区域利差分位数视图DAO
 *
 * <AUTHOR>
 * @create: 2024-11-05
 */
@Component
public class PgLgBondYieldSpreadQuantileViewDAO {

    public static final String LG_QUANTILE_MV_NAME_PREFIX = "v_lg_bond_yield_spread_quantile";

    @Resource
    private PgLgBondYieldSpreadQuantileViewMapper pgLgBondYieldSpreadQuantileViewMapper;


    /**
     * 创建利差追踪历史分位视图
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    public void createLgBondYieldSpreadQuantileView(Date startDate, Date endDate) {
        String lgQuantileMvName = LG_QUANTILE_MV_NAME_PREFIX +
                startDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE) +
                endDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        pgLgBondYieldSpreadQuantileViewMapper.createLgBondYieldSpreadQuantileView(lgQuantileMvName, startDate.toString(), endDate.toString());
    }

    /**
     * 创建利差追踪历史分位视图
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    public void dropLgBondYieldSpreadQuantileView(Date startDate, Date endDate) {
        String lgQuantileMvName = LG_QUANTILE_MV_NAME_PREFIX +
                startDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE) +
                endDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        pgLgBondYieldSpreadQuantileViewMapper.dropMv(lgQuantileMvName);
    }

    /**
     * 查询利差追踪历史分位数据
     *
     * @param spreadDate 利差日期
     * @return {@link List}<{@link PgLgBondYieldSpreadQuantileViewDO}> 利差追踪历史分位数据集
     */
    public List<PgLgBondYieldSpreadQuantileViewDO> listLgBondYieldSpreadQuantiles(Date spreadDate) {
        DynamicQuery<PgLgBondYieldSpreadQuantileViewDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadQuantileViewDO.class)
                .and(PgLgBondYieldSpreadQuantileViewDO::getSpreadDate, isEqual(spreadDate));
        return pgLgBondYieldSpreadQuantileViewMapper.selectByDynamicQuery(query);
    }

    /**
     * 查询利差追踪历史分位数据
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param spreadDate 利差日期
     * @return {@link List}<{@link PgLgBondYieldSpreadQuantileViewDO}> 利差追踪历史分位数据集
     */
    public List<PgLgBondYieldSpreadQuantileViewDO> listLgBondYieldSpreadQuantiles(@NotNull Date startDate, @NotNull Date endDate, @NotNull Date spreadDate) {
        String lgQuantileMvName = LG_QUANTILE_MV_NAME_PREFIX +
                startDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE) +
                endDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        return pgLgBondYieldSpreadQuantileViewMapper.listLgBondYieldSpreadQuantileView(lgQuantileMvName, spreadDate.toString());
    }

}
