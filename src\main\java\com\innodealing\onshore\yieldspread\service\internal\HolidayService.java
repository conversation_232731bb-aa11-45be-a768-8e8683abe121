package com.innodealing.onshore.yieldspread.service.internal;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Date;

/**
 * 假日服务
 *
 * <AUTHOR>
 **/
@FeignClient(name = "holidayService", url = "${bond.basic.api.url}", path = "/internal/holiday")
public interface HolidayService {
    /**
     * 是否是假日
     *
     * @param date 日期
     * @return 是否假日
     */
    @GetMapping
    Boolean isHoliday(@RequestParam Date date);

    /**
     * 获取上一个工作日期
     *
     * @param date 日期
     * @return 上一个工作日期
     */
    @GetMapping("last/work/day")
    Date lastWorkDay(@RequestParam Date date);

    /**
     * 获取最新的工作日（包括自己），如果 beforeWorkdays 如果是0 那么就看自己是不是工作日，是就返回自己
     *
     * @param date           日期
     * @param beforeWorkdays beforeWorkdays
     * @return 上一个工作日期
     */
    @GetMapping("latest/work/day")
    Date latestWorkDay(@RequestParam Date date, @RequestParam int beforeWorkdays);

    /**
     * 获取给定日期的下一个工作日（包括自己）
     *
     * @param date date
     * @return 获取给定日期的下一个工作日（包括自己）
     */
    @GetMapping("next/work/day")
    Date getNextWorkDay(@RequestParam Date date);
}
