package com.innodealing.onshore.yieldspread.dao.pgshard.partition;

import com.github.wz2cool.dynamic.BaseFilterDescriptor;
import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.InduShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.InduBondShardYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.InduShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 行业利差曲线分区DAO
 *
 * <AUTHOR>
 */
@Repository
public class InduBondShardYieldSpreadCurveRepository {

    @Resource
    private InduShardBondYieldSpreadCurveMapper induShardBondYieldSpreadCurveMapper;

    /**
     * 产业利差曲线分片查询
     *
     * @param searchParameter 查询参数
     * @param router          路由
     * @return 分片表结果集
     */
    public List<InduShardBondYieldSpreadCurveDO> listInduShardBondYieldSpreadCurves(InduBondShardYieldSpreadParamDTO searchParameter, AbstractRatingRouter router) {
        BaseFilterDescriptor<InduShardBondYieldSpreadCurveDO>[] baseFilterDescriptors = this.listFilters(searchParameter).getFilters();
        DynamicQuery<InduShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(InduShardBondYieldSpreadCurveDO.class)
                .and(baseFilterDescriptors);
        return induShardBondYieldSpreadCurveMapper.selectByDynamicQueryRouter(query, router);
    }

    private FilterGroupDescriptor<InduShardBondYieldSpreadCurveDO> listFilters(InduBondShardYieldSpreadParamDTO request) {
        Optional.ofNullable(request.getSpreadRemainingTenorTag()).ifPresent(request::setRemainingTenor);
        Optional.ofNullable(request.getGuaranteeStatus()).ifPresent(request::setGuaranteedStatus);
        return FilterGroupDescriptor.create(InduShardBondYieldSpreadCurveDO.class)
                .and(nonNull(request.getSpreadBondType()), InduShardBondYieldSpreadCurveDO::getSpreadBondType, isEqual(request.getSpreadBondType()))
                .and(isNull(request.getSpreadBondType()), InduShardBondYieldSpreadCurveDO::getUsingSpreadBondType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getBondExtRatingMapping()), InduShardBondYieldSpreadCurveDO::getBondExtRatingMapping, isEqual(request.getBondExtRatingMapping()))
                .and(isNull(request.getBondExtRatingMapping()), InduShardBondYieldSpreadCurveDO::getUsingBondExtRatingMapping, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getGuaranteedStatus()), InduShardBondYieldSpreadCurveDO::getGuaranteedStatus, isEqual(request.getGuaranteedStatus()))
                .and(isNull(request.getGuaranteedStatus()), InduShardBondYieldSpreadCurveDO::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getRemainingTenor()), InduShardBondYieldSpreadCurveDO::getSpreadRemainingTenorTag, isEqual(request.getRemainingTenor()))
                .and(isNull(request.getRemainingTenor()), InduShardBondYieldSpreadCurveDO::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getStartSpreadDate()), InduShardBondYieldSpreadCurveDO::getSpreadDate, greaterThanOrEqual(request.getStartSpreadDate()))
                .and(nonNull(request.getEndSpreadDate()), InduShardBondYieldSpreadCurveDO::getSpreadDate, lessThanOrEqual(request.getEndSpreadDate()))
                .and(nonNull(request.getSpreadDate()), InduShardBondYieldSpreadCurveDO::getSpreadDate, isEqual(request.getSpreadDate()))
                .and(isNotEmpty(request.getIndu2Codes()), InduShardBondYieldSpreadCurveDO::getInduLevelCode, in(request.getIndu2Codes()))
                .and(isNotEmpty(request.getIndu1Codes()), InduShardBondYieldSpreadCurveDO::getInduLevelCode, in(request.getIndu1Codes()))
                .and(nonNull(request.getIndustryCode1()), InduShardBondYieldSpreadCurveDO::getInduLevelCode, isEqual(request.getIndustryCode1()))
                .and(nonNull(request.getIndustryCode2()), InduShardBondYieldSpreadCurveDO::getInduLevelCode, isEqual(request.getIndustryCode2()))
                ;
    }

}
