package com.innodealing.onshore.yieldspread.dao.dmdc;

import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.innodealing.onshore.yieldspread.mapper.dmdc.ComInterestInduHistMapper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.ComInterestInduHistDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.group.ComInterestInduHistGroupDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;
import java.util.Set;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 行业主体利差(老表) DAO
 *
 * <AUTHOR>
 **/
@Repository
public class ComInterestInduHistDAO {

    @Resource
    private ComInterestInduHistMapper comInterestInduHistMapper;

    /**
     * 行业主体数据
     *
     * @param interestDate 利差日期
     * @param comUniCodes  主体code
     * @return 行业主体数据
     */
    public List<ComInterestInduHistDO> listComInterestInduHistDOByInterestDate(Date interestDate, Set<Long> comUniCodes) {
        GroupedQuery<ComInterestInduHistGroupDO, ComInterestInduHistDO> groupedQuery =
                GroupByQuery.createQuery(ComInterestInduHistGroupDO.class, ComInterestInduHistDO.class)
                        .select(ComInterestInduHistDO::getId, ComInterestInduHistDO::getComUniCode,
                                ComInterestInduHistDO::getInterestDate, ComInterestInduHistDO::getIndustryCode1,
                                ComInterestInduHistDO::getIndustryCode2, ComInterestInduHistDO::getIndustryName1,
                                ComInterestInduHistDO::getIndustryName2, ComInterestInduHistDO::getEnterpriseType)
                        .and(ComInterestInduHistGroupDO::getInterestDate, isEqual(interestDate))
                        .and(CollectionUtils.isNotEmpty(comUniCodes), ComInterestInduHistGroupDO::getComUniCode, in(comUniCodes))
                        .groupBy(ComInterestInduHistGroupDO::getComUniCode, ComInterestInduHistGroupDO::getInterestDate);
        return comInterestInduHistMapper.selectByGroupedQuery(groupedQuery);
    }
}
