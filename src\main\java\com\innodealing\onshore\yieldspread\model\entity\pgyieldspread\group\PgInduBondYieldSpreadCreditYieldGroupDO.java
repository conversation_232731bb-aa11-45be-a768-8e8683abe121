package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 产业债收益率计算信用利差中位数组DO
 *
 * <AUTHOR>
 **/
@Table(name = "indu_bond_yield_spread")
public class PgInduBondYieldSpreadCreditYieldGroupDO {
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 债券隐含评级
     */
    @Column
    private Integer bondImpliedRatingMapping;
    /**
     * 1y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 1 THEN bond_credit_spread END)")
    private BigDecimal ytm1Y;
    /**
     * 2y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 2 THEN bond_credit_spread END)")
    private BigDecimal ytm2Y;
    /**
     * 3y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 3 THEN bond_credit_spread END)")
    private BigDecimal ytm3Y;
    /**
     * 4y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 4 THEN bond_credit_spread END)")
    private BigDecimal ytm4Y;
    /**
     * 5y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 5 THEN bond_credit_spread END)")
    private BigDecimal ytm5Y;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Integer getBondImpliedRatingMapping() {
        return bondImpliedRatingMapping;
    }

    public void setBondImpliedRatingMapping(Integer bondImpliedRatingMapping) {
        this.bondImpliedRatingMapping = bondImpliedRatingMapping;
    }

    public BigDecimal getYtm1Y() {
        return ytm1Y;
    }

    public void setYtm1Y(BigDecimal ytm1Y) {
        this.ytm1Y = ytm1Y;
    }

    public BigDecimal getYtm2Y() {
        return ytm2Y;
    }

    public void setYtm2Y(BigDecimal ytm2Y) {
        this.ytm2Y = ytm2Y;
    }

    public BigDecimal getYtm3Y() {
        return ytm3Y;
    }

    public void setYtm3Y(BigDecimal ytm3Y) {
        this.ytm3Y = ytm3Y;
    }

    public BigDecimal getYtm4Y() {
        return ytm4Y;
    }

    public void setYtm4Y(BigDecimal ytm4Y) {
        this.ytm4Y = ytm4Y;
    }

    public BigDecimal getYtm5Y() {
        return ytm5Y;
    }

    public void setYtm5Y(BigDecimal ytm5Y) {
        this.ytm5Y = ytm5Y;
    }
}