package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.service.ComYieldSpreadOneYearService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * (内部)主体利差分析
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 主体利差分析")
@RestController
@RequestMapping("internal/com-yield-spread")
public class InternalComYieldSpreadOneYearController {
    @Resource
    private ComYieldSpreadOneYearService comYieldSpreadOneYearService;

    @ApiOperation(value = "查询主体利差信用利差(全部)数据")
    @PostMapping("/list/com-credit-spreads")
    public List<ComCreditSpreadDTO> listComCreditSpreads(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam Date endDate,
            @ApiParam(name = "comUniCodeList", value = "主体唯一代码集合")
            @RequestBody Set<Long> comUniCodeList) {
        return comYieldSpreadOneYearService.listComCreditSpreads(startDate, endDate, comUniCodeList);
    }

    @ApiOperation(value = "同步一年历史主体利差")
    @GetMapping("/history/sync")
    public int syncHistoryComYieldSpread(@ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
                                         @ApiParam(name = "endDate", value = "结束日期") @RequestParam(required = false) Date endDate) {
        return comYieldSpreadOneYearService.syncHistoryComYieldSpread(startDate, endDate);
    }

    @ApiOperation(value = "同步主体利差,如果日期为空，默认同步昨日数据")
    @GetMapping("/sync")
    public int syncComYieldSpread(@ApiParam(name = "syncDate", value = "同步日期") @RequestParam(required = false) Date syncDate) {
        return comYieldSpreadOneYearService.syncComYieldSpread(syncDate);
    }

    @ApiOperation(value = "清除一年以前数据")
    @GetMapping("/clear")
    public int clearComYieldSpread() {
        return comYieldSpreadOneYearService.clearComYieldSpread();
    }
}
