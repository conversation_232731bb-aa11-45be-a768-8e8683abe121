package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst;
import com.innodealing.onshore.yieldspread.enums.SpreadQuantileTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadTimeRangeEnum;
import com.innodealing.onshore.yieldspread.model.dto.SpreadDateDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondRatingResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTimeRangeDTO;
import com.innodealing.onshore.yieldspread.service.YieldSpreadCommonService;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.SPREAD_DATE_LAST_WORK_DAY_KEY;


/**
 * 利差公共服务
 *
 * <AUTHOR>
 */
@Service
public class YieldSpreadCommonServiceImpl implements YieldSpreadCommonService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final int THREE_MONTH_DAYS = 89;
    private static final int SIX_MONTH_DAYS = 179;

    private static final int SEVEN_DAYS = 7;

    @Resource
    private HolidayService holidayService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public SpreadDateDTO getSpreadDate(Date newSpreadDate) {
        LocalDate localDate = Objects.isNull(newSpreadDate) ? LocalDate.now() : newSpreadDate.toLocalDate();
        Date before90Date = holidayService.lastWorkDay(Date.valueOf(localDate.minusDays(THREE_MONTH_DAYS)));
        Date before180Date = holidayService.lastWorkDay(Date.valueOf(localDate.minusDays(SIX_MONTH_DAYS)));
        return new SpreadDateDTO(Date.valueOf(localDate), before90Date, before180Date);
    }

    @Override
    public List<BondRatingResponseDTO> listBondRatings(String[] ratingNames) {
        if (ArrayUtils.isEmpty(ratingNames)) {
            return Collections.emptyList();
        }
        List<BondRatingResponseDTO> responses = Lists.newArrayList();
        for (String ratingName : ratingNames) {
            Integer ratingMapping = RatingUtils.getRatingMapping(ratingName);
            responses.add(new BondRatingResponseDTO(ratingMapping, ratingName));
        }
        return responses;
    }

    @Override
    public List<YieldSpreadTimeRangeDTO> listTimeRange(LocalDate queryDate){
        String cacheKey = String.format(YieldSpreadCacheConst.SPREAD_TIME_RANGE_KEY, Date.valueOf(queryDate));
        String spreadTimeRangeStr = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(spreadTimeRangeStr)) {
            return JSON.parseArray(spreadTimeRangeStr, YieldSpreadTimeRangeDTO.class);
        }
        List<YieldSpreadTimeRangeDTO> yieldSpreadTimeRangeDTOList = getTimeRangeWithWorkDay(queryDate);
        stringRedisTemplate.opsForValue()
                .set(cacheKey, JSON.toJSONString(yieldSpreadTimeRangeDTOList),getExpirationTimeMillis(), TimeUnit.MILLISECONDS);
        return yieldSpreadTimeRangeDTOList;
    }

    @Override
    public Map<Date, SpreadDateDTO> getSpreadDateMap(@NonNull Date startDate, @NonNull Date endDate) {
        String cacheKey = String.format(SPREAD_DATE_LAST_WORK_DAY_KEY, startDate.toString(), endDate.toString());
        String lastWorkDay = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(lastWorkDay)) {
            List<SpreadDateDTO> spreadDateList = JSON.parseArray(lastWorkDay, SpreadDateDTO.class);
            return spreadDateList.stream().collect(Collectors.toMap(SpreadDateDTO::getNewSpreadDate, Function.identity(), (o, v) -> o));
        }
        long diffDays = DateExtensionUtils.getDiffDays(startDate, endDate);
        Map<Date, SpreadDateDTO> dateMap = Maps.newHashMap();
        for (long i = 0; i <= diffDays; i++) {
            Date date = Date.valueOf(startDate.toLocalDate().plusDays(i));
            SpreadDateDTO spreadDate = this.getSpreadDate(date);
            dateMap.put(date, spreadDate);
        }
        stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(dateMap.values()));
        return dateMap;
    }

    @Override
    public Date getQuantileStartDate(Date issueDate, SpreadQuantileTypeEnum spreadQuantileTypeEnum) {
        int minusYears = spreadQuantileTypeEnum.getYear();
        LocalDate minus3YearStartDate = issueDate.toLocalDate().minusYears(minusYears);
        Date minStartDate = Date.valueOf(minus3YearStartDate.minusDays(SEVEN_DAYS));
        Date workDate = holidayService.latestWorkDay(Date.valueOf(minus3YearStartDate), 0);
        return workDate.before(minStartDate) ? minStartDate : workDate;
    }

    @Override
    public List<YieldSpreadTimeRangeDTO> getTimeRangeWithWorkDay(LocalDate date){
        List<YieldSpreadTimeRangeDTO> yieldSpreadTimeRangeDTOList = Arrays.stream(SpreadTimeRangeEnum.values()).map(x -> {
            Pair<Date, Date> defaultTimeRange = x.getDefaultTimeRange(date);
            YieldSpreadTimeRangeDTO yieldSpreadTimeRangeDTO = new YieldSpreadTimeRangeDTO();
            yieldSpreadTimeRangeDTO.setRangeType(x.getValue());
            yieldSpreadTimeRangeDTO.setStartDate(defaultTimeRange.getLeft());
            yieldSpreadTimeRangeDTO.setEndDate(defaultTimeRange.getRight());
            return yieldSpreadTimeRangeDTO;
        }).collect(Collectors.toList());
        yieldSpreadTimeRangeDTOList.forEach(x -> {
            // 如果前后区间相等, 直接接返回,即使那天是非交易日
            if (Objects.equals(x.getStartDate(), x.getEndDate())) {
                x.setStartDate(x.getStartDate());
                x.setEndDate(x.getEndDate());
                return;
            }
            try {
                Date nextWorkDay = holidayService.getNextWorkDay(x.getStartDate());
                // beforeWorkdays=1 ,不包括自己往前推 (前闭后开)
                Date latestWorkDay = holidayService.lastWorkDay(x.getEndDate());
                if (latestWorkDay.before(nextWorkDay)) {
                    // 范围内都是非工作日, 直接返回原范围 (前闭后开,自行减1)
                    x.setStartDate(x.getStartDate());
                    x.setEndDate(Date.valueOf(x.getEndDate().toLocalDate().minusDays(1)));
                }else {
                    x.setStartDate(nextWorkDay);
                    x.setEndDate(latestWorkDay);
                }
            } catch (Exception e) {
                logger.warn("holidayService_error", e);
            }
        });
        return yieldSpreadTimeRangeDTOList;
    }

    private long getExpirationTimeMillis() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime end = now.plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        return ChronoUnit.MILLIS.between(now, end);
    }
}
