package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


/**
 * 曲线请求DTO-主体利差类型（城投|行业公用）
 *
 * <AUTHOR>
 * @date 2022-09-26
 */
public class CurveComSpreadDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("利差债券类型 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)")
    private Integer spreadBondType;
    @ApiModelProperty("主体唯一编码")
    private Long comUniCode;
    @ApiModelProperty("曲线名称(导出时前端出入)")
    private String curveName;

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

    @Override
    public String toString() {
        return "CurveComSpreadDTO{" +
                "spreadBondType=" + spreadBondType +
                ", comUniCode=" + comUniCode +
                '}';
    }
}
