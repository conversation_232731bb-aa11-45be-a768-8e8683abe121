package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 曲线数据
 *
 * <AUTHOR>
 */
public class CurveResDTO {

    @ApiModelProperty("曲线id")
    private Long curveId;

    @ApiModelProperty("曲线名称")
    private String curveName;

    /**
     * @see com.innodealing.onshore.yieldspread.enums.CurveTypeEnum
     */
    @ApiModelProperty("曲线类型")
    private Integer curveType;

    @ApiModelProperty("曲线中位数分位线")
    private CurvePercentileResDTO medianPercentile;

    @ApiModelProperty("曲线平均数分位线")
    private CurvePercentileResDTO avgPercentile;

    @ApiModelProperty("曲线数据")
    private List<CurveDataResDTO> curveData;

    public Integer getCurveType() {
        return curveType;
    }

    public void setCurveType(Integer curveType) {
        this.curveType = curveType;
    }

    public Long getCurveId() {
        return curveId;
    }

    public void setCurveId(Long curveId) {
        this.curveId = curveId;
    }

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

    public CurvePercentileResDTO getMedianPercentile() {
        return medianPercentile;
    }

    public void setMedianPercentile(CurvePercentileResDTO medianPercentile) {
        this.medianPercentile = medianPercentile;
    }

    public CurvePercentileResDTO getAvgPercentile() {
        return avgPercentile;
    }

    public void setAvgPercentile(CurvePercentileResDTO avgPercentile) {
        this.avgPercentile = avgPercentile;
    }

    public List<CurveDataResDTO> getCurveData() {
        return Objects.isNull(curveData) ? new ArrayList<>() : new ArrayList<>(curveData);
    }

    public void setCurveData(List<CurveDataResDTO> curveData) {
        this.curveData = Objects.isNull(curveData) ? new ArrayList<>() : new ArrayList<>(curveData);
    }

}
