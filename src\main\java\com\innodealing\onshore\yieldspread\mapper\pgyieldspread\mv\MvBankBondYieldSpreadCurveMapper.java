package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvBankBondYieldSpreadCurveParameter;
import org.apache.ibatis.annotations.Param;

/**
 * 银行利差曲线物化视图
 * <AUTHOR>
 */
public interface MvBankBondYieldSpreadCurveMapper extends PgBaseMapper<MvBankBondYieldSpreadCurveParameter> {


    /**
     * 创建物化视图
     * @param parameter 创建条件
     */
    void createMvRatingRouter(@Param("parameter") MvBankBondYieldSpreadCurveParameter parameter);

}
