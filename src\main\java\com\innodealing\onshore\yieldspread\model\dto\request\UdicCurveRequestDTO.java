
package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Date;
import java.util.Objects;

/**
 * 城投利差曲线请求DTO
 *
 * <AUTHOR>
 */
public class UdicCurveRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("利差请求类型, 2. 组合条件, 3. 主体利差, 4. 单券利差")
    private Integer spreadRequestType;
    @ApiModelProperty("利差曲线类型 1. 信用利差，2. 超额利差，3. 估值收益率")
    private Integer spreadCurveType;
    @ApiModelProperty("是否展示国开基准曲线，是|true, 否|false")
    private Boolean displayCdbBenchmarkCurve;
    @ApiModelProperty("开始利差日期,格式:yyyy-MM-dd")
    private Date startSpreadDate;
    @ApiModelProperty("结束利差日期,格式:yyyy-MM-dd")
    private Date endSpreadDate;
    @ApiModelProperty("2. 组合查询")
    private UdicCurveCompositionConditionDTO compositionCondition;
    @ApiModelProperty("3. 主体利差查询")
    private CurveComSpreadDTO comSpread;
    @ApiModelProperty("4. 单券利差查询")
    private CurveBondSpreadDTO bondSpread;

    public Integer getSpreadRequestType() {
        return spreadRequestType;
    }

    public void setSpreadRequestType(Integer spreadRequestType) {
        this.spreadRequestType = spreadRequestType;
    }

    public Integer getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(Integer spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public Boolean getDisplayCdbBenchmarkCurve() {
        return displayCdbBenchmarkCurve;
    }

    public void setDisplayCdbBenchmarkCurve(Boolean displayCdbBenchmarkCurve) {
        this.displayCdbBenchmarkCurve = displayCdbBenchmarkCurve;
    }

    public UdicCurveCompositionConditionDTO getCompositionCondition() {
        return compositionCondition;
    }

    public void setCompositionCondition(UdicCurveCompositionConditionDTO compositionCondition) {
        this.compositionCondition = compositionCondition;
    }

    public CurveComSpreadDTO getComSpread() {
        return comSpread;
    }

    public void setComSpread(CurveComSpreadDTO comSpread) {
        this.comSpread = comSpread;
    }

    public CurveBondSpreadDTO getBondSpread() {
        return bondSpread;
    }

    public void setBondSpread(CurveBondSpreadDTO bondSpread) {
        this.bondSpread = bondSpread;
    }

    public Date getStartSpreadDate() {
        return Objects.isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public void setStartSpreadDate(Date startSpreadDate) {
        this.startSpreadDate = Objects.isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public Date getEndSpreadDate() {
        return Objects.isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public void setEndSpreadDate(Date endSpreadDate) {
        this.endSpreadDate = Objects.isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    @Override
    public String toString() {
        return "UdicCurveRequestDTO{" +
                "spreadRequestType=" + spreadRequestType +
                ", spreadCurveType=" + spreadCurveType +
                ", displayCdbBenchmarkCurve=" + displayCdbBenchmarkCurve +
                ", startSpreadDate=" + startSpreadDate +
                ", endSpreadDate=" + endSpreadDate +
                ", compositionCondition=" + compositionCondition +
                ", comSpread=" + comSpread +
                ", bondSpread=" + bondSpread +
                '}';
    }
}