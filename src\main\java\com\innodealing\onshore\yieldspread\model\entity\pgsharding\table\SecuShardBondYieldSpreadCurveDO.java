package com.innodealing.onshore.yieldspread.model.entity.pgsharding.table;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 证券利差曲线分片表实体
 *
 * <AUTHOR>
 */
@Table(name = "secu_curve")
public class SecuShardBondYieldSpreadCurveDO {

    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;

    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;

    /**
     * 债券信用利差;单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpread;

    /**
     * 债券超额利差;单位(BP)
     */
    @Column
    private BigDecimal bondExcessSpread;

    /**
     * 中债收益率;单位(%)
     */
    @Column
    private BigDecimal cbYield;

    /**
     * 债券信用利差;单位(BP)
     */
    @Column
    private BigDecimal avgBondCreditSpread;

    /**
     * 债券超额利差;单位(BP)
     */
    @Column
    private BigDecimal avgBondExcessSpread;

    /**
     * 中债收益率;单位(%)
     */
    @Column
    private BigDecimal avgCbYield;

    /**
     * 信用利差债券数
     */
    private Integer bondCreditSpreadCount;

    /**
     * 超额利差债券数
     */
    private Integer bondExcessSpreadCount;

    /**
     * 中债利差债券数
     */
    private Integer cbYieldCount;

    /**
     * 利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer securitySeniorityRanking;

    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;

    /**
     * 是否根据利差债券类别进行分组: 0：是，1：否
     */
    @Column
    private Integer usingSecuritySeniorityRanking;

    /**
     * 是否根据利差剩余期限标签进行分组: 0：是，1：否
     */
    @Column
    private Integer usingSpreadRemainingTenorTag;

    public Integer getUsingSecuritySeniorityRanking() {
        return usingSecuritySeniorityRanking;
    }

    public void setUsingSecuritySeniorityRanking(Integer usingSecuritySeniorityRanking) {
        this.usingSecuritySeniorityRanking = usingSecuritySeniorityRanking;
    }

    public Integer getUsingSpreadRemainingTenorTag() {
        return usingSpreadRemainingTenorTag;
    }

    public void setUsingSpreadRemainingTenorTag(Integer usingSpreadRemainingTenorTag) {
        this.usingSpreadRemainingTenorTag = usingSpreadRemainingTenorTag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getCbYieldCount() {
        return cbYieldCount;
    }

    public void setCbYieldCount(Integer cbYieldCount) {
        this.cbYieldCount = cbYieldCount;
    }

    public Integer getSecuritySeniorityRanking() {
        return securitySeniorityRanking;
    }

    public void setSecuritySeniorityRanking(Integer securitySeniorityRanking) {
        this.securitySeniorityRanking = securitySeniorityRanking;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

}
