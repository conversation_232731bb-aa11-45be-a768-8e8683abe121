package com.innodealing.onshore.yieldspread.model.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 金融主体利差
 *
 * <AUTHOR>
 */
public class BaseFinanceComYieldSpreadBO {

    @ApiModelProperty("利差日期")
    private Date spreadDate;

    @ApiModelProperty("主体code")
    private Long comUniCode;

    @ApiModelProperty("主体评级")
    private Integer comExtRatingMapping;

    @ApiModelProperty("所属行业")
    private String induLevel2Name;

    @ApiModelProperty("总资产(万元)")
    private BigDecimal totalAssets;

    @ApiModelProperty("净利润(万元)")
    private BigDecimal netProfit;

    @ApiModelProperty("主体信用利差(普通);单位(BP)")
    private BigDecimal comSeniorCreditSpread;

    @ApiModelProperty("主体超额利差(普通);单位(BP)")
    private BigDecimal comSeniorExcessSpread;

    @ApiModelProperty("主体信用利差(全部债券);单位(BP)")
    private BigDecimal comCreditSpread;

    @ApiModelProperty("主体信用利差 近三月变动(BP)")
    private BigDecimal creditSpreadChange3M;

    @ApiModelProperty("主体信用利差 近六月变动(BP)")
    private BigDecimal creditSpreadChange6M;

    @ApiModelProperty("信用利差3年历史分位")
    private BigDecimal creditSpreadQuantile3Y;

    @ApiModelProperty("信用利差5年历史分位")
    private BigDecimal creditSpreadQuantile5Y;

    @ApiModelProperty("主体信用利差(永续);单位(BP)")
    private BigDecimal comPerpetualCreditSpread;

    @ApiModelProperty("主体超额利差(全部债券);单位(BP)")
    private BigDecimal comExcessSpread;

    @ApiModelProperty("主体超额利差 近三月变动(BP)")
    private BigDecimal excessSpreadChange3M;

    @ApiModelProperty("主体超额利差 近六月变动(BP)")
    private BigDecimal excessSpreadChange6M;

    @ApiModelProperty("超额利差3年历史分位")
    private BigDecimal excessSpreadQuantile3Y;

    @ApiModelProperty("超额利差5年历史分位")
    private BigDecimal excessSpreadQuantile5Y;

    @ApiModelProperty("主体超额利差(永续);单位(BP)")
    private BigDecimal comPerpetualExcessSpread;

    @ApiModelProperty("主体估值收益率(普通);单位(%)")
    private BigDecimal comSeniorCbYield;

    @ApiModelProperty("主体估值收益率(全部债券);单位(%)")
    private BigDecimal comCbYield;

    @ApiModelProperty("主体估值收益率(永续);单位(%)")
    private BigDecimal comPerpetualCbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    public BigDecimal getComSeniorCreditSpread() {
        return comSeniorCreditSpread;
    }

    public void setComSeniorCreditSpread(BigDecimal comSeniorCreditSpread) {
        this.comSeniorCreditSpread = comSeniorCreditSpread;
    }

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getComSeniorExcessSpread() {
        return comSeniorExcessSpread;
    }

    public void setComSeniorExcessSpread(BigDecimal comSeniorExcessSpread) {
        this.comSeniorExcessSpread = comSeniorExcessSpread;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public BigDecimal getComSeniorCbYield() {
        return comSeniorCbYield;
    }

    public void setComSeniorCbYield(BigDecimal comSeniorCbYield) {
        this.comSeniorCbYield = comSeniorCbYield;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }
}
