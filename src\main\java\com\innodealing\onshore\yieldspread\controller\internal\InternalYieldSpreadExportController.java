package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.international.common.template.actuator.EasyExcelTemplateActuator;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.controller.ExportController;
import com.innodealing.onshore.yieldspread.model.dto.request.InduPanoramaExportRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.UdicPanoramaExportRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondBaseDataExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.CurveDataExportRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InduPanoramaExportExcelDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicSpreadPanoramaExportExcelDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.BaseCurveYieldSpreadExcelDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.BondBaseDataExcelDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.CurveExportExcelDTO;
import com.innodealing.onshore.yieldspread.service.ExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * (内部)利差分析导出接口
 *
 * <AUTHOR>
 */
@Api(tags = "(内部)利差分析-导出")
@RestController
@RequestMapping("internal/yield-spread/export")
public class InternalYieldSpreadExportController extends ExportController {
    @Resource
    private ExportService exportService;

    @ApiOperation(value = "行业-导出利差全景图", produces = "application/octet-stream")
    @PostMapping(value = "/indu/panorama")
    public void exportInduPanorama(@RequestBody InduPanoramaExportRequestDTO request) throws IOException {
        List<InduPanoramaExportExcelDTO> panoramaList = exportService.listInduPanoramaExcels(request);
        String fileName = "产业利差全景" + LocalDate.now().toString();
        String title = request.buildExportTitle();
        EasyExcelTemplateActuator.exportForDmByDefaultTemplate(fileName, title, InduPanoramaExportExcelDTO.class, panoramaList, response);
    }

    @ApiOperation(value = "城投-导出利差全景图", produces = "application/octet-stream")
    @PostMapping(value = "/udic/panorama")
    public void udicSpreadPanorama(@ApiParam(name = "requestDTO", value = "城投利差-全景请求参数", required = true)
                                   @RequestBody UdicPanoramaExportRequestDTO request) throws IOException {
        List<UdicSpreadPanoramaExportExcelDTO> panoramaList = exportService.listUdicPanoramasExcels(request);
        String fileName = "城投利差全景" + LocalDate.now().toString();
        String title = request.buildExportTitle();
        EasyExcelTemplateActuator.exportForDmByDefaultTemplate(fileName, title, UdicSpreadPanoramaExportExcelDTO.class, panoramaList, response);
    }

    @ApiOperation("导出债券基础信息")
    @PostMapping("/bond-base-data")
    public void exportBondBaseData(@RequestBody @NotEmpty(message = "债券集合不能为空")
                                   @Size(message = "最多 {max} 只债券，请调整后再导出", max = YieldSpreadConst.IMPORT_BOND_SIZE_UPPER_LIMIT)
                                   List<BondBaseDataExportReqDTO> bondBaseData) throws IOException {
        List<BondBaseDataExcelDTO> bondBaseDataExcelDTOList = BeanCopyUtils.copyList(bondBaseData, BondBaseDataExcelDTO.class);
        String fileName = "自定义曲线样本";
        super.exportExcel(fileName, fileName, bondBaseDataExcelDTOList, BondBaseDataExcelDTO.class);
    }

    @ApiOperation("导出曲线数据")
    @PostMapping("/curve-data")
    public void exportCurveData(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveIds", value = "曲线id") @NotEmpty(message = "请选择曲线") @RequestParam List<Long> curveIds,
            @ApiParam(name = "arithmeticType", value = "曲线算法类型 1：中位数,2：平均数") @NotNull(message = "请选择曲线算法类型") @RequestParam Integer arithmeticType,
            @ApiParam(name = "startDate", value = "起始日期") @NotNull(message = "请选择起始时间") @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @NotNull(message = "请选择结束时间") @RequestParam Date endDate) throws IOException {
        CurveDataExportRequestDTO request = new CurveDataExportRequestDTO();
        request.setCurveIds(curveIds);
        request.setArithmeticType(arithmeticType);
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        List<CurveExportExcelDTO> curveExportExcels = exportService.exportCurveData(userid, request);
        String fileName = YieldSpreadConst.EXPORT_CURVE_PREFIX + LocalDate.now();
        super.exportExcel(fileName, YieldSpreadConst.EXPORT_CURVE_PREFIX, curveExportExcels, CurveExportExcelDTO.class);
    }

    @ApiOperation("导出曲线数据-v2")
    @PostMapping("/curve-data/v2")
    public void exportCurveDataV2(@ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
                                  @RequestBody @Validated CurveDataExportRequestDTO request) throws IOException {
        List<CurveExportExcelDTO> curveExportExcels = exportService.exportCurveData(userid, request);
        String fileName = YieldSpreadConst.EXPORT_CURVE_PREFIX + LocalDate.now();
        super.exportExcel(fileName, YieldSpreadConst.EXPORT_CURVE_PREFIX, curveExportExcels, CurveExportExcelDTO.class);
    }

    @ApiOperation("导出单券利差数据")
    @PostMapping("/single-bond")
    public void exportSingleBondYieldSpread(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveIds", value = "曲线id") @NotEmpty(message = "请选择曲线") @RequestParam List<Long> curveIds,
            @ApiParam(name = "spreadDate", value = "利差日期") @NotNull(message = "请选择利差日期") Date spreadDate) throws IOException {
        Map<String, List<BaseCurveYieldSpreadExcelDTO>> excelDataMap = exportService.exportSingleBondYieldSpread(userid, curveIds, spreadDate);
        String fileName = "单券利差数据" + spreadDate;
        super.exportExcelMultipleSheet(fileName, excelDataMap);
    }

    @ApiOperation("导出主体利差数据")
    @PostMapping("/com")
    public void exportComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveIds", value = "曲线id") @NotEmpty(message = "请选择曲线") @RequestParam List<Long> curveIds,
            @ApiParam(name = "spreadDate", value = "利差日期") @NotNull(message = "请选择利差日期") Date spreadDate) throws IOException {
        Map<String, List<BaseCurveYieldSpreadExcelDTO>> excelDataMap = exportService.exportComYieldSpread(userid, curveIds, spreadDate);
        String fileName = "主体利差数据" + spreadDate;
        super.exportExcelMultipleSheet(fileName, excelDataMap);
    }

}
