package com.innodealing.onshore.yieldspread.model.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 银行债券利差查询参数
 *
 * <AUTHOR>
 */
public class BankYieldSearchParam extends UniversalYieldSpreadSearchParam {

    /**
     * 银行类型
     */
    private List<Integer> bankTypes;

    public List<Integer> getBankTypes() {
        return Objects.isNull(bankTypes) ? new ArrayList<>() : new ArrayList<>(bankTypes);
    }

    public void setBankTypes(List<Integer> bankTypes) {
        this.bankTypes = Objects.isNull(bankTypes) ? new ArrayList<>() : new ArrayList<>(bankTypes);
    }

}
