package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 地方债区域利差折线图数据数据
 *
 * <AUTHOR>
 */
public class YieldLgLineCacheDataBO {

    /**
     * 日期
     */
    private Date[] issueDates;

    /**
     * 数据数组
     */
    private BigDecimal[] yields;

    public Date[] getIssueDates() {
        return Objects.isNull(issueDates) ? new Date[0] : issueDates.clone();
    }

    public void setIssueDates(Date[] issueDates) {
        this.issueDates = Objects.isNull(issueDates) ? new Date[0] : issueDates.clone();
    }

    public BigDecimal[] getYields() {
        return Objects.isNull(yields) ? new BigDecimal[0] : yields.clone();
    }

    public void setYields(BigDecimal[] yields) {
        this.yields = Objects.isNull(yields) ? new BigDecimal[0] : yields.clone();
    }
}
