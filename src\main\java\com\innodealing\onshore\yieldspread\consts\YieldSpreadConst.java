package com.innodealing.onshore.yieldspread.consts;

import com.google.common.collect.Lists;
import com.innodealing.onshore.yieldspread.enums.SpreadCurveTypeEnum;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;

/**
 * 利差常量类
 *
 * <AUTHOR>
 */
@SuppressWarnings("squid:S2386")
public final class YieldSpreadConst {

    private YieldSpreadConst() {
    }

    public static final Long DEFAULT_INDUSTRY_CODE = 999_999L;

    public static final String DEFAULT_INDUSTRY_NAME = "城投";

    public static final String UDIC_TABLE_NAME = "udic_bond_yield_spread";

    /**
     * dm口径城投 逻辑表
     */
    public static final String UDIC_DM_CALIBER_TABLE_NAME = "udic_bond_yield_spread_dm";

    /**
     * 城投区域逻辑表名
     */
    public static final String UDIC_AREA_YIELD_SPREAD_TABLE_NAME = "udic_area_yield_spread";

    public static final String UDIC_COM_TABLE_NAME = "udic_com_yield_spread";

    public static final String INDU_TABLE_NAME = "indu_bond_yield_spread";

    public static final String SECU_TABLE_NAME = "secu_bond_yield_spread";

    public static final String INSU_TABLE_NAME = "insu_bond_yield_spread";

    public static final String BANK_TABLE_NAME = "bank_bond_yield_spread";

    /**
     * 地方债区域利差刷数据物化视图名称
     */
    public static final String LG_MV_NAME = "v_lg_bond_yield_spread_area";

    public static final String LG_BOND_TABLE_NAME = "lg_bond_yield_spread";

    public static final int WORK_THREAD_NUM = 8;

    public static final int SPLIT_MONTH = 6;

    public static final int SHARD_WORK_THREAD_NUM = 8;

    public static final int EXPORT_WORK_THREAD_NUM = 20;

    public static final int YY_RATING_VALUE = 5;

    public static final String NOT_HAS_FIVE_BONDS_MSG = "该方案样本券不足5只无法生成利差曲线";

    public static final String NOT_HAS_BONDS_MSG = "该方案无样本券无法生成利差曲线";

    public static final String SPEND_TIME_LOG = "[{}] spend time [{}] ms";

    public static final int EXPORT_START_PAGE = 1;

    public static final int EXPORT_MAX_ROW = 10000;

    public static final String CURVE_EXPORT_TITLE = "数据来源:DM";

    public static final String COMMA = ",";

    public static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100L);

    /**
     * 曲线起始日期
     */
    public static final Date CURVE_START_DATE = Date.valueOf("2019-01-01");

    /**
     * 基准曲线统一的用户id
     */
    public static final long BENCHMARK_CURVE_USER_ID = 0L;

    /**
     * 用户勾选曲线上限
     */
    public static final int SELECTED_CURVE_UPPER_LIMIT = 10;

    /**
     * 用户保存的曲线上限
     */
    public static final int SAVE_CURVE_UPPER_LIMIT = 200;

    /**
     * 用户正在生成的自定义曲线上限
     */
    public static final int GENERATING_CURVE_UPPER_LIMIT = 5;

    /**
     * 用户自定义曲线导入的债券上限
     */
    public static final int IMPORT_BOND_SIZE_UPPER_LIMIT = 2000;

    /**
     * 解析债券数量上限
     */
    public static final int ANALYSIS_BOND_SIZE_UPPER_LIMIT = 2000;

    /**
     * 自定义曲线名称最大长度
     */
    public static final int CUSTOM_CURVE_NAME_MAX_LENGTH = 20;

    /**
     * 生成曲线的最大并发数
     */
    public static final int GENERATE_CURVE_MAX_CONCURRENCY = 1;

    /**
     * 生成曲线的令牌租赁时间以及pg查询生成自定义曲线的超时时间 毫秒
     */
    public static final int GENERATE_CURVE_PERMIT_LEASE_TIME = 5 * 60 * 1000;

    /**
     * 25百分位
     */
    public static final double PERCENTILE25 = 0.25;

    /**
     * 75百分位
     */
    public static final double PERCENTILE75 = 0.75;

    /**
     * 曲线组名称最大长度
     */
    public static final int GROUP_NAME_MAX_LENGTH = 20;

    /**
     * 用户曲线组添加上限
     */
    public static final int GROUP_COUNT_UPPER_LIMIT = 21;

    /**
     * 默认组组名
     */
    public static final String DEFAULT_GROUP_NAME = "默认组";

    /**
     * 默认组序号
     */
    public static final int DEFAULT_GROUP_ORDER = Integer.MAX_VALUE - 1;

    /**
     * 组序号步长 采用步长的方式，在中间插入时可避免大量无效更新
     */
    public static final int GROUP_ORDER_STEP = 65536;

    /**
     * 曲线序号步长
     */
    public static final int CURVE_ORDER_STEP = 65536;

    /**
     * 批量获取债券利差数量上限
     */
    public static final int BATCH_SEARCH_BOND_YIELD_SPREAD_UPPER_LIMIT = 1000;

    /**
     * 债券利差开始时间
     */
    public static final Date BOND_YIELD_SPREAD_START_DATE = Date.valueOf("2019-01-01");

    /**
     * 国开收益率曲线code
     */
    public static final long CDB_CURVE_UNI_CODE = 101_032L;

    public static final int ONE_YEAR = 365;

    public static final int SIX_DECIMAL_PLACE = 6;

    public static final int FOUR_DECIMAL_PLACE = 4;

    public static final int TWO_DECIMAL_PLACE = 2;

    public static final String EMPTY_PLACEHOLDER = "--";

    public static final String NO_PERMISSIONS_PLACEHOLDER = "**";

    /**
     * 分片请求参数名称  固定值
     */
    public static final String SHARDING_HIND_PARAM = "shardingHindParam";

    /**
     * RDS mysql强制路由到 主库查询的 常量
     */
    public static final String RDS_MYSQL_HINT_FORCE_TO_MASTER = "/*FORCE_MASTER*/";

    /**
     * 数字 int 0
     */
    public static final int INT_ZERO = 0;

    /**
     * 数字 int 1
     */
    public static final int INT_ONE = 1;

    /**
     * 一天
     */
    public static final int ONE_DAY = 1;

    /**
     * 两天
     */
    public static final int TWO_DAYS = 2;

    /**
     * 五年
     */
    public static final int FIVE_YEARS = 5;

    /**
     * 利差曲线数据(导出前缀)
     */
    public static final String EXPORT_CURVE_PREFIX = "利差曲线数据";

    /**
     * 利差曲线数据(曲线类型)
     */
    public static final List<SpreadCurveTypeEnum> CURVE_EXPORT_TYPE_LIST =
            Lists.newArrayList(SpreadCurveTypeEnum.CREDIT_SPREAD, SpreadCurveTypeEnum.EXCESS_SPREAD, SpreadCurveTypeEnum.CB_YIELD_SPREAD);
}
