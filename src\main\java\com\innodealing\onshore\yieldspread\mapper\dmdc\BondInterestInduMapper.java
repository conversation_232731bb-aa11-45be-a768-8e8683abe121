package com.innodealing.onshore.yieldspread.mapper.dmdc;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.BondInterestInduDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.group.BondInterestInduGroupDO;

/**
 * 产业债利差(老表)
 *
 * <AUTHOR>
 **/
public interface BondInterestInduMapper extends SelectByGroupedQueryMapper<BondInterestInduGroupDO, BondInterestInduDO> {
}
