package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.InduBondYieldSpreadGroupDO;

/**
 * 产业债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface InduBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<InduBondYieldSpreadDO,
        InduBondYieldSpreadGroupDO> {

}
