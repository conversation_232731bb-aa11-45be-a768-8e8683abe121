package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 城投发行人yy评级映射标签
 *
 * <AUTHOR>
 */
public enum SpreadUdicComYyRatingMappingTagEnum implements ITextValueEnum {
    /**
     * 投资级
     */
    INVESTMENT(1, new Integer[]{1, 2, 3, 4, 5, 6}, "投资级"),
    /**
     * 投机级
     */
    SPECULATION(2, new Integer[]{7, 8}, "投机级");

    private Integer tag;

    private Integer[] mapping;

    private String text;

    SpreadUdicComYyRatingMappingTagEnum(Integer tag, Integer[] mapping, String text) {
        this.tag = tag;
        this.mapping = mapping.clone();
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return tag;
    }

    public Integer[] getMapping() {
        return Objects.isNull(mapping) ? new Integer[0] : mapping.clone();
    }

    /**
     * 获取yy评级映射
     *
     * @param mapping 评级映射
     * @return 评级映射枚举
     */
    public static Optional<SpreadUdicComYyRatingMappingTagEnum> getComYyRatingEnum(Integer mapping) {
        for (SpreadUdicComYyRatingMappingTagEnum value : values()) {
            if (Arrays.asList(value.getMapping()).contains(mapping)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

}
