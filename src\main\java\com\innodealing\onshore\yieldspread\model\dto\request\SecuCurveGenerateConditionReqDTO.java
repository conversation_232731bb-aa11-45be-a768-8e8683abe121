package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.SecuritySeniorityRankingEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadFinancialBondImpliedRatingMappingEnum;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 证券利差曲线生成条件
 *
 * <AUTHOR>
 */
public class SecuCurveGenerateConditionReqDTO extends AbstractCurveGenerateConditionReqDTO {

    private static final String CURVE_NAME_PREFIX = "证券";

    private static final Map<Integer, String> BOND_TYPE_NAME_MAP = initBondTypeNameMap();

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.SecuritySeniorityRankingEnum
     */
    @ApiModelProperty("债券类型 1:普通,2:次级,3:永续")
    private Integer spreadBondType;

    @Override
    public String getCurveName() {
        return CURVE_NAME_PREFIX +
                super.jointShortName() +
                this.getBondTypeName() +
                super.jointBondImpliedRatingName(SpreadFinancialBondImpliedRatingMappingEnum.class) +
                super.jointRemainingTenorName();

    }

    private String getBondTypeName() {
        return Objects.nonNull(spreadBondType) ? (SEPARATOR + BOND_TYPE_NAME_MAP.get(this.spreadBondType)) : "";
    }

    private static Map<Integer, String> initBondTypeNameMap() {
        Map<Integer, String> bondTypeNameMap = new HashMap<>(SecuritySeniorityRankingEnum.values().length);
        bondTypeNameMap.put(SecuritySeniorityRankingEnum.NORMAL.getValue(), "普通债");
        bondTypeNameMap.put(SecuritySeniorityRankingEnum.SUBORDINATED.getValue(), "次级债");
        bondTypeNameMap.put(SecuritySeniorityRankingEnum.PERPETUA.getValue(), "永续债");
        return bondTypeNameMap;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

}
