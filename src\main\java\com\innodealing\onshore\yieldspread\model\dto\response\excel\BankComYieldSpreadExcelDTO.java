package com.innodealing.onshore.yieldspread.model.dto.response.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.innodealing.international.common.template.source.excel.convert.ExcelDateConvert;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.CURVE_EXPORT_TITLE;

/**
 * 银行主体利差
 *
 * <AUTHOR>
 */
public class BankComYieldSpreadExcelDTO extends BaseCurveYieldSpreadExcelDTO {

    @ApiModelProperty("发行人名称")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "发行人"})
    @ColumnWidth(25)
    private String comUniName;

    @ApiModelProperty("利差日期")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "利差日期"}, converter = ExcelDateConvert.class)
    @ColumnWidth(25)
    private Date spreadDate;

    @ApiModelProperty("主体评级")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "主体评级"})
    @ColumnWidth(25)
    private String comExtRatingMappingStr;

    @ApiModelProperty("所属行业")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "所属行业"})
    @ColumnWidth(25)
    private String induLevel2Name;

    @ApiModelProperty("银行类型 2:国有银行,3:股份制银行,4:城商行,5:农商行")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "银行类型"})
    @ColumnWidth(25)
    private String bankTypeStr;

    @ApiModelProperty("总资产(亿)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "总资产(亿)"})
    @ColumnWidth(25)
    private BigDecimal totalAssets;

    @ApiModelProperty("净利润(亿)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "净利润(亿)"})
    @ColumnWidth(25)
    private BigDecimal netProfit;

    @ApiModelProperty("不良贷款率(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "不良贷款率(%)"})
    @ColumnWidth(25)
    private BigDecimal nplRatio;

    @ApiModelProperty("杠杆率(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "杠杆率(%)"})
    @ColumnWidth(25)
    private BigDecimal leverageRatio;

    @ApiModelProperty("资本充足率(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "资本充足率(%)"})
    @ColumnWidth(25)
    private BigDecimal car;

    @ApiModelProperty("贷款拨备率(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "贷款拨备率(%)"})
    @ColumnWidth(25)
    private BigDecimal loanProvisRatio;

    @ApiModelProperty("信用利差(BP) – 全部债券")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP) – 全部债券"})
    @ColumnWidth(25)
    private BigDecimal comCreditSpread;

    @ApiModelProperty("主体信用利差 近三月变动(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "近3月变动(BP)"})
    @ColumnWidth(25)
    private BigDecimal creditSpreadChange3M;

    @ApiModelProperty("主体信用利差 近六月变动(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "近6月变动(BP)"})
    @ColumnWidth(25)
    private BigDecimal creditSpreadChange6M;

    @ApiModelProperty("信用利差3年历史分位")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "3年历史分位(%)"})
    @ColumnWidth(25)
    private BigDecimal creditSpreadQuantile3Y;

    @ApiModelProperty("信用利差5年历史分位")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "5年历史分位(%)"})
    @ColumnWidth(25)
    private BigDecimal creditSpreadQuantile5Y;

    @ApiModelProperty("超额利差(BP) – 全部债券")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP) – 全部债券"})
    @ColumnWidth(25)
    private BigDecimal comExcessSpread;

    @ApiModelProperty("主体超额利差 近三月变动(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "近3月变动(BP)"})
    @ColumnWidth(25)
    private BigDecimal excessSpreadChange3M;

    @ApiModelProperty("主体超额利差 近六月变动(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "近6月变动(BP)"})
    @ColumnWidth(25)
    private BigDecimal excessSpreadChange6M;

    @ApiModelProperty("超额利差3年历史分位")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "3年历史分位(%)"})
    @ColumnWidth(25)
    private BigDecimal excessSpreadQuantile3Y;

    @ApiModelProperty("超额利差5年历史分位")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "5年历史分位(%)"})
    @ColumnWidth(25)
    private BigDecimal excessSpreadQuantile5Y;

    @ApiModelProperty("信用利差(BP) - 普通债")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP) - 普通债"})
    @ColumnWidth(25)
    private BigDecimal comSeniorCreditSpread;

    @ApiModelProperty("信用利差(BP)-二级资本")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)-二级资本"})
    @ColumnWidth(25)
    private BigDecimal comTier2CreditSpread;

    @ApiModelProperty("信用利差(BP) – 永续")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP) – 永续"})
    @ColumnWidth(25)
    private BigDecimal comPerpetualCreditSpread;

    @ApiModelProperty("超额利差(BP) - 普通债")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP) - 普通债"})
    @ColumnWidth(25)
    private BigDecimal comSeniorExcessSpread;

    @ApiModelProperty("超额利差(BP)-二级资本")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)-二级资本"})
    @ColumnWidth(25)
    private BigDecimal comTier2ExcessSpread;

    @ApiModelProperty("超额利差(BP) – 永续")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP) – 永续"})
    @ColumnWidth(25)
    private BigDecimal comPerpetualExcessSpread;

    @ApiModelProperty("估值收益率–普通债")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率–普通债"})
    @ColumnWidth(25)
    private BigDecimal comSeniorCbYield;

    @ApiModelProperty("估值收益率–全部债券")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率–全部债券"})
    @ColumnWidth(25)
    private BigDecimal comCbYield;

    @ApiModelProperty("估值收益率–二级资本")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率–二级资本"})
    @ColumnWidth(25)
    private BigDecimal comTier2CbYield;

    @ApiModelProperty("估值收益率–永续")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率–永续"})
    @ColumnWidth(25)
    private BigDecimal comPerpetualCbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public String getComExtRatingMappingStr() {
        return comExtRatingMappingStr;
    }

    public void setComExtRatingMappingStr(String comExtRatingMappingStr) {
        this.comExtRatingMappingStr = comExtRatingMappingStr;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }

    public String getBankTypeStr() {
        return bankTypeStr;
    }

    public void setBankTypeStr(String bankTypeStr) {
        this.bankTypeStr = bankTypeStr;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    public BigDecimal getNplRatio() {
        return nplRatio;
    }

    public void setNplRatio(BigDecimal nplRatio) {
        this.nplRatio = nplRatio;
    }

    public BigDecimal getLeverageRatio() {
        return leverageRatio;
    }

    public void setLeverageRatio(BigDecimal leverageRatio) {
        this.leverageRatio = leverageRatio;
    }

    public BigDecimal getCar() {
        return car;
    }

    public void setCar(BigDecimal car) {
        this.car = car;
    }

    public BigDecimal getLoanProvisRatio() {
        return loanProvisRatio;
    }

    public void setLoanProvisRatio(BigDecimal loanProvisRatio) {
        this.loanProvisRatio = loanProvisRatio;
    }

    public BigDecimal getComSeniorCreditSpread() {
        return comSeniorCreditSpread;
    }

    public void setComSeniorCreditSpread(BigDecimal comSeniorCreditSpread) {
        this.comSeniorCreditSpread = comSeniorCreditSpread;
    }

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getComSeniorExcessSpread() {
        return comSeniorExcessSpread;
    }

    public void setComSeniorExcessSpread(BigDecimal comSeniorExcessSpread) {
        this.comSeniorExcessSpread = comSeniorExcessSpread;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComTier2CreditSpread() {
        return comTier2CreditSpread;
    }

    public void setComTier2CreditSpread(BigDecimal comTier2CreditSpread) {
        this.comTier2CreditSpread = comTier2CreditSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComTier2ExcessSpread() {
        return comTier2ExcessSpread;
    }

    public void setComTier2ExcessSpread(BigDecimal comTier2ExcessSpread) {
        this.comTier2ExcessSpread = comTier2ExcessSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public BigDecimal getComSeniorCbYield() {
        return comSeniorCbYield;
    }

    public void setComSeniorCbYield(BigDecimal comSeniorCbYield) {
        this.comSeniorCbYield = comSeniorCbYield;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComTier2CbYield() {
        return comTier2CbYield;
    }

    public void setComTier2CbYield(BigDecimal comTier2CbYield) {
        this.comTier2CbYield = comTier2CbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }
}
