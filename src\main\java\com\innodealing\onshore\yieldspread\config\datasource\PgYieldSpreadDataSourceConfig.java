package com.innodealing.onshore.yieldspread.config.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * GreenPlumDataSourceConfig
 *
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.innodealing.onshore.yieldspread.mapper.pgyieldspread"},
        sqlSessionFactoryRef = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
public class PgYieldSpreadDataSourceConfig extends BaseSourceConfig {

    public static final String TRANSACTION_NAME = "pgyieldspreadTransactionManager";

    private static final String DATA_SOURCE_NAME = "pgyieldspreadDataSource";
    private static final String DATA_SOURCE_PREFIX = "pgyieldspread.datasource";
    public static final String SESSION_FACTORY_NAME = "pgyieldspreadSqlSessionFactory";
    protected static final String[] ALIAS_PACKAGES = {"com.innodealing.onshore.yieldspread.model.entity.pgyieldspread"};
    public static final String TRANSACTION_TEMPLATE_NAME = "pgYieldspreadTransactionTemplate";

    /**
     * 创建数据源
     *
     * @return 返回数据源
     */
    @Bean(name = DATA_SOURCE_NAME, initMethod = "init", destroyMethod = "close")
    @ConfigurationProperties(prefix = DATA_SOURCE_PREFIX)
    public DruidDataSource dataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    /**
     * 配置事务
     *
     * @return 事务
     */
    @Bean(name = TRANSACTION_NAME)
    public DataSourceTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }

    /**
     * 创建SqlSessionFactory对象
     *
     * @param dataSource 数据源
     * @return SqlSessionFactory对象
     * @throws Exception 异常
     */
    @Bean(name = SESSION_FACTORY_NAME)
    public SqlSessionFactory sqlSessionFactory(@Qualifier(DATA_SOURCE_NAME) DataSource dataSource) throws Exception {
        return super.getSessionFactory(dataSource, ALIAS_PACKAGES);
    }

    /**
     * 创建TransactionTemplate对象
     *
     * @param transactionManager 事务管理器
     * @return 事务模板
     */
    @Bean(name = TRANSACTION_TEMPLATE_NAME)
    @Primary
    public TransactionTemplate transactionTemplate(@Qualifier(TRANSACTION_NAME) DataSourceTransactionManager transactionManager) {
        return new TransactionTemplate(transactionManager);
    }
}
