package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 债券利差曲线数据
 *
 * <AUTHOR>
 */
public class BondYieldSpreadResDTO {

    @ApiModelProperty("利差日期")
    private Date spreadDate;

    @ApiModelProperty("利差数据")
    private BigDecimal yieldSpreadValue;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getYieldSpreadValue() {
        return yieldSpreadValue;
    }

    public void setYieldSpreadValue(BigDecimal yieldSpreadValue) {
        this.yieldSpreadValue = yieldSpreadValue;
    }

}
