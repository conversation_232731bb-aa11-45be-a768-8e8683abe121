package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvSecuComYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.MvSecuBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvSecuComYieldSpreadCurveDO;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.persistence.Table;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;

/**
 * 证券利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvSecuComYieldSpreadCurveDAO extends AbstractMvComYieldSpreadCurveDAO<MvSecuBondYieldSpreadCurveParameter> {
    private static final String TABLE_NAME = MvSecuComYieldSpreadCurveDO.class.getAnnotation(Table.class).name();

    private MvSecuComYieldSpreadCurveMapper mvSecuComYieldSpreadCurveMapper;

    /**
     * 构造函数
     *
     * @param mvSecuComYieldSpreadCurveMapper mapper
     */
    protected MvSecuComYieldSpreadCurveDAO(MvSecuComYieldSpreadCurveMapper mvSecuComYieldSpreadCurveMapper) {
        super(mvSecuComYieldSpreadCurveMapper);
        this.mvSecuComYieldSpreadCurveMapper = mvSecuComYieldSpreadCurveMapper;
    }

    @Override
    public List<ComYieldSpreadCurveBO> list(@NonNull Long comUniCode, Integer securitySeniorityRanking) {
        DynamicQuery<MvSecuComYieldSpreadCurveDO> query = DynamicQuery.createQuery(MvSecuComYieldSpreadCurveDO.class)
                .and(MvSecuComYieldSpreadCurveDO::getComUniCode, x -> x.isEqual(comUniCode))
                .and(Objects.nonNull(securitySeniorityRanking), MvSecuComYieldSpreadCurveDO::getSecuritySeniorityRanking, x -> x.isEqual(securitySeniorityRanking))
                .and(Objects.isNull(securitySeniorityRanking), MvSecuComYieldSpreadCurveDO::getUsingSecuritySeniorityRanking, x -> x.isEqual(UNUSED_FIELD_GROUP.getValue()))
                .orderBy(MvSecuComYieldSpreadCurveDO::getSpreadDate, SortDirections::asc);
        List<MvSecuComYieldSpreadCurveDO> mvSecuComYieldSpreadCurveList = mvSecuComYieldSpreadCurveMapper.selectByDynamicQuery(query);
        return mvSecuComYieldSpreadCurveList.stream().map(super::handlePrecision).collect(Collectors.toList());
    }

    @Override
    protected String tableName() {
        return TABLE_NAME;
    }
}
