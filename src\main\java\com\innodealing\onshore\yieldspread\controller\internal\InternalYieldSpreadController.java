package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.service.CalcYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * (内部)利差分析
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 利差分析")
@RestController
@RequestMapping("internal/yieldSpread")
public class InternalYieldSpreadController {

    @Resource
    private CalcYieldSpreadService calcYieldSpreadService;

    @ApiOperation(value = "根据日期计算行业利差(债券,主体)（补充债） (不传日期默认跑前一天的数据)")
    @PostMapping("/supplementCalcInduBondYieldSpreadsBySpreadDate")
    public int supplementCalcInduBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList,
            @RequestParam(defaultValue = "false") Boolean isEnableOldData) {
        return calcYieldSpreadService.supplementCalcInduBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList, isEnableOldData);
    }

    @ApiOperation(value = "根据日期计算行业利差(债券,主体) (不传日期默认跑前一天的数据)")
    @PostMapping("/calcInduBondYieldSpreadsBySpreadDate")
    public int calcInduBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList) {
        return calcYieldSpreadService.calcInduBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList, false);
    }

    @ApiOperation(value = "根据日期计算城投利差(债券,主体)(不传日期默认跑前一天的数据)")
    @PostMapping("/calcUdicBondYieldSpreadsBySpreadDate")
    public int calcUdicBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList,
            @ApiParam(name = "shardingHindParam", value = "分片参数")
            @RequestParam(required = false) String shardingHindParam) {
        return calcYieldSpreadService.calcUdicBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList, false);
    }

    @ApiOperation(value = "根据日期计算主体利差变动(不传日期默认跑前一天的数据)")
    @PostMapping("/calcComYieldSpreadChangeBySpreadDate")
    public int calcComYieldSpreadChangeBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "comUniCodeList", value = "主体唯一代码集合")
            @RequestParam(required = false) Set<Long> comUniCodeList) {
        return calcYieldSpreadService.calcComYieldSpreadChangeBySpreadDate(startDate, endDate, comUniCodeList);
    }

    @ApiOperation(value = "根据日期计算保留最新一天主体利差变动")
    @PostMapping("/calcRecentComYieldSpreadChange")
    public void calcRecentComYieldSpreadChange() {
        calcYieldSpreadService.calcRecentComYieldSpreadChange();
    }

    @ApiOperation(value = "根据日期计算证券主体利差变动(不传日期默认跑前一天的数据)")
    @PostMapping("/calcSecuComYieldSpreadChangeBySpreadDate")
    public int calcSecuComYieldSpreadChangeBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "comUniCodeList", value = "主体唯一代码集合")
            @RequestParam(required = false) Set<Long> comUniCodeList) {
        return calcYieldSpreadService.calcSecuComYieldSpreadChangeBySpreadDate(startDate, endDate, comUniCodeList);
    }

    @ApiOperation(value = "根据日期计算证券利差(债券,主体) (不传日期默认跑前一天的数据)")
    @PostMapping("/calcSecuBondYieldSpreadsBySpreadDate")
    public int calcSecuBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList) {
        return calcYieldSpreadService.calcSecuBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList);
    }

    @ApiOperation(value = "根据日期计算保险利差(债券,主体) (不传日期默认跑前一天的数据)")
    @PostMapping("/calcInsuBondYieldSpreadsBySpreadDate")
    public int calcInsuBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList) {
        return calcYieldSpreadService.calcInsuBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList);
    }

    @ApiOperation(value = "根据日期计算银行利差(债券,主体) (不传日期默认跑前一天的数据)")
    @PostMapping("/calc-bank-bond-yield-spreads-by-spreadDate")
    public int calcBankBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList) {
        return calcYieldSpreadService.calcBankBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList);
    }

    @ApiOperation(value = "根据日期计算债券利差(任何一个日期为空都会默认跑前一天的数据)")
    @PostMapping("/calc-bond-yield-spread")
    public int calcBondYieldSpread(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodes", value = "债券唯一代码集合") @RequestParam(required = false) List<Long> bondUniCodes) {
        return calcYieldSpreadService.calcBondYieldSpread(startDate, endDate, bondUniCodes);
    }

}
