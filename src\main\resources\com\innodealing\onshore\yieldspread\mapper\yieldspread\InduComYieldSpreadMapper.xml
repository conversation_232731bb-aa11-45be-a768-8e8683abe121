<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.yieldspread.InduComYieldSpreadMapper">

    <select id="getComYieldSpreadPaging"
            resultType="com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO">
        SELECT
        id AS id,
        com_private_cb_yield AS com_private_cb_yield,
        total_assets AS total_assets,
        com_public_excess_spread AS com_public_excess_spread,
        com_perpetual_excess_spread AS com_perpetual_excess_spread,
        net_profit AS net_profit,
        com_perpetual_cb_yield AS com_perpetual_cb_yield,
        indu_level1_name AS indu_level1_name,
        indu_level2_code AS indu_level2_code,
        com_public_cb_yield AS com_public_cb_yield,
        bond_balance AS bond_balance,
        com_private_excess_spread AS com_private_excess_spread,
        indu_level2_name AS indu_level2_name,
        com_credit_spread AS com_credit_spread,
        asset_liability_ratio AS asset_liability_ratio,
        indu_level1_code AS indu_level1_code,
        business_nature AS business_nature,
        com_public_credit_spread AS com_public_credit_spread,
        com_cb_yield AS com_cb_yield,
        spread_date AS spread_date,
        com_private_credit_spread AS com_private_credit_spread,
        deleted AS deleted,
        com_excess_spread AS com_excess_spread,
        com_uni_code AS com_uni_code,
        com_perpetual_credit_spread AS com_perpetual_credit_spread,
        com_ext_rating_mapping AS com_ext_rating_mapping,
        net_operating_cash_flow AS net_operating_cash_flow
        FROM indu_com_yield_spread
        INNER JOIN
        (select id from indu_com_yield_spread
        WHERE (com_uni_code IN
        <foreach collection="comUniCodes" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        AND spread_date = #{spreadDate} AND deleted = 0)
        order by ${sortProperty} ${sortDirection} limit #{startIndex},#{pageSize}) as limt USING(id)
    </select>


    <select id="getComYieldSpreadPagingByJoin"
            resultType="com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO">
        SELECT indu_com_yield_spread.id, indu_com_yield_spread.com_private_cb_yield AS com_private_cb_yield,
        indu_com_yield_spread.total_assets AS total_assets,
        indu_com_yield_spread.com_public_excess_spread AS com_public_excess_spread,
        indu_com_yield_spread.com_perpetual_excess_spread AS com_perpetual_excess_spread,
        indu_com_yield_spread.net_profit AS net_profit,
        indu_com_yield_spread.com_perpetual_cb_yield AS com_perpetual_cb_yield,
        indu_com_yield_spread.indu_level1_name AS indu_level1_name,
        indu_com_yield_spread.indu_level2_code AS indu_level2_code,
        indu_com_yield_spread.com_public_cb_yield AS com_public_cb_yield,
        indu_com_yield_spread.bond_balance AS bond_balance,
        indu_com_yield_spread.com_private_excess_spread AS com_private_excess_spread,
        indu_com_yield_spread.indu_level2_name AS indu_level2_name,
        indu_com_yield_spread.com_credit_spread AS com_credit_spread,
        indu_com_yield_spread.asset_liability_ratio AS asset_liability_ratio,
        indu_com_yield_spread.indu_level1_code AS indu_level1_code,
        indu_com_yield_spread.business_nature AS business_nature,
        indu_com_yield_spread.com_public_credit_spread AS com_public_credit_spread,
        indu_com_yield_spread.com_cb_yield AS com_cb_yield,
        indu_com_yield_spread.spread_date AS spread_date,
        indu_com_yield_spread.com_private_credit_spread AS com_private_credit_spread,
        indu_com_yield_spread.deleted AS deleted,
        indu_com_yield_spread.com_excess_spread AS com_excess_spread,
        indu_com_yield_spread.com_uni_code AS com_uni_code,
        indu_com_yield_spread.com_perpetual_credit_spread AS com_perpetual_credit_spread,
        indu_com_yield_spread.com_ext_rating_mapping AS com_ext_rating_mapping,
        indu_com_yield_spread.net_operating_cash_flow AS net_operating_cash_flow
        FROM indu_com_yield_spread
        INNER JOIN (
        select distinct com_uni_code from indu_bond_yield_spread_${parameter.year}
        where spread_date = #{parameter.spreadDate}
        <if test="parameter.comUniCodes != null and parameter.comUniCodes.length > 0">
            AND com_uni_code in
            <foreach collection="parameter.comUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.bondUniCodes != null and parameter.bondUniCodes.length > 0">
            AND bond_uni_code in
            <foreach collection="parameter.bondUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.industryCode1 != null">
            AND indu_level1_code = #{parameter.industryCode1}
        </if>
        <if test="parameter.industryCode2 != null">
            AND indu_level2_code = #{parameter.industryCode2}
        </if>
        <if test="parameter.spreadBondType != null">
            AND spread_bond_type = #{parameter.spreadBondType}
        </if>
        <if test="parameter.bondExtRatingMapping != null">
            AND bond_ext_rating_mapping = #{parameter.bondExtRatingMapping}
        </if>
        <if test="parameter.bondImpliedRatingMappings != null and parameter.bondImpliedRatingMappings.length > 0">
            AND bond_implied_rating_mapping in
            <foreach collection="parameter.bondImpliedRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.comYyRatingMappings != null and parameter.comYyRatingMappings.length > 0">
            AND com_yy_rating_mapping in
            <foreach collection="parameter.comYyRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.businessFilterNatures != null and parameter.businessFilterNatures.length > 0">
            AND business_filter_nature in
            <foreach collection="parameter.businessFilterNatures" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.spreadRemainingTenorTag != null">
            AND spread_remaining_tenor_tag = #{parameter.spreadRemainingTenorTag}
        </if>
        <if test="parameter.guaranteeStatus != null">
            AND guaranteed_status = #{parameter.guaranteeStatus}
        </if>
        <if test="parameter.comUniCode != null">
            AND com_uni_code = #{parameter.comUniCode}
        </if>
        <if test="parameter.bondUniCode != null">
            AND bond_uni_code = #{parameter.bondUniCode}
        </if>
        ) tmp
        on indu_com_yield_spread.com_uni_code = tmp.com_uni_code
        WHERE (indu_com_yield_spread.spread_date = #{parameter.spreadDate} AND indu_com_yield_spread.deleted = 0)
        order by ${parameter.propertyName} ${parameter.sortDirection}
        limit #{parameter.startIndex},#{parameter.pageSize}
    </select>

    <select id="getComYieldSpreadChangePagingByJoin"
            resultType="com.innodealing.onshore.yieldspread.model.view.InduComYieldSpreadView">
        SELECT indu_com_yield_spread.id, indu_com_yield_spread.com_private_cb_yield AS com_private_cb_yield,
        indu_com_yield_spread.total_assets AS total_assets,
        indu_com_yield_spread.com_public_excess_spread AS com_public_excess_spread,
        indu_com_yield_spread.com_perpetual_excess_spread AS com_perpetual_excess_spread,
        indu_com_yield_spread.net_profit AS net_profit,
        indu_com_yield_spread.com_perpetual_cb_yield AS com_perpetual_cb_yield,
        indu_com_yield_spread.indu_level1_name AS indu_level1_name,
        indu_com_yield_spread.indu_level2_code AS indu_level2_code,
        indu_com_yield_spread.com_public_cb_yield AS com_public_cb_yield,
        indu_com_yield_spread.bond_balance AS bond_balance,
        indu_com_yield_spread.com_private_excess_spread AS com_private_excess_spread,
        indu_com_yield_spread.indu_level2_name AS indu_level2_name,
        indu_com_yield_spread.com_credit_spread AS com_credit_spread,
        indu_com_yield_spread.asset_liability_ratio AS asset_liability_ratio,
        indu_com_yield_spread.indu_level1_code AS indu_level1_code,
        indu_com_yield_spread.business_nature AS business_nature,
        indu_com_yield_spread.com_public_credit_spread AS com_public_credit_spread,
        indu_com_yield_spread.com_cb_yield AS com_cb_yield,
        indu_com_yield_spread.spread_date AS spread_date,
        indu_com_yield_spread.com_private_credit_spread AS com_private_credit_spread,
        indu_com_yield_spread.deleted AS deleted,
        indu_com_yield_spread.com_excess_spread AS com_excess_spread,
        indu_com_yield_spread.com_uni_code AS com_uni_code,
        indu_com_yield_spread.com_perpetual_credit_spread AS com_perpetual_credit_spread,
        indu_com_yield_spread.com_ext_rating_mapping AS com_ext_rating_mapping,
        indu_com_yield_spread.net_operating_cash_flow AS net_operating_cash_flow,
        com_yield_spread_change.credit_spread_change_3m AS credit_spread_change_3m,
        com_yield_spread_change.credit_spread_change_6m AS credit_spread_change_6m,
        com_yield_spread_change.excess_spread_change_3m AS excess_spread_change_3m,
        com_yield_spread_change.excess_spread_change_6m AS excess_spread_change_6m
        FROM indu_com_yield_spread
        INNER JOIN (
        select distinct com_uni_code from indu_bond_yield_spread_${parameter.year}
        where spread_date = #{parameter.spreadDate}
        <if test="parameter.comUniCodes != null and parameter.comUniCodes.length > 0">
            AND com_uni_code in
            <foreach collection="parameter.comUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.bondUniCodes != null and parameter.bondUniCodes.length > 0">
            AND bond_uni_code in
            <foreach collection="parameter.bondUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.industryCode1 != null">
            AND indu_level1_code = #{parameter.industryCode1}
        </if>
        <if test="parameter.industryCode2 != null">
            AND indu_level2_code = #{parameter.industryCode2}
        </if>
        <if test="parameter.spreadBondType != null">
            AND spread_bond_type = #{parameter.spreadBondType}
        </if>
        <if test="parameter.bondExtRatingMapping != null">
            AND bond_ext_rating_mapping = #{parameter.bondExtRatingMapping}
        </if>
        <if test="parameter.bondImpliedRatingMappings != null and parameter.bondImpliedRatingMappings.length > 0">
            AND bond_implied_rating_mapping in
            <foreach collection="parameter.bondImpliedRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.comYyRatingMappings != null and parameter.comYyRatingMappings.length > 0">
            AND com_yy_rating_mapping in
            <foreach collection="parameter.comYyRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.businessFilterNatures != null and parameter.businessFilterNatures.length > 0">
            AND business_filter_nature in
            <foreach collection="parameter.businessFilterNatures" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.spreadRemainingTenorTag != null">
            AND spread_remaining_tenor_tag = #{parameter.spreadRemainingTenorTag}
        </if>
        <if test="parameter.guaranteeStatus != null">
            AND guaranteed_status = #{parameter.guaranteeStatus}
        </if>
        <if test="parameter.comUniCode != null">
            AND com_uni_code = #{parameter.comUniCode}
        </if>
        <if test="parameter.bondUniCode != null">
            AND bond_uni_code = #{parameter.bondUniCode}
        </if>
        ) tmp
        on indu_com_yield_spread.com_uni_code = tmp.com_uni_code
        LEFT JOIN com_yield_spread_change ON indu_com_yield_spread.spread_date = com_yield_spread_change.spread_date
        AND indu_com_yield_spread.com_uni_code = com_yield_spread_change.com_uni_code
        and com_yield_spread_change.com_spread_sector = 2
        WHERE (indu_com_yield_spread.spread_date = #{parameter.spreadDate} AND indu_com_yield_spread.deleted = 0)
        <if test="parameter.propertyName != null and parameter.propertyName != ''">
            <choose>
                <when test="parameter.propertyName == 'credit_spread_change_3m'">
                    order by com_yield_spread_change.${parameter.propertyName} ${parameter.sortDirection}
                </when>
                <when test="parameter.propertyName == 'credit_spread_change_6m'">
                    order by com_yield_spread_change.${parameter.propertyName} ${parameter.sortDirection}
                </when>
                <when test="parameter.propertyName == 'excess_spread_change_3m'">
                    order by com_yield_spread_change.${parameter.propertyName} ${parameter.sortDirection}
                </when>
                <when test="parameter.propertyName == 'excess_spread_change_6m'">
                    order by com_yield_spread_change.${parameter.propertyName} ${parameter.sortDirection}
                </when>
                <otherwise>
                    order by indu_com_yield_spread.${parameter.propertyName} ${parameter.sortDirection}
                </otherwise>
            </choose>
        </if>
        limit #{parameter.startIndex},#{parameter.pageSize}
    </select>

    <select id="getComYieldSpreadPagingCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM indu_com_yield_spread
        INNER JOIN (
        select distinct com_uni_code from indu_bond_yield_spread_${parameter.year}
        where spread_date = #{parameter.spreadDate}
        <if test="parameter.comUniCodes != null and parameter.comUniCodes.length > 0">
            AND com_uni_code in
            <foreach collection="parameter.comUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.bondUniCodes != null and parameter.bondUniCodes.length > 0">
            AND bond_uni_code in
            <foreach collection="parameter.bondUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.industryCode1 != null">
            AND indu_level1_code = #{parameter.industryCode1}
        </if>
        <if test="parameter.industryCode2 != null">
            AND indu_level2_code = #{parameter.industryCode2}
        </if>
        <if test="parameter.spreadBondType != null">
            AND spread_bond_type = #{parameter.spreadBondType}
        </if>
        <if test="parameter.bondExtRatingMapping != null">
            AND bond_ext_rating_mapping = #{parameter.bondExtRatingMapping}
        </if>
        <if test="parameter.bondImpliedRatingMappings != null and parameter.bondImpliedRatingMappings.length > 0">
            AND bond_implied_rating_mapping in
            <foreach collection="parameter.bondImpliedRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.comYyRatingMappings != null and parameter.comYyRatingMappings.length > 0">
            AND com_yy_rating_mapping in
            <foreach collection="parameter.comYyRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.businessFilterNatures != null and parameter.businessFilterNatures.length > 0">
            AND business_filter_nature in
            <foreach collection="parameter.businessFilterNatures" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.spreadRemainingTenorTag != null">
            AND spread_remaining_tenor_tag = #{parameter.spreadRemainingTenorTag}
        </if>
        <if test="parameter.guaranteeStatus != null">
            AND guaranteed_status = #{parameter.guaranteeStatus}
        </if>
        <if test="parameter.comUniCode != null">
            AND com_uni_code = #{parameter.comUniCode}
        </if>
        <if test="parameter.bondUniCode != null">
            AND bond_uni_code = #{parameter.bondUniCode}
        </if>
        ) tmp
        on indu_com_yield_spread.com_uni_code = tmp.com_uni_code
        WHERE (indu_com_yield_spread.spread_date = #{parameter.spreadDate} AND indu_com_yield_spread.deleted = 0)
    </select>

    <select id="getComYieldSpreadPagingByExists"
            resultType="com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO">
        SELECT indu_com_yield_spread.id, indu_com_yield_spread.com_private_cb_yield AS com_private_cb_yield,
        indu_com_yield_spread.total_assets AS total_assets,
        indu_com_yield_spread.com_public_excess_spread AS com_public_excess_spread,
        indu_com_yield_spread.com_perpetual_excess_spread AS com_perpetual_excess_spread,
        indu_com_yield_spread.net_profit AS net_profit,
        indu_com_yield_spread.com_perpetual_cb_yield AS com_perpetual_cb_yield,
        indu_com_yield_spread.indu_level1_name AS indu_level1_name,
        indu_com_yield_spread.indu_level2_code AS indu_level2_code,
        indu_com_yield_spread.com_public_cb_yield AS com_public_cb_yield,
        indu_com_yield_spread.bond_balance AS bond_balance,
        indu_com_yield_spread.com_private_excess_spread AS com_private_excess_spread,
        indu_com_yield_spread.indu_level2_name AS indu_level2_name,
        indu_com_yield_spread.com_credit_spread AS com_credit_spread,
        indu_com_yield_spread.asset_liability_ratio AS asset_liability_ratio,
        indu_com_yield_spread.indu_level1_code AS indu_level1_code,
        indu_com_yield_spread.business_nature AS business_nature,
        indu_com_yield_spread.com_public_credit_spread AS com_public_credit_spread,
        indu_com_yield_spread.com_cb_yield AS com_cb_yield,
        indu_com_yield_spread.spread_date AS spread_date,
        indu_com_yield_spread.com_private_credit_spread AS com_private_credit_spread,
        indu_com_yield_spread.deleted AS deleted,
        indu_com_yield_spread.com_excess_spread AS com_excess_spread,
        indu_com_yield_spread.com_uni_code AS com_uni_code,
        indu_com_yield_spread.com_perpetual_credit_spread AS com_perpetual_credit_spread,
        indu_com_yield_spread.com_ext_rating_mapping AS com_ext_rating_mapping,
        indu_com_yield_spread.net_operating_cash_flow AS net_operating_cash_flow
        FROM indu_com_yield_spread
        where spread_date = #{parameter.spreadDate}
        AND exists (select 1 from indu_bond_yield_spread_${parameter.year}
        where spread_date = #{parameter.spreadDate}
        and com_uni_code = indu_com_yield_spread.com_uni_code
        <if test="parameter.comUniCodes != null and parameter.comUniCodes.length > 0">
            AND com_uni_code in
            <foreach collection="parameter.comUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.bondUniCodes != null and parameter.bondUniCodes.length > 0">
            AND bond_uni_code in
            <foreach collection="parameter.bondUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.industryCode1 != null">
            AND indu_level1_code = #{parameter.industryCode1}
        </if>
        <if test="parameter.industryCode2 != null">
            AND indu_level2_code = #{parameter.industryCode2}
        </if>
        <if test="parameter.spreadBondType != null">
            AND spread_bond_type = #{parameter.spreadBondType}
        </if>
        <if test="parameter.bondExtRatingMapping != null">
            AND bond_ext_rating_mapping = #{parameter.bondExtRatingMapping}
        </if>
        <if test="parameter.bondImpliedRatingMappings != null and parameter.bondImpliedRatingMappings.length > 0">
            AND bond_implied_rating_mapping in
            <foreach collection="parameter.bondImpliedRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.comYyRatingMappings != null and parameter.comYyRatingMappings.length > 0">
            AND com_yy_rating_mapping in
            <foreach collection="parameter.comYyRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.businessFilterNatures != null and parameter.businessFilterNatures.length > 0">
            AND business_filter_nature in
            <foreach collection="parameter.businessFilterNatures" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.spreadRemainingTenorTag != null">
            AND spread_remaining_tenor_tag = #{parameter.spreadRemainingTenorTag}
        </if>
        <if test="parameter.guaranteeStatus != null">
            AND guaranteed_status = #{parameter.guaranteeStatus}
        </if>
        <if test="parameter.comUniCode != null">
            AND com_uni_code = #{parameter.comUniCode}
        </if>
        <if test="parameter.bondUniCode != null">
            AND bond_uni_code = #{parameter.bondUniCode}
        </if>)
        order by ${parameter.propertyName} ${parameter.sortDirection}
        limit #{parameter.startIndex},#{parameter.pageSize}
    </select>

    <select id="getComYieldSpreadChangePagingByExists"
            resultType="com.innodealing.onshore.yieldspread.model.view.InduComYieldSpreadView">
        SELECT indu_com_yield_spread.id, indu_com_yield_spread.com_private_cb_yield AS com_private_cb_yield,
        indu_com_yield_spread.total_assets AS total_assets,
        indu_com_yield_spread.com_public_excess_spread AS com_public_excess_spread,
        indu_com_yield_spread.com_perpetual_excess_spread AS com_perpetual_excess_spread,
        indu_com_yield_spread.net_profit AS net_profit,
        indu_com_yield_spread.com_perpetual_cb_yield AS com_perpetual_cb_yield,
        indu_com_yield_spread.indu_level1_name AS indu_level1_name,
        indu_com_yield_spread.indu_level2_code AS indu_level2_code,
        indu_com_yield_spread.com_public_cb_yield AS com_public_cb_yield,
        indu_com_yield_spread.bond_balance AS bond_balance,
        indu_com_yield_spread.com_private_excess_spread AS com_private_excess_spread,
        indu_com_yield_spread.indu_level2_name AS indu_level2_name,
        indu_com_yield_spread.com_credit_spread AS com_credit_spread,
        indu_com_yield_spread.asset_liability_ratio AS asset_liability_ratio,
        indu_com_yield_spread.indu_level1_code AS indu_level1_code,
        indu_com_yield_spread.business_nature AS business_nature,
        indu_com_yield_spread.com_public_credit_spread AS com_public_credit_spread,
        indu_com_yield_spread.com_cb_yield AS com_cb_yield,
        indu_com_yield_spread.spread_date AS spread_date,
        indu_com_yield_spread.com_private_credit_spread AS com_private_credit_spread,
        indu_com_yield_spread.deleted AS deleted,
        indu_com_yield_spread.com_excess_spread AS com_excess_spread,
        indu_com_yield_spread.com_uni_code AS com_uni_code,
        indu_com_yield_spread.com_perpetual_credit_spread AS com_perpetual_credit_spread,
        indu_com_yield_spread.com_ext_rating_mapping AS com_ext_rating_mapping,
        indu_com_yield_spread.net_operating_cash_flow AS net_operating_cash_flow,
        com_yield_spread_change.credit_spread_change_3m AS credit_spread_change_3m,
        com_yield_spread_change.credit_spread_change_6m AS credit_spread_change_6m,
        com_yield_spread_change.excess_spread_change_3m AS excess_spread_change_3m,
        com_yield_spread_change.excess_spread_change_6m AS excess_spread_change_6m
        FROM indu_com_yield_spread
        LEFT JOIN com_yield_spread_change ON indu_com_yield_spread.spread_date = com_yield_spread_change.spread_date
        AND indu_com_yield_spread.com_uni_code = com_yield_spread_change.com_uni_code
        and com_yield_spread_change.com_spread_sector = 2
        where indu_com_yield_spread.spread_date = #{parameter.spreadDate}
        AND exists (select 1 from indu_bond_yield_spread_${parameter.year}
        where spread_date = #{parameter.spreadDate}
        and com_uni_code = indu_com_yield_spread.com_uni_code
        <if test="parameter.comUniCodes != null and parameter.comUniCodes.length > 0">
            AND com_uni_code in
            <foreach collection="parameter.comUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.bondUniCodes != null and parameter.bondUniCodes.length > 0">
            AND bond_uni_code in
            <foreach collection="parameter.bondUniCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.industryCode1 != null">
            AND indu_level1_code = #{parameter.industryCode1}
        </if>
        <if test="parameter.industryCode2 != null">
            AND indu_level2_code = #{parameter.industryCode2}
        </if>
        <if test="parameter.spreadBondType != null">
            AND spread_bond_type = #{parameter.spreadBondType}
        </if>
        <if test="parameter.bondExtRatingMapping != null">
            AND bond_ext_rating_mapping = #{parameter.bondExtRatingMapping}
        </if>
        <if test="parameter.bondImpliedRatingMappings != null and parameter.bondImpliedRatingMappings.length > 0">
            AND bond_implied_rating_mapping in
            <foreach collection="parameter.bondImpliedRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.comYyRatingMappings != null and parameter.comYyRatingMappings.length > 0">
            AND com_yy_rating_mapping in
            <foreach collection="parameter.comYyRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.businessFilterNatures != null and parameter.businessFilterNatures.length > 0">
            AND business_filter_nature in
            <foreach collection="parameter.businessFilterNatures" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.spreadRemainingTenorTag != null">
            AND spread_remaining_tenor_tag = #{parameter.spreadRemainingTenorTag}
        </if>
        <if test="parameter.guaranteeStatus != null">
            AND guaranteed_status = #{parameter.guaranteeStatus}
        </if>
        <if test="parameter.comUniCode != null">
            AND com_uni_code = #{parameter.comUniCode}
        </if>
        <if test="parameter.bondUniCode != null">
            AND bond_uni_code = #{parameter.bondUniCode}
        </if>)
        <if test="parameter.propertyName != null and parameter.propertyName != ''">
            <choose>
                <when test="parameter.propertyName == 'credit_spread_change_3m'">
                    order by com_yield_spread_change.${parameter.propertyName} ${parameter.sortDirection}
                </when>
                <when test="parameter.propertyName == 'credit_spread_change_6m'">
                    order by com_yield_spread_change.${parameter.propertyName} ${parameter.sortDirection}
                </when>
                <when test="parameter.propertyName == 'excess_spread_change_3m'">
                    order by com_yield_spread_change.${parameter.propertyName} ${parameter.sortDirection}
                </when>
                <when test="parameter.propertyName == 'excess_spread_change_6m'">
                    order by com_yield_spread_change.${parameter.propertyName} ${parameter.sortDirection}
                </when>
                <otherwise>
                    order by indu_com_yield_spread.${parameter.propertyName} ${parameter.sortDirection}
                </otherwise>
            </choose>
        </if>
        limit #{parameter.startIndex},#{parameter.pageSize}
    </select>

    <select id="listComYieldSpreads" resultType="com.innodealing.onshore.yieldspread.model.view.InduComYieldSpreadView">
        SELECT
        <if test="isNewest">
            com_change.credit_spread_change_3m,
            com_change.credit_spread_change_6m,
            com_change.excess_spread_change_3m,
            com_change.excess_spread_change_6m,
            com_change.credit_spread_quantile_3y,
            com_change.credit_spread_quantile_5y,
            com_change.excess_spread_quantile_3y,
            com_change.excess_spread_quantile_5y,
        </if>
        indu_com_yield_spread.id,
        indu_com_yield_spread.com_private_cb_yield AS com_private_cb_yield,
        indu_com_yield_spread.total_assets AS total_assets,
        indu_com_yield_spread.com_public_excess_spread AS com_public_excess_spread,
        indu_com_yield_spread.com_perpetual_excess_spread AS com_perpetual_excess_spread,
        indu_com_yield_spread.net_profit AS net_profit,
        indu_com_yield_spread.com_perpetual_cb_yield AS com_perpetual_cb_yield,
        indu_com_yield_spread.indu_level1_name AS indu_level1_name,
        indu_com_yield_spread.indu_level2_code AS indu_level2_code,
        indu_com_yield_spread.com_public_cb_yield AS com_public_cb_yield,
        indu_com_yield_spread.bond_balance AS bond_balance,
        indu_com_yield_spread.com_private_excess_spread AS com_private_excess_spread,
        indu_com_yield_spread.indu_level2_name AS indu_level2_name,
        indu_com_yield_spread.com_credit_spread AS com_credit_spread,
        indu_com_yield_spread.asset_liability_ratio AS asset_liability_ratio,
        indu_com_yield_spread.indu_level1_code AS indu_level1_code,
        indu_com_yield_spread.business_nature AS business_nature,
        indu_com_yield_spread.com_public_credit_spread AS com_public_credit_spread,
        indu_com_yield_spread.com_cb_yield AS com_cb_yield,
        indu_com_yield_spread.spread_date AS spread_date,
        indu_com_yield_spread.com_private_credit_spread AS com_private_credit_spread,
        indu_com_yield_spread.deleted AS deleted,
        indu_com_yield_spread.com_excess_spread AS com_excess_spread,
        indu_com_yield_spread.com_uni_code AS com_uni_code,
        indu_com_yield_spread.com_perpetual_credit_spread AS com_perpetual_credit_spread,
        indu_com_yield_spread.com_ext_rating_mapping AS com_ext_rating_mapping,
        indu_com_yield_spread.net_operating_cash_flow AS net_operating_cash_flow
        FROM indu_com_yield_spread
        INNER JOIN (
        select distinct com_uni_code from indu_bond_yield_spread_${parameter.year}
        where spread_date = #{parameter.spreadDate}
        <if test="parameter.comUniCode != null">
            AND com_uni_code = #{parameter.comUniCode}
        </if>
        <if test="parameter.bondUniCode != null">
            AND bond_uni_code = #{parameter.bondUniCode}
        </if>
        <if test="parameter.industryCode1 != null">
            AND indu_level1_code = #{parameter.industryCode1}
        </if>
        <if test="parameter.industryCode2 != null">
            AND indu_level2_code = #{parameter.industryCode2}
        </if>
        <if test="parameter.spreadBondType != null">
            AND spread_bond_type = #{parameter.spreadBondType}
        </if>
        <if test="parameter.bondExtRatingMapping != null">
            AND bond_ext_rating_mapping = #{parameter.bondExtRatingMapping}
        </if>
        <if test="parameter.bondImpliedRatingMappings != null and parameter.bondImpliedRatingMappings.length > 0">
            AND bond_implied_rating_mapping in
            <foreach collection="parameter.bondImpliedRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.comYyRatingMappings != null and parameter.comYyRatingMappings.length > 0">
            AND com_yy_rating_mapping in
            <foreach collection="parameter.comYyRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.businessFilterNatures != null and parameter.businessFilterNatures.size() > 0">
            AND business_filter_nature in
            <foreach collection="parameter.businessFilterNatures" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.remainingTenor != null">
            AND spread_remaining_tenor_tag = #{parameter.remainingTenor}
        </if>
        <if test="parameter.guaranteedStatus != null">
            AND guaranteed_status = #{parameter.guaranteedStatus}
        </if>
        ) tmp on indu_com_yield_spread.com_uni_code = tmp.com_uni_code
        <if test="isNewest">
            LEFT JOIN com_yield_spread_change com_change ON indu_com_yield_spread.spread_date = com_change.spread_date
            AND indu_com_yield_spread.com_uni_code = com_change.com_uni_code
            and com_change.com_spread_sector = 2
        </if>
        WHERE indu_com_yield_spread.spread_date = #{parameter.spreadDate} AND indu_com_yield_spread.deleted = 0
        <if test="parameter.sort != null">
            order by ${parameter.sort.propertyName} ${parameter.sort.sortDirection}
        </if>
        limit #{parameter.startIndex},#{parameter.pageSize}
    </select>

    <select id="countComYieldSpread" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM indu_com_yield_spread
        INNER JOIN (
        select distinct com_uni_code from indu_bond_yield_spread_${parameter.year}
        where spread_date = #{parameter.spreadDate}
        <if test="parameter.comUniCode != null">
            AND com_uni_code = #{parameter.comUniCode}
        </if>
        <if test="parameter.bondUniCode != null">
            AND bond_uni_code = #{parameter.bondUniCode}
        </if>
        <if test="parameter.industryCode1 != null">
            AND indu_level1_code = #{parameter.industryCode1}
        </if>
        <if test="parameter.industryCode2 != null">
            AND indu_level2_code = #{parameter.industryCode2}
        </if>
        <if test="parameter.spreadBondType != null">
            AND spread_bond_type = #{parameter.spreadBondType}
        </if>
        <if test="parameter.bondExtRatingMapping != null">
            AND bond_ext_rating_mapping = #{parameter.bondExtRatingMapping}
        </if>
        <if test="parameter.bondImpliedRatingMappings != null and parameter.bondImpliedRatingMappings.length > 0">
            AND bond_implied_rating_mapping in
            <foreach collection="parameter.bondImpliedRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.comYyRatingMappings != null and parameter.comYyRatingMappings.length > 0">
            AND com_yy_rating_mapping in
            <foreach collection="parameter.comYyRatingMappings" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.businessFilterNatures != null and parameter.businessFilterNatures.size() > 0">
            AND business_filter_nature in
            <foreach collection="parameter.businessFilterNatures" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="parameter.remainingTenor != null">
            AND spread_remaining_tenor_tag = #{parameter.remainingTenor}
        </if>
        <if test="parameter.guaranteedStatus != null">
            AND guaranteed_status = #{parameter.guaranteedStatus}
        </if>
        ) tmp on indu_com_yield_spread.com_uni_code = tmp.com_uni_code
        WHERE indu_com_yield_spread.spread_date = #{parameter.spreadDate} AND indu_com_yield_spread.deleted = 0
    </select>

    <select id="listComYieldQuantileStatisticsViews"
            resultType="com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO">
        SELECT
        t1.com_uni_code,
        CASE WHEN t2.com_credit_spread is not null THEN COUNT(t1.com_credit_spread &lt; t2.com_credit_spread or null)
        ELSE NULL END comCreditSpreadLessIssueCount,
        CASE WHEN t2.com_credit_spread is not null THEN COUNT(t1.com_credit_spread) ELSE NULL END comCreditSpreadCount,
        CASE WHEN t2.com_excess_spread is not null THEN COUNT(t1.com_excess_spread &lt; t2.com_excess_spread or null)
        ELSE NULL END comExcessSpreadLessIssueCount,
        CASE WHEN t2.com_excess_spread is not null THEN COUNT(t1.com_excess_spread) ELSE NULL END comExcessSpreadCount
        FROM
        indu_com_yield_spread AS t1
        INNER JOIN indu_com_yield_spread t2 ON t1.com_uni_code = t2.com_uni_code
        WHERE
        t1.spread_date >= #{startDate}
        AND t1.spread_date &lt;= #{endDate}
        AND t1.deleted = 0
        AND t2.spread_date = #{issueDate}
        AND t2.deleted = 0
        <if test="comUniCodeList != null and comUniCodeList.size >0">
            AND t2.com_uni_code IN
            <foreach collection="comUniCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        t1.com_uni_code
    </select>
</mapper>