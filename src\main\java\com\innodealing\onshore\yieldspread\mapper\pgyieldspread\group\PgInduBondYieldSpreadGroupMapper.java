package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgInduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadGroupDO;


/**
 * pg产业债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface PgInduBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<PgInduBondYieldSpreadDO,
        PgInduBondYieldSpreadGroupDO> {

}
