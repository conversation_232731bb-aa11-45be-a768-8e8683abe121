package com.innodealing.onshore.yieldspread.controller.api;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldTraceExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceLineChartPeriodResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceLineChartRatingResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldSpreadTraceTabResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldTraceLineCommonResponseDTO;
import com.innodealing.onshore.yieldspread.service.BondYieldSpreadTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;
import java.util.List;

/**
 * 利差追踪
 *
 * <AUTHOR>
 * @date 2023/4/26 11:32
 */
@Api(tags = "(API)利差追踪")
@RestController
@RequestMapping("api/bond/yield/spread/trace")
public class BondYieldSpreadTraceController {

    @Resource
    private BondYieldSpreadTraceService bondYieldSpreadTraceService;

    @Resource
    private HttpServletResponse httpServletResponse;

    @ApiOperation(value = "利差追踪展示-到期收益率绝对值")
    @GetMapping(value = "/chart/abs")
    public RestResponse<List<YieldSpreadTraceTabResponseDTO>> listYieldSpreadTraceAbs(
            @ApiParam(value = "债券类型(1 国债 2 国开债 4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充)",
                    name = "bondType") @RequestParam("bondType") Integer bondType,
            @ApiParam(value = "利率时间", name = "spreadDate") @RequestParam Date spreadDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        return RestResponse.Success(bondYieldSpreadTraceService.listYieldSpreadTraceAbs(userId, bondType, spreadDate));
    }

    @ApiOperation(value = "利差追踪展示-历史分位")
    @GetMapping(value = "/chart/hist-quantile")
    public RestResponse<List<YieldSpreadTraceTabResponseDTO>> listYieldSpreadTraceQuantile(
            @ApiParam(value = "债券类型(1 国债 2 国开债 4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充)",
                    name = "bondType") @RequestParam("bondType") Integer bondType,
            @ApiParam(name = "quantileType", value = "分位类型 1:3年，2:5年")
            @RequestParam(required = false) Integer quantileType,
            @ApiParam(value = "自定义开始时间", name = "startDate")
            @RequestParam(required = false) Date startDate,
            @ApiParam(value = "自定义结束时间", name = "endDate")
            @RequestParam(required = false) Date endDate,
            @ApiParam(value = "利率时间", name = "spreadDate") @RequestParam Date spreadDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        return RestResponse.Success(bondYieldSpreadTraceService.listYieldSpreadTraceQuantile(userId, bondType, spreadDate, quantileType, startDate, endDate));
    }

    @ApiOperation(value = "利差追踪展示-区间变动")
    @GetMapping(value = "/chart/interval-change")
    public RestResponse<List<YieldSpreadTraceTabResponseDTO>> listYieldSpreadTraceChange(
            @ApiParam(value = "债券类型(1 国债 2 国开债 4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充)",
                    name = "bondType") @RequestParam("bondType") Integer bondType,
            @ApiParam(value = "变动类型(1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动 6 一日变动 7 两周变动)", name = "changeType")
            @RequestParam(value = "changeType", required = false) Integer changeType,
            @ApiParam(value = "自定义开始时间", name = "startDate")
            @RequestParam(required = false) Date startDate,
            @ApiParam(value = "自定义结束时间", name = "endDate")
            @RequestParam(required = false) Date endDate,
            @ApiParam(value = "利率时间", name = "spreadDate") @RequestParam Date spreadDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        return RestResponse.Success(bondYieldSpreadTraceService.listYieldSpreadTraceChange(userId, bondType, spreadDate, changeType, startDate, endDate));
    }

    @ApiOperation(value = "利差追踪-折线图(同评级)")
    @GetMapping(value = "/line-chart/rating")
    public RestResponse<YieldSpreadTraceLineChartRatingResponseDTO> lineChartWithRating(
            @ApiParam(value = "债券类型(1 国债 2 国开债 4:中短期票据,  5:产业债, 6:城投, 7:银行普通债, 8:银行二级资本债, 9:银行永续债, 10:证券公司债, 11:证券次级债, 12:证券永续债, 13:同业存单，14 保险资本补充)",
                    name = "bondType", required = true) @RequestParam("bondType") Integer bondType,
            @ApiParam(value = "图类型，1:到期收益率 2:信用利差(债券类型-国开) 3:等级利差 4:期限利差 5:品种利差(债券类型-中短期票据) 6:条款利差(债券类型-普通债)",
                    name = "chartType", required = true) @RequestParam("chartType") Integer chartType,
            @ApiParam(value = "评级类型，1:AAA+ 2:AAA 3:AAA- 4:AA+ 5:AA 6:AA(2) 7:AA- 0:证券次级债，证券永续债,国债,国开债时，评级类型为0,因为这两种没有评级",
                    name = "ratingType", required = true) @RequestParam(value = "ratingType") Integer ratingType,
            @ApiParam(name = "startDate", value = "起始日期") @RequestParam("startDate") Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam("endDate") Date endDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid) {
        return RestResponse.Success(bondYieldSpreadTraceService.lineChartWithRating(bondType, chartType, ratingType, startDate, endDate, userid));
    }

    @ApiOperation(value = "利差追踪-折线图(同期限)")
    @GetMapping(value = "/line-chart/period")
    public RestResponse<YieldSpreadTraceLineChartPeriodResponseDTO> lineChartWithPeriod(
            @ApiParam(value = "债券类型(1 国债 2 国开债 4:中短期票据,  5:产业债, 6:城投, 7:银行普通债, 8:银行二级资本债, 9:银行永续债, 10:证券公司债, 11:证券次级债, 12:证券永续债, 13:同业存单，14 保险资本补充)",
                    name = "bondType", required = true) @RequestParam("bondType") Integer bondType,
            @ApiParam(value = "图类型，1:到期收益率 2:信用利差(债券类型-国开) 3:等级利差 4:期限利差 5:品种利差(债券类型-中短期票据) 6:条款利差(债券类型-普通债)",
                    name = "chartType", required = true) @RequestParam("chartType") Integer chartType,
            @ApiParam(value = "期限类型，1:1M 3:3M 6:6M 9:9M 12:1Y 24:2Y 36:3Y 60:5Y 84:7Y 120:10Y 180:15Y 240:20Y 360:30Y  600:50Y 0:证券次级债，证券永续债时，期限类型为0,因为只有一个评级，展示所有期限",
                    name = "periodType", required = true) @RequestParam(value = "periodType", required = false) Integer periodType,
            @ApiParam(name = "startDate", value = "起始日期") @RequestParam("startDate") Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam("endDate") Date endDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid) {
        return RestResponse.Success(bondYieldSpreadTraceService.lineChartWithPeriod(bondType, chartType, periodType, startDate, endDate, userid));
    }

    @ApiOperation(value = "利差追踪-品种类折线图(同品种)")
    @GetMapping(value = "/line-chart-variety/varieties")
    public RestResponse<YieldSpreadTraceLineChartRatingResponseDTO> lineChartVarietyWithVarieties(
            @ApiParam(value = "债券类型(17 利率债)",
                    name = "bondType", required = true) @RequestParam("bondType") Integer bondType,
            @ApiParam(value = "图类型，1:到期收益率 2:信用利差(债券类型-国开) 3:等级利差 4:期限利差 5:品种利差(债券类型-中短期票据) 6:条款利差(债券类型-普通债) 8品种利差(债券类型-国债)",
                    name = "chartType", required = true) @RequestParam("chartType") Integer chartType,
            @ApiParam(value = "利差追踪:1 国债  4国开 203 农发 204 进出口 51 地方债",
                    name = "curveCode", required = true) @RequestParam(value = "curveCode") Integer curveCode,
            @ApiParam(name = "startDate", value = "起始日期") @RequestParam("startDate") Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam("endDate") Date endDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid) {
        return RestResponse.Success(bondYieldSpreadTraceService.lineChartVarietyWithVarieties(bondType, chartType, curveCode, startDate, endDate, userid));
    }

    @ApiOperation(value = "利差追踪-品种类折线图(同期限)")
    @GetMapping(value = "/line-chart-variety/period")
    public RestResponse<YieldTraceLineCommonResponseDTO> lineChartVarietyWithPeriod(
            @ApiParam(value = "债券类型(17 利率债)",
                    name = "bondType", required = true) @RequestParam("bondType") Integer bondType,
            @ApiParam(value = "图类型，1:到期收益率 2:信用利差(债券类型-国开) 3:等级利差 4:期限利差 5:品种利差(债券类型-中短期票据) 6:条款利差(债券类型-普通债) 8品种利差(债券类型-国债)",
                    name = "chartType", required = true) @RequestParam("chartType") Integer chartType,
            @ApiParam(value = "期限类型，期限类型，1:1M 3:3M 6:6M 9:9M 12:1Y 24:2Y 36:3Y 60:5Y 84:7Y 120:10Y 180:15Y 240:20Y 360:30Y  600:50Y",
                    name = "periodType", required = true) @RequestParam(value = "periodType", required = false) Integer periodType,
            @ApiParam(name = "startDate", value = "起始日期") @RequestParam("startDate") Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam("endDate") Date endDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid) {
        return RestResponse.Success(bondYieldSpreadTraceService.lineChartVarietyWithPeriod(bondType, chartType, periodType, startDate, endDate, userid));
    }

    @ApiOperation(value = "利差追踪展示-导出")
    @PostMapping(value = "/chart/export")
    public void exportYieldSpreadTrace(@Validated @RequestBody BondYieldTraceExportReqDTO bondYieldTraceExportReqDTO,
                                       @ApiParam(name = "userid", value = "用户编号", hidden = true)
                                       @CookieValue("userid") Long userId) throws IOException {
        bondYieldSpreadTraceService.exportYieldSpreadTrace(httpServletResponse, userId, bondYieldTraceExportReqDTO);
    }

    @ApiOperation(value = "利差追踪展示-最新数据日期")
    @GetMapping(value = "/max-spread-date")
    public RestResponse<String> maxSpreadDate(){
        return RestResponse.Success(bondYieldSpreadTraceService.maxSpreadDate().toString());
    }
}
