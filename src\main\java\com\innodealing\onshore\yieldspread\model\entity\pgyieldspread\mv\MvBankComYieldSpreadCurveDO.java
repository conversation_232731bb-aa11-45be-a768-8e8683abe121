package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 主体利差曲线-银行
 *
 * <AUTHOR>
 */
@Table(name = "mv_bank_com_yield_spread_curve")
public class MvBankComYieldSpreadCurveDO extends BaseMvComYieldSpreadCurveDO {

    /**
     * 银行求偿顺序 1:普通;2:二级资本债;3:永续债
     */
    @Column
    private Integer bankSeniorityRanking;
    /**
     * 是否使用银行求偿顺序进行分组 0:是 1:否
     */
    @Column
    private Integer usingBankSeniorityRanking;

    public Integer getBankSeniorityRanking() {
        return bankSeniorityRanking;
    }

    public void setBankSeniorityRanking(Integer bankSeniorityRanking) {
        this.bankSeniorityRanking = bankSeniorityRanking;
    }

    public Integer getUsingBankSeniorityRanking() {
        return usingBankSeniorityRanking;
    }

    public void setUsingBankSeniorityRanking(Integer usingBankSeniorityRanking) {
        this.usingBankSeniorityRanking = usingBankSeniorityRanking;
    }
}
