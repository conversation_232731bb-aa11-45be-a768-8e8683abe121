package com.innodealing.onshore.yieldspread.service.impl;

import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserSpreadConfigDAO;
import com.innodealing.onshore.yieldspread.model.bo.UserSpreadConfigBO;
import com.innodealing.onshore.yieldspread.model.dto.UserSpreadConfigDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserSpreadConfigDO;
import com.innodealing.onshore.yieldspread.service.UserSpreadConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 用户利差展示设置表Service层 {@link UserSpreadConfigDO}
 *
 * <AUTHOR>
 */
@Service
public class UserSpreadConfigServiceImpl implements UserSpreadConfigService {

    @Resource
    private UserSpreadConfigDAO userSpreadConfigDAO;

    /**
     * 保存用户利差配置
     *
     * @param userId              用户id
     * @param userSpreadConfigDTO 用户利差配置信息
     */
    @Override
    public void saveUserSpreadConfig(Long userId, List<UserSpreadConfigDTO> userSpreadConfigDTO) {
        List<UserSpreadConfigDO> userSpreadConfigDOList = userSpreadConfigDTO.stream()
                .map(dto -> {
                    UserSpreadConfigDO userSpreadConfigDO = BeanCopyUtils.copyProperties(dto, UserSpreadConfigDO.class);
                    userSpreadConfigDO.setConfigJson(JSON.toJSONString(dto.getConfigDetails()));
                    userSpreadConfigDO.setUserId(userId);
                    userSpreadConfigDO.setDeleted(Deleted.NO_DELETED.getValue());
                    return userSpreadConfigDO;
                }).collect(Collectors.toList());
        userSpreadConfigDAO.saveBatchUserSpreadConfigDOs(userSpreadConfigDOList);
    }

    /**
     * 根据配置id获取用户利差配置列表
     *
     * @param userId    用户id
     * @param configIds 用户利差配置
     * @return 用户利差配置列表 {@link UserSpreadConfigDTO}
     */
    @Override
    public List<UserSpreadConfigDTO> listUserSpreadConfigByConfigIds(Long userId, List<Long> configIds) {
        List<UserSpreadConfigBO<Object>> userSpreadConfigBOList = userSpreadConfigDAO.listUserSpreadConfigByConfigIds(userId, configIds, Object.class);
        return BeanCopyUtils.copyList(userSpreadConfigBOList, UserSpreadConfigDTO.class);
    }

    /**
     * 默认利差配置列表
     *
     * @param configIds 配置id
     * @return 配置列表 {@link UserSpreadConfigDTO}
     */
    @Override
    public List<UserSpreadConfigDTO> listDefaultSpreadConfigByConfigIds(List<Long> configIds) {
        List<UserSpreadConfigBO<Object>> defaultSpreadConfigBOList = userSpreadConfigDAO.listDefaultSpreadConfigByConfigIds(configIds, Object.class);
        return BeanCopyUtils.copyList(defaultSpreadConfigBOList, UserSpreadConfigDTO.class);
    }
}
