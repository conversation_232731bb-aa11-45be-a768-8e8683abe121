package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvInduBondYieldSpreadPanoramaMapper;
import com.innodealing.onshore.yieldspread.model.bo.InduSpreadPanoramaBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvInduBondYieldSpreadPanoramaDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.divideHundredThousand;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 行业利差全景DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvInduBondYieldSpreadPanoramaDAO {

    @Resource
    private MvInduBondYieldSpreadPanoramaMapper mvInduBondYieldSpreadPanoramaMapper;

    /**
     * 刷新行业利差全景物化视图
     */
    public void refreshMvInduBondYieldSpreadPanorama() {
        mvInduBondYieldSpreadPanoramaMapper.refreshMvInduBondYieldSpreadPanorama();
    }

    /**
     * 查询行业利差全景-物化视图
     *
     * @param spreadDate                  利差日期
     * @param bondExtRatingMapping        债券评级
     * @param spreadBondType              债券类型
     * @param guaranteeStatus             担保状态
     * @param spreadRemainingTenorTag     剩余期限
     * @param bondImpliedRatingMappingTag 债券隐含评级标签
     * @param comYyRatingMappingTag       yy评级标签
     * @return {@link List}<{@link InduSpreadPanoramaBO}> 物化视图中行业利差全景数据响应集
     */
    public List<InduSpreadPanoramaBO> listInduBondYieldSpreadPanoramas(Date spreadDate, Integer bondExtRatingMapping,
                                                                       Integer spreadBondType, Integer guaranteeStatus, Integer spreadRemainingTenorTag,
                                                                       Integer bondImpliedRatingMappingTag, Integer comYyRatingMappingTag) {
        DynamicQuery<MvInduBondYieldSpreadPanoramaDO> groupQuery = DynamicQuery.createQuery(MvInduBondYieldSpreadPanoramaDO.class)
                .select(MvInduBondYieldSpreadPanoramaDO::getInduLevel1Code,
                        MvInduBondYieldSpreadPanoramaDO::getInduLevel2Code,
                        MvInduBondYieldSpreadPanoramaDO::getSpreadDate,
                        MvInduBondYieldSpreadPanoramaDO::getBondCreditSpreadCount,
                        MvInduBondYieldSpreadPanoramaDO::getBondExcessSpreadCount,
                        MvInduBondYieldSpreadPanoramaDO::getBondCreditSpread,
                        MvInduBondYieldSpreadPanoramaDO::getBondExcessSpread)
                .and(MvInduBondYieldSpreadPanoramaDO::getSpreadDate, isEqual(spreadDate))
                .and(nonNull(bondExtRatingMapping), MvInduBondYieldSpreadPanoramaDO::getBondExtRatingMapping, isEqual(bondExtRatingMapping))
                .and(isNull(bondExtRatingMapping), MvInduBondYieldSpreadPanoramaDO::getUsingBondExtRatingMapping, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(spreadBondType), MvInduBondYieldSpreadPanoramaDO::getSpreadBondType, isEqual(spreadBondType))
                .and(isNull(spreadBondType), MvInduBondYieldSpreadPanoramaDO::getUsingSpreadBondType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(guaranteeStatus), MvInduBondYieldSpreadPanoramaDO::getGuaranteedStatus, isEqual(guaranteeStatus))
                .and(isNull(guaranteeStatus), MvInduBondYieldSpreadPanoramaDO::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(bondImpliedRatingMappingTag), MvInduBondYieldSpreadPanoramaDO::getBondImpliedRatingMappingTag, isEqual(bondImpliedRatingMappingTag))
                .and(isNull(bondImpliedRatingMappingTag), MvInduBondYieldSpreadPanoramaDO::getUsingBondImpliedRatingMappingTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(comYyRatingMappingTag), MvInduBondYieldSpreadPanoramaDO::getComYyRatingMappingTag, isEqual(comYyRatingMappingTag))
                .and(isNull(comYyRatingMappingTag), MvInduBondYieldSpreadPanoramaDO::getUsingComYyRatingMappingTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(spreadRemainingTenorTag), MvInduBondYieldSpreadPanoramaDO::getSpreadRemainingTenorTag, isEqual(spreadRemainingTenorTag))
                .and(isNull(spreadRemainingTenorTag), MvInduBondYieldSpreadPanoramaDO::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()));
        List<MvInduBondYieldSpreadPanoramaDO> mvInduBondYieldSpreadPanoramaList = mvInduBondYieldSpreadPanoramaMapper.selectByDynamicQuery(groupQuery);
        if (CollectionUtils.isEmpty(mvInduBondYieldSpreadPanoramaList)) {
            return Collections.emptyList();
        }
        return mvInduBondYieldSpreadPanoramaList.stream().map(panorama -> {
            InduSpreadPanoramaBO induPanoramaDTO = BeanCopyUtils.copyProperties(panorama, InduSpreadPanoramaBO.class);
            // 物化视图中的数据乘以100_000,需要除以100_000
            ObjectExtensionUtils.ifNonNull(panorama.getBondCreditSpread(), v -> divideHundredThousand(BigDecimal.valueOf(v)).ifPresent(induPanoramaDTO::setBondCreditSpread));
            ObjectExtensionUtils.ifNonNull(panorama.getBondExcessSpread(), v -> divideHundredThousand(BigDecimal.valueOf(v)).ifPresent(induPanoramaDTO::setBondExcessSpread));
            return induPanoramaDTO;
        }).collect(Collectors.toList());
    }
}
