package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.UpdateQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.UserSpreadConfigMapper;
import com.innodealing.onshore.yieldspread.model.bo.UserSpreadConfigBO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserSpreadConfigDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;


/**
 * 用户利差展示设置表数据库访问层 {@link UserSpreadConfigDO}
 * 对UserSpreadConfigMapper层做出简单封装 {@link UserSpreadConfigMapper}
 *
 * <AUTHOR>
 */
@Repository
public class UserSpreadConfigDAO {

    @Resource
    private UserSpreadConfigMapper userSpreadConfigMapper;

    @Resource(name = YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private RedisService redisService;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;


    /**
     * 利差配置默认用户id
     */
    private static final Long SPREAD_CONFIG_DEFAULT_USER_ID = -1L;

    /**
     * 幂等保存用户利差展示设置  userSpreadConfigDOs
     *
     * @param userSpreadConfigDOs {@link UserSpreadConfigDO}
     */
    public void saveBatchUserSpreadConfigDOs(final Collection<UserSpreadConfigDO> userSpreadConfigDOs) {
        if (CollectionUtils.isEmpty(userSpreadConfigDOs)) {
            return;
        }

        Map<String, Long> businessKeyToIdMap = getBusinessKeyToIdMap(userSpreadConfigDOs);
        List<UserSpreadConfigDO> insertList = Lists.newArrayListWithExpectedSize(userSpreadConfigDOs.size());
        List<UserSpreadConfigDO> updateList = Lists.newArrayListWithExpectedSize(userSpreadConfigDOs.size());
        for (UserSpreadConfigDO userSpreadConfigDO : userSpreadConfigDOs) {
            Long oldId = businessKeyToIdMap.get(getBusinessKey(userSpreadConfigDO));
            if (Objects.isNull(oldId)) {
                insertList.add(userSpreadConfigDO);
            } else {
                userSpreadConfigDO.setId(oldId);
                updateList.add(userSpreadConfigDO);
            }
        }
        // 开启事务执行
        transactionTemplate.execute(transactionStatus -> {
            // 批量操作
            insertBatchUserSpreadConfigDOs(insertList);
            updateBatchUserSpreadConfigDOsByPrimaryKey(updateList);
            return true;
        });
        //修改的是默认配置的话缓存删除
        List<String> delKeyList = userSpreadConfigDOs.stream()
                .filter(configDO -> SPREAD_CONFIG_DEFAULT_USER_ID.equals(configDO.getUserId()))
                .map(configDO ->
                        getUserSpreadConfigRedisKey(configDO.getUserId(), configDO.getTabConfigId()).orElse(null)
                ).filter(Objects::nonNull).collect(Collectors.toList());
        redisService.delete(delKeyList);
    }


    /**
     * 批量新增用户利差展示设置 {@link UserSpreadConfigMapper#insertSelective(Object)}
     *
     * @param userSpreadConfigDOs {@link UserSpreadConfigDO}
     * @return 影响的行数
     */
    private void insertBatchUserSpreadConfigDOs(final List<UserSpreadConfigDO> userSpreadConfigDOs) {
        if (CollectionUtils.isEmpty(userSpreadConfigDOs)) {
            return;
        }
        final MapperBatchAction<UserSpreadConfigMapper> insertBatchAction = MapperBatchAction.create(UserSpreadConfigMapper.class, sqlSessionFactory);
        for (UserSpreadConfigDO userSpreadConfigDO : userSpreadConfigDOs) {
            insertBatchAction.addAction(mapper -> mapper.insertSelective(userSpreadConfigDO));
        }
        insertBatchAction.doBatchActions();
    }

    /**
     * 批量更新用户利差展示设置 {@link UserSpreadConfigMapper#updateByPrimaryKeySelective(Object)}
     *
     * @param userSpreadConfigDOs {@link UserSpreadConfigDO}
     * @return 影响的行数
     */
    private void updateBatchUserSpreadConfigDOsByPrimaryKey(final List<UserSpreadConfigDO> userSpreadConfigDOs) {
        if (CollectionUtils.isEmpty(userSpreadConfigDOs)) {
            return;
        }
        final MapperBatchAction<UserSpreadConfigMapper> updateBatchAction = MapperBatchAction.create(UserSpreadConfigMapper.class, sqlSessionFactory);
        for (UserSpreadConfigDO userSpreadConfigDO : userSpreadConfigDOs) {
            UpdateQuery<UserSpreadConfigDO> updateQuery = UpdateQuery.createQuery(UserSpreadConfigDO.class)
                    .set(userSpreadConfigDO, ignore -> ignore.ignore(UserSpreadConfigDO::getId, UserSpreadConfigDO::getCreateTime, UserSpreadConfigDO::getUpdateTime))
                    .and(UserSpreadConfigDO::getId, isEqual(userSpreadConfigDO.getId()));
            updateBatchAction.addAction(mapper -> mapper.updateByUpdateQuery(updateQuery));
        }
        updateBatchAction.doBatchActions();
    }


    /**
     * 获取DB中已经存在的  用户利差展示设置 数据
     *
     * @param userSpreadConfigDOs 用户利差
     * @return map  key:业务key value:edb指标数据预测id
     */
    private Map<String, Long> getBusinessKeyToIdMap(Collection<UserSpreadConfigDO> userSpreadConfigDOs) {
        if (CollectionUtils.isEmpty(userSpreadConfigDOs)) {
            return Maps.newHashMap();
        }
        DynamicQuery<UserSpreadConfigDO> dynamicQuery = DynamicQuery.createQuery(UserSpreadConfigDO.class)
                .select(UserSpreadConfigDO::getId, UserSpreadConfigDO::getUserId, UserSpreadConfigDO::getTabConfigId)
                .and(UserSpreadConfigDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        for (UserSpreadConfigDO userSpreadConfigDO : userSpreadConfigDOs) {
            dynamicQuery.or(g -> g.and(UserSpreadConfigDO::getUserId, isEqual(userSpreadConfigDO.getUserId()))
                    .and(UserSpreadConfigDO::getUserId, isEqual(userSpreadConfigDO.getTabConfigId())));
        }
        return userSpreadConfigMapper.selectByDynamicQuery(dynamicQuery).stream()
                .collect(Collectors.toMap(this::getBusinessKey, UserSpreadConfigDO::getId, (v1, v2) -> v2));
    }

    /**
     * 获取用户利差展示设置业务key
     *
     * @param userSpreadConfigDO {@link UserSpreadConfigDO}
     * @return 用户利差展示设置业务key
     */
    private String getBusinessKey(UserSpreadConfigDO userSpreadConfigDO) {
        return String.format("%s:%s", userSpreadConfigDO.getUserId(), userSpreadConfigDO.getTabConfigId());
    }

    /**
     * 获取用户配置列表
     *
     * @param userId    用户id
     * @param configIds 配置id列表
     * @param clazz     指定参数类型
     * @param <E>       the parameter of the class
     * @return 用户利差配置列表 @link UserSpreadConfigBO}
     */
    public <E> List<UserSpreadConfigBO<E>> listUserSpreadConfigByConfigIds(@NotNull Long userId, @NotEmpty List<Long> configIds, Class<E> clazz) {
        DynamicQuery<UserSpreadConfigDO> dynamicQuery = DynamicQuery.createQuery(UserSpreadConfigDO.class)
                .select(UserSpreadConfigDO::getId, UserSpreadConfigDO::getUserId,
                        UserSpreadConfigDO::getTabConfigId, UserSpreadConfigDO::getConfigJson)
                .and(UserSpreadConfigDO::getUserId, isEqual(userId))
                .and(UserSpreadConfigDO::getTabConfigId, in(configIds))
                .and(UserSpreadConfigDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<UserSpreadConfigBO<E>> userSpreadConfigBOList = userSpreadConfigMapper.selectByDynamicQuery(dynamicQuery).stream().map(configDO -> {
            UserSpreadConfigBO<E> userSpreadConfigBO = BeanCopyUtils.copyProperties(configDO, UserSpreadConfigBO.class);
            userSpreadConfigBO.setConfigDetails(JSON.parseArray(configDO.getConfigJson(), clazz));
            return userSpreadConfigBO;
        }).collect(Collectors.toList());
        List<Long> userConfigIds = userSpreadConfigBOList.stream().map(UserSpreadConfigBO::getTabConfigId).collect(Collectors.toList());
        configIds.removeAll(userConfigIds);
        if (CollectionUtils.isNotEmpty(configIds)) {
            //取默认配置
            List<UserSpreadConfigBO<E>> defaultSpreadConfigBOList = listDefaultSpreadConfigByConfigIds(configIds, clazz)
                    .stream().peek(defaultConfigDO -> defaultConfigDO.setUserId(userId)).collect(Collectors.toList());
            userSpreadConfigBOList.addAll(defaultSpreadConfigBOList);
        }
        return userSpreadConfigBOList;
    }

    /**
     * 获取用户单项配置
     *
     * @param userId   用户id
     * @param configId 配置id
     * @param clazz    指定参数类型
     * @param <E>      the parameter of the class
     * @return 用户利差配置列表 @link UserSpreadConfigBO}
     */
    public <E> Optional<UserSpreadConfigBO<E>> getUserSpreadConfigByConfigId(@NotNull Long userId, @NotNull Long configId, Class<E> clazz) {
        DynamicQuery<UserSpreadConfigDO> dynamicQuery = DynamicQuery.createQuery(UserSpreadConfigDO.class)
                .select(UserSpreadConfigDO::getId, UserSpreadConfigDO::getUserId,
                        UserSpreadConfigDO::getTabConfigId, UserSpreadConfigDO::getConfigJson)
                .and(UserSpreadConfigDO::getUserId, isEqual(userId))
                .and(UserSpreadConfigDO::getTabConfigId, isEqual(configId))
                .and(UserSpreadConfigDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        Optional<UserSpreadConfigBO<E>> userSpreadConfigOpt = userSpreadConfigMapper.selectFirstByDynamicQuery(dynamicQuery).map(configDO -> {
            UserSpreadConfigBO<E> userSpreadConfigBO = BeanCopyUtils.copyProperties(configDO, UserSpreadConfigBO.class);
            userSpreadConfigBO.setConfigDetails(JSON.parseArray(configDO.getConfigJson(), clazz));
            return userSpreadConfigBO;
        });
        if (userSpreadConfigOpt.isPresent()) {
            return userSpreadConfigOpt;
        }
        //取默认配置
        return getDefaultSpreadConfigByConfigId(configId, clazz);
    }

    /**
     * 获取系统默认配置列表
     *
     * @param configIds 配置id列表
     * @param <E>       the parameter of the class
     * @param clazz     类型
     * @return 用户利差配置列表 @link UserSpreadConfigBO}
     */
    public <E> List<UserSpreadConfigBO<E>> listDefaultSpreadConfigByConfigIds(@NotEmpty List<Long> configIds, Class<E> clazz) {
        List<UserSpreadConfigBO<E>> cacheList = configIds.stream().map(configId -> {
            UserSpreadConfigBO<E> userSpreadConfigBO = new UserSpreadConfigBO<>();
            userSpreadConfigBO.setUserId(SPREAD_CONFIG_DEFAULT_USER_ID);
            userSpreadConfigBO.setTabConfigId(configId);
            getUserSpreadConfigCache(SPREAD_CONFIG_DEFAULT_USER_ID, configId)
                    .ifPresent(jsonStr -> userSpreadConfigBO.setConfigDetails(JSON.parseArray(jsonStr, clazz)));
            return userSpreadConfigBO;
        }).filter(configBO -> CollectionUtils.isNotEmpty(configBO.getConfigDetails())).collect(Collectors.toList());
        //缓存中存在的 configId
        List<Long> cacheConfigIds = cacheList.stream().map(UserSpreadConfigBO::getTabConfigId).collect(Collectors.toList());
        configIds.removeAll(cacheConfigIds);
        if (CollectionUtils.isEmpty(configIds)) {
            return cacheList;
        }

        DynamicQuery<UserSpreadConfigDO> dynamicQuery = DynamicQuery.createQuery(UserSpreadConfigDO.class)
                .select(UserSpreadConfigDO::getId, UserSpreadConfigDO::getUserId,
                        UserSpreadConfigDO::getTabConfigId, UserSpreadConfigDO::getConfigJson)
                .and(UserSpreadConfigDO::getUserId, isEqual(SPREAD_CONFIG_DEFAULT_USER_ID))
                .and(UserSpreadConfigDO::getTabConfigId, in(configIds))
                .and(UserSpreadConfigDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<UserSpreadConfigDO> userSpreadConfigDOList = userSpreadConfigMapper.selectByDynamicQuery(dynamicQuery);
        //设置缓存
        userSpreadConfigDOList.forEach(configDO ->
                setUserSpreadConfigCache(configDO.getUserId(), configDO.getTabConfigId(), configDO.getConfigJson())
        );
        List<UserSpreadConfigBO<E>> configList = userSpreadConfigDOList.stream().map(configDO -> {
            UserSpreadConfigBO<E> userSpreadConfigBO = BeanCopyUtils.copyProperties(configDO, UserSpreadConfigBO.class);
            userSpreadConfigBO.setConfigDetails(JSON.parseArray(configDO.getConfigJson(), clazz));
            return userSpreadConfigBO;
        }).collect(Collectors.toList());
        cacheList.addAll(configList);
        return cacheList;
    }

    /**
     * 获取系统单个默认配置
     *
     * @param configId 配置id列表
     * @param <E>      the parameter of the class
     * @param clazz    类型
     * @return 用户利差配置列表 @link UserSpreadConfigBO}
     */
    public <E> Optional<UserSpreadConfigBO<E>> getDefaultSpreadConfigByConfigId(@NotNull Long configId, Class<E> clazz) {
        Optional<String> cacheOpt = getUserSpreadConfigCache(SPREAD_CONFIG_DEFAULT_USER_ID, configId);
        if (cacheOpt.isPresent()) {
            UserSpreadConfigBO<E> userSpreadConfigBO = new UserSpreadConfigBO<>();
            userSpreadConfigBO.setUserId(SPREAD_CONFIG_DEFAULT_USER_ID);
            userSpreadConfigBO.setTabConfigId(configId);
            userSpreadConfigBO.setConfigDetails(JSON.parseArray(cacheOpt.get(), clazz));
            return Optional.of(userSpreadConfigBO);
        }
        DynamicQuery<UserSpreadConfigDO> dynamicQuery = DynamicQuery.createQuery(UserSpreadConfigDO.class)
                .select(UserSpreadConfigDO::getId, UserSpreadConfigDO::getUserId,
                        UserSpreadConfigDO::getTabConfigId, UserSpreadConfigDO::getConfigJson)
                .and(UserSpreadConfigDO::getUserId, isEqual(SPREAD_CONFIG_DEFAULT_USER_ID))
                .and(UserSpreadConfigDO::getTabConfigId, isEqual(configId))
                .and(UserSpreadConfigDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        Optional<UserSpreadConfigDO> userSpreadConfigOpt = userSpreadConfigMapper.selectFirstByDynamicQuery(dynamicQuery);
        //查不到设置 "[]" 防止缓存穿透
        String cacheValue = userSpreadConfigOpt.map(UserSpreadConfigDO::getConfigJson).orElse(YieldSpreadCacheConst.USER_SPREAD_CONFIG_SPACE_DEFAULT_VALUE);
        setUserSpreadConfigCache(SPREAD_CONFIG_DEFAULT_USER_ID, configId, cacheValue);
        return userSpreadConfigOpt.map(configDO -> {
            UserSpreadConfigBO<E> userSpreadConfigBO = BeanCopyUtils.copyProperties(configDO, UserSpreadConfigBO.class);
            userSpreadConfigBO.setConfigDetails(JSON.parseArray(configDO.getConfigJson(), clazz));
            return userSpreadConfigBO;
        });
    }

    /**
     * 获取用户配置 redis key
     *
     * @param userId   用户id
     * @param configId 配置id
     * @return key
     */
    private Optional<String> getUserSpreadConfigRedisKey(Long userId, Long configId) {
        if (Objects.isNull(userId) || Objects.isNull(configId)) {
            return Optional.empty();
        }
        return Optional.of(String.format(YieldSpreadCacheConst.USER_SPREAD_CONFIG_KEY, userId, configId));
    }

    /**
     * 获取用户配置 redis 缓存数据
     *
     * @param userId   用户id
     * @param configId 配置id
     * @return key
     */
    private Optional<String> getUserSpreadConfigCache(Long userId, Long configId) {
        if (Objects.isNull(userId) || Objects.isNull(configId)) {
            return Optional.empty();
        }

        return redisService.get(getUserSpreadConfigRedisKey(userId, configId).orElse(null));
    }

    /**
     * 设置用户配置 redis 缓存数据
     *
     * @param userId     用户id
     * @param configId   配置id
     * @param cacheValue 缓存值
     */
    private void setUserSpreadConfigCache(Long userId, Long configId, String cacheValue) {
        if (Objects.isNull(userId) || Objects.isNull(configId)) {
            return;
        }
        redisService.set(getUserSpreadConfigRedisKey(SPREAD_CONFIG_DEFAULT_USER_ID, configId).orElse(null), cacheValue, YieldSpreadCacheConst.CACHE_ONE_DAYS, TimeUnit.DAYS);
    }


}
