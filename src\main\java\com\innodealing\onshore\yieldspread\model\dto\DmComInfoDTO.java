package com.innodealing.onshore.yieldspread.model.dto;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * dm城投主体信息表 DTO
 *
 * <AUTHOR>
 * @date 2023/04/12
 */
public class DmComInfoDTO {
    @ApiModelProperty("主体编码")
    private Long comUniCode;
    @ApiModelProperty("dm区域编码")
    private Long areaUniCode;
    @ApiModelProperty("评分区域编码")
    private Long scoreAreaUniCode;
    @ApiModelProperty("城投主体状态: 1正常,0剔除")
    private Integer udicStatus;
    @ApiModelProperty("一级行业名称")
    private String induLevel1Name;
    @ApiModelProperty("二级行业名称")
    private String induLevel2Name;
    @ApiModelProperty("平台重要性(平台等级)：1.核心平台 2.重要平台 3.次要平台")
    private Integer platformLevel;
    @ApiModelProperty("数据年份")
    private Integer dataYear;
    @ApiModelProperty("dm城投原始评分(越大越好)")
    private BigDecimal dmUdicScore;
    @ApiModelProperty("dm城投标准评分(越小越好)")
    private BigDecimal dmUdicStandardScore;
    @ApiModelProperty("dm城投评分状态 1:有效,0:无效)")
    private Integer dmUdicScoreStatus;
    @ApiModelProperty("投资属性")
    private String investProperty;
    @ApiModelProperty("区域总分(十分制)")
    private BigDecimal areaTotalScore;
    @ApiModelProperty("政府支持意愿分数(十分制)")
    private BigDecimal govSupportWillScore;
    @ApiModelProperty("企业资质得分(十分制)")
    private BigDecimal businessQualificationScore;
    @ApiModelProperty("数据日期")
    private Date dataDate;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }


    public Long getAreaUniCode() {
        return areaUniCode;
    }

    public void setAreaUniCode(Long areaUniCode) {
        this.areaUniCode = areaUniCode;
    }

    public Long getScoreAreaUniCode() {
        return scoreAreaUniCode;
    }

    public void setScoreAreaUniCode(Long scoreAreaUniCode) {
        this.scoreAreaUniCode = scoreAreaUniCode;
    }

    public Integer getUdicStatus() {
        return udicStatus;
    }

    public void setUdicStatus(Integer udicStatus) {
        this.udicStatus = udicStatus;
    }

    public String getInduLevel1Name() {
        return induLevel1Name;
    }

    public void setInduLevel1Name(String induLevel1Name) {
        this.induLevel1Name = induLevel1Name;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }

    public Integer getPlatformLevel() {
        return platformLevel;
    }

    public void setPlatformLevel(Integer platformLevel) {
        this.platformLevel = platformLevel;
    }

    public Integer getDataYear() {
        return dataYear;
    }

    public void setDataYear(Integer dataYear) {
        this.dataYear = dataYear;
    }

    public BigDecimal getDmUdicScore() {
        return dmUdicScore;
    }

    public void setDmUdicScore(BigDecimal dmUdicScore) {
        this.dmUdicScore = dmUdicScore;
    }

    public BigDecimal getDmUdicStandardScore() {
        return dmUdicStandardScore;
    }

    public void setDmUdicStandardScore(BigDecimal dmUdicStandardScore) {
        this.dmUdicStandardScore = dmUdicStandardScore;
    }

    public Integer getDmUdicScoreStatus() {
        return dmUdicScoreStatus;
    }

    public void setDmUdicScoreStatus(Integer dmUdicScoreStatus) {
        this.dmUdicScoreStatus = dmUdicScoreStatus;
    }

    public String getInvestProperty() {
        return investProperty;
    }

    public void setInvestProperty(String investProperty) {
        this.investProperty = investProperty;
    }

    public BigDecimal getAreaTotalScore() {
        return areaTotalScore;
    }

    public void setAreaTotalScore(BigDecimal areaTotalScore) {
        this.areaTotalScore = areaTotalScore;
    }

    public BigDecimal getGovSupportWillScore() {
        return govSupportWillScore;
    }

    public void setGovSupportWillScore(BigDecimal govSupportWillScore) {
        this.govSupportWillScore = govSupportWillScore;
    }

    public BigDecimal getBusinessQualificationScore() {
        return businessQualificationScore;
    }

    public void setBusinessQualificationScore(BigDecimal businessQualificationScore) {
        this.businessQualificationScore = businessQualificationScore;
    }

    public Date getDataDate() {
        return Objects.isNull(dataDate) ? null : new Date(dataDate.getTime());
    }

    public void setDataDate(Date dataDate) {
        this.dataDate = Objects.isNull(dataDate) ? null : new Date(dataDate.getTime());
    }
}