package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.BankBondYieldSpreadGroupDO;

/**
 * 银行债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface BankBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<BankBondYieldSpreadDO,
        BankBondYieldSpreadGroupDO> {

}
