package com.innodealing.onshore.yieldspread.model.dto.response.excel;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 固定列曲线导出DTO
 *
 * @param <T> excelExportDTO
 * <AUTHOR>
 * @create: 2024-12-18
 */
public class FixCurveExportExcelDTO<T> {

    private String sheetName;

    private List<T> list;

    private Class<T> headClass;

    public FixCurveExportExcelDTO() {
    }

    /**
     * 有参构造
     *
     * @param sheetName sheetName
     * @param list      数据集合
     * @param headClass class
     */
    public FixCurveExportExcelDTO(String sheetName, List<T> list, Class<T> headClass) {
        this.sheetName = sheetName;
        this.list = list;
        this.headClass = headClass;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public List<T> getList() {
        return Objects.isNull(list) ? new ArrayList<>() : new ArrayList<>(list);
    }

    public void setList(List<T> list) {
        this.list = Objects.isNull(list) ? new ArrayList<>() : new ArrayList<>(list);
    }

    public Class<T> getHeadClass() {
        return headClass;
    }

    public void setHeadClass(Class<T> headClass) {
        this.headClass = headClass;
    }
}
