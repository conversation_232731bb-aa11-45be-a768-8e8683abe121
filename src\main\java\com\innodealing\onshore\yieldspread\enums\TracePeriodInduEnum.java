package com.innodealing.onshore.yieldspread.enums;

/**
 * 利差追踪-期限利差-产业债期限枚举
 *
 * <AUTHOR>
 * @date 2024/10/21 19:03
 **/
public enum TracePeriodInduEnum implements ITracePeriodCommonEnum {
    /**
     * 产业债-期限利差-1年期
     */
    TRACE_PERIOD_CBM_1Y(PeriodEnum.ONE_YEAR, "2Y-1Y", PeriodEnum.TWO_YEARS, PeriodEnum.ONE_YEAR),
    TRACE_PERIOD_CBM_2Y(PeriodEnum.TWO_YEARS, "3Y-2Y", PeriodEnum.THREE_YEARS, PeriodEnum.TWO_YEARS),
    TRACE_PERIOD_CBM_3Y(PeriodEnum.THREE_YEARS, "5Y-3Y", PeriodEnum.FIVE_YEARS, PeriodEnum.THREE_YEARS),
    TRACE_PERIOD_CBM_5Y(PeriodEnum.FIVE_YEARS, "5Y-3Y", PeriodEnum.FIVE_YEARS, PeriodEnum.THREE_YEARS);


    /**
     * 对应期限类型
     */
    private final PeriodEnum periodEnum;

    /**
     * 期限利差描述
     */
    private final String desc;

    /**
     * 减数
     */
    private final PeriodEnum subtrahendEnum;

    /**
     * 被减数
     */
    private final PeriodEnum minuendEnum;

    TracePeriodInduEnum(PeriodEnum periodEnum, String desc, PeriodEnum subtrahendEnum, PeriodEnum minuendEnum) {
        this.periodEnum = periodEnum;
        this.desc = desc;
        this.subtrahendEnum = subtrahendEnum;
        this.minuendEnum = minuendEnum;
    }

    @Override
    public PeriodEnum getPeriodEnum() {
        return periodEnum;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public PeriodEnum getSubtrahendEnum() {
        return subtrahendEnum;
    }

    @Override
    public PeriodEnum getMinuendEnum() {
        return minuendEnum;
    }
}
