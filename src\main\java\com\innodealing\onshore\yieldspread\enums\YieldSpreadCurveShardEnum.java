package com.innodealing.onshore.yieldspread.enums;

/**
 * 利差曲线分片一级分类
 * <AUTHOR>
 */
public enum YieldSpreadCurveShardEnum {

    /**
     * 无评级分片
     */
    ALL(0,"all"),

    /**
     * 产业1分片
     */
    INDU_LEVEL_1(1,"indu1"),

    /**
     * 产业2分片
     */
    INDU_LEVEL_2(2,"indu2"),

    /**
     * 城市分片
     */
    OPERATOR_CITY(3,"city"),

    /**
     * 省份分片
     */
    OPERATOR_PROVINCE(4,"province")
    ;

    private final int value;
    private final String text;

    YieldSpreadCurveShardEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    public int getValue() {
        return value;
    }

    public String getText() {
        return text;
    }
}
