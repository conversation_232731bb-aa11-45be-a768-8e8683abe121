package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvUdicBondYieldSpreadPanoramaDO;

/**
 * 城投债利差全景-物化视图
 *
 * <AUTHOR>
 **/
public interface MvUdicBondYieldSpreadPanoramaMapper extends DynamicQueryMapper<MvUdicBondYieldSpreadPanoramaDO> {

    /**
     * 刷新城投利差全景物化视图
     */
    void refreshMvUdicBondYieldSpreadPanorama();
}
