<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view.PgLgBondYieldSpreadQuantileViewMapper">

    <insert id="createLgBondYieldSpreadQuantileView">
        create
        or replace view
        ${lgQuantileMvName}
        as
        select spread_date
             , com_uni_code
             , lg_area_name
             , lg_bond_type
             , prepayment_status
             , fund_use_type
             , using_lg_bond_type
             , using_prepayment_status
             , using_fund_use_type
             , (case when credit_spread_1m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type,lg_bond_type,prepayment_status,fund_use_type ,credit_spread_1m is not null order by credit_spread_1m) end) * 100             as credit_spread_1m
             , (case when credit_spread_tb_1m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type,lg_bond_type,prepayment_status,fund_use_type ,credit_spread_tb_1m is not null order by credit_spread_tb_1m) end) * 100    as credit_spread_tb_1m
             , (case when cb_yield_1m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_1m is not null order by cb_yield_1m) end) * 100                            as cb_yield_1m

             , (case when credit_spread_3m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_3m is not null order by credit_spread_3m) end) * 100             as credit_spread_3m
             , (case when credit_spread_tb_3m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_3m is not null order by credit_spread_tb_3m) end) * 100    as credit_spread_tb_3m
             , (case when cb_yield_3m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_3m is not null order by cb_yield_3m) end) * 100                            as cb_yield_3m

             , (case when credit_spread_6m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_6m is not null order by credit_spread_6m) end) * 100             as credit_spread_6m
             , (case when credit_spread_tb_6m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_6m is not null order by credit_spread_tb_6m) end) * 100    as credit_spread_tb_6m
             , (case when cb_yield_6m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_6m is not null order by cb_yield_6m) end) * 100                            as cb_yield_6m

             , (case when credit_spread_9m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_9m is not null order by credit_spread_9m) end) * 100             as credit_spread_9m
             , (case when credit_spread_tb_9m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_9m is not null order by credit_spread_tb_9m) end) * 100    as credit_spread_tb_9m
             , (case when cb_yield_9m is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_9m is not null order by cb_yield_9m) end) * 100                            as cb_yield_9m

             , (case when credit_spread_1y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_1y is not null order by credit_spread_1y) end) * 100             as credit_spread_1y
             , (case when credit_spread_tb_1y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_1y is not null order by credit_spread_tb_1y) end) * 100    as credit_spread_tb_1y
             , (case when cb_yield_1y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_1y is not null order by cb_yield_1y) end) * 100                            as cb_yield_1y

             , (case when credit_spread_2y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_2y is not null order by credit_spread_2y) end) * 100             as credit_spread_2y
             , (case when credit_spread_tb_2y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_2y is not null order by credit_spread_tb_2y) end) * 100    as credit_spread_tb_2y
             , (case when cb_yield_2y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_2y is not null order by cb_yield_2y) end) * 100                            as cb_yield_2y

             , (case when credit_spread_3y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_3y is not null order by credit_spread_3y) end) * 100             as credit_spread_3y
             , (case when credit_spread_tb_3y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_3y is not null order by credit_spread_tb_3y) end) * 100    as credit_spread_tb_3y
             , (case when cb_yield_3y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_3y is not null order by cb_yield_3y) end) * 100                            as cb_yield_3y

             , (case when credit_spread_5y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_5y is not null order by credit_spread_5y) end) * 100             as credit_spread_5y
             , (case when credit_spread_tb_5y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_5y is not null order by credit_spread_tb_5y) end) * 100    as credit_spread_tb_5y
             , (case when cb_yield_5y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_5y is not null order by cb_yield_5y) end) * 100                            as cb_yield_5y

             , (case when credit_spread_7y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_7y is not null order by credit_spread_7y) end) * 100             as credit_spread_7y
             , (case when credit_spread_tb_7y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_7y is not null order by credit_spread_tb_7y) end) * 100    as credit_spread_tb_7y
             , (case when cb_yield_7y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_7y is not null order by cb_yield_7y) end) * 100                            as cb_yield_7y

             , (case when credit_spread_10y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_10y is not null order by credit_spread_10y) end) * 100          as credit_spread_10y
             , (case when credit_spread_tb_10y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_10y is not null order by credit_spread_tb_10y) end) * 100 as credit_spread_tb_10y
             , (case when cb_yield_10y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_10y is not null order by cb_yield_10y) end) * 100                         as cb_yield_10y

             , (case when credit_spread_15y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_15y is not null order by credit_spread_15y) end) * 100          as credit_spread_15y
             , (case when credit_spread_tb_15y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_15y is not null order by credit_spread_tb_15y) end) * 100 as credit_spread_tb_15y
             , (case when cb_yield_15y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_15y is not null order by cb_yield_15y) end) * 100                         as cb_yield_15y

             , (case when credit_spread_20y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_20y is not null order by credit_spread_20y) end) * 100          as credit_spread_20y
             , (case when credit_spread_tb_20y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_20y is not null order by credit_spread_tb_20y) end) * 100 as credit_spread_tb_20y
             , (case when cb_yield_20y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_20y is not null order by cb_yield_20y) end) * 100                         as cb_yield_20y

             , (case when credit_spread_30y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_30y is not null order by credit_spread_30y) end) * 100          as credit_spread_30y
             , (case when credit_spread_tb_30y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,credit_spread_tb_30y is not null order by credit_spread_tb_30y) end) * 100 as credit_spread_tb_30y
             , (case when cb_yield_30y is not null then percent_rank() over (partition by com_uni_code,using_lg_bond_type,using_prepayment_status,using_fund_use_type ,lg_bond_type,prepayment_status,fund_use_type,cb_yield_30y is not null order by cb_yield_30y) end) * 100                         as cb_yield_30y
        from bond_spread_lg_base
        where spread_date <![CDATA[ <= ]]> '${endDate}'
          and spread_date <![CDATA[ >= ]]> '${startDate}'
          and deleted = 0
    </insert>
    <select id="listLgBondYieldSpreadQuantileView"
            resultType="com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgBondYieldSpreadQuantileViewDO">
        SELECT spread_date             AS spreadDate,
               com_uni_code            AS com_uni_code,
               lg_area_name            AS lgAreaName,
               lg_bond_type            AS lgBondType,
               prepayment_status       AS prepaymentStatus,
               fund_use_type           AS fund_use_type,
               using_lg_bond_type      AS usingLgBondType,
               using_fund_use_type     AS usingFundUseType,
               using_prepayment_status AS usingPrepaymentStatus,
               cb_yield_1m             AS cbYield1M,
               cb_yield_3m             AS cbYield3M,
               cb_yield_6m             AS cbYield6M,
               cb_yield_9m             AS cbYield9M,
               cb_yield_1y             AS cbYield1Y,
               cb_yield_2y             AS cbYield2Y,
               cb_yield_3y             AS cbYield3Y,
               cb_yield_5y             AS cbYield5Y,
               cb_yield_7y             AS cbYield7Y,
               cb_yield_10y            AS cbYield10Y,
               cb_yield_15y            AS cbYield15Y,
               cb_yield_20y            AS cbYield20Y,
               cb_yield_30y            AS cbYield30Y,
               credit_spread_1m        AS creditSpread1M,
               credit_spread_3m        AS creditSpread3M,
               credit_spread_6m        AS creditSpread6M,
               credit_spread_9m        AS creditSpread9M,
               credit_spread_1y        AS creditSpread1Y,
               credit_spread_2y        AS creditSpread2Y,
               credit_spread_3y        AS creditSpread3Y,
               credit_spread_5y        AS creditSpread5Y,
               credit_spread_7y        AS creditSpread7Y,
               credit_spread_10y       AS creditSpread10Y,
               credit_spread_15y       AS creditSpread15Y,
               credit_spread_20y       AS creditSpread20Y,
               credit_spread_30y       AS creditSpread30Y,
               credit_spread_tb_1m     AS creditSpreadTb1M,
               credit_spread_tb_3m     AS creditSpreadTb3M,
               credit_spread_tb_6m     AS creditSpreadTb6M,
               credit_spread_tb_9m     AS creditSpreadTb9M,
               credit_spread_tb_1y     AS creditSpreadTb1Y,
               credit_spread_tb_2y     AS creditSpreadTb2Y,
               credit_spread_tb_3y     AS creditSpreadTb3Y,
               credit_spread_tb_5y     AS creditSpreadTb5Y,
               credit_spread_tb_7y     AS creditSpreadTb7Y,
               credit_spread_tb_10y    AS creditSpreadTb10Y,
               credit_spread_tb_15y    AS creditSpreadTb15Y,
               credit_spread_tb_20y    AS creditSpreadTb20Y,
               credit_spread_tb_30y    AS creditSpreadTb30Y
        FROM ${lgQuantileMvName}
        WHERE spread_date = '${spreadDate}'
    </select>
</mapper>