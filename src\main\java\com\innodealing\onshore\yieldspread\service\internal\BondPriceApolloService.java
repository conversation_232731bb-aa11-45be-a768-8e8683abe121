package com.innodealing.onshore.yieldspread.service.internal;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.onshore.bondmetadata.dto.bondpriceapollo.CurveMaturityStructureDTO;
import com.innodealing.onshore.bondmetadata.dto.bondpriceapollo.CurveMaturityStructureRequestDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.sql.Date;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * onshore-bond-price-apollo
 *
 * <AUTHOR>
 **/
@FeignClient(name = "BondPriceApolloService", url = "${bond.price.apollo.api.url}", path = "internal")
public interface BondPriceApolloService {

    /**
     * 获取某天的曲线期限结构 根据发布日期和曲线唯一编码
     *
     * @param curveUniCodes 曲线唯一编码
     * @param issueDate     发布日期
     * @return Map<Long, List < CurveMaturityStructureDTO>> key=curveUniCode
     */
    default Map<Long, List<CurveMaturityStructureDTO>> getCurveMaturityStructureMap(List<Long> curveUniCodes, Date issueDate) {
        if (CollectionUtils.isEmpty(curveUniCodes) || Objects.isNull(issueDate)) {
            return Maps.newHashMap();
        }
        CurveMaturityStructureRequestDTO requestDTO = new CurveMaturityStructureRequestDTO();
        requestDTO.setIssueDate(issueDate);
        requestDTO.setCurveUniCodes(curveUniCodes);
        return listCurveMaturityStructures(requestDTO).stream()
                .collect(Collectors.groupingBy(CurveMaturityStructureDTO::getCurveUniCode));
    }

    /**
     * 获取某天的曲线期限结构
     * 按remainingTenor升序
     *
     * @param curveUniCode 曲线唯一编码
     * @param issueDate    发布日期
     * @return List<CurveMaturityStructureDTO>
     */
    default List<CurveMaturityStructureDTO> listSortedCurveMaturityStructures(Long curveUniCode, Date issueDate) {
        return getCurveMaturityStructureMap(Lists.newArrayList(curveUniCode), issueDate).get(curveUniCode)
                .stream().filter(v -> Objects.nonNull(v.getRemainingTenor()))
                .sorted(Comparator.comparing(CurveMaturityStructureDTO::getRemainingTenor)).collect(Collectors.toList());
    }

    /**
     * 获取某天的曲线期限结构 根据发布日期和曲线唯一编码
     *
     * @param curveUniCodes 曲线唯一编码
     * @param issueDate     发布日期
     * @return List<CurveMaturityStructureDTO>
     */
    default List<CurveMaturityStructureDTO> listCurveMaturityStructures(List<Long> curveUniCodes, Date issueDate) {
        if (CollectionUtils.isEmpty(curveUniCodes) || Objects.isNull(issueDate)) {
            return Lists.newArrayList();
        }
        CurveMaturityStructureRequestDTO requestDTO = new CurveMaturityStructureRequestDTO();
        requestDTO.setIssueDate(issueDate);
        requestDTO.setCurveUniCodes(curveUniCodes);
        return listCurveMaturityStructures(requestDTO);
    }

    /**
     * 获取某天的曲线期限结构 根据发布日期和曲线唯一编码
     *
     * @param requestDTO 请求参数
     * @return List<CurveMaturityStructureDTO>
     */
    @PostMapping("curve/list")
    List<CurveMaturityStructureDTO> listCurveMaturityStructures(@RequestBody CurveMaturityStructureRequestDTO requestDTO);

}
