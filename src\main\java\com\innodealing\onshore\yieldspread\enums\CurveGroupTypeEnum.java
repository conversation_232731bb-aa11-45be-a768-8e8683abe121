package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 曲线组类型，属于CurveGroupCategoryEnum
 *
 * <AUTHOR>
 * @see CurveGroupCategoryEnum
 */
public enum CurveGroupTypeEnum implements ITextValueEnum {
    /**
     * 默认组
     */
    DEFAULT_GROUP(1, "默认组"),
    ORDINARY_GROUP(2, "普通组"),
    BENCHMARK_GROUP(3, "基准曲线组");

    CurveGroupTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    private final int value;

    private final String text;

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }
}
