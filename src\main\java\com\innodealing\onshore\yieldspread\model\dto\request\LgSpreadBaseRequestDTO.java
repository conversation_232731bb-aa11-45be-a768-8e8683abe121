package com.innodealing.onshore.yieldspread.model.dto.request;

import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBaseLgBondYieldSpreadDO;
import io.swagger.annotations.ApiModelProperty;

import java.sql.Date;
import java.util.Objects;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 地方债区域利差请求基础参数
 *
 * <AUTHOR>
 */
public class LgSpreadBaseRequestDTO extends LgSpreadSelectedRequestDTO {

    @ApiModelProperty(value = "数据类型: 1 信用利差(减国开) 3 到期收益率 4 信用利差(减国债)", required = true)
    private Integer spreadDataType;

    @ApiModelProperty(value = "利差日期", example = "2020-04-07", required = true)
    private Date spreadDate;

    @ApiModelProperty("排序字段")
    private SortDTO sort;

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }

    public Integer getSpreadDataType() {
        return spreadDataType;
    }

    public void setSpreadDataType(Integer spreadDataType) {
        this.spreadDataType = spreadDataType;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }


    /**
     * 根据请求对象获取筛选条件
     *
     * @param clazz 数据所在类
     * @param <T>   泛型
     * @return 返回筛选条件
     */
    public <T extends PgBaseLgBondYieldSpreadDO> FilterGroupDescriptor<T> listLgSpreadBaseFilters(Class<T> clazz) {
        return super.listLgSpreadSelectedFilters(clazz).and(T::getSpreadDate, isEqual(spreadDate));
    }
}
