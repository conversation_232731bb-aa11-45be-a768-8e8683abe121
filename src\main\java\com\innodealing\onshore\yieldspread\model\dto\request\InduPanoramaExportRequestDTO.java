package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.enums.*;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.COMMA;

/**
 * 行业全景导出请求dto
 *
 * <AUTHOR>
 */
public class InduPanoramaExportRequestDTO extends InduPanoramaRequestDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份", required = true)
    private Integer year;
    @ApiModelProperty(value = "年跨度，1:上半年, 2:下半年", required = true)
    private Integer yearSpan;

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getYearSpan() {
        return yearSpan;
    }

    public void setYearSpan(Integer yearSpan) {
        this.yearSpan = yearSpan;
    }

    @Override
    public String toString() {
        return "InduPanoramaExportRequestDTO{" +
                "year=" + year +
                ", yearSpan=" + yearSpan +
                ", bondExtRatingMapping=" + bondExtRatingMapping +
                ", bondImpliedRatingMappingTag=" + bondImpliedRatingMappingTag +
                ", comYyRatingMappingTag=" + comYyRatingMappingTag +
                ", spreadRemainingTenorTag=" + spreadRemainingTenorTag +
                ", spreadBondType=" + spreadBondType +
                ", guaranteeStatus=" + guaranteeStatus +
                '}';
    }

    /**
     * 构建导出标题
     *
     * @return {@link String} 导出标题
     */
    public String buildExportTitle() {
        StringBuilder sb = new StringBuilder("数据来源:DM,");
        EnumUtils.getEnumByValue(yearSpan, SpreadYearSpanEnum.class).ifPresent(span -> sb.append(year).append(span.getText()).append(COMMA));
        if (Objects.nonNull(bondExtRatingMapping)) {
            sb.append("外评").append(RatingUtils.getRating(bondExtRatingMapping)).append(COMMA);
        }
        if (Objects.nonNull(bondImpliedRatingMappingTag)) {
            sb.append("中债隐含").append(ITextValueEnum.getEnum(SpreadInduBondImpliedRatingMappingTagEnum.class, bondImpliedRatingMappingTag).getText()).append(COMMA);
        }
        if (Objects.nonNull(comYyRatingMappingTag)) {
            sb.append("YY").append(ITextValueEnum.getEnum(SpreadComYyRatingMappingTagEnum.class, comYyRatingMappingTag).getText()).append(COMMA);
        }
        if (Objects.nonNull(spreadRemainingTenorTag)) {
            sb.append(spreadRemainingTenorTag).append("Y").append(COMMA);
        }
        if (Objects.nonNull(spreadBondType)) {
            sb.append(ITextValueEnum.getEnum(SpreadBondTypeEnum.class, spreadBondType).getText()).append(COMMA);
        }
        if (Objects.nonNull(guaranteeStatus)) {
            sb.append(ITextValueEnum.getEnum(GuaranteedStatusEnum.class, guaranteeStatus).getDesc()).append(COMMA);
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }
}
