package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 用户配置枚举
 *
 * <AUTHOR>
 * @date 2024/10/18 13:42
 **/
public enum UserConfigEnum implements ITextValueEnum {
    /**
     * 用户全局配置枚举
     */
    CONFIG_10001(10001, "全景-展示品种"),
    CONFIG_10002(10002, "全景-展示期限"),
    CONFIG_20402(20402, "利差追踪-中短期票据-展示期限"),
    CONFIG_20502(20502, "利差追踪-产业债-展示期限"),
    CONFIG_20602(20602, "利差追踪-城投债-展示期限"),
    CONFIG_20702(20702, "利差追踪-银行普通债-展示期限"),
    CONFIG_20802(20802, "利差追踪-银行二级资本债-展示期限"),
    CONFIG_20902(20902, "利差追踪-银行永续债-展示期限"),
    CONFIG_21002(21002, "利差追踪-证券公司债-展示期限"),
    CONFIG_21102(21102, "利差追踪-证券次级债-展示期限"),
    CONFIG_21202(21202, "利差追踪-证券永续债-展示期限"),
    CONFIG_21402(21402, "利差追踪-保险资本补充-展示期限"),
    CONFIG_21702(21702, "利差追踪-利率债-展示期限"),
    CONFIG_30103(30103, "地方区域债-展示地区"),
    CONFIG_30102(30102, "地方区域债-展示期限");

    UserConfigEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    private final int value;

    private final String text;


    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }
}
