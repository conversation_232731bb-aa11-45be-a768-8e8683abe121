package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.InsuBondYieldSpreadGroupDO;

/**
 * 保险债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface InsuBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<InsuBondYieldSpreadDO,
        InsuBondYieldSpreadGroupDO> {

}
