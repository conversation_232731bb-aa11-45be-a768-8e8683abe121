package com.innodealing.onshore.yieldspread.service;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.TrendReplayComYieldSpreadRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.UdicListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicComYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicComYieldSpreadDO;
import org.springframework.lang.Nullable;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 城投主体利差 Service
 *
 * <AUTHOR>
 **/
public interface UdicComYieldSpreadService {

    /**
     * 城投主体利差计算
     *
     * @param comYieldSpreadDOs 城投主体利差数据
     * @param spreadDate        利差日期
     * @param isEnableOldData   是否启用老数据
     * @return InduComYieldSpreadDO
     */
    Integer calcUdicComYieldSpreadsBySpreadDate(List<UdicComYieldSpreadDO> comYieldSpreadDOs,
                                                Date spreadDate, Boolean isEnableOldData);

    /**
     * 城投主体利差分页查询
     *
     * @param request 城投主体利差分页查询请求参数
     * @return {@link NormPagingResult}<{@link UdicComYieldSpreadResponseDTO}> 城投主体利差分页查询响应DTO
     */
    NormPagingResult<UdicComYieldSpreadResponseDTO> getComYieldSpreadPaging(UdicListRequestDTO request);

    /**
     * 城投主体利差分页查询(测试)
     *
     * @param request 城投主体利差分页查询请求参数
     * @return {@link NormPagingResult}<{@link UdicComYieldSpreadResponseDTO}> 城投主体利差分页查询响应DTO
     */
    NormPagingResult<UdicComYieldSpreadResponseDTO> getComYieldSpreadPagingByExists(UdicListRequestDTO request);

    /**
     * 获取最大利差日期
     *
     * @return {@link String} 利差日期
     */
    Date getMaxSpreadDate();

    /**
     * 城投主体利差分页查询总数
     *
     * @param request 城投主体利差分页查询总数请求参数
     * @return {@link Long} 总条数
     */
    Long getComYieldSpreadPagingCount(UdicListRequestDTO request);

    /**
     * 获取主体利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体利差
     */
    List<UdicComYieldSpreadResponseDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 获取主体数量
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体数量
     */
    Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 获取城投主体利差数据集
     *
     * @param spreadDate 利差日期
     * @param udicComs   主体唯一编码集合
     * @return {@link List}<{@link UdicComYieldSpreadResponseDTO}>
     */
    List<UdicComYieldSpreadResponseDTO> listComs(Date spreadDate, @Nullable Set<Long> udicComs);

    /**
     * 查询利差曲线数据集
     *
     * @param comUniCode     主体唯一编码
     * @param spreadBondType 利差债券类型
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @return {@link List}<{@link ComYieldSpreadCurveDTO}>
     */
    List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer spreadBondType, Date startDate, Date endDate);

    /**
     * 查询走势复盘主体利差信用利差(全部)数据
     *
     * @param spreadStartDate 开始日期
     * @param spreadEndDate   结束日期
     * @param requestComList  主体请求体
     * @return list
     */
    List<ComCreditSpreadDTO> listTrendReplayComYieldSpreads(Date spreadStartDate, Date spreadEndDate,
                                                            List<TrendReplayComYieldSpreadRequestDTO> requestComList);

    /**
     * 查询发行人的最新的信用主体利差
     *
     * @param comUniCodes 发行人代码集合
     * @return {@link List}<{@link ComCreditSpreadDTO}>
     */
    List<ComCreditSpreadDTO> listCreditSpreadByComUniCodes(Set<Long> comUniCodes);
}
