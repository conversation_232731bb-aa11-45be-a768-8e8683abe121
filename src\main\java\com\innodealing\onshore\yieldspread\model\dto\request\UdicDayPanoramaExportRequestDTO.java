package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.AreaTypeEnum;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.enums.GuaranteedStatusEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadComYyRatingMappingTagEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadInduBondImpliedRatingMappingTagEnum;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.COMMA;

/**
 * 城投全景导出请求dto
 *
 * <AUTHOR>
 */
public class UdicDayPanoramaExportRequestDTO extends UdicPanoramaRequestDTO {
    private static final long serialVersionUID = 1L;
    /**
     * 行政区划映射
     */
    private static final Map<Integer, String> ADMINISTRATIVE_DIVISION_MAP = new HashMap<>();

    static {
        ADMINISTRATIVE_DIVISION_MAP.put(AreaTypeEnum.PROVINCE.getValue(), "省级");
        ADMINISTRATIVE_DIVISION_MAP.put(AreaTypeEnum.CITY.getValue(), "市级");
        ADMINISTRATIVE_DIVISION_MAP.put(AreaTypeEnum.DISTRICT.getValue(), "区/县级");
        ADMINISTRATIVE_DIVISION_MAP.put(AreaTypeEnum.ZONE.getValue(), "园区");
    }

    @ApiModelProperty(value = "生成日期", required = true)
    @NotNull
    private Date selectDate;
    @ApiModelProperty(value = "1:省级，2:市级", required = true)
    private Integer areaType;

    public Date getSelectDate() {
        return Objects.isNull(selectDate) ? null : new Date(selectDate.getTime());
    }

    public void setSelectDate(Date selectDate) {
        this.selectDate = new Date(selectDate.getTime());
    }

    public Integer getAreaType() {
        return areaType;
    }

    public void setAreaType(Integer areaType) {
        this.areaType = areaType;
    }

    @Override
    public String toString() {
        return "UdicPanoramaExportRequestDTO{" +
                "selectDate=" + selectDate +
                ", areaType=" + areaType +
                ", administrativeDivision=" + administrativeDivision +
                ", bondExtRatingMapping=" + bondExtRatingMapping +
                ", bondImpliedRatingMappingTag=" + bondImpliedRatingMappingTag +
                ", comYyRatingMappingTag=" + comYyRatingMappingTag +
                ", spreadRemainingTenorTag=" + spreadRemainingTenorTag +
                ", spreadBondType=" + spreadBondType +
                ", guaranteeStatus=" + guaranteeStatus +
                '}';
    }

    /**
     * 构建导出标题
     *
     * @return {@link String} 导出标题
     */
    public String buildExportTitle() {
        StringBuilder sb = new StringBuilder("数据来源:DM,");
        if (Objects.nonNull(selectDate)) {
            sb.append(selectDate.toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE)).append(COMMA);
        }
        if (Objects.nonNull(bondExtRatingMapping)) {
            sb.append("外评").append(RatingUtils.getRating(bondExtRatingMapping)).append(COMMA);
        }
        if (Objects.nonNull(bondImpliedRatingMappingTag)) {
            sb.append("中债隐含").append(ITextValueEnum.getEnum(SpreadInduBondImpliedRatingMappingTagEnum.class, bondImpliedRatingMappingTag).getText()).append(COMMA);
        }
        if (Objects.nonNull(comYyRatingMappingTag)) {
            sb.append("YY").append(ITextValueEnum.getEnum(SpreadComYyRatingMappingTagEnum.class, comYyRatingMappingTag).getText()).append(COMMA);
        }
        if (Objects.nonNull(spreadRemainingTenorTag)) {
            sb.append(spreadRemainingTenorTag).append("Y").append(COMMA);
        }
        if (Objects.nonNull(spreadBondType)) {
            sb.append(ITextValueEnum.getEnum(SpreadBondTypeEnum.class, spreadBondType).getText()).append(COMMA);
        }
        if (Objects.nonNull(guaranteeStatus)) {
            sb.append(ITextValueEnum.getEnum(GuaranteedStatusEnum.class, guaranteeStatus).getDesc()).append(COMMA);
        }
        if (Objects.nonNull(administrativeDivision)) {
            sb.append(ADMINISTRATIVE_DIVISION_MAP.get(administrativeDivision)).append(COMMA);
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }
}
