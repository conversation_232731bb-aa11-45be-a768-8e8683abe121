package com.innodealing.onshore.yieldspread.service;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondFilterV3DTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.BankCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BankSingleBondYieldSpreadResDTO;
import org.springframework.lang.Nullable;

import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 银行债利差 Service
 *
 * <AUTHOR>
 **/
public interface BankBondYieldSpreadService {

    /**
     * 银行利率债
     *
     * @param onshoreBondInfoDTOs     债券info
     * @param onshoreBondFilterV3DTOS filter属性
     * @param bondYieldCurveMap       主体
     * @param spreadDate              利差日期
     * @return count
     */
    int calcBankBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs,
                                             Map<Long, OnshoreBondFilterV3DTO> onshoreBondFilterV3DTOS,
                                             Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                             Date spreadDate);

    /**
     * 刷新历史物化视图
     *
     * @param isTableRefresh 是否刷新表结构
     */
    void refreshMvBankBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh);

    /**
     * 刷新物化视图
     *
     * @param param 时间片
     */
    void refreshMvBankBondYieldSpreadRatingCurve(RefreshYieldCurveParam param);

    /**
     * 存入曲线池
     *
     * @param userid       用户id
     * @param curveGroupId 曲线组id
     * @param params       保存曲线参数
     * @return 操作结果
     */
    boolean saveCurve(Long userid, Long curveGroupId, BankCurveGenerateConditionReqDTO params);

    /**
     * 更新曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @param request 更新参数
     * @return 执行结果
     */
    boolean updateCurve(Long userid, Long curveId, BankCurveGenerateConditionReqDTO request);

    /**
     * 银行单券利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 单券利差
     */
    NormPagingResult<BankSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 查询曲线数据集
     *
     * @param bondUniCode 债券唯一编码
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveDTO}>
     */
    List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate);

    /**
     * 查询债券数据集合
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link BankSingleBondYieldSpreadResDTO}>
     */
    List<BankSingleBondYieldSpreadResDTO> listBonds(Date spreadDate, @Nullable Set<Long> bondUniCodes);
}
