package com.innodealing.onshore.yieldspread.helper;

import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.yieldspread.exception.TipsException;

/**
 * 断言工具类
 *
 * <AUTHOR>
 */
@SuppressWarnings("squid:S2301")
public final class AssertUtil {

    /**
     * 断言一个布尔表达式，如果表达式的计算结果为false，则抛出RuntimeException。
     *
     * @param expression a boolean expression
     * @param exception  businessException
     */
    public static void isTrue(boolean expression, RuntimeException exception) {
        if (!expression) {
            throw exception;
        }
    }

    /**
     * 断言一个布尔表达式，如果表达式的计算结果为false，则抛出BusinessException。
     *
     * @param expression        a boolean expression
     * @param businessException businessException
     */
    public static void isTrue(boolean expression, BusinessException businessException) {
        if (!expression) {
            throw businessException;
        }
    }

    /**
     * 断言一个布尔表达式，如果表达式的计算结果为false，则抛出TipsException。
     *
     * @param expression        a boolean expression
     * @param expressionMessage expression message
     */
    public static void isTrue(boolean expression, String expressionMessage) {
        if (!expression) {
            throw new TipsException(expressionMessage);
        }
    }

    /**
     * 断言一个布尔表达式，如果表达式的计算结果为true，则抛出TipsException。
     *
     * @param expression        a boolean expression
     * @param expressionMessage expression message
     */
    public static void isFalse(boolean expression, String expressionMessage) {
        if (expression) {
            throw new TipsException(expressionMessage);
        }
    }

    private AssertUtil() {
    }

}
