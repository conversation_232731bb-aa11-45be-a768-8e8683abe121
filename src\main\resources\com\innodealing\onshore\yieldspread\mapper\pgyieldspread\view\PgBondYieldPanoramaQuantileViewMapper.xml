<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view.PgBondYieldPanoramaQuantileViewMapper">

    <insert id="createPanoramaQuantileView">
        create or replace view v_bond_yield_panorama_quantile as select
        issue_date,
        curve_code,
        (case when ytm_1m is not null then percent_rank() over (partition by curve_code,ytm_1m is not null order by ytm_1m) end) * 100 as ytm_1m,
        (case when ytm_3m is not null then percent_rank() over (partition by curve_code,ytm_3m is not null order by ytm_3m) end) * 100 as ytm_3m,
        (case when ytm_6m is not null then percent_rank() over (partition by curve_code,ytm_6m is not null order by ytm_6m) end) * 100 as ytm_6m,
        (case when ytm_9m is not null then percent_rank() over (partition by curve_code,ytm_9m is not null order by ytm_9m) end) * 100 as ytm_9m,
        (case when ytm_1y is not null then percent_rank() over (partition by curve_code,ytm_1y is not null order by ytm_1y) end) * 100 as ytm_1y,
        (case when ytm_2y is not null then percent_rank() over (partition by curve_code,ytm_2y is not null order by ytm_2y) end) * 100 as ytm_2y,
        (case when ytm_3y is not null then percent_rank() over (partition by curve_code,ytm_3y is not null order by ytm_3y) end) * 100 as ytm_3y,
        (case when ytm_4y is not null then percent_rank() over (partition by curve_code,ytm_4y is not null order by ytm_4y) end) * 100 as ytm_4y,
        (case when ytm_5y is not null then percent_rank() over (partition by curve_code,ytm_5y is not null order by ytm_5y) end) * 100 as ytm_5y,
        (case when ytm_6y is not null then percent_rank() over (partition by curve_code,ytm_6y is not null order by ytm_6y) end) * 100 as ytm_6y,
        (case when ytm_7y is not null then percent_rank() over (partition by curve_code,ytm_7y is not null order by ytm_7y) end) * 100 as ytm_7y,
        (case when ytm_10y is not null then percent_rank() over (partition by curve_code,ytm_10y is not null order by ytm_10y) end) * 100 as ytm_10y,
        (case when ytm_15y is not null then percent_rank() over (partition by curve_code,ytm_15y is not null order by ytm_15y) end) * 100 as ytm_15y,
        (case when ytm_20y is not null then percent_rank() over (partition by curve_code,ytm_20y is not null order by ytm_20y) end) * 100 as ytm_20y,
        (case when ytm_30y is not null then percent_rank() over (partition by curve_code,ytm_30y is not null order by ytm_30y) end) * 100 as ytm_30y,
        (case when ytm_50y is not null then percent_rank() over (partition by curve_code,ytm_50y is not null order by ytm_50y) end) * 100 as ytm_50y
        from bond_yield_panorama_abs
        where issue_date <![CDATA[ <= ]]> '${endDate}'
        and issue_date <![CDATA[ >= ]]> '${startDate}'
        and deleted = 0
    </insert>
</mapper>