package com.innodealing.onshore.yieldspread.model.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 自定义曲线主体利差
 *
 * <AUTHOR>
 */
public class CustomComYieldSpreadBO {

    @ApiModelProperty("发行人代码")
    private Long comUniCode;

    @ApiModelProperty("利差日期")
    private Date spreadDate;

    @ApiModelProperty("主体评级")
    private Integer comExtRatingMapping;

    @ApiModelProperty("总资产(万元)")
    private BigDecimal totalAssets;

    @ApiModelProperty("主体信用利差(全部债券);单位(BP)")
    private BigDecimal comCreditSpread;

    @ApiModelProperty("主体超额利差(全部债券);单位(BP)")
    private BigDecimal comExcessSpread;

    @ApiModelProperty("主体估值收益率(全部债券);单位(%)")
    private BigDecimal comCbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

}
