package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvInsuBondYieldSpreadCurveParameter;
import org.apache.ibatis.annotations.Param;

/**
 * 保险利差曲线物化视图
 *
 * <AUTHOR>
 */
public interface MvInsuBondYieldSpreadCurveMapper extends PgBaseMapper<MvInsuBondYieldSpreadCurveParameter> {


    /**
     * 创建物化视图
     *
     * @param parameter 创建条件
     */
    void createMvRatingRouter(@Param("parameter") MvInsuBondYieldSpreadCurveParameter parameter);

}
