package com.innodealing.onshore.yieldspread.processor;

import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 利差追踪-默认利差处理器
 *
 * <AUTHOR>
 */
@Component
public class DefaultYieldSpreadTraceProcessor implements YieldSpreadTraceProcessor {

    @Override
    public Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum) {
        return true;
    }

    @Override
    public List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context) {
        List<PgBondYieldSpreadTraceAbsDO> traceList = Lists.newArrayList();
        List<PgBondYieldPanoramaAbsDO> absPanoramaList = context.getAbsBondYieldPanoramas();
        for (PgBondYieldPanoramaAbsDO bondYieldPanoramaAbsDO : absPanoramaList) {
            PgBondYieldSpreadTraceAbsDO pgBondYieldSpreadTraceAbsDO =
                    BeanCopyUtils.copyProperties(bondYieldPanoramaAbsDO, PgBondYieldSpreadTraceAbsDO.class);
            pgBondYieldSpreadTraceAbsDO.setBondType(context.getBondTypeEnum().getValue());
            pgBondYieldSpreadTraceAbsDO.setChartType(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue());
            pgBondYieldSpreadTraceAbsDO.setCurveCode(bondYieldPanoramaAbsDO.getCurveCode());
            pgBondYieldSpreadTraceAbsDO.setIssueDate(context.getIssueDate());
            pgBondYieldSpreadTraceAbsDO.setDeleted(Deleted.NO_DELETED.getValue());
            traceList.add(pgBondYieldSpreadTraceAbsDO);
        }
        return traceList;
    }
}
