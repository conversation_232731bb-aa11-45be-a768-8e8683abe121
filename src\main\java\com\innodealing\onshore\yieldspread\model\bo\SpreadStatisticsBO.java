package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;

/**
 * 利差统计 最大值  最小值  中位数 对象
 *
 * <AUTHOR>
 * @date 2024/10/21 11:13
 **/
public class SpreadStatisticsBO {

    private BigDecimal minYield;

    private BigDecimal maxYield;

    private BigDecimal medianYield;

    public BigDecimal getMinYield() {
        return minYield;
    }

    public void setMinYield(BigDecimal minYield) {
        this.minYield = minYield;
    }

    public BigDecimal getMaxYield() {
        return maxYield;
    }

    public void setMaxYield(BigDecimal maxYield) {
        this.maxYield = maxYield;
    }

    public BigDecimal getMedianYield() {
        return medianYield;
    }

    public void setMedianYield(BigDecimal medianYield) {
        this.medianYield = medianYield;
    }
}
