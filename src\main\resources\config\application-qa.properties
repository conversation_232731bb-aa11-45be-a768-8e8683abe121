server.port=8080
server.servlet.context-path=/onshore-yield-spread
spring.jackson.serialization.write-dates-as-timestamps=true
# Druid
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.allow=
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.web-stat-filter.enabled=true
#mysql
yieldspread.datasource.url=*****************************************************************************************************************************************************************************************************
yieldspread.datasource.username=ops_ops
yieldspread.datasource.password=t8Vq*WbtQXc#2
yieldspread.datasource.driver-class-name=com.mysql.jdbc.Driver
yieldspread.datasource.db-type=mysql
yieldspread.datasource.use-global-data-source-stat=true
yieldspread.datasource.pool-prepared-statements=true
yieldspread.datasource.filters=stat,wall
yieldspread.datasource.test-while-idle=true
yieldspread.datasource.initial-size=5
yieldspread.datasource.min-idle=5
yieldspread.datasource.max-active=50
yieldspread.datasource.time-between-eviction-runs-millis=60000
yieldspread.datasource.min-evictable-idle-time-millis=300000
yieldspread.datasource.validation-query=SELECT 1
# dmdc
dmdc.datasource.url=****************************************************************************************************************************************************************
dmdc.datasource.username=ops_readonly
dmdc.datasource.password=t8Vq*WbtQXc#3
dmdc.datasource.driver-class-name=com.mysql.jdbc.Driver
dmdc.datasource.db-type=mysql
dmdc.datasource.use-global-data-source-stat=true
dmdc.datasource.pool-prepared-statements=true
dmdc.datasource.filters=stat,wall
dmdc.datasource.test-while-idle=true
dmdc.datasource.initial-size=5
dmdc.datasource.min-idle=5
dmdc.datasource.max-active=50
dmdc.datasource.time-between-eviction-runs-millis=60000
dmdc.datasource.min-evictable-idle-time-millis=300000
dmdc.datasource.validation-query=SELECT 1
# postgresql
pgyieldspread.datasource.url=************************************************************************************************************************************
pgyieldspread.datasource.username=adbadmin
pgyieldspread.datasource.password=qicY30WxyJ123
pgyieldspread.datasource.driver-class-name=org.postgresql.Driver
pgyieldspread.datasource.db-type=postgresql
pgyieldspread.datasource.use-global-data-source-stat=true
pgyieldspread.datasource.pool-prepared-statements=true
pgyieldspread.datasource.filters=stat
pgyieldspread.datasource.initial-size=5
pgyieldspread.datasource.min-idle=5
pgyieldspread.datasource.max-active=50
pgyieldspread.datasource.time-between-eviction-runs-millis=60000
pgyieldspread.datasource.validation-query=SELECT 'x'
# test connection is while idle
pgyieldspread.datasource.test-while-idle=true
#30 min
pgyieldspread.datasource.min-evictable-idle-time-millis=1800000
#3 hour     this default value is 7 hour\uFF0C bug we pgsql server config is 6 hour\u3002 so throw "server closed the connection unexpectedly"
pgyieldspread.datasource.max-evictable-idle-time-millis=10800000
# default value
sharding.show.sql=true
sharding.yield.spread=2017-01-01
# http
#url.prefix=https://web.innodealing.com
#url.prefix=http://restnewdev.innodealing.com
url.prefix=http://restnewqa.innodealing.com
bond.basic.api.url=${url.prefix}/onshore-bond-basic
com.service.api.url=${url.prefix}/onshore-com-service
udic.service.api.url=${url.prefix}/onshore-udic-service
bond.price.api.url=${url.prefix}/onshore-bond-price
bond.price.apollo.api.url=${url.prefix}/onshore-bond-price-apollo
bond.rating.api.url=${url.prefix}/onshore-bond-rating
area.service.api.url=${url.prefix}/onshore-area-service
bond.finance.api.url=${url.prefix}/onshore-bond-finance
bond.financial.institution.api.url=${url.prefix}/onshore-financial-institution
user.service.api.url=http://restnewqa.innodealing.com/onshore-user-service

# redis
#spring.redis.host=r-uf6whjg0sb78oqq66b.redis.rds.aliyuncs.com
#spring.redis.port=6379
#spring.redis.password=t8Vq*WbtQXc#5
#spring.redis.database=0
spring.redis.host=*************
spring.redis.port=6379
spring.redis.password=!qaz@wsx
spring.redis.database=1

#rocketmq
rocketmq.name-server=************:9876
rocketmq.producer.access-key=superadmin
rocketmq.producer.secret-key=12345678
rocketmq.producer.group=onshore-yield-spread
#logging.level.com.innodealing.onshore.yieldspread.mapper=debug
logging.level.com.innodealing.onshore.yieldspread=info
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
logging.level.com.innodealing.onshore.yieldspread.mapper=debug