package com.innodealing.onshore.yieldspread.model.dto;

import com.google.common.collect.Lists;
import com.innodealing.commons.object.ObjectExtensionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.lang.NonNull;

import java.sql.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 收益率全景+利差追踪+地方债区域利差利差日期DTO
 *
 * <AUTHOR>
 */
public class BondYieldPanoramaTraceSpreadDateDTO {
    /**
     * 绝对值利差日期
     */
    private Date absSpreadDate;
    /**
     * 历史分位利差日期
     */
    private Date quantileSpreadDate;
    /**
     * 区间变动利差日期
     */
    private Date changeSpreadDate;

    /**
     * 获取地方债利差有数据最大日期
     *
     * @return {@link Date} 利差日期
     */
    public Optional<Date> maxSpreadDate(){
        if (ObjectUtils.allNull(absSpreadDate, quantileSpreadDate, changeSpreadDate)) {
            return Optional.empty();
        }
        List<Date> list = Lists.newArrayList(absSpreadDate, quantileSpreadDate, changeSpreadDate);
        return list.stream().filter(Objects::nonNull).max(java.util.Date::compareTo);
    }

    /**
     * 获取或者提供默认绝对值利差日期
     *
     * @param spreadDate 默认利差日期
     * @return {@link Date} 利差日期
     */
    public Date getOrDefaultAbs(@NonNull Date spreadDate) {
        return ObjectExtensionUtils.getOrDefault(absSpreadDate, spreadDate);
    }

    /**
     * 获取或者提供默认区间变动利差日期
     *
     * @param spreadDate 默认利差日期
     * @return {@link Date} 利差日期
     */
    public Date getOrDefaultChange(@NonNull Date spreadDate) {
        return ObjectExtensionUtils.getOrDefault(changeSpreadDate, spreadDate);
    }

    /**
     * 获取或者提供默认年分位利差日期
     *
     * @param spreadDate 默认利差日期
     * @return {@link Date} 利差日期
     */
    public Date getOrDefaultQuantile(@NonNull Date spreadDate) {
        return ObjectExtensionUtils.getOrDefault(quantileSpreadDate, spreadDate);
    }

    public Date getAbsSpreadDate() {
        return Objects.isNull(absSpreadDate) ? null : new Date(absSpreadDate.getTime());
    }

    public void setAbsSpreadDate(Date absSpreadDate) {
        this.absSpreadDate = Objects.isNull(absSpreadDate) ? null : new Date(absSpreadDate.getTime());
    }

    public Date getQuantileSpreadDate() {
        return Objects.isNull(quantileSpreadDate) ? null : new Date(quantileSpreadDate.getTime());
    }

    public void setQuantileSpreadDate(Date quantileSpreadDate) {
        this.quantileSpreadDate = Objects.isNull(quantileSpreadDate) ? null : new Date(quantileSpreadDate.getTime());
    }

    public Date getChangeSpreadDate() {
        return Objects.isNull(changeSpreadDate) ? null : new Date(changeSpreadDate.getTime());
    }

    public void setChangeSpreadDate(Date changeSpreadDate) {
        this.changeSpreadDate = Objects.isNull(changeSpreadDate) ? null : new Date(changeSpreadDate.getTime());
    }
}
