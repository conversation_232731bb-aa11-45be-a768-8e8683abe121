package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;


/**
 * 城投债券利差曲线物化视图DO(包含省)
 *
 * <AUTHOR>
 */
@Table(name = "mv_udic_bond_yield_spread_curve_province")
public class MvUdicBondYieldSpreadCurveProvinceDO extends BaseMvUdicBondYieldSpreadCurveDO {
    /**
     * 省级编码
     */
    @Column
    private Long provinceUniCode;

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }
}