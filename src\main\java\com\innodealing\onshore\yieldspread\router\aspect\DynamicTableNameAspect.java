package com.innodealing.onshore.yieldspread.router.aspect;

import com.innodealing.onshore.yieldspread.helper.DynamicTableNameParamUtil;
import com.innodealing.onshore.yieldspread.helper.ShardingHindStrParamUtil;
import com.innodealing.onshore.yieldspread.model.bo.DynamicTableNameBO;
import com.innodealing.onshore.yieldspread.router.annotation.DynamicTableNameParam;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * mybatis动态表 切面
 *
 * <AUTHOR>
 * @date 2024/6/11 14:26
 **/
@Aspect
@Order(5)
@Component
public class DynamicTableNameAspect {

    /**
     * 注解切面
     */
    @Pointcut("@annotation(com.innodealing.onshore.yieldspread.router.annotation.DynamicTableNameParam)")
    public void dynamicTableNameAnnotation() {
        // the pointcut expression
    }

    /**
     * 环绕通知
     *
     * @param point 切点
     * @return object
     * @throws Throwable 异常
     */
    @Around("dynamicTableNameAnnotation()")
    public Object doSharding(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method repositoryMethod = signature.getMethod();
        DynamicTableNameParam annotation = repositoryMethod.getAnnotation(DynamicTableNameParam.class);
        String logicTableName = annotation.logicTableName();
        String shardingHindStrParam = ShardingHindStrParamUtil.getHindStrParam();
        if (StringUtils.isBlank(shardingHindStrParam) || StringUtils.isBlank(logicTableName)) {
            return point.proceed();
        }
        String realTableName = String.format("%s_%s", logicTableName, shardingHindStrParam.trim());
        DynamicTableNameBO dynamicTableNameBO = new DynamicTableNameBO();
        dynamicTableNameBO.setRealTableName(realTableName);
        dynamicTableNameBO.setLogicTableName(logicTableName);
        try {
            DynamicTableNameParamUtil.setDynamicTableNameParamLocal(dynamicTableNameBO);
            return point.proceed();
        } finally {
            DynamicTableNameParamUtil.removeDynamicTableNameParam();
        }
    }
}
