package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.YieldSpreadCompositeSearchMapper;
import com.innodealing.onshore.yieldspread.model.bo.CustomComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.CustomSingleBondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.CustomYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 利差综合查询
 *
 * <AUTHOR>
 */
@Repository
public class YieldSpreadCompositeSearchDAO {

    @Resource
    private YieldSpreadCompositeSearchMapper yieldSpreadCompositeSearchMapper;

    /**
     * 自定义曲线单券利差分页查询
     *
     * @param param 查询参数
     * @return 自定义曲线单券利差
     */
    public List<CustomSingleBondYieldSpreadBO> listSingleBondYieldSpreads(CustomYieldSearchParam param) {
        if (Objects.nonNull(param.getSort())) {
            SortDTO sort = param.getSort();
            param.setSort(YieldSpreadHelper.convertSortPropertyToTableColumName(sort, BankBondYieldSpreadDO.class));
        }
        return yieldSpreadCompositeSearchMapper.listSingleBondYieldSpreads(param);
    }

    /**
     * 定义曲线主体利差数量
     *
     * @param customYieldSearchParam 查询参数
     * @return 主体利差数量
     */
    public Long countSingleBondYieldSpread(CustomYieldSearchParam customYieldSearchParam) {
        return yieldSpreadCompositeSearchMapper.countSingleBondYieldSpread(customYieldSearchParam);
    }

    /**
     * 分页查询自定义曲线主体利差
     *
     * @param param 查询参数
     * @return 主体利差
     */
    public List<CustomComYieldSpreadBO> listComYieldSpreads(CustomYieldSearchParam param) {
        return yieldSpreadCompositeSearchMapper.listComYieldSpreads(param);
    }

    /**
     * 定义曲线主体利差数量
     *
     * @param customYieldSearchParam 查询参数
     * @return 主体利差数量
     */
    public Long countComYieldSpread(CustomYieldSearchParam customYieldSearchParam) {
        return yieldSpreadCompositeSearchMapper.countComYieldSpread(customYieldSearchParam);
    }

}
