package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBondYieldSpreadTraceQuantileMapper;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceQuantileDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;


/**
 * 债券利差追踪-历史分位DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgBondYieldSpreadTraceQuantileDAO {

    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d:%d:%d";
    private static final String BOND_YIELD_SPREAD_TRACE_QUANTILE_PK = "yieldSpread:bondYieldSpreadTraceQuantilePk";

    @Resource
    private PgBondYieldSpreadTraceQuantileMapper bondYieldSpreadTraceQuantileMapper;
    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;
    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;

    /**
     * 收益率追踪查询
     *
     * @param bondTypeEnum 债券类型
     * @param spreadDate   利率时间
     * @param quantileType 分位类型 1:3年 2:5年
     * @return 收益率追踪
     */
    public List<PgBondYieldSpreadTraceBO> listYieldSpreadTraces(YieldPanoramaBondTypeEnum bondTypeEnum, Date spreadDate, Integer quantileType) {
        if (!ObjectUtils.allNotNull(bondTypeEnum, spreadDate)) {
            return Collections.emptyList();
        }
        Long minPk = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<PgBondYieldSpreadTraceQuantileDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceQuantileDO.class)
                .and(PgBondYieldSpreadTraceQuantileDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldSpreadTraceQuantileDO::getId, lessThanOrEqual(maxPk))
                .and(PgBondYieldSpreadTraceQuantileDO::getBondType, isEqual(bondTypeEnum.getValue()))
                .and(PgBondYieldSpreadTraceQuantileDO::getQuantileType, isEqual(quantileType))
                .and(PgBondYieldSpreadTraceQuantileDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<PgBondYieldSpreadTraceQuantileDO> pgBondYieldPanoramaChangeDOList =
                bondYieldSpreadTraceQuantileMapper.selectByDynamicQuery(dynamicQuery);
        return CollectionUtils.isNotEmpty(pgBondYieldPanoramaChangeDOList) ?
                BeanCopyUtils.copyList(pgBondYieldPanoramaChangeDOList, PgBondYieldSpreadTraceBO.class) : Lists.newArrayList();
    }

    /**
     * 保存利差追踪-历史分位数据集
     *
     * @param issueDate                    发行日期
     * @param yieldSpreadTraceQuantileList 利差追踪-历史分位数据集
     * @return int 保存行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int saveBondYieldSpreadTraceQuantileList(@NonNull Date issueDate, List<PgBondYieldSpreadTraceQuantileDO> yieldSpreadTraceQuantileList) {
        if (CollectionUtils.isEmpty(yieldSpreadTraceQuantileList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        DynamicQuery<PgBondYieldSpreadTraceQuantileDO> query = DynamicQuery.createQuery(PgBondYieldSpreadTraceQuantileDO.class)
                .and(PgBondYieldSpreadTraceQuantileDO::getIssueDate, isEqual(issueDate));
        Map<String, PgBondYieldSpreadTraceQuantileDO> curveCodeIssueDateMap = bondYieldSpreadTraceQuantileMapper.selectByDynamicQuery(query).stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        List<PgBondYieldSpreadTraceQuantileDO> insertList = new ArrayList<>();
        List<PgBondYieldSpreadTraceQuantileDO> updateList = new ArrayList<>();
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        for (PgBondYieldSpreadTraceQuantileDO bondYieldSpreadTraceQuantileDO : yieldSpreadTraceQuantileList) {
            String key = this.getKey(bondYieldSpreadTraceQuantileDO);
            if (curveCodeIssueDateMap.containsKey(key)) {
                PgBondYieldSpreadTraceQuantileDO existBondYieldPanoramaQuantile = curveCodeIssueDateMap.get(key);
                bondYieldSpreadTraceQuantileDO.setId(existBondYieldPanoramaQuantile.getId());
                bondYieldSpreadTraceQuantileDO.setCreateTime(null);
                bondYieldSpreadTraceQuantileDO.setUpdateTime(now);
                updateList.add(bondYieldSpreadTraceQuantileDO);
            } else {
                bondYieldSpreadTraceQuantileDO.setId(redisService.generatePk(BOND_YIELD_SPREAD_TRACE_QUANTILE_PK, bondYieldSpreadTraceQuantileDO.getIssueDate()));
                bondYieldSpreadTraceQuantileDO.setCreateTime(now);
                bondYieldSpreadTraceQuantileDO.setUpdateTime(now);
                insertList.add(bondYieldSpreadTraceQuantileDO);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param quantile 利差追踪-历史分位
     * @return {@link String} key值
     */
    private String getKey(PgBondYieldSpreadTraceQuantileDO quantile) {
        return String.format(NAMESPACE_KEY_PLACEHOLDER, quantile.getBondType(), quantile.getChartType(), quantile.getQuantileType(), quantile.getCurveCode(),
                quantile.getIssueDate().getTime());
    }

    /**
     * 批量更新
     *
     * @param updateList 利差追踪-历史分位数据集
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgBondYieldSpreadTraceQuantileDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldSpreadTraceQuantileMapper> updateBatchAction =
                MapperBatchAction.create(PgBondYieldSpreadTraceQuantileMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldSpreadTraceQuantileDO quantile : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgBondYieldSpreadTraceQuantileDO> updateQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceQuantileDO.class)
                        .and(PgBondYieldSpreadTraceQuantileDO::getId, isEqual(quantile.getId()));
                mapper.updateSelectiveByDynamicQuery(quantile, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 利差追踪-历史分位数据集
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgBondYieldSpreadTraceQuantileDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldSpreadTraceQuantileMapper> insertBatchAction =
                MapperBatchAction.create(PgBondYieldSpreadTraceQuantileMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldSpreadTraceQuantileDO quantile : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(quantile));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgBondYieldSpreadTraceQuantileDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceQuantileDO.class)
                .orderBy(PgBondYieldSpreadTraceQuantileDO::getIssueDate, SortDirections::desc);
        return bondYieldSpreadTraceQuantileMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgBondYieldSpreadTraceQuantileDO::getIssueDate);
    }
}
