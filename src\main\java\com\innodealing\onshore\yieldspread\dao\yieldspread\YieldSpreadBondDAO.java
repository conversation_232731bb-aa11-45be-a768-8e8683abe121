package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.NormPagingQuery;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.YieldSpreadBondMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 利差债券主体类型
 *
 * <AUTHOR>
 */
@Repository
public class YieldSpreadBondDAO {

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private YieldSpreadBondMapper yieldSpreadBondMapper;

    /**
     * 从给定的bondUniCode中找出利差债券
     *
     * @param bondUniCodes 债券bondUniCode
     * @return 利差债券bondUniCode
     */
    public Set<Long> findOutYieldSpreadBondUniCodes(Set<Long> bondUniCodes) {
        DynamicQuery<YieldSpreadBondDO> query = DynamicQuery.createQuery(YieldSpreadBondDO.class)
                .select(YieldSpreadBondDO::getBondUniCode)
                .and(YieldSpreadBondDO::getBondUniCode, in(bondUniCodes));
        List<YieldSpreadBondDO> yieldSpreadBonds = yieldSpreadBondMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(yieldSpreadBonds)) {
            return Collections.emptySet();
        }
        return yieldSpreadBonds.stream().map(YieldSpreadBondDO::getBondUniCode).collect(Collectors.toSet());
    }

    /**
     * 从给定的bondCodes中找出利差债券
     *
     * @param bondCodes 债券bondCodes
     * @return 利差债券
     */
    public Set<Long> findOutBondUniCodesBuBondCodes(Set<String> bondCodes) {
        DynamicQuery<YieldSpreadBondDO> query = DynamicQuery.createQuery(YieldSpreadBondDO.class)
                .select(YieldSpreadBondDO::getBondUniCode)
                .and(YieldSpreadBondDO::getBondCode, in(bondCodes));
        List<YieldSpreadBondDO> yieldSpreadBonds = yieldSpreadBondMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(yieldSpreadBonds)) {
            return Collections.emptySet();
        }
        return yieldSpreadBonds.stream().map(YieldSpreadBondDO::getBondUniCode).collect(Collectors.toSet());
    }

    /**
     * 从给定的bondUniCode中找出利差债券
     *
     * @param bondUniCodes         债券bondUniCode
     * @param comYieldSpreadSector 利差口径
     * @return 利差债券bondUniCode
     */
    public Set<Long> findOutYieldSpreadBondUniCodes(Set<Long> bondUniCodes, int comYieldSpreadSector) {
        DynamicQuery<YieldSpreadBondDO> query = DynamicQuery.createQuery(YieldSpreadBondDO.class)
                .select(YieldSpreadBondDO::getBondUniCode)
                .and(YieldSpreadBondDO::getComSpreadSector, isEqual(comYieldSpreadSector))
                .and(YieldSpreadBondDO::getBondUniCode, in(bondUniCodes));
        List<YieldSpreadBondDO> yieldSpreadBonds = yieldSpreadBondMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(yieldSpreadBonds)) {
            return new HashSet<>();
        }
        return yieldSpreadBonds.stream().map(YieldSpreadBondDO::getBondUniCode).collect(Collectors.toSet());
    }

    /**
     * 保存债券，已经存在的不会做任何操作
     *
     * @param yieldSpreadBonds 要保存的债券
     * @return 数量
     */
    public int saveYieldSpreadBonds(List<YieldSpreadBondDO> yieldSpreadBonds) {
        if (CollectionUtils.isEmpty(yieldSpreadBonds)) {
            return 0;
        }
        Map<Integer, List<YieldSpreadBondDO>> bondMap = yieldSpreadBonds.stream().collect(Collectors.groupingBy(YieldSpreadBondDO::getComSpreadSector));
        List<YieldSpreadBondDO> insertList = new ArrayList<>();
        for (Map.Entry<Integer, List<YieldSpreadBondDO>> entry : bondMap.entrySet()) {
            List<YieldSpreadBondDO> list = bondMap.get(entry.getKey());
            Set<Long> bondUniCodes = list.stream().map(YieldSpreadBondDO::getBondUniCode).collect(Collectors.toSet());
            Set<Long> oldBondUniCodes = this.findOutYieldSpreadBondUniCodes(bondUniCodes, entry.getKey());

            for (YieldSpreadBondDO yieldSpreadBond : yieldSpreadBonds) {
                if (oldBondUniCodes.contains(yieldSpreadBond.getBondUniCode())) {
                    continue;
                }
                yieldSpreadBond.setDeleted(Deleted.NO_DELETED.getValue());
                yieldSpreadBond.setId(null);
                insertList.add(yieldSpreadBond);
                oldBondUniCodes.add(yieldSpreadBond.getBondUniCode());
            }
        }
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        return this.batchInsert(insertList);
    }

    /**
     * 批量插入
     *
     * @param insertList bondList
     * @return 数量
     */
    public int batchInsert(List<YieldSpreadBondDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<YieldSpreadBondMapper> insertBatchAction =
                MapperBatchAction.create(YieldSpreadBondMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (YieldSpreadBondDO yieldSpreadBond : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(yieldSpreadBond));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 分页查询利差债
     *
     * @param pageNum  页码
     * @param pageSize 每页数据量
     * @return 利差债
     */
    public NormPagingResult<YieldSpreadBondDO> pagingYieldSpreadBonds(int pageNum, int pageSize) {
        NormPagingQuery<YieldSpreadBondDO> query = NormPagingQuery
                .createQuery(YieldSpreadBondDO.class, pageNum, pageSize);
        return yieldSpreadBondMapper.selectByNormalPaging(query);

    }

    /**
     * 根据主体查询 do
     *
     * @param comUniCodes findComSpreadSector
     * @return list
     */
    public List<YieldSpreadBondDO> findComSpreadSector(List<Long> comUniCodes) {
        DynamicQuery<YieldSpreadBondDO> query = DynamicQuery.createQuery(YieldSpreadBondDO.class)
                .select(YieldSpreadBondDO::getComUniCode, YieldSpreadBondDO::getComSpreadSector)
                .and(YieldSpreadBondDO::getComUniCode, in(comUniCodes));
        query.setDistinct(true);
        return yieldSpreadBondMapper.selectByDynamicQuery(query);
    }
}
