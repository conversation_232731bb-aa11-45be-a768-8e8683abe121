package com.innodealing.onshore.yieldspread.model.dto;

import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldPanoramaBO;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 收益全景获取数据
 *
 * <AUTHOR>
 * @date 2024/5/7 10:35
 **/
public class PgBondYieldPanoramaDTO {
    /**
     * 利差全景数据
     */
    private List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    public List<PgBondYieldPanoramaBO> getPgBondYieldPanoramaBOList() {
        return Objects.isNull(pgBondYieldPanoramaBOList) ? new ArrayList<>() : new ArrayList<>(pgBondYieldPanoramaBOList);
    }

    public void setPgBondYieldPanoramaBOList(List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList) {
        this.pgBondYieldPanoramaBOList = Objects.isNull(pgBondYieldPanoramaBOList) ? new ArrayList<>() : new ArrayList<>(pgBondYieldPanoramaBOList);
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public Date getEndDate() {
        return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public void setEndDate(Date endDate) {
        this.endDate = Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }
}
