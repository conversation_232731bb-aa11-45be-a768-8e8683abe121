package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationShortInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationV2RequestDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.BenchmarkCurveResponseDTO;
import com.innodealing.onshore.bondmetadata.tuple.Tuple2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * onshore-bond-price
 *
 * <AUTHOR>
 * @date 2022/03/28
 **/
@FeignClient(name = "BondPriceService", url = "${bond.price.api.url}", path = "internal")
public interface BondPriceService {

    /**
     * 批量获取中债估值
     *
     * @param bondDatePairs DM债券代码-发行日期
     * @param currency      1:国内 3:国际
     * @return 中债估值集合
     */
    @PostMapping("chinaBond/valuation/info/short/get")
    List<CbValuationShortInfoResponseDTO> listCbValuationShortInfos(@RequestBody Tuple2<Long, Date>[] bondDatePairs,
                                                                    @RequestParam(defaultValue = "1") Integer currency);

    /**
     * 批量获取中债估值
     *
     * @param request 中债估值查询参数
     * @return 中债估值集合
     */
    @PostMapping("chinaBond/valuation/list-v2")
    List<CbValuationShortInfoResponseDTO> listCbValuations(@RequestBody CbValuationV2RequestDTO request);

    /**
     * 获取收益率曲线
     *
     * @param requestDTO 收益率曲线 请求参数 RequestDTO
     * @return 收益率曲线
     */
    @PostMapping("bondYield/curve/list")
    List<BondYieldCurveDTO> listBondYieldCurves(@RequestBody BondYieldCurveRequestDTO requestDTO);

    /**
     * 查询基准曲线
     *
     * @param curveCode   曲线代码
     * @param ytmProperty ytm字段
     * @param startDate   起始时间
     * @param endDate     结束时间
     * @return 基准曲线数据
     */
    @GetMapping("bondYield/curve/list/benchmark-curve")
    List<BenchmarkCurveResponseDTO> listBenchmarkYields(@RequestParam Integer curveCode,
                                                        @RequestParam String ytmProperty,
                                                        @RequestParam java.sql.Date startDate,
                                                        @RequestParam java.sql.Date endDate);

    /**
     * 批量获取中债估值
     *
     * @param issueDate    发行日期
     * @param bondUniCodes 债券唯一代码
     * @return key 债券唯一代码,value 中债估值精简信息
     */
    default Map<Long, CbValuationShortInfoResponseDTO> getCbValuationShortInfoMap(java.sql.Date issueDate, Set<Long> bondUniCodes) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyMap();
        }
        List<Tuple2<Long, Date>> tuple2List = new ArrayList<>();
        for (Long bondUniCode : bondUniCodes) {
            Tuple2<Long, Date> tuple2 = new Tuple2<>();
            tuple2.setT1(bondUniCode);
            tuple2.setT2(issueDate);
            tuple2List.add(tuple2);
        }
        List<CbValuationShortInfoResponseDTO> cbValuationShortInfoResponseDTOs = listCbValuationShortInfos
                (tuple2List.stream().toArray(Tuple2[]::new), 1);
        if (CollectionUtils.isEmpty(cbValuationShortInfoResponseDTOs)) {
            return Collections.emptyMap();
        }
        return cbValuationShortInfoResponseDTOs
                .stream()
                .collect(Collectors.toMap(CbValuationShortInfoResponseDTO::getBondUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 批量获取中债估值
     *
     * @param issueDate    发行日期
     * @param bondUniCodes 债券唯一代码
     * @return key 债券唯一代码,value 中债估值精简信息
     */
    default Map<Long, CbValuationShortInfoResponseDTO> getCbValuationMap(java.sql.Date issueDate, List<Long> bondUniCodes) {
        if (Objects.isNull(issueDate) || CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyMap();
        }
        CbValuationV2RequestDTO request = new CbValuationV2RequestDTO();
        request.setBondUniCodes(bondUniCodes);
        request.setIssueDate(issueDate);

        List<CbValuationShortInfoResponseDTO> cbValuations = listCbValuations(request);
        if (CollectionUtils.isEmpty(cbValuations)) {
            return Collections.emptyMap();
        }
        return cbValuations.stream().collect(Collectors.toMap(CbValuationShortInfoResponseDTO::getBondUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 获取收益率曲线
     *
     * @param issueDate  曲线日期
     * @param curveCodes 曲线代码
     * @return key 曲线代码,value 收益率曲线
     */
    default Map<Integer, BondYieldCurveDTO> getBondYieldCurveMap(java.sql.Date issueDate, List<Integer> curveCodes) {
        if (CollectionUtils.isEmpty(curveCodes)) {
            return Collections.emptyMap();
        }
        BondYieldCurveRequestDTO requestDTO = new BondYieldCurveRequestDTO();
        requestDTO.setCurveCodes(curveCodes);
        requestDTO.setIssueDate(issueDate);
        return listBondYieldCurves(requestDTO).stream()
                .collect(Collectors.toMap(BondYieldCurveDTO::getCurveCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 查询某天有中债估值的bondUniCode，按照ASC排序，每次获取大于bondUniCode的500个数据
     *
     * @param issueDate        利差日期
     * @param startBondUniCode 起始BondUniCode
     * @return bondUniCodes
     */
    @GetMapping("price-valuation/list/cb/bond-uni-code")
    List<Long> listHasValuationBondUniCodes(@RequestParam Date issueDate, @RequestParam Long startBondUniCode);

}
