package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CustomComYieldSpreadResDTO;

import java.util.List;

/**
 * 自定义主体利差 Service
 *
 * <AUTHOR>
 **/
public interface CustomComYieldSpreadService {

    /**
     * 主体利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体利差
     */
    List<CustomComYieldSpreadResDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 主体利差数量
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体利差数量
     */
    Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request);

}
