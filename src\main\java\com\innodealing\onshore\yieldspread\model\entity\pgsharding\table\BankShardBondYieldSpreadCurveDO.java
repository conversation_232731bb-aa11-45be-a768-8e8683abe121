package com.innodealing.onshore.yieldspread.model.entity.pgsharding.table;


import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 银行债券利差曲线 分片DO
 *
 * <AUTHOR>
 */
@Table(name = "bank_curve")
public class BankShardBondYieldSpreadCurveDO {
    @Id
    @Column
    private Long id;
    /**
     * 银行公司债求偿顺序   1:普通（剔除永续）、2:二级资本债、 3:永续"
     */
    @Column
    private Integer bankSeniorityRanking;

    /**
     * 银行类型
     */
    @Column
    private Integer bankType;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 债券信用利差;单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpread;
    /**
     * 债券超额利差;单位(BP)
     */
    @Column
    private BigDecimal bondExcessSpread;
    /**
     * 中债收益率;单位(%)
     */
    @Column
    private BigDecimal cbYield;

    @Column
    private BigDecimal avgBondCreditSpread;
    /**
     * 超额利差;单位(BP)
     */
    @Column
    private BigDecimal avgBondExcessSpread;
    /**
     * 中债收益率;单位(BP)
     */
    @Column
    private BigDecimal avgCbYield;

    @Column
    private Integer bondCreditSpreadCount;
    /**
     * 超额利差;单位(BP)
     */
    @Column
    private Integer bondExcessSpreadCount;
    /**
     * 中债收益率;单位(BP)
     */
    @Column
    private Integer cbYieldCount;

    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;

    /**
     * 是否根据银行类型分组
     */
    @Column
    private Integer usingBankType;

    /**
     * 是否根据利差债券类别进行分组: 0：是，1：否
     */
    @Column
    private Integer usingBankSeniorityRanking;

    /**
     * 是否根据利差剩余期限标签进行分组: 0：是，1：否
     */
    @Column
    private Integer usingSpreadRemainingTenorTag;

    public Integer getUsingBankType() {
        return usingBankType;
    }

    public void setUsingBankType(Integer usingBankType) {
        this.usingBankType = usingBankType;
    }

    public Integer getUsingBankSeniorityRanking() {
        return usingBankSeniorityRanking;
    }

    public void setUsingBankSeniorityRanking(Integer usingBankSeniorityRanking) {
        this.usingBankSeniorityRanking = usingBankSeniorityRanking;
    }

    public Integer getUsingSpreadRemainingTenorTag() {
        return usingSpreadRemainingTenorTag;
    }

    public void setUsingSpreadRemainingTenorTag(Integer usingSpreadRemainingTenorTag) {
        this.usingSpreadRemainingTenorTag = usingSpreadRemainingTenorTag;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Integer getBankSeniorityRanking() {
        return bankSeniorityRanking;
    }

    public void setBankSeniorityRanking(Integer bankSeniorityRanking) {
        this.bankSeniorityRanking = bankSeniorityRanking;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getBankType() {
        return bankType;
    }

    public void setBankType(Integer bankType) {
        this.bankType = bankType;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }
    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }
    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }
    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }
    public Integer getCbYieldCount() {
        return cbYieldCount;
    }
    public void setCbYieldCount(Integer cbYieldCount) {
        this.cbYieldCount = cbYieldCount;
    }
}
