package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgInduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadCreditYieldGroupDO;

/**
 * pg indu债券利差计算信用利差中位数Mapper
 *
 * <AUTHOR>
 */
public interface PgInduBondYieldSpreadCreditYieldGroupMapper extends SelectByGroupedQueryMapper<PgInduBondYieldSpreadDO,
        PgInduBondYieldSpreadCreditYieldGroupDO> {
}
