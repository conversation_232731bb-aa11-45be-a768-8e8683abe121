package com.innodealing.onshore.yieldspread.mapper.pgsharding.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.mv.MvInsuShardBondYieldSpreadCurveDO;

import javax.persistence.Table;

/**
 * 保险利差曲线分片查询
 *
 * <AUTHOR>
 */

public interface MvInsuShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<MvInsuShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     *
     * @return tableName
     */
    default String getLogicTable() {
        return MvInsuShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }


}
