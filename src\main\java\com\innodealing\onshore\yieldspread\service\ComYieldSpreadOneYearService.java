package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import org.springframework.lang.Nullable;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 主体利差(一年)服务
 *
 * <AUTHOR>
 */
public interface ComYieldSpreadOneYearService {

    /**
     * 刷新历史主体利差
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return int 刷新数据量
     */
    int syncHistoryComYieldSpread(@Nullable Date startDate, @Nullable Date endDate);

    /**
     * 刷新主体利差
     *
     * @param syncDate 同步日期
     * @return int 刷新数据量
     */
    int syncComYieldSpread(Date syncDate);

    /**
     * 查询主体利差信用利差(全部)数据
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param comUniCodeList 发行人唯一编码列表
     * @return {@link List}<{@link ComCreditSpreadDTO}> 主体信用利差响应集合
     */
    List<ComCreditSpreadDTO> listComCreditSpreads(Date startDate, Date endDate, Set<Long> comUniCodeList);

    /**
     * 清除主体利差数据
     *
     * @return int 清除行数
     */
    int clearComYieldSpread();
}
