package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 主体利差简短BO
 *
 * <AUTHOR>
 **/
public class ShortComYieldSpreadBO {

    /**
     * 发行人代码
     */
    private Long comUniCode;
    /**
     * 利差日期
     */
    private Date spreadDate;

    private BigDecimal comSpread;

    public BigDecimal getComSpread() {
        return comSpread;
    }

    public void setComSpread(BigDecimal comSpread) {
        this.comSpread = comSpread;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }
}