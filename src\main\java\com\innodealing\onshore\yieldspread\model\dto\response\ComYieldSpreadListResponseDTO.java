package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 主体利差列表响应DTO
 *
 * <AUTHOR>
 */
public class ComYieldSpreadListResponseDTO {

    @ApiModelProperty("产业主体")
    private List<InduComYieldSpreadResponseDTO> indus;
    @ApiModelProperty("城投主体")
    private List<UdicComYieldSpreadResponseDTO> udics;
    @ApiModelProperty("银行主体")
    private List<BankComYieldSpreadResDTO> banks;
    @ApiModelProperty("证券主体")
    private List<SecuComYieldSpreadResDTO> secus;
    @ApiModelProperty("保险主体")
    private List<InsuComYieldSpreadResDTO> insus;
    @ApiModelProperty("自选主体")
    private List<CustomComYieldSpreadResDTO> customComs;

    public List<InduComYieldSpreadResponseDTO> getIndus() {
        return Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public void setIndus(List<InduComYieldSpreadResponseDTO> indus) {
        this.indus = Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public List<UdicComYieldSpreadResponseDTO> getUdics() {
        return Objects.isNull(udics) ? new ArrayList<>() : new ArrayList<>(udics);
    }

    public void setUdics(List<UdicComYieldSpreadResponseDTO> udics) {
        this.udics = Objects.isNull(udics) ? new ArrayList<>() : new ArrayList<>(udics);
    }

    public List<BankComYieldSpreadResDTO> getBanks() {
        return Objects.isNull(banks) ? new ArrayList<>() : new ArrayList<>(banks);
    }

    public void setBanks(List<BankComYieldSpreadResDTO> banks) {
        this.banks = Objects.isNull(banks) ? new ArrayList<>() : new ArrayList<>(banks);
    }

    public List<SecuComYieldSpreadResDTO> getSecus() {
        return Objects.isNull(secus) ? new ArrayList<>() : new ArrayList<>(secus);
    }

    public void setSecus(List<SecuComYieldSpreadResDTO> secus) {
        this.secus = Objects.isNull(secus) ? new ArrayList<>() : new ArrayList<>(secus);
    }

    public List<InsuComYieldSpreadResDTO> getInsus() {
        return Objects.isNull(insus) ? new ArrayList<>() : new ArrayList<>(insus);
    }

    public void setInsus(List<InsuComYieldSpreadResDTO> insus) {
        this.insus = Objects.isNull(insus) ? new ArrayList<>() : new ArrayList<>(insus);
    }

    public List<CustomComYieldSpreadResDTO> getCustomComs() {
        return Objects.isNull(customComs) ? new ArrayList<>() : new ArrayList<>(customComs);
    }

    public void setCustomComs(List<CustomComYieldSpreadResDTO> customComs) {
        this.customComs = Objects.isNull(customComs) ? new ArrayList<>() : new ArrayList<>(customComs);
    }
}
