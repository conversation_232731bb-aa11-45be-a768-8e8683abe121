package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayNumber2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayNumber4ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 保险主体利差
 *
 * <AUTHOR>
 */
public class InsuComYieldSpreadResDTO extends BaseFinanceComYieldSpreadResDTO {

    @ApiModelProperty("企业性质")
    private Integer businessNature;

    @ApiModelProperty("企业性质")
    private String businessNatureStr;

    @ApiModelProperty("主体信用利差(资本补充);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = DisplayNumber2ScaleJsonSerializer.class)
    private BigDecimal comTier2CreditSpread;

    @ApiModelProperty("主体超额利差(资本补充);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = DisplayNumber2ScaleJsonSerializer.class)
    private BigDecimal comTier2ExcessSpread;

    @ApiModelProperty("主体估值收益率(资本补充);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = DisplayNumber4ScaleJsonSerializer.class)
    private BigDecimal comTier2CbYield;

    public Integer getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(Integer businessNature) {
        this.businessNature = businessNature;
    }

    public String getBusinessNatureStr() {
        return businessNatureStr;
    }

    public void setBusinessNatureStr(String businessNatureStr) {
        this.businessNatureStr = businessNatureStr;
    }

    public BigDecimal getComTier2CreditSpread() {
        return comTier2CreditSpread;
    }

    public void setComTier2CreditSpread(BigDecimal comTier2CreditSpread) {
        this.comTier2CreditSpread = comTier2CreditSpread;
    }

    public BigDecimal getComTier2ExcessSpread() {
        return comTier2ExcessSpread;
    }

    public void setComTier2ExcessSpread(BigDecimal comTier2ExcessSpread) {
        this.comTier2ExcessSpread = comTier2ExcessSpread;
    }

    public BigDecimal getComTier2CbYield() {
        return comTier2CbYield;
    }

    public void setComTier2CbYield(BigDecimal comTier2CbYield) {
        this.comTier2CbYield = comTier2CbYield;
    }
}
