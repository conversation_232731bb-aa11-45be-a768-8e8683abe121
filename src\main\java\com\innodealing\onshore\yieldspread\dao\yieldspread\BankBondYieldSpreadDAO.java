package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.QueryHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.BankBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.BankBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.dto.BankYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.BankBondYieldSpreadGroupDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 银行债利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class BankBondYieldSpreadDAO {

    @Resource
    private BankBondYieldSpreadMapper bankBondYieldSpreadMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private BankBondYieldSpreadGroupMapper bankBondYieldSpreadGroupMapper;

    private final QueryHelper queryHelper = new QueryHelper();

    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    public void createShardingTable(String tableName) {
        bankBondYieldSpreadMapper.createShardingTable(tableName);
    }

    /**
     * 批量更新
     *
     * @param startDate                 开始日期
     * @param endDate                   结束日期
     * @param bankBondYieldSpreadDOList 证券债利差列表
     * @return 受影响的行数
     */
    public int saveBankBondYieldSpreadDOList(Date startDate, Date endDate, List<BankBondYieldSpreadDO> bankBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(bankBondYieldSpreadDOList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        long startPk = ShardingUtils.getMinPkOfDate(startDate);
        long endPk = ShardingUtils.getMaxPkOfDate(endDate);
        Set<Long> bondUniCodes = bankBondYieldSpreadDOList.stream().map(BankBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<BankBondYieldSpreadDO> query = DynamicQuery.createQuery(BankBondYieldSpreadDO.class)
                .select(BankBondYieldSpreadDO::getId, BankBondYieldSpreadDO::getBondUniCode,
                        BankBondYieldSpreadDO::getSpreadDate, BankBondYieldSpreadDO::getInduLevel1Code,
                        BankBondYieldSpreadDO::getInduLevel1Name, BankBondYieldSpreadDO::getInduLevel2Code,
                        BankBondYieldSpreadDO::getInduLevel2Name, BankBondYieldSpreadDO::getBankType)
                .and(BankBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(BankBondYieldSpreadDO::getSpreadDate, isEqual(startDate))
                .and(BankBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<BankBondYieldSpreadDO> existDataList = bankBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(bankBondYieldSpreadDOList));
        } else {
            Map<String, BankBondYieldSpreadDO> existBankBondYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<BankBondYieldSpreadDO> insertList = new ArrayList<>();
            List<BankBondYieldSpreadDO> updateList = new ArrayList<>();
            for (BankBondYieldSpreadDO bankBondYieldSpreadDO : bankBondYieldSpreadDOList) {
                BankBondYieldSpreadDO existBankBondYieldSpreadDO = existBankBondYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, bankBondYieldSpreadDO.getBondUniCode(),
                                bankBondYieldSpreadDO.getSpreadDate().getTime()));
                if (isNull(existBankBondYieldSpreadDO)) {
                    insertList.add(bankBondYieldSpreadDO);
                } else {
                    bankBondYieldSpreadDO.setId(existBankBondYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existBankBondYieldSpreadDO.getInduLevel1Code(),
                            bankBondYieldSpreadDO::setInduLevel1Code);
                    ObjectExtensionUtils.ifNonNull(existBankBondYieldSpreadDO.getInduLevel1Name(),
                            bankBondYieldSpreadDO::setInduLevel1Name);
                    ObjectExtensionUtils.ifNonNull(existBankBondYieldSpreadDO.getInduLevel2Code(),
                            bankBondYieldSpreadDO::setInduLevel2Code);
                    ObjectExtensionUtils.ifNonNull(existBankBondYieldSpreadDO.getInduLevel2Name(),
                            bankBondYieldSpreadDO::setInduLevel2Name);
                    ObjectExtensionUtils.ifNonNull(existBankBondYieldSpreadDO.getBankType(),
                            bankBondYieldSpreadDO::setBankType);
                    updateList.add(bankBondYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 证券债利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<BankBondYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<BankBondYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(BankBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (BankBondYieldSpreadDO bankBondYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<BankBondYieldSpreadDO> updateQuery = DynamicQuery.createQuery(BankBondYieldSpreadDO.class)
                        .and(BankBondYieldSpreadDO::getId, isEqual(bankBondYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(bankBondYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 证券债利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<BankBondYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<BankBondYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(BankBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (BankBondYieldSpreadDO bankBondYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(bankBondYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 根据主体分组获取证券债利差
     *
     * @param spreadDate 开始日期
     * @return 证券债利差
     */
    public List<BankBondYieldSpreadGroupDO> listBankBondYieldSpreadGroupDOs(Date spreadDate) {
        long startPk = ShardingUtils.getMinPkOfDate(spreadDate);
        long endPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        GroupedQuery<BankBondYieldSpreadDO, BankBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(BankBondYieldSpreadDO.class, BankBondYieldSpreadGroupDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER)
                        .select(BankBondYieldSpreadGroupDO::getComUniCode,
                                BankBondYieldSpreadGroupDO::getInduLevel1Code, BankBondYieldSpreadGroupDO::getInduLevel1Name,
                                BankBondYieldSpreadGroupDO::getInduLevel2Code,
                                BankBondYieldSpreadGroupDO::getInduLevel2Name, BankBondYieldSpreadGroupDO::getBankType,
                                BankBondYieldSpreadGroupDO::getComExtRatingMapping, BankBondYieldSpreadGroupDO::getSpreadDate)
                        .and(BankBondYieldSpreadDO::getId, between(startPk, endPk))
                        .and(BankBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(g -> g.and(BankBondYieldSpreadDO::getBondCreditSpread, notEqual(null))
                                .or(BankBondYieldSpreadDO::getBondExcessSpread, notEqual(null)))
                        .groupBy(BankBondYieldSpreadDO::getComUniCode);
        return bankBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 查询银行单券利差
     *
     * @param param 查询参数
     * @return 单券利差
     */
    public NormPagingResult<BankBondYieldSpreadDO> listBankSingleBondYieldSpreads(BankYieldSearchParam param) {
        NormPagingQuery<BankBondYieldSpreadDO> query = NormPagingQuery
                .createQuery(BankBondYieldSpreadDO.class, param.getPageNum(), param.getPageSize())
                .and(this.listCommonFilters(param));
        if (Objects.nonNull(param.getSort())) {
            SortDTO sort = param.getSort();
            String columnName = queryHelper.getQueryColumnByProperty(BankBondYieldSpreadDO.class, sort.getPropertyName());
            query.addSorts(new CustomSortDescriptor(columnName + " IS NULL"));
            query.addSorts(new SortDescriptor(sort.getPropertyName(), sort.getSortDirection()));
        }
        return bankBondYieldSpreadMapper.selectByNormalPaging(query);
    }

    private BaseFilterDescriptor<BankBondYieldSpreadDO>[] listCommonFilters(BankYieldSearchParam param) {
        long startPk = ShardingUtils.getMinPkOfDate(param.getSpreadDate());
        long endPk = ShardingUtils.getMaxPkOfDate(param.getSpreadDate());
        FilterGroupDescriptor<BankBondYieldSpreadDO> filterGroup = FilterGroupDescriptor.create(BankBondYieldSpreadDO.class)
                .and(BankBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(BankBondYieldSpreadDO::getSpreadDate, isEqual(param.getSpreadDate()))
                .and(nonNull(param.getSpreadBondType()), BankBondYieldSpreadDO::getBankSeniorityRanking, isEqual(param.getSpreadBondType()))
                .and(CollectionUtils.isNotEmpty(param.getBankTypes()), BankBondYieldSpreadDO::getBankType, in(param.getBankTypes()))
                .and(nonNull(param.getRemainingTenor()), BankBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(param.getRemainingTenor()))
                .and(nonNull(param.getComUniCode()), BankBondYieldSpreadDO::getComUniCode, isEqual(param.getComUniCode()))
                .and(nonNull(param.getBondUniCode()), BankBondYieldSpreadDO::getBondUniCode, isEqual(param.getBondUniCode()))
                .and(isNotEmpty(param.getBondImpliedRatingMappings()), BankBondYieldSpreadDO::getBondImpliedRatingMapping,
                        in(param.getBondImpliedRatingMappings()))
                .and(BankBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return filterGroup.getFilters();
    }

    /**
     * 获取债券
     *
     * @return 债券
     */
    public List<BankBondYieldSpreadGroupDO> listBonds() {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(YieldSpreadConst.CURVE_START_DATE);
        Long manPkOfDate = ShardingUtils.getMaxPkOfDate(Date.valueOf(LocalDate.now()));
        GroupedQuery<BankBondYieldSpreadDO, BankBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(BankBondYieldSpreadDO.class, BankBondYieldSpreadGroupDO.class)
                        .select(BankBondYieldSpreadGroupDO::getComUniCode,
                                BankBondYieldSpreadGroupDO::getBondUniCode,
                                BankBondYieldSpreadGroupDO::getBondCode)
                        .and(BankBondYieldSpreadDO::getId, between(minPkOfDate, manPkOfDate))
                        .and(BankBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                        .groupBy(BankBondYieldSpreadDO::getBondUniCode);
        return bankBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }


    /**
     * 查询银行单券利差数据集
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码
     * @return {@link List}<{@link BankBondYieldSpreadDO}>
     */
    public List<BankBondYieldSpreadDO> listBankBondYieldSpreads(@NonNull Date spreadDate, @NonNull Collection<Long> bondUniCodes) {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPkOfDate = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<BankBondYieldSpreadDO> query = DynamicQuery.createQuery(BankBondYieldSpreadDO.class)
                .and(BankBondYieldSpreadDO::getId, between(minPkOfDate, maxPkOfDate))
                .and(BankBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        return bankBondYieldSpreadMapper.selectByDynamicQuery(query);
    }

}
