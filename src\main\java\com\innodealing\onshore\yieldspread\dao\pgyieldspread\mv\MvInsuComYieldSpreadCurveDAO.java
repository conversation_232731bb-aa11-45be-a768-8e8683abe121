package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvInsuComYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.MvInsuBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvInsuComYieldSpreadCurveDO;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.persistence.Table;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;

/**
 * 利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvInsuComYieldSpreadCurveDAO extends AbstractMvComYieldSpreadCurveDAO<MvInsuBondYieldSpreadCurveParameter> {
    private static final String TABLE_NAME = MvInsuComYieldSpreadCurveDO.class.getAnnotation(Table.class).name();

    private MvInsuComYieldSpreadCurveMapper mvInsuComYieldSpreadCurveMapper;

    /**
     * 构造函数
     *
     * @param mvInsuComYieldSpreadCurveMapper mapper
     */
    protected MvInsuComYieldSpreadCurveDAO(MvInsuComYieldSpreadCurveMapper mvInsuComYieldSpreadCurveMapper) {
        super(mvInsuComYieldSpreadCurveMapper);
        this.mvInsuComYieldSpreadCurveMapper = mvInsuComYieldSpreadCurveMapper;
    }


    @Override
    public List<ComYieldSpreadCurveBO> list(@NonNull Long comUniCode, Integer insuranceSeniorityRanking) {
        DynamicQuery<MvInsuComYieldSpreadCurveDO> query = DynamicQuery.createQuery(MvInsuComYieldSpreadCurveDO.class)
                .and(MvInsuComYieldSpreadCurveDO::getComUniCode, x -> x.isEqual(comUniCode))
                .and(Objects.nonNull(insuranceSeniorityRanking), MvInsuComYieldSpreadCurveDO::getInsuranceSeniorityRanking, x -> x.isEqual(insuranceSeniorityRanking))
                .and(Objects.isNull(insuranceSeniorityRanking), MvInsuComYieldSpreadCurveDO::getUsingInsuranceSeniorityRanking, x -> x.isEqual(UNUSED_FIELD_GROUP.getValue()))
                .orderBy(MvInsuComYieldSpreadCurveDO::getSpreadDate, SortDirections::asc);
        List<MvInsuComYieldSpreadCurveDO> mvInsuComYieldSpreadCurveList = mvInsuComYieldSpreadCurveMapper.selectByDynamicQuery(query);
        return mvInsuComYieldSpreadCurveList.stream().map(super::handlePrecision).collect(Collectors.toList());
    }

    @Override
    protected String tableName() {
        return TABLE_NAME;
    }
}
