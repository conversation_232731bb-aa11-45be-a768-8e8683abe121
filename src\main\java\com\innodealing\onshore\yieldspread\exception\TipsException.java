package com.innodealing.onshore.yieldspread.exception;

/**
 * 针对web端进行提示的异常类
 *
 * <AUTHOR> zhaoyonglu
 */
public class TipsException extends RuntimeException {

    private static final String ERROR_MSG = "非法参数";

    private static final int ERROR_CODE = -1;

    private final int errorCode;

    /**
     * 构造方法
     */
    public TipsException() {
        super(ERROR_MSG);
        this.errorCode = ERROR_CODE;
    }

    /**
     * 构造方法
     *
     * @param message 异常消息
     */
    public TipsException(String message) {
        super(message);
        this.errorCode = ERROR_CODE;
    }

    /**
     * 构造方法
     *
     * @param errorCode errorCode
     * @param message   异常消息
     */
    public TipsException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造方法
     *
     * @param message 异常消息
     * @param ex      异常对象
     */
    public TipsException(String message, Throwable ex) {
        super(message, ex);
        this.errorCode = ERROR_CODE;
    }

    public int getErrorCode() {
        return errorCode;
    }

}
