package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.config.SyncConfiguration;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;

import java.sql.Date;
import java.util.List;

/**
 * 优化的债券收益率利差追踪服务接口
 * 提供更灵活的参数配置和并行处理能力
 * 
 * <AUTHOR>
 */
public interface OptimizedBondYieldSpreadTraceService {
    
    /**
     * 使用配置对象同步利差追踪数据
     * 
     * @param config 同步配置
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceWithConfig(SyncConfiguration config);
    


}
