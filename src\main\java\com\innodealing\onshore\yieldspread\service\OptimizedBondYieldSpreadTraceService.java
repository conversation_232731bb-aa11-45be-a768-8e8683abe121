package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.config.SyncConfiguration;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;

import java.sql.Date;
import java.util.List;

/**
 * 优化的债券收益率利差追踪服务接口
 * 提供更灵活的参数配置和并行处理能力
 * 
 * <AUTHOR>
 */
public interface OptimizedBondYieldSpreadTraceService {
    
    /**
     * 使用配置对象同步利差追踪数据
     * 
     * @param config 同步配置
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceWithConfig(SyncConfiguration config);
    
    /**
     * 按债券类型同步利差追踪数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param bondTypes 债券类型列表，为空表示所有类型
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceByBondTypes(Date startDate, Date endDate, List<YieldPanoramaBondTypeEnum> bondTypes);
    
    /**
     * 按曲线代码同步利差追踪数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param curveCodes 曲线代码列表，为空表示所有曲线
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceByCurveCodes(Date startDate, Date endDate, List<Integer> curveCodes);
    
    /**
     * 并行同步利差追踪数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param parallelism 并行度
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceParallel(Date startDate, Date endDate, int parallelism);
    
    /**
     * 同步单个债券类型的利差追踪数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param bondType 债券类型
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceForSingleBondType(Date startDate, Date endDate, YieldPanoramaBondTypeEnum bondType);
    
    /**
     * 同步单个曲线代码的利差追踪数据
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param curveCode 曲线代码
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceForSingleCurveCode(Date startDate, Date endDate, Integer curveCode);
    
    /**
     * 仅同步绝对值数据
     * 
     * @param config 同步配置
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceAbsOnly(SyncConfiguration config);
    
    /**
     * 仅同步分位数据
     * 
     * @param config 同步配置
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceQuantileOnly(SyncConfiguration config);
    
    /**
     * 仅同步变动数据
     * 
     * @param config 同步配置
     * @return 同步影响的行数
     */
    int syncBondYieldSpreadTraceChangeOnly(SyncConfiguration config);
    
    /**
     * 获取同步进度信息
     * 
     * @param config 同步配置
     * @return 进度信息
     */
    String getSyncProgress(SyncConfiguration config);
    
    /**
     * 估算同步时间
     * 
     * @param config 同步配置
     * @return 估算的同步时间（秒）
     */
    long estimateSyncTime(SyncConfiguration config);
    
    /**
     * 验证同步配置
     * 
     * @param config 同步配置
     * @return 验证结果，空字符串表示验证通过
     */
    String validateSyncConfiguration(SyncConfiguration config);
}
