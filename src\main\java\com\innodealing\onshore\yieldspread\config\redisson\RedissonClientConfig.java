package com.innodealing.onshore.yieldspread.config.redisson;

import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * redisson 客户端配置
 *
 * <AUTHOR>
 **/
@Configuration
public class RedissonClientConfig {
    @Resource
    private RedissonClientProperties redissonClientProperties;

    /**
     * 创建 redisson 客户端
     *
     * @return redisson 客户端
     */
    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        String address = String.format("redis://%s:%d", redissonClientProperties.getHost(), redissonClientProperties.getPort());
        SingleServerConfig singleServerConfig = config.useSingleServer()
                .setAddress(address)
                .setDatabase(redissonClientProperties.getDatabase());
        if (StringUtils.isNoneBlank(redissonClientProperties.getPassword())) {
            singleServerConfig.setPassword(redissonClientProperties.getPassword());
        }
        return Redisson.create(config);
    }
}
