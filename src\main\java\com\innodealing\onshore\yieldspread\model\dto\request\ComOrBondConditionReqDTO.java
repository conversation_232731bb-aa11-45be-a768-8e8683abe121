package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * 主体或债券搜索条件
 *
 * <AUTHOR>
 */
public class ComOrBondConditionReqDTO {

    /**
     * @see com.innodealing.onshore.yieldspread.enums.SpreadCodeTypeEnum
     */
    @ApiModelProperty("条件类型 1：主体利差，2：单券利差")
    private Integer conditionType;

    @ApiModelProperty("主体或债券的uniCode")
    private Long uniCode;

    @ApiModelProperty("主体或债券的shortName")
    private String shortName;

    public Integer getConditionType() {
        return conditionType;
    }

    public void setConditionType(Integer conditionType) {
        this.conditionType = conditionType;
    }

    public Long getUniCode() {
        return uniCode;
    }

    public void setUniCode(Long uniCode) {
        this.uniCode = uniCode;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

}
