package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 利差曲线BO
 *
 * <AUTHOR>
 */
public class BondYieldSpreadCurveBO {

    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 信用利差
     */
    private BigDecimal bondCreditSpread;
    /**
     * 超额利差
     */
    private BigDecimal bondExcessSpread;
    /**
     * 估值收益率
     */
    private BigDecimal cbYield;
    /**
     * 国开基准曲线
     */
    private BigDecimal cdbLerpYield;
    /**
     * 信用利差不为空的债券样本数量
     */
    private Integer bondCreditSpreadCount;
    /**
     * 超额利差不为空的债券样本数量
     */
    private Integer bondExcessSpreadCount;
    /**
     * 中债收益率不为空的债券样本数量
     */
    private Integer cbYieldCount;
    /**
     * 国开插值收益率不为空的债券样本数量
     */
    private Integer cdbLerpYieldCount;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(BigDecimal cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getCbYieldCount() {
        return cbYieldCount;
    }

    public void setCbYieldCount(Integer cbYieldCount) {
        this.cbYieldCount = cbYieldCount;
    }

    public Integer getCdbLerpYieldCount() {
        return cdbLerpYieldCount;
    }

    public void setCdbLerpYieldCount(Integer cdbLerpYieldCount) {
        this.cdbLerpYieldCount = cdbLerpYieldCount;
    }
}
