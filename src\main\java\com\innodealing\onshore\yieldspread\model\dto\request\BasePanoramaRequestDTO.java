package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Date;
import java.util.Objects;

import static java.util.Objects.isNull;

/**
 * 全景利差基础请求DTO
 *
 * <AUTHOR>
 */
public class BasePanoramaRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("债券外部评级")
    protected Integer bondExtRatingMapping;
    @ApiModelProperty("隐含评级标签 1(AAA+,AAA,AAA-)  2(AA+,AA,AA(2),AA-)  行业：3(A+,A,A-)")
    protected Integer bondImpliedRatingMappingTag;
    @ApiModelProperty("产业: yy评级标签 1(1,2,3,4,5)  2(6,7,8) 城投: yy评级标签 1(1,2,3,4,5,6)  2(7,8)")
    protected Integer comYyRatingMappingTag;
    @ApiModelProperty("剩余期限(1,2,3,4,5)")
    protected Integer spreadRemainingTenorTag;
    @ApiModelProperty("利差债券类型 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)")
    protected Integer spreadBondType;
    @ApiModelProperty("担保状态: 0: 无, 1: 有")
    protected Integer guaranteeStatus;
    @ApiModelProperty(value = "利差日期", example = "2020-04-07")
    protected Date spreadDate;
    @ApiModelProperty("排序")
    protected SortDTO sort;
    @ApiModelProperty("隐含评级标签 AAA+,AAA,AAA-,AA+,AA,AA(2),AA-,A+,A,A-")
    protected Integer[] bondImpliedRatingMappings;
    @ApiModelProperty("yy评级标签 1,2,3,4,5,6,7,8)")
    protected Integer[] comYyRatingMappings;

    public Integer[] getBondImpliedRatingMappings() {
        return isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public void setBondImpliedRatingMappings(Integer[] bondImpliedRatingMappings) {
        this.bondImpliedRatingMappings = isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public Integer[] getComYyRatingMappings() {
        return isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public void setComYyRatingMappings(Integer[] comYyRatingMappings) {
        this.comYyRatingMappings = isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer getBondImpliedRatingMappingTag() {
        return bondImpliedRatingMappingTag;
    }

    public void setBondImpliedRatingMappingTag(Integer bondImpliedRatingMappingTag) {
        this.bondImpliedRatingMappingTag = bondImpliedRatingMappingTag;
    }

    public Integer getComYyRatingMappingTag() {
        return comYyRatingMappingTag;
    }

    public void setComYyRatingMappingTag(Integer comYyRatingMappingTag) {
        this.comYyRatingMappingTag = comYyRatingMappingTag;
    }


    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getGuaranteeStatus() {
        return guaranteeStatus;
    }

    public void setGuaranteeStatus(Integer guaranteeStatus) {
        this.guaranteeStatus = guaranteeStatus;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }
}
