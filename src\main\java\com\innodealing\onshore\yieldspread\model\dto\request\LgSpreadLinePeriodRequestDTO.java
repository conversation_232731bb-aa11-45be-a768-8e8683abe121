package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 地方债区域利差请求基础参数
 *
 * <AUTHOR>
 */
public class LgSpreadLinePeriodRequestDTO extends LgSpreadDateRangeRequestDTO {

    @ApiModelProperty(value = "地区code集合", required = true)
    private List<Long> comUniCodeList;
    @ApiModelProperty(value = "期限类型，期限类型，1:1M 3:3M 6:6M 9:9M 12:1Y 24:2Y 36:3Y 60:5Y 84:7Y 120:10Y 180:15Y 240:20Y 360:30Y  600:50Y", required = true)
    private Integer periodType;


    public List<Long> getComUniCodeList() {
        return Objects.isNull(comUniCodeList) ? new ArrayList<>() : new ArrayList<>(comUniCodeList);
    }

    public void setComUniCodeList(List<Long> comUniCodeList) {
        this.comUniCodeList = Objects.isNull(comUniCodeList) ? new ArrayList<>() : new ArrayList<>(comUniCodeList);
    }

    public Integer getPeriodType() {
        return periodType;
    }

    public void setPeriodType(Integer periodType) {
        this.periodType = periodType;
    }
}
