package com.innodealing.onshore.yieldspread.model.dto;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 唯一编码收集结果DTO(包含债券，主体)
 *
 * <AUTHOR>
 */
public class UniCodeCollectResultDTO {

    private Set<Long> induUniCodes;

    private Set<Long> udicUniCodes;

    private Set<Long> secuUniCodes;

    private Set<Long> bankUniCodes;

    private Set<Long> insuUniCodes;

    /**
     * 构造函数
     */
    public UniCodeCollectResultDTO() {
        this.induUniCodes = new HashSet<>();
        this.udicUniCodes = new HashSet<>();
        this.secuUniCodes = new HashSet<>();
        this.bankUniCodes = new HashSet<>();
        this.insuUniCodes = new HashSet<>();
    }

    public Set<Long> getInduUniCodes() {
        return Objects.isNull(induUniCodes) ? new HashSet<>() : new HashSet<>(induUniCodes);
    }

    /**
     * 添加产业
     *
     * @param induUniCodes 产业债券
     */
    public void addInduUniCodes(Set<Long> induUniCodes) {
        this.induUniCodes.addAll(Objects.isNull(induUniCodes) ? new HashSet<>() : induUniCodes);
    }

    public Set<Long> getUdicUniCodes() {
        return Objects.isNull(udicUniCodes) ? new HashSet<>() : new HashSet<>(udicUniCodes);
    }

    /**
     * 添加城投
     *
     * @param udicUniCodes 城投
     */
    public void addUdicUniCodes(Set<Long> udicUniCodes) {
        this.udicUniCodes.addAll(Objects.isNull(udicUniCodes) ? new HashSet<>() : udicUniCodes);
    }

    public Set<Long> getSecuUniCodes() {
        return Objects.isNull(secuUniCodes) ? new HashSet<>() : new HashSet<>(secuUniCodes);
    }

    /**
     * 添加证券
     *
     * @param secuUniCodes 证券
     */
    public void addSecuUniCodes(Set<Long> secuUniCodes) {
        this.secuUniCodes.addAll(Objects.isNull(secuUniCodes) ? new HashSet<>() : secuUniCodes);
    }

    public Set<Long> getBankUniCodes() {
        return Objects.isNull(bankUniCodes) ? new HashSet<>() : new HashSet<>(bankUniCodes);
    }

    /**
     * 添加银行
     *
     * @param bankUniCodes 银行债券
     */
    public void addBankUniCodes(Set<Long> bankUniCodes) {
        this.bankUniCodes.addAll(Objects.isNull(bankUniCodes) ? new HashSet<>() : bankUniCodes);
    }

    public Set<Long> getInsuUniCodes() {
        return Objects.isNull(insuUniCodes) ? new HashSet<>() : new HashSet<>(insuUniCodes);
    }

    /**
     * 添加保险
     *
     * @param insuUniCodes 保险债券
     */
    public void addInsuUniCodes(Set<Long> insuUniCodes) {
        this.insuUniCodes.addAll(Objects.isNull(insuUniCodes) ? new HashSet<>() : insuUniCodes);
    }
}
