package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.enums.UdicRegionEnum;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.mv.MvUdicShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvUdicBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvUdicBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.YyRatingRouter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 城投利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvUdicBondYieldSpreadCurveDAO {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MvUdicBondYieldSpreadCurveMapper mvUdicBondYieldSpreadCurveMapper;

    @Resource
    private MvUdicShardBondYieldSpreadCurveMapper mvUdicShardBondYieldSpreadCurveMapper;

    /**
     * 刷新物化视图
     *
     * @param udicRegionEnum 城投区域
     */
    public void refreshMvUdicBondYieldSpreadCurve(UdicRegionEnum udicRegionEnum) {
        if (Objects.isNull(udicRegionEnum) || udicRegionEnum == UdicRegionEnum.ALL) {
            mvUdicBondYieldSpreadCurveMapper.refreshMvUdicBondYieldSpreadCurveAll();
        }
        if (Objects.isNull(udicRegionEnum) || udicRegionEnum == UdicRegionEnum.PROVINCE) {
            mvUdicBondYieldSpreadCurveMapper.refreshMvUdicBondYieldSpreadCurveProvince();
        }
        if (Objects.isNull(udicRegionEnum) || udicRegionEnum == UdicRegionEnum.CITY) {
            mvUdicBondYieldSpreadCurveMapper.refreshMvUdicBondYieldSpreadCurveCity();
        }
        if (Objects.isNull(udicRegionEnum) || udicRegionEnum == UdicRegionEnum.DISTRICT) {
            mvUdicBondYieldSpreadCurveMapper.refreshMvUdicBondYieldSpreadCurveDistrict();
        }
    }

    /**
     * 刷新上一天日期的物化视图
     */
    public void refreshCurveYesterday() {
        mvUdicBondYieldSpreadCurveMapper.refreshMvUdicBondYieldSpreadCurveAllYesterday();
        mvUdicBondYieldSpreadCurveMapper.refreshMvUdicBondYieldSpreadCurveProvinceYesterday();
        mvUdicBondYieldSpreadCurveMapper.refreshMvUdicBondYieldSpreadCurveCityYesterday();
        mvUdicBondYieldSpreadCurveMapper.refreshMvUdicBondYieldSpreadCurveDistrictYesterday();
    }

    /**
     * 创建城投利差曲线视图
     *
     * @param router 路由
     * @param param  刷新参数
     */
    public void createOrRefreshUdicCurveMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        MvUdicBondYieldSpreadCurveParameter parameter = this.builderShardMvParam(router);
        logger.info("[refreshMvInduBondYieldSpreadBondCurveRouter]开始创建城投评级分片物化视图:{}", parameter.getTableName());
        parameter.setTableName(parameter.getTableName());
        if (param.isMvRefresh()) {
            mvUdicBondYieldSpreadCurveMapper.dropMv(parameter.getTableName());
            mvUdicBondYieldSpreadCurveMapper.createMvRatingRouter(parameter);
        } else {
            mvUdicBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurve(parameter.getTableName());
        }
        logger.info("[refreshMvInduBondYieldSpreadBondCurveRouter]结束创建城投评级分片物化视图:{}", parameter.getTableName());

    }

    /**
     * 时间范围不为空，删除临时表
     *
     * @param router 路由
     */
    public void droTempMv(AbstractRatingRouter router) {
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (Objects.nonNull(spreadDateRange)) {
            mvUdicBondYieldSpreadCurveMapper.dropMv(this.getMvName(router));
        }
    }

    private MvUdicBondYieldSpreadCurveParameter builderShardMvParam(AbstractRatingRouter router) {
        MvUdicBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, MvUdicBondYieldSpreadCurveParameter.class);
        parameter.setTableName(this.getMvName(router));
        parameter.setOperatorLevel(router.getLevel());
        if (router instanceof ImplicitRatingRouter) {
            parameter.setImpliedRatingMappings(router.getRatings());
        }
        if (router instanceof YyRatingRouter) {
            parameter.setYyRatingMappings(router.getRatings());
        }
        return parameter;
    }

    /**
     * 获取视图名称
     *
     * @param router 路由
     * @return 视图名称
     */
    public String getMvName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()), mvUdicShardBondYieldSpreadCurveMapper.getLogicTable(),
                RatingCombinationHelper.getMvNameSuffix(router));
    }

}
