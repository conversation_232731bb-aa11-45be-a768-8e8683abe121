package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.SecuComYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadChangeBO;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.bo.MixYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.bo.SecuComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.SecuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.SPREAD_KEEP_SCALE;

/**
 * 证券主体利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class SecuComYieldSpreadDAO {

    @Resource
    private SecuComYieldSpreadMapper secuComYieldSpreadMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 批量更新
     *
     * @param spreadDate               利差日期
     * @param secuComYieldSpreadDOList 证券主体利差列表
     * @return 受影响的行数
     */
    public int saveSecuComYieldSpreadDOList(Date spreadDate, List<SecuComYieldSpreadDO> secuComYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(secuComYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> comUniCodes = secuComYieldSpreadDOList.stream().map(SecuComYieldSpreadDO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        AtomicInteger effectRows = new AtomicInteger();
        // 批量查询已存在的数据
        DynamicQuery<SecuComYieldSpreadDO> query = DynamicQuery.createQuery(SecuComYieldSpreadDO.class)
                .select(SecuComYieldSpreadDO::getId, SecuComYieldSpreadDO::getComUniCode,
                        SecuComYieldSpreadDO::getSpreadDate, SecuComYieldSpreadDO::getInduLevel1Code,
                        SecuComYieldSpreadDO::getInduLevel1Name, SecuComYieldSpreadDO::getInduLevel2Code,
                        SecuComYieldSpreadDO::getInduLevel2Name, SecuComYieldSpreadDO::getBusinessNature)
                .and(SecuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(SecuComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<SecuComYieldSpreadDO> existDataList = secuComYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(secuComYieldSpreadDOList));
        } else {
            Map<String, SecuComYieldSpreadDO> existSecuComYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                    x.getComUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<SecuComYieldSpreadDO> insertList = new ArrayList<>();
            List<SecuComYieldSpreadDO> updateList = new ArrayList<>();
            for (SecuComYieldSpreadDO secuComYieldSpreadDO : secuComYieldSpreadDOList) {
                SecuComYieldSpreadDO existSecuComYieldSpreadDO = existSecuComYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                secuComYieldSpreadDO.getComUniCode(), secuComYieldSpreadDO.getSpreadDate().getTime()));
                if (Objects.isNull(existSecuComYieldSpreadDO)) {
                    insertList.add(secuComYieldSpreadDO);
                } else {
                    secuComYieldSpreadDO.setId(existSecuComYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existSecuComYieldSpreadDO.getInduLevel1Code(), secuComYieldSpreadDO::setInduLevel1Code);
                    ObjectExtensionUtils.ifNonNull(existSecuComYieldSpreadDO.getInduLevel1Name(), secuComYieldSpreadDO::setInduLevel1Name);
                    ObjectExtensionUtils.ifNonNull(existSecuComYieldSpreadDO.getInduLevel2Code(), secuComYieldSpreadDO::setInduLevel2Code);
                    ObjectExtensionUtils.ifNonNull(existSecuComYieldSpreadDO.getInduLevel2Name(), secuComYieldSpreadDO::setInduLevel2Name);
                    ObjectExtensionUtils.ifNonNull(existSecuComYieldSpreadDO.getBusinessNature(), secuComYieldSpreadDO::setBusinessNature);
                    updateList.add(secuComYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 证券主体利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<SecuComYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<SecuComYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(SecuComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (SecuComYieldSpreadDO secuComYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<SecuComYieldSpreadDO> updateQuery = DynamicQuery.createQuery(SecuComYieldSpreadDO.class)
                        .and(SecuComYieldSpreadDO::getId, isEqual(secuComYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(secuComYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 证券主体利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<SecuComYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<SecuComYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(SecuComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (SecuComYieldSpreadDO secuComYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(secuComYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 查询债券主体利差
     *
     * @param spreadDate     利差日期
     * @param comUniCodeList 主体列表
     * @return ComYieldSpreadChangeBO
     */
    public List<ComYieldSpreadChangeBO> listSecuComYieldSpreads(Date spreadDate, Set<Long> comUniCodeList) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<SecuComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(SecuComYieldSpreadDO.class)
                .select(SecuComYieldSpreadDO::getComUniCode, SecuComYieldSpreadDO::getSpreadDate,
                        SecuComYieldSpreadDO::getComCreditSpread, SecuComYieldSpreadDO::getComExcessSpread)
                .and(SecuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(CollectionUtils.isNotEmpty(comUniCodeList),
                        SecuComYieldSpreadDO::getComUniCode, in(comUniCodeList));
        return BeanCopyUtils.copyList(secuComYieldSpreadMapper.selectByDynamicQuery(groupQuery), ComYieldSpreadChangeBO.class);
    }

    /**
     * 获取证券主体利差某一天的 所有 主题
     *
     * @param spreadDate 利差日期
     * @return comUniCodeList 主体列表
     */
    public List<Long> listSecuComYieldSpreadComUniCodes(@NonNull Date spreadDate) {
        DynamicQuery<SecuComYieldSpreadDO> query = DynamicQuery.createQuery(SecuComYieldSpreadDO.class)
                .select(SecuComYieldSpreadDO::getComUniCode)
                .and(SecuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(SecuComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return secuComYieldSpreadMapper.selectByDynamicQuery(query).stream().map(SecuComYieldSpreadDO::getComUniCode).collect(Collectors.toList());
    }

    /**
     * 获取证券历史分位的统计数据
     *
     * @param startDate      时间范围 开始时间
     * @param endDate        时间范围结束时间
     * @param issueDate      利差日期
     * @param comUniCodeList 主体列表
     * @return 分位统计数据
     */
    public List<ComYieldSpreadQuantileViewDO> listSecuComYieldQuantileStatistics(@NonNull Date startDate, @NonNull Date endDate,
                                                                                 @NonNull Date issueDate, List<Long> comUniCodeList) {
        return secuComYieldSpreadMapper.listComYieldQuantileStatisticsViews(startDate, endDate, issueDate, comUniCodeList);
    }

    /**
     * 获取主体利差
     *
     * @param isNewest 是否为最新一天
     * @param param    请求参数
     * @return 主体利差
     */
    public List<SecuComYieldSpreadBO> listComYieldSpreads(boolean isNewest, SecuYieldSearchParam param) {
        return secuComYieldSpreadMapper.listComYieldSpreads(isNewest, param);
    }

    /**
     * 获取主体数量
     *
     * @param param 请求参数
     * @return 主体数量
     */
    public Long countComYieldSpread(SecuYieldSearchParam param) {
        return secuComYieldSpreadMapper.countComYieldSpread(param);
    }

    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<SecuComYieldSpreadDO> query = DynamicQuery.createQuery(SecuComYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt = secuComYieldSpreadMapper.selectMaxByDynamicQuery(SecuComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 查询主库获取最大日期
     *
     * @return
     */
    public Optional<Date> getMaxSpreadDateForMaster() {
        DynamicQuery<SecuComYieldSpreadDO> query = DynamicQuery.createQuery(SecuComYieldSpreadDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER);
        Optional<java.util.Date> dateOpt = secuComYieldSpreadMapper.selectMaxByDynamicQuery(SecuComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 查询主体利差数据集
     *
     * @param spreadDate  利差日期
     * @param comUniCodes 主体列表
     * @return ComYieldSpreadChangeBO
     */
    public List<SecuComYieldSpreadDO> listComYieldSpreads(@NonNull Date spreadDate, @NonNull Collection<Long> comUniCodes) {
        DynamicQuery<SecuComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(SecuComYieldSpreadDO.class)
                .and(SecuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(SecuComYieldSpreadDO::getComUniCode, in(comUniCodes));
        return BeanCopyUtils.copyList(secuComYieldSpreadMapper.selectByDynamicQuery(groupQuery), SecuComYieldSpreadDO.class);
    }


    /**
     * 查询证券主体利差数据集
     *
     * @param spreadDate 利差日期
     * @return {@link List}<{@link SecuComYieldSpreadDO}> 城投主体利差响应数据集
     */
    public List<ComYieldSpreadShortBO> listShortInfosBySpreadDate(Date spreadDate) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<SecuComYieldSpreadDO> query = DynamicQuery.createQuery(SecuComYieldSpreadDO.class)
                .select(SecuComYieldSpreadDO::getSpreadDate,
                        SecuComYieldSpreadDO::getComUniCode,
                        SecuComYieldSpreadDO::getComCreditSpread,
                        SecuComYieldSpreadDO::getComExcessSpread,
                        SecuComYieldSpreadDO::getComCbYield)
                .and(SecuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(SecuComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return BeanCopyUtils.copyList(secuComYieldSpreadMapper.selectByDynamicQuery(query), ComYieldSpreadShortBO.class);
    }

    /**
     * 获取主体利差
     *
     * @param comUniCodes 主体列表
     * @return 主体利差列表
     */
    public List<MixYieldSpreadShortBO> listAllYieldSpreads(List<Long> comUniCodes) {
        if (!ObjectExtensionUtils.isAllNotEmpty(comUniCodes)) {
            return Collections.emptyList();
        }
        DynamicQuery<SecuComYieldSpreadDO> query = DynamicQuery.createQuery(SecuComYieldSpreadDO.class)
                .select(SecuComYieldSpreadDO::getSpreadDate, SecuComYieldSpreadDO::getComUniCode,
                        SecuComYieldSpreadDO::getComCreditSpread, SecuComYieldSpreadDO::getComExcessSpread,
                        SecuComYieldSpreadDO::getComSeniorCreditSpread, SecuComYieldSpreadDO::getComSeniorExcessSpread,
                        SecuComYieldSpreadDO::getComSubordinatedCreditSpread, SecuComYieldSpreadDO::getComSubordinatedExcessSpread,
                        SecuComYieldSpreadDO::getComPerpetualCreditSpread, SecuComYieldSpreadDO::getComPerpetualExcessSpread)
                .and(SecuComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<SecuComYieldSpreadDO> comYieldSpreadList = secuComYieldSpreadMapper.selectByDynamicQuery(query);
        return comYieldSpreadList.stream().map(com -> {
            MixYieldSpreadShortBO mixYieldSpreadShortBO = new MixYieldSpreadShortBO();
            mixYieldSpreadShortBO.setComUniCode(com.getComUniCode());
            mixYieldSpreadShortBO.setSpreadDate(com.getSpreadDate());
            handlePrecision(mixYieldSpreadShortBO, com);
            return mixYieldSpreadShortBO;
        }).collect(Collectors.toList());
    }

    private void handlePrecision(MixYieldSpreadShortBO mixYieldSpreadShortBO, SecuComYieldSpreadDO comYieldSpreadDO) {
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setComCreditSpread);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setComExcessSpread);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComSeniorCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldOne);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComSeniorExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldOne);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComSubordinatedCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldTwo);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComSubordinatedExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldTwo);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPerpetualCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldTree);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPerpetualExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldTree);
    }
}
