package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition;

import javax.persistence.Column;
import javax.persistence.Table;


/**
 * 城投债券利差曲线分区表(包含省)
 *
 * <AUTHOR>
 */
@Table(name = "udic_bond_yield_spread_curve_province")
public class PgUdicBondYieldSpreadCurveProvinceDO extends BasePgUdicBondYieldSpreadCurveDO {
    /**
     * 省级编码
     */
    @Column
    private Long provinceUniCode;

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }
}