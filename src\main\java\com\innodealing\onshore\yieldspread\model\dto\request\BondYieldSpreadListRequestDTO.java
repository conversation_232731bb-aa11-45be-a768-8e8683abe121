package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 单券利差债券列表响应DTO
 *
 * <AUTHOR>
 */
public class BondYieldSpreadListRequestDTO {

    @ApiModelProperty(value = "产业债券唯一编码")
    @Size(message = "产业债券编码最大为{max}个", max = 4)
    private List<BondYieldSpreadItemListRequestDTO> indus;
    @ApiModelProperty(value = "城投债券唯一编码")
    @Size(message = "城投债券编码最大为{max}个", max = 4)
    private List<BondYieldSpreadItemListRequestDTO> udics;
    @ApiModelProperty(value = "银行债券唯一编码")
    @Size(message = "银行债券编码最大为{max}个", max = 4)
    private List<BondYieldSpreadItemListRequestDTO> banks;
    @ApiModelProperty(value = "证券债券唯一编码")
    @Size(message = "证券债券编码最大为{max}个", max = 4)
    private List<BondYieldSpreadItemListRequestDTO> secus;

    @ApiModelProperty(value = "保险债券唯一编码")
    @Size(message = "保险债券编码最大为{max}个", max = 4)
    private List<BondYieldSpreadItemListRequestDTO> insus;
    @ApiModelProperty(value = "自选债券唯一编码")
    @Size(message = "自选债券编码最大为{max}个", max = 4)
    private List<BondYieldSpreadItemListRequestDTO> customBonds;
    @ApiModelProperty(value = "利差日期")
    @NotNull(message = "利差日期不能为空")
    private Date spreadDate;

    public List<BondYieldSpreadItemListRequestDTO> getIndus() {
        return Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public void setIndus(List<BondYieldSpreadItemListRequestDTO> indus) {
        this.indus = Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public List<BondYieldSpreadItemListRequestDTO> getUdics() {
        return Objects.isNull(udics) ? new ArrayList<>() : new ArrayList<>(udics);
    }

    public void setUdics(List<BondYieldSpreadItemListRequestDTO> udics) {
        this.udics = Objects.isNull(udics) ? new ArrayList<>() : new ArrayList<>(udics);
    }

    public List<BondYieldSpreadItemListRequestDTO> getBanks() {
        return Objects.isNull(banks) ? new ArrayList<>() : new ArrayList<>(banks);
    }

    public void setBanks(List<BondYieldSpreadItemListRequestDTO> banks) {
        this.banks = Objects.isNull(banks) ? new ArrayList<>() : new ArrayList<>(banks);
    }

    public List<BondYieldSpreadItemListRequestDTO> getSecus() {
        return Objects.isNull(secus) ? new ArrayList<>() : new ArrayList<>(secus);
    }

    public void setSecus(List<BondYieldSpreadItemListRequestDTO> secus) {
        this.secus = Objects.isNull(secus) ? new ArrayList<>() : new ArrayList<>(secus);
    }

    public List<BondYieldSpreadItemListRequestDTO> getInsus() {
        return Objects.isNull(insus) ? new ArrayList<>() : new ArrayList<>(insus);
    }

    public void setInsus(List<BondYieldSpreadItemListRequestDTO> indus) {
        this.insus = Objects.isNull(indus) ? new ArrayList<>() : new ArrayList<>(indus);
    }

    public List<BondYieldSpreadItemListRequestDTO> getCustomBonds() {
        return Objects.isNull(customBonds) ? new ArrayList<>() : new ArrayList<>(customBonds);
    }

    public void setCustomBonds(List<BondYieldSpreadItemListRequestDTO> customBonds) {
        this.customBonds = Objects.isNull(customBonds) ? new ArrayList<>() : new ArrayList<>(customBonds);
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = new Date(spreadDate.getTime());
    }
}
