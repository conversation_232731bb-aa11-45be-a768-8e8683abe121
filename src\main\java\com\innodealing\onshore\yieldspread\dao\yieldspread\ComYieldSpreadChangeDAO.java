package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.ComYieldSpreadChangeMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.ComYieldSpreadChangeDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 主体利差变动 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class ComYieldSpreadChangeDAO {
    @Resource
    private ComYieldSpreadChangeMapper comYieldSpreadChangeMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 批量更新
     *
     * @param spreadDate              利差日期
     * @param comYieldSpreadChangeDOs 主体利差变动
     * @param yieldSpreadSector 主体利差模块
     * @return 受影响的行数
     */
    public int saveComYieldSpreadChanges(Date spreadDate, List<ComYieldSpreadChangeDO> comYieldSpreadChangeDOs
            , Integer yieldSpreadSector) {
        if (CollectionUtils.isEmpty(comYieldSpreadChangeDOs)) {
            return 0;
        }
        Set<Long> comUniCodes = comYieldSpreadChangeDOs.stream().map(ComYieldSpreadChangeDO::getComUniCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toSet());
        AtomicInteger effectRows = new AtomicInteger();
        // 批量查询已存在的数据
        DynamicQuery<ComYieldSpreadChangeDO> query = DynamicQuery.createQuery(ComYieldSpreadChangeDO.class)
                .select(ComYieldSpreadChangeDO::getId, ComYieldSpreadChangeDO::getComUniCode,
                        ComYieldSpreadChangeDO::getSpreadDate)
                .and(ComYieldSpreadChangeDO::getSpreadDate, isEqual(spreadDate))
                .and(ComYieldSpreadChangeDO::getComUniCode, in(comUniCodes))
                .and(ComYieldSpreadChangeDO::getComSpreadSector, isEqual(yieldSpreadSector));
        List<ComYieldSpreadChangeDO> existDataList = comYieldSpreadChangeMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(comYieldSpreadChangeDOs));
        } else {
            Map<String, ComYieldSpreadChangeDO> existcomYieldSpreadChangeDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                    x.getComUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<ComYieldSpreadChangeDO> insertList = new ArrayList<>();
            List<ComYieldSpreadChangeDO> updateList = new ArrayList<>();
            for (ComYieldSpreadChangeDO comYieldSpreadChangeDO : comYieldSpreadChangeDOs) {
                ComYieldSpreadChangeDO existcomYieldSpreadChangeDO = existcomYieldSpreadChangeDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                comYieldSpreadChangeDO.getComUniCode(), comYieldSpreadChangeDO.getSpreadDate().getTime()));
                if (Objects.isNull(existcomYieldSpreadChangeDO)) {
                    insertList.add(comYieldSpreadChangeDO);
                } else {
                    comYieldSpreadChangeDO.setId(existcomYieldSpreadChangeDO.getId());
                    updateList.add(comYieldSpreadChangeDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 清空昨日之前的变动数据
     *
     * @param startDate 开始日期， 这样以前的数据就需要清除
     * @return 影响行数
     */
    @Transactional(transactionManager = YieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int clearOldSpreadChanges(Date startDate) {
        DynamicQuery<ComYieldSpreadChangeDO> query = DynamicQuery.createQuery(ComYieldSpreadChangeDO.class)
                .and(ComYieldSpreadChangeDO::getSpreadDate, lessThan(startDate));
        return comYieldSpreadChangeMapper.deleteByDynamicQuery(query);
    }

    /**
     * 批量更新
     *
     * @param updateList 主体利差变动列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<ComYieldSpreadChangeDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<ComYieldSpreadChangeMapper> updateBatchAction =
                MapperBatchAction.create(ComYieldSpreadChangeMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (ComYieldSpreadChangeDO comYieldSpreadChangeDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<ComYieldSpreadChangeDO> updateQuery = DynamicQuery.createQuery(ComYieldSpreadChangeDO.class)
                        .and(ComYieldSpreadChangeDO::getId, isEqual(comYieldSpreadChangeDO.getId()));
                mapper.updateByDynamicQuery(comYieldSpreadChangeDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 主体利差变动列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<ComYieldSpreadChangeDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<ComYieldSpreadChangeMapper> insertBatchAction =
                MapperBatchAction.create(ComYieldSpreadChangeMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (ComYieldSpreadChangeDO comYieldSpreadChangeDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(comYieldSpreadChangeDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 查询最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<ComYieldSpreadChangeDO> query = DynamicQuery.createQuery(ComYieldSpreadChangeDO.class);
        Optional<java.util.Date> dateOpt = comYieldSpreadChangeMapper.selectMaxByDynamicQuery(ComYieldSpreadChangeDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }
}
