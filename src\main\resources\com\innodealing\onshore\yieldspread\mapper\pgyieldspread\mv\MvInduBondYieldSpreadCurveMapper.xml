<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvInduBondYieldSpreadCurveMapper">


    <sql id="ratingRouterQuerySql">
        SELECT max(indu_bond_yield_spread.id) AS id,
        indu_bond_yield_spread.spread_date,
        <choose>
            <when test="parameter.induLevel == 'indu1'">
                indu_bond_yield_spread.indu_level1_code AS indu_level_code,
            </when>
            <when test="parameter.induLevel == 'indu2'">
                indu_bond_yield_spread.indu_level2_code AS indu_level_code,
            </when>
            <otherwise>
                000000 AS indu_level_code,
            </otherwise>
        </choose>
        MEDIAN(((indu_bond_yield_spread.bond_credit_spread * (100000)::numeric))::bigint) AS bond_credit_spread,
        MEDIAN(((indu_bond_yield_spread.bond_excess_spread * (100000)::numeric))::bigint) AS bond_excess_spread,
        MEDIAN(((indu_bond_yield_spread.cb_yield * (100000)::numeric))::bigint) AS cb_yield,
        AVG(((indu_bond_yield_spread.bond_credit_spread * (100000)::numeric))::bigint) AS avg_bond_credit_spread,
        AVG(((indu_bond_yield_spread.bond_excess_spread * (100000)::numeric))::bigint) AS avg_bond_excess_spread,
        AVG(((indu_bond_yield_spread.cb_yield * (100000)::numeric))::bigint) AS avg_cb_yield,
        count(indu_bond_yield_spread.bond_credit_spread) AS bond_credit_spread_count,
        count(indu_bond_yield_spread.bond_excess_spread) AS bond_excess_spread_count,
        count(indu_bond_yield_spread.cb_yield) AS cb_yield_count,
        indu_bond_yield_spread.spread_bond_type,
        indu_bond_yield_spread.bond_ext_rating_mapping,
        indu_bond_yield_spread.spread_remaining_tenor_tag,
        indu_bond_yield_spread.guaranteed_status,
        grouping(indu_bond_yield_spread.spread_bond_type) AS using_spread_bond_type,
        grouping(indu_bond_yield_spread.bond_ext_rating_mapping) AS using_bond_ext_rating_mapping,
        grouping(indu_bond_yield_spread.spread_remaining_tenor_tag) AS using_spread_remaining_tenor_tag,
        grouping(indu_bond_yield_spread.guaranteed_status) AS using_guaranteed_status
        FROM yield_spread.indu_bond_yield_spread
        where 1=1
        <if test="parameter.spreadDateRange != null">
            AND indu_bond_yield_spread.spread_date BETWEEN '${parameter.spreadDateRange.startDate}' and
            '${parameter.spreadDateRange.endDate}'
        </if>
        <if test="parameter.impliedRatingMappings != null and parameter.impliedRatingMappings.size() > 0">
            AND indu_bond_yield_spread.bond_implied_rating_mapping in
            <foreach collection="parameter.impliedRatingMappings" item="item" open="(" close=")" separator=",">
                '${item}'
            </foreach>
        </if>
        <if test="parameter.yyRatingMappings != null and parameter.yyRatingMappings.size() > 0">
            AND indu_bond_yield_spread.com_yy_rating_mapping in
            <foreach collection="parameter.yyRatingMappings" item="item" open="(" close=")" separator=",">
                '${item}'
            </foreach>
        </if>
        GROUP BY
        indu_bond_yield_spread.spread_date,
        <if test="parameter.induLevel == 'indu1'">
            indu_bond_yield_spread.indu_level1_code,
        </if>
        <if test="parameter.induLevel == 'indu2'">
            indu_bond_yield_spread.indu_level2_code,
        </if>
        CUBE (
        indu_bond_yield_spread.spread_bond_type,
        indu_bond_yield_spread.bond_ext_rating_mapping,
        indu_bond_yield_spread.spread_remaining_tenor_tag,
        indu_bond_yield_spread.guaranteed_status
        )
    </sql>

    <update id="createMvRatingRouter">
        create MATERIALIZED view ${parameter.tableName}
        WITH (appendoptimized= true, orientation = column)
        as
        <include refid="ratingRouterQuerySql"></include>
    </update>

    <update id="refreshMvInduBondYieldSpreadCurveAll">
        REFRESH
        MATERIALIZED VIEW mv_indu_bond_yield_spread_curve_all;
    </update>

    <update id="refreshMvInduBondYieldSpreadCurveIndu1">
        REFRESH
        MATERIALIZED VIEW mv_indu_bond_yield_spread_curve_indu1;
    </update>

    <update id="refreshMvInduBondYieldSpreadCurveIndu2">
        REFRESH
        MATERIALIZED VIEW mv_indu_bond_yield_spread_curve_indu2;
    </update>

    <update id="refreshMvInduBondYieldSpreadCurveAllYesterday">
        REFRESH
        MATERIALIZED VIEW mv_indu_bond_yield_spread_curve_all_yesterday;
    </update>

    <update id="refreshMvInduBondYieldSpreadCurveIndu1Yesterday">
        REFRESH
        MATERIALIZED VIEW mv_indu_bond_yield_spread_curve_indu1_yesterday;
    </update>

    <update id="refreshMvInduBondYieldSpreadCurveIndu2Yesterday">
        REFRESH
        MATERIALIZED VIEW mv_indu_bond_yield_spread_curve_indu2_yesterday;
    </update>
</mapper>