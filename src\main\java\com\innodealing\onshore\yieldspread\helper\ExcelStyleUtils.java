package com.innodealing.onshore.yieldspread.helper;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;

/**
 * excel 样式设置
 *
 * <AUTHOR>
 * @create: 2024-12-18
 */
public final class ExcelStyleUtils {

    private static final String DEFAULT_HEAD_FONT = "宋体";
    private static final String DEFAULT_CONTENT_FONT = "Calibri";

    private ExcelStyleUtils() {
    }

    /**
     * 表头样式
     * @return 默认表头样式
     */
    public static WriteCellStyle getHeadStyle(){
        return getHeadStyle(DEFAULT_HEAD_FONT);
    }

    /**
     * 内容样式
     * @return 默认内容样式
     */
    public static WriteCellStyle getContentStyle(){
        return getContentStyle(DEFAULT_CONTENT_FONT);
    }

    /**
     * 表头样式
     * @param fontName 字体
     * @return 表头样式
     */
    public static WriteCellStyle getHeadStyle(String fontName) {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 字体
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName(fontName);
        headWriteCellStyle.setWriteFont(headWriteFont);
        return headWriteCellStyle;

    }

    /**
     * 内容样式
     * @param fontName 字体
     * @return 内容样式
     */
    public static WriteCellStyle getContentStyle(String fontName) {
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置字体
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName(fontName);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        return contentWriteCellStyle;
    }

}
