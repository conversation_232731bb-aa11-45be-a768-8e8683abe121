package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.UdicAreaYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.bo.UdicAreaYieldSpreadQueryBO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicAreaYieldSpreadDO;
import com.innodealing.onshore.yieldspread.router.annotation.ShardingHindStrParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;


/**
 * dm城投口径区域利差表数据库访问层 {@link UdicAreaYieldSpreadDO}
 * 对UdicRegionYieldSpreadDmMapper层做出简单封装 {@link UdicAreaYieldSpreadMapper}
 *
 * <AUTHOR>
 */
@Repository
public class UdicAreaYieldSpreadDAO {

    @Resource
    private UdicAreaYieldSpreadMapper udicAreaYieldSpreadMapper;

    @Resource(name = YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 幂等保存dm城投口径区域利差  udicRegionYieldSpreadDmDOs
     *
     * @param udicAreaYieldSpreadDOs {@link UdicAreaYieldSpreadDO}
     * @param spreadDate             利差日期
     */
    @ShardingHindStrParam(logicTableName = YieldSpreadConst.UDIC_AREA_YIELD_SPREAD_TABLE_NAME)
    @Transactional(rollbackFor = Exception.class, transactionManager = YieldSpreadDataSourceConfig.TRANSACTION_NAME)
    public void saveBatchUdicAreaYieldSpreadDOs(@NotEmpty final Collection<UdicAreaYieldSpreadDO> udicAreaYieldSpreadDOs, @NotNull Date spreadDate) {
        if (CollectionUtils.isEmpty(udicAreaYieldSpreadDOs)) {
            return;
        }

        Map<String, Long> businessKeyToIdMap = getBusinessKeyToIdMap(udicAreaYieldSpreadDOs, spreadDate);
        List<UdicAreaYieldSpreadDO> insertList = Lists.newArrayListWithExpectedSize(udicAreaYieldSpreadDOs.size());
        List<UdicAreaYieldSpreadDO> updateList = Lists.newArrayListWithExpectedSize(udicAreaYieldSpreadDOs.size());
        for (UdicAreaYieldSpreadDO udicAreaYieldSpreadDO : udicAreaYieldSpreadDOs) {
            Long oldId = businessKeyToIdMap.get(getBusinessKey(udicAreaYieldSpreadDO));
            if (Objects.isNull(oldId)) {
                insertList.add(udicAreaYieldSpreadDO);
            } else {
                udicAreaYieldSpreadDO.setId(oldId);
                updateList.add(udicAreaYieldSpreadDO);
            }
        }
        insertBatchUdicAreaYieldSpreadDmDOs(insertList);
        updateBatchUdicAreaYieldSpreadDmDOsByPrimaryKey(updateList);
    }


    /**
     * 批量新增dm城投口径区域利差 {@link UdicAreaYieldSpreadMapper#insertSelective(Object)}
     *
     * @param udicAreaYieldSpreadDOs {@link UdicAreaYieldSpreadDO}
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = YieldSpreadDataSourceConfig.TRANSACTION_NAME)
    public void insertBatchUdicAreaYieldSpreadDmDOs(final List<UdicAreaYieldSpreadDO> udicAreaYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(udicAreaYieldSpreadDOs)) {
            return;
        }
        final MapperBatchAction<UdicAreaYieldSpreadMapper> insertBatchAction = MapperBatchAction
                .create(UdicAreaYieldSpreadMapper.class, sqlSessionFactory);
        for (UdicAreaYieldSpreadDO udicAreaYieldSpreadDO : udicAreaYieldSpreadDOs) {
            insertBatchAction.addAction(mapper -> mapper.insertSelective(udicAreaYieldSpreadDO));
        }
        insertBatchAction.doBatchActions();
    }

    /**
     * 批量更新dm城投口径区域利差 {@link UdicAreaYieldSpreadMapper#updateByPrimaryKeySelective(Object)}
     *
     * @param udicAreaYieldSpreadDOs {@link UdicAreaYieldSpreadDO}
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = YieldSpreadDataSourceConfig.TRANSACTION_NAME)
    public void updateBatchUdicAreaYieldSpreadDmDOsByPrimaryKey(final List<UdicAreaYieldSpreadDO> udicAreaYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(udicAreaYieldSpreadDOs)) {
            return;
        }
        final MapperBatchAction<UdicAreaYieldSpreadMapper> updateBatchAction = MapperBatchAction
                .create(UdicAreaYieldSpreadMapper.class, sqlSessionFactory);
        for (UdicAreaYieldSpreadDO udicAreaYieldSpreadDO : udicAreaYieldSpreadDOs) {
            UpdateQuery<UdicAreaYieldSpreadDO> updateQuery = UpdateQuery.createQuery(UdicAreaYieldSpreadDO.class)
                    .set(udicAreaYieldSpreadDO, ignore -> ignore.ignore(UdicAreaYieldSpreadDO::getId,
                            UdicAreaYieldSpreadDO::getDeleted,
                            UdicAreaYieldSpreadDO::getCreateTime,
                            UdicAreaYieldSpreadDO::getUpdateTime))
                    .and(UdicAreaYieldSpreadDO::getId, isEqual(udicAreaYieldSpreadDO.getId()));
            updateBatchAction.addAction(mapper -> mapper.updateByUpdateQuery(updateQuery));
        }
        updateBatchAction.doBatchActions();
    }

    /**
     * 更新dm城投口径区域利差 删除状态
     *
     * @param ids     id 列表
     * @param deleted 删除状态枚举
     */
    public void updateDeletedByIds(Collection<Long> ids, Deleted deleted) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        UpdateQuery<UdicAreaYieldSpreadDO> updateQuery = UpdateQuery.createQuery(UdicAreaYieldSpreadDO.class)
                .set(UdicAreaYieldSpreadDO::getDeleted, deleted.getValue())
                .and(UdicAreaYieldSpreadDO::getId, c -> c.in(ids));
        udicAreaYieldSpreadMapper.updateByUpdateQuery(updateQuery);
    }

    /**
     * 根据id查询dm城投口径区域利差 {@link UdicAreaYieldSpreadMapper#selectByPrimaryKey(Object)}
     *
     * @param id id 主键id
     * @return {@link UdicAreaYieldSpreadDO}
     */
    public Optional<UdicAreaYieldSpreadDO> getUdicAreaYieldSpreadDmDOById(Long id) {
        return Optional.ofNullable(udicAreaYieldSpreadMapper.selectByPrimaryKey(id));
    }

    /**
     * 分页查询 dm城投口径区域利差
     *
     * @param udicAreaYieldSpreadQueryBO {@link UdicAreaYieldSpreadQueryBO}
     * @param pageSize                   每页大小
     * @param pageNum                    页码
     * @param sortProperty               排序字段
     * @param sortDirection              排序方向
     * @return {@link NormPagingResult}
     */
    public NormPagingResult<UdicAreaYieldSpreadDO> pageQuery(
            UdicAreaYieldSpreadQueryBO udicAreaYieldSpreadQueryBO, Integer pageSize, Integer pageNum, String sortProperty,
            SortDirection sortDirection) {
        NormPagingQuery<UdicAreaYieldSpreadDO> query =
                NormPagingQuery.createQuery(UdicAreaYieldSpreadDO.class, pageNum, pageSize, false, true)
                        .and(Objects.nonNull(udicAreaYieldSpreadQueryBO.getDeleted()),
                                UdicAreaYieldSpreadDO::getDeleted, isEqual(udicAreaYieldSpreadQueryBO.getDeleted()));
        query.addSorts(new SortDescriptor(sortProperty, sortDirection));
        return udicAreaYieldSpreadMapper.selectByNormalPaging(query);
    }

    /**
     * 获取某个利差日期 DB中已经存在的  城投口径区域利差 数据
     *
     * @param spreadDate             利差日期
     * @param udicAreaYieldSpreadDOs 城投利差数据
     * @return map  key:业务key value:edb指标数据预测id
     */
    private Map<String, Long> getBusinessKeyToIdMap(Collection<UdicAreaYieldSpreadDO> udicAreaYieldSpreadDOs, Date spreadDate) {
        if (CollectionUtils.isEmpty(udicAreaYieldSpreadDOs) || Objects.isNull(spreadDate)) {
            return Maps.newHashMap();
        }
        DynamicQuery<UdicAreaYieldSpreadDO> dynamicQuery = DynamicQuery.createQuery(UdicAreaYieldSpreadDO.class)
                .select(UdicAreaYieldSpreadDO::getId, UdicAreaYieldSpreadDO::getAreaUniCode, UdicAreaYieldSpreadDO::getSpreadDate)
                .and(UdicAreaYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(UdicAreaYieldSpreadDO::getAreaUniCode, in(udicAreaYieldSpreadDOs.stream().map(UdicAreaYieldSpreadDO::getAreaUniCode).collect(Collectors.toList())));
        return udicAreaYieldSpreadMapper.selectByDynamicQuery(dynamicQuery).stream()
                .collect(Collectors.toMap(this::getBusinessKey, UdicAreaYieldSpreadDO::getId, (v1, v2) -> v2));
    }

    /**
     * 获取dm城投口径区域利差业务key
     *
     * @param udicAreaYieldSpreadDO {@link UdicAreaYieldSpreadDO}
     * @return dm城投口径区域利差业务key
     */
    private String getBusinessKey(UdicAreaYieldSpreadDO udicAreaYieldSpreadDO) {
        return String.format("%s:%s", udicAreaYieldSpreadDO.getAreaUniCode(), udicAreaYieldSpreadDO.getSpreadDate().toString());
    }
}
