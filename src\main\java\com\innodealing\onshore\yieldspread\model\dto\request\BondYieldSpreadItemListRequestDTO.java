package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * 单券利差债券列表响应DTO
 *
 * <AUTHOR>
 */
public class BondYieldSpreadItemListRequestDTO {

    @ApiModelProperty(value = "产业债券唯一编码")
    private Long bondUniCode;
    @ApiModelProperty(value = "曲线id")
    private Long curveId;

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Long getCurveId() {
        return curveId;
    }

    public void setCurveId(Long curveId) {
        this.curveId = curveId;
    }
}
