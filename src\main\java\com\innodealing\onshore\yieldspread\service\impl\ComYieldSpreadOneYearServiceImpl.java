package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.InternalKeyValueRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.dao.yieldspread.*;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.model.bo.ComCreditSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.*;
import com.innodealing.onshore.yieldspread.service.ComYieldSpreadOneYearService;
import com.innodealing.onshore.yieldspread.service.RedisService;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import com.innodealing.onshore.yieldspread.service.internal.InternalKeyValueService;
import jodd.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 主体利差服务
 *
 * <AUTHOR>
 */
@Service
public class ComYieldSpreadOneYearServiceImpl implements ComYieldSpreadOneYearService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final String REFRESH_HIST_COM_YIELD_SPREAD_KEY = "yield_spread:sync_com_yield_spread_one_year";
    private static final String YIELD_SPREAD_COM_YIELD_SPREAD_FLOW_ID = "yieldSpread:comYieldSpreadOneYearFlowId";
    private static final Integer CORE_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    private static final Integer BATCH_SIZE = 1000;
    private static final ExecutorService WORKER_POOL = new ThreadPoolExecutor(CORE_SIZE, CORE_SIZE,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(), ThreadFactoryBuilder.create()
            .setNameFormat("yield-spread-com-yield-spread-one-year-thread-%d").get());

    @Resource
    private InduComYieldSpreadDAO induComYieldSpreadDAO;
    @Resource
    private UdicComYieldSpreadDAO udicComYieldSpreadDAO;
    @Resource
    private ComYieldSpreadOneYearDAO comYieldSpreadOneYearDAO;
    @Resource
    private InternalKeyValueService internalKeyValueService;
    @Resource
    private RedisService redisService;
    @Resource
    private BankComYieldSpreadDAO bankComYieldSpreadDAO;
    @Resource
    private SecuComYieldSpreadDAO secuComYieldSpreadDAO;
    @Resource
    private HolidayService holidayService;

    @Override
    public int syncHistoryComYieldSpread(@Nullable Date startDate, @Nullable Date endDate) {
        // 查询同步日期,默认是前一天
        StopWatch stopWatch = new StopWatch();
        LocalDate localEndDate = Optional.ofNullable(endDate).map(Date::toLocalDate).orElseGet(() -> LocalDate.now().minusDays(1));
        LocalDate localStartDate = Optional.ofNullable(startDate).map(Date::toLocalDate)
                .orElseGet(() -> internalKeyValueService.getLocalDateValue(REFRESH_HIST_COM_YIELD_SPREAD_KEY).orElse(localEndDate.minusYears(1)));
        logger.info("syncHistoryComYieldSpread sync start, start_date:{}, end_date:{}", localStartDate, localEndDate);
        stopWatch.start("syncHistoryComYieldSpread");
        int count = 0;
        for (LocalDate syncDate = localStartDate; syncDate.compareTo(localEndDate) <= 0; syncDate = syncDate.plusDays(1)) {
            count += this.syncComYieldSpread(Date.valueOf(syncDate));
            internalKeyValueService.saveKeyValue(new InternalKeyValueRequestDTO(REFRESH_HIST_COM_YIELD_SPREAD_KEY, syncDate.plusDays(1).toString()));
            logger.info("syncHistoryComYieldSpread sync date: {} success", syncDate);
        }
        stopWatch.stop();
        logger.info("{} task end, spend time {} s", stopWatch.getLastTaskName(), stopWatch.getTotalTimeSeconds());
        return count;
    }

    @Override
    public int syncComYieldSpread(Date syncDate) {
        // 日期为空默认同步昨天数据
        Date spreadDate = ObjectExtensionUtils.getOrDefault(syncDate, Date.valueOf(LocalDate.now().minusDays(1)));
        if (holidayService.isHoliday(spreadDate)) {
            logger.info("ComYieldSpreadOneYearService#syncComYieldSpread date is holiday, date: {}", spreadDate);
            return 0;
        }
        SwThreadPoolWorker<Object> worker = SwThreadPoolWorker.of(WORKER_POOL);
        // 1. 城投
        CompletableFuture<Map<Long, ComYieldSpreadShortBO>> udicYieldSpreadFuture = worker.submit(() -> udicComYieldSpreadDAO.listShortInfosBySpreadDate(spreadDate)
                .stream().collect(Collectors.toMap(ComYieldSpreadShortBO::getComUniCode, Function.identity(), (o, v) -> o)));
        // 2. 银行
        CompletableFuture<Map<Long, ComYieldSpreadShortBO>> bankYieldSpreadFuture = worker.submit(() -> bankComYieldSpreadDAO.listShortInfosBySpreadDate(spreadDate)
                .stream().collect(Collectors.toMap(ComYieldSpreadShortBO::getComUniCode, Function.identity(), (o, v) -> o)));
        // 3. 证券
        CompletableFuture<Map<Long, ComYieldSpreadShortBO>> secuYieldSpreadFuture = worker.submit(() -> secuComYieldSpreadDAO.listShortInfosBySpreadDate(spreadDate)
                .stream().collect(Collectors.toMap(ComYieldSpreadShortBO::getComUniCode, Function.identity(), (o, v) -> o)));
        // 4. 产业
        CompletableFuture<Map<Long, ComYieldSpreadShortBO>> induYieldSpreadFuture = worker.submit(() -> induComYieldSpreadDAO.listShortInfosBySpreadDate(spreadDate)
                .stream().collect(Collectors.toMap(ComYieldSpreadShortBO::getComUniCode, Function.identity(), (o, v) -> o)));
        worker.doWorks(udicYieldSpreadFuture, bankYieldSpreadFuture, secuYieldSpreadFuture, induYieldSpreadFuture);
        Map<Long, ComYieldSpreadShortBO> udicYieldSpreadMap = udicYieldSpreadFuture.join();
        Map<Long, ComYieldSpreadShortBO> bankYieldSpreadMap = bankYieldSpreadFuture.join();
        Map<Long, ComYieldSpreadShortBO> secuYieldSpreadMap = secuYieldSpreadFuture.join();
        Map<Long, ComYieldSpreadShortBO> induYieldSpreadMap = induYieldSpreadFuture.join();
        Set<Long> comUniCodes = new HashSet<>(udicYieldSpreadMap.keySet());
        List<ComYieldSpreadOneYearDO> yieldSpreadList = Lists.newArrayList();
        // udic
        yieldSpreadList.addAll(this.toYieldSpread(udicYieldSpreadMap, spreadDate, ComYieldSpreadSectorEnum.UDIC));
        // bank
        bankYieldSpreadMap.entrySet().removeIf(entry -> comUniCodes.contains(entry.getKey()));
        comUniCodes.addAll(bankYieldSpreadMap.keySet());
        yieldSpreadList.addAll(this.toYieldSpread(bankYieldSpreadMap, spreadDate, ComYieldSpreadSectorEnum.BANK));
        // secu
        secuYieldSpreadMap.entrySet().removeIf(entry -> comUniCodes.contains(entry.getKey()));
        comUniCodes.addAll(secuYieldSpreadMap.keySet());
        yieldSpreadList.addAll(this.toYieldSpread(secuYieldSpreadMap, spreadDate, ComYieldSpreadSectorEnum.SECU));
        // indu
        induYieldSpreadMap.entrySet().removeIf(entry -> comUniCodes.contains(entry.getKey()));
        yieldSpreadList.addAll(this.toYieldSpread(induYieldSpreadMap, spreadDate, ComYieldSpreadSectorEnum.INDU));
        int effectRows = 0;
        List<List<ComYieldSpreadOneYearDO>> partitionList = ListUtils.partition(yieldSpreadList, BATCH_SIZE);
        for (List<ComYieldSpreadOneYearDO> comYieldSpreadOneYearDOS : partitionList) {
            effectRows += comYieldSpreadOneYearDAO.saveComYieldSpreadDOList(spreadDate, comYieldSpreadOneYearDOS);
        }
        return effectRows;
    }

    private List<ComYieldSpreadOneYearDO> toYieldSpread(Map<Long, ComYieldSpreadShortBO> udicYieldSpreadMap, Date spreadDate,
                                                        ComYieldSpreadSectorEnum comYieldSpreadSectorEnum) {
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        List<ComYieldSpreadOneYearDO> comYieldSpreadList = Lists.newArrayListWithExpectedSize(udicYieldSpreadMap.size());
        for (ComYieldSpreadShortBO comYieldSpreadShortBO : udicYieldSpreadMap.values()) {
            ComYieldSpreadOneYearDO comYieldSpreadOneYearDO = BeanCopyUtils.copyProperties(comYieldSpreadShortBO, ComYieldSpreadOneYearDO.class);
            comYieldSpreadOneYearDO.setId(redisService.generatePk(YIELD_SPREAD_COM_YIELD_SPREAD_FLOW_ID, spreadDate));
            comYieldSpreadOneYearDO.setComSpreadSector(comYieldSpreadSectorEnum.getValue());
            comYieldSpreadOneYearDO.setCreateTime(now);
            comYieldSpreadOneYearDO.setUpdateTime(now);
            comYieldSpreadOneYearDO.setDeleted(Deleted.NO_DELETED.getValue());
            comYieldSpreadList.add(comYieldSpreadOneYearDO);
        }
        return comYieldSpreadList;
    }

    @Override
    public List<ComCreditSpreadDTO> listComCreditSpreads(Date startDate, Date endDate, Set<Long> comUniCodeList) {
        if (CollectionUtils.isEmpty(comUniCodeList)) {
            return Collections.emptyList();
        }
        List<ComCreditSpreadBO> comYieldSpreadList = comYieldSpreadOneYearDAO.listComCreditSpreads(startDate, endDate, comUniCodeList);
        return BeanCopyUtils.copyList(comYieldSpreadList, ComCreditSpreadDTO.class);
    }

    @Override
    public int clearComYieldSpread() {
        LocalDate clearDate = LocalDate.now().minusDays(1).minusYears(1);
        return comYieldSpreadOneYearDAO.clearComYieldSpread(Date.valueOf(clearDate));
    }
}
