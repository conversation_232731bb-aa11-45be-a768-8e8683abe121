package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadChangeDO;

/**
 * 地方债区域利差历变动 Mapper
 *
 * <AUTHOR>
 * @create: 2024-11-06
 */
public interface PgLgBondYieldSpreadChangeMapper extends DynamicQueryMapper<PgLgBondYieldSpreadChangeDO> {
}
