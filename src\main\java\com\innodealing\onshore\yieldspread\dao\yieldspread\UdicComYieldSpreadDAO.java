package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.QueryHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.UdicComYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.UdicComYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.bo.*;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadQueryParameter;
import com.innodealing.onshore.yieldspread.model.dto.UdicYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.ComYieldSpreadChangeDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.UdicComYieldSpreadGroupMaxDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import com.innodealing.onshore.yieldspread.model.view.UdicComYieldSpreadView;
import com.innodealing.onshore.yieldspread.router.annotation.DynamicTableNameParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.github.wz2cool.dynamic.helper.CommonsHelper.getPropertyName;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.SPREAD_KEEP_SCALE;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE;

/**
 * 城投主体利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class UdicComYieldSpreadDAO {

    @Resource
    private UdicComYieldSpreadMapper udicComYieldSpreadMapper;

    @Resource
    private UdicComYieldSpreadGroupMapper udicComYieldSpreadGroupMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    private final QueryHelper queryHelper = new QueryHelper();

    private static final Set<String> CHANGE_ORDER_PROPERTY_SET;

    static {
        CHANGE_ORDER_PROPERTY_SET = new HashSet<>();
        CHANGE_ORDER_PROPERTY_SET.add(getPropertyName(ComYieldSpreadChangeDO::getCreditSpreadChange3M));
        CHANGE_ORDER_PROPERTY_SET.add(getPropertyName(ComYieldSpreadChangeDO::getCreditSpreadChange6M));
        CHANGE_ORDER_PROPERTY_SET.add(getPropertyName(ComYieldSpreadChangeDO::getExcessSpreadChange3M));
        CHANGE_ORDER_PROPERTY_SET.add(getPropertyName(ComYieldSpreadChangeDO::getExcessSpreadChange6M));
    }

    /**
     * 查询城投主体利差
     *
     * @param spreadDate     利差日期
     * @param comUniCodeList 主体列表
     * @return ComYieldSpreadChangeBO
     */
    public List<ComYieldSpreadChangeBO> listUdicComYieldSpreads(Date spreadDate, Set<Long> comUniCodeList) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<UdicComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                .select(UdicComYieldSpreadDO::getComUniCode, UdicComYieldSpreadDO::getSpreadDate,
                        UdicComYieldSpreadDO::getComCreditSpread, UdicComYieldSpreadDO::getComExcessSpread)
                .and(UdicComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(CollectionUtils.isNotEmpty(comUniCodeList),
                        UdicComYieldSpreadDO::getComUniCode, in(comUniCodeList));
        return BeanCopyUtils.copyList(udicComYieldSpreadMapper.selectByDynamicQuery(groupQuery), ComYieldSpreadChangeBO.class);
    }

    /**
     * 获取城投主体利差某一天的 所有 主题
     *
     * @param spreadDate 利差日期
     * @return comUniCodeList 主体列表
     */
    public List<Long> listUdicComYieldSpreadComUniCodes(@NonNull Date spreadDate) {
        DynamicQuery<UdicComYieldSpreadDO> query = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                .select(UdicComYieldSpreadDO::getComUniCode)
                .and(UdicComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(UdicComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return udicComYieldSpreadMapper.selectByDynamicQuery(query).stream().map(UdicComYieldSpreadDO::getComUniCode).collect(Collectors.toList());
    }

    /**
     * 获取城投历史分位的统计数据
     *
     * @param startDate      时间范围 开始时间
     * @param endDate        时间范围结束时间
     * @param issueDate      利差日期
     * @param comUniCodeList 主体列表
     * @return 分位统计数据
     */
    public List<ComYieldSpreadQuantileViewDO> listUdicComYieldQuantileStatistics(@NonNull Date startDate, @NonNull Date endDate,
                                                                                 @NonNull Date issueDate, List<Long> comUniCodeList) {
        return udicComYieldSpreadMapper.listComYieldQuantileStatisticsViews(startDate, endDate, issueDate, comUniCodeList);
    }

    /**
     * 批量更新
     *
     * @param spreadDate               利差日期
     * @param udicComYieldSpreadDOList 城投主体利差列表
     * @return 受影响的行数
     */
    @DynamicTableNameParam(logicTableName = YieldSpreadConst.UDIC_COM_TABLE_NAME)
    public int saveUdicComYieldSpreadDOList(Date spreadDate, List<UdicComYieldSpreadDO> udicComYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(udicComYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> comUniCodes = udicComYieldSpreadDOList.stream().map(UdicComYieldSpreadDO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        AtomicInteger effectRows = new AtomicInteger();
        // 批量查询已存在的数据
        DynamicQuery<UdicComYieldSpreadDO> query = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                .select(UdicComYieldSpreadDO::getId, UdicComYieldSpreadDO::getComUniCode,
                        UdicComYieldSpreadDO::getSpreadDate, UdicComYieldSpreadDO::getProvinceUniCode,
                        UdicComYieldSpreadDO::getProvinceName, UdicComYieldSpreadDO::getCityUniCode,
                        UdicComYieldSpreadDO::getCityName, UdicComYieldSpreadDO::getDistrictUniCode,
                        UdicComYieldSpreadDO::getDistrictName, UdicComYieldSpreadDO::getAdministrativeRegion,
                        UdicComYieldSpreadDO::getActualControllerFullName, UdicComYieldSpreadDO::getAreaName)
                .and(UdicComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(UdicComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<UdicComYieldSpreadDO> existDataList = udicComYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(udicComYieldSpreadDOList));
        } else {
            Map<String, UdicComYieldSpreadDO> existUdicComYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                    x.getComUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<UdicComYieldSpreadDO> insertList = new ArrayList<>();
            List<UdicComYieldSpreadDO> updateList = new ArrayList<>();
            for (UdicComYieldSpreadDO udicComYieldSpreadDO : udicComYieldSpreadDOList) {
                UdicComYieldSpreadDO existUdicComYieldSpreadDO = existUdicComYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                udicComYieldSpreadDO.getComUniCode(), udicComYieldSpreadDO.getSpreadDate().getTime()));
                if (Objects.isNull(existUdicComYieldSpreadDO)) {
                    insertList.add(udicComYieldSpreadDO);
                } else {
                    udicComYieldSpreadDO.setId(existUdicComYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getProvinceUniCode(), udicComYieldSpreadDO::setProvinceUniCode);
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getProvinceName(), udicComYieldSpreadDO::setProvinceName);
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getCityUniCode(), udicComYieldSpreadDO::setCityUniCode);
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getCityName(), udicComYieldSpreadDO::setCityName);
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getDistrictName(), udicComYieldSpreadDO::setDistrictName);
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getDistrictUniCode(), udicComYieldSpreadDO::setDistrictUniCode);
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getAdministrativeRegion(),
                            udicComYieldSpreadDO::setAdministrativeRegion);
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getActualControllerFullName(),
                            udicComYieldSpreadDO::setActualControllerFullName);
                    ObjectExtensionUtils.ifNonNull(existUdicComYieldSpreadDO.getAreaName(), udicComYieldSpreadDO::setAreaName);

                    updateList.add(udicComYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 城投主体利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<UdicComYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<UdicComYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(UdicComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (UdicComYieldSpreadDO udicComYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<UdicComYieldSpreadDO> updateQuery = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                        .and(UdicComYieldSpreadDO::getId, isEqual(udicComYieldSpreadDO.getId()));
                mapper.updateByDynamicQuery(udicComYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 城投主体利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<UdicComYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<UdicComYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(UdicComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (UdicComYieldSpreadDO udicComYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(udicComYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 城投主体利差分页查询数据
     *
     * @param comUniCodes 发行人唯一编码集合
     * @param spreadDate  利差日期
     * @param pageNum     页码
     * @param pageSize    每页数据量
     * @param sort        排序
     * @return {@link NormPagingResult}<{@link UdicComYieldSpreadDO}> 分页查询城投主体利差数据响应集合
     */
    public NormPagingResult<UdicComYieldSpreadDO> getComYieldSpreadPaging(Set<Long> comUniCodes, Date spreadDate, Integer pageNum,
                                                                          Integer pageSize, SortDTO sort) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return new NormPagingResult<>();
        }
        NormPagingQuery<UdicComYieldSpreadDO> query = NormPagingQuery.createQuery(UdicComYieldSpreadDO.class, pageNum, pageSize, false, false)
                .and(UdicComYieldSpreadDO::getComUniCode, in(comUniCodes))
                .and(UdicComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(UdicComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        if (Objects.nonNull(sort)) {
            String columnName = queryHelper.getQueryColumnByProperty(UdicComYieldSpreadDO.class, sort.getPropertyName());
            query.addSorts(new CustomSortDescriptor(columnName + " IS NULL"));
            query.addSorts(new SortDescriptor(sort.getPropertyName(), sort.getSortDirection()));
        }
        return udicComYieldSpreadMapper.selectByNormalPaging(query);
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<UdicComYieldSpreadDO> query = DynamicQuery.createQuery(UdicComYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt = udicComYieldSpreadMapper.selectMaxByDynamicQuery(UdicComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 从主库获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDateForMaster() {
        DynamicQuery<UdicComYieldSpreadDO> query = DynamicQuery.createQuery(UdicComYieldSpreadDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER);
        Optional<java.util.Date> dateOpt = udicComYieldSpreadMapper.selectMaxByDynamicQuery(UdicComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 城投主体利差分页查询数据
     *
     * @param request 城投主体利差分页查询数据请求参数
     * @return {@link NormPagingResult}<{@link UdicComYieldSpreadDO}> 城投主体利差分页查询响应数据
     */
    public List<UdicComYieldSpreadDO> getComYieldSpreadPagingByJoin(UdicBondYieldSpreadParamDTO request) {
        UdicBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(request);
        return udicComYieldSpreadMapper.getComYieldSpreadPagingByJoin(parameter);
    }

    /**
     * 城投主体利差分页查询数据-包含变动数据
     *
     * @param request 城投主体利差分页查询数据请求参数
     * @return {@link NormPagingResult}<{@link UdicComYieldSpreadDO}> 城投主体利差分页查询响应数据
     */
    public List<UdicComYieldSpreadView> getComYieldSpreadChangePagingByJoin(UdicBondYieldSpreadParamDTO request) {
        UdicBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(request);
        return udicComYieldSpreadMapper.getComYieldSpreadChangePagingByJoin(parameter);
    }

    private UdicBondYieldSpreadQueryParameter buildPagingQueryParameter(UdicBondYieldSpreadParamDTO request) {
        SortDTO sort = request.getSort();
        String propertyName;
        UdicBondYieldSpreadQueryParameter clone = BeanCopyUtils.copyProperties(request, UdicBondYieldSpreadQueryParameter.class);
        if (Objects.nonNull(sort)) {
            if (CHANGE_ORDER_PROPERTY_SET.contains(sort.getPropertyName())) {
                propertyName = queryHelper.getQueryColumnByProperty(ComYieldSpreadChangeDO.class, sort.getPropertyName());
            } else {
                propertyName = queryHelper.getQueryColumnByProperty(UdicComYieldSpreadDO.class, sort.getPropertyName());
            }
            clone.setPropertyName(propertyName);
            clone.setSortDirection(sort.getSortDirection().name());
        }
        Integer startIndex = (request.getPageNum() - 1) * request.getPageSize();
        clone.setStartIndex(startIndex);
        clone.setYear(request.getSpreadDate().toLocalDate().getYear());
        return clone;
    }

    /**
     * 城投主体利差分页查询数据总条数
     *
     * @param query 城投主体利差分页查询数据总条数请求参数
     * @return {@link Long} 总条数
     */
    public Long getComYieldSpreadPagingCount(UdicBondYieldSpreadParamDTO query) {
        UdicBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(query);
        return udicComYieldSpreadMapper.getComYieldSpreadPagingCount(parameter);
    }

    /**
     * 城投主体利差分页查询数据
     *
     * @param request 城投主体利差分页查询数据请求参数
     * @return {@link NormPagingResult}<{@link UdicComYieldSpreadDO}> 城投主体利差分页查询响应数据
     */
    public NormPagingResult<UdicComYieldSpreadDO> getComYieldSpreadPagingByExists(UdicBondYieldSpreadParamDTO request) {
        UdicBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(request);
        List<UdicComYieldSpreadDO> comYieldSpreadList = udicComYieldSpreadMapper.getComYieldSpreadPagingByExists(parameter);
        NormPagingResult<UdicComYieldSpreadDO> pagingResult = new NormPagingResult<>();
        pagingResult.setPageNum(parameter.getPageNum());
        pagingResult.setPageSize(parameter.getPageSize());
        pagingResult.setList(comYieldSpreadList);
        return pagingResult;
    }

    /**
     * 城投主体利差分页查询数据-包含变动数据
     *
     * @param request 城投主体利差分页查询数据请求参数
     * @return {@link NormPagingResult}<{@link UdicComYieldSpreadDO}> 城投主体利差分页查询响应数据
     */
    public NormPagingResult<UdicComYieldSpreadView> getComYieldSpreadChangePagingByExists(UdicBondYieldSpreadParamDTO request) {
        UdicBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(request);
        List<UdicComYieldSpreadView> comYieldSpreadList = udicComYieldSpreadMapper.getComYieldSpreadChangePagingByExists(parameter);
        NormPagingResult<UdicComYieldSpreadView> pagingResult = new NormPagingResult<>();
        pagingResult.setPageNum(parameter.getPageNum());
        pagingResult.setPageSize(parameter.getPageSize());
        pagingResult.setList(comYieldSpreadList);
        return pagingResult;
    }

    /**
     * 查询并计算利差曲线数据-主体利差方式
     *
     * @param comUniCode      发行人唯一编码
     * @param spreadBondType  债券类型, 根据不同类型，查询不同字段，并不是全部返回
     * @param startSpreadDate 利差开始日期
     * @param endSpreadDate   利差结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 债券每天中位数数据集合
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurves(@NonNull Long comUniCode, Integer spreadBondType,
                                                              @NonNull Date startSpreadDate, @NonNull Date endSpreadDate) {
        if (!ObjectExtensionUtils.isAllNotEmpty(comUniCode, startSpreadDate, endSpreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<UdicComYieldSpreadDO> query = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                .select(UdicComYieldSpreadDO::getSpreadDate, UdicComYieldSpreadDO::getComUniCode)
                .and(UdicComYieldSpreadDO::getComUniCode, isEqual(comUniCode))
                .and(UdicComYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startSpreadDate))
                .and(UdicComYieldSpreadDO::getSpreadDate, lessThanOrEqual(endSpreadDate));
        Optional<SpreadBondTypeEnum> spreadBondTypeEnumOpt = EnumUtils.getEnumByValue(spreadBondType, SpreadBondTypeEnum.class);
        if (!spreadBondTypeEnumOpt.isPresent()) {
            query.addSelectedProperties(getPropertyName(UdicComYieldSpreadDO::getComCreditSpread),
                    getPropertyName(UdicComYieldSpreadDO::getComExcessSpread), getPropertyName(UdicComYieldSpreadDO::getComCbYield));
            List<UdicComYieldSpreadDO> comYieldSpreadList = udicComYieldSpreadMapper.selectByDynamicQuery(query);
            return comYieldSpreadList.stream().map(com -> {
                BondYieldSpreadCurveBO bondYieldSpreadCurveBO = new BondYieldSpreadCurveBO();
                bondYieldSpreadCurveBO.setSpreadDate(com.getSpreadDate());
                handlePrecision(bondYieldSpreadCurveBO, com.getComCreditSpread(), com.getComExcessSpread(), com.getComCbYield());
                return bondYieldSpreadCurveBO;
            }).collect(Collectors.toList());
        }
        SpreadBondTypeEnum spreadBondTypeEnum = spreadBondTypeEnumOpt.get();
        if (SpreadBondTypeEnum.SPREAD_NOT_PUBLIC_OFFERING.equals(spreadBondTypeEnum)) {
            query.addSelectedProperties(getPropertyName(UdicComYieldSpreadDO::getComPrivateCreditSpread),
                    getPropertyName(UdicComYieldSpreadDO::getComPrivateExcessSpread), getPropertyName(UdicComYieldSpreadDO::getComPrivateCbYield));
            List<UdicComYieldSpreadDO> comYieldSpreadList = udicComYieldSpreadMapper.selectByDynamicQuery(query);
            return comYieldSpreadList.stream().map(com -> {
                BondYieldSpreadCurveBO bondYieldSpreadCurveBO = new BondYieldSpreadCurveBO();
                bondYieldSpreadCurveBO.setSpreadDate(com.getSpreadDate());
                handlePrecision(bondYieldSpreadCurveBO, com.getComPrivateCreditSpread(), com.getComPrivateExcessSpread(), com.getComPrivateCbYield());
                return bondYieldSpreadCurveBO;
            }).collect(Collectors.toList());
        }
        if (SpreadBondTypeEnum.SPREAD_IS_PUBLIC_OFFERING.equals(spreadBondTypeEnum)) {
            query.addSelectedProperties(getPropertyName(UdicComYieldSpreadDO::getComPublicCreditSpread),
                    getPropertyName(UdicComYieldSpreadDO::getComPublicExcessSpread), getPropertyName(UdicComYieldSpreadDO::getComPublicCbYield));
            List<UdicComYieldSpreadDO> comYieldSpreadList = udicComYieldSpreadMapper.selectByDynamicQuery(query);
            return comYieldSpreadList.stream().map(com -> {
                BondYieldSpreadCurveBO bondYieldSpreadCurveBO = new BondYieldSpreadCurveBO();
                bondYieldSpreadCurveBO.setSpreadDate(com.getSpreadDate());
                handlePrecision(bondYieldSpreadCurveBO, com.getComPublicCreditSpread(), com.getComPublicExcessSpread(), com.getComPublicCbYield());
                return bondYieldSpreadCurveBO;
            }).collect(Collectors.toList());
        }
        query.addSelectedProperties(getPropertyName(UdicComYieldSpreadDO::getComPerpetualCreditSpread),
                getPropertyName(UdicComYieldSpreadDO::getComPerpetualExcessSpread), getPropertyName(UdicComYieldSpreadDO::getComPerpetualCbYield));
        List<UdicComYieldSpreadDO> comYieldSpreadList = udicComYieldSpreadMapper.selectByDynamicQuery(query);
        return comYieldSpreadList.stream().map(com -> {
            BondYieldSpreadCurveBO bondYieldSpreadCurveBO = new BondYieldSpreadCurveBO();
            bondYieldSpreadCurveBO.setSpreadDate(com.getSpreadDate());
            handlePrecision(bondYieldSpreadCurveBO, com.getComPerpetualCreditSpread(), com.getComPerpetualExcessSpread(), com.getComPerpetualCbYield());
            return bondYieldSpreadCurveBO;
        }).collect(Collectors.toList());
    }

    private void handlePrecision(BondYieldSpreadCurveBO bondYieldSpreadCurveBO, BigDecimal comCreditSpread, BigDecimal comExcessSpread, BigDecimal comCbYield) {
        BigDecimalUtils.handlerPrecision(comCreditSpread, SPREAD_KEEP_SCALE).ifPresent(bondYieldSpreadCurveBO::setBondCreditSpread);
        BigDecimalUtils.handlerPrecision(comExcessSpread, SPREAD_KEEP_SCALE).ifPresent(bondYieldSpreadCurveBO::setBondExcessSpread);
        BigDecimalUtils.handlerPrecision(comCbYield, YIELD_SPREAD_KEEP_SCALE).ifPresent(bondYieldSpreadCurveBO::setCbYield);
    }

    /**
     * 查询城投主体利差数据集
     *
     * @param spreadDate 利差日期
     * @return {@link List}<{@link ComYieldSpreadShortBO}> 城投主体利差响应数据集
     */
    public List<ComYieldSpreadShortBO> listShortInfosBySpreadDate(Date spreadDate) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<UdicComYieldSpreadDO> query = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                .select(UdicComYieldSpreadDO::getSpreadDate,
                        UdicComYieldSpreadDO::getComUniCode,
                        UdicComYieldSpreadDO::getComCreditSpread,
                        UdicComYieldSpreadDO::getComExcessSpread,
                        UdicComYieldSpreadDO::getComCbYield)
                .and(UdicComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(UdicComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return BeanCopyUtils.copyList(udicComYieldSpreadMapper.selectByDynamicQuery(query), ComYieldSpreadShortBO.class);
    }

    /**
     * 获取主体利差
     *
     * @param isNewest 是否为最新一天
     * @param param    请求参数
     * @return 主体利差
     */
    public List<UdicComYieldSpreadView> listComYieldSpreads(boolean isNewest, UdicYieldSearchParam param) {
        return udicComYieldSpreadMapper.listComYieldSpreads(isNewest, param);
    }

    /**
     * 获取主体数量
     *
     * @param param 请求参数
     * @return 主体数量
     */
    public Long countComYieldSpread(UdicYieldSearchParam param) {
        return udicComYieldSpreadMapper.countComYieldSpread(param);
    }

    /**
     * 查询城投主体利差
     *
     * @param spreadDate     利差日期
     * @param comUniCodeList 主体列表
     * @return UdicComYieldSpreadDO
     */
    public List<UdicComYieldSpreadDO> listComYieldSpreads(@NonNull Date spreadDate, @NonNull Set<Long> comUniCodeList) {
        DynamicQuery<UdicComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                .and(UdicComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(UdicComYieldSpreadDO::getComUniCode, in(comUniCodeList));
        return BeanCopyUtils.copyList(udicComYieldSpreadMapper.selectByDynamicQuery(groupQuery), UdicComYieldSpreadDO.class);
    }

    /**
     * 查询主体最新利差
     *
     * @param comUniCodeList 主体列表
     * @return ComYieldSpreadChangeBO
     */
    public List<ComCreditSpreadBO> listComCreditSpreads(Set<Long> comUniCodeList) {
        if (CollectionUtils.isEmpty(comUniCodeList)) {
            return Collections.emptyList();
        }
        GroupedQuery<UdicComYieldSpreadDO, UdicComYieldSpreadGroupMaxDO> groupedQuery = GroupByQuery.createQuery(UdicComYieldSpreadDO.class, UdicComYieldSpreadGroupMaxDO.class)
                .select(UdicComYieldSpreadGroupMaxDO::getMaxSpreadDate, UdicComYieldSpreadGroupMaxDO::getComUniCode)
                .and(UdicComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                .and(UdicComYieldSpreadDO::getComUniCode, in(comUniCodeList))
                .groupBy(UdicComYieldSpreadDO::getComUniCode);

        List<UdicComYieldSpreadGroupMaxDO> udicComYieldSpreadGroupMaxDOList = udicComYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(udicComYieldSpreadGroupMaxDOList)) {
            return Collections.emptyList();
        }
        DynamicQuery<UdicComYieldSpreadDO> query = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                .select(UdicComYieldSpreadDO::getSpreadDate,
                        UdicComYieldSpreadDO::getComUniCode,
                        UdicComYieldSpreadDO::getComCreditSpread)
                .and(UdicComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        getComYieldSpreadMaxFilterGroup(udicComYieldSpreadGroupMaxDOList).ifPresent(query::addFilters);
        return BeanCopyUtils.copyList(udicComYieldSpreadMapper.selectByDynamicQuery(query), ComCreditSpreadBO.class);
    }

    /**
     * 主体 利差日期筛选
     *
     * @param udicComYieldSpreadGroupMaxDOS 筛选BO请求参数
     * @return 主体 利差日期筛选
     */
    private Optional<FilterGroupDescriptor<UdicComYieldSpreadDO>> getComYieldSpreadMaxFilterGroup(List<UdicComYieldSpreadGroupMaxDO> udicComYieldSpreadGroupMaxDOS) {
        if (CollectionUtils.isEmpty(udicComYieldSpreadGroupMaxDOS)) {
            return Optional.empty();
        }
        FilterGroupDescriptor<UdicComYieldSpreadDO> filterGroup = new FilterGroupDescriptor<>();
        filterGroup.setCondition(FilterCondition.AND);
        for (UdicComYieldSpreadGroupMaxDO udicComYieldSpreadGroupMaxDO : udicComYieldSpreadGroupMaxDOS) {
            FilterGroupDescriptor<UdicComYieldSpreadDO> currentfilterGroup = new FilterGroupDescriptor<>();
            currentfilterGroup.setCondition(FilterCondition.OR);
            currentfilterGroup.and(UdicComYieldSpreadDO::getComUniCode, isEqual(udicComYieldSpreadGroupMaxDO.getComUniCode()))
                    .and(UdicComYieldSpreadDO::getSpreadDate, isEqual(udicComYieldSpreadGroupMaxDO.getMaxSpreadDate()));
            filterGroup.addFilters(currentfilterGroup);
        }
        return Optional.of(filterGroup);
    }

    /**
     * 获取主体利差
     *
     * @param comUniCodes 主体列表
     * @return 主体利差列表
     */
    public List<MixYieldSpreadShortBO> listAllYieldSpreads(List<Long> comUniCodes) {
        if (!ObjectExtensionUtils.isAllNotEmpty(comUniCodes)) {
            return Collections.emptyList();
        }
        DynamicQuery<UdicComYieldSpreadDO> query = DynamicQuery.createQuery(UdicComYieldSpreadDO.class)
                .select(UdicComYieldSpreadDO::getSpreadDate, UdicComYieldSpreadDO::getComUniCode,
                        UdicComYieldSpreadDO::getComCreditSpread, UdicComYieldSpreadDO::getComExcessSpread,
                        UdicComYieldSpreadDO::getComPrivateCreditSpread, UdicComYieldSpreadDO::getComPrivateExcessSpread,
                        UdicComYieldSpreadDO::getComPublicCreditSpread, UdicComYieldSpreadDO::getComPublicExcessSpread,
                        UdicComYieldSpreadDO::getComPerpetualCreditSpread, UdicComYieldSpreadDO::getComPerpetualExcessSpread)
                .and(UdicComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<UdicComYieldSpreadDO> comYieldSpreadList = udicComYieldSpreadMapper.selectByDynamicQuery(query);
        return comYieldSpreadList.stream().map(com -> {
            MixYieldSpreadShortBO mixYieldSpreadShortBO = new MixYieldSpreadShortBO();
            mixYieldSpreadShortBO.setComUniCode(com.getComUniCode());
            mixYieldSpreadShortBO.setSpreadDate(com.getSpreadDate());
            handlePrecision(mixYieldSpreadShortBO, com);
            return mixYieldSpreadShortBO;
        }).collect(Collectors.toList());
    }

    private void handlePrecision(MixYieldSpreadShortBO mixYieldSpreadShortBO, UdicComYieldSpreadDO comYieldSpreadDO) {
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setComCreditSpread);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setComExcessSpread);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPublicCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldOne);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPublicExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldOne);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPrivateCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldTwo);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPrivateExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldTwo);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPerpetualCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldTree);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPerpetualExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldTree);
    }
}
