package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.model.dto.request.CustomCurveGenerateReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.service.CustomComYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.CustomCurveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * (内部)自定义曲线
 *
 * <AUTHOR>
 */
@Api(tags = "(内部)自定义曲线")
@RestController
@RequestMapping("internal/custom-curve")
@Validated
public class InternalCustomCurveController {

    @Resource
    private CustomCurveService customCurveService;

    @Resource
    private CustomComYieldSpreadService customComYieldSpreadService;

    @ApiOperation(value = "曲线名称是否重复")
    @GetMapping("curve-name/is-duplicate")
    public Boolean curveNameIsDuplicate(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveName", value = "曲线名称")
            @NotBlank(message = "曲线名称不能为空") @Length(message = "自定义曲线名称不能超过 {max} 个字符", max = YieldSpreadConst.CUSTOM_CURVE_NAME_MAX_LENGTH)
            @RequestParam String curveName) {
        return customCurveService.curveNameIsDuplicate(userid, curveName);
    }

    @ApiOperation(value = "解析债券")
    @PostMapping("analysis-bonds")
    public BondAnalysisResDTO analysisBonds(
            @RequestBody
            @NotEmpty(message = "导入的债券集合不能为空")
            @Size(message = "单条曲线最多能上传 {max} 只债券，请调整后再上传", max = YieldSpreadConst.IMPORT_BOND_SIZE_UPPER_LIMIT)
            Set<String> bondNameOrCodeList) {
        return customCurveService.analysisBonds(bondNameOrCodeList);
    }

    @ApiOperation(value = "保存曲线")
    @PostMapping("custom-curve-save")
    public Boolean saveCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated CustomCurveGenerateReqDTO customCurveGenerateReqDTO) {
        return customCurveService.saveCurve(userid, curveGroupId, customCurveGenerateReqDTO);
    }

    @ApiOperation(value = "获取自定义曲线列表")
    @GetMapping("custom-curve-list")
    public List<CustomCurveBasicInfoResDTO> listCustomCurves(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "generateStatus", value = "自定义曲线生成状态：待生成=1，生成中=2，已生成=3，生成失败=4")
            @RequestParam(name = "generateStatus", required = false) Set<Integer> generateStatus) {
        return customCurveService.listCustomCurves(userid, generateStatus);
    }

    @ApiOperation(value = "重新生成，生成失败后可重新生成")
    @PostMapping("custom-curve-regenerate")
    public Boolean regenerate(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam(name = "curveId") Long curveId) {
        return customCurveService.regenerate(userid, curveId);
    }

    @ApiOperation(value = "取消生成曲线")
    @GetMapping("cancel-generate")
    public Boolean cancelGenerate(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam Long curveId) {
        return customCurveService.cancelGenerate(userid, curveId);
    }

    @ApiOperation(value = "更新曲线")
    @PostMapping("custom-curve-update")
    public Boolean updateCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Valid CustomCurveGenerateReqDTO generateReqDTO) {
        return customCurveService.update(userid, curveId, generateReqDTO);
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public List<CustomSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return customCurveService.pagingSingleBondYieldSpreads(userid, request);
    }

    @PostMapping("/count-single-bond")
    @ApiOperation(value = "表区-单券利差条数")
    public Long countSingleBondYieldSpread(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return customCurveService.countSingleBondYieldSpread(userid, request);
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差  分页")
    public List<CustomComYieldSpreadResDTO> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return customComYieldSpreadService.listComYieldSpreads(userid, request);
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public Long countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return customComYieldSpreadService.countComYieldSpread(userid, request);
    }

    @ApiOperation(value = "生成情况")
    @GetMapping("/generate-detail")
    public GenerateDetailResDTO generateDetail() {
        return customCurveService.generateDetail();
    }

    @ApiOperation(value = "加减令牌数")
    @GetMapping("/add-permits")
    public void addPermits(@ApiParam(name = "permitCount", value = "令牌数") @RequestParam Integer permitCount) {
        customCurveService.addPermits(permitCount);
    }

    @ApiOperation(value = "设置总令牌数")
    @GetMapping("/set-permits")
    public void setPermits(@ApiParam(name = "totalCount", value = "总令牌数") @RequestParam Integer permitCount) {
        customCurveService.setPermits(permitCount);
    }

    @ApiOperation(value = "生成昨日曲线数据")
    @GetMapping("/generate-yesterday-curve-data")
    public void generateYesterdayCurveData() {
        customCurveService.generateYesterdayCurveData();
    }

    @ApiOperation(value = "生成指定日期曲线数据,不传默认昨日")
    @GetMapping("/generate-curve-data")
    public void generateCurveData(@RequestParam(required = false) Date spreadDate) {
        customCurveService.generateCurveData(spreadDate);
    }

}
