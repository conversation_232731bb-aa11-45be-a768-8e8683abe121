package com.innodealing.onshore.yieldspread.dao.pgyieldspread.repository;

import com.github.wz2cool.dynamic.BaseFilterDescriptor;
import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.innodealing.onshore.yieldspread.dao.pgshard.partition.InduBondShardYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveShardEnum;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgInduBondYieldSpreadCurveAllMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu1Mapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu2Mapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgInduBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.InduYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.BasePgInduBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveAllDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu1DO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu2DO;
import com.innodealing.onshore.yieldspread.router.annotation.ShardYieldSpreadCurve;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 产业利差曲线数据源
 *
 * <AUTHOR>
 */
@Repository
@SuppressWarnings({"squid:S1452"})
public class PgInduBondYieldSpreadCurveRepository implements PgInduBondYieldSpreadCurveMapper {

    @Resource
    private PgInduBondYieldSpreadCurveAllMapper pgInduBondYieldSpreadCurveAllMapper;

    @Resource
    private PgInduBondYieldSpreadCurveIndu1Mapper pgInduBondYieldSpreadCurveIndu1Mapper;

    @Resource
    private PgInduBondYieldSpreadCurveIndu2Mapper pgInduBondYieldSpreadCurveIndu2Mapper;

    @Resource
    private PgInduBondYieldSpreadCurveMapper pgInduBondYieldSpreadCurveMapper;

    /**
     * 同步表
     */
    public void syncCurveIncrFromMV() {
        pgInduBondYieldSpreadCurveAllMapper.syncCurveIncrFromMV();
        pgInduBondYieldSpreadCurveIndu1Mapper.syncCurveIncrFromMV();
        pgInduBondYieldSpreadCurveIndu2Mapper.syncCurveIncrFromMV();
    }

    /**
     * 刷新物化视图
     */
    public void refreshMvInduBondYieldSpreadCurveFromMV() {
        pgInduBondYieldSpreadCurveAllMapper.refreshMvInduBondYieldSpreadCurveFromMV();
        pgInduBondYieldSpreadCurveIndu1Mapper.refreshMvInduBondYieldSpreadCurveFromMV();
        pgInduBondYieldSpreadCurveIndu2Mapper.refreshMvInduBondYieldSpreadCurveFromMV();
    }

    /**
     * 查询产业利差
     *
     * @param searchParameter searchParameter
     * @return list
     */
    @ShardYieldSpreadCurve(type = InduBondShardYieldSpreadCurveRepository.class, method = "listInduShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.INDU_LEVEL_1)
    public List<PgInduBondYieldSpreadCurveIndu1DO> pgListYieldSpreadPanoramasForIndu1s(InduBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgInduBondYieldSpreadCurveIndu1DO>[] baseFilterDescriptors = this.listFilters(searchParameter, PgInduBondYieldSpreadCurveIndu1DO.class).getFilters();
        DynamicQuery<PgInduBondYieldSpreadCurveIndu1DO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadCurveIndu1DO.class)
                .select(PgInduBondYieldSpreadCurveIndu1DO::getSpreadDate,
                        PgInduBondYieldSpreadCurveIndu1DO::getInduLevel1Code,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondCreditSpread,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondExcessSpread,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondCreditSpreadCount,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondExcessSpreadCount)
                .and(baseFilterDescriptors);
        return pgInduBondYieldSpreadCurveIndu1Mapper.selectByDynamicQuery(query);
    }

    /**
     * 查询产业利差
     *
     * @param searchParameter searchParameter
     * @param indu2Codes      产业2code
     * @return list
     */
    @ShardYieldSpreadCurve(type = InduBondShardYieldSpreadCurveRepository.class, method = "listInduShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.INDU_LEVEL_2)
    public List<PgInduBondYieldSpreadCurveIndu2DO> pgListYieldSpreadPanoramasForIndu2s(InduBondYieldSpreadParamDTO searchParameter, Long[] indu2Codes) {
        FilterGroupDescriptor<PgInduBondYieldSpreadCurveIndu2DO> descriptor = this.listFilters(searchParameter, PgInduBondYieldSpreadCurveIndu2DO.class);
        BaseFilterDescriptor<PgInduBondYieldSpreadCurveIndu2DO>[] baseFilterDescriptors =
                ArrayUtils.isNotEmpty(indu2Codes) ? descriptor.and(PgInduBondYieldSpreadCurveIndu2DO::getInduLevel2Code, in(indu2Codes)).getFilters() : descriptor.getFilters();
        DynamicQuery<PgInduBondYieldSpreadCurveIndu2DO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadCurveIndu2DO.class)
                .select(PgInduBondYieldSpreadCurveIndu2DO::getSpreadDate,
                        PgInduBondYieldSpreadCurveIndu2DO::getInduLevel2Code,
                        PgInduBondYieldSpreadCurveIndu2DO::getBondCreditSpread,
                        PgInduBondYieldSpreadCurveIndu2DO::getBondExcessSpread,
                        PgInduBondYieldSpreadCurveIndu2DO::getBondCreditSpreadCount,
                        PgInduBondYieldSpreadCurveIndu2DO::getBondExcessSpreadCount)
                .and(baseFilterDescriptors);
        return pgInduBondYieldSpreadCurveIndu2Mapper.selectByDynamicQuery(query);
    }

    /**
     * 查询产业利差
     *
     * @param searchParameter searchParameter
     * @return list
     */
    @ShardYieldSpreadCurve(type = InduBondShardYieldSpreadCurveRepository.class, method = "listInduShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.INDU_LEVEL_2)
    public List<? extends BasePgInduBondYieldSpreadCurveDO> listYieldSpreadCurvesForIndu2s(InduBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgInduBondYieldSpreadCurveIndu2DO>[] baseFilterDescriptors = this.listFilters(searchParameter, PgInduBondYieldSpreadCurveIndu2DO.class)
                .and(PgInduBondYieldSpreadCurveIndu2DO::getInduLevel2Code, isEqual(searchParameter.getIndustryCode2())).getFilters();
        DynamicQuery<PgInduBondYieldSpreadCurveIndu2DO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadCurveIndu2DO.class)
                .select(PgInduBondYieldSpreadCurveIndu2DO::getSpreadDate,
                        PgInduBondYieldSpreadCurveIndu2DO::getBondCreditSpread,
                        PgInduBondYieldSpreadCurveIndu2DO::getBondExcessSpread,
                        PgInduBondYieldSpreadCurveIndu2DO::getCbYield,
                        PgInduBondYieldSpreadCurveIndu2DO::getBondCreditSpreadCount,
                        PgInduBondYieldSpreadCurveIndu2DO::getBondExcessSpreadCount,
                        PgInduBondYieldSpreadCurveIndu2DO::getCbYieldCount)
                .and(baseFilterDescriptors);
        return pgInduBondYieldSpreadCurveIndu2Mapper.selectByDynamicQuery(query);
    }

    /**
     * 查询产业利差
     *
     * @param searchParameter searchParameter
     * @return list
     */
    @ShardYieldSpreadCurve(type = InduBondShardYieldSpreadCurveRepository.class, method = "listInduShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.INDU_LEVEL_1)
    public List<? extends BasePgInduBondYieldSpreadCurveDO> listYieldSpreadCurvesForIndu1s(InduBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgInduBondYieldSpreadCurveIndu1DO>[] baseFilterDescriptors = this.listFilters(searchParameter, PgInduBondYieldSpreadCurveIndu1DO.class)
                .and(PgInduBondYieldSpreadCurveIndu1DO::getInduLevel1Code, isEqual(searchParameter.getIndustryCode1())).getFilters();
        DynamicQuery<PgInduBondYieldSpreadCurveIndu1DO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadCurveIndu1DO.class)
                .select(PgInduBondYieldSpreadCurveIndu1DO::getSpreadDate,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondCreditSpread,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondExcessSpread,
                        PgInduBondYieldSpreadCurveIndu1DO::getCbYield,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondCreditSpreadCount,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondExcessSpreadCount,
                        PgInduBondYieldSpreadCurveIndu1DO::getCbYieldCount)
                .and(baseFilterDescriptors);
        return pgInduBondYieldSpreadCurveIndu1Mapper.selectByDynamicQuery(query);
    }

    /**
     * 查询产业利差
     *
     * @param searchParameter searchParameter
     * @return list
     */
    @ShardYieldSpreadCurve(type = InduBondShardYieldSpreadCurveRepository.class, method = "listInduShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.ALL)
    public List<? extends BasePgInduBondYieldSpreadCurveDO> listYieldSpreadCurvesForAlls(InduBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgInduBondYieldSpreadCurveAllDO>[] baseFilterDescriptors = listFilters(searchParameter, PgInduBondYieldSpreadCurveAllDO.class).getFilters();
        DynamicQuery<PgInduBondYieldSpreadCurveAllDO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadCurveAllDO.class)
                .select(PgInduBondYieldSpreadCurveAllDO::getSpreadDate,
                        PgInduBondYieldSpreadCurveAllDO::getBondCreditSpread,
                        PgInduBondYieldSpreadCurveAllDO::getBondExcessSpread,
                        PgInduBondYieldSpreadCurveAllDO::getCbYield,
                        PgInduBondYieldSpreadCurveAllDO::getBondCreditSpreadCount,
                        PgInduBondYieldSpreadCurveAllDO::getBondExcessSpreadCount,
                        PgInduBondYieldSpreadCurveAllDO::getCbYieldCount).and(baseFilterDescriptors);
        return pgInduBondYieldSpreadCurveAllMapper.selectByDynamicQuery(query);
    }

    private <T extends BasePgInduBondYieldSpreadCurveDO> FilterGroupDescriptor<T> listFilters(InduBondYieldSpreadParamDTO request, Class<T> clazz) {
        return FilterGroupDescriptor.create(clazz)
                .and(nonNull(request.getSpreadBondType()), T::getSpreadBondType, isEqual(request.getSpreadBondType()))
                .and(isNull(request.getSpreadBondType()), T::getUsingSpreadBondType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getBondExtRatingMapping()), T::getBondExtRatingMapping, isEqual(request.getBondExtRatingMapping()))
                .and(isNull(request.getBondExtRatingMapping()), T::getUsingBondExtRatingMapping, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getGuaranteeStatus()), T::getGuaranteedStatus, isEqual(request.getGuaranteeStatus()))
                .and(isNull(request.getGuaranteeStatus()), T::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getSpreadRemainingTenorTag()), T::getSpreadRemainingTenorTag, isEqual(request.getSpreadRemainingTenorTag()))
                .and(isNull(request.getSpreadRemainingTenorTag()), T::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getStartSpreadDate()), T::getSpreadDate, greaterThanOrEqual(request.getStartSpreadDate()))
                .and(nonNull(request.getEndSpreadDate()), T::getSpreadDate, lessThanOrEqual(request.getEndSpreadDate()))
                .and(nonNull(request.getSpreadDate()), T::getSpreadDate, isEqual(request.getSpreadDate()));
    }

    /**
     * 查询产业利差
     *
     * @param searchParam 搜索条件
     * @return 利差数据
     */
    @ShardYieldSpreadCurve(type = InduBondShardYieldSpreadCurveRepository.class, method = "listInduShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.ALL)
    public List<PgInduBondYieldSpreadCurveAllDO> listYieldSpreadsFromAll(InduYieldSearchParam searchParam) {
        BaseFilterDescriptor<PgInduBondYieldSpreadCurveAllDO>[] baseFilterDescriptors = listFiltersForAll(searchParam, PgInduBondYieldSpreadCurveAllDO.class).getFilters();
        DynamicQuery<PgInduBondYieldSpreadCurveAllDO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadCurveAllDO.class)
                .and(baseFilterDescriptors);
        return pgInduBondYieldSpreadCurveAllMapper.selectByDynamicQuery(query);
    }

    private <T extends BasePgInduBondYieldSpreadCurveDO> FilterGroupDescriptor<T> listFiltersForAll(InduYieldSearchParam searchParam, Class<T> clazz) {
        return FilterGroupDescriptor.create(clazz)
                .and(nonNull(searchParam.getSpreadBondType()), T::getSpreadBondType, isEqual(searchParam.getSpreadBondType()))
                .and(isNull(searchParam.getSpreadBondType()), T::getUsingSpreadBondType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(searchParam.getGuaranteedStatus()), T::getGuaranteedStatus, isEqual(searchParam.getGuaranteedStatus()))
                .and(isNull(searchParam.getGuaranteedStatus()), T::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(searchParam.getBondExtRatingMapping()), T::getBondExtRatingMapping, isEqual(searchParam.getBondExtRatingMapping()))
                .and(isNull(searchParam.getBondExtRatingMapping()), T::getUsingBondExtRatingMapping, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(searchParam.getRemainingTenor()), T::getSpreadRemainingTenorTag, isEqual(searchParam.getRemainingTenor()))
                .and(isNull(searchParam.getRemainingTenor()), T::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(searchParam.getSpreadDate()), T::getSpreadDate, isEqual(searchParam.getSpreadDate()));
    }

    /**
     * 查询产业利差
     *
     * @param searchParam 搜索条件
     * @return 利差数据
     */
    @ShardYieldSpreadCurve(type = InduBondShardYieldSpreadCurveRepository.class, method = "listInduShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.INDU_LEVEL_1)
    public List<PgInduBondYieldSpreadCurveIndu1DO> listYieldSpreadsFromIndu1(InduYieldSearchParam searchParam) {
        BaseFilterDescriptor<PgInduBondYieldSpreadCurveIndu1DO>[] baseFilterDescriptors = listFiltersForIndu1(searchParam).getFilters();
        DynamicQuery<PgInduBondYieldSpreadCurveIndu1DO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadCurveIndu1DO.class)
                .select(PgInduBondYieldSpreadCurveIndu1DO::getSpreadDate,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondCreditSpread,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondExcessSpread,
                        PgInduBondYieldSpreadCurveIndu1DO::getCbYield,
                        PgInduBondYieldSpreadCurveIndu1DO::getAvgBondCreditSpread,
                        PgInduBondYieldSpreadCurveIndu1DO::getAvgBondExcessSpread,
                        PgInduBondYieldSpreadCurveIndu1DO::getAvgCbYield,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondCreditSpreadCount,
                        PgInduBondYieldSpreadCurveIndu1DO::getBondExcessSpreadCount,
                        PgInduBondYieldSpreadCurveIndu1DO::getCbYieldCount)
                .and(baseFilterDescriptors);
        return pgInduBondYieldSpreadCurveIndu1Mapper.selectByDynamicQuery(query);
    }

    private FilterGroupDescriptor<PgInduBondYieldSpreadCurveIndu1DO> listFiltersForIndu1(InduYieldSearchParam searchParam) {
        FilterGroupDescriptor<PgInduBondYieldSpreadCurveIndu1DO> tFilterGroupDescriptor = listFiltersForAll(searchParam, PgInduBondYieldSpreadCurveIndu1DO.class);
        tFilterGroupDescriptor.and(nonNull(searchParam.getIndustryCode1()), PgInduBondYieldSpreadCurveIndu1DO::getInduLevel1Code, isEqual(searchParam.getIndustryCode1()));
        return tFilterGroupDescriptor;
    }

    /**
     * 查询产业利差
     *
     * @param searchParam 搜索条件
     * @return 利差数据
     */
    @ShardYieldSpreadCurve(type = InduBondShardYieldSpreadCurveRepository.class, method = "listInduShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.INDU_LEVEL_2)
    public List<PgInduBondYieldSpreadCurveIndu2DO> listYieldSpreadsFromIndu2(InduYieldSearchParam searchParam) {
        BaseFilterDescriptor<PgInduBondYieldSpreadCurveIndu2DO>[] baseFilterDescriptors = listFiltersForIndu2(searchParam).getFilters();
        DynamicQuery<PgInduBondYieldSpreadCurveIndu2DO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadCurveIndu2DO.class)
                .and(baseFilterDescriptors);
        return pgInduBondYieldSpreadCurveIndu2Mapper.selectByDynamicQuery(query);
    }

    private FilterGroupDescriptor<PgInduBondYieldSpreadCurveIndu2DO> listFiltersForIndu2(InduYieldSearchParam searchParam) {
        FilterGroupDescriptor<PgInduBondYieldSpreadCurveIndu2DO> tFilterGroupDescriptor = listFiltersForAll(searchParam, PgInduBondYieldSpreadCurveIndu2DO.class);
        tFilterGroupDescriptor.and(nonNull(searchParam.getIndustryCode2()), PgInduBondYieldSpreadCurveIndu2DO::getInduLevel2Code, isEqual(searchParam.getIndustryCode2()));
        return tFilterGroupDescriptor;
    }

    public void createTableRatingRouter(InduBondYieldSpreadCurveParameter parameter) {
        pgInduBondYieldSpreadCurveMapper.createTableRatingRouter(parameter);
    }

    public void syncCurveIncrFromMV(String tableName, String mvTableName) {
        pgInduBondYieldSpreadCurveMapper.syncCurveIncrFromMV(tableName, mvTableName);
    }

    public Boolean dropMv(String tableName) {
        return pgInduBondYieldSpreadCurveMapper.dropMv(tableName);
    }

    public Boolean dropTable(String tableName) {
        return pgInduBondYieldSpreadCurveMapper.dropTable(tableName);
    }

    @Override
    public Boolean tableIsExists(String tableName) {
        return pgInduBondYieldSpreadCurveMapper.tableIsExists(tableName);
    }

    @Override
    public void refreshMvInduBondYieldSpreadCurve(String tableName) {
        pgInduBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurve(tableName);
    }

}
