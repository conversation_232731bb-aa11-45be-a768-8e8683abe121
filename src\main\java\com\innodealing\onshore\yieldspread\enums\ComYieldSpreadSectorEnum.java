package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 利差口径枚举
 *
 * <AUTHOR>
 */
public enum ComYieldSpreadSectorEnum implements ITextValueEnum {

    /**
     * 城投口径
     */
    UDIC(1, "城投"),

    /**
     * 产业口径
     */
    INDU(2, "产业"),

    /**
     * 证券口径
     */
    SECU(3, "证券"),

    /**
     * 银行口径
     */
    BANK(4, "银行"),

    /**
     * 保险口径
     */
    INSU(7, "保险");


    private final int value;
    private final String text;

    ComYieldSpreadSectorEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
