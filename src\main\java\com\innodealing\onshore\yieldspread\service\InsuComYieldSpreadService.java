package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InsuComYieldSpreadResDTO;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 保险主体利差 Service
 *
 * <AUTHOR>
 **/
public interface InsuComYieldSpreadService {

    /**
     * 查询发行人的最新的信用主体利差
     *
     * @param comUniCodes 发行人代码集合
     * @return {@link List}<{@link ComCreditSpreadDTO}>
     */
    List<ComCreditSpreadDTO> listCreditSpreadByComUniCodes(Set<Long> comUniCodes);

    /**
     * 获取主体利差
     *
     * @param userid  用户id
     * @param request 查询条件参数
     * @return 响应前端对象
     */
    List<InsuComYieldSpreadResDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 获取主体利差条数
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 主体利差条数
     */
    Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 查询利差曲线数据集
     *
     * @param comUniCode                主体唯一编码
     * @param insuranceSeniorityRanking 银行求偿顺序
     * @param startDate                 开始日期
     * @param endDate                   结束日期
     * @return {@link List}<{@link ComYieldSpreadCurveDTO}>
     * @see com.innodealing.onshore.yieldspread.enums.InsuSeniorityRankingEnum
     */
    List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer insuranceSeniorityRanking, Date startDate, Date endDate);

    /**
     * 获取保险主体利差数据集
     *
     * @param spreadDate 利差日期
     * @param insuComs   主体唯一编码集合
     * @return {@link List}<{@link InsuComYieldSpreadResDTO}>
     */
    List<InsuComYieldSpreadResDTO> listComs(Date spreadDate, Set<Long> insuComs);
}
