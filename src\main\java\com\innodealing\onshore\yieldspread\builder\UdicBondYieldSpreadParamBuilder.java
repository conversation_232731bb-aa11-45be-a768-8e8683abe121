package com.innodealing.onshore.yieldspread.builder;

import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.CurveBondSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.CurveComSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.CurveFavoriteGroupDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.UdicCurveCompositionConditionDTO;
import org.apache.commons.lang3.ArrayUtils;

import java.sql.Date;
import java.util.Objects;
import java.util.Optional;

import static java.util.Objects.isNull;

/**
 * 城投债券利差参数构造器
 *
 * <AUTHOR>
 */
public class UdicBondYieldSpreadParamBuilder {
    private static final Integer DEFAULT_PAGE_SIZE = 50;
    private final UdicBondYieldSpreadParamDTO udicBondYieldSpread;

    /**
     * 构造函数
     */
    public UdicBondYieldSpreadParamBuilder() {
        udicBondYieldSpread = new UdicBondYieldSpreadParamDTO();
    }

    /**
     * 关注组条件请求参数
     *
     * @param favoriteGroup 关注组请求参数
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder favoriteGroup(CurveFavoriteGroupDTO favoriteGroup) {
        if (Objects.isNull(favoriteGroup)) {
            return this;
        }
        udicBondYieldSpread.setComUniCodes(favoriteGroup.getComUniCodes());
        udicBondYieldSpread.setBondUniCodes(favoriteGroup.getBondUniCodes());
        return this;
    }

    /**
     * 查询组合条件请求参数
     *
     * @param compositionCondition 查询组合条件请求参数
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder compositionCondition(UdicCurveCompositionConditionDTO compositionCondition) {
        if (Objects.isNull(compositionCondition)) {
            return this;
        }
        udicBondYieldSpread.setBondExtRatingMapping(compositionCondition.getBondExtRatingMapping());
        udicBondYieldSpread.setSpreadBondType(compositionCondition.getSpreadBondType());
        if (Objects.nonNull(compositionCondition.getBondImpliedRatingMappingTag())) {
            SpreadUdicBondImpliedRatingMappingTagEnum bondImpliedRatingMappingTag =
                    ITextValueEnum.getEnum(SpreadUdicBondImpliedRatingMappingTagEnum.class, compositionCondition.getBondImpliedRatingMappingTag());
            udicBondYieldSpread.setBondImpliedRatingMappings(bondImpliedRatingMappingTag.getMapping());
        }
        Integer[] bondImpliedRatingMappings = compositionCondition.getBondImpliedRatingMappings();
        if (ArrayUtils.isNotEmpty(bondImpliedRatingMappings)){
            udicBondYieldSpread.setBondImpliedRatingMappings(bondImpliedRatingMappings);
        }
        udicBondYieldSpread.setBondImpliedRatingMappingTag(compositionCondition.getBondImpliedRatingMappingTag());
        udicBondYieldSpread.setAdministrativeDivision(compositionCondition.getAdministrativeDivision());
        udicBondYieldSpread.setProvinceUniCode(compositionCondition.getProvinceUniCode());
        udicBondYieldSpread.setCityUniCode(compositionCondition.getCityUniCode());
        udicBondYieldSpread.setGuaranteeStatus(compositionCondition.getGuaranteeStatus());
        if (Objects.nonNull(compositionCondition.getComYyRatingMappingTag())) {
            SpreadComYyRatingMappingTagEnum comYyRatingMappingTag = ITextValueEnum.getEnum(SpreadComYyRatingMappingTagEnum.class, compositionCondition.getComYyRatingMappingTag());
            udicBondYieldSpread.setComYyRatingMappings(comYyRatingMappingTag.getMapping());
        }
        Integer[] comYyRatingMappings = compositionCondition.getComYyRatingMappings();
        if (ArrayUtils.isNotEmpty(comYyRatingMappings)){
            udicBondYieldSpread.setComYyRatingMappings(comYyRatingMappings);
        }
        udicBondYieldSpread.setComYyRatingMappingTag(compositionCondition.getComYyRatingMappingTag());
        udicBondYieldSpread.setSpreadRemainingTenorTag(compositionCondition.getSpreadRemainingTenorTag());
        return this;
    }

    /**
     * 主体利差请求参数
     *
     * @param comSpread 主体利差请求参数
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder comSpread(CurveComSpreadDTO comSpread) {
        if (Objects.isNull(comSpread)) {
            return this;
        }
        udicBondYieldSpread.setComUniCode(comSpread.getComUniCode());
        udicBondYieldSpread.setSpreadBondType(comSpread.getSpreadBondType());
        return this;
    }

    /**
     * 单券利差请求参数
     *
     * @param bondSpread 单券利差请求参数
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder bondSpread(CurveBondSpreadDTO bondSpread) {
        if (Objects.isNull(bondSpread)) {
            return this;
        }
        udicBondYieldSpread.setBondUniCode(bondSpread.getBondUniCode());
        return this;
    }

    /**
     * 排序
     *
     * @param sort        排序DTO
     * @param defaultSort 默认排序
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder sortOrDefault(SortDTO sort, SortDTO defaultSort) {
        udicBondYieldSpread.setSort(ObjectExtensionUtils.getOrDefault(sort, defaultSort));
        return this;
    }


    /**
     * 利差日期
     *
     * @param spreadDate  利差日期
     * @param defaultDate 默认利差日期
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder spreadDateOrDefault(Date spreadDate, Date defaultDate) {
        udicBondYieldSpread.setSpreadDate(ObjectExtensionUtils.getOrDefault(spreadDate, defaultDate));
        return this;
    }

    /**
     * 设置搜索的发行人唯一编码|债券唯一编码
     *
     * @param codeType   编码类型 1主体编码,2债券编码
     * @param searchCode 唯一编码
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder searchCode(Integer codeType, Long searchCode) {
        if (Objects.isNull(codeType) || Objects.isNull(searchCode)) {
            return this;
        }
        SpreadCodeTypeEnum codeTypeEnum = ITextValueEnum.getEnum(SpreadCodeTypeEnum.class, codeType);
        if (codeTypeEnum.equals(SpreadCodeTypeEnum.BOND_CODE)) {
            udicBondYieldSpread.setBondUniCode(searchCode);
        } else {
            udicBondYieldSpread.setComUniCode(searchCode);
        }
        return this;
    }

    /**
     * 设置分页参数
     *
     * @param pageNum  页码
     * @param pageSize 每页数据量
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder page(Integer pageNum, Integer pageSize) {
        udicBondYieldSpread.setPageNum(isNull(pageNum) || pageNum <= 0 ? 1 : pageNum);
        udicBondYieldSpread.setPageSize(ObjectExtensionUtils.getOrDefault(pageSize, DEFAULT_PAGE_SIZE));
        return this;
    }

    /**
     * 利差曲线属性设置
     *
     * @param spreadCurveType          利差曲线类型 1. 信用利差，2. 超额利差，3. 估值收益率
     * @param displayCdbBenchmarkCurve 是否展示国开基准曲线，是|true, 否|false
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder spreadCurve(Integer spreadCurveType, Boolean displayCdbBenchmarkCurve) {
        SpreadCurveTypeEnum spreadCurveTypeEnum = Optional.ofNullable(spreadCurveType)
                .map(curveType -> ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, spreadCurveType))
                .orElse(SpreadCurveTypeEnum.CREDIT_SPREAD);
        udicBondYieldSpread.setSpreadCurveType(spreadCurveTypeEnum);
        udicBondYieldSpread.setDisplayCdbBenchmarkCurve(displayCdbBenchmarkCurve);
        return this;
    }

    /**
     * 设置发行人唯一编码集合
     *
     * @param comUniCodes 发行人唯一编码
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder comUniCodes(Long[] comUniCodes) {
        udicBondYieldSpread.setComUniCodes(comUniCodes);
        return this;
    }

    /**
     * 设置债券唯一编码集合
     *
     * @param bondUniCodes 债券唯一编码
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder bondUniCodes(Long[] bondUniCodes) {
        udicBondYieldSpread.setBondUniCodes(bondUniCodes);
        return this;
    }

    /**
     * 利差日期跨度
     *
     * @param startSpreadDate 开始利差日期
     * @param endSpreadDate   结束利差日期
     * @return {@link UdicBondYieldSpreadParamBuilder} 城投债券利差参数构造器
     */
    public UdicBondYieldSpreadParamBuilder spanSpreadDate(Date startSpreadDate, Date endSpreadDate) {
        udicBondYieldSpread.setStartSpreadDate(startSpreadDate);
        udicBondYieldSpread.setEndSpreadDate(endSpreadDate);
        return this;
    }

    /**
     * 构建城投债券利差请求参数对象
     *
     * @return <{@link UdicBondYieldSpreadParamDTO}> 城投债券利差请求参数对象
     */
    public UdicBondYieldSpreadParamDTO build() {
        return udicBondYieldSpread;
    }
}