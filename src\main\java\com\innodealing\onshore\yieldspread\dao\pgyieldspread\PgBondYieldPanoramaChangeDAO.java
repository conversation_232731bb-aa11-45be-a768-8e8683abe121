package com.innodealing.onshore.yieldspread.dao.pgyieldspread;


import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.enums.BondYieldIntervalChangeTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBondYieldPanoramaChangeMapper;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldPanoramaBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaChangeDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 债券收益率全景-区间变动DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgBondYieldPanoramaChangeDAO {

    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d";
    private static final String BOND_YIELD_PANORAMA_QUANTILE_PK = "yieldSpread:bondYieldPanoramaChangePk";

    @Resource
    private PgBondYieldPanoramaChangeMapper bondYieldPanoramaChangeMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;

    /**
     * 收益率全景区域变动查询
     *
     * @param bondTypeList     债券类型列表
     * @param changeTenorEnums 变动类型
     * @param spreadDate       利率时间
     * @return 收益率全景
     */
    public List<PgBondYieldPanoramaBO> listYieldPanoramas(List<Integer> bondTypeList, BondYieldIntervalChangeTypeEnum changeTenorEnums, Date spreadDate) {
        if (!ObjectUtils.allNotNull(changeTenorEnums, spreadDate)) {
            return Lists.newArrayList();
        }
        Long minPk = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<PgBondYieldPanoramaChangeDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldPanoramaChangeDO.class)
                .and(PgBondYieldPanoramaChangeDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaChangeDO::getId, lessThanOrEqual(maxPk))
                .and(CollectionUtils.isNotEmpty(bondTypeList), PgBondYieldPanoramaChangeDO::getBondType, in(bondTypeList))
                .and(PgBondYieldPanoramaChangeDO::getChangeType, isEqual(changeTenorEnums.getValue()))
                .and(PgBondYieldPanoramaChangeDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<PgBondYieldPanoramaChangeDO> pgBondYieldPanoramaChangeDOList =
                bondYieldPanoramaChangeMapper.selectByDynamicQuery(dynamicQuery);
        return CollectionUtils.isNotEmpty(pgBondYieldPanoramaChangeDOList) ?
                BeanCopyUtils.copyList(pgBondYieldPanoramaChangeDOList, PgBondYieldPanoramaBO.class) : Lists.newArrayList();
    }

    /**
     * 保存债券收益率全景变动数据
     *
     * @param issueDate               发行日期
     * @param yieldPanoramaChangeList 收益率全景变动列表
     * @return int 保存行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int saveBondYieldPanoramaChangeList(@NonNull Date issueDate, List<PgBondYieldPanoramaChangeDO> yieldPanoramaChangeList) {
        if (CollectionUtils.isEmpty(yieldPanoramaChangeList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        Set<Integer> curveCodes = yieldPanoramaChangeList.stream().map(PgBondYieldPanoramaChangeDO::getCurveCode).collect(Collectors.toSet());
        DynamicQuery<PgBondYieldPanoramaChangeDO> query = DynamicQuery.createQuery(PgBondYieldPanoramaChangeDO.class)
                .and(PgBondYieldPanoramaChangeDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaChangeDO::getId, lessThanOrEqual(maxPk))
                .and(PgBondYieldPanoramaChangeDO::getCurveCode, in(curveCodes));
        Map<String, PgBondYieldPanoramaChangeDO> curveCodeIssueDateMap = bondYieldPanoramaChangeMapper.selectByDynamicQuery(query).stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        List<PgBondYieldPanoramaChangeDO> insertList = new ArrayList<>();
        List<PgBondYieldPanoramaChangeDO> updateList = new ArrayList<>();
        for (PgBondYieldPanoramaChangeDO bondYieldPanoramaChange : yieldPanoramaChangeList) {
            String key = this.getKey(bondYieldPanoramaChange);
            if (curveCodeIssueDateMap.containsKey(key)) {
                PgBondYieldPanoramaChangeDO existBondYieldPanoramaChange = curveCodeIssueDateMap.get(key);
                bondYieldPanoramaChange.setId(existBondYieldPanoramaChange.getId());
                bondYieldPanoramaChange.setCreateTime(null);
                bondYieldPanoramaChange.setUpdateTime(now);
                updateList.add(bondYieldPanoramaChange);
            } else {
                bondYieldPanoramaChange.setId(redisService.generatePk(BOND_YIELD_PANORAMA_QUANTILE_PK, bondYieldPanoramaChange.getIssueDate()));
                bondYieldPanoramaChange.setCreateTime(now);
                bondYieldPanoramaChange.setUpdateTime(now);
                insertList.add(bondYieldPanoramaChange);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param change 债券收益率全景变动
     * @return {@link String} key值
     */
    private String getKey(PgBondYieldPanoramaChangeDO change) {
        return String.format(NAMESPACE_KEY_PLACEHOLDER, change.getCurveCode(), change.getChangeType(), change.getIssueDate().getTime());
    }

    /**
     * 批量更新
     *
     * @param updateList 债券收益率全景分位数
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgBondYieldPanoramaChangeDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldPanoramaChangeMapper> updateBatchAction =
                MapperBatchAction.create(PgBondYieldPanoramaChangeMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldPanoramaChangeDO change : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgBondYieldPanoramaChangeDO> updateQuery = DynamicQuery.createQuery(PgBondYieldPanoramaChangeDO.class)
                        .and(PgBondYieldPanoramaChangeDO::getId, isEqual(change.getId()));
                mapper.updateSelectiveByDynamicQuery(change, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 债券收益率全景变动
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgBondYieldPanoramaChangeDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldPanoramaChangeMapper> insertBatchAction =
                MapperBatchAction.create(PgBondYieldPanoramaChangeMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldPanoramaChangeDO change : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(change));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 债券收益率全景区间变动数据
     *
     * @param curveCodes 曲线code集合
     * @param issueDate  发行日期
     * @return {@link List}<{@link PgBondYieldPanoramaChangeDO}> 债券收益率全景区间变动数据
     */
    public List<PgBondYieldPanoramaChangeDO> listBondYieldPanoramaChanges(Collection<Integer> curveCodes, @NonNull Date issueDate) {
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        DynamicQuery<PgBondYieldPanoramaChangeDO> query = DynamicQuery.createQuery(PgBondYieldPanoramaChangeDO.class)
                .and(PgBondYieldPanoramaChangeDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaChangeDO::getId, lessThanOrEqual(maxPk))
                .and(CollectionUtils.isNotEmpty(curveCodes), PgBondYieldPanoramaChangeDO::getCurveCode, in(curveCodes))
                .and(PgBondYieldPanoramaChangeDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return bondYieldPanoramaChangeMapper.selectByDynamicQuery(query);
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgBondYieldPanoramaChangeDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldPanoramaChangeDO.class)
                .orderBy(PgBondYieldPanoramaChangeDO::getIssueDate, SortDirections::desc);
        return bondYieldPanoramaChangeMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgBondYieldPanoramaChangeDO::getIssueDate);
    }
}
