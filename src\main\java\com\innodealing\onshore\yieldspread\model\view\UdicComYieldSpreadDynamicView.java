package com.innodealing.onshore.yieldspread.model.view;

import com.github.wz2cool.dynamic.mybatis.View;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;


/**
 * 城投主体利差视图
 *
 * <AUTHOR>
 */
@View("udic_com_yield_spread LEFT JOIN com_yield_spread_change ON udic_com_yield_spread.com_uni_code = com_yield_spread_change.com_uni_code " +
        "and udic_com_yield_spread.spread_date = com_yield_spread_change.spread_date\n" +
        "and com_yield_spread_change.deleted = 0\n" +
        "and com_yield_spread_change.com_spread_sector = 1")
public class UdicComYieldSpreadDynamicView {
    /**
     * 发行人代码
     */
    @Column(table = "udic_com_yield_spread")
    private Long comUniCode;
    /**
     * 省份编码
     */
    @Column(table = "udic_com_yield_spread")
    private Long provinceUniCode;
    /**
     * 省份名称
     */
    @Column(table = "udic_com_yield_spread")
    private String provinceName;
    /**
     * 地级市编码
     */
    @Column(table = "udic_com_yield_spread")
    private Long cityUniCode;
    /**
     * 城市名称
     */
    @Column(table = "udic_com_yield_spread")
    private String cityName;
    /**
     * 利差日期
     */
    @Column(table = "udic_com_yield_spread")
    private Date spreadDate;
    /**
     * 实际控制人全称
     */
    @Column(table = "udic_com_yield_spread")
    private String actualControllerFullName;
    /**
     * 行政区域
     */
    @Column(table = "udic_com_yield_spread")
    private Integer administrativeRegion;
    /**
     * 区域名称
     */
    @Column(table = "udic_com_yield_spread")
    private String areaName;
    /**
     * 主体外部评级映射
     */
    @Column(table = "udic_com_yield_spread")
    private Integer comExtRatingMapping;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comCreditSpread;
    /**
     * 超额利差(全部债券);单位(BP)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comExcessSpread;
    /**
     * 信用利差(公募);单位(BP)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPublicCreditSpread;
    /**
     * 超额利差(公募);单位(BP)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPublicExcessSpread;
    /**
     * 信用利差(私募);单位(BP)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPrivateCreditSpread;
    /**
     * 超额利差(私募);单位(BP)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPrivateExcessSpread;
    /**
     * 信用利差(永续);单位(BP)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPerpetualCreditSpread;
    /**
     * 超额利差(永续);单位(BP)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPerpetualExcessSpread;
    /**
     * 估值收益率(全部债券);单位(%)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comCbYield;
    /**
     * 估值收益率(公募);单位(%)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPublicCbYield;
    /**
     * 估值收益率(私募);单位(%)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPrivateCbYield;
    /**
     * 估值收益率(永续);单位(%)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal comPerpetualCbYield;
    /**
     * 债券余额(万)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal bondBalance;
    /**
     * 城投有息债务(单位:万元)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal hideDebt;
    /**
     * 总资产(万元)
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal totalAssets;
    /**
     * 资产负债率%
     */
    @Column(table = "udic_com_yield_spread")
    private BigDecimal assetLiabilityRatio;
    /**
     * 信用利差近3个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_change_3m")
    private BigDecimal creditSpreadChange3M;
    /**
     * 信用利差近6个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_change_6m")
    private BigDecimal creditSpreadChange6M;
    /**
     * 超额利差近3个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_change_3m")
    private BigDecimal excessSpreadChange3M;
    /**
     * 超额利差近6个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_change_6m")
    private BigDecimal excessSpreadChange6M;

    /**
     * 信用利差3年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_quantile_3y")
    private BigDecimal creditSpreadQuantile3Y;

    /**
     * 信用利差5年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_quantile_5y")
    private BigDecimal creditSpreadQuantile5Y;

    /**
     * 超额利差3年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_quantile_3y")
    private BigDecimal excessSpreadQuantile3Y;

    /**
     * 超额利差5年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_quantile_5y")
    private BigDecimal excessSpreadQuantile5Y;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public String getActualControllerFullName() {
        return actualControllerFullName;
    }

    public void setActualControllerFullName(String actualControllerFullName) {
        this.actualControllerFullName = actualControllerFullName;
    }

    public Integer getAdministrativeRegion() {
        return administrativeRegion;
    }

    public void setAdministrativeRegion(Integer administrativeRegion) {
        this.administrativeRegion = administrativeRegion;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComPublicCreditSpread() {
        return comPublicCreditSpread;
    }

    public void setComPublicCreditSpread(BigDecimal comPublicCreditSpread) {
        this.comPublicCreditSpread = comPublicCreditSpread;
    }

    public BigDecimal getComPublicExcessSpread() {
        return comPublicExcessSpread;
    }

    public void setComPublicExcessSpread(BigDecimal comPublicExcessSpread) {
        this.comPublicExcessSpread = comPublicExcessSpread;
    }

    public BigDecimal getComPrivateCreditSpread() {
        return comPrivateCreditSpread;
    }

    public void setComPrivateCreditSpread(BigDecimal comPrivateCreditSpread) {
        this.comPrivateCreditSpread = comPrivateCreditSpread;
    }

    public BigDecimal getComPrivateExcessSpread() {
        return comPrivateExcessSpread;
    }

    public void setComPrivateExcessSpread(BigDecimal comPrivateExcessSpread) {
        this.comPrivateExcessSpread = comPrivateExcessSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComPublicCbYield() {
        return comPublicCbYield;
    }

    public void setComPublicCbYield(BigDecimal comPublicCbYield) {
        this.comPublicCbYield = comPublicCbYield;
    }

    public BigDecimal getComPrivateCbYield() {
        return comPrivateCbYield;
    }

    public void setComPrivateCbYield(BigDecimal comPrivateCbYield) {
        this.comPrivateCbYield = comPrivateCbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public BigDecimal getHideDebt() {
        return hideDebt;
    }

    public void setHideDebt(BigDecimal hideDebt) {
        this.hideDebt = hideDebt;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getAssetLiabilityRatio() {
        return assetLiabilityRatio;
    }

    public void setAssetLiabilityRatio(BigDecimal assetLiabilityRatio) {
        this.assetLiabilityRatio = assetLiabilityRatio;
    }

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }
}
