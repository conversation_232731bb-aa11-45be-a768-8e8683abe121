package com.innodealing.onshore.yieldspread.model.dto.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.innodealing.international.common.template.source.excel.convert.ExcelDateConvert;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.BaseCurveYieldSpreadExcelDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.CURVE_EXPORT_TITLE;

/**
 * 城投债券利差列表导出DTO
 *
 * <AUTHOR>
 */
public class UdicBondSpreadExportExcelDTO extends BaseCurveYieldSpreadExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 债券简称
     */
    @ApiModelProperty("债券简称")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "债券简称"})
    @ColumnWidth(25)
    private String bondShortName;

    /**
     * 债券代码
     */
    @ApiModelProperty("债券代码")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "债券代码"})
    @ColumnWidth(25)
    private String bondCode;

    /**
     * 剩余期限
     */
    @ApiModelProperty("剩余期限")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "剩余期限"})
    @ColumnWidth(25)
    private String remainingTenor;

    /**
     * 发行人
     */
    @ApiModelProperty("发行人")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "发行人"})
    @ColumnWidth(25)
    private String comUniName;

    /**
     * 利差日期
     */
    @ApiModelProperty("利差日期")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "利差日期"}, converter = ExcelDateConvert.class)
    @ColumnWidth(25)
    protected Date spreadDate;

    /**
     * 有无担保
     */
    @ApiModelProperty("有无担保: 0: 无; 1: 有")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "有无担保"})
    @ColumnWidth(25)
    private String guaranteedStatusText;

    @ApiModelProperty("票面利率(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "票面利率(%)"})
    @ColumnWidth(25)
    private BigDecimal latestCouponRate;

    @ApiModelProperty("剩余规模(亿)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "剩余规模(亿)"})
    @ColumnWidth(25)
    private BigDecimal bondBalance;

    /**
     * 主体评级
     */
    @ApiModelProperty("主体评级")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "主体评级"})
    @ColumnWidth(25)
    private String comExtRating;

    /**
     * 债项评级
     */
    @ApiModelProperty("债项评级")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "债项评级"})
    @ColumnWidth(25)
    private String bondExtRating;

    @ApiModelProperty("债项隐含评级")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "隐含评级"})
    @ColumnWidth(25)
    private String bondImpliedRating;

    /**
     * 债券信用利差;单位(BP)
     */
    @ApiModelProperty("信用利差(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)"})
    @ColumnWidth(25)
    private BigDecimal bondCreditSpread;

    /**
     * 债券超额利差;单位(BP)
     */
    @ApiModelProperty("超额利差(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)"})
    @ColumnWidth(25)
    private BigDecimal bondExcessSpread;

    @ApiModelProperty("估值收益率")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率"})
    @ColumnWidth(25)
    private String cbYieldStr;

    @ApiModelProperty("国开收益率(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "国开收益率(%)"})
    @ColumnWidth(25)
    private String cdbLerpYield;

    @ApiModelProperty("中债城投曲线(%)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "中债城投曲线(%)"})
    @ColumnWidth(25)
    private String impliedRatingLerpYield;

    public String getBondImpliedRating() {
        return bondImpliedRating;
    }

    public void setBondImpliedRating(String bondImpliedRating) {
        this.bondImpliedRating = bondImpliedRating;
    }

    public String getCbYieldStr() {
        return cbYieldStr;
    }

    public void setCbYieldStr(String cbYieldStr) {
        this.cbYieldStr = cbYieldStr;
    }

    public String getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(String cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

    public String getImpliedRatingLerpYield() {
        return impliedRatingLerpYield;
    }

    public void setImpliedRatingLerpYield(String impliedRatingLerpYield) {
        this.impliedRatingLerpYield = impliedRatingLerpYield;
    }

    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public String getComExtRating() {
        return comExtRating;
    }

    public void setComExtRating(String comExtRating) {
        this.comExtRating = comExtRating;
    }

    public String getBondExtRating() {
        return bondExtRating;
    }

    public void setBondExtRating(String bondExtRating) {
        this.bondExtRating = bondExtRating;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public String getGuaranteedStatusText() {
        return guaranteedStatusText;
    }

    public void setGuaranteedStatusText(String guaranteedStatusText) {
        this.guaranteedStatusText = guaranteedStatusText;
    }

}
