package com.innodealing.onshore.yieldspread.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.international.common.template.utils.ExcelUtils;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondpriceapollo.CurveMaturityStructureDTO;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.enums.SecuritySeniorityRankingEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.builder.BondYieldSpreadBuilder;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.*;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.view.PgBondYieldPanoramaQuantileViewDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserSpreadConfigDAO;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.handler.CustomManyMergeHandler;
import com.innodealing.onshore.yieldspread.handler.CustomTemplateSheetHandler;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.PeriodExcelFillDataBO;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldPanoramaBO;
import com.innodealing.onshore.yieldspread.model.bo.SpreadStatisticsBO;
import com.innodealing.onshore.yieldspread.model.bo.UserSpreadConfigBO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldPanoramaTraceSpreadDateDTO;
import com.innodealing.onshore.yieldspread.model.dto.PgBondYieldPanoramaDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldPanoramaExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldPanoramaResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldPanoramaTypeDataExportExcelDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldPanoramaTypeDataResponseDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaChangeDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaQuantileDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadCbYieldGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadCbYieldGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgBondYieldPanoramaQuantileViewDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgPanoramaQuantileStatisticsViewDO;
import com.innodealing.onshore.yieldspread.service.BondYieldPanoramaService;
import com.innodealing.onshore.yieldspread.service.YieldSpreadCommonService;
import com.innodealing.onshore.yieldspread.service.internal.BondPriceApolloService;
import com.innodealing.onshore.yieldspread.service.internal.BondPriceService;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import com.innodealing.onshore.yieldspread.service.internal.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.ONE_HUNDRED;
import static com.innodealing.onshore.yieldspread.enums.BondImpliedRatingCurveCodeMappingEnum.getBondImpliedRating;
import static com.innodealing.onshore.yieldspread.enums.BondImpliedRatingCurveCodeMappingEnum.getInduBondCurveCodeByRating;
import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.*;

/**
 * 债券收益率全景服务
 *
 * <AUTHOR>
 */
@Service
public class BondYieldPanoramaServiceImpl implements BondYieldPanoramaService, InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(BondYieldPanoramaServiceImpl.class);
    private static final Long INDU1_REAL_ESTATE_CODE = 430_000L;

    private static final int TWO_DAYS = 2;

    private static final int PANORAMA_QUANTILE_SCALE = 2;
    /**
     * 全景区间变动小数位
     */
    private static final int PANORAMA_CHANGE_SCALE = 2;

    /**
     * 全景单元格合并初始首行索引值
     */
    private static final int INIT_PANORAMA_MERGE_CELL_FIRST_ROW_INDEX = 2;

    /**
     * 全景单元格合并初始最后一行索引值
     */
    private static final int INIT_PANORAMA_MERGE_CELL_LAST_ROW_INDEX = 2;


    private static final String FILE_NAME = "收益率全景-%s";

    private final List<Integer> curveCodes = new ArrayList<>();

    private final List<Long> uniCurveCodes = new ArrayList<>();

    private final Map<BondYieldTableTypeEnum, BiFunction<BondYieldPanoramaTraceSpreadDateDTO, Date, Date>> spreadDateSelectorMap = new ConcurrentHashMap<>();

    private static final String PANORAMA_SPREAD_DATE_KEY = "yield-spread:bond_yield_panorama:%s";

    @Resource
    private PgBondYieldPanoramaAbsDAO pgBondYieldPanoramaAbsDAO;

    @Resource
    private PgBondYieldPanoramaChangeDAO pgBondYieldPanoramaChangeDAO;

    @Resource
    private PgBondYieldPanoramaQuantileDAO pgBondYieldPanoramaQuantileDAO;

    @Resource
    private UserSpreadConfigDAO userSpreadConfigDAO;

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private BondPriceApolloService bondPriceApolloService;

    @Resource
    private PgInduBondYieldSpreadDAO pgInduBondYieldSpreadDAO;

    @Resource
    private HolidayService holidayService;

    @Resource
    private PgBondYieldPanoramaQuantileViewDAO pgBondYieldPanoramaQuantileViewDAO;

    @Resource
    private PgSecuBondYieldSpreadDAO pgSecuBondYieldSpreadDAO;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserService userService;

    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;

    @Override
    public void afterPropertiesSet() {
        Set<YieldPanoramaBondTypeEnum> excludeBondTypes = EnumSet.of(INDUSTRIAL_BOND, SECURITIES_SUB_BOND,
                SECURITIES_PERPETUAL_BOND, INSU_CAPITAL_SUPPLEMENT, CHINA_BOND_IMPORT, CHINA_BOND_NO);
        List<YieldSpreadCurveCodeEnum> allCurveCodes = new ArrayList<>(Arrays.asList(YieldSpreadCurveCodeEnum.values()));
        allCurveCodes.removeIf(code -> excludeBondTypes.contains(code.getBondType()));
        curveCodes.addAll(allCurveCodes.stream().map(YieldSpreadCurveCodeEnum::getValue).collect(Collectors.toList()));
        Set<YieldPanoramaBondTypeEnum> allUniBondTypes = EnumSet.of(INSU_CAPITAL_SUPPLEMENT, CHINA_BOND_IMPORT, CHINA_BOND_NO);
        List<YieldSpreadCurveCodeEnum> allUniCurveCodes = Arrays.stream(YieldSpreadCurveCodeEnum.values())
                .filter(code -> allUniBondTypes.contains(code.getBondType()))
                .collect(Collectors.toList());
        uniCurveCodes.addAll(allUniCurveCodes.stream()
                .map(uniCurveCodeEnum -> CurveUniCodeEnum.getCurveUniCodeByCurveCode(uniCurveCodeEnum.getValue()))
                .filter(Optional::isPresent)
                .map(Optional::get).collect(Collectors.toList()));
        spreadDateSelectorMap.put(BondYieldTableTypeEnum.ABS, BondYieldPanoramaTraceSpreadDateDTO::getOrDefaultAbs);
        spreadDateSelectorMap.put(BondYieldTableTypeEnum.HIST_QUANTILE, BondYieldPanoramaTraceSpreadDateDTO::getOrDefaultQuantile);
        spreadDateSelectorMap.put(BondYieldTableTypeEnum.INTERVAL_CHANGE, BondYieldPanoramaTraceSpreadDateDTO::getOrDefaultChange);
    }

    /**
     * 获取到期收益率全景绝对值
     *
     * @param userId     用户id
     * @param spreadDate 利率时间
     * @return 收益率全景绝对值
     */
    @Override
    public YieldPanoramaResponseDTO getYieldPanoramaAbs(Long userId, Date spreadDate) {
        PgBondYieldPanoramaDTO pgBondYieldPanoramaDTO =
                this.listPgBondYieldPanoramaBOs(userId, BondYieldTableTypeEnum.ABS.getValue(), null, spreadDate, null, null);
        List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList = pgBondYieldPanoramaDTO.getPgBondYieldPanoramaBOList();
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        // 只限制到期收益率
        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        return convertToPanoramaResponse(userId, pgBondYieldPanoramaBOList, hasPermission, null, null);
    }

    /**
     * 获取到期收益率全景分位
     *
     * @param userId       用户id
     * @param spreadDate   利率时间
     * @param quantileType 分位类型 1:3年，2:5年
     * @param startDate    自定义开始时间
     * @param endDate      自定义结束时间
     * @return 收益率全景历史分位
     */
    @Override
    public YieldPanoramaResponseDTO getYieldPanoramaQuantile(Long userId, Date spreadDate, Integer quantileType, Date startDate, Date endDate) {
        PgBondYieldPanoramaDTO pgBondYieldPanoramaDTO
                = this.listPgBondYieldPanoramaBOs(userId, BondYieldTableTypeEnum.HIST_QUANTILE.getValue(), quantileType, spreadDate, startDate, endDate);
        List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList = pgBondYieldPanoramaDTO.getPgBondYieldPanoramaBOList();
        startDate = pgBondYieldPanoramaDTO.getStartDate();
        endDate = pgBondYieldPanoramaDTO.getEndDate();
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        // 只限制到期收益率
        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);

        return convertToPanoramaResponse(userId, pgBondYieldPanoramaBOList, hasPermission, startDate, endDate);
    }

    /**
     * 获取到期收益率全景区间变动
     *
     * @param userId     用户id
     * @param spreadDate 利率时间
     * @param changeType 变动类型(1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动 6 一日变动 7 两周变动)
     * @param startDate  自定义开始时间
     * @param endDate    自定义结束时间
     * @return 收益率全景区间变动
     */
    @Override
    public YieldPanoramaResponseDTO getYieldPanoramaChange(Long userId, Date spreadDate, Integer changeType, Date startDate, Date endDate) {
        PgBondYieldPanoramaDTO pgBondYieldPanoramaDTO
                = this.listPgBondYieldPanoramaBOs(userId, BondYieldTableTypeEnum.INTERVAL_CHANGE.getValue(), changeType, spreadDate, startDate, endDate);
        List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList = pgBondYieldPanoramaDTO.getPgBondYieldPanoramaBOList();
        startDate = pgBondYieldPanoramaDTO.getStartDate();
        endDate = pgBondYieldPanoramaDTO.getEndDate();
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        // 只限制到期收益率
        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        return convertToPanoramaResponse(userId, pgBondYieldPanoramaBOList, hasPermission, startDate, endDate);
    }

    /**
     * 实时获取历史分位数据
     *
     * @param bondTypeList 债券类型列表
     * @param startDate    开始时间
     * @param endDate      结束时间
     * @param issueDate    利率时间
     * @return
     */
    private List<PgBondYieldPanoramaBO> listPgBondYieldPanoramaOfQuantile(List<Integer> curveCodes, List<Integer> bondTypeList, Date startDate, Date endDate, final Date issueDate) {
        List<PgPanoramaQuantileStatisticsViewDO> pgPanoramaQuantileStatisticsViewDOs =
                pgBondYieldPanoramaAbsDAO.listPanoramaQuantileViews(curveCodes, bondTypeList, startDate, endDate, issueDate);
        return pgPanoramaQuantileStatisticsViewDOs.stream().map(pgPanoramaQuantileStatisticsViewDO -> {
            PgBondYieldPanoramaBO pgBondYieldPanoramaBO = new PgBondYieldPanoramaBO();
            pgBondYieldPanoramaBO.setStartDate(startDate);
            pgBondYieldPanoramaBO.setTableType(BondYieldTableTypeEnum.HIST_QUANTILE.getValue());
            pgBondYieldPanoramaBO.setIssueDate(issueDate);
            fillPanoramaBOByStatisticsView(pgPanoramaQuantileStatisticsViewDO, pgBondYieldPanoramaBO);
            return pgBondYieldPanoramaBO;
        }).collect(Collectors.toList());
    }

    /**
     * PgPanoramaQuantileStatisticsViewDO 转 PgBondYieldPanoramaBO
     *
     * @param pgPanoramaQuantileStatisticsViewDO 分位统计数据
     * @param pgBondYieldPanoramaBO              待填充对象
     */
    private void fillPanoramaBOByStatisticsView(PgPanoramaQuantileStatisticsViewDO pgPanoramaQuantileStatisticsViewDO, PgBondYieldPanoramaBO pgBondYieldPanoramaBO) {
        pgBondYieldPanoramaBO.setCurveCode(pgPanoramaQuantileStatisticsViewDO.getCurveCode());
        Integer ytm1MLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm1MLessIssueCount();
        Integer ytm1MCount = pgPanoramaQuantileStatisticsViewDO.getYtm1MCount();
        pgBondYieldPanoramaBO.setYtm1M(CalculationHelper.safeCalPercentRankIgnore(ytm1MLessIssueCount, ytm1MCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 3月
        Integer ytm3MLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm3MLessIssueCount();
        Integer ytm3MCount = pgPanoramaQuantileStatisticsViewDO.getYtm3MCount();
        pgBondYieldPanoramaBO.setYtm3M(CalculationHelper.safeCalPercentRankIgnore(ytm3MLessIssueCount, ytm3MCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 6月
        Integer ytm6MLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm6MLessIssueCount();
        Integer ytm6MCount = pgPanoramaQuantileStatisticsViewDO.getYtm6MCount();
        pgBondYieldPanoramaBO.setYtm6M(CalculationHelper.safeCalPercentRankIgnore(ytm6MLessIssueCount, ytm6MCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 9月
        Integer ytm9MLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm9MLessIssueCount();
        Integer ytm9MCount = pgPanoramaQuantileStatisticsViewDO.getYtm9MCount();
        pgBondYieldPanoramaBO.setYtm9M(CalculationHelper.safeCalPercentRankIgnore(ytm9MLessIssueCount, ytm9MCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 1年
        Integer ytm1YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm1YLessIssueCount();
        Integer ytm1YCount = pgPanoramaQuantileStatisticsViewDO.getYtm1YCount();
        pgBondYieldPanoramaBO.setYtm1Y(CalculationHelper.safeCalPercentRankIgnore(ytm1YLessIssueCount, ytm1YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 2年
        Integer ytm2YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm2YLessIssueCount();
        Integer ytm2YCount = pgPanoramaQuantileStatisticsViewDO.getYtm2YCount();
        pgBondYieldPanoramaBO.setYtm2Y(CalculationHelper.safeCalPercentRankIgnore(ytm2YLessIssueCount, ytm2YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 3年
        Integer ytm3YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm3YLessIssueCount();
        Integer ytm3YCount = pgPanoramaQuantileStatisticsViewDO.getYtm3YCount();
        pgBondYieldPanoramaBO.setYtm3Y(CalculationHelper.safeCalPercentRankIgnore(ytm3YLessIssueCount, ytm3YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 4年
        Integer ytm4YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm4YLessIssueCount();
        Integer ytm4YCount = pgPanoramaQuantileStatisticsViewDO.getYtm4YCount();
        pgBondYieldPanoramaBO.setYtm4Y(CalculationHelper.safeCalPercentRankIgnore(ytm4YLessIssueCount, ytm4YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 5年
        Integer ytm5YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm5YLessIssueCount();
        Integer ytm5YCount = pgPanoramaQuantileStatisticsViewDO.getYtm5YCount();
        pgBondYieldPanoramaBO.setYtm5Y(CalculationHelper.safeCalPercentRankIgnore(ytm5YLessIssueCount, ytm5YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 7年
        Integer ytm7YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm7YLessIssueCount();
        Integer ytm7YCount = pgPanoramaQuantileStatisticsViewDO.getYtm7YCount();
        pgBondYieldPanoramaBO.setYtm7Y(CalculationHelper.safeCalPercentRankIgnore(ytm7YLessIssueCount, ytm7YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 10年
        Integer ytm10YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm10YLessIssueCount();
        Integer ytm10YCount = pgPanoramaQuantileStatisticsViewDO.getYtm10YCount();
        pgBondYieldPanoramaBO.setYtm10Y(CalculationHelper.safeCalPercentRankIgnore(ytm10YLessIssueCount, ytm10YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 15年
        Integer ytm15YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm15YLessIssueCount();
        Integer ytm15YCount = pgPanoramaQuantileStatisticsViewDO.getYtm15YCount();
        pgBondYieldPanoramaBO.setYtm15Y(CalculationHelper.safeCalPercentRankIgnore(ytm15YLessIssueCount, ytm15YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 20年
        Integer ytm20YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm20YLessIssueCount();
        Integer ytm20YCount = pgPanoramaQuantileStatisticsViewDO.getYtm20YCount();
        pgBondYieldPanoramaBO.setYtm20Y(CalculationHelper.safeCalPercentRankIgnore(ytm20YLessIssueCount, ytm20YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 30年
        Integer ytm30YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm30YLessIssueCount();
        Integer ytm30YCount = pgPanoramaQuantileStatisticsViewDO.getYtm30YCount();
        pgBondYieldPanoramaBO.setYtm30Y(CalculationHelper.safeCalPercentRankIgnore(ytm30YLessIssueCount, ytm30YCount, PANORAMA_QUANTILE_SCALE).orElse(null));
        // 50年
        Integer ytm50YLessIssueCount = pgPanoramaQuantileStatisticsViewDO.getYtm50YLessIssueCount();
        Integer ytm50YCount = pgPanoramaQuantileStatisticsViewDO.getYtm50YCount();
        pgBondYieldPanoramaBO.setYtm50Y(CalculationHelper.safeCalPercentRankIgnore(ytm50YLessIssueCount, ytm50YCount, PANORAMA_QUANTILE_SCALE).orElse(null));

    }


    /**
     * 实时计算区间变动
     *
     * @param bondTypeList 债券类型列表
     * @param startDate    开始时间
     * @param endDate      结束时间
     * @return 收益率全景区间变动数据
     */
    private List<PgBondYieldPanoramaBO> listPgBondYieldPanoramaOfChange(List<Integer> bondTypeList, Date startDate, Date endDate) {
        if (startDate.toLocalDate().isAfter(endDate.toLocalDate())) {
            throw new TipsException("开始时间不能大于结束时间");
        }
        List<PgBondYieldPanoramaAbsDO> beforeBondYieldPanoramaAbsList = pgBondYieldPanoramaAbsDAO.listBondYieldPanoramaQuantiles(bondTypeList, startDate);
        if (CollectionUtils.isEmpty(beforeBondYieldPanoramaAbsList)) {
            return Lists.newArrayList();
        }
        List<PgBondYieldPanoramaChangeDO> dataList = Lists.newArrayList();

        List<PgBondYieldPanoramaAbsDO> afterBondYieldPanoramaAbsList = pgBondYieldPanoramaAbsDAO.listBondYieldPanoramaQuantiles(bondTypeList, endDate);
        if (CollectionUtils.isEmpty(afterBondYieldPanoramaAbsList)) {
            return Lists.newArrayList();
        }

        Map<Integer, PgBondYieldPanoramaAbsDO> beforeCurveCodeMap =
                beforeBondYieldPanoramaAbsList.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        Map<Integer, PgBondYieldPanoramaAbsDO> afterCurveCodeMap =
                afterBondYieldPanoramaAbsList.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        for (Map.Entry<Integer, PgBondYieldPanoramaAbsDO> afterEntry : afterCurveCodeMap.entrySet()) {
            PgBondYieldPanoramaAbsDO beforeAbs = beforeCurveCodeMap.get(afterEntry.getKey());
            Optional<PgBondYieldPanoramaChangeDO> change =
                    this.buildBondYieldPanoramaChange(afterEntry.getValue(), beforeAbs, null, startDate);
            change.ifPresent(dataList::add);
        }
        return BeanCopyUtils.copyList(dataList, PgBondYieldPanoramaBO.class);
    }

    /**
     * 根据收益率全景数据 和 是否权限 转化为 返回前端对象
     *
     * @param pgBondYieldPanoramaBOList 收益率全景数据列表
     * @param hasPermission             是否有权限
     * @param startDate                 填充统计时间范围的开始时间(不填充则为null)
     * @param endDate                   填充统计时间范围的结束时间(不填充则为null)
     * @return 前端对象展示
     */
    private YieldPanoramaResponseDTO convertToPanoramaResponse(Long userId, List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList,
                                                               boolean hasPermission, Date startDate, Date endDate) {
        YieldPanoramaResponseDTO resultDTO = new YieldPanoramaResponseDTO();
        List<YieldPanoramaTypeDataResponseDTO> dataResponseDTOList =
                pgBondYieldPanoramaBOList.stream().map(pgBondYieldPanoramaBO -> {
                            YieldSpreadCurveCodeEnum enumNullable = EnumUtils.getEnumNullable(YieldSpreadCurveCodeEnum.class, pgBondYieldPanoramaBO.getCurveCode());
                            if (Objects.nonNull(enumNullable) && YieldPanoramaBondTypeEnum.getHasPermissionBondTypeEnums().contains(enumNullable.getBondType())) {
                                // 产业债不需要权限
                                return BondYieldSpreadBuilder.builder(true, pgBondYieldPanoramaBO);
                            }
                            return BondYieldSpreadBuilder.builder(hasPermission, pgBondYieldPanoramaBO);
                        })
                        .sorted(Comparator.comparing(YieldPanoramaTypeDataResponseDTO::getDataSort)).collect(Collectors.toList());

        List<Integer> periodConfigList = userSpreadConfigDAO.getUserSpreadConfigByConfigId(userId,
                (long) UserConfigEnum.CONFIG_10002.getValue(), Integer.class).map(UserSpreadConfigBO::getConfigDetails).orElse(Lists.newArrayList());
        SpreadStatisticsBO spreadStatisticsBO = YieldSpreadHelper.statisticsSpreadByPeriod(pgBondYieldPanoramaBOList, periodConfigList);

        // 处理最大最小和中位数 为了页面颜色展示
        resultDTO.setMinYield(spreadStatisticsBO.getMinYield());
        resultDTO.setMaxYield(spreadStatisticsBO.getMaxYield());
        resultDTO.setMedianYield(spreadStatisticsBO.getMedianYield());
        resultDTO.setYieldPanoramaDataList(dataResponseDTOList);
        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            resultDTO.setStatisticalDateStart(startDate.toLocalDate().format(DateTimeFormatter.ISO_DATE));
            resultDTO.setStatisticalDateEnd(endDate.toLocalDate().format(DateTimeFormatter.ISO_DATE));
            resultDTO.setStatisticalDateRange(String.format(BondYieldSpreadBuilder.STATISTICAL_DATE_RANGE, startDate.toLocalDate().format(DateTimeFormatter.ISO_DATE),
                    endDate.toLocalDate().format(DateTimeFormatter.ISO_DATE)));
        }
        return resultDTO;
    }

    /**
     * 导出收益率全景
     *
     * @param httpServletResponse           响应体
     * @param userId                        用户id
     * @param bondYieldPanoramaExportReqDTO 请求参数
     * @throws IOException Exception
     */
    @Override
    public void exportYieldPanorama(HttpServletResponse httpServletResponse
            , Long userId, BondYieldPanoramaExportReqDTO bondYieldPanoramaExportReqDTO) throws IOException {
        ServletOutputStream outputStream = httpServletResponse.getOutputStream();
        ExcelWriter excelWriter = null;

        Date spreadDate = bondYieldPanoramaExportReqDTO.getSpreadDate();
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        boolean cbPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        try (InputStream inputStream = new ClassPathResource("/static/template/excel/利差全景模板.xlsx").getInputStream()) {
            ExcelUtils.setExportResponse(httpServletResponse,
                    String.format(FILE_NAME, spreadDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE)));
            // 创建多sheet的表格writer
            excelWriter = EasyExcelFactory.write(outputStream).withTemplate(inputStream).excelType(ExcelTypeEnum.XLSX).build();
            writeExcelForYieldPanorama(userId, cbPermission, excelWriter,
                    BondYieldTableTypeEnum.ABS.getValue(), spreadDate, bondYieldPanoramaExportReqDTO);
            writeExcelForYieldPanorama(userId, cbPermission,
                    excelWriter, BondYieldTableTypeEnum.HIST_QUANTILE.getValue(), spreadDate,
                    bondYieldPanoramaExportReqDTO);
            writeExcelForYieldPanorama(userId, cbPermission,
                    excelWriter, BondYieldTableTypeEnum.INTERVAL_CHANGE.getValue(), spreadDate,
                    bondYieldPanoramaExportReqDTO);
        } finally {
            Optional.ofNullable(excelWriter).ifPresent(ExcelWriter::finish);
            outputStream.flush();
        }
    }

    /**
     * 导出收益率全景 sheet
     *
     * @param userId                        用户id
     * @param cbCurveAuthFlag               中债曲线权限
     * @param excelWriter                   excel书写对象
     * @param issueDate                     实际利率时间
     * @param tableType                     利差图类型 类型 1 绝对值 2 3年历史分位 3 区间变动
     * @param bondYieldPanoramaExportReqDTO 参数
     */
    private void writeExcelForYieldPanorama(Long userId, boolean cbCurveAuthFlag, ExcelWriter excelWriter, Integer tableType
            , Date issueDate, BondYieldPanoramaExportReqDTO bondYieldPanoramaExportReqDTO) {

        List<Integer> periodCodes = userSpreadConfigDAO.getUserSpreadConfigByConfigId(userId, (long) UserConfigEnum.CONFIG_10002.getValue(), Integer.class)
                .map(UserSpreadConfigBO::getConfigDetails).orElseGet(Lists::newArrayList);
        periodCodes.sort(Integer::compareTo);

        List<YieldPanoramaTypeDataExportExcelDTO> resultExcelDTOList = Lists.newArrayList();

        // 获取需要的数据
        List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList;
        String title;

        PgBondYieldPanoramaDTO pgBondYieldPanoramaDTO = writeExcelForYieldPanoramaDate(userId, tableType, issueDate, bondYieldPanoramaExportReqDTO);

        pgBondYieldPanoramaBOList = pgBondYieldPanoramaDTO.getPgBondYieldPanoramaBOList();
        Date startDate = pgBondYieldPanoramaDTO.getStartDate();
        Date endDate = pgBondYieldPanoramaDTO.getEndDate();
        if (CollectionUtils.isNotEmpty(pgBondYieldPanoramaBOList)) {
            resultExcelDTOList = pgBondYieldPanoramaBOList.stream().map(pgBondYieldPanoramaBO -> {
                        YieldSpreadCurveCodeEnum enumNullable = EnumUtils.getEnumNullable(YieldSpreadCurveCodeEnum.class, pgBondYieldPanoramaBO.getCurveCode());
                        if (Objects.nonNull(enumNullable) && YieldPanoramaBondTypeEnum.getHasPermissionBondTypeEnums().contains(enumNullable.getBondType())) {
                            // 产业债 和 保险资本补充不需要权限
                            return BondYieldSpreadBuilder.excelBuilder(true, pgBondYieldPanoramaBO);
                        }
                        return BondYieldSpreadBuilder.excelBuilder(cbCurveAuthFlag, pgBondYieldPanoramaBO);
                    })
                    .sorted(Comparator.comparing(YieldPanoramaTypeDataExportExcelDTO::getDataSort)).collect(Collectors.toList());
        }
        BondYieldTableTypeEnum spreadTypeEnum = ITextValueEnum.getEnum(BondYieldTableTypeEnum.class, tableType);
        final String sheetName = spreadTypeEnum.getText();
        final Integer sheetNo = spreadTypeEnum.getValue() - 1;

        // 合并对象
        CustomManyMergeHandler customManyMergeHandler = fillPanoramaMergeCell(resultExcelDTOList, periodCodes.size());
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(sheetNo, sheetName)
                .registerWriteHandler(new CustomTemplateSheetHandler(sheetNo, sheetName))
                .registerWriteHandler(customManyMergeHandler)
                .build();

        List<PeriodExcelFillDataBO> periodExcelFillDataBOList = PeriodEnum.getPeriodEnumByValues(periodCodes).stream().map(periodEnum -> {
            PeriodExcelFillDataBO periodExcelFillDataBO = new PeriodExcelFillDataBO();
            periodExcelFillDataBO.setPeriod(periodEnum.getText());
            periodExcelFillDataBO.setYieldPeriodFill(String.format(periodEnum.getPeriodExcelFill(), "data"));
            return periodExcelFillDataBO;
        }).collect(Collectors.toList());
        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
        excelWriter.fill(new FillWrapper("fill", periodExcelFillDataBOList), fillConfig, writeSheet);


        Map<String, Object> excelShow = Maps.newHashMap();
        if (BondYieldTableTypeEnum.ABS.equals(spreadTypeEnum)) {
            title = String.format(BondYieldSpreadBuilder.STATISTICAL_DATE, issueDate);
        } else if (BondYieldTableTypeEnum.HIST_QUANTILE.equals(spreadTypeEnum)) {
            title = String.format(BondYieldSpreadBuilder.STATISTICAL_DATE_RANGE, startDate, endDate);
        } else {
            title = String.format(BondYieldSpreadBuilder.STATISTICAL_DATE_RANGE, startDate, endDate);
        }
        excelShow.put("title", title);
        excelWriter.fill(excelShow, writeSheet);
        excelWriter.fill(new FillWrapper("data", resultExcelDTOList), writeSheet);
        excelWriter.fill(new FillWrapper("curve", resultExcelDTOList), writeSheet);
    }

    /**
     * 根据导出数据获取全景excel单元格合并处理器
     *
     * @param resultExcelDTOList 实际数据
     * @param periodNum          期限数
     * @return 单元格合并处理器
     */
    private CustomManyMergeHandler fillPanoramaMergeCell(List<YieldPanoramaTypeDataExportExcelDTO> resultExcelDTOList, int periodNum) {
        CustomManyMergeHandler customManyMergeHandler = new CustomManyMergeHandler();
        // 第一行表头合并值
        customManyMergeHandler.addOnceAbsoluteMerge(0, 0, 0, 1 + periodNum);
        if (CollectionUtils.isEmpty(resultExcelDTOList)) {
            return customManyMergeHandler;
        }
        int firstRowIndex = INIT_PANORAMA_MERGE_CELL_FIRST_ROW_INDEX;
        int lastRowIndex = INIT_PANORAMA_MERGE_CELL_LAST_ROW_INDEX;
        String beforeTypeName = "";
        for (YieldPanoramaTypeDataExportExcelDTO excelDTO : resultExcelDTOList) {
            String typeName = excelDTO.getTypeName();
            String ratingName = excelDTO.getRatingName();
            if (StringUtils.isBlank(typeName)) {
                // 正常不会出现,出现是数据异常
                firstRowIndex++;
                lastRowIndex++;
                continue;
            }

            if (!typeName.equals(beforeTypeName)) {
                beforeTypeName = typeName;
                if (lastRowIndex - firstRowIndex > 1) {
                    customManyMergeHandler.addOnceAbsoluteMerge(firstRowIndex, lastRowIndex - 1, 0, 0);
                }
                firstRowIndex = lastRowIndex;

            }
            if (StringUtils.isBlank(ratingName)) {
                customManyMergeHandler.addOnceAbsoluteMerge(firstRowIndex, lastRowIndex, 0, 1);
                firstRowIndex++;
                beforeTypeName = "";
            }
            lastRowIndex++;
        }
        return customManyMergeHandler;
    }

    private PgBondYieldPanoramaDTO writeExcelForYieldPanoramaDate(Long userId, Integer tableType, Date issueDate, BondYieldPanoramaExportReqDTO bondYieldPanoramaExportReqDTO) {
        Integer tableTypeParam = null;
        Date startDate = null;
        Date endDate = null;

        if (tableType.equals(BondYieldTableTypeEnum.INTERVAL_CHANGE.getValue())) {
            if (Objects.nonNull(bondYieldPanoramaExportReqDTO.getChangeStartDate())
                    && Objects.nonNull(bondYieldPanoramaExportReqDTO.getChangeEndDate())) {
                startDate = bondYieldPanoramaExportReqDTO.getChangeStartDate();
                endDate = bondYieldPanoramaExportReqDTO.getChangeEndDate();
            } else {
                tableTypeParam = Optional.ofNullable(bondYieldPanoramaExportReqDTO.getChangeType())
                        .map(change -> ITextValueEnum.getEnum(BondYieldIntervalChangeTypeEnum.class, change))
                        .orElse(BondYieldIntervalChangeTypeEnum.getDefaultChangeType()).getValue();
            }
        } else if (tableType.equals(BondYieldTableTypeEnum.HIST_QUANTILE.getValue())) {
            if (Objects.nonNull(bondYieldPanoramaExportReqDTO.getQuantileStartDate()) && Objects.nonNull(bondYieldPanoramaExportReqDTO.getQuantileEndDate())) {
                startDate = bondYieldPanoramaExportReqDTO.getQuantileStartDate();
                endDate = bondYieldPanoramaExportReqDTO.getQuantileEndDate();
            } else {
                tableTypeParam = EnumUtils.ofNullable(SpreadQuantileTypeEnum.class, bondYieldPanoramaExportReqDTO.getQuantileType())
                        .orElse(SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE).getValue();
            }
        }
        return listPgBondYieldPanoramaBOs(userId, tableType, tableTypeParam, issueDate, startDate, endDate);
    }

    /**
     * 查询全景数据
     *
     * @param userId         用户id
     * @param tableType      利差图类型 1 绝对值 2 历史分位 3 区间变动
     * @param tableTypeParam tableType=2时,为 BondYieldTableTypeEnum 枚举value值;tableType=3时,为 BondYieldIntervalChangeTypeEnum 枚举value值;
     * @param issueDate      利率时间
     * @param startDate      tableType=2 或3 自定义时间范围 开始时间
     * @param endDate        tableType=2 或3 自定义时间范围 结束时间
     * @return 全景数据
     */
    private PgBondYieldPanoramaDTO listPgBondYieldPanoramaBOs(Long userId, Integer tableType, Integer tableTypeParam, Date issueDate,
                                                              Date startDate, Date endDate) {
        List<Integer> bondTypeList = userSpreadConfigDAO.getUserSpreadConfigByConfigId(userId,
                (long) UserConfigEnum.CONFIG_10001.getValue(), Integer.class).map(UserSpreadConfigBO::getConfigDetails).orElse(Lists.newArrayList());
        BondYieldTableTypeEnum spreadTypeEnum = ITextValueEnum.getEnum(BondYieldTableTypeEnum.class, tableType);
        List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList = Lists.newArrayList();
        if (Objects.equals(spreadTypeEnum, BondYieldTableTypeEnum.INTERVAL_CHANGE)) {
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                pgBondYieldPanoramaBOList = listPgBondYieldPanoramaOfChange(bondTypeList, startDate, endDate);
            } else {
                BondYieldIntervalChangeTypeEnum changeTypeEnum = ITextValueEnum.getEnum(BondYieldIntervalChangeTypeEnum.class, tableTypeParam);
                List<PgBondYieldPanoramaBO> changePanoramaList = pgBondYieldPanoramaChangeDAO.listYieldPanoramas(bondTypeList, changeTypeEnum, issueDate);
                pgBondYieldPanoramaBOList = changePanoramaList.stream()
                        .peek(change -> change.setTableType(BondYieldTableTypeEnum.INTERVAL_CHANGE.getValue())).collect(Collectors.toList());
                startDate = this.getChangeStartDate(issueDate, changeTypeEnum);
                endDate = issueDate;
            }
        } else if (Objects.equals(spreadTypeEnum, BondYieldTableTypeEnum.ABS)) {
            pgBondYieldPanoramaBOList = pgBondYieldPanoramaAbsDAO.listYieldPanoramas(bondTypeList, issueDate);
        } else if (Objects.equals(spreadTypeEnum, BondYieldTableTypeEnum.HIST_QUANTILE)) {
            // 历史分位
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                pgBondYieldPanoramaBOList = listPgBondYieldPanoramaOfQuantile(Collections.emptyList(), bondTypeList, startDate, endDate, issueDate);
            } else {
                SpreadQuantileTypeEnum quantileTypeEnum = EnumUtils.ofNullable(SpreadQuantileTypeEnum.class, tableTypeParam).orElse(SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE);
                pgBondYieldPanoramaBOList = pgBondYieldPanoramaQuantileDAO.listYieldPanoramas(bondTypeList, issueDate, quantileTypeEnum.getValue())
                        .stream().peek(quantile -> quantile.setTableType(BondYieldTableTypeEnum.HIST_QUANTILE.getValue())).collect(Collectors.toList());
                startDate = yieldSpreadCommonService.getQuantileStartDate(issueDate, quantileTypeEnum);
                endDate = issueDate;
            }

        }
        PgBondYieldPanoramaDTO pgBondYieldPanoramaDTO = new PgBondYieldPanoramaDTO();
        pgBondYieldPanoramaDTO.setPgBondYieldPanoramaBOList(fillPgBondYieldPanoramaBOS(pgBondYieldPanoramaBOList, bondTypeList));
        pgBondYieldPanoramaDTO.setStartDate(startDate);
        pgBondYieldPanoramaDTO.setEndDate(endDate);
        return pgBondYieldPanoramaDTO;
    }

    /**
     * 填充没有数值的曲线
     *
     * @param pgBondYieldPanoramaBOList 有数值的曲线数据
     * @param bondTypeList              用户所配置的债券类型列表
     * @return
     */
    private List<PgBondYieldPanoramaBO> fillPgBondYieldPanoramaBOS(List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOList, List<Integer> bondTypeList) {
        if (CollectionUtils.isEmpty(bondTypeList)) {
            return pgBondYieldPanoramaBOList;
        }

        List<Integer> curveCodeList = pgBondYieldPanoramaBOList.stream()
                .map(PgBondYieldPanoramaBO::getCurveCode).filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> noDataCurveCodeList = bondTypeList.stream()
                .flatMap(bondType -> ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType).getCurveCodes().stream())
                .filter(x -> !curveCodeList.contains(x))
                .collect(Collectors.toList());
        for (Integer noDataCurveCode : noDataCurveCodeList) {
            PgBondYieldPanoramaBO pgBondYieldPanoramaBO = new PgBondYieldPanoramaBO();
            pgBondYieldPanoramaBO.setCurveCode(noDataCurveCode);
            pgBondYieldPanoramaBOList.add(pgBondYieldPanoramaBO);
        }
        return pgBondYieldPanoramaBOList.stream().filter(x -> EnumUtils.getEnumByValue(x.getCurveCode(), YieldSpreadCurveCodeEnum.class)
                .isPresent()).collect(Collectors.toList());
    }

    @Override
    public Date maxSpreadDate() {
        BondYieldPanoramaTraceSpreadDateDTO maxSpreadDate = getMaxSpreadDate();
        return maxSpreadDate.maxSpreadDate().orElseGet(() -> {
            logger.warn("[全景利差] maxSpreadDate_empty");
            return Date.valueOf(LocalDate.now().minusDays(1));
        });
    }

    private BondYieldPanoramaTraceSpreadDateDTO getMaxSpreadDate() {
        String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String redisKey = String.format(PANORAMA_SPREAD_DATE_KEY, now);
        String spreadDateJson = stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(spreadDateJson)) {
            return JSON.parseObject(spreadDateJson, BondYieldPanoramaTraceSpreadDateDTO.class);
        }
        BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO = new BondYieldPanoramaTraceSpreadDateDTO();
        pgBondYieldPanoramaAbsDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
        pgBondYieldPanoramaQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
        pgBondYieldPanoramaChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
        if (ObjectUtils.anyNotNull(spreadDateDTO.getAbsSpreadDate(), spreadDateDTO.getChangeSpreadDate(), spreadDateDTO.getQuantileSpreadDate())) {
            stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(spreadDateDTO), TWO_DAYS, TimeUnit.DAYS);
        }
        return spreadDateDTO;
    }

    @Override
    public int syncHistBondYieldPanorama(Date startDate) {
        Date endDate = Date.valueOf(LocalDate.now());
        return this.syncBondYieldPanorama(startDate, endDate);
    }

    @Override
    public int syncHistIntervalChange(Date startDate) {
        AtomicInteger effectRows = new AtomicInteger();
        Date endDate = Date.valueOf(LocalDate.now());
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            effectRows.addAndGet(this.calIntervalChange(Date.valueOf(beginDate), Collections.emptyList()));
        }
        return effectRows.get();
    }

    @Override
    public int syncHistQuantile(Date startDate) {
        AtomicInteger effectRows = new AtomicInteger();
        Date endDate = Date.valueOf(LocalDate.now());
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            effectRows.addAndGet(this.calHistQuantile(Date.valueOf(beginDate), Collections.emptyList()));
            logger.info("syncBondYieldPanoramaQuantile 同步全景利差分位日期:{}", beginDate);
        }
        return effectRows.get();
    }

    @Override
    public int syncBondYieldSpreadPanoramaByCurveCodes(Date startDate, Date endDate, List<Integer> curveCodeList) {
        AtomicInteger effectRows = new AtomicInteger();
        startDate = Objects.isNull(startDate) ? Date.valueOf(LocalDate.now().minusDays(1)) : startDate;
        endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
        curveCodeList = CollectionUtils.isEmpty(curveCodeList) ? curveCodes : curveCodeList;
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        // 同步计算基础数据
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            Date issueDate = Date.valueOf(beginDate);
            effectRows.addAndGet(this.syncBondYieldCurve(issueDate, curveCodeList));
            effectRows.addAndGet(this.syncBondYieldUniCurve(issueDate, curveCodeList));
            effectRows.addAndGet(this.syncInduBondYieldSpread(issueDate, curveCodeList));
            effectRows.addAndGet(this.syncSecuBondYieldSpread(issueDate, curveCodeList));
            effectRows.addAndGet(this.calHistQuantile(issueDate, curveCodeList));
            effectRows.addAndGet(this.calIntervalChange(issueDate, curveCodeList));
            logger.info("syncBondYieldPanorama同步全景利差日期:{}", issueDate);
        }
        // 将最大的日期缓存起来，当查询当天的时候，需要从缓存中查询DB中最大的日期
        String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String redisKey = String.format(PANORAMA_SPREAD_DATE_KEY, now);
        BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO = new BondYieldPanoramaTraceSpreadDateDTO();
        pgBondYieldPanoramaAbsDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
        pgBondYieldPanoramaQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
        pgBondYieldPanoramaChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
        stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(spreadDateDTO), TWO_DAYS, TimeUnit.DAYS);
        return effectRows.get();
    }

    @Override
    public int syncBondYieldPanorama(Date startDate, Date endDate) {
        return this.syncBondYieldSpreadPanoramaByCurveCodes(startDate, endDate, Collections.emptyList());
    }

    private int syncInduBondYieldSpread(Date issueDate, List<Integer> curveCodeList) {
        if (CollectionUtils.isEmpty(curveCodeList)) {
            return 0;
        }
        List<Integer> curveCodes = Arrays.stream(BondImpliedRatingCurveCodeMappingEnum.values()).map(BondImpliedRatingCurveCodeMappingEnum::getCurveCode).collect(Collectors.toList());
        curveCodeList = curveCodeList.stream().filter(curveCodes::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curveCodeList)) {
            return 0;
        }
        List<PgInduBondYieldSpreadCbYieldGroupDO> induBondYieldSpreadCbYieldList =
                pgInduBondYieldSpreadDAO.listYieldSpreadCbYieldMedians(issueDate, getBondImpliedRating(), INDU1_REAL_ESTATE_CODE);
        List<PgBondYieldPanoramaAbsDO> bondYieldPanoramaAbsList = induBondYieldSpreadCbYieldList.stream().map(yieldPanorama -> {
            PgBondYieldPanoramaAbsDO bondYieldPanoramaAbsDO = BeanCopyUtils.copyProperties(yieldPanorama, PgBondYieldPanoramaAbsDO.class);
            bondYieldPanoramaAbsDO.setCurveCode(getInduBondCurveCodeByRating(yieldPanorama.getBondImpliedRatingMapping()));
            Optional<YieldSpreadCurveCodeEnum> curveCode = EnumUtils.ofNullable(YieldSpreadCurveCodeEnum.class, bondYieldPanoramaAbsDO.getCurveCode());
            bondYieldPanoramaAbsDO.setBondType(curveCode.map(code -> code.getBondType().getValue()).orElse(null));
            bondYieldPanoramaAbsDO.setIssueDate(issueDate);
            bondYieldPanoramaAbsDO.setDeleted(Deleted.NO_DELETED.getValue());
            return bondYieldPanoramaAbsDO;
        }).collect(Collectors.toList());
        return pgBondYieldPanoramaAbsDAO.saveBondYieldAbsList(issueDate, bondYieldPanoramaAbsList);
    }

    private int syncSecuBondYieldSpread(Date issueDate, List<Integer> curveCodeList) {
        List<Integer> securitySeniorityRankings = Arrays.asList(SecuritySeniorityRankingEnum.PERPETUA.getValue(), SecuritySeniorityRankingEnum.SUBORDINATED.getValue());
        List<PgSecuBondYieldSpreadCbYieldGroupDO> pgSecuBondYieldSpreadCbYields =
                pgSecuBondYieldSpreadDAO.listYieldSpreadCbYieldMedians(issueDate, securitySeniorityRankings);
        List<PgBondYieldPanoramaAbsDO> bondYieldPanoramaAbsList = pgSecuBondYieldSpreadCbYields.stream().map(yieldPanorama -> {
            int curveCode;
            if (SecuritySeniorityRankingEnum.SUBORDINATED.getValue() == yieldPanorama.getSecuritySeniorityRanking()) {
                curveCode = YieldSpreadCurveCodeEnum.SS_BONDS.getValue();
            } else {
                curveCode = YieldSpreadCurveCodeEnum.SP_BONDS.getValue();
            }
            PgBondYieldPanoramaAbsDO bondYieldPanoramaAbsDO = BeanCopyUtils.copyProperties(yieldPanorama, PgBondYieldPanoramaAbsDO.class);
            bondYieldPanoramaAbsDO.setCurveCode(curveCode);
            Optional<YieldSpreadCurveCodeEnum> curveCodeEnum = EnumUtils.ofNullable(YieldSpreadCurveCodeEnum.class, bondYieldPanoramaAbsDO.getCurveCode());
            bondYieldPanoramaAbsDO.setBondType(curveCodeEnum.map(code -> code.getBondType().getValue()).orElse(null));
            bondYieldPanoramaAbsDO.setIssueDate(issueDate);
            bondYieldPanoramaAbsDO.setDeleted(Deleted.NO_DELETED.getValue());
            return bondYieldPanoramaAbsDO;
        }).collect(Collectors.toList());
        return pgBondYieldPanoramaAbsDAO.saveBondYieldAbsList(issueDate, bondYieldPanoramaAbsList);
    }

    private int syncBondYieldCurve(Date issueDate, List<Integer> curveCodeList) {
        BondYieldCurveRequestDTO request = new BondYieldCurveRequestDTO();
        request.setIssueDate(issueDate);
        request.setCurveCodes(CollectionUtils.isEmpty(curveCodeList) ? curveCodes : curveCodeList);
        List<BondYieldCurveDTO> bondYieldCurveList = bondPriceService.listBondYieldCurves(request);
        Map<Integer, BondYieldCurveDTO> curveCodeMap = bondYieldCurveList.stream()
                .collect(Collectors.toMap(BondYieldCurveDTO::getCurveCode, Function.identity(), (o, v) -> o.getCreateTime().after(v.getCreateTime()) ? o : v));
        List<PgBondYieldPanoramaAbsDO> bondYieldPanoramaAbsList = Lists.newArrayListWithExpectedSize(bondYieldCurveList.size());
        for (Map.Entry<Integer, BondYieldCurveDTO> entry : curveCodeMap.entrySet()) {
            BondYieldCurveDTO curve = entry.getValue();
            PgBondYieldPanoramaAbsDO bondYieldPanoramaAbs = BeanCopyUtils.copyProperties(curve, PgBondYieldPanoramaAbsDO.class);
            Optional<YieldSpreadCurveCodeEnum> curveCodeEnum = EnumUtils.ofNullable(YieldSpreadCurveCodeEnum.class, bondYieldPanoramaAbs.getCurveCode());
            bondYieldPanoramaAbs.setBondType(curveCodeEnum.map(code -> code.getBondType().getValue()).orElse(null));
            bondYieldPanoramaAbs.setDeleted(Deleted.NO_DELETED.getValue());
            bondYieldPanoramaAbsList.add(bondYieldPanoramaAbs);
        }
        return pgBondYieldPanoramaAbsDAO.saveBondYieldAbsList(issueDate, bondYieldPanoramaAbsList);
    }

    private int syncBondYieldUniCurve(Date issueDate, List<Integer> curveCodeList) {
        List<Long> uniCurveCodeList = curveCodeList.stream().map(CurveUniCodeEnum::getCurveUniCodeByCurveCode)
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        List<Long> syncCurveCodes = CollectionUtils.isEmpty(uniCurveCodeList) ? this.uniCurveCodes : uniCurveCodeList;
        Map<Long, List<CurveMaturityStructureDTO>> curveMaturityStructureMap = bondPriceApolloService.getCurveMaturityStructureMap(syncCurveCodes, issueDate);
        List<PgBondYieldPanoramaAbsDO> bondYieldPanoramaAbsList = curveMaturityStructureMap.entrySet().stream().map(entry -> {
            Long curveUniCode = entry.getKey();
            List<CurveMaturityStructureDTO> curveMaturityStructureDTOList = entry.getValue();
            return fillPgBondYieldPanoramaAbsDO(curveMaturityStructureDTOList, curveUniCode, issueDate);
        }).collect(Collectors.toList());
        return pgBondYieldPanoramaAbsDAO.saveBondYieldAbsList(issueDate, bondYieldPanoramaAbsList);
    }

    private PgBondYieldPanoramaAbsDO fillPgBondYieldPanoramaAbsDO(List<CurveMaturityStructureDTO> curveMaturityStructureDTOList, Long curveUniCode, Date issueDate) {
        PgBondYieldPanoramaAbsDO bondYieldPanoramaAbs = new PgBondYieldPanoramaAbsDO();
        for (CurveMaturityStructureDTO curveMaturityStructureDTO : curveMaturityStructureDTOList) {
            if (Integer.valueOf(0).equals(curveMaturityStructureDTO.getKeyTenorStatus())) {
                String keyTenor = curveMaturityStructureDTO.getKeyTenor();
                String fieldName = YieldSpreadHelper.getUniCurveYtmInfoMap().get(keyTenor);
                if (StringUtils.isNotBlank(fieldName)) {
                    Class<? extends PgBondYieldPanoramaAbsDO> fromClass = bondYieldPanoramaAbs.getClass();
                    try {
                        String methodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        Method method = fromClass.getMethod(methodName, BigDecimal.class);
                        method.invoke(bondYieldPanoramaAbs, curveMaturityStructureDTO.getCurveYield());
                    } catch (NoSuchMethodException e) {
                        logger.error(e.getMessage());
                    } catch (Exception e) {
                        logger.error("getYieldCurveInfo error", e);
                    }
                }

            }
        }

        bondYieldPanoramaAbs.setCurveCode(CurveUniCodeEnum.getCurveCodeByCurveUniCode(curveUniCode).orElse(null));
        Optional<YieldSpreadCurveCodeEnum> curveCodeEnum = EnumUtils.ofNullable(YieldSpreadCurveCodeEnum.class, bondYieldPanoramaAbs.getCurveCode());
        bondYieldPanoramaAbs.setBondType(curveCodeEnum.map(code -> code.getBondType().getValue()).orElse(null));
        bondYieldPanoramaAbs.setIssueDate(issueDate);
        bondYieldPanoramaAbs.setDeleted(Deleted.NO_DELETED.getValue());
        return bondYieldPanoramaAbs;
    }

    private int calHistQuantile(Date issueDate, @Nullable List<Integer> curveCodeList) {
        List<PgBondYieldPanoramaQuantileDO> dataList = Lists.newArrayList();
        for (SpreadQuantileTypeEnum quantileTypeEnum : SpreadQuantileTypeEnum.values()) {
            // 3年历史分位,5年历史分位
            Date startDate = yieldSpreadCommonService.getQuantileStartDate(issueDate, quantileTypeEnum);
            //速度慢，优化速度
            // pgBondYieldPanoramaQuantileViewDAO.createPanoramaQuantileView(startDate, issueDate, curveCodeList);
            // List<PgBondYieldPanoramaQuantileViewDO> bondYieldPanoramaQuantileViewList = pgBondYieldPanoramaQuantileViewDAO.listBondYieldPanoramaQuantiles(issueDate);
            List<PgBondYieldPanoramaBO> pgBondYieldPanoramaBOS = this.listPgBondYieldPanoramaOfQuantile(curveCodeList, Collections.emptyList(), startDate, issueDate, issueDate);
            for (PgBondYieldPanoramaBO pgBondYieldPanoramaQuantileViewDO : pgBondYieldPanoramaBOS) {
                PgBondYieldPanoramaQuantileDO bondYieldPanoramaQuantile = BeanCopyUtils.copyProperties(pgBondYieldPanoramaQuantileViewDO, PgBondYieldPanoramaQuantileDO.class);
                Optional<YieldSpreadCurveCodeEnum> curveCodeEnum = EnumUtils.ofNullable(YieldSpreadCurveCodeEnum.class, bondYieldPanoramaQuantile.getCurveCode());
                bondYieldPanoramaQuantile.setBondType(curveCodeEnum.map(code -> code.getBondType().getValue()).orElse(null));
                bondYieldPanoramaQuantile.setQuantileType(quantileTypeEnum.getValue());
                bondYieldPanoramaQuantile.setStartDate(startDate);
                bondYieldPanoramaQuantile.setDeleted(Deleted.NO_DELETED.getValue());
                dataList.add(bondYieldPanoramaQuantile);
            }
        }
        return pgBondYieldPanoramaQuantileDAO.saveBondYieldPanoramaList(issueDate, dataList);
    }

    private int calIntervalChange(Date issueDate, @Nullable List<Integer> curveCodeList) {
        List<PgBondYieldPanoramaAbsDO> pgBondYieldPanoramaAbsList = pgBondYieldPanoramaAbsDAO.listBondYieldPanoramaQuantiles(null, issueDate);
        if (CollectionUtils.isEmpty(pgBondYieldPanoramaAbsList)) {
            return 0;
        }
        List<PgBondYieldPanoramaChangeDO> dataList = Lists.newArrayList();
        for (BondYieldIntervalChangeTypeEnum changeType : BondYieldIntervalChangeTypeEnum.values()) {
            Date startDate = this.getChangeStartDate(issueDate, changeType);
            List<PgBondYieldPanoramaAbsDO> beforeBondYieldPanoramaAbsList = pgBondYieldPanoramaAbsDAO.listBondYieldPanoramaQuantilesByCurveCodes(curveCodeList, startDate);
            if (CollectionUtils.isEmpty(beforeBondYieldPanoramaAbsList)) {
                continue;
            }
            Map<Integer, PgBondYieldPanoramaAbsDO> beforeCurveCodeMap =
                    beforeBondYieldPanoramaAbsList.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
            Map<Integer, PgBondYieldPanoramaAbsDO> currentCurveCodeMap =
                    pgBondYieldPanoramaAbsList.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
            for (Map.Entry<Integer, PgBondYieldPanoramaAbsDO> curentEntry : currentCurveCodeMap.entrySet()) {
                PgBondYieldPanoramaAbsDO beforeAbs = beforeCurveCodeMap.get(curentEntry.getKey());
                Optional<PgBondYieldPanoramaChangeDO> change =
                        this.buildBondYieldPanoramaChange(curentEntry.getValue(), beforeAbs, changeType.getValue(), startDate);
                change.ifPresent(dataList::add);
            }
        }
        return pgBondYieldPanoramaChangeDAO.saveBondYieldPanoramaChangeList(issueDate, dataList);
    }

    private Date getChangeStartDate(Date issueDate, BondYieldIntervalChangeTypeEnum changeType) {
        LocalDate startDate = changeType.getIntervalStartDate(issueDate.toLocalDate());
        // 获取最近一天的工作日，包含自己
        Date workDate = holidayService.latestWorkDay(Date.valueOf(startDate), 0);
        Date minStartDate = changeType.getIntervalMinStartDate(startDate);
        return workDate.before(minStartDate) ? minStartDate : workDate;
    }

    private Optional<PgBondYieldPanoramaChangeDO> buildBondYieldPanoramaChange(PgBondYieldPanoramaAbsDO currentAbs, PgBondYieldPanoramaAbsDO beforeAbs,
                                                                               Integer changeType, Date startDate) {
        if (Objects.isNull(currentAbs) || Objects.isNull(beforeAbs)) {
            return Optional.empty();
        }
        PgBondYieldPanoramaChangeDO pgBondYieldPanoramaChangeDO = new PgBondYieldPanoramaChangeDO();
        pgBondYieldPanoramaChangeDO.setCurveCode(currentAbs.getCurveCode());
        Optional<YieldSpreadCurveCodeEnum> curveCodeEnum = EnumUtils.ofNullable(YieldSpreadCurveCodeEnum.class, currentAbs.getCurveCode());
        pgBondYieldPanoramaChangeDO.setBondType(curveCodeEnum.map(code -> code.getBondType().getValue()).orElse(null));
        pgBondYieldPanoramaChangeDO.setYtm1M(this.safeSubtract(currentAbs.getYtm1M(), beforeAbs.getYtm1M()));
        pgBondYieldPanoramaChangeDO.setYtm3M(this.safeSubtract(currentAbs.getYtm3M(), beforeAbs.getYtm3M()));
        pgBondYieldPanoramaChangeDO.setYtm6M(this.safeSubtract(currentAbs.getYtm6M(), beforeAbs.getYtm6M()));
        pgBondYieldPanoramaChangeDO.setYtm9M(this.safeSubtract(currentAbs.getYtm9M(), beforeAbs.getYtm9M()));
        pgBondYieldPanoramaChangeDO.setYtm1Y(this.safeSubtract(currentAbs.getYtm1Y(), beforeAbs.getYtm1Y()));
        pgBondYieldPanoramaChangeDO.setYtm2Y(this.safeSubtract(currentAbs.getYtm2Y(), beforeAbs.getYtm2Y()));
        pgBondYieldPanoramaChangeDO.setYtm3Y(this.safeSubtract(currentAbs.getYtm3Y(), beforeAbs.getYtm3Y()));
        pgBondYieldPanoramaChangeDO.setYtm4Y(this.safeSubtract(currentAbs.getYtm4Y(), beforeAbs.getYtm4Y()));
        pgBondYieldPanoramaChangeDO.setYtm5Y(this.safeSubtract(currentAbs.getYtm5Y(), beforeAbs.getYtm5Y()));
        pgBondYieldPanoramaChangeDO.setYtm6Y(this.safeSubtract(currentAbs.getYtm6Y(), beforeAbs.getYtm6Y()));
        pgBondYieldPanoramaChangeDO.setYtm7Y(this.safeSubtract(currentAbs.getYtm7Y(), beforeAbs.getYtm7Y()));
        pgBondYieldPanoramaChangeDO.setYtm10Y(this.safeSubtract(currentAbs.getYtm10Y(), beforeAbs.getYtm10Y()));
        pgBondYieldPanoramaChangeDO.setYtm15Y(this.safeSubtract(currentAbs.getYtm15Y(), beforeAbs.getYtm15Y()));
        pgBondYieldPanoramaChangeDO.setYtm20Y(this.safeSubtract(currentAbs.getYtm20Y(), beforeAbs.getYtm20Y()));
        pgBondYieldPanoramaChangeDO.setYtm30Y(this.safeSubtract(currentAbs.getYtm30Y(), beforeAbs.getYtm30Y()));
        pgBondYieldPanoramaChangeDO.setYtm50Y(this.safeSubtract(currentAbs.getYtm50Y(), beforeAbs.getYtm50Y()));
        pgBondYieldPanoramaChangeDO.setIssueDate(currentAbs.getIssueDate());
        pgBondYieldPanoramaChangeDO.setDeleted(Deleted.NO_DELETED.getValue());
        pgBondYieldPanoramaChangeDO.setChangeType(changeType);
        pgBondYieldPanoramaChangeDO.setStartDate(startDate);
        return Optional.of(pgBondYieldPanoramaChangeDO);
    }

    private BigDecimal safeSubtract(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return null;
        }
        return value1.subtract(value2).multiply(ONE_HUNDRED).setScale(PANORAMA_CHANGE_SCALE, RoundingMode.HALF_UP);
    }

}
