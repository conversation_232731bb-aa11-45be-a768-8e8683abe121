package com.innodealing.onshore.yieldspread.model.dto;

import com.innodealing.onshore.yieldspread.builder.InduBondYieldSpreadParamBuilder;
import com.innodealing.onshore.yieldspread.enums.SpreadCurveTypeEnum;

import java.io.Serializable;
import java.sql.Date;

import static java.util.Objects.isNull;

/**
 * 行业债券利差查询参数DTO
 *
 * <AUTHOR>
 */
public class InduBondYieldSpreadParamDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 利差类型 1. 信用利差，2. 超额利差，3. 估值收益率
     */
    private SpreadCurveTypeEnum spreadCurveType;
    /**
     * 是否展示国开基准曲线，是|true, 否|false
     */
    private Boolean displayCdbBenchmarkCurve;
    /**
     * 主体唯一编码集合
     */
    private Long[] comUniCodes;
    /**
     * 债券唯一编码集合
     */
    private Long[] bondUniCodes;
    /**
     * 一级行业编码
     */
    private Long industryCode1;
    /**
     * 二级行业编码
     */
    private Long industryCode2;
    /**
     * 债券类型: 1公募; 0私募; 2永续债
     */
    private Integer spreadBondType;
    /**
     * 债项评级 AAA,AA+,AA)
     */
    private Integer bondExtRatingMapping;
    /**
     * 隐含评级(AAA+,AAA,AAA-,AA+,AA,AA-,A+,A,A-)
     */
    private Integer[] bondImpliedRatingMappings;
    /**
     * yy评级(投资级:1、2、3、4、5,投机级:6、7、8、风险级:9、10)
     */
    private Integer[] comYyRatingMappings;
    /**
     * 隐含评级标签
     */
    private Integer bondImpliedRatingMappingTag;
    /**
     * yy评级标签
     */
    private Integer comYyRatingMappingTag;
    /**
     * 企业性质 1央企,2国企,6民企
     */
    private Integer[] businessFilterNatures;
    /**
     * 剩余期限(1,2,3,4,5)
     */
    private Integer spreadRemainingTenorTag;
    /**
     * 担保状态: 0: 无, 1: 有
     */
    protected Integer guaranteeStatus;
    /**
     * 主体唯一编码
     */
    private Long comUniCode;
    /**
     * 债券唯一编码
     */
    private Long bondUniCode;
    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 开始利差日期
     */
    private Date startSpreadDate;
    /**
     * 结束利差日期
     */
    private Date endSpreadDate;
    /**
     * 排序字段
     */
    private SortDTO sort;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页数据量
     */
    private Integer pageSize;

    public SpreadCurveTypeEnum getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(SpreadCurveTypeEnum spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public Boolean getDisplayCdbBenchmarkCurve() {
        return displayCdbBenchmarkCurve;
    }

    public void setDisplayCdbBenchmarkCurve(Boolean displayCdbBenchmarkCurve) {
        this.displayCdbBenchmarkCurve = displayCdbBenchmarkCurve;
    }

    public Long[] getComUniCodes() {
        return isNull(comUniCodes) ? new Long[0] : comUniCodes.clone();
    }

    public void setComUniCodes(Long[] comUniCodes) {
        this.comUniCodes = isNull(comUniCodes) ? new Long[0] : comUniCodes.clone();
    }

    public Long[] getBondUniCodes() {
        return isNull(bondUniCodes) ? new Long[0] : bondUniCodes.clone();
    }

    public void setBondUniCodes(Long[] bondUniCodes) {
        this.bondUniCodes = isNull(bondUniCodes) ? new Long[0] : bondUniCodes.clone();
    }

    public Long getIndustryCode1() {
        return industryCode1;
    }

    public void setIndustryCode1(Long industryCode1) {
        this.industryCode1 = industryCode1;
    }

    public Long getIndustryCode2() {
        return industryCode2;
    }

    public void setIndustryCode2(Long industryCode2) {
        this.industryCode2 = industryCode2;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer[] getBusinessFilterNatures() {
        return isNull(businessFilterNatures) ? new Integer[0] : businessFilterNatures.clone();
    }

    public void setBusinessFilterNatures(Integer[] businessFilterNatures) {
        this.businessFilterNatures = isNull(businessFilterNatures) ? new Integer[0] : businessFilterNatures.clone();
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getGuaranteeStatus() {
        return guaranteeStatus;
    }

    public void setGuaranteeStatus(Integer guaranteeStatus) {
        this.guaranteeStatus = guaranteeStatus;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Date getSpreadDate() {
        return isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getStartSpreadDate() {
        return isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public void setStartSpreadDate(Date startSpreadDate) {
        this.startSpreadDate = isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public Date getEndSpreadDate() {
        return isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public void setEndSpreadDate(Date endSpreadDate) {
        this.endSpreadDate = isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public Integer getBondImpliedRatingMappingTag() {
        return bondImpliedRatingMappingTag;
    }

    public void setBondImpliedRatingMappingTag(Integer bondImpliedRatingMappingTag) {
        this.bondImpliedRatingMappingTag = bondImpliedRatingMappingTag;
    }

    public Integer getComYyRatingMappingTag() {
        return comYyRatingMappingTag;
    }

    public void setComYyRatingMappingTag(Integer comYyRatingMappingTag) {
        this.comYyRatingMappingTag = comYyRatingMappingTag;
    }

    public Integer[] getBondImpliedRatingMappings() {
        return isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public void setBondImpliedRatingMappings(Integer[] bondImpliedRatingMappings) {
        this.bondImpliedRatingMappings = isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public Integer[] getComYyRatingMappings() {
        return isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public void setComYyRatingMappings(Integer[] comYyRatingMappings) {
        this.comYyRatingMappings = isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    /**
     * 获取债券收益率构造器
     *
     * @return {@link InduBondYieldSpreadParamBuilder} 债券收益率构造器
     */
    public static InduBondYieldSpreadParamBuilder builder() {
        return new InduBondYieldSpreadParamBuilder();
    }
}
