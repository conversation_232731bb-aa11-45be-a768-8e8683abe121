package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayStringJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 金融主体利差
 *
 * <AUTHOR>
 */
public class BaseFinanceComYieldSpreadResDTO {

    @ApiModelProperty("发行人代码")
    private Long comUniCode;

    @ApiModelProperty("利差日期")
    private Date spreadDate;

    @ApiModelProperty("发行人名称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String comUniName;

    @ApiModelProperty("主体评级")
    private Integer comExtRatingMapping;

    @ApiModelProperty("主体评级")
    private String comExtRatingMappingStr;

    @ApiModelProperty("所属行业")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String induLevel2Name;

    @ApiModelProperty("总资产(亿)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal totalAssets;

    @ApiModelProperty("净利润(亿)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal netProfit;

    @ApiModelProperty("主体信用利差(普通);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comSeniorCreditSpread;

    @ApiModelProperty("主体超额利差(普通);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comSeniorExcessSpread;

    @ApiModelProperty("主体信用利差(全部债券);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comCreditSpread;

    @ApiModelProperty("主体信用利差 近三月变动(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadChange3M;

    @ApiModelProperty("主体信用利差 近六月变动(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadChange6M;

    @ApiModelProperty("信用利差3年历史分位")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadQuantile3Y;

    @ApiModelProperty("信用利差5年历史分位")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadQuantile5Y;

    @ApiModelProperty("主体信用利差(永续);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comPerpetualCreditSpread;

    @ApiModelProperty("主体超额利差(全部债券);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comExcessSpread;

    @ApiModelProperty("主体超额利差 近三月变动(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadChange3M;

    @ApiModelProperty("主体超额利差 近六月变动(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadChange6M;

    @ApiModelProperty("超额利差3年历史分位")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadQuantile3Y;

    @ApiModelProperty("超额利差5年历史分位")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadQuantile5Y;

    @ApiModelProperty("主体超额利差(永续);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comPerpetualExcessSpread;

    @ApiModelProperty("主体估值收益率(普通);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comSeniorCbYield;

    @ApiModelProperty("主体估值收益率(全部债券);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comCbYield;

    @ApiModelProperty("主体估值收益率(永续);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comPerpetualCbYield;

    @ApiModelProperty("曲线id")
    private Long curveId;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public String getComExtRatingMappingStr() {
        return comExtRatingMappingStr;
    }

    public void setComExtRatingMappingStr(String comExtRatingMappingStr) {
        this.comExtRatingMappingStr = comExtRatingMappingStr;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    public BigDecimal getComSeniorCreditSpread() {
        return comSeniorCreditSpread;
    }

    public void setComSeniorCreditSpread(BigDecimal comSeniorCreditSpread) {
        this.comSeniorCreditSpread = comSeniorCreditSpread;
    }

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getComSeniorExcessSpread() {
        return comSeniorExcessSpread;
    }

    public void setComSeniorExcessSpread(BigDecimal comSeniorExcessSpread) {
        this.comSeniorExcessSpread = comSeniorExcessSpread;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public BigDecimal getComSeniorCbYield() {
        return comSeniorCbYield;
    }

    public void setComSeniorCbYield(BigDecimal comSeniorCbYield) {
        this.comSeniorCbYield = comSeniorCbYield;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public Long getCurveId() {
        return curveId;
    }

    public void setCurveId(Long curveId) {
        this.curveId = curveId;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }
}
