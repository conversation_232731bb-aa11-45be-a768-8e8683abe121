package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.LgBondYieldSpreadDO;
import org.apache.ibatis.annotations.Param;

/**
 * 地方债利差Mapper
 * <AUTHOR>
 * @create: 2024-10-24
 */
public interface LgBondYieldSpreadMapper extends DynamicQueryMapper<LgBondYieldSpreadDO> {

    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    void createShardingTable(@Param("tableName") String tableName);

}
