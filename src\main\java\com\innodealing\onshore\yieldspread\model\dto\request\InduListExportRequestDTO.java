package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 行业-导出成分数据请求DTO
 *
 * <AUTHOR>
 * @date 2022-09-26
 */
public class InduListExportRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("2. 组合查询集合")
    private List<InduCurveCompositionConditionDTO> compositionConditions;
    @ApiModelProperty(value = "利差日期", example = "2020-04-07", required = true)
    protected Date spreadDate;
    @ApiModelProperty("排序")
    protected SortDTO sort;

    public List<InduCurveCompositionConditionDTO> getCompositionConditions() {
        return Objects.isNull(compositionConditions) ? new ArrayList<>() : new ArrayList<>(compositionConditions);
    }

    public void setCompositionConditions(List<InduCurveCompositionConditionDTO> compositionConditions) {
        this.compositionConditions = Objects.isNull(compositionConditions) ? new ArrayList<>() : new ArrayList<>(compositionConditions);
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }
}
