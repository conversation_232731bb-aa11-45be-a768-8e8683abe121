package com.innodealing.onshore.yieldspread.router.shard;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 隐含评级 router组合类
 *
 * <AUTHOR>
 */
@JSONType(serialzeFeatures = SerializerFeature.SortField)
@SuppressWarnings({"squid:S2162"})
public class ImplicitRatingRouter extends AbstractRatingRouter {

    @JSONField(ordinal = 1)
    private Integer tripleAPlus;
    @JSONField(ordinal = 2)
    private Integer tripleA;
    @JSONField(ordinal = 3)
    private Integer tripleASub;
    @JSONField(ordinal = 4)
    private Integer doubleAPlus;
    @JSONField(ordinal = 5)
    private Integer doubleA;
    @JSONField(ordinal = 6)
    private Integer doubleA2;
    @JSONField(ordinal = 7)
    private Integer doubleASub;
    @JSONField(ordinal = 8)
    private Integer oneAPlus;
    @JSONField(ordinal = 9)
    private Integer oneA;
    @JSONField(ordinal = 10)
    private Integer oneASub;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ImplicitRatingRouter)) {
            return false;
        }
        ImplicitRatingRouter that = (ImplicitRatingRouter) o;
        return Objects.equals(getSpreadDateRange(), that.getSpreadDateRange()) && Objects.equals(getLevel(), that.getLevel()) &&
                Objects.equals(getTripleAPlus(), that.getTripleAPlus()) && Objects.equals(getTripleA(), that.getTripleA()) &&
                Objects.equals(getTripleASub(), that.getTripleASub()) && Objects.equals(getDoubleAPlus(), that.getDoubleAPlus()) &&
                Objects.equals(getDoubleA(), that.getDoubleA()) && Objects.equals(getDoubleA2(), that.getDoubleA2()) &&
                Objects.equals(getDoubleASub(), that.getDoubleASub()) && Objects.equals(getOneAPlus(), that.getOneAPlus()) &&
                Objects.equals(getOneA(), that.getOneA()) && Objects.equals(getOneASub(), that.getOneASub());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getSpreadDateRange(), getLevel(),
                getTripleAPlus(), getTripleA(), getTripleASub(),
                getDoubleAPlus(), getDoubleA(), getDoubleA2(), getDoubleASub(),
                getOneAPlus(), getOneA(), getOneASub());
    }

    public Integer getTripleAPlus() {
        return tripleAPlus;
    }

    public void setTripleAPlus(Integer tripleAPlus) {
        this.tripleAPlus = tripleAPlus;
    }

    public Integer getTripleA() {
        return tripleA;
    }

    public void setTripleA(Integer tripleA) {
        this.tripleA = tripleA;
    }

    public Integer getTripleASub() {
        return tripleASub;
    }

    public void setTripleASub(Integer tripleASub) {
        this.tripleASub = tripleASub;
    }

    public Integer getDoubleAPlus() {
        return doubleAPlus;
    }

    public void setDoubleAPlus(Integer doubleAPlus) {
        this.doubleAPlus = doubleAPlus;
    }

    public Integer getDoubleA() {
        return doubleA;
    }

    public void setDoubleA(Integer doubleA) {
        this.doubleA = doubleA;
    }

    public Integer getDoubleA2() {
        return doubleA2;
    }

    public void setDoubleA2(Integer doubleA2) {
        this.doubleA2 = doubleA2;
    }

    public Integer getDoubleASub() {
        return doubleASub;
    }

    public void setDoubleASub(Integer doubleASub) {
        this.doubleASub = doubleASub;
    }

    public Integer getOneAPlus() {
        return oneAPlus;
    }

    public void setOneAPlus(Integer oneAPlus) {
        this.oneAPlus = oneAPlus;
    }

    public Integer getOneA() {
        return oneA;
    }

    public void setOneA(Integer oneA) {
        this.oneA = oneA;
    }

    public Integer getOneASub() {
        return oneASub;
    }

    public void setOneASub(Integer oneASub) {
        this.oneASub = oneASub;
    }


    @Override
    public List<Integer> getRatings() {
        return implicitRatings();
    }

    private List<Integer> implicitRatings() {
        return Stream.of(this.getTripleAPlus(), this.getTripleA(), this.getTripleASub(),
                        this.getDoubleAPlus(), this.getDoubleA(), this.getDoubleA2(), this.getDoubleASub(),
                        this.getOneAPlus(), this.getOneA(), this.getOneASub())
                .filter(Objects::nonNull).collect(Collectors.toList());

    }


    @Override
    public int compareTo(AbstractRatingRouter o) {
        int x = this.hashCode();
        int y = o.hashCode();
        return Integer.compare(x, y);
    }
}
