package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.dto.udic.UdicComInfoForSpreadDTO;
import com.innodealing.onshore.bondmetadata.enums.UrbanInvestEnum;
import com.innodealing.onshore.yieldspread.model.dto.DmComInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 城投服务
 *
 * <AUTHOR>
 **/
@FeignClient(name = "udicInfoService", url = "${udic.service.api.url}", path = "/internal")
public interface UdicInfoService {
    /**
     * 根据主体编码返回城投主体信息
     *
     * @param comUniCodes 主体编码集合
     * @return 城投主体信息
     */
    @PostMapping("com-info/listComInfoForSpreads")
    List<UdicComInfoForSpreadDTO> listComInfoForSpreadDTOs(@RequestBody List<Long> comUniCodes);

    /**
     * 查询所有dm城投的主体信息
     *
     * @return 城投主体数据
     */
    @PostMapping("dm-com-info/list/all")
    List<DmComInfoDTO> listDmComInfoAll();

    /**
     * 根据主体编码返回城投主体信息
     *
     * @param comUniCodes 发行人唯一代码
     * @return key 发行人唯一代码,value 城投主体信息
     */
    default Map<Long, UdicComInfoForSpreadDTO> getComInfoForSpreadDTOMap(Set<Long> comUniCodes) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return Collections.emptyMap();
        }
        return listComInfoForSpreadDTOs(new ArrayList<>(comUniCodes)).stream()
                .collect(Collectors.toMap(UdicComInfoForSpreadDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 获取dm口径城投所有主体编码 comUniCode 列表
     *
     * @return 所有主体编码 comUniCode 列表
     */
    default Set<Long> getDmComUniCodeList() {
        List<DmComInfoDTO> dmComInfoDTOs = listDmComInfoAll();
        return dmComInfoDTOs.stream().
                filter(dmComInfoDTO -> Integer.valueOf(UrbanInvestEnum.URBAN_INVEST.getValue()).equals(dmComInfoDTO.getUdicStatus())).
                map(DmComInfoDTO::getComUniCode).
                collect(Collectors.toSet());
    }
}
