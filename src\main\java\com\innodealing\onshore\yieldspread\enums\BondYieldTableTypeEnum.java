package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 债券收益率table 类型 1 绝对值 2 3年历史分位 3 区间变动
 *
 * <AUTHOR>
 */
public enum BondYieldTableTypeEnum implements ITextValueEnum {
    /**
     * 债券收益率table 类型 1 绝对值 2 3年历史分位 3 区间变动
     */
    ABS(1, "到期收益率", ""),
    HIST_QUANTILE(2, "历史分位", "three"),
    INTERVAL_CHANGE(3, "区间变动", "change");

    private final int value;
    private final String text;

    private final String excelPlaceholder;

    BondYieldTableTypeEnum(Integer value, String text, String excelPlaceholder) {
        this.value = value;
        this.text = text;
        this.excelPlaceholder = excelPlaceholder;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }

    public String getExcelPlaceholder() {
        return excelPlaceholder;
    }
}
