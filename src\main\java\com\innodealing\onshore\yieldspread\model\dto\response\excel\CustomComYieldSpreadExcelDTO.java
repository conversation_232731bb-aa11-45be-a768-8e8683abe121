package com.innodealing.onshore.yieldspread.model.dto.response.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.innodealing.international.common.template.source.excel.convert.ExcelDateConvert;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.CURVE_EXPORT_TITLE;

/**
 * 自定义曲线主体利差
 *
 * <AUTHOR>
 */
public class CustomComYieldSpreadExcelDTO extends BaseCurveYieldSpreadExcelDTO {

    @ApiModelProperty("发行人")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "发行人"})
    @ColumnWidth(25)
    private String comUniName;

    @ApiModelProperty("利差日期")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "利差日期"}, converter = ExcelDateConvert.class)
    @ColumnWidth(25)
    private Date spreadDate;

    @ApiModelProperty("主体评级")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "主体评级"})
    @ColumnWidth(25)
    private String comExtRatingMappingStr;

    @ApiModelProperty("总资产(亿)")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "总资产(亿)"})
    @ColumnWidth(25)
    private BigDecimal totalAssets;

    @ApiModelProperty("信用利差(BP)-全部债券")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)-全部债券"})
    @ColumnWidth(25)
    private BigDecimal comCreditSpread;

    @ApiModelProperty("超额利差(BP)-全部债券")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)-全部债券"})
    @ColumnWidth(25)
    private BigDecimal comExcessSpread;

    @ApiModelProperty("估值收益率-全部债券")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率-全部债券"})
    @ColumnWidth(25)
    private BigDecimal comCbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public String getComExtRatingMappingStr() {
        return comExtRatingMappingStr;
    }

    public void setComExtRatingMappingStr(String comExtRatingMappingStr) {
        this.comExtRatingMappingStr = comExtRatingMappingStr;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

}
