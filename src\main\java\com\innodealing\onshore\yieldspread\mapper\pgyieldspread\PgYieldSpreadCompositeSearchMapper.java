package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * Pg利差综合查询
 *
 * <AUTHOR>
 */
@Mapper
public interface PgYieldSpreadCompositeSearchMapper {

    /**
     * 根据bondUniCode获取债券利差，会涉及到城投、产业、银行、证券
     *
     * @param bondUniCodes 债券code
     * @param spreadDate   利差日期
     * @return 利差数据
     */
    List<BondYieldSpreadBO> listBondYieldSpreads(@Param("bondUniCodes") Set<Long> bondUniCodes, @Param("spreadDate") Date spreadDate);

    /**
     * 设置pg超时时间
     *
     * @param timeOut 超时时间
     */
    void setStatementTimeout(@Param("timeOut") long timeOut);

    /**
     * 获取pg超时时间
     *
     * @return 超时时间
     */
    String getStatementTimeout();

}
