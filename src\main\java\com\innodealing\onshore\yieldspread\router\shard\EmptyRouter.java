package com.innodealing.onshore.yieldspread.router.shard;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveShardEnum;

import java.util.Collections;
import java.util.List;

/**
 * 利差曲线路由空实现， 默认逻辑走all
 * 无评级分片
 * <AUTHOR>
 */
@JSONType(serialzeFeatures = SerializerFeature.SortField)
public class EmptyRouter extends AbstractRatingRouter {
    @JSONField(serialize = false)
    private final String level = YieldSpreadCurveShardEnum.ALL.getText();

    @Override
    public String getLevel() {
        return level;
    }

    @Override
    public void setLevel(String level) {
        throw new IllegalArgumentException("EmptyRouter 不允许设置level");
    }


    @Override
    public int compareTo(AbstractRatingRouter o) {
        return 0;
    }

    @Override
    public List<Integer> getRatings() {
        return Collections.emptyList();
    }
}
