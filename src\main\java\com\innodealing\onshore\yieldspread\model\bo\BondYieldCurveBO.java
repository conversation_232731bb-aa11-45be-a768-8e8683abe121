package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 收益率曲线BO
 *
 * <AUTHOR>
 */
public class BondYieldCurveBO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 曲线代码
     */
    private Integer curveCode;
    /**
     * 曲线名称
     */
    private String curveName;
    /**
     * 刪除状态 (0 未刪除， 1 已刪除)
     */
    private Integer deleted;
    /**
     * 0月到期收益率
     */
    private BigDecimal ytm0M;
    /**
     * 1月到期收益率
     */
    private BigDecimal ytm1M;
    /**
     * 2月到期收益率
     */
    private BigDecimal ytm2M;
    /**
     * 3月到期收益率
     */
    private BigDecimal ytm3M;
    /**
     * 6月到期收益率
     */
    private BigDecimal ytm6M;
    /**
     * 9月到期收益率
     */
    private BigDecimal ytm9M;
    /**
     * 1年到期收益率
     */
    private BigDecimal ytm1Y;
    /**
     * 2年到期收益率
     */
    private BigDecimal ytm2Y;
    /**
     * 3年到期收益率
     */
    private BigDecimal ytm3Y;
    /**
     * 4年到期收益率
     */
    private BigDecimal ytm4Y;
    /**
     * 5年到期收益率
     */
    private BigDecimal ytm5Y;
    /**
     * 6年到期收益率
     */
    private BigDecimal ytm6Y;
    /**
     * 7年到期收益率
     */
    private BigDecimal ytm7Y;
    /**
     * 8年到期收益率
     */
    private BigDecimal ytm8Y;
    /**
     * 9年到期收益率
     */
    private BigDecimal ytm9Y;
    /**
     * 10年到期收益率
     */
    private BigDecimal ytm10Y;
    /**
     * 15年到期收益率
     */
    private BigDecimal ytm15Y;
    /**
     * 20年到期收益率
     */
    private BigDecimal ytm20Y;
    /**
     * 30年到期收益率
     */
    private BigDecimal ytm30Y;
    /**
     * 40年到期收益率
     */
    private BigDecimal ytm40Y;
    /**
     * 50年到期收益率
     */
    private BigDecimal ytm50Y;
    /**
     * 发布日期
     */
    private Date issueDate;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCurveCode() {
        return curveCode;
    }

    public void setCurveCode(Integer curveCode) {
        this.curveCode = curveCode;
    }

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public BigDecimal getYtm0M() {
        return ytm0M;
    }

    public void setYtm0M(BigDecimal ytm0M) {
        this.ytm0M = ytm0M;
    }

    public BigDecimal getYtm1M() {
        return ytm1M;
    }

    public void setYtm1M(BigDecimal ytm1M) {
        this.ytm1M = ytm1M;
    }

    public BigDecimal getYtm2M() {
        return ytm2M;
    }

    public void setYtm2M(BigDecimal ytm2M) {
        this.ytm2M = ytm2M;
    }

    public BigDecimal getYtm3M() {
        return ytm3M;
    }

    public void setYtm3M(BigDecimal ytm3M) {
        this.ytm3M = ytm3M;
    }

    public BigDecimal getYtm6M() {
        return ytm6M;
    }

    public void setYtm6M(BigDecimal ytm6M) {
        this.ytm6M = ytm6M;
    }

    public BigDecimal getYtm9M() {
        return ytm9M;
    }

    public void setYtm9M(BigDecimal ytm9M) {
        this.ytm9M = ytm9M;
    }

    public BigDecimal getYtm1Y() {
        return ytm1Y;
    }

    public void setYtm1Y(BigDecimal ytm1Y) {
        this.ytm1Y = ytm1Y;
    }

    public BigDecimal getYtm2Y() {
        return ytm2Y;
    }

    public void setYtm2Y(BigDecimal ytm2Y) {
        this.ytm2Y = ytm2Y;
    }

    public BigDecimal getYtm3Y() {
        return ytm3Y;
    }

    public void setYtm3Y(BigDecimal ytm3Y) {
        this.ytm3Y = ytm3Y;
    }

    public BigDecimal getYtm4Y() {
        return ytm4Y;
    }

    public void setYtm4Y(BigDecimal ytm4Y) {
        this.ytm4Y = ytm4Y;
    }

    public BigDecimal getYtm5Y() {
        return ytm5Y;
    }

    public void setYtm5Y(BigDecimal ytm5Y) {
        this.ytm5Y = ytm5Y;
    }

    public BigDecimal getYtm6Y() {
        return ytm6Y;
    }

    public void setYtm6Y(BigDecimal ytm6Y) {
        this.ytm6Y = ytm6Y;
    }

    public BigDecimal getYtm7Y() {
        return ytm7Y;
    }

    public void setYtm7Y(BigDecimal ytm7Y) {
        this.ytm7Y = ytm7Y;
    }

    public BigDecimal getYtm8Y() {
        return ytm8Y;
    }

    public void setYtm8Y(BigDecimal ytm8Y) {
        this.ytm8Y = ytm8Y;
    }

    public BigDecimal getYtm9Y() {
        return ytm9Y;
    }

    public void setYtm9Y(BigDecimal ytm9Y) {
        this.ytm9Y = ytm9Y;
    }

    public BigDecimal getYtm10Y() {
        return ytm10Y;
    }

    public void setYtm10Y(BigDecimal ytm10Y) {
        this.ytm10Y = ytm10Y;
    }

    public BigDecimal getYtm15Y() {
        return ytm15Y;
    }

    public void setYtm15Y(BigDecimal ytm15Y) {
        this.ytm15Y = ytm15Y;
    }

    public BigDecimal getYtm20Y() {
        return ytm20Y;
    }

    public void setYtm20Y(BigDecimal ytm20Y) {
        this.ytm20Y = ytm20Y;
    }

    public BigDecimal getYtm30Y() {
        return ytm30Y;
    }

    public void setYtm30Y(BigDecimal ytm30Y) {
        this.ytm30Y = ytm30Y;
    }

    public BigDecimal getYtm40Y() {
        return ytm40Y;
    }

    public void setYtm40Y(BigDecimal ytm40Y) {
        this.ytm40Y = ytm40Y;
    }

    public BigDecimal getYtm50Y() {
        return ytm50Y;
    }

    public void setYtm50Y(BigDecimal ytm50Y) {
        this.ytm50Y = ytm50Y;
    }

    public Date getIssueDate() {
        return issueDate == null ? null : new Date(issueDate.getTime());
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate == null ? null : new Date(issueDate.getTime());
    }

    public Timestamp getCreateTime() {
        return createTime == null ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime == null ? null : new Timestamp(createTime.getTime());
    }

    public Timestamp getUpdateTime() {
        return updateTime == null ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime == null ? null : new Timestamp(updateTime.getTime());
    }

}