package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.BusinessFilterNatureEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.enums.InsuSeniorityRankingEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadFinancialBondImpliedRatingMappingEnum;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * 保险利差曲线生成条件
 *
 * <AUTHOR>
 */
public class InsuCurveGenerateConditionReqDTO extends AbstractCurveGenerateConditionReqDTO {

    private static final String CURVE_NAME_PREFIX = "保险";

    private static final Map<Integer, String> BOND_TYPE_NAME_MAP = initBondTypeNameMap();


    @ApiModelProperty("债券类型 1: 资本补充债,2:永续")
    private Integer spreadBondType;

    @ApiModelProperty("企业性质（经营类型过滤使用）1:央企, 2:国企, 3:民企, 999:其他")
    private List<Integer> businessFilterNatures;

    @Override
    public String getCurveName() {
        return CURVE_NAME_PREFIX +
                super.jointShortName() +
                this.jointBondTypeName() +
                super.jointBondImpliedRatingName(SpreadFinancialBondImpliedRatingMappingEnum.class) +
                this.jointBusinessFilterNatureName() +
                super.jointRemainingTenorName();

    }

    private String jointBondTypeName() {
        return Objects.nonNull(spreadBondType) ? (SEPARATOR + BOND_TYPE_NAME_MAP.get(this.spreadBondType)) : "";
    }

    private String jointBusinessFilterNatureName() {
        if (CollectionUtils.isEmpty(this.businessFilterNatures)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (Integer businessFilterNature : this.businessFilterNatures) {
            sb.append(EnumUtils.getEnum(BusinessFilterNatureEnum.class, businessFilterNature).getText()).append(OBLIQUE_LINE);
        }
        return SEPARATOR + sb.substring(0, sb.length() - 1);
    }

    private static Map<Integer, String> initBondTypeNameMap() {
        Map<Integer, String> bondTypeNameMap = new HashMap<>(InsuSeniorityRankingEnum.values().length);
        bondTypeNameMap.put(InsuSeniorityRankingEnum.TIER2.getValue(), "资本补充债");
        bondTypeNameMap.put(InsuSeniorityRankingEnum.PERPETUAL.getValue(), "永续债");
        return bondTypeNameMap;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public List<Integer> getBusinessFilterNatures() {
        return Objects.isNull(businessFilterNatures) ? new ArrayList<>() : new ArrayList<>(businessFilterNatures);
    }

    public void setBusinessFilterNatures(List<Integer> businessFilterNatures) {
        this.businessFilterNatures = Objects.isNull(businessFilterNatures) ? new ArrayList<>() : new ArrayList<>(businessFilterNatures);
    }
}
