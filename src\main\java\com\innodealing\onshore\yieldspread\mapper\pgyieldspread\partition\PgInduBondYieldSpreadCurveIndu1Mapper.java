package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu1DO;

/**
 * 行业利差曲线-分区表(包含行业1)
 *
 * <AUTHOR>
 */
public interface PgInduBondYieldSpreadCurveIndu1Mapper extends DynamicQueryMapper<PgInduBondYieldSpreadCurveIndu1DO> {

    /**
     * 从物化视图刷新行业利差曲线数据
     */
    void refreshMvInduBondYieldSpreadCurveFromMV();

    /**
     * 从物化视图中同步昨日利差曲线数据
     */
    void syncCurveIncrFromMV();
}
