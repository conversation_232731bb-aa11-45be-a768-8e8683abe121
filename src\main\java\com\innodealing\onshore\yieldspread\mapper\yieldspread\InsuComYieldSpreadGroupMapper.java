package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.InsuComYieldSpreadGroupMaxDO;

/**
 * 保险信用主体利差 group mapper
 *
 * <AUTHOR>
 * @date 2024/4/3 14:55
 **/
public interface InsuComYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<InsuComYieldSpreadDO
        , InsuComYieldSpreadGroupMaxDO> {
}
