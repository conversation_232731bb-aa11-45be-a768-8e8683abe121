package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;


/**
 * 城投曲线请求DTO-组合条件请求参数
 *
 * <AUTHOR>
 */
public class UdicCurveCompositionConditionDTO extends CurveCompositionConditionDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("省份编码")
    private Long provinceUniCode;

    @ApiModelProperty("地级市编码")
    private Long cityUniCode;

    @ApiModelProperty("行政区划")
    private Integer administrativeDivision;

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }

    public Integer getAdministrativeDivision() {
        return administrativeDivision;
    }

    public void setAdministrativeDivision(Integer administrativeDivision) {
        this.administrativeDivision = administrativeDivision;
    }

    @Override
    public String toString() {
        return "UdicCurveCompositionConditionDTO{" +
                "provinceUniCode=" + provinceUniCode +
                ", cityUniCode=" + cityUniCode +
                ", administrativeDivision=" + administrativeDivision +
                ", spreadBondType=" + spreadBondType +
                ", bondExtRatingMapping=" + bondExtRatingMapping +
                ", bondImpliedRatingMappingTag=" + bondImpliedRatingMappingTag +
                ", comYyRatingMappingTag=" + comYyRatingMappingTag +
                ", spreadRemainingTenorTag=" + spreadRemainingTenorTag +
                ", guaranteeStatus=" + guaranteeStatus +
                '}';
    }
}
