package com.innodealing.onshore.yieldspread.service.dmdc.impl;


import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.constant.RedisKeys;
import com.innodealing.onshore.bondmetadata.enums.BusinessNatureEnum;
import com.innodealing.onshore.yieldspread.dao.dmdc.BondInterestCtzDAO;
import com.innodealing.onshore.yieldspread.dao.dmdc.BondInterestInduDAO;
import com.innodealing.onshore.yieldspread.dao.dmdc.ComInterestCtzHistDAO;
import com.innodealing.onshore.yieldspread.dao.dmdc.ComInterestInduHistDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InduBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InduComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UdicBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UdicComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.BondInterestCtzDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.BondInterestInduDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.ComInterestCtzHistDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.ComInterestInduHistDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import com.innodealing.onshore.yieldspread.service.dmdc.InterestDataMigrateService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * 利差数据迁移 Service
 *
 * <AUTHOR>
 **/
@Service
public class InterestDataMigrateServiceImpl implements InterestDataMigrateService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private BondInterestCtzDAO bondInterestCtzDAO;
    @Resource
    private BondInterestInduDAO bondInterestInduDAO;
    @Resource
    private ComInterestCtzHistDAO comInterestCtzHistDAO;
    @Resource
    private ComInterestInduHistDAO comInterestInduHistDAO;
    @Resource
    private InduBondYieldSpreadDAO induBondYieldSpreadDAO;
    @Resource
    private InduComYieldSpreadDAO induComYieldSpreadDAO;
    @Resource
    private UdicBondYieldSpreadDAO udicBondYieldSpreadDAO;
    @Resource
    private UdicComYieldSpreadDAO udicComYieldSpreadDAO;
    @Resource
    private RedisService redisService;

    @Override
    public int bondInterestCtzMigrate(Date startDate, Date endDate) {
        int effectRows = 0;
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return effectRows;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        for (Date spreadDate : spreadDates) {
            StopWatch stopWatch1 = new StopWatch();
            stopWatch1.start();
            List<BondInterestCtzDO> bondInterestCtzDOs =
                    bondInterestCtzDAO.listBondInterestCtzDOByInterestDate(spreadDate, new HashSet<>());
            if (CollectionUtils.isEmpty(bondInterestCtzDOs)) {
                continue;
            }
            effectRows += saveBondInterestCtzDO(spreadDate, bondInterestCtzDOs);
        }
        logger.info("[bondInterestCtzMigrate] effectRows:{} stopWatch: {}", effectRows, stopWatch.getTime());
        return effectRows;
    }

    @Override
    public int bondInterestInduMigrate(Date startDate, Date endDate) {
        int effectRows = 0;
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return effectRows;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        for (Date spreadDate : spreadDates) {
            List<BondInterestInduDO> bondInterestInduDOs =
                    bondInterestInduDAO.listBondInterestInduDOByInterestDate(spreadDate, new HashSet<>());
            if (CollectionUtils.isEmpty(bondInterestInduDOs)) {
                continue;
            }
            effectRows += saveBondInterestInduDO(spreadDate, bondInterestInduDOs);
        }
        logger.info("[bondInterestInduMigrate] effectRows:{} stopWatch: {}", effectRows, stopWatch.getTime());
        return effectRows;
    }

    @Override
    public int comInterestCtzHistMigrate(Date startDate, Date endDate) {
        int effectRows = 0;
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return effectRows;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        for (Date spreadDate : spreadDates) {
            List<ComInterestCtzHistDO> comInterestCtzHistDOs =
                    comInterestCtzHistDAO.listComInterestCtzHistDOByInterestDate(spreadDate, new HashSet<>());
            if (CollectionUtils.isEmpty(comInterestCtzHistDOs)) {
                continue;
            }
            effectRows += saveComInterestCtzHistDO(spreadDate, comInterestCtzHistDOs);
        }
        logger.info("[comInterestCtzHistMigrate]  effectRows:{}  stopWatch: {}", effectRows, stopWatch.getTime());
        return effectRows;
    }

    @Override
    public int comInterestInduHistMigrate(Date startDate, Date endDate) {
        int effectRows = 0;
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return effectRows;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        if (CollectionUtils.isEmpty(spreadDates)) {
            return effectRows;
        }
        for (Date spreadDate : spreadDates) {
            List<ComInterestInduHistDO> comInterestInduHistDOs =
                    comInterestInduHistDAO.listComInterestInduHistDOByInterestDate(spreadDate, new HashSet<>());
            if (CollectionUtils.isEmpty(comInterestInduHistDOs)) {
                continue;
            }
            effectRows += saveComInterestInduHistDO(spreadDate, comInterestInduHistDOs);
        }
        logger.info("[comInterestInduHistMigrate]  effectRows:{}  stopWatch: {}", effectRows, stopWatch.getTime());
        return effectRows;
    }

    private int saveBondInterestCtzDO(Date date, List<BondInterestCtzDO> bondInterestCtzDOs) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int effectRows = 0;
        try {
            List<List<BondInterestCtzDO>> lists = com.google.common.collect.Lists.partition(
                    bondInterestCtzDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<BondInterestCtzDO> list : lists) {
                effectRows += udicBondYieldSpreadDAO.saveUdicBondYieldSpreadDOList(date, date, this.convertUdicBondYieldSpreadDO(list));
            }
        } catch (Exception e) {
            logger.error(String.format("can't handle saveBondInterestCtzDO date: %d", date.getTime()), e);
        }
        logger.info("[saveBondInterestCtzDO] date: {} effectRows:{} stopWatch: {}",
                date, effectRows, stopWatch.getTime());
        return effectRows;
    }

    private int saveBondInterestInduDO(Date date, List<BondInterestInduDO> bondInterestInduDOs) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int effectRows = 0;
        try {
            List<List<BondInterestInduDO>> lists = com.google.common.collect.Lists.
                    partition(bondInterestInduDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<BondInterestInduDO> list : lists) {
                effectRows += induBondYieldSpreadDAO.saveInduBondYieldSpreadDOList(date, date, this.convertInduBondYieldSpreadDO(list));
            }
        } catch (Exception e) {
            logger.error(String.format("can't handle saveBondInterestInduDO date: %d", date.getTime()), e);
        }
        logger.info("[saveBondInterestInduDO] date: {} effectRows:{} stopWatch: {}",
                date, effectRows, stopWatch.getTime());
        return effectRows;
    }

    private int saveComInterestCtzHistDO(Date date, List<ComInterestCtzHistDO> comInterestCtzHistDOs) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int effectRows = 0;
        try {
            List<List<ComInterestCtzHistDO>> lists = com.google.common.collect.Lists.
                    partition(comInterestCtzHistDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<ComInterestCtzHistDO> list : lists) {
                effectRows += udicComYieldSpreadDAO.saveUdicComYieldSpreadDOList(date, this.convertUdicComYieldSpreadDO(list));
            }
        } catch (Exception e) {
            logger.error(String.format("can't handle saveComInterestCtzHistDO date: %d", date.getTime()), e);
        }
        logger.info("[saveComInterestCtzHistDO] date: {} effectRows:{} stopWatch: {}",
                date, effectRows, stopWatch.getTime());
        return effectRows;
    }

    private int saveComInterestInduHistDO(Date date, List<ComInterestInduHistDO> comInterestInduHistDOs) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int effectRows = 0;
        try {
            List<List<ComInterestInduHistDO>> lists = com.google.common.collect.Lists.
                    partition(comInterestInduHistDOs, YieldSpreadHelper.BATCH_SIZE);
            for (List<ComInterestInduHistDO> list : lists) {
                effectRows += induComYieldSpreadDAO.saveInduComYieldSpreadDOList(date, this.convertInduComYieldSpreadDO(list));
            }
        } catch (Exception e) {
            logger.error(String.format("can't handle saveComInterestInduHistDO date: %d", date.getTime()), e);
        }
        logger.info("[saveComInterestInduHistDO] date: {} effectRows:{} stopWatch: {}",
                date, effectRows, stopWatch.getTime());
        return effectRows;
    }

    private List<UdicBondYieldSpreadDO> convertUdicBondYieldSpreadDO(List<BondInterestCtzDO> bondInterestCtzDOs) {
        List<UdicBondYieldSpreadDO> udicBondYieldSpreadDOs = new ArrayList<>();
        for (BondInterestCtzDO bondInterestCtzDO : bondInterestCtzDOs) {
            UdicBondYieldSpreadDO result = new UdicBondYieldSpreadDO();
            result.setBondUniCode(bondInterestCtzDO.getBondUniCode());
            result.setBondCode(bondInterestCtzDO.getBondCode());
            result.setComUniCode(bondInterestCtzDO.getComUniCode());
            result.setSpreadDate(bondInterestCtzDO.getInterestDate());
            result.setProvinceUniCode(bondInterestCtzDO.getAreaUniCode1());
            result.setProvinceName(bondInterestCtzDO.getAreaName1());
            result.setCityUniCode(bondInterestCtzDO.getAreaUniCode2());
            result.setCityName(bondInterestCtzDO.getAreaName2());
            result.setAdministrativeRegion(bondInterestCtzDO.getAreaLevelId());
            result.setId(redisService.generatePk(RedisKeys.YIELD_SPREAD_UDIC_BOND_YIELD_SPREAD_FLOW_ID, result.getSpreadDate()));
            result.setDeleted(0);
            // 这里原来的字段没有 所以我们通过  AdministrativeRegion 字段映射
            if (Objects.nonNull(result.getAdministrativeRegion())) {
                result.setAdministrativeDivision(YieldSpreadHelper.getAdministrativeDivisionMap().get(result.getAdministrativeRegion()));
            }
            udicBondYieldSpreadDOs.add(result);
        }
        return udicBondYieldSpreadDOs;
    }

    private List<InduBondYieldSpreadDO> convertInduBondYieldSpreadDO(List<BondInterestInduDO> bondInterestInduDOs) {
        List<InduBondYieldSpreadDO> induBondYieldSpreadDOs = new ArrayList<>();
        for (BondInterestInduDO bondInterestInduDO : bondInterestInduDOs) {
            InduBondYieldSpreadDO result = new InduBondYieldSpreadDO();
            result.setBondUniCode(bondInterestInduDO.getBondUniCode());
            result.setComUniCode(bondInterestInduDO.getComUniCode());
            result.setBondCode(bondInterestInduDO.getBondCode());
            result.setSpreadDate(bondInterestInduDO.getInterestDate());
            result.setInduLevel1Code(bondInterestInduDO.getIndustryCode1());
            result.setInduLevel1Name(bondInterestInduDO.getIndustryName1());
            result.setInduLevel2Code(bondInterestInduDO.getIndustryCode2());
            result.setInduLevel2Name(bondInterestInduDO.getIndustryName2());
            result.setBusinessNature(bondInterestInduDO.getEnterpriseType());
            result.setId(redisService.generatePk(RedisKeys.YIELD_SPREAD_INDU_BOND_YIELD_SPREAD_FLOW_ID, result.getSpreadDate()));
            result.setDeleted(0);
            // 这里原来的字段没有 所以我们通过  BusinessNature 字段映射
            if (Objects.nonNull(result.getBusinessNature())) {
                result.setBusinessFilterNature(
                        BusinessNatureEnum.getBusinessFilterNatureEnum(result.getBusinessNature()).getValue()
                );
            }
            induBondYieldSpreadDOs.add(result);
        }
        return induBondYieldSpreadDOs;
    }

    private List<UdicComYieldSpreadDO> convertUdicComYieldSpreadDO(List<ComInterestCtzHistDO> comInterestCtzHistDOs) {
        List<UdicComYieldSpreadDO> udicComYieldSpreadDOs = new ArrayList<>();
        for (ComInterestCtzHistDO comInterestCtzHistDO : comInterestCtzHistDOs) {
            UdicComYieldSpreadDO result = new UdicComYieldSpreadDO();
            result.setComUniCode(comInterestCtzHistDO.getComUniCode());
            result.setSpreadDate(comInterestCtzHistDO.getInterestDate());
            result.setProvinceUniCode(comInterestCtzHistDO.getAreaUniCode1());
            result.setProvinceName(comInterestCtzHistDO.getAreaName1());
            result.setCityUniCode(comInterestCtzHistDO.getAreaUniCode2());
            result.setCityName(comInterestCtzHistDO.getAreaName2());
            result.setAdministrativeRegion(comInterestCtzHistDO.getAreaLevelId());
            result.setActualControllerFullName(comInterestCtzHistDO.getRealCtrlName());
            result.setAreaName(comInterestCtzHistDO.getAreaName());
            result.setDeleted(0);
            udicComYieldSpreadDOs.add(result);
        }
        return udicComYieldSpreadDOs;
    }

    private List<InduComYieldSpreadDO> convertInduComYieldSpreadDO(List<ComInterestInduHistDO> comInterestInduHistDOs) {
        List<InduComYieldSpreadDO> induComYieldSpreadDOs = new ArrayList<>();
        for (ComInterestInduHistDO comInterestInduHistDO : comInterestInduHistDOs) {
            InduComYieldSpreadDO result = new InduComYieldSpreadDO();
            result.setComUniCode(comInterestInduHistDO.getComUniCode());
            result.setSpreadDate(comInterestInduHistDO.getInterestDate());
            result.setInduLevel1Code(comInterestInduHistDO.getIndustryCode1());
            result.setInduLevel1Name(comInterestInduHistDO.getIndustryName1());
            result.setInduLevel2Code(comInterestInduHistDO.getIndustryCode2());
            result.setInduLevel2Name(comInterestInduHistDO.getIndustryName2());
            result.setBusinessNature(comInterestInduHistDO.getEnterpriseType());
            result.setDeleted(0);
            induComYieldSpreadDOs.add(result);
        }
        return induComYieldSpreadDOs;
    }
}
