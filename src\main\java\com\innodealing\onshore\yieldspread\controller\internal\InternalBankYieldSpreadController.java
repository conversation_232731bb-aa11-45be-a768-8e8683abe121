package com.innodealing.onshore.yieldspread.controller.internal;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.yieldspread.helper.ValidationUtil;
import com.innodealing.onshore.yieldspread.model.dto.request.BankCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BankComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BankSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.service.BankBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.BankComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;

/**
 * (内部)银行利差分析接口
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部)利差分析-银行")
@RestController
@RequestMapping("internal/bank/yield-spread")
public class InternalBankYieldSpreadController {

    @Resource
    private BankBondYieldSpreadService bankBondYieldSpreadService;

    @Resource
    private BankComYieldSpreadService bankComYieldSpreadService;

    @ApiOperation(value = "银行利差分析-刷新行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard")
    public void refreshCurveRatingShard(@RequestBody RefreshYieldCurveParam param) {
        ValidationUtil.valid(param);
        bankBondYieldSpreadService.refreshMvBankBondYieldSpreadRatingCurve(param);
    }

    @ApiOperation(value = "银行利差分析-刷新昨日行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard-yesterday")
    public void refreshCurveRatingShardYesterday() {
        RefreshYieldCurveParam param = new RefreshYieldCurveParam();
        param.setMvRefresh(true);
        param.setStartDate(Date.valueOf(LocalDate.now().minusDays(1)));
        param.setEndDate(Date.valueOf(LocalDate.now().minusDays(1)));
        bankBondYieldSpreadService.refreshMvBankBondYieldSpreadRatingCurve(param);
    }

    @ApiOperation(value = "银行利差分析-刷新历史行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard-history")
    public void refreshCurveRatingShardHistory(@RequestParam("isTableRefresh") Boolean isTableRefresh) {
        bankBondYieldSpreadService.refreshMvBankBondYieldSpreadRatingCurveHistory(isTableRefresh);
    }

    @PostMapping("curve-save")
    @ApiOperation(value = "保存曲线")
    public Boolean saveCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId") Long curveGroupId,
            @RequestBody @Validated BankCurveGenerateConditionReqDTO request) {
        return bankBondYieldSpreadService.saveCurve(userid, curveGroupId, request);
    }

    @PostMapping("curve-update")
    @ApiOperation(value = "更新曲线")
    public Boolean updateCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Validated BankCurveGenerateConditionReqDTO request) {
        return bankBondYieldSpreadService.updateCurve(userid, curveId, request);
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public NormPagingResult<BankSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return bankBondYieldSpreadService.pagingSingleBondYieldSpreads(userid, request);
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差  分页")
    public List<BankComYieldSpreadResDTO> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return bankComYieldSpreadService.listComYieldSpreads(userid, request);
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public Long countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return bankComYieldSpreadService.countComYieldSpread(userid, request);
    }

}
