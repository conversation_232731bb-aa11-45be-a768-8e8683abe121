package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.CurveCode;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 利差全景数据图类型枚举,
 * 注意：要按照评级从高到低编码，代码中用到了ordinal()进行排序
 *
 * <AUTHOR>
 */
public enum YieldSpreadCurveCodeEnum implements ITextValueEnum {

    /**
     * 国债
     */
    CHINA_BOND(CurveCode.CHINA_BOND.getValue(), "国债", YieldPanoramaBondTypeEnum.CHINA_BOND),
    /**
     * 国开债
     */
    CHINA_BOND_KAI(CurveCode.CHINA_BOND_KAI.getValue(), "国开", YieldPanoramaBondTypeEnum.CD_BOND),
    /**
     * 中债进出口行债
     */
    CHINA_BOND_IMPORT(CurveUniCodeEnum.CHINA_BOND_IMPORT.getCurveCode(), "进出口", YieldPanoramaBondTypeEnum.CHINA_BOND_IMPORT),
    /**
     * 中债农发债
     */
    CHINA_BOND_NO(CurveUniCodeEnum.CHINA_BOND_NO.getCurveCode(), "农发", YieldPanoramaBondTypeEnum.CHINA_BOND_NO),
    /**
     * 地方政府债
     */
    CHINA_BOND_LGB(CurveCode.CHINA_BOND_CITY.getValue(), "地方债", YieldPanoramaBondTypeEnum.LOCAL_TREASURY_BOND),
    /**
     * 中短期票据
     */
    CHINA_BOND_MID_AAA_PLUS(CurveCode.CHINA_BOND_MID_AAA_PLUS.getValue(), "AAA+", YieldPanoramaBondTypeEnum.MEDIUM_AND_SHORT_TERMS_NOTE),
    CHINA_BOND_MID(CurveCode.CHINA_BOND_MID.getValue(), "AAA", YieldPanoramaBondTypeEnum.MEDIUM_AND_SHORT_TERMS_NOTE),
    CHINA_BOND_MID_AAA_SUB(CurveCode.CHINA_BOND_MID_AAA_SUB.getValue(), "AAA-", YieldPanoramaBondTypeEnum.MEDIUM_AND_SHORT_TERMS_NOTE),
    CHINA_BOND_MID_AA_PLUS(CurveCode.CHINA_BOND_MID_AA_PLUS.getValue(), "AA+", YieldPanoramaBondTypeEnum.MEDIUM_AND_SHORT_TERMS_NOTE),
    CHINA_BOND_MID_AA(CurveCode.CHINA_BOND_MID_AA.getValue(), "AA", YieldPanoramaBondTypeEnum.MEDIUM_AND_SHORT_TERMS_NOTE),
    CHINA_BOND_MID_AA_SUB(CurveCode.CHINA_BOND_MID_AA_SUB.getValue(), "AA-", YieldPanoramaBondTypeEnum.MEDIUM_AND_SHORT_TERMS_NOTE),
    /**
     * 产业债
     */
    CHINA_INDUSTRIAL_AAA(100, "AAA", YieldPanoramaBondTypeEnum.INDUSTRIAL_BOND),
    CHINA_INDUSTRIAL_AAA_SUB(101, "AAA-", YieldPanoramaBondTypeEnum.INDUSTRIAL_BOND),
    CHINA_INDUSTRIAL_AA_PLUS(102, "AA+", YieldPanoramaBondTypeEnum.INDUSTRIAL_BOND),
    CHINA_INDUSTRIAL_AA(103, "AA", YieldPanoramaBondTypeEnum.INDUSTRIAL_BOND),
    /**
     * 城投债
     */
    CHINA_CT_AAA(CurveCode.CHINA_CT_AAA.getValue(), "AAA", YieldPanoramaBondTypeEnum.URBAN_BOND),
    CHINA_BOND_CT_AA_PLUS(CurveCode.CHINA_BOND_CT_AA_PLUS.getValue(), "AA+", YieldPanoramaBondTypeEnum.URBAN_BOND),
    CHINA_BOND_CT_AA(CurveCode.CHINA_BOND_CT_AA.getValue(), "AA", YieldPanoramaBondTypeEnum.URBAN_BOND),
    CHINA_BOND_CT_AA_TWO(CurveCode.CHINA_BOND_CT_AA_TWO.getValue(), "AA(2)", YieldPanoramaBondTypeEnum.URBAN_BOND),
    CHINA_BOND_CT_AA_SUB(CurveCode.CHINA_BOND_CT_AA_SUB.getValue(), "AA-", YieldPanoramaBondTypeEnum.URBAN_BOND),
    /**
     * 银行普通债
     */
    BO_BONDS_AAA(CurveCode.CHINA_BOND_ORD.getValue(), "AAA", YieldPanoramaBondTypeEnum.GENERAL_BANK_BOND),
    BO_BONDS_AAA_SUB(CurveCode.GENERAL_BANK_BOND_AAA_SUB.getValue(), "AAA-", YieldPanoramaBondTypeEnum.GENERAL_BANK_BOND),
    BO_BONDS_AA_PLUS(CurveCode.GENERAL_BANK_BOND_AA_PLUS.getValue(), "AA+", YieldPanoramaBondTypeEnum.GENERAL_BANK_BOND),
    BO_BONDS_AA(CurveCode.GENERAL_BANK_BOND_AA.getValue(), "AA", YieldPanoramaBondTypeEnum.GENERAL_BANK_BOND),
    BO_BONDS_AA_SUB(CurveCode.GENERAL_BANK_BOND_AA_SUB.getValue(), "AA-", YieldPanoramaBondTypeEnum.GENERAL_BANK_BOND),
    /**
     * 银行二级资本债
     */
    BT2_BONDS_AAA_SUB(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AAA_SUB.getValue(), "AAA-", YieldPanoramaBondTypeEnum.BANK_SECONDARY_CAPITAL_BOND),
    BT2_BONDS_AA_PLUS(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA_PLUS.getValue(), "AA+", YieldPanoramaBondTypeEnum.BANK_SECONDARY_CAPITAL_BOND),
    BT2_BONDS_AA(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA.getValue(), "AA", YieldPanoramaBondTypeEnum.BANK_SECONDARY_CAPITAL_BOND),
    BT2_BONDS_AA_SUB(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA_SUB.getValue(), "AA-", YieldPanoramaBondTypeEnum.BANK_SECONDARY_CAPITAL_BOND),
    /**
     * 银行永续债
     */
    BP_BONDS_AAA_SUB(CurveCode.BANK_PERPETUAL_BOND_AAA_SUB.getValue(), "AAA-", YieldPanoramaBondTypeEnum.BANK_PERPETUAL_BOND),
    BP_BONDS_AA_PLUS(CurveCode.BANK_PERPETUAL_BOND_AA_PLUS.getValue(), "AA+", YieldPanoramaBondTypeEnum.BANK_PERPETUAL_BOND),
    BP_BONDS_AA(CurveCode.BANK_PERPETUAL_BOND_AA.getValue(), "AA", YieldPanoramaBondTypeEnum.BANK_PERPETUAL_BOND),
    BP_BONDS_AA_SUB(CurveCode.BANK_PERPETUAL_BOND_AA_SUB.getValue(), "AA-", YieldPanoramaBondTypeEnum.BANK_PERPETUAL_BOND),
    /**
     * 证券普通债
     */
    SC_BONDS_AAA(CurveCode.SECURITIES_BOND_AAA.getValue(), "AAA", YieldPanoramaBondTypeEnum.SECURITIES_BOND),
    SC_BONDS_AAA_SUB(CurveCode.SECURITIES_BOND_AAA_SUB.getValue(), "AAA-", YieldPanoramaBondTypeEnum.SECURITIES_BOND),
    SC_BONDS_AA_PLUS(CurveCode.SECURITIES_BOND_AA_PLUS.getValue(), "AA+", YieldPanoramaBondTypeEnum.SECURITIES_BOND),
    SC_BONDS_AA(CurveCode.SECURITIES_BOND_AA.getValue(), "AA", YieldPanoramaBondTypeEnum.SECURITIES_BOND),
    /**
     * 证券次级债
     */
    SS_BONDS(104, null, YieldPanoramaBondTypeEnum.SECURITIES_SUB_BOND),
    /**
     * 证券永续债
     */
    SP_BONDS(105, null, YieldPanoramaBondTypeEnum.SECURITIES_PERPETUAL_BOND),

    /**
     * 保险资本补充
     */
    ICS_AA_PLUS(CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA_PLUS.getCurveCode(), "AA+", YieldPanoramaBondTypeEnum.INSU_CAPITAL_SUPPLEMENT),
    ICS_AA(CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA.getCurveCode(), "AA", YieldPanoramaBondTypeEnum.INSU_CAPITAL_SUPPLEMENT),
    ICS_AA_SUB(CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA_SUB.getCurveCode(), "AA-", YieldPanoramaBondTypeEnum.INSU_CAPITAL_SUPPLEMENT),
    /**
     * 同业存单
     */
    NCD_AAA(CurveCode.NCD_AAA.getValue(), "AAA", YieldPanoramaBondTypeEnum.NCD),
    NCD_AAA_SUB(CurveCode.NCD_AAA_SUB.getValue(), "AAA-", YieldPanoramaBondTypeEnum.NCD),
    NCD_AA_PLUS(CurveCode.NCD_AA_PLUS.getValue(), "AA+", YieldPanoramaBondTypeEnum.NCD),
    NCD_AA(CurveCode.NCD_AA.getValue(), "AA", YieldPanoramaBondTypeEnum.NCD),
    NCD_AA_SUB(CurveCode.NCD_AA_SUB.getValue(), "AA-", YieldPanoramaBondTypeEnum.NCD);

    private final Integer code;
    private final String text;
    private final YieldPanoramaBondTypeEnum bondTypeEnum;

    /*** 国债*/
    private static final Set<YieldSpreadCurveCodeEnum> CHINA_BONDS = EnumSet.of(CHINA_BOND);
    /*** 国开*/
    private static final Set<YieldSpreadCurveCodeEnum> CD_BONDS = EnumSet.of(CHINA_BOND_KAI);
    /*** 地方政府债*/
    private static final Set<YieldSpreadCurveCodeEnum> LOCAL_TREASURY_BONDS = EnumSet.of(CHINA_BOND_LGB);
    /*** 中短期票据*/
    private static final Set<YieldSpreadCurveCodeEnum> MEDIUM_AND_SHORT_TERMS_NOTES =
            EnumSet.of(CHINA_BOND_MID_AAA_PLUS, CHINA_BOND_MID, CHINA_BOND_MID_AAA_SUB, CHINA_BOND_MID_AA_PLUS, CHINA_BOND_MID_AA, CHINA_BOND_MID_AA_SUB);
    /*** 产业债*/
    private static final Set<YieldSpreadCurveCodeEnum> INDUSTRIAL_BONDS =
            EnumSet.of(CHINA_INDUSTRIAL_AAA, CHINA_INDUSTRIAL_AAA_SUB, CHINA_INDUSTRIAL_AA_PLUS, CHINA_INDUSTRIAL_AA);
    /*** 城投债*/
    private static final Set<YieldSpreadCurveCodeEnum> URBAN_BONDS =
            EnumSet.of(CHINA_CT_AAA, CHINA_BOND_CT_AA_PLUS, CHINA_BOND_CT_AA, CHINA_BOND_CT_AA_TWO, CHINA_BOND_CT_AA_SUB);
    /*** 银行普通债*/
    private static final Set<YieldSpreadCurveCodeEnum> GENERAL_BANK_BONDS =
            EnumSet.of(BO_BONDS_AAA, BO_BONDS_AAA_SUB, BO_BONDS_AA_PLUS, BO_BONDS_AA, BO_BONDS_AA_SUB);
    /*** 银行二级资本债*/
    private static final Set<YieldSpreadCurveCodeEnum> BANK_SECONDARY_CAPITAL_BONDS =
            EnumSet.of(BT2_BONDS_AAA_SUB, BT2_BONDS_AA_PLUS, BT2_BONDS_AA, BT2_BONDS_AA_SUB);
    /*** 银行永续债*/
    private static final Set<YieldSpreadCurveCodeEnum> BANK_PERPETUAL_BONDS =
            EnumSet.of(BP_BONDS_AAA_SUB, BP_BONDS_AA_PLUS, BP_BONDS_AA, BP_BONDS_AA_SUB);
    /*** 证券公司债*/
    private static final Set<YieldSpreadCurveCodeEnum> SECURITIES_BONDS =
            EnumSet.of(SC_BONDS_AAA, SC_BONDS_AAA_SUB, SC_BONDS_AA_PLUS, SC_BONDS_AA);
    /*** 证券次级债*/
    private static final Set<YieldSpreadCurveCodeEnum> SECURITIES_SUB_BONDS = EnumSet.of(SS_BONDS);
    /*** 证券永续债*/
    private static final Set<YieldSpreadCurveCodeEnum> SECURITIES_PERPETUAL_BONDS = EnumSet.of(SP_BONDS);
    /*** 同业存单*/
    private static final Set<YieldSpreadCurveCodeEnum> NCDS = EnumSet.of(NCD_AAA, NCD_AAA_SUB, NCD_AA_PLUS, NCD_AA, NCD_AA_SUB);

    /*** 保险资本补充*/
    private static final Set<YieldSpreadCurveCodeEnum> INSU_CAPITAL_SUPPLEMENTS = EnumSet.of(ICS_AA_PLUS, ICS_AA, ICS_AA_SUB);

    /*** 地方利率债*/
    private static final Set<YieldSpreadCurveCodeEnum> INTEREST_RATE_BOND = EnumSet.of(CHINA_BOND, CHINA_BOND_KAI,
            CHINA_BOND_IMPORT, CHINA_BOND_NO, CHINA_BOND_LGB);
    private static final Map<YieldPanoramaBondTypeEnum, List<YieldSpreadCurveCodeEnum>> CURVE_CODE_MAP = initCurveCodeMap();

    private static Map<YieldPanoramaBondTypeEnum, List<YieldSpreadCurveCodeEnum>> initCurveCodeMap() {
        return Arrays.stream(values()).collect(Collectors.groupingBy(YieldSpreadCurveCodeEnum::getBondType));
    }

    /*** AAA+ */
    private static final Set<YieldSpreadCurveCodeEnum> AAA_PLUS_RATINGS =
            EnumSet.of(CHINA_BOND_MID_AAA_PLUS);

    /*** AAA */
    private static final Set<YieldSpreadCurveCodeEnum> AAA_RATINGS =
            EnumSet.of(CHINA_BOND_MID, CHINA_INDUSTRIAL_AAA, CHINA_CT_AAA, BO_BONDS_AAA, SC_BONDS_AAA, NCD_AAA);

    /*** AAA- */
    private static final Set<YieldSpreadCurveCodeEnum> AAA_SUB_RATINGS =
            EnumSet.of(CHINA_BOND_MID_AAA_SUB, CHINA_INDUSTRIAL_AAA_SUB, BO_BONDS_AAA_SUB, BT2_BONDS_AAA_SUB, BP_BONDS_AAA_SUB, SC_BONDS_AAA_SUB, NCD_AAA_SUB);

    /*** AA+ */
    private static final Set<YieldSpreadCurveCodeEnum> AA_PLUS_RATINGS =
            EnumSet.of(CHINA_BOND_MID_AA_PLUS, CHINA_INDUSTRIAL_AA_PLUS, CHINA_BOND_CT_AA_PLUS, BO_BONDS_AA_PLUS, BT2_BONDS_AA_PLUS, BP_BONDS_AA_PLUS,
                    SC_BONDS_AA_PLUS, NCD_AA_PLUS, ICS_AA_PLUS);

    /*** AA */
    private static final Set<YieldSpreadCurveCodeEnum> AA_RATINGS =
            EnumSet.of(CHINA_BOND_MID_AA, CHINA_INDUSTRIAL_AA, CHINA_BOND_CT_AA, BO_BONDS_AA, BT2_BONDS_AA, BP_BONDS_AA, SC_BONDS_AA, NCD_AA, ICS_AA);

    /*** AA(2) */
    private static final Set<YieldSpreadCurveCodeEnum> AA_TWO_RATINGS = EnumSet.of(CHINA_BOND_CT_AA_TWO);

    /*** AA- */
    private static final Set<YieldSpreadCurveCodeEnum> AA_SUB_RATINGS =
            EnumSet.of(CHINA_BOND_MID_AA_SUB, CHINA_BOND_CT_AA_SUB, BO_BONDS_AA_SUB, BT2_BONDS_AA_SUB, BP_BONDS_AA_SUB, NCD_AA_SUB, ICS_AA_SUB);

    /**
     * 获取中债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getChinaBonds() {
        return toValues(CHINA_BONDS);
    }

    /**
     * 获取国开curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getChinaBondKais() {
        return toValues(CD_BONDS);
    }

    /**
     * 获取地方政府债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getChinaBondLgbs() {
        return toValues(LOCAL_TREASURY_BONDS);
    }

    /**
     * 获取中短期票据curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getMediumAndShortTermsNotes() {
        return toValues(MEDIUM_AND_SHORT_TERMS_NOTES);
    }

    /**
     * 获取产业债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getIndustrialBonds() {
        return toValues(INDUSTRIAL_BONDS);
    }

    /**
     * 获取城投债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getUrbanBonds() {
        return toValues(URBAN_BONDS);
    }

    /**
     * 获取银行普通债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getGeneralBankBonds() {
        return toValues(GENERAL_BANK_BONDS);
    }

    /**
     * 获取银行二级资本债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getBankSecondaryCapitalBonds() {
        return toValues(BANK_SECONDARY_CAPITAL_BONDS);
    }

    /**
     * 获取银行永续债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getBankPerpetualBonds() {
        return toValues(BANK_PERPETUAL_BONDS);
    }

    /**
     * 获取证券公司债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getSecuritiesBonds() {
        return toValues(SECURITIES_BONDS);
    }

    /**
     * 获取证券公司二级债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getSecuritiesSubBonds() {
        return toValues(SECURITIES_SUB_BONDS);
    }

    /**
     * 获取证券公司永续债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getSecuritiesPerpetualBonds() {
        return toValues(SECURITIES_PERPETUAL_BONDS);
    }

    /**
     * 获取同业存单curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getNcds() {
        return toValues(NCDS);
    }

    /**
     * 获取保险资本补充curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getInsuCapitalSupplements() {
        return toValues(INSU_CAPITAL_SUPPLEMENTS);
    }

    /**
     * 获取利率债curveCode
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getInterestRateBonds() {
        return toValues(INTEREST_RATE_BOND);
    }

    /**
     * 获取AAA+评级曲线代码
     *
     * @return {@link Set}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static Set<YieldSpreadCurveCodeEnum> getAAAPlusRatings() {
        return EnumSet.copyOf(AAA_PLUS_RATINGS);
    }

    /**
     * 获取AAA评级曲线代码
     *
     * @return {@link Set}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static Set<YieldSpreadCurveCodeEnum> getAAARatings() {
        return EnumSet.copyOf(AAA_RATINGS);
    }

    /**
     * 获取AAA-评级曲线代码
     *
     * @return {@link Set}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static Set<YieldSpreadCurveCodeEnum> getAAASubRatings() {
        return EnumSet.copyOf(AAA_SUB_RATINGS);
    }

    /**
     * 获取AA+评级曲线代码
     *
     * @return {@link Set}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static Set<YieldSpreadCurveCodeEnum> getAAPlusRatings() {
        return EnumSet.copyOf(AA_PLUS_RATINGS);
    }

    /**
     * 获取AA评级曲线代码
     *
     * @return {@link Set}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static Set<YieldSpreadCurveCodeEnum> getAARatings() {
        return EnumSet.copyOf(AA_RATINGS);
    }

    /**
     * 获取AA(2)评级曲线代码
     *
     * @return {@link Set}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static Set<YieldSpreadCurveCodeEnum> getAATwoRatings() {
        return EnumSet.copyOf(AA_TWO_RATINGS);
    }

    /**
     * 获取AA-评级曲线代码
     *
     * @return {@link Set}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static Set<YieldSpreadCurveCodeEnum> getAASubRatings() {
        return EnumSet.copyOf(AA_SUB_RATINGS);
    }


    /**
     * 将curveCode枚举集合转为value集合
     *
     * @param curveCodes 曲线代码
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> toValues(Collection<YieldSpreadCurveCodeEnum> curveCodes) {
        return curveCodes.stream().map(YieldSpreadCurveCodeEnum::getValue).collect(Collectors.toSet());
    }

    /**
     * 将curveCode集合转为enum枚举
     *
     * @param curveCodes 曲线代码枚举
     * @return {@link List}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static List<YieldSpreadCurveCodeEnum> toEnums(Collection<Integer> curveCodes) {
        return CollectionUtils.emptyIfNull(curveCodes).stream().distinct()
                .map(curveCode -> EnumUtils.ofNullable(YieldSpreadCurveCodeEnum.class, curveCode))
                .filter(Optional::isPresent).map(Optional::get)
                .collect(Collectors.toList());
    }

    /**
     * 根据债券类型获取curveCode集合
     *
     * @param bondTypeEnum 债券枚举类型
     * @return {@link List}<{@link YieldSpreadCurveCodeEnum}>
     */
    public static List<YieldSpreadCurveCodeEnum> getCurveCodesEnumByBondType(YieldPanoramaBondTypeEnum bondTypeEnum) {
        return CURVE_CODE_MAP.getOrDefault(bondTypeEnum, Collections.emptyList()).stream()
                .sorted(Comparator.comparing(Enum::ordinal)).collect(Collectors.toList());
    }

    YieldSpreadCurveCodeEnum(Integer code, String text, YieldPanoramaBondTypeEnum bondTypeEnum) {
        this.code = code;
        this.text = text;
        this.bondTypeEnum = bondTypeEnum;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }

    public YieldPanoramaBondTypeEnum getBondType() {
        return bondTypeEnum;
    }
}
