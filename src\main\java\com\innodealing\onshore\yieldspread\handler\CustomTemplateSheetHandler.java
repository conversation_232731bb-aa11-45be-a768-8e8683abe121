package com.innodealing.onshore.yieldspread.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.innodealing.commons.object.ObjectExtensionUtils;

import java.util.Objects;


/**
 * 自定义模板sheet写入处理器
 *
 * <AUTHOR>
 */
public class CustomTemplateSheetHandler implements SheetWriteHandler {

    private static final Integer DEFAULT_SHEET_NO = 0;

    private Integer sheetNo;
    private String sheetName;

    /**
     * 自定义模板sheet写入处理器构造函数
     *
     * @param sheetNo   sheetNo
     * @param sheetName sheet名
     */
    public CustomTemplateSheetHandler(Integer sheetNo, String sheetName) {
        this.sheetNo = ObjectExtensionUtils.getOrDefault(sheetNo, DEFAULT_SHEET_NO);
        this.sheetName = sheetName;
    }

    /**
     * 功能：动态修改模板中sheet的名称
     * sheet创建完成后调用
     *
     * @param writeWorkbookHolder workbook持有器
     * @param writeSheetHolder    sheet持有器
     */
    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if (Objects.isNull(sheetName)) {
            return;
        }
        writeWorkbookHolder.getCachedWorkbook().setSheetName(sheetNo, sheetName);
    }
}
