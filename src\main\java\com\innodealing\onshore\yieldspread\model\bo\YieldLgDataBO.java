package com.innodealing.onshore.yieldspread.model.bo;

import java.sql.Date;
import java.util.Objects;

/**
 * 地方债区域利差数据
 *
 * <AUTHOR>
 */
public class YieldLgDataBO extends YieldPeriodDataBO {

    /**
     * 发行人代码
     */
    private Long comUniCode;

    /**
     * 地方债地区
     */
    private String lgAreaName;

    /**
     * 利差日期
     */
    private Date spreadDate;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getLgAreaName() {
        return lgAreaName;
    }

    public void setLgAreaName(String lgAreaName) {
        this.lgAreaName = lgAreaName;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }
}
