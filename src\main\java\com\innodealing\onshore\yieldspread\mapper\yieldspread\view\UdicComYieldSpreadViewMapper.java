package com.innodealing.onshore.yieldspread.mapper.yieldspread.view;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.github.wz2cool.dynamic.mybatis.mapper.SelectViewByDynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.view.UdicComYieldSpreadDynamicView;


/**
 * 城投主体利差视图
 *
 * <AUTHOR>
 */
public interface UdicComYieldSpreadViewMapper extends SelectViewByDynamicQueryMapper<UdicComYieldSpreadDynamicView>,
        DynamicQueryMapper<UdicComYieldSpreadDynamicView> {
}
