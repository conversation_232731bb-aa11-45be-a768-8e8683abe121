package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * 保险单券利差
 *
 * <AUTHOR>
 */
public class InsuSingleBondYieldSpreadResDTO extends BaseSingleBondYieldSpreadResDTO {

    @ApiModelProperty("企业性质")
    private Integer businessNature;

    @ApiModelProperty("企业性质")
    private String businessNatureStr;

    @ApiModelProperty("求偿顺序 1、资本补充债 2、永续")
    private Integer insuranceSeniorityRanking;

    @ApiModelProperty("求偿顺序 1、资本补充债 2、永续")
    private String insuranceSeniorityRankingStr;

    public Integer getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(Integer businessNature) {
        this.businessNature = businessNature;
    }

    public String getBusinessNatureStr() {
        return businessNatureStr;
    }

    public void setBusinessNatureStr(String businessNatureStr) {
        this.businessNatureStr = businessNatureStr;
    }

    public Integer getInsuranceSeniorityRanking() {
        return insuranceSeniorityRanking;
    }

    public void setInsuranceSeniorityRanking(Integer insuranceSeniorityRanking) {
        this.insuranceSeniorityRanking = insuranceSeniorityRanking;
    }

    public String getInsuranceSeniorityRankingStr() {
        return insuranceSeniorityRankingStr;
    }

    public void setInsuranceSeniorityRankingStr(String insuranceSeniorityRankingStr) {
        this.insuranceSeniorityRankingStr = insuranceSeniorityRankingStr;
    }
}
