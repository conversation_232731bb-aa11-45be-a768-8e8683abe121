package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 曲线生成状态
 *
 * <AUTHOR>
 */
public enum CurveGenerateStatusEnum implements ITextValueEnum {
    /**
     * 等待生成
     */
    WAITING(1, "待生成"),
    GENERATING(2, "生成中"),
    SUCCEED(3, "生成成功"),
    FAILED(4, "生成失败");

    CurveGenerateStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    private final int value;

    private final String text;

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }
}
