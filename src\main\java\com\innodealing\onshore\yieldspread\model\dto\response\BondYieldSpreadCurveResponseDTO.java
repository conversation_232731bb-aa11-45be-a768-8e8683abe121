package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 单券利差曲线响应DTO
 *
 * <AUTHOR>
 */
public class BondYieldSpreadCurveResponseDTO {

    /**
     * 利差日期
     */
    @ApiModelProperty("利差日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date spreadDate;
    /**
     * 信用利差
     */
    @ApiModelProperty("债券信用利差(中位数)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondCreditSpread;
    /**
     * 超额利差
     */
    @ApiModelProperty("债券超额利差(中位数)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondExcessSpread;
    /**
     * 估值收益率
     */
    @ApiModelProperty("估值收益率(中位数)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal cbYield;

    /**
     * 信用利差
     */
    @ApiModelProperty("债券信用利差(平均数)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal avgBondCreditSpread;
    /**
     * 超额利差
     */
    @ApiModelProperty("债券超额利差(平均数)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal avgBondExcessSpread;
    /**
     * 估值收益率
     */
    @ApiModelProperty("估值收益率")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal avgCbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }


    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }
}
