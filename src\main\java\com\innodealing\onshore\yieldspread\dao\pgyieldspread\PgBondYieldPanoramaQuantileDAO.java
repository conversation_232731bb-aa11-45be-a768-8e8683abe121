package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBondYieldPanoramaQuantileMapper;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldPanoramaBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaQuantileDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 债券收益率全景-历史分位DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgBondYieldPanoramaQuantileDAO {

    public static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d";
    private static final String BOND_YIELD_PANORAMA_QUANTILE_PK = "yieldSpread:bondYieldPanoramaQuantilePk";
    @Resource
    private PgBondYieldPanoramaQuantileMapper bondYieldPanoramaQuantileMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;

    /**
     * 收益率全景查询
     *
     * @param bondTypeList 债券品种列表
     * @param spreadDate   利率时间
     * @param quantileType 分位类型 1:3年 2:5年
     * @return 收益率全景
     */
    public List<PgBondYieldPanoramaBO> listYieldPanoramas(List<Integer> bondTypeList, Date spreadDate, Integer quantileType) {
        if (Objects.isNull(spreadDate)) {
            return Lists.newArrayList();
        }
        Long minPk = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<PgBondYieldPanoramaQuantileDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldPanoramaQuantileDO.class)
                .and(PgBondYieldPanoramaQuantileDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaQuantileDO::getId, lessThanOrEqual(maxPk))
                .and(PgBondYieldPanoramaQuantileDO::getQuantileType, isEqual(quantileType))
                .and(CollectionUtils.isNotEmpty(bondTypeList), PgBondYieldPanoramaQuantileDO::getBondType, in(bondTypeList))
                .and(PgBondYieldPanoramaQuantileDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<PgBondYieldPanoramaQuantileDO> pgBondYieldPanoramaChangeDOList =
                bondYieldPanoramaQuantileMapper.selectByDynamicQuery(dynamicQuery);
        return CollectionUtils.isNotEmpty(pgBondYieldPanoramaChangeDOList) ?
                BeanCopyUtils.copyList(pgBondYieldPanoramaChangeDOList, PgBondYieldPanoramaBO.class) : Lists.newArrayList();
    }

    /**
     * 保存债券收益率全景-历史分位数据
     *
     * @param issueDate                 发行日期
     * @param yieldPanoramaQuantileList 债券收益率全景-历史分位数据
     * @return int 保存行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int saveBondYieldPanoramaList(@NonNull Date issueDate, List<PgBondYieldPanoramaQuantileDO> yieldPanoramaQuantileList) {
        if (CollectionUtils.isEmpty(yieldPanoramaQuantileList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        Set<Integer> curveCodes = yieldPanoramaQuantileList.stream().map(PgBondYieldPanoramaQuantileDO::getCurveCode).collect(Collectors.toSet());
        DynamicQuery<PgBondYieldPanoramaQuantileDO> query = DynamicQuery.createQuery(PgBondYieldPanoramaQuantileDO.class)
                .and(PgBondYieldPanoramaQuantileDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaQuantileDO::getId, lessThanOrEqual(maxPk))
                .and(PgBondYieldPanoramaQuantileDO::getCurveCode, in(curveCodes));
        Map<String, PgBondYieldPanoramaQuantileDO> curveCodeIssueDateMap = bondYieldPanoramaQuantileMapper.selectByDynamicQuery(query).stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        List<PgBondYieldPanoramaQuantileDO> insertList = new ArrayList<>();
        List<PgBondYieldPanoramaQuantileDO> updateList = new ArrayList<>();
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        for (PgBondYieldPanoramaQuantileDO bondYieldPanoramaQuantileDO : yieldPanoramaQuantileList) {
            String key = this.getKey(bondYieldPanoramaQuantileDO);
            if (curveCodeIssueDateMap.containsKey(key)) {
                PgBondYieldPanoramaQuantileDO existBondYieldPanoramaQuantile = curveCodeIssueDateMap.get(key);
                bondYieldPanoramaQuantileDO.setId(existBondYieldPanoramaQuantile.getId());
                bondYieldPanoramaQuantileDO.setCreateTime(null);
                bondYieldPanoramaQuantileDO.setUpdateTime(now);
                updateList.add(bondYieldPanoramaQuantileDO);
            } else {
                bondYieldPanoramaQuantileDO.setId(redisService.generatePk(BOND_YIELD_PANORAMA_QUANTILE_PK, bondYieldPanoramaQuantileDO.getIssueDate()));
                bondYieldPanoramaQuantileDO.setCreateTime(now);
                bondYieldPanoramaQuantileDO.setUpdateTime(now);
                insertList.add(bondYieldPanoramaQuantileDO);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param panorama 债券收益率全景分位数
     * @return {@link String} key值
     */
    private String getKey(PgBondYieldPanoramaQuantileDO panorama) {
        return String.format(NAMESPACE_KEY_PLACEHOLDER, panorama.getCurveCode(), panorama.getQuantileType(), panorama.getIssueDate().getTime());
    }

    /**
     * 批量更新
     *
     * @param updateList 债券收益率全景分位数
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgBondYieldPanoramaQuantileDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldPanoramaQuantileMapper> updateBatchAction =
                MapperBatchAction.create(PgBondYieldPanoramaQuantileMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldPanoramaQuantileDO quantile : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgBondYieldPanoramaQuantileDO> updateQuery = DynamicQuery.createQuery(PgBondYieldPanoramaQuantileDO.class)
                        .and(PgBondYieldPanoramaQuantileDO::getId, isEqual(quantile.getId()));
                mapper.updateSelectiveByDynamicQuery(quantile, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 债券收益率全景分位数
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgBondYieldPanoramaQuantileDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldPanoramaQuantileMapper> insertBatchAction =
                MapperBatchAction.create(PgBondYieldPanoramaQuantileMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldPanoramaQuantileDO quantile : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(quantile));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 债券收益率全景基础数据
     *
     * @param issueDate  发行日期
     * @param curveCodes curveCode集合
     * @return {@link List}<{@link PgBondYieldPanoramaQuantileDO}> 债券收益率全景基础数据
     */
    public List<PgBondYieldPanoramaQuantileDO> listBondYieldPanoramaQuantiles(Collection<Integer> curveCodes, @NonNull Date issueDate) {
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        DynamicQuery<PgBondYieldPanoramaQuantileDO> query = DynamicQuery.createQuery(PgBondYieldPanoramaQuantileDO.class)
                .and(PgBondYieldPanoramaQuantileDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaQuantileDO::getId, lessThanOrEqual(maxPk))
                .and(CollectionUtils.isNotEmpty(curveCodes), PgBondYieldPanoramaQuantileDO::getCurveCode, in(curveCodes))
                .and(PgBondYieldPanoramaQuantileDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return bondYieldPanoramaQuantileMapper.selectByDynamicQuery(query);
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgBondYieldPanoramaQuantileDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldPanoramaQuantileDO.class)
                .orderBy(PgBondYieldPanoramaQuantileDO::getIssueDate, SortDirections::desc);
        return bondYieldPanoramaQuantileMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgBondYieldPanoramaQuantileDO::getIssueDate);
    }
}
