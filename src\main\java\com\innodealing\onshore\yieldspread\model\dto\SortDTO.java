package com.innodealing.onshore.yieldspread.model.dto;

import com.github.wz2cool.dynamic.SortDirection;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 排序
 *
 * <AUTHOR>
 **/
public class SortDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("排序属性")
    private String propertyName;
    @ApiModelProperty("排序方向 ASC|DESC")
    private SortDirection sortDirection;

    public SortDTO() {
    }

    /**
     * 排序DTO
     *
     * @param propertyName  属性名
     * @param sortDirection 排序方向
     */
    public SortDTO(String propertyName, SortDirection sortDirection) {
        this.propertyName = propertyName;
        this.sortDirection = sortDirection;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public SortDirection getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(SortDirection sortDirection) {
        this.sortDirection = sortDirection;
    }
}
