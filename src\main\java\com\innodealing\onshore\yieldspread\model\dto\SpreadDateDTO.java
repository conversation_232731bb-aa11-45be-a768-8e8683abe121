package com.innodealing.onshore.yieldspread.model.dto;

import java.io.Serializable;
import java.sql.Date;
import java.util.Objects;

/**
 * 利差日期
 *
 * <AUTHOR>
 */
public class SpreadDateDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 最新利差日期
     */
    private Date newSpreadDate;

    /**
     * 90天前利差日期
     */
    private Date before90Date;

    /**
     * 180天前利差日期
     */
    private Date before180Date;

    public SpreadDateDTO() {}

    /**
     * 利差日期封装类
     *
     * @param newSpreadDate 最新利差日期
     * @param before90Date  三个月前利差日期
     * @param before180Date 六个月前利差日期
     */
    public SpreadDateDTO(Date newSpreadDate, Date before90Date, Date before180Date) {
        this.newSpreadDate = Objects.isNull(newSpreadDate) ? null : new Date(newSpreadDate.getTime());
        this.before90Date = Objects.isNull(before90Date) ? null : new Date(before90Date.getTime());
        this.before180Date = Objects.isNull(before180Date) ? null : new Date(before180Date.getTime());
    }

    public Date getNewSpreadDate() {
        return Objects.isNull(newSpreadDate) ? null : new Date(newSpreadDate.getTime());
    }

    public void setNewSpreadDate(Date newSpreadDate) {
        this.newSpreadDate = Objects.isNull(newSpreadDate) ? null : new Date(newSpreadDate.getTime());
    }

    public Date getBefore90Date() {
        return Objects.isNull(before90Date) ? null : new Date(before90Date.getTime());
    }

    public void setBefore90Date(Date before90Date) {
        this.before90Date = Objects.isNull(before90Date) ? null : new Date(before90Date.getTime());
    }

    public Date getBefore180Date() {
        return Objects.isNull(before180Date) ? null : new Date(before180Date.getTime());
    }

    public void setBefore180Date(Date before180Date) {
        this.before180Date = Objects.isNull(before180Date) ? null : new Date(before180Date.getTime());
    }
}
