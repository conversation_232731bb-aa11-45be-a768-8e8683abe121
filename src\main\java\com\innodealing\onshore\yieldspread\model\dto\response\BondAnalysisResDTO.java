package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 导入债券解析结果
 *
 * <AUTHOR>
 */
public class BondAnalysisResDTO {

    @ApiModelProperty("正常债券")
    private List<BondBasicInfoResDTO> normalBonds;

    @ApiModelProperty("无效的不存在的或非利差债券")
    private List<String> invalidBonds;

    public List<BondBasicInfoResDTO> getNormalBonds() {
        return Objects.isNull(normalBonds) ? new ArrayList<>() : new ArrayList<>(normalBonds);
    }

    public void setNormalBonds(List<BondBasicInfoResDTO> normalBonds) {
        this.normalBonds = Objects.isNull(normalBonds) ? new ArrayList<>() : new ArrayList<>(normalBonds);
    }

    public List<String> getInvalidBonds() {
        return Objects.isNull(invalidBonds) ? new ArrayList<>() : new ArrayList<>(invalidBonds);
    }

    public void setInvalidBonds(List<String> invalidBonds) {
        this.invalidBonds = Objects.isNull(invalidBonds) ? new ArrayList<>() : new ArrayList<>(invalidBonds);
    }

}
