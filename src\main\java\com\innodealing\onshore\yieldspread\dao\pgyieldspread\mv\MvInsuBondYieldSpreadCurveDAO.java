package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.mv.MvInsuShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvInsuBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvInsuBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 保险利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvInsuBondYieldSpreadCurveDAO {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MvInsuBondYieldSpreadCurveMapper mvInsuBondYieldSpreadCurveMapper;

    @Resource
    private MvInsuShardBondYieldSpreadCurveMapper mvInsuShardBondYieldSpreadCurveMapper;

    /**
     * 创建或刷新保险利差曲线物化视图
     *
     * @param router 路由
     * @param param  刷新参数
     */
    public void createOrRefreshInsuCurveMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        String tableName = this.getMvName(router);
        logger.info("[refreshMvInsuBondYieldSpreadBondCurveRouter]开始创建保险评级分片物化视图:{}", tableName);
        MvInsuBondYieldSpreadCurveParameter parameter = this.builderShardMvParam(router);
        parameter.setTableName(tableName);
        if (param.isMvRefresh()) {
            mvInsuBondYieldSpreadCurveMapper.dropMv(tableName);
            mvInsuBondYieldSpreadCurveMapper.createMvRatingRouter(parameter);
        } else {
            mvInsuBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurve(tableName);
        }
    }

    /**
     * 时间范围不为空，删除临时表
     *
     * @param router 路由
     */
    public void droTempMv(AbstractRatingRouter router) {
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (Objects.nonNull(spreadDateRange)) {
            mvInsuBondYieldSpreadCurveMapper.dropMv(this.getMvName(router));
        }
    }

    private MvInsuBondYieldSpreadCurveParameter builderShardMvParam(AbstractRatingRouter router) {
        MvInsuBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, MvInsuBondYieldSpreadCurveParameter.class);
        parameter.setImpliedRatingMappings(router.getRatings());
        return parameter;
    }

    /**
     * 根据路由获取物化视图名称
     *
     * @param router 路由
     * @return mvName
     */
    public String getMvName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()),
                mvInsuShardBondYieldSpreadCurveMapper.getLogicTable(), RatingCombinationHelper.getMvNameSuffix(router));
    }

}
