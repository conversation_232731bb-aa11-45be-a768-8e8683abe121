package com.innodealing.onshore.yieldspread.config.shardingsphere;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.hint.HintShardingValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * 利差统计口径分片参数
 *
 * <AUTHOR>
 */
public class StrParamHintShardingAlgorithm implements HintShardingAlgorithm<String> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String STR_PARAM_HINT_TABLE_FORMAT = "%s_%s";

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, HintShardingValue<String> shardingValue) {
        String logicTableName = shardingValue.getLogicTableName();
        Collection<String> values = shardingValue.getValues();
        if (CollectionUtils.isEmpty(values)) {
            return Collections.singletonList(logicTableName);
        }
        return values.stream().map(hindParam -> formatShardTableName(logicTableName, hindParam)).collect(Collectors.toList());
    }


    private String formatShardTableName(String logicTableName, String hindParam) {
        String format = String.format(STR_PARAM_HINT_TABLE_FORMAT, logicTableName, hindParam);
        logger.info("[StrParamHintShardingAlgorithm] doSharding,shardTableName:{}", format);
        return format;
    }

}
