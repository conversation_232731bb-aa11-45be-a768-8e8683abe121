package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * 银行单券利差
 *
 * <AUTHOR>
 */
public class BankSingleBondYieldSpreadResDTO extends BaseSingleBondYieldSpreadResDTO {

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.BankTypeEnum
     */
    @ApiModelProperty("银行类型 2:国有银行,3:股份制银行,4:城商行,5:农商行")
    private Integer bankType;

    @ApiModelProperty("银行类型 2:国有银行,3:股份制银行,4:城商行,5:农商行")
    private String bankTypeStr;

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.BankSeniorityRankingEnum
     */
    @ApiModelProperty("求偿顺序 1:普通,2:二级资本债,3:永续")
    private Integer bankSeniorityRanking;

    @ApiModelProperty("求偿顺序 1:普通,2:二级资本债,3:永续")
    private String bankSeniorityRankingStr;

    public Integer getBankType() {
        return bankType;
    }

    public void setBankType(Integer bankType) {
        this.bankType = bankType;
    }

    public Integer getBankSeniorityRanking() {
        return bankSeniorityRanking;
    }

    public void setBankSeniorityRanking(Integer bankSeniorityRanking) {
        this.bankSeniorityRanking = bankSeniorityRanking;
    }

    public String getBankTypeStr() {
        return bankTypeStr;
    }

    public void setBankTypeStr(String bankTypeStr) {
        this.bankTypeStr = bankTypeStr;
    }

    public String getBankSeniorityRankingStr() {
        return bankSeniorityRankingStr;
    }

    public void setBankSeniorityRankingStr(String bankSeniorityRankingStr) {
        this.bankSeniorityRankingStr = bankSeniorityRankingStr;
    }

}
