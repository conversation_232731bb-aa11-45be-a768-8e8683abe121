package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * 债券基本信息
 *
 * <AUTHOR>
 */
public class BondBasicInfoResDTO {

    @ApiModelProperty("债券唯一代码")
    private Long bondUniCode;

    @ApiModelProperty("债券代码")
    private String bondCode;

    @ApiModelProperty("债券简称")
    private String bondShortName;

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

}
