package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvUdicComYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.MvUdicBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvUdicComYieldSpreadCurveDO;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.persistence.Table;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;

/**
 * 城投利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvUdicComYieldSpreadCurveDAO extends AbstractMvComYieldSpreadCurveDAO<MvUdicBondYieldSpreadCurveParameter> {
    private static final String TABLE_NAME = MvUdicComYieldSpreadCurveDO.class.getAnnotation(Table.class).name();

    private MvUdicComYieldSpreadCurveMapper mvUdicComYieldSpreadCurveMapper;

    /**
     * 构造函数
     *
     * @param mvUdicComYieldSpreadCurveMapper mapper
     */
    protected MvUdicComYieldSpreadCurveDAO(MvUdicComYieldSpreadCurveMapper mvUdicComYieldSpreadCurveMapper) {
        super(mvUdicComYieldSpreadCurveMapper);
        this.mvUdicComYieldSpreadCurveMapper = mvUdicComYieldSpreadCurveMapper;
    }

    @Override
    public List<ComYieldSpreadCurveBO> list(@NonNull Long comUniCode, Integer spreadBondType) {
        DynamicQuery<MvUdicComYieldSpreadCurveDO> query = DynamicQuery.createQuery(MvUdicComYieldSpreadCurveDO.class)
                .and(MvUdicComYieldSpreadCurveDO::getComUniCode, x -> x.isEqual(comUniCode))
                .and(Objects.nonNull(spreadBondType), MvUdicComYieldSpreadCurveDO::getSpreadBondType, x -> x.isEqual(spreadBondType))
                .and(Objects.isNull(spreadBondType), MvUdicComYieldSpreadCurveDO::getUsingSpreadBondType, x -> x.isEqual(UNUSED_FIELD_GROUP.getValue()))
                .orderBy(MvUdicComYieldSpreadCurveDO::getSpreadDate, SortDirections::asc);
        List<MvUdicComYieldSpreadCurveDO> mvUdicComYieldSpreadCurveList = mvUdicComYieldSpreadCurveMapper.selectByDynamicQuery(query);
        return mvUdicComYieldSpreadCurveList.stream().map(super::handlePrecision).collect(Collectors.toList());

    }

    @Override
    protected String tableName() {
        return TABLE_NAME;
    }
}
