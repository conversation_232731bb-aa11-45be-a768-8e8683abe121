package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 利差利率变动周期枚举
 *
 * <AUTHOR>
 * @date 2023/4/26 15:54
 */
public enum YieldSpreadChartTypeEnum implements ITextValueEnum {

    /**
     * 变动周期枚举
     */
    MATU_SPREAD(1, "到期收益率", "matu", "%s到期收益率", 1),
    CREDIT_SPREAD(2, "信用利差", "credit", "%s-国开", 2),
    GRADE_SPREAD(3, "等级利差", "grade", "%s等级利差", 4),
    TENOR_SPREAD(4, "期限利差", "tenor", "%s期限利差", 5),
    VARIETY_SPREAD(5, "品种利差", "variety", "%s-中短期票据", 6),
    TERM_SPREAD(6, "条款利差", "term", "%s-普通债", 8),
    CREDIT_SUB_CB_SPREAD(7, "信用(减国债)利差", "creditSubCb", "%s-国债", 3),
    VARIETY_SPREAD_SUB_CB(8, "品种利差", "varietySubCb", "%s-国债", 7);
    private final Integer code;
    private final String text;
    private final String excelDataName;
    private final String excelDataDec;
    private final Integer typeSort;

    /*** 国债*/
    private static final Set<YieldSpreadChartTypeEnum> CHINA_BONDS =
            EnumSet.of(MATU_SPREAD, TENOR_SPREAD);

    /*** 国开债*/
    private static final Set<YieldSpreadChartTypeEnum> CHINA_KAI_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SUB_CB_SPREAD, TENOR_SPREAD);

    /*** 中短期票据*/
    private static final Set<YieldSpreadChartTypeEnum> MEDIUM_AND_SHORT_TERMS_NOTES =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, GRADE_SPREAD, TENOR_SPREAD);
    /*** 产业债*/
    private static final Set<YieldSpreadChartTypeEnum> INDUSTRIAL_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, GRADE_SPREAD, TENOR_SPREAD, VARIETY_SPREAD);
    /*** 城投债*/
    private static final Set<YieldSpreadChartTypeEnum> URBAN_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, GRADE_SPREAD, TENOR_SPREAD, VARIETY_SPREAD);
    /*** 银行普通债*/
    private static final Set<YieldSpreadChartTypeEnum> GENERAL_BANK_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, GRADE_SPREAD, TENOR_SPREAD, VARIETY_SPREAD);
    /*** 银行二级资本债*/
    private static final Set<YieldSpreadChartTypeEnum> BANK_SECONDARY_CAPITAL_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, GRADE_SPREAD, TENOR_SPREAD, VARIETY_SPREAD, TERM_SPREAD);
    /*** 银行永续债*/
    private static final Set<YieldSpreadChartTypeEnum> BANK_PERPETUAL_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, GRADE_SPREAD, TENOR_SPREAD, VARIETY_SPREAD, TERM_SPREAD);
    /*** 证券公司债*/
    private static final Set<YieldSpreadChartTypeEnum> SECURITIES_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, GRADE_SPREAD, TENOR_SPREAD, VARIETY_SPREAD);
    /*** 证券次级债*/
    private static final Set<YieldSpreadChartTypeEnum> SECURITIES_SUB_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, TENOR_SPREAD, TERM_SPREAD);
    /*** 证券永续债*/
    private static final Set<YieldSpreadChartTypeEnum> SECURITIES_PERPETUAL_BONDS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, TENOR_SPREAD, TERM_SPREAD);
    /*** 同业存单*/
    private static final Set<YieldSpreadChartTypeEnum> NCDS = EnumSet.of(MATU_SPREAD, GRADE_SPREAD, TENOR_SPREAD);

    /*** 保险资本补充*/
    private static final Set<YieldSpreadChartTypeEnum> INSU_CAPITAL_SUPPLEMENTS =
            EnumSet.of(MATU_SPREAD, CREDIT_SPREAD, GRADE_SPREAD, TENOR_SPREAD, VARIETY_SPREAD);

    /*** 利率债*/
    private static final Set<YieldSpreadChartTypeEnum> INTEREST_RATE_BONDS =
            EnumSet.of(MATU_SPREAD, TENOR_SPREAD, VARIETY_SPREAD_SUB_CB);

    /**
     * 获取中短期票据chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getChinaBond() {
        return toValues(CHINA_BONDS);
    }

    /**
     * 获取中短期票据chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getChinaKaiBond() {
        return toValues(CHINA_KAI_BONDS);
    }

    /**
     * 获取中短期票据chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getMediumAndShortTermsNotes() {
        return toValues(MEDIUM_AND_SHORT_TERMS_NOTES);
    }

    /**
     * 获取产业债chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getIndustrialBonds() {
        return toValues(INDUSTRIAL_BONDS);
    }

    /**
     * 获取城投债chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getUrbanBonds() {
        return toValues(URBAN_BONDS);
    }

    /**
     * 获取银行普通债chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getGeneralBankBonds() {
        return toValues(GENERAL_BANK_BONDS);
    }

    /**
     * 获取银行二级资本债chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getBankSecondaryCapitalBonds() {
        return toValues(BANK_SECONDARY_CAPITAL_BONDS);
    }

    /**
     * 获取银行永续债chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getBankPerpetualBonds() {
        return toValues(BANK_PERPETUAL_BONDS);
    }

    /**
     * 获取证券公司债chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getSecuritiesBonds() {
        return toValues(SECURITIES_BONDS);
    }

    /**
     * 获取证券公司二级债chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getSecuritiesSubBonds() {
        return toValues(SECURITIES_SUB_BONDS);
    }

    /**
     * 获取证券公司永续债chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getSecuritiesPerpetualBonds() {
        return toValues(SECURITIES_PERPETUAL_BONDS);
    }

    /**
     * 获取同业存单chartType
     *
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> getNcds() {
        return toValues(NCDS);
    }

    /**
     * 获取保险资本补充chartType
     *
     * @return
     */
    public static Set<Integer> getInsuCapitalSupplements() {
        return toValues(INSU_CAPITAL_SUPPLEMENTS);
    }

    /**
     * 获取利率债chartType
     *
     * @return
     */
    public static Set<Integer> getInterestRateBond() {
        return toValues(INTEREST_RATE_BONDS);
    }

    /**
     * 将chartType枚举集合转为value集合
     *
     * @param chartTypeEnums 图类型
     * @return {@link Set}<{@link Integer}>
     */
    public static Set<Integer> toValues(Set<YieldSpreadChartTypeEnum> chartTypeEnums) {
        return chartTypeEnums.stream().map(YieldSpreadChartTypeEnum::getValue).collect(Collectors.toSet());
    }

    /**
     * 根据图类型获得 展示顺序
     *
     * @param chartType YieldSpreadChartTypeEnum code
     * @return YieldSpreadChartTypeEnum typeSort
     */
    public static Optional<Integer> getTypeSortByChartType(Integer chartType) {
        return Arrays.stream(YieldSpreadChartTypeEnum.values()).
                filter(x -> Integer.valueOf(x.getValue()).equals(chartType)).
                findAny().map(YieldSpreadChartTypeEnum::getTypeSort);
    }

    YieldSpreadChartTypeEnum(Integer code, String text, String excelDataName, String excelDataDec, Integer typeSort) {
        this.code = code;
        this.text = text;
        this.excelDataName = excelDataName;
        this.excelDataDec = excelDataDec;
        this.typeSort = typeSort;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }

    public String getExcelDataName() {
        return excelDataName;
    }

    public String getExcelDataDec() {
        return excelDataDec;
    }

    public Integer getTypeSort() {
        return typeSort;
    }
}
