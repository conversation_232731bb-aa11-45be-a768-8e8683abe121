package com.innodealing.onshore.yieldspread.model.dto;

import java.sql.Date;

import static java.util.Objects.isNull;

/**
 * 利差曲线分片查询参数 父类
 *
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S2974","squid:S1694"})
public abstract class AbstractShardParamDTO {

    /**
     * 利差日期
     */
    private Date spreadDate;

    /**
     * 开始利差日期
     */
    private Date startSpreadDate;

    /**
     * 结束利差日期
     */
    private Date endSpreadDate;

    /**
     * 隐含评级(AAA+,AAA,AAA-,AA+,AA,AA-,A+,A,A-)
     */
    private Integer[] bondImpliedRatingMappings;

    /**
     * yy评级(投资级:1、2、3、4、5,投机级:6、7、8、风险级:9、10)
     */
    private Integer[] comYyRatingMappings;

    private String level;

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Date getSpreadDate() {
        return isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Date getStartSpreadDate() {
        return isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public void setStartSpreadDate(Date startSpreadDate) {
        this.startSpreadDate = isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public Date getEndSpreadDate() {
        return isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public void setEndSpreadDate(Date endSpreadDate) {
        this.endSpreadDate = isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public Integer[] getBondImpliedRatingMappings() {
        return isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public void setBondImpliedRatingMappings(Integer[] bondImpliedRatingMappings) {
        this.bondImpliedRatingMappings = isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public Integer[] getComYyRatingMappings() {
        return isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

    public void setComYyRatingMappings(Integer[] comYyRatingMappings) {
        this.comYyRatingMappings = isNull(comYyRatingMappings) ? new Integer[0] : comYyRatingMappings.clone();
    }

}
