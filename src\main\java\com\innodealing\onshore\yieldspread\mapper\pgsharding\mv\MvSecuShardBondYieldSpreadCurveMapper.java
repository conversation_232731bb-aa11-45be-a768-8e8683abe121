package com.innodealing.onshore.yieldspread.mapper.pgsharding.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.mv.MvSecuShardBondYieldSpreadCurveDO;

import javax.persistence.Table;

/**
 * 证券利差曲线-物化视图
 *
 * <AUTHOR>
 */

public interface MvSecuShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<MvSecuShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     * @return tableName
     */
    default String getLogicTable() {
        return MvSecuShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }


}
