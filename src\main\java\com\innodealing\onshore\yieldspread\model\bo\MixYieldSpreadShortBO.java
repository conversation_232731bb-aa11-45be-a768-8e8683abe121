package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 混合利差曲线数据
 * 包含:
 * 利差一级分类(信用利差、超额利差) 和 二级分类(
 * 银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；
 * 证券主体：0 全部，1 普通，2：次级 ，3 永续 ;
 * 城投主体利差：0：全部，1 公募，2 私募，3 永续；
 * 行业：0：全部，1 公募，2 私募，3 永续
 * ) 的利差数据
 * 总计8个字段的数据
 *
 * <AUTHOR>
 */
public class MixYieldSpreadShortBO {

    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 主体唯一编码
     */
    private Long comUniCode;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    private BigDecimal comCreditSpread;
    /**
     * 超额利差(全部债券);单位(BP)
     */
    private BigDecimal comExcessSpread;
    /**
     * 二级分类 - 1
     * 银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；
     * 证券主体：0 全部，1 普通，2：次级 ，3 永续 ;
     * 城投主体利差：0：全部，1 公募，2 私募，3 永续；
     * 行业：0：全部，1 公募，2 私募，3 永续
     */
    private BigDecimal secondCreditYieldOne;

    /**
     * 二级分类 - 1
     * 银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；
     * 证券主体：0 全部，1 普通，2：次级 ，3 永续 ;
     * 城投主体利差：0：全部，1 公募，2 私募，3 永续；
     * 行业：0：全部，1 公募，2 私募，3 永续
     */
    private BigDecimal secondExcessYieldOne;

    /**
     * 二级分类 - 1
     * 银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；
     * 证券主体：0 全部，1 普通，2：次级 ，3 永续 ;
     * 城投主体利差：0：全部，1 公募，2 私募，3 永续；
     * 行业：0：全部，1 公募，2 私募，3 永续
     */
    private BigDecimal secondCreditYieldTwo;

    /**
     * 二级分类 - 1
     * 银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；
     * 证券主体：0 全部，1 普通，2：次级 ，3 永续 ;
     * 城投主体利差：0：全部，1 公募，2 私募，3 永续；
     * 行业：0：全部，1 公募，2 私募，3 永续
     */
    private BigDecimal secondExcessYieldTwo;

    /**
     * 二级分类 - 1
     * 银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；
     * 证券主体：0 全部，1 普通，2：次级 ，3 永续 ;
     * 城投主体利差：0：全部，1 公募，2 私募，3 永续；
     * 行业：0：全部，1 公募，2 私募，3 永续
     */
    private BigDecimal secondCreditYieldTree;

    /**
     * 二级分类 - 1
     * 银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；
     * 证券主体：0 全部，1 普通，2：次级 ，3 永续 ;
     * 城投主体利差：0：全部，1 公募，2 私募，3 永续；
     * 行业：0：全部，1 公募，2 私募，3 永续
     */
    private BigDecimal secondExcessYieldTree;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getSecondCreditYieldOne() {
        return secondCreditYieldOne;
    }

    public void setSecondCreditYieldOne(BigDecimal secondCreditYieldOne) {
        this.secondCreditYieldOne = secondCreditYieldOne;
    }

    public BigDecimal getSecondExcessYieldOne() {
        return secondExcessYieldOne;
    }

    public void setSecondExcessYieldOne(BigDecimal secondExcessYieldOne) {
        this.secondExcessYieldOne = secondExcessYieldOne;
    }

    public BigDecimal getSecondCreditYieldTwo() {
        return secondCreditYieldTwo;
    }

    public void setSecondCreditYieldTwo(BigDecimal secondCreditYieldTwo) {
        this.secondCreditYieldTwo = secondCreditYieldTwo;
    }

    public BigDecimal getSecondExcessYieldTwo() {
        return secondExcessYieldTwo;
    }

    public void setSecondExcessYieldTwo(BigDecimal secondExcessYieldTwo) {
        this.secondExcessYieldTwo = secondExcessYieldTwo;
    }

    public BigDecimal getSecondCreditYieldTree() {
        return secondCreditYieldTree;
    }

    public void setSecondCreditYieldTree(BigDecimal secondCreditYieldTree) {
        this.secondCreditYieldTree = secondCreditYieldTree;
    }

    public BigDecimal getSecondExcessYieldTree() {
        return secondExcessYieldTree;
    }

    public void setSecondExcessYieldTree(BigDecimal secondExcessYieldTree) {
        this.secondExcessYieldTree = secondExcessYieldTree;
    }
}