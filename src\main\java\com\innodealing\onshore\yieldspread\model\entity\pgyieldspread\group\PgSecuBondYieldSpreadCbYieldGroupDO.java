package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 证券债收益率计算中债收益率中位数组DO
 *
 * <AUTHOR>
 **/
@Table(name = "secu_bond_yield_spread")
public class PgSecuBondYieldSpreadCbYieldGroupDO {
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 证券公司债求偿顺序   1:普通（剔除永续）、2:次级（剔除永续）、 3:永续"
     */
    @Column
    private Integer securitySeniorityRanking;
    /**
     * 1y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 1 THEN cb_yield END)")
    private BigDecimal ytm1Y;
    /**
     * 2y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 2 THEN cb_yield END)")
    private BigDecimal ytm2Y;
    /**
     * 3y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 3 THEN cb_yield END)")
    private BigDecimal ytm3Y;
    /**
     * 4y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 4 THEN cb_yield END)")
    private BigDecimal ytm4Y;
    /**
     * 5y到期收益率
     */
    @Column(name = "median(CASE WHEN spread_remaining_tenor_tag = 5 THEN cb_yield END)")
    private BigDecimal ytm5Y;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getYtm1Y() {
        return ytm1Y;
    }

    public void setYtm1Y(BigDecimal ytm1Y) {
        this.ytm1Y = ytm1Y;
    }

    public BigDecimal getYtm2Y() {
        return ytm2Y;
    }

    public void setYtm2Y(BigDecimal ytm2Y) {
        this.ytm2Y = ytm2Y;
    }

    public BigDecimal getYtm3Y() {
        return ytm3Y;
    }

    public void setYtm3Y(BigDecimal ytm3Y) {
        this.ytm3Y = ytm3Y;
    }

    public BigDecimal getYtm4Y() {
        return ytm4Y;
    }

    public void setYtm4Y(BigDecimal ytm4Y) {
        this.ytm4Y = ytm4Y;
    }

    public BigDecimal getYtm5Y() {
        return ytm5Y;
    }

    public void setYtm5Y(BigDecimal ytm5Y) {
        this.ytm5Y = ytm5Y;
    }

    public Integer getSecuritySeniorityRanking() {
        return securitySeniorityRanking;
    }

    public void setSecuritySeniorityRanking(Integer securitySeniorityRanking) {
        this.securitySeniorityRanking = securitySeniorityRanking;
    }
}