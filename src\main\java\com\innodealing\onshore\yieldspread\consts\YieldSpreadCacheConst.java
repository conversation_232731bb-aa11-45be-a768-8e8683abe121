package com.innodealing.onshore.yieldspread.consts;

/**
 * 利差缓存常量类
 *
 * <AUTHOR>
 */
public final class YieldSpreadCacheConst {

    private YieldSpreadCacheConst() {
    }

    public static final String LIST_INDU_SPREAD_PANORAMAS_KEY = "onshoreYieldSpread:indu:list_spread_panoramas:%d";

    public static final String LIST_UDIC_SPREAD_PANORAMAS_KEY = "onshoreYieldSpread:udic:list_spread_panoramas:%d";

    public static final String LIST_INDU_SPREAD_CURVES_KEY = "onshoreYieldSpread:indu:list_spread_curves:%d";
    public static final String LIST_BOND_INDU_SPREAD_CURVES_KEY = "onshoreYieldSpread:bond:indu:list_spread_curves:%d";

    public static final String LIST_UDIC_SPREAD_CURVES_KEY = "onshoreYieldSpread:udic:list_spread_curves:%d";
    public static final String LIST_BOND_UDIC_SPREAD_CURVES_KEY = "onshoreYieldSpread:bond:udic:list_spread_curves:%d";

    public static final String LIST_BOND_BANK_SPREAD_CURVES_KEY = "onshoreYieldSpread:bond:bank:list_spread_curves:%d";

    public static final String LIST_BOND_INSU_SPREAD_CURVES_KEY = "onshoreYieldSpread:bond:insu:list_spread_curves:%d";

    public static final String LIST_BOND_SECU_SPREAD_CURVES_KEY = "onshoreYieldSpread:bond:secu:list_spread_curves:%d";

    public static final String LIST_COM_INDU_SPREAD_CURVES_KEY = "onshoreYieldSpread:com:indu:list_spread_curves:%d:%d";

    public static final String LIST_COM_UDIC_SPREAD_CURVES_KEY = "onshoreYieldSpread:com:udic:list_spread_curves:%d:%d";

    public static final String LIST_COM_BANK_SPREAD_CURVES_KEY = "onshoreYieldSpread:com:bank:list_spread_curves:%d:%d";

    public static final String LIST_COM_INSU_SPREAD_CURVES_KEY = "onshoreYieldSpread:com:insu:list_spread_curves:%d:%d";

    public static final String LIST_COM_SECU_SPREAD_CURVES_KEY = "onshoreYieldSpread:com:secu:list_spread_curves:%d:%d";

    public static final String MAX_INDU_BOND_SPREAD_DATE_KEY = "onshoreYieldSpread:indu:bond:max_spread_date";

    public static final String MAX_INDU_COM_SPREAD_DATE_KEY = "onshoreYieldSpread:indu:com:max_spread_date";

    public static final String MAX_UDIC_BOND_SPREAD_DATE_KEY = "onshoreYieldSpread:udic:bond:max_spread_date";

    public static final String MAX_UDIC_COM_SPREAD_DATE_KEY = "onshoreYieldSpread:udic:com:max_spread_date";

    public static final String SPREAD_DATE_LAST_WORK_DAY_KEY = "onshoreYieldSpread:last_work_day:%s:%s";
    public static final String SPREAD_TIME_RANGE_KEY = "onshoreYieldSpread:time_range:%s";

    public static final String SPREAD_INDU_COM_SPREAD_KEY = "onshoreYieldSpread:indu_com_spread:%d";

    public static final String SPREAD_INDU_COM_SPREAD_COUNT_KEY = "onshoreYieldSpread:indu_com_spread_count:%s";

    public static final String SPREAD_UDIC_COM_SPREAD_KEY = "onshoreYieldSpread:udic_com_spread:%d";

    public static final String SPREAD_UDIC_COM_SPREAD_COUNT_KEY = "onshoreYieldSpread:udic_com_spread_count:%s";

    public static final String SPREAD_BANK_COM_SPREAD_COUNT_KEY = "onshoreYieldSpread:bank_com_spread_count:%s";

    public static final String SPREAD_INSU_COM_SPREAD_COUNT_KEY = "onshoreYieldSpread:insu_com_spread_count:%s";

    public static final String SPREAD_SECU_COM_SPREAD_COUNT_KEY = "onshoreYieldSpread:secu_com_spread_count:%s";

    public static final String SPREAD_CUSTOM_COM_SPREAD_COUNT_KEY = "onshoreYieldSpread:custom_com_spread_count:%s";

    public static final String COUNT_CUSTOM_BOND_SPREAD_KEY = "onshoreYieldSpread:custom_bond_spread_count:%s";

    /**
     * 生成曲线时控制并发的Semaphore
     */
    public static final String SEMAPHORE_GENERATE_CURVE = "onshoreYieldSpread:curve_pool:generate_curve_semaphore";

    /**
     * 利差曲线缓存key
     * %d 曲线类型
     * %s 如果是自定义债：curveId，否则：筛选条件的json字符串的md5
     */
    public static final String SPREAD_CURVE_KEY = "onshoreYieldSpread:curve_pool:curve_data:%d:%s";

    public static final String MAX_BANK_BOND_SPREAD_DATE_KEY = "onshoreYieldSpread:bank:bond:max_spread_date";

    public static final String MAX_BANK_COM_SPREAD_DATE_KEY = "onshoreYieldSpread:bank:com:max_spread_date";

    public static final String MAX_SECU_BOND_SPREAD_DATE_KEY = "onshoreYieldSpread:secu:bond:max_spread_date";

    public static final String MAX_SECU_COM_SPREAD_DATE_KEY = "onshoreYieldSpread:secu:com:max_spread_date";

    public static final String MAX_INSU_BOND_SPREAD_DATE_KEY = "onshoreYieldSpread:insu:bond:max_spread_date";

    public static final String MAX_INSU_COM_SPREAD_DATE_KEY = "onshoreYieldSpread:insu:com:max_spread_date";

    public static final String MAX_CUSTOM_BOND_SPREAD_DATE_KEY = "onshoreYieldSpread:custom:bond:max_spread_date";

    public static final String MAX_CUSTOM_COM_SPREAD_DATE_KEY = "onshoreYieldSpread:custom:com:max_spread_date";

    public static final String BOND_YIELD_SPREAD_PK_GENERATE_KEY = "onshoreYieldSpread:bond_yield_spread:pk";

    /**
     * 用于缓存 利差曲线分析中 主体 key 是否是今天第一次查询的key。如果是第一次查询，则默认查询过去三年的数据到缓存中，否则的话，直接根据传入的时间进行查询
     * 第一个是利差行业分类:第二个是主体
     */
    public static final String RELAY_COM_YIELD_ZSET_KEY = "onshoreYieldSpread:replay_com_yield_zset_key:%s:%s";

    /**
     * 用户配置redis key
     */
    public static final String USER_SPREAD_CONFIG_KEY = "onshoreYieldSpread:user_spread_config:%s:%s";

    /**
     * 用户配置redis 默认值空值
     */
    public static final String USER_SPREAD_CONFIG_SPACE_DEFAULT_VALUE = "[]";

    public static final Long CACHE_FOUR_HOURS = 4L;

    public static final Long CACHE_ONE_DAYS = 1L;

    public static final Long CACHE_EIGHT_HOURS = 8L;

    public static final int EXPIRATION_HOUR = 5;

}
