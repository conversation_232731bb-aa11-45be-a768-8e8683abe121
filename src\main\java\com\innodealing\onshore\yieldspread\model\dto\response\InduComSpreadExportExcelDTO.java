package com.innodealing.onshore.yieldspread.model.dto.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.innodealing.international.common.template.source.excel.convert.ExcelDateConvert;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.BaseCurveYieldSpreadExcelDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.CURVE_EXPORT_TITLE;

/**
 * 行业利差主体成分数据导出DTO
 *
 * <AUTHOR>
 */
public class InduComSpreadExportExcelDTO extends BaseCurveYieldSpreadExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("发行人")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "发行人"})
    @ColumnWidth(25)
    private String comUniName;

    @ApiModelProperty("利差日期")
    @ExcelProperty(value = {CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "利差日期"}, converter = ExcelDateConvert.class)
    @ColumnWidth(25)
    private Date spreadDate;

    @ApiModelProperty("主体外部评级")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "主体评级"})
    @ColumnWidth(25)
    private String comExtRating;

    @ApiModelProperty("企业性质")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "企业性质"})
    @ColumnWidth(25)
    private String businessNatureName;

    @ApiModelProperty("所属行业")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "所属行业"})
    @ColumnWidth(25)
    private String induLevel1Name;

    @ApiModelProperty("存量债规模(亿)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "存量债规模(亿)"})
    @ColumnWidth(25)
    private BigDecimal bondBalance;

    @ApiModelProperty("总资产(亿)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "总资产(亿)"})
    @ColumnWidth(25)
    private BigDecimal totalAssets;

    @ApiModelProperty("净利润(亿)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "净利润(亿)"})
    @ColumnWidth(25)
    private BigDecimal netProfit;

    @ApiModelProperty("净经营性活动现金流(亿)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "净经营性活动现金流(亿)"})
    @ColumnWidth(25)
    private BigDecimal netOperatingCashFlow;

    @ApiModelProperty("信用利差(BP)-全部债券")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)-全部债券"})
    @ColumnWidth(25)
    private BigDecimal comCreditSpread;

    @ApiModelProperty("信用利差 近三月变动(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差-近三月变动(BP)"})
    @ColumnWidth(25)
    private BigDecimal creditSpreadChange3M;

    @ApiModelProperty("信用利差 近六月变动(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差-近六月变动(BP)"})
    @ColumnWidth(25)
    private BigDecimal creditSpreadChange6M;

    @ApiModelProperty("信用利差3年历史分位")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "3年历史分位(%)"})
    @ColumnWidth(25)
    private BigDecimal creditSpreadQuantile3Y;

    @ApiModelProperty("信用利差5年历史分位")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "5年历史分位(%)"})
    @ColumnWidth(25)
    private BigDecimal creditSpreadQuantile5Y;

    @ApiModelProperty("信用利差(BP)-公募")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)-公募"})
    @ColumnWidth(25)
    private BigDecimal comPublicCreditSpread;

    @ApiModelProperty("信用利差(BP)-私募")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)-私募"})
    @ColumnWidth(25)
    private BigDecimal comPrivateCreditSpread;

    @ApiModelProperty("信用利差(BP)-永续")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "信用利差(BP)-永续"})
    @ColumnWidth(25)
    private BigDecimal comPerpetualCreditSpread;

    @ApiModelProperty("超额利差(BP)-全部债券")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)-全部债券"})
    @ColumnWidth(25)
    private BigDecimal comExcessSpread;

    @ApiModelProperty("超额利差 近三月变动(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差-近三月变动(BP)"})
    @ColumnWidth(25)
    private BigDecimal excessSpreadChange3M;

    @ApiModelProperty("超额利差 近六月变动(BP)")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差-近六月变动(BP)"})
    @ColumnWidth(25)
    private BigDecimal excessSpreadChange6M;

    @ApiModelProperty("超额利差3年历史分位")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "3年历史分位(%)"})
    @ColumnWidth(25)
    private BigDecimal excessSpreadQuantile3Y;

    @ApiModelProperty("超额利差5年历史分位")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "5年历史分位(%)"})
    @ColumnWidth(25)
    private BigDecimal excessSpreadQuantile5Y;

    @ApiModelProperty("超额利差(BP)-公募")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)-公募"})
    @ColumnWidth(25)
    private BigDecimal comPublicExcessSpread;

    @ApiModelProperty("超额利差(BP)-私募")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)-私募"})
    @ColumnWidth(25)
    private BigDecimal comPrivateExcessSpread;

    @ApiModelProperty("超额利差(BP)-永续")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "超额利差(BP)-永续"})
    @ColumnWidth(25)
    private BigDecimal comPerpetualExcessSpread;

    @ApiModelProperty("估值收益率-全部债券")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率-全部债券"})
    @ColumnWidth(25)
    private BigDecimal comCbYield;

    @ApiModelProperty("估值收益率-公募")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率-公募"})
    @ColumnWidth(25)
    private BigDecimal comPublicCbYield;

    @ApiModelProperty("估值收益率-私募")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率-私募"})
    @ColumnWidth(25)
    private BigDecimal comPrivateCbYield;

    @ApiModelProperty("估值收益率-永续")
    @ExcelProperty({CURVE_EXPORT_TITLE, CURVE_EXPORT_TITLE, "估值收益率-永续"})
    @ColumnWidth(25)
    private BigDecimal comPerpetualCbYield;

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    public BigDecimal getNetOperatingCashFlow() {
        return netOperatingCashFlow;
    }

    public void setNetOperatingCashFlow(BigDecimal netOperatingCashFlow) {
        this.netOperatingCashFlow = netOperatingCashFlow;
    }

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public String getComExtRating() {
        return comExtRating;
    }

    public void setComExtRating(String comExtRating) {
        this.comExtRating = comExtRating;
    }

    public String getBusinessNatureName() {
        return businessNatureName;
    }

    public void setBusinessNatureName(String businessNatureName) {
        this.businessNatureName = businessNatureName;
    }

    public String getInduLevel1Name() {
        return induLevel1Name;
    }

    public void setInduLevel1Name(String induLevel1Name) {
        this.induLevel1Name = induLevel1Name;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComPublicCreditSpread() {
        return comPublicCreditSpread;
    }

    public void setComPublicCreditSpread(BigDecimal comPublicCreditSpread) {
        this.comPublicCreditSpread = comPublicCreditSpread;
    }

    public BigDecimal getComPrivateCreditSpread() {
        return comPrivateCreditSpread;
    }

    public void setComPrivateCreditSpread(BigDecimal comPrivateCreditSpread) {
        this.comPrivateCreditSpread = comPrivateCreditSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComPublicExcessSpread() {
        return comPublicExcessSpread;
    }

    public void setComPublicExcessSpread(BigDecimal comPublicExcessSpread) {
        this.comPublicExcessSpread = comPublicExcessSpread;
    }

    public BigDecimal getComPrivateExcessSpread() {
        return comPrivateExcessSpread;
    }

    public void setComPrivateExcessSpread(BigDecimal comPrivateExcessSpread) {
        this.comPrivateExcessSpread = comPrivateExcessSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComPublicCbYield() {
        return comPublicCbYield;
    }

    public void setComPublicCbYield(BigDecimal comPublicCbYield) {
        this.comPublicCbYield = comPublicCbYield;
    }

    public BigDecimal getComPrivateCbYield() {
        return comPrivateCbYield;
    }

    public void setComPrivateCbYield(BigDecimal comPrivateCbYield) {
        this.comPrivateCbYield = comPrivateCbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }
}
