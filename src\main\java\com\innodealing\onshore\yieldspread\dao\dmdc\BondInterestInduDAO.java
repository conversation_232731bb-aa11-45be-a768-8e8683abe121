package com.innodealing.onshore.yieldspread.dao.dmdc;

import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.innodealing.onshore.yieldspread.mapper.dmdc.BondInterestInduMapper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.BondInterestInduDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.group.BondInterestInduGroupDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;
import java.util.Set;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 产业债利差(老表) DAO
 *
 * <AUTHOR>
 **/
@Repository
public class BondInterestInduDAO {

    @Resource
    private BondInterestInduMapper bondInterestInduMapper;

    /**
     * 产业债数据
     *
     * @param interestDate 利差日期
     * @param bondUniCodes 债券code
     * @return 产业债数据
     */
    public List<BondInterestInduDO> listBondInterestInduDOByInterestDate(Date interestDate, Set<Long> bondUniCodes) {
        GroupedQuery<BondInterestInduGroupDO, BondInterestInduDO> groupedQuery =
                GroupByQuery.createQuery(BondInterestInduGroupDO.class, BondInterestInduDO.class)
                        .select(BondInterestInduDO::getId, BondInterestInduDO::getBondUniCode,
                                BondInterestInduDO::getComUniCode, BondInterestInduDO::getBondCode,
                                BondInterestInduDO::getInterestDate, BondInterestInduDO::getIndustryCode1,
                                BondInterestInduDO::getIndustryCode2, BondInterestInduDO::getIndustryName1,
                                BondInterestInduDO::getIndustryName2, BondInterestInduDO::getEnterpriseType)
                        .and(BondInterestInduGroupDO::getInterestDate, isEqual(interestDate))
                        .and(CollectionUtils.isNotEmpty(bondUniCodes), BondInterestInduGroupDO::getBondUniCode, in(bondUniCodes))
                        .groupBy(BondInterestInduGroupDO::getBondUniCode, BondInterestInduGroupDO::getInterestDate);
        return bondInterestInduMapper.selectByGroupedQuery(groupedQuery);
    }
}
