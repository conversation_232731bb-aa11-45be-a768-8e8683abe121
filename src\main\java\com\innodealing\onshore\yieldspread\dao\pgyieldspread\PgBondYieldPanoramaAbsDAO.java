package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBondYieldPanoramaAbsMapper;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldPanoramaBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBaseBondYieldPanoramaDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgPanoramaQuantileStatisticsViewDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 债券收益率全景-绝对值DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgBondYieldPanoramaAbsDAO {

    private static final String BOND_YIELD_PANORAMA_ABS_PK = "yieldSpread:bondYieldPanoramaAbsPk";
    @Resource
    private PgBondYieldPanoramaAbsMapper bondYieldPanoramaAbsMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;


    /**
     * 保存债券收益率全景数据
     *
     * @param issueDate            发行日期
     * @param yieldPanoramaAbsList 收益率全景绝对值列表
     * @return int 保存行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int saveBondYieldAbsList(@NonNull Date issueDate, List<PgBondYieldPanoramaAbsDO> yieldPanoramaAbsList) {
        if (CollectionUtils.isEmpty(yieldPanoramaAbsList)) {
            return 0;
        }
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        AtomicInteger effectRows = new AtomicInteger();
        Set<Integer> curveCodes = yieldPanoramaAbsList.stream().map(PgBaseBondYieldPanoramaDO::getCurveCode).collect(Collectors.toSet());
        DynamicQuery<PgBondYieldPanoramaAbsDO> query = DynamicQuery.createQuery(PgBondYieldPanoramaAbsDO.class)
                .and(PgBondYieldPanoramaAbsDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaAbsDO::getId, lessThanOrEqual(maxPk))
                .and(PgBondYieldPanoramaAbsDO::getCurveCode, in(curveCodes));
        Map<String, PgBondYieldPanoramaAbsDO> curveCodeIssueDateMap = bondYieldPanoramaAbsMapper.selectByDynamicQuery(query)
                .stream().collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        List<PgBondYieldPanoramaAbsDO> insertList = new ArrayList<>();
        List<PgBondYieldPanoramaAbsDO> updateList = new ArrayList<>();
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        for (PgBondYieldPanoramaAbsDO bondYieldPanoramaQuantileDO : yieldPanoramaAbsList) {
            String key = this.getKey(bondYieldPanoramaQuantileDO);
            if (curveCodeIssueDateMap.containsKey(key)) {
                PgBondYieldPanoramaAbsDO existBondYieldPanoramaQuantile = curveCodeIssueDateMap.get(key);
                bondYieldPanoramaQuantileDO.setId(existBondYieldPanoramaQuantile.getId());
                bondYieldPanoramaQuantileDO.setCreateTime(null);
                bondYieldPanoramaQuantileDO.setUpdateTime(now);
                updateList.add(bondYieldPanoramaQuantileDO);
            } else {
                bondYieldPanoramaQuantileDO.setId(redisService.generatePk(BOND_YIELD_PANORAMA_ABS_PK, bondYieldPanoramaQuantileDO.getIssueDate()));
                bondYieldPanoramaQuantileDO.setCreateTime(now);
                bondYieldPanoramaQuantileDO.setUpdateTime(now);
                insertList.add(bondYieldPanoramaQuantileDO);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param panorama 债券收益率全景分位数
     * @return {@link String} key值
     */
    private String getKey(PgBondYieldPanoramaAbsDO panorama) {
        return String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, panorama.getCurveCode(), panorama.getIssueDate().getTime());
    }

    /**
     * 批量更新
     *
     * @param updateList 债券收益率全景分位数
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgBondYieldPanoramaAbsDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldPanoramaAbsMapper> updateBatchAction =
                MapperBatchAction.create(PgBondYieldPanoramaAbsMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldPanoramaAbsDO abs : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgBondYieldPanoramaAbsDO> updateQuery =
                        DynamicQuery.createQuery(PgBondYieldPanoramaAbsDO.class).and(PgBondYieldPanoramaAbsDO::getId, isEqual(abs.getId()));
                mapper.updateSelectiveByDynamicQuery(abs, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 债券收益率全景分位数
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgBondYieldPanoramaAbsDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldPanoramaAbsMapper> insertBatchAction =
                MapperBatchAction.create(PgBondYieldPanoramaAbsMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldPanoramaAbsDO abs : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(abs));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 债券收益率全景基础数据
     *
     * @param bondTypeList 债券品种列表
     * @param issueDate    发行日期
     * @return {@link List}<{@link PgBondYieldPanoramaAbsDO}> 债券收益率全景基础数据
     */
    public List<PgBondYieldPanoramaAbsDO> listBondYieldPanoramaQuantiles(List<Integer> bondTypeList, @NonNull Date issueDate) {
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        DynamicQuery<PgBondYieldPanoramaAbsDO> query = DynamicQuery.createQuery(PgBondYieldPanoramaAbsDO.class)
                .and(PgBondYieldPanoramaAbsDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaAbsDO::getId, lessThanOrEqual(maxPk))
                .and(CollectionUtils.isNotEmpty(bondTypeList), PgBondYieldPanoramaAbsDO::getBondType, DynamicQueryBuilderHelper.in(bondTypeList))
                .and(PgBondYieldPanoramaAbsDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return bondYieldPanoramaAbsMapper.selectByDynamicQuery(query);
    }

    /**
     * 债券收益率全景基础数据
     *
     * @param curveCodeList 曲线code
     * @param issueDate    发行日期
     * @return {@link List}<{@link PgBondYieldPanoramaAbsDO}> 债券收益率全景基础数据
     */
    public List<PgBondYieldPanoramaAbsDO> listBondYieldPanoramaQuantilesByCurveCodes(List<Integer> curveCodeList, @NonNull Date issueDate) {
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        DynamicQuery<PgBondYieldPanoramaAbsDO> query = DynamicQuery.createQuery(PgBondYieldPanoramaAbsDO.class)
                .and(PgBondYieldPanoramaAbsDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaAbsDO::getId, lessThanOrEqual(maxPk))
                .and(CollectionUtils.isNotEmpty(curveCodeList), PgBondYieldPanoramaAbsDO::getCurveCode, DynamicQueryBuilderHelper.in(curveCodeList))
                .and(PgBondYieldPanoramaAbsDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return bondYieldPanoramaAbsMapper.selectByDynamicQuery(query);
    }


    /**
     * 收益率绝对值查询
     *
     * @param bondTypeList 债券品种列表
     * @param issueDate    利差日期
     * @return 收益率全景
     */
    public List<PgBondYieldPanoramaBO> listYieldPanoramas(List<Integer> bondTypeList, Date issueDate) {
        if (Objects.isNull(issueDate)) {
            return Lists.newArrayList();
        }
        Long minPk = ShardingUtils.getMinPkOfDate(issueDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(issueDate);
        DynamicQuery<PgBondYieldPanoramaAbsDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldPanoramaAbsDO.class)
                .and(PgBondYieldPanoramaAbsDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldPanoramaAbsDO::getId, lessThanOrEqual(maxPk))
                .and(CollectionUtils.isNotEmpty(bondTypeList), PgBondYieldPanoramaAbsDO::getBondType, in(bondTypeList))
                .and(PgBondYieldPanoramaAbsDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<PgBondYieldPanoramaAbsDO> pgBondYieldPanoramaAbsDOList = bondYieldPanoramaAbsMapper.selectByDynamicQuery(dynamicQuery);
        return CollectionUtils.isNotEmpty(pgBondYieldPanoramaAbsDOList) ? BeanCopyUtils.copyList(pgBondYieldPanoramaAbsDOList, PgBondYieldPanoramaBO.class) : Lists.newArrayList();
    }

    /**
     * 查询历史分位数据
     *
     * @param bondTypeList 债券类型列表
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param issueDate    发行日期
     * @return 历史分位统计数据
     */
    public List<PgPanoramaQuantileStatisticsViewDO> listPanoramaQuantileViews(@Nullable List<Integer> curveCodes, @Nullable List<Integer> bondTypeList, @NonNull Date startDate, @NonNull Date endDate, @NonNull Date issueDate) {
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        LocalDate localIssueDate = issueDate.toLocalDate();
        if (localStartDate.isAfter(localEndDate)) {
            throw new TipsException("开始时间不能大于结束时间");
        }
        if (localIssueDate.isBefore(localStartDate) || localIssueDate.isAfter(localEndDate)) {
            throw new TipsException("发行时间不在自定义时间范围内");
        }
        return bondYieldPanoramaAbsMapper.listPanoramaQuantileStatisticsViews(curveCodes, bondTypeList, startDate, endDate, issueDate);
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgBondYieldPanoramaAbsDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldPanoramaAbsDO.class)
                .orderBy(PgBondYieldPanoramaAbsDO::getIssueDate, SortDirections::desc);
        return bondYieldPanoramaAbsMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgBondYieldPanoramaAbsDO::getIssueDate);
    }
}
