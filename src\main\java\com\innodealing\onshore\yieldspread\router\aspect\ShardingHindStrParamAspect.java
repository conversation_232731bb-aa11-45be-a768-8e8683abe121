package com.innodealing.onshore.yieldspread.router.aspect;

import com.innodealing.onshore.yieldspread.helper.ShardingHindStrParamUtil;
import com.innodealing.onshore.yieldspread.router.annotation.ShardingHindStrParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.hint.HintManager;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 分片参数 切面
 *
 * <AUTHOR>
 * @date 2024/6/11 14:26
 **/
@Aspect
@Order(4)
@Component
public class ShardingHindStrParamAspect {

    /**
     * 注解切面
     */
    @Pointcut("@annotation(com.innodealing.onshore.yieldspread.router.annotation.ShardingHindStrParam)")
    public void shardingHindAnnotation() {
        // the pointcut expression
    }

    /**
     * 环绕通知
     *
     * @param point 切点
     * @return object
     * @throws Throwable 异常
     */
    @Around("shardingHindAnnotation()")
    public Object doSharding(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method repositoryMethod = signature.getMethod();
        ShardingHindStrParam annotation = repositoryMethod.getAnnotation(ShardingHindStrParam.class);
        String logicTableName = annotation.logicTableName();
        String shardingHindStrParam = ShardingHindStrParamUtil.getHindStrParam();
        if (StringUtils.isBlank(shardingHindStrParam)) {
            return point.proceed();
        }
        try (HintManager hintManager = HintManager.getInstance()) {
            hintManager.addTableShardingValue(logicTableName, shardingHindStrParam.trim());
            return point.proceed();
        }
    }
}
