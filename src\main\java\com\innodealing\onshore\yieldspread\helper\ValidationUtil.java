package com.innodealing.onshore.yieldspread.helper;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * validation校验器
 *
 * <AUTHOR>
 */
public final class ValidationUtil {
    private ValidationUtil() {
    }

    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 校验
     *
     * @param object param
     * @return message
     */
    public static void valid(Object object) {
        //如果被校验对象userInfo没有检验通过，则set里面就有校验信息，返回出去
        Set<ConstraintViolation<Object>> validate = VALIDATOR.validate(object);
        validate.stream().filter(v -> StringUtils.isNotBlank(v.getMessage()))
                .forEach(v -> {
                    throw new IllegalArgumentException(v.getMessage());
                });
    }
}

