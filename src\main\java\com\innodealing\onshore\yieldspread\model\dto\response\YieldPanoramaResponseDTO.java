package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 收益率全景
 *
 * <AUTHOR>
 */
public class YieldPanoramaResponseDTO {

    @ApiModelProperty("统计时间区间")
    private String statisticalDateRange;
    @ApiModelProperty("统计时间区间开始时间")
    private String statisticalDateStart;
    @ApiModelProperty("统计时间区间结束时间")
    private String statisticalDateEnd;
    @ApiModelProperty("最大收益率")
    private BigDecimal maxYield;
    @ApiModelProperty("最小收益率")
    private BigDecimal minYield;
    @ApiModelProperty("中位数收益率")
    private BigDecimal medianYield;
    @ApiModelProperty("全景数据")
    private List<YieldPanoramaTypeDataResponseDTO> yieldPanoramaDataList;

    public String getStatisticalDateRange() {
        return statisticalDateRange;
    }

    public void setStatisticalDateRange(String statisticalDateRange) {
        this.statisticalDateRange = statisticalDateRange;
    }

    public String getStatisticalDateStart() {
        return statisticalDateStart;
    }

    public void setStatisticalDateStart(String statisticalDateStart) {
        this.statisticalDateStart = statisticalDateStart;
    }

    public String getStatisticalDateEnd() {
        return statisticalDateEnd;
    }

    public void setStatisticalDateEnd(String statisticalDateEnd) {
        this.statisticalDateEnd = statisticalDateEnd;
    }

    public BigDecimal getMaxYield() {
        return maxYield;
    }

    public void setMaxYield(BigDecimal maxYield) {
        this.maxYield = maxYield;
    }

    public BigDecimal getMinYield() {
        return minYield;
    }

    public void setMinYield(BigDecimal minYield) {
        this.minYield = minYield;
    }

    public List<YieldPanoramaTypeDataResponseDTO> getYieldPanoramaDataList() {
        return yieldPanoramaDataList == null ? Collections.emptyList() : yieldPanoramaDataList;
    }

    public void setYieldPanoramaDataList(List<YieldPanoramaTypeDataResponseDTO> yieldPanoramaDataList) {
        this.yieldPanoramaDataList = yieldPanoramaDataList == null ? Collections.emptyList() : yieldPanoramaDataList;
    }

    public BigDecimal getMedianYield() {
        return medianYield;
    }

    public void setMedianYield(BigDecimal medianYield) {
        this.medianYield = medianYield;
    }
}
