image: reginter.innodealing.com/public/maven3-aliyun-jdk8:3

cache:
  paths:
    - .m2/

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2"

stages:
  - build

job_build:
  variables:
    # 比较的分支,默认为master,如果改动比较大,建议写成你的release分支
    TARGET_BRANCH: "master"
    HOST_URL: "http://*************:9007"
    LOGIN_TOKEN: "92e00c70c33d1387218f25a3c6027a66daaaff85"
    ACCESS_TOKEN: KXG1q6AAv_Hszu-Kr7wg
  stage: build
  script:
    - |
      output=$(curl -s --header "PRIVATE-TOKEN: $ACCESS_TOKEN" "http://git.innodealing.cn/api/v4/projects/$CI_PROJECT_ID/merge_requests?source_branch=$CI_COMMIT_REF_NAME")
      echo "$output" | java Ci "$TARGET_BRANCH" "$CI_PROJECT_ID" "$CI_COMMIT_SHA" "$CI_COMMIT_REF_NAME" "$HOST_URL" "$LOGIN_TOKEN" "$output"
  tags:
    - build