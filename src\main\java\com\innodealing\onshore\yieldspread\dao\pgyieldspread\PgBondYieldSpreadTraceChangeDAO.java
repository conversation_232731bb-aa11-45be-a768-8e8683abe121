package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.enums.BondYieldIntervalChangeTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBondYieldSpreadTraceChangeMapper;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceBO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceChangeDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 债券利差追踪-区间变动DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgBondYieldSpreadTraceChangeDAO {

    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d:%d:%d";
    private static final String BOND_YIELD_SPREAD_TRACE_CHANGE_PK = "yieldSpread:bondYieldSpreadTraceChangePk";
    @Resource
    private PgBondYieldSpreadTraceChangeMapper bondYieldSpreadTraceChangeMapper;
    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;
    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;

    /**
     * 收益率追踪区域变动查询
     *
     * @param bondType        债券类型
     * @param changeTenorEnum 变动类型
     * @param spreadDate      利率时间
     * @return 收益率追踪
     */
    public List<PgBondYieldSpreadTraceBO> listYieldSpreadTraces(
            YieldPanoramaBondTypeEnum bondType, BondYieldIntervalChangeTypeEnum changeTenorEnum, Date spreadDate) {
        if (!ObjectUtils.allNotNull(bondType, changeTenorEnum, spreadDate)) {
            return Lists.newArrayList();
        }
        Long minPk = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<PgBondYieldSpreadTraceChangeDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceChangeDO.class)
                .and(PgBondYieldSpreadTraceChangeDO::getId, greaterThanOrEqual(minPk))
                .and(PgBondYieldSpreadTraceChangeDO::getId, lessThanOrEqual(maxPk))
                .and(PgBondYieldSpreadTraceChangeDO::getBondType, isEqual(bondType.getValue()))
                .and(PgBondYieldSpreadTraceChangeDO::getChangeType, isEqual(changeTenorEnum.getValue()))
                .and(PgBondYieldSpreadTraceChangeDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        List<PgBondYieldSpreadTraceChangeDO> pgBondYieldPanoramaChangeDOList =
                bondYieldSpreadTraceChangeMapper.selectByDynamicQuery(dynamicQuery);
        return CollectionUtils.isNotEmpty(pgBondYieldPanoramaChangeDOList) ?
                BeanCopyUtils.copyList(pgBondYieldPanoramaChangeDOList, PgBondYieldSpreadTraceBO.class) : Lists.newArrayList();
    }

    /**
     * 保存利差追踪-区间变动列表
     *
     * @param issueDate                  发行日期
     * @param yieldSpreadTraceChangeList 利差追踪-区间变动列表
     * @return int 保存行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int saveBondYieldSpreadTraceChangeList(@NonNull Date issueDate, List<PgBondYieldSpreadTraceChangeDO> yieldSpreadTraceChangeList) {
        if (CollectionUtils.isEmpty(yieldSpreadTraceChangeList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        DynamicQuery<PgBondYieldSpreadTraceChangeDO> query = DynamicQuery.createQuery(PgBondYieldSpreadTraceChangeDO.class)
                .and(PgBondYieldSpreadTraceChangeDO::getIssueDate, isEqual(issueDate));
        Map<String, PgBondYieldSpreadTraceChangeDO> curveCodeIssueDateMap = bondYieldSpreadTraceChangeMapper.selectByDynamicQuery(query).stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        List<PgBondYieldSpreadTraceChangeDO> insertList = new ArrayList<>();
        List<PgBondYieldSpreadTraceChangeDO> updateList = new ArrayList<>();
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        for (PgBondYieldSpreadTraceChangeDO bondYieldSpreadTraceQuantileDO : yieldSpreadTraceChangeList) {
            String key = this.getKey(bondYieldSpreadTraceQuantileDO);
            if (curveCodeIssueDateMap.containsKey(key)) {
                PgBondYieldSpreadTraceChangeDO existBondYieldPanoramaQuantile = curveCodeIssueDateMap.get(key);
                bondYieldSpreadTraceQuantileDO.setId(existBondYieldPanoramaQuantile.getId());
                bondYieldSpreadTraceQuantileDO.setCreateTime(null);
                bondYieldSpreadTraceQuantileDO.setUpdateTime(now);
                updateList.add(bondYieldSpreadTraceQuantileDO);
            } else {
                bondYieldSpreadTraceQuantileDO.setId(redisService.generatePk(BOND_YIELD_SPREAD_TRACE_CHANGE_PK, bondYieldSpreadTraceQuantileDO.getIssueDate()));
                bondYieldSpreadTraceQuantileDO.setUpdateTime(now);
                bondYieldSpreadTraceQuantileDO.setCreateTime(now);
                insertList.add(bondYieldSpreadTraceQuantileDO);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param change 保存利差追踪-区间变动
     * @return {@link String} key值
     */
    private String getKey(PgBondYieldSpreadTraceChangeDO change) {
        return String.format(NAMESPACE_KEY_PLACEHOLDER, change.getBondType(), change.getChartType(), change.getCurveCode(), change.getChangeType(),
                change.getIssueDate().getTime());
    }

    /**
     * 批量更新
     *
     * @param updateList 利差追踪-区间变动列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgBondYieldSpreadTraceChangeDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldSpreadTraceChangeMapper> updateBatchAction =
                MapperBatchAction.create(PgBondYieldSpreadTraceChangeMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldSpreadTraceChangeDO change : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgBondYieldSpreadTraceChangeDO> updateQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceChangeDO.class)
                        .and(PgBondYieldSpreadTraceChangeDO::getId, isEqual(change.getId()));
                mapper.updateSelectiveByDynamicQuery(change, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 利差追踪-区间变动列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgBondYieldSpreadTraceChangeDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgBondYieldSpreadTraceChangeMapper> insertBatchAction =
                MapperBatchAction.create(PgBondYieldSpreadTraceChangeMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBondYieldSpreadTraceChangeDO change : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(change));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgBondYieldSpreadTraceChangeDO> dynamicQuery = DynamicQuery.createQuery(PgBondYieldSpreadTraceChangeDO.class)
                .orderBy(PgBondYieldSpreadTraceChangeDO::getIssueDate, SortDirections::desc);
        return bondYieldSpreadTraceChangeMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgBondYieldSpreadTraceChangeDO::getIssueDate);
    }
}
