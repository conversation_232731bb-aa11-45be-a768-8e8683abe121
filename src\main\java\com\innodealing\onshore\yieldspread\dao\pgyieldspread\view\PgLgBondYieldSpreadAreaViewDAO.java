package com.innodealing.onshore.yieldspread.dao.pgyieldspread.view;

import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.CommonUtils;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view.PgLgBondYieldSpreadAreaViewMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgBondYieldSpreadAreaViewDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 地方债 区域利差视图 DAO
 *
 * <AUTHOR>
 * @create: 2024-11-04
 */
@Component
public class PgLgBondYieldSpreadAreaViewDAO {

    @Resource
    private PgLgBondYieldSpreadAreaViewMapper pgLgBondYieldSpreadAreaViewMapper;

    /**
     * 创建地方债利差区域视图
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param comUniCode 地方债主体code,不传 默认 所有发行人地区(不包含全国)
     */
    public void createLgBondYieldSpreadAreaView(@NotNull Date startDate, @NotNull Date endDate, Long comUniCode) {
        String mvName = YieldSpreadConst.LG_MV_NAME +
                startDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE) +
                endDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        pgLgBondYieldSpreadAreaViewMapper.createLgBondYieldSpreadAreaView(mvName, startDate.toString(), endDate.toString(), comUniCode);
    }


    /**
     * 查询地方债利差区域数据
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param spreadDate 利差日期
     * @return {@link List}<{@link PgLgBondYieldSpreadAreaViewDO}> 地方债利差区域数据集
     */
    public List<PgLgBondYieldSpreadAreaViewDO> listPgLgBondYieldSpreadArea(@NotNull Date startDate, @NotNull Date endDate, Date spreadDate) {
        String mvName = YieldSpreadConst.LG_MV_NAME +
                startDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE) +
                endDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        return pgLgBondYieldSpreadAreaViewMapper.listLgBondYieldSpreadAreaView(mvName, spreadDate.toString()).stream().peek(viewDO -> {
            setCreditSpread(viewDO);
            setCreditSpreadTb(viewDO);
        }).collect(Collectors.toList());
    }

    private void setCreditSpread(PgLgBondYieldSpreadAreaViewDO viewDO) {
        CommonUtils.safeSetScale(viewDO.getCreditSpread1M(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread1M);
        CommonUtils.safeSetScale(viewDO.getCreditSpread3M(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread3M);
        CommonUtils.safeSetScale(viewDO.getCreditSpread6M(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread6M);
        CommonUtils.safeSetScale(viewDO.getCreditSpread9M(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread9M);
        CommonUtils.safeSetScale(viewDO.getCreditSpread1Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread1Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpread2Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread2Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpread3Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread3Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpread5Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread5Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpread7Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread7Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpread10Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread10Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpread15Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread15Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpread20Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread20Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpread30Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpread30Y);
    }

    private void setCreditSpreadTb(PgLgBondYieldSpreadAreaViewDO viewDO) {
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb1M(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb1M);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb3M(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb3M);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb6M(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb6M);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb9M(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb9M);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb1Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb1Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb2Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb2Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb3Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb3Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb5Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb5Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb7Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb7Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb10Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb10Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb15Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb15Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb20Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb20Y);
        CommonUtils.safeSetScale(viewDO.getCreditSpreadTb30Y(), YieldSpreadConst.TWO_DECIMAL_PLACE).ifPresent(viewDO::setCreditSpreadTb30Y);
    }

    /**
     * 删除物化视图
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    public void dropLgBondYieldSpreadAreaMv(@NotNull Date startDate, @NotNull Date endDate) {
        String mvName = YieldSpreadConst.LG_MV_NAME +
                startDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE) +
                endDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE);
        pgLgBondYieldSpreadAreaViewMapper.dropMv(mvName);
    }

}
