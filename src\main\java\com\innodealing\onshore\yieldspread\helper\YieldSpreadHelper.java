package com.innodealing.onshore.yieldspread.helper;

import com.github.wz2cool.dynamic.mybatis.QueryHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.dto.bondpriceapollo.CurveMaturityStructureDTO;
import com.innodealing.onshore.bondmetadata.enums.*;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.SpreadStatisticsBO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.IndustryResponseDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.ComYieldSpreadChangeDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.helper.CommonsHelper.getPropertyName;
import static com.innodealing.onshore.bondmetadata.constant.BigDecimals.TWO_SCALE;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.SPLIT_MONTH;

/**
 * 利差帮助类
 *
 * <AUTHOR>
 * @date 2022/08/17
 **/
@SuppressWarnings({"squid:S109", "squid:S3776", "squid:S00103"})
public final class YieldSpreadHelper {

    private static final Logger logger = LoggerFactory.getLogger(YieldSpreadHelper.class);

    private YieldSpreadHelper() {
    }

    public static final BigDecimal BP_WEIGHT = BigDecimal.valueOf(100);

    public static final int SPREAD_KEEP_SCALE = 2;

    public static final int YIELD_SPREAD_KEEP_SCALE = 4;

    private static final Integer HALF_YEAR = 183;

    private static final Integer ONE_YEAR = 365;

    private static final Integer FOUR_HALF_YEAR = 1643;

    private static final Integer SIX_YEAR = 2190;

    private static final String SPLIT_PLACEHOLDER = "-";

    public static final Integer MIN_BOND_SIZE = 1;

    /**
     * 期限字段正则
     */
    public static final String PERIOD_FIELD_REGEX = "([a-zA-Z]+)(\\d+)([YM])$";

    /**
     * 行政区划映射
     */
    private static final Map<Integer, Integer> ADMINISTRATIVE_DIVISION_MAP = initAdministrativeDivisionMap();

    /**
     * 国开曲线
     */
    public static final Integer CDB_YIELD_CURVE = 4;

    /**
     * 行业曲线映射
     */
    private static final Map<Integer, Integer> BOND_YIELD_CURVE_INDU_MAP = initBondYieldCurveInduMap();

    /**
     * 城投曲线映射
     */
    private static final Map<Integer, Integer> BOND_YIELD_CURVE_UDIC_MAP = initBondYieldCurveUdicMap();

    /**
     * 证券曲线映射
     */
    private static final Map<Integer, Integer> BOND_YIELD_CURVE_SECU_MAP = initBondYieldCurveSecuMap();

    /**
     * 银行曲线
     */
    private static final Map<String, Integer> BOND_YIELD_CURVE_BANK_MAP;

    /**
     * 保险曲线映射
     */
    private static final Map<Integer, Long> BOND_YIELD_CURVE_INSU_MAP = initBondYieldCurveInsuMap();


    /**
     * 产业利差债券类型
     */
    private static final List<Integer> INDU_SPREAD_BOND_TYPE_LIST = initInduSpreadBondTypeList();

    /**
     * 城投利差债券类型
     */
    private static final List<Integer> UDIC_SPREAD_BOND_TYPE_LIST = initUdicSpreadBondTypeList();

    /**
     * 银行利差债券类型
     */
    private static final Set<Integer> BANK_SPREAD_BOND_TYPE_LIST;

    /**
     * 证券利差债券类型
     */
    private static final Set<Integer> SECU_SPREAD_BOND_TYPE_LIST = initSecuSpreadBondTypeList();

    /**
     * 地方债利差债券类型
     */
    private static final Set<Integer> LG_BOND_SPREAD_TYPE_LIST = initLgBondSpreadTypeList();

    /**
     * 保险利差债券类型
     */
    private static final Set<Integer> INSU_SPREAD_BOND_TYPE_LIST = initInsuSpreadBondTypeList();

    /**
     * 利差行业编码
     */
    private static final Map<Long, String> INDU_UNICODE_MAP = initInduUnicodeMap();

    /**
     * 证券利差行业编码
     */
    public static final Long SECU_INDU_CODE = 490_100L;

    /**
     * 银行利差行业编码
     */
    public static final Long BANK_INDU_CODE = 480_000L;

    /**
     * 保险利差行业编码
     */
    public static final Long INSU_INDU_CODE = 490_200L;

    /**
     * 利差二级行业编码
     */
    private static final List<Long> INDU2_UNICODE_LIST = initIndu2UnicodeList();

    /**
     * 行业2，行业1映射集合
     */
    private static final Map<Long, Long> INDU2_INDU1_MAPPING = initIndu2Indu1Mapping();

    /**
     * 曲线收益率信息
     */
    private static final List<String> CURVE_YTM_INFO_LIST = initCurveYtmInfoList();

    /**
     * 重构表 curve_maturity_structure key_tenor关键期限字段 与曲线收益率映射
     */
    private static final Map<String, String> UNI_CURVE_YTM_INFO_MAP = initUniCurveYtmInfoList();

    /**
     * 行业响应数据集
     */
    private static final List<IndustryResponseDTO> INDUSTRY_RESPONSE_DTO_LIST = initIndustryResponseList();

    /**
     * 债券隐含评级标签映射
     * AAA+,AAA,AAA- 映射为 AAA级
     * AA+,AA,AA- ,AA(2)   映射为  AA级
     * A+,A,A-       映射为   A级
     */
    private static final Map<Integer, Integer> BOND_IMPLIED_RATING_MAPPING_TAG_MAPPING = initBondImpliedRatingTagMapping();

    /**
     * yy评级标签映射
     * 1~5级    映射为 投资级
     * 6,7,8    映射为  投机级
     */
    private static final Map<Integer, Integer> COM_YY_RATING_MAPPING_TAG_MAPPING = initComYyRatingTagMapping();

    /**
     * 城投yy评级标签映射
     * 1~6级    映射为 投资级
     * 7,8    映射为  投机级
     */
    private static final Map<Integer, Integer> UDIC_COM_YY_RATING_MAPPING_TAG_MAPPING = initUdicComYyRatingTagMapping();

    private static final Set<String> CHANGE_ORDER_PROPERTY_SET = initChangeOrderPropertySet();

    private static final QueryHelper QUERY_HELPER = new QueryHelper();

    private static final Map<Integer, String> BANK_SENIORITY_RANKING_TEXT_MAP = initBankSeniorityRankingTextMap();

    /**
     * 地方债 发行人 和 区域 映射
     */
    private static final Map<Long, String> LG_COM_AREA_NAME_MAP = initLgComUniCodeToAreaNameMap();

    private static Map<Long, String> initLgComUniCodeToAreaNameMap() {
        Map<Long, String> map = Maps.newHashMap();
        map.put(10_004_523L, "重庆");
        map.put(10_011_235L, "深圳");
        map.put(10_011_088L, "广东");
        map.put(10_011_099L, "浙江");
        map.put(10_011_144L, "宁波");
        map.put(10_011_227L, "新疆");
        map.put(10_012_367L, "兵团");
        map.put(10_011_233L, "江西");
        map.put(10_011_132L, "四川");
        map.put(10_010_894L, "内蒙古");
        map.put(10_010_888L, "北京");
        map.put(10_011_082L, "湖北");
        map.put(10_010_896L, "江苏");
        map.put(10_011_232L, "广西");
        map.put(10_011_029L, "河南");
        map.put(10_011_234L, "厦门");
        map.put(10_011_178L, "福建");
        map.put(10_011_228L, "安徽");
        map.put(10_010_902L, "吉林");
        map.put(10_010_953L, "黑龙江");
        map.put(10_011_230L, "云南");
        map.put(10_011_158L, "天津");
        map.put(10_010_999L, "山西");
        map.put(10_011_049L, "山东");
        map.put(10_011_151L, "青岛");
        map.put(10_011_091L, "湖南");
        map.put(10_011_050L, "陕西");
        map.put(10_011_105L, "大连");
        map.put(10_011_087L, "辽宁");
        map.put(10_011_093L, "海南");
        map.put(10_010_893L, "河北");
        map.put(10_011_175L, "青海");
        map.put(10_011_133L, "贵州");
        map.put(10_011_164L, "宁夏");
        map.put(10_011_231L, "上海");
        map.put(10_011_018L, "甘肃");
        map.put(10_012_355L, "西藏");
        return map;
    }


    private static Map<Integer, String> initBankSeniorityRankingTextMap() {
        Map<Integer, String> map = new HashMap<>(3);
        map.put(BankSeniorityRankingEnum.NORMAL.getValue(), "普通");
        map.put(BankSeniorityRankingEnum.TIER_2_BOND.getValue(), "二级资本债");
        map.put(BankSeniorityRankingEnum.PERPETUA.getValue(), "永续");
        return map;
    }

    static {
        BANK_SPREAD_BOND_TYPE_LIST = Sets.newHashSet(
                BondType.CENTRAL_BANK_BILL.getValue(),
                BondType.ENTERPRISE_BOND.getValue(),
                BondType.CORPORATE_BOND.getValue(),
                BondType.MEDIUM_TERM_NOTES.getValue(),
                BondType.POLICY_BANK_ORDINARY_BOND.getValue(),
                BondType.POLICY_BANK_SUBORDINATED_BOND.getValue(),
                BondType.COMMERCIAL_BANK_ORDINARY_BONDS.getValue(),
                BondType.COMMERCIAL_BANK_SUBORDINATED_BOND.getValue(),
                BondType.TIER_TWO_CAPITAL_INSTRUMENT.getValue(),
                BondType.TREASURY_CASH_MANAGEMENT.getValue(),
                BondType.INTERNATIONAL_AGENCY_BOND.getValue()
        );
        BOND_YIELD_CURVE_BANK_MAP = new ConcurrentHashMap<>();
        // 评级求偿银行曲线
        // ====================================求偿 普通（剔除永续） ================================
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.NORMAL.getValue(), ImplicitRatingTagEnum.AAA.getValue()), CurveCode.CHINA_BOND_ORD.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.NORMAL.getValue(), ImplicitRatingTagEnum.AAA_SUB.getValue()), CurveCode.GENERAL_BANK_BOND_AAA_SUB.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.NORMAL.getValue(), ImplicitRatingTagEnum.AA_PLUS.getValue()), CurveCode.GENERAL_BANK_BOND_AA_PLUS.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.NORMAL.getValue(), ImplicitRatingTagEnum.AA.getValue()), CurveCode.GENERAL_BANK_BOND_AA.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.NORMAL.getValue(), ImplicitRatingTagEnum.AA_SUB.getValue()), CurveCode.GENERAL_BANK_BOND_AA_SUB.getValue());
        // ====================================求偿 二级资本债   ================================
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.TIER_2_BOND.getValue(), ImplicitRatingTagEnum.AAA_SUB.getValue()), CurveCode.BANK_SECONDARY_CAPITAL_BOND_AAA_SUB.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.TIER_2_BOND.getValue(), ImplicitRatingTagEnum.AA_PLUS.getValue()), CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA_PLUS.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.TIER_2_BOND.getValue(), ImplicitRatingTagEnum.AA.getValue()), CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.TIER_2_BOND.getValue(), ImplicitRatingTagEnum.AA_SUB.getValue()), CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA_SUB.getValue());
        // ====================================求偿 永续   ================================
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.PERPETUA.getValue(), ImplicitRatingTagEnum.AAA_SUB.getValue()), CurveCode.BANK_PERPETUAL_BOND_AAA_SUB.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.PERPETUA.getValue(), ImplicitRatingTagEnum.AA_PLUS.getValue()), CurveCode.BANK_PERPETUAL_BOND_AA_PLUS.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.PERPETUA.getValue(), ImplicitRatingTagEnum.AA.getValue()), CurveCode.BANK_PERPETUAL_BOND_AA.getValue());
        BOND_YIELD_CURVE_BANK_MAP.put(bankYieldCurveKey(BankSeniorityRankingEnum.PERPETUA.getValue(), ImplicitRatingTagEnum.AA_SUB.getValue()), CurveCode.BANK_PERPETUAL_BOND_AA_SUB.getValue());
    }

    /**
     * 批次大小
     */
    public static final int BATCH_SIZE = 500;
    /**
     * 小批次大小
     */
    public static final int MIN_BATCH_SIZE = 100;

    public static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d";

    public static final String BANK_BOND_RATING_KEY = "%s_%s";

    public static Map<Integer, String> getBankSeniorityRankingTextMap() {
        return BANK_SENIORITY_RANKING_TEXT_MAP;
    }

    public static Map<Long, String> getLgComAreaNameMap() {
        return LG_COM_AREA_NAME_MAP;
    }

    public static Map<Integer, Integer> getAdministrativeDivisionMap() {
        return ADMINISTRATIVE_DIVISION_MAP;
    }

    public static Map<Integer, Integer> getBondYieldCurveInduMap() {
        return BOND_YIELD_CURVE_INDU_MAP;
    }

    public static Map<Integer, Integer> getBondYieldCurveUdicMap() {
        return BOND_YIELD_CURVE_UDIC_MAP;
    }

    public static Map<Integer, Integer> getBondYieldCurveSecuMap() {
        return BOND_YIELD_CURVE_SECU_MAP;
    }

    public static Map<Integer, Long> getBondYieldCurveInsuMap() {
        return BOND_YIELD_CURVE_INSU_MAP;
    }

    public static Map<String, Integer> getBondYieldCurveBankMap() {
        return BOND_YIELD_CURVE_BANK_MAP;
    }

    public static List<Integer> getInduSpreadBondTypeList() {
        return Objects.isNull(INDU_SPREAD_BOND_TYPE_LIST) ? Collections.emptyList() : INDU_SPREAD_BOND_TYPE_LIST;
    }

    public static List<Integer> getBankSpreadBondTypeList() {
        return Objects.isNull(BANK_SPREAD_BOND_TYPE_LIST) ? Collections.emptyList() : new ArrayList<>(BANK_SPREAD_BOND_TYPE_LIST);
    }

    public static List<Integer> getUdicSpreadBondTypeList() {
        return Objects.isNull(UDIC_SPREAD_BOND_TYPE_LIST) ? Collections.emptyList() : UDIC_SPREAD_BOND_TYPE_LIST;
    }

    public static List<Integer> getSecuSpreadBondTypeList() {
        return Objects.isNull(SECU_SPREAD_BOND_TYPE_LIST) ? Collections.emptyList() : new ArrayList<>(SECU_SPREAD_BOND_TYPE_LIST);
    }

    public static List<Integer> getLgBondSpreadTypeList() {
        return Objects.isNull(LG_BOND_SPREAD_TYPE_LIST) ? Collections.emptyList() : new ArrayList<>(LG_BOND_SPREAD_TYPE_LIST);
    }

    public static List<Integer> getInsuSpreadBondTypeList() {
        return Objects.isNull(INSU_SPREAD_BOND_TYPE_LIST) ? Collections.emptyList() : new ArrayList<>(INSU_SPREAD_BOND_TYPE_LIST);
    }

    public static Map<Long, String> getInduUnicodeMap() {
        return Objects.isNull(INDU_UNICODE_MAP) ? Collections.emptyMap() : INDU_UNICODE_MAP;
    }

    public static List<Long> getIndu2UnicodeList() {
        return Objects.isNull(INDU2_UNICODE_LIST) ? Collections.emptyList() : INDU2_UNICODE_LIST;
    }

    public static List<IndustryResponseDTO> getIndustryResponseList() {
        return Objects.isNull(INDUSTRY_RESPONSE_DTO_LIST) ? Collections.emptyList() : INDUSTRY_RESPONSE_DTO_LIST;
    }

    public static Map<Integer, Integer> getBondImpliedRatingMappingTagMap() {
        return Objects.isNull(BOND_IMPLIED_RATING_MAPPING_TAG_MAPPING) ? Collections.emptyMap() : BOND_IMPLIED_RATING_MAPPING_TAG_MAPPING;
    }

    public static Map<Integer, Integer> getComYyRatingMappingTagMap() {
        return Objects.isNull(COM_YY_RATING_MAPPING_TAG_MAPPING) ? Collections.emptyMap() : COM_YY_RATING_MAPPING_TAG_MAPPING;
    }

    public static Map<Integer, Integer> getUdicComYyRatingMappingTagMap() {
        return Objects.isNull(UDIC_COM_YY_RATING_MAPPING_TAG_MAPPING) ? Collections.emptyMap() : UDIC_COM_YY_RATING_MAPPING_TAG_MAPPING;
    }

    public static Map<Long, Long> getIndu2Indu1Mapping() {
        return Objects.isNull(INDU2_INDU1_MAPPING) ? Collections.emptyMap() : INDU2_INDU1_MAPPING;
    }

    public static Map<String, String> getUniCurveYtmInfoMap() {
        return Objects.isNull(UNI_CURVE_YTM_INFO_MAP) ? Collections.emptyMap() : UNI_CURVE_YTM_INFO_MAP;
    }

    /**
     * 获取插值收益率
     *
     * @param tenorDay   剩余期限天数
     * @param yieldCurve 收益率曲线
     * @return 插值收益率
     */
    public static BigDecimal getLerpYield(Integer tenorDay, BondYieldCurveBO yieldCurve) {

        BigDecimal yield = getWholeYearAndWholeMonthYield(tenorDay, yieldCurve);
        if (Objects.nonNull(yield)) {
            return yield;
        }
        return getUpAndLowYield(tenorDay, yieldCurve);
    }

    /**
     * 获取插值收益率
     *
     * @param tenorDay                   剩余期限天数
     * @param curveMaturityStructureDTOS 收益率曲线列表(竖表)
     * @return 插值收益率
     */
    public static BigDecimal curveMaturityStructureDTOs(Integer tenorDay, List<CurveMaturityStructureDTO> curveMaturityStructureDTOS) {
        BigDecimal remainingYear = CalculationHelper.convertRemainingTenorDayToYear(tenorDay);
        Optional<BigDecimal> tenorYear = getWholeYearAndWholeMonthYield(remainingYear, curveMaturityStructureDTOS);
        return tenorYear.orElseGet(() -> getUpAndLowYield(remainingYear, curveMaturityStructureDTOS));
    }

    /**
     * 整年整月收益率
     *
     * @param remainingTenor 剩余期限(天数)
     * @param yieldCurve     收益率曲线
     * @return 整年整月收益率
     */
    private static BigDecimal getWholeYearAndWholeMonthYield(Integer remainingTenor, BondYieldCurveBO yieldCurve) {
        if (Objects.isNull(remainingTenor) || Objects.isNull(yieldCurve)) {
            return null;
        }
        BigDecimal yield = null;
        for (String info : CURVE_YTM_INFO_LIST) {
            String[] infoArray = info.split(SPLIT_PLACEHOLDER);
            Integer day = Integer.valueOf(infoArray[0]);
            String filed = infoArray[2];
            if (day.equals(remainingTenor)) {
                yield = getYieldCurveInfo(yieldCurve, filed);
                break;
            }
        }
        return yield;
    }

    /**
     * 整年整月收益率(竖表)
     *
     * @param remainingYear              剩余期限(年)
     * @param curveMaturityStructureDTOS 收益率曲线(竖表)
     * @return 整年整月收益率
     */
    private static Optional<BigDecimal> getWholeYearAndWholeMonthYield(BigDecimal remainingYear, List<CurveMaturityStructureDTO> curveMaturityStructureDTOS) {
        if (Objects.isNull(remainingYear) || CollectionUtils.isEmpty(curveMaturityStructureDTOS)) {
            return Optional.empty();
        }
        BigDecimal yield = null;
        for (CurveMaturityStructureDTO curveMaturityStructureDTO : curveMaturityStructureDTOS) {
            BigDecimal curveRemainingYear = curveMaturityStructureDTO.getRemainingTenor();
            if (remainingYear.compareTo(curveRemainingYear) == 0) {
                yield = curveMaturityStructureDTO.getCurveYield();
                break;
            }
        }
        return Optional.ofNullable(yield);
    }

    /**
     * 上下限收益率
     *
     * @param remainingTenor 剩余期限(天数)
     * @param yieldCurve     收益率曲线
     * @return 上下限收益率
     */
    private static BigDecimal getUpAndLowYield(Integer remainingTenor, BondYieldCurveBO yieldCurve) {
        BigDecimal yieldUp = null;
        BigDecimal yieldLow = null;
        BigDecimal remainingTenorYear = null;
        BigDecimal remainingTenorUp = null;
        BigDecimal remainingTenorLow = null;
        int index = 0;
        for (String info : CURVE_YTM_INFO_LIST) {
            String[] infoArray = info.split(SPLIT_PLACEHOLDER);
            Integer day = Integer.valueOf(infoArray[0]);
            if (remainingTenor <= day) {
                for (int i = index - 1; i >= 0; i--) {
                    String[] currInfoLowArray = CURVE_YTM_INFO_LIST.get(i).split(SPLIT_PLACEHOLDER);
                    yieldLow = getYieldCurveInfo(yieldCurve, currInfoLowArray[2]);
                    remainingTenorLow = new BigDecimal(currInfoLowArray[1]);
                    if (Objects.nonNull(yieldLow) && yieldLow.compareTo(BigDecimal.ZERO) > 0) {
                        break;
                    }
                }
                for (int i = index; i < CURVE_YTM_INFO_LIST.size(); i++) {
                    String[] currInfoUpArray = CURVE_YTM_INFO_LIST.get(i).split(SPLIT_PLACEHOLDER);
                    yieldUp = getYieldCurveInfo(yieldCurve, currInfoUpArray[2]);
                    remainingTenorUp = new BigDecimal(currInfoUpArray[1]);
                    if (Objects.nonNull(yieldUp) && yieldUp.compareTo(BigDecimal.ZERO) > 0) {
                        break;
                    }
                }
                break;
            }
            index++;
        }
        Optional<BigDecimal> optionalRemainingTenor = BigDecimalUtils.safeDivide(new BigDecimal(remainingTenor),
                new BigDecimal(ONE_YEAR), RoundingMode.HALF_UP);
        if (optionalRemainingTenor.isPresent()) {
            remainingTenorYear = optionalRemainingTenor.get();
        }
        return calculateLerpYield(yieldUp, yieldLow, remainingTenorYear, remainingTenorUp, remainingTenorLow);
    }

    /**
     * 上下限收益率
     *
     * @param remainingYear              剩余期限(年)
     * @param curveMaturityStructureDTOS 收益率曲线(竖表)
     * @return 上下限收益率
     */
    private static BigDecimal getUpAndLowYield(BigDecimal remainingYear, List<CurveMaturityStructureDTO> curveMaturityStructureDTOS) {
        BigDecimal curveYieldUp = null;
        BigDecimal curveYieldLow = null;
        BigDecimal remainingTenorUp = null;
        BigDecimal remainingTenorLow = null;

        List<CurveMaturityStructureDTO> sortCurveDTOList = curveMaturityStructureDTOS.stream()
                .sorted(Comparator.comparing(CurveMaturityStructureDTO::getRemainingTenor))
                .collect(Collectors.toList());

        int index = 0;
        for (CurveMaturityStructureDTO curveMaturityStructureDTO : sortCurveDTOList) {
            BigDecimal currentRemainingTenor = curveMaturityStructureDTO.getRemainingTenor();
            if (remainingYear.compareTo(currentRemainingTenor) <= 0) {
                for (int i = index - 1; i >= 0; i--) {
                    CurveMaturityStructureDTO currInfoLowDTO = sortCurveDTOList.get(i);
                    curveYieldLow = currInfoLowDTO.getCurveYield();
                    remainingTenorLow = currInfoLowDTO.getRemainingTenor();
                    if (Objects.nonNull(curveYieldLow) && curveYieldLow.compareTo(BigDecimal.ZERO) > 0) {
                        break;
                    }
                }
                for (int i = index; i < sortCurveDTOList.size(); i++) {
                    CurveMaturityStructureDTO currInfoUpDTO = sortCurveDTOList.get(i);
                    curveYieldUp = currInfoUpDTO.getCurveYield();
                    remainingTenorUp = currInfoUpDTO.getRemainingTenor();
                    if (Objects.nonNull(curveYieldUp) && curveYieldUp.compareTo(BigDecimal.ZERO) > 0) {
                        break;
                    }
                }
                break;
            }
            index++;
        }
        return calculateLerpYield(curveYieldUp, curveYieldLow, remainingYear, remainingTenorUp, remainingTenorLow);
    }

    /**
     * 计算插值收益率 y1+(y2-y1)(3.5-3)/(5-3) 计算规则
     *
     * @param yieldUp            收益率上限
     * @param yieldLow           收益率下限
     * @param remainingTenorYear 剩余期限(年)
     * @param remainingTenorUp   剩余期限上限(年)
     * @param remainingTenorLow  剩余期限下限(年)
     * @return 插值收益率
     */
    private static BigDecimal calculateLerpYield(BigDecimal yieldUp, BigDecimal yieldLow,
                                                 BigDecimal remainingTenorYear, BigDecimal remainingTenorUp,
                                                 BigDecimal remainingTenorLow) {
        if (Objects.isNull(yieldUp) || Objects.isNull(yieldLow)) {
            return null;
        }
        BigDecimal remainingTenorSubRemainingTenorLow = null;
        BigDecimal remainingTenorUpSubRemainingTenorLow = null;
        BigDecimal yieldDiff = null;
        BigDecimal yieldDiffMclRemainingTenorDiff = null;
        BigDecimal yieldUpSubYieldLow = yieldUp.subtract(yieldLow);

        Optional<BigDecimal> optionalRemainingTenorLowDiff = com.innodealing.commons.object.BigDecimalUtils.safeSubtract(remainingTenorYear, remainingTenorLow);
        if (optionalRemainingTenorLowDiff.isPresent()) {
            remainingTenorSubRemainingTenorLow = optionalRemainingTenorLowDiff.get();
        }
        Optional<BigDecimal> optionalRemainingTenorUpAndLowDiff = com.innodealing.commons.object.BigDecimalUtils.safeSubtract(remainingTenorUp, remainingTenorLow);
        if (optionalRemainingTenorUpAndLowDiff.isPresent()) {
            remainingTenorUpSubRemainingTenorLow = optionalRemainingTenorUpAndLowDiff.get();
        }
        Optional<BigDecimal> optionalYieldDiffMclRemainingTenorDiff = BigDecimalUtils.safeMultiply(yieldUpSubYieldLow,
                remainingTenorSubRemainingTenorLow);
        if (optionalYieldDiffMclRemainingTenorDiff.isPresent()) {
            yieldDiffMclRemainingTenorDiff = optionalYieldDiffMclRemainingTenorDiff.get();
        }
        Optional<BigDecimal> optionalYieldDiff = BigDecimalUtils.safeDivide(yieldDiffMclRemainingTenorDiff,
                remainingTenorUpSubRemainingTenorLow, RoundingMode.HALF_UP);
        if (optionalYieldDiff.isPresent()) {
            yieldDiff = optionalYieldDiff.get();
        }
        if (Objects.isNull(yieldDiff)) {
            return null;
        }
        return yieldLow.add(yieldDiff).setScale(YIELD_SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP);
    }

    private static BigDecimal getYieldCurveInfo(Object fromObject, String fieldName) {
        Class<?> fromClass = fromObject.getClass();
        BigDecimal ytm = null;
        try {
            Method method = fromClass.getMethod("get" + fieldName);
            Object value = method.invoke(fromObject);
            if (Objects.nonNull(value)) {
                ytm = new BigDecimal(value.toString());
            }
            return ytm;
        } catch (Exception ex) {
            logger.error("getYieldCurveInfo error", ex);
        }
        return null;
    }

    /**
     * 获取利差债券剩余期限标签 计算范围半年到六年 计算天数范围183天到2190天
     *
     * @param remainingTenorDay 剩余期限日期
     * @return 剩余期限标签
     */
    public static Integer getSpreadRemainingTenorTag(Integer remainingTenorDay) {
        if (Objects.isNull(remainingTenorDay)) {
            return 0;
        }
        if (remainingTenorDay >= FOUR_HALF_YEAR) {
            return SpreadRemainingTenorTagEnum.SPREAD_REMAINING_TENOR_TAG_FIVE.getValue();
        }
        return new BigDecimal(remainingTenorDay).divide(new BigDecimal(ONE_YEAR), 0, BigDecimal.ROUND_HALF_UP).intValue();
    }

    /**
     * 校验利差剩余期限是否有效
     *
     * @param remainingTenorDay 剩余期限日期
     * @return 是否有效
     */
    public static boolean spreadRemainingTenorIsValid(Integer remainingTenorDay) {
        if (Objects.isNull(remainingTenorDay)) {
            return false;
        }
        return remainingTenorDay >= YieldSpreadHelper.HALF_YEAR && remainingTenorDay < YieldSpreadHelper.SIX_YEAR;
    }

    /**
     * 获取当前日期前一天
     *
     * @return 当前日期前一天
     */
    public static Date getYesterDay() {
        return DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
    }

    /**
     * 获取对应关系的银行曲线
     *
     * @param bankSeniorityRanking     银行求偿
     * @param bondImpliedRatingMapping 债券隐含评级
     * @return 曲线code
     */
    public static Integer getBankYieldCurve(Integer bankSeniorityRanking, Integer bondImpliedRatingMapping) {
        return BOND_YIELD_CURVE_BANK_MAP.get(bankYieldCurveKey(bankSeniorityRanking, bondImpliedRatingMapping));
    }

    /**
     * 获取百分位数
     *
     * @param numbers    待选集合
     * @param percentile 百分位[0,1]
     * @param needSort   是否需要排序
     * @return 百分位对应的数，如果待选集合为空返回null
     */
    @Nullable
    public static BigDecimal getPercentile(List<BigDecimal> numbers, Double percentile, Boolean needSort) {
        if (Objects.isNull(percentile) || percentile < 0 || percentile > 1) {
            throw new IllegalArgumentException("percentile param illegal, should be between 0 and 1.");
        }
        if (CollectionUtils.isEmpty(numbers)) {
            return null;
        }
        int size = numbers.size();
        if (size == 1) {
            return numbers.get(0);
        }
        List<BigDecimal> sortedNumbers = Objects.isNull(needSort) || needSort ? numbers.stream().sorted().collect(Collectors.toList()) : numbers;
        if (percentile == 0) {
            return sortedNumbers.get(0);
        }
        if (percentile == 1) {
            return sortedNumbers.get(size - 1);
        }
        BigDecimal location = new BigDecimal(size - 1).multiply(new BigDecimal(String.valueOf(percentile)));
        int index = location.intValue();
        BigDecimal sub = location.subtract(new BigDecimal(index));
        return (new BigDecimal(1).subtract(sub)).multiply(sortedNumbers.get(index)).add(sub.multiply(sortedNumbers.get(index + 1)));
    }

    private static String bankYieldCurveKey(Integer bankSeniorityRanking, Integer bondImpliedRatingMapping) {
        return String.format(BANK_BOND_RATING_KEY, bankSeniorityRanking, bondImpliedRatingMapping);
    }

    private static Map<Integer, Integer> initAdministrativeDivisionMap() {
        Map<Integer, Integer> divisionMap = new ConcurrentHashMap<>(16);
        //省级
        divisionMap.put(AdministrativeRegionEnum.MUNICIPAL.getValue(), AreaTypeEnum.PROVINCE.getValue());
        divisionMap.put(AdministrativeRegionEnum.PROVINCIAL.getValue(), AreaTypeEnum.PROVINCE.getValue());
        // 市级
        divisionMap.put(AdministrativeRegionEnum.STRONG_PREFECTURE_LEVEL_CITY.getValue(), AreaTypeEnum.CITY.getValue());
        divisionMap.put(AdministrativeRegionEnum.PREFECTURE_LEVEL_CITY.getValue(), AreaTypeEnum.CITY.getValue());
        divisionMap.put(AdministrativeRegionEnum.SUB_PROVINCIAL_CITY.getValue(), AreaTypeEnum.CITY.getValue());
        divisionMap.put(AdministrativeRegionEnum.STATE_PLAN_CITY.getValue(), AreaTypeEnum.CITY.getValue());
        divisionMap.put(AdministrativeRegionEnum.DISTRICT_UNDER_MUNICIPALITY.getValue(), AreaTypeEnum.CITY.getValue());
        // 区县
        divisionMap.put(AdministrativeRegionEnum.GENERAL_DISTRICT_COUNTY.getValue(), AreaTypeEnum.DISTRICT.getValue());
        divisionMap.put(AdministrativeRegionEnum.TOP100_COUNTY.getValue(), AreaTypeEnum.DISTRICT.getValue());
        divisionMap.put(AdministrativeRegionEnum.TOP100_DISTRICT.getValue(), AreaTypeEnum.DISTRICT.getValue());
        // 园区
        divisionMap.put(AdministrativeRegionEnum.SUB_PROVINCIAL_STATE_LEVEL_NEW_DISTRICT.getValue(),
                AreaTypeEnum.ZONE.getValue());
        divisionMap.put(AdministrativeRegionEnum.MAIN_HALL_LEVEL_STATE_LEVEL_NEW_DISTRICT.getValue(),
                AreaTypeEnum.ZONE.getValue());
        divisionMap.put(AdministrativeRegionEnum.SUB_HALL_LEVEL_STATE_LEVEL_NEW_DISTRICT.getValue(),
                AreaTypeEnum.ZONE.getValue());
        divisionMap.put(AdministrativeRegionEnum.STATE_LEVEL_DEVELOPMENT_ZONE.getValue(), AreaTypeEnum.ZONE.getValue());
        divisionMap.put(AdministrativeRegionEnum.PROVINCIAL_DEVELOPMENT_ZONE.getValue(), AreaTypeEnum.ZONE.getValue());
        return divisionMap;
    }

    private static Map<Integer, Integer> initBondYieldCurveInduMap() {
        // 行业曲线映射
        Map<Integer, Integer> induCurveMap = new ConcurrentHashMap<>(9);
        induCurveMap.put(10, CurveCode.CHINA_BOND_MID_AAA_PLUS.getValue());
        induCurveMap.put(20, CurveCode.CHINA_BOND_MID.getValue());
        induCurveMap.put(30, CurveCode.CHINA_BOND_MID_AAA_SUB.getValue());
        induCurveMap.put(40, CurveCode.CHINA_BOND_MID_AA_PLUS.getValue());
        induCurveMap.put(50, CurveCode.CHINA_BOND_MID_AA.getValue());
        induCurveMap.put(60, CurveCode.CHINA_BOND_MID_AA_SUB.getValue());
        induCurveMap.put(70, CurveCode.CHINA_BOND_MID_A_PLUS.getValue());
        induCurveMap.put(80, CurveCode.CHINA_BOND_MID_A.getValue());
        induCurveMap.put(90, CurveCode.CHINA_BOND_MID_A_SUB.getValue());
        return induCurveMap;
    }

    private static Map<Integer, Integer> initBondYieldCurveUdicMap() {
        // 城投曲线映射
        Map<Integer, Integer> udicCurveMap = new ConcurrentHashMap<>(7);
        udicCurveMap.put(10, CurveCode.CHINA_CT_AAA.getValue());
        udicCurveMap.put(20, CurveCode.CHINA_CT_AAA.getValue());
        udicCurveMap.put(30, CurveCode.CHINA_CT_AAA.getValue());
        udicCurveMap.put(40, CurveCode.CHINA_BOND_CT_AA_PLUS.getValue());
        udicCurveMap.put(50, CurveCode.CHINA_BOND_CT_AA.getValue());
        udicCurveMap.put(60, CurveCode.CHINA_BOND_CT_AA_SUB.getValue());
        udicCurveMap.put(55, CurveCode.CHINA_BOND_CT_AA_TWO.getValue());
        return udicCurveMap;
    }

    private static Map<Integer, Integer> initBondYieldCurveSecuMap() {
        // 评级映射证券曲线
        Map<Integer, Integer> secuCurveMap = new ConcurrentHashMap<>(4);
        secuCurveMap.put(20, CurveCode.SECURITIES_BOND_AAA.getValue());
        secuCurveMap.put(30, CurveCode.SECURITIES_BOND_AAA_SUB.getValue());
        secuCurveMap.put(40, CurveCode.SECURITIES_BOND_AA_PLUS.getValue());
        secuCurveMap.put(50, CurveCode.SECURITIES_BOND_AA.getValue());
        return secuCurveMap;
    }

    private static Map<Integer, Long> initBondYieldCurveInsuMap() {
        // 评级映射保险曲线
        Map<Integer, Long> insuCurveMap = new ConcurrentHashMap<>(3);
        insuCurveMap.put(40, CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA_PLUS.getCurveUniCode());
        insuCurveMap.put(50, CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA.getCurveUniCode());
        insuCurveMap.put(60, CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA_SUB.getCurveUniCode());
        return insuCurveMap;
    }

    private static List<Integer> initInduSpreadBondTypeList() {
        // 产业利差债券类型 4,5,8,9,12,37,21,22,23,31
        List<Integer> induBondTypeList = new ArrayList<>();
        induBondTypeList.add(BondType.ENTERPRISE_BOND.getValue());
        induBondTypeList.add(BondType.CORPORATE_BOND.getValue());
        induBondTypeList.add(BondType.MEDIUM_TERM_NOTES.getValue());
        induBondTypeList.add(BondType.SHORT_TERM_COMMERCIAL_PAPER.getValue());
        induBondTypeList.add(BondType.COMMERCIAL_BANK_ORDINARY_BONDS.getValue());
        induBondTypeList.add(BondType.SECURITIES_COMPANY_BONDS.getValue());
        induBondTypeList.add(BondType.COLLECTIVE_NOTES.getValue());
        induBondTypeList.add(BondType.ORIENTATION_TOOL.getValue());
        induBondTypeList.add(BondType.SUPER_SHORT_TERM_FINANCING_BOND.getValue());
        induBondTypeList.add(BondType.PROJECT_INCOME_NOTES.getValue());
        return induBondTypeList;
    }

    private static List<Integer> initUdicSpreadBondTypeList() {
        // 城投利差债券类型 4,5,8,9,22,23,25,36
        List<Integer> udicBondTypeList = new ArrayList<>();
        udicBondTypeList.add(BondType.ENTERPRISE_BOND.getValue());
        udicBondTypeList.add(BondType.CORPORATE_BOND.getValue());
        udicBondTypeList.add(BondType.MEDIUM_TERM_NOTES.getValue());
        udicBondTypeList.add(BondType.SHORT_TERM_COMMERCIAL_PAPER.getValue());
        udicBondTypeList.add(BondType.ORIENTATION_TOOL.getValue());
        udicBondTypeList.add(BondType.SUPER_SHORT_TERM_FINANCING_BOND.getValue());
        udicBondTypeList.add(BondType.GOVERNMENT_BACKED_BOND.getValue());
        udicBondTypeList.add(BondType.GREEN_BOND_FINANCING_TOOL.getValue());
        return udicBondTypeList;
    }

    private static Set<Integer> initLgBondSpreadTypeList() {
        return Sets.newHashSet(BondType.LOCAL_TREASURY_BOND.getValue());
    }

    private static Set<Integer> initSecuSpreadBondTypeList() {
        return Sets.newHashSet(
                BondType.ENTERPRISE_BOND.getValue(),
                BondType.CORPORATE_BOND.getValue(),
                BondType.MEDIUM_TERM_NOTES.getValue(),
                BondType.SHORT_TERM_COMMERCIAL_PAPER.getValue(),
                BondType.OTHER_FINANCIAL_BOND.getValue(),
                BondType.INTERNATIONAL_AGENCY_BOND.getValue(),
                BondType.SECURITIES_COMPANY_BONDS.getValue(),
                BondType.SECURITY_SUBORDINATED_BOND.getValue(),
                BondType.SECURITY_SHORT_TERM_FINANCING_BOND.getValue());
    }

    private static Set<Integer> initInsuSpreadBondTypeList() {
        return Sets.newHashSet(
                BondType.ENTERPRISE_BOND.getValue(),
                BondType.CORPORATE_BOND.getValue(),
                BondType.MEDIUM_TERM_NOTES.getValue(),
                BondType.SHORT_TERM_COMMERCIAL_PAPER.getValue(),
                BondType.TIER_TWO_CAPITAL_INSTRUMENT.getValue(),
                BondType.OTHER_FINANCIAL_BOND.getValue(),
                BondType.COLLECTIVE_NOTES.getValue(),
                BondType.ORIENTATION_TOOL.getValue(),
                BondType.SUPER_SHORT_TERM_FINANCING_BOND.getValue(),
                BondType.INSURANCE_COMPANY_SUBORDINATED_TERM.getValue(),
                BondType.SME_COLLECTIVE_BOND.getValue(),
                BondType.PROJECT_INCOME_NOTES.getValue(),
                BondType.PROJECT_INCOME_BOND.getValue(),
                BondType.BOND_FINANCING_INSTRUMENT.getValue(),
                BondType.GREEN_BOND_FINANCING_TOOL.getValue());
    }

    private static Map<Long, String> initInduUnicodeMap() {
        // 利差行业编码
        Map<Long, String> induUnicodeMap = new ConcurrentHashMap<>(23);
        induUnicodeMap.put(430_000L, "房地产");
        induUnicodeMap.put(370_000L, "医药生物");
        induUnicodeMap.put(510_000L, "综合");
        induUnicodeMap.put(610_000L, "建筑材料");
        induUnicodeMap.put(420_000L, "交通运输");
        induUnicodeMap.put(410_000L, "公用事业");
        induUnicodeMap.put(450_000L, "商业贸易");
        induUnicodeMap.put(640_000L, "机械设备");
        induUnicodeMap.put(220_000L, "化工");
        induUnicodeMap.put(240_000L, "有色金属");
        induUnicodeMap.put(210_000L, "采掘");
        induUnicodeMap.put(490_000L, "非银金融");
        induUnicodeMap.put(460_000L, "休闲服务");
        induUnicodeMap.put(280_000L, "汽车");
        induUnicodeMap.put(620_000L, "建筑装饰");
        induUnicodeMap.put(230_000L, "钢铁");
        induUnicodeMap.put(340_000L, "食品饮料");
        induUnicodeMap.put(420_100L, "港口");
        induUnicodeMap.put(410_100L, "电力");
        induUnicodeMap.put(610_100L, "水泥制造");
        induUnicodeMap.put(420_200L, "高速公路");
        induUnicodeMap.put(210_200L, "煤炭开采");
        induUnicodeMap.put(480_000L, "银行");
        return induUnicodeMap;
    }

    private static List<Long> initIndu2UnicodeList() {
        // 利差二级行业编码
        List<Long> indu2UnicodeList = new ArrayList<>();
        indu2UnicodeList.add(420_100L);
        indu2UnicodeList.add(410_100L);
        indu2UnicodeList.add(610_100L);
        indu2UnicodeList.add(420_200L);
        indu2UnicodeList.add(210_200L);
        return indu2UnicodeList;
    }

    private static Map<Long, Long> initIndu2Indu1Mapping() {
        // 行业2与行业1的映射关系
        Map<Long, Long> indu2Indu1Map = new ConcurrentHashMap<>(5);
        indu2Indu1Map.put(420_100L, 420_000L);
        indu2Indu1Map.put(410_100L, 410_000L);
        indu2Indu1Map.put(610_100L, 610_000L);
        indu2Indu1Map.put(420_200L, 420_000L);
        indu2Indu1Map.put(210_200L, 210_000L);
        return indu2Indu1Map;
    }

    private static List<String> initCurveYtmInfoList() {
        // 曲线收益率信息
        List<String> curveYtmInfoList = new ArrayList<>();
        curveYtmInfoList.add("0-0-Ytm0M");
        curveYtmInfoList.add("30-0.083-Ytm1M");
        curveYtmInfoList.add("60-0.167-Ytm2M");
        curveYtmInfoList.add("90-0.25-Ytm3M");
        curveYtmInfoList.add("180-0.5-Ytm6M");
        curveYtmInfoList.add("270-0.75-Ytm9M");
        curveYtmInfoList.add("365-1-Ytm1Y");
        curveYtmInfoList.add("730-2-Ytm2Y");
        curveYtmInfoList.add("1095-3-Ytm3Y");
        curveYtmInfoList.add("1460-4-Ytm4Y");
        curveYtmInfoList.add("1825-5-Ytm5Y");
        curveYtmInfoList.add("2190-6-Ytm6Y");
        curveYtmInfoList.add("2555-7-Ytm7Y");
        curveYtmInfoList.add("2920-8-Ytm8Y");
        curveYtmInfoList.add("3285-9-Ytm9Y");
        curveYtmInfoList.add("3650-10-Ytm10Y");
        curveYtmInfoList.add("5475-15-Ytm15Y");
        curveYtmInfoList.add("7300-20-Ytm20Y");
        curveYtmInfoList.add("10950-30-Ytm30Y");
        curveYtmInfoList.add("18250-50-Ytm50Y");

        return curveYtmInfoList;
    }

    private static Map<String, String> initUniCurveYtmInfoList() {
        // 曲线收益率信息 业务需要的期限
        HashMap<String, String> map = Maps.newHashMapWithExpectedSize(17);
        map.put("1m", "ytm1M");
        map.put("3m", "ytm3M");
        map.put("0.5y", "ytm6M");
        map.put("6m", "ytm6M");
        map.put("9m", "ytm9M");
        map.put("1y", "ytm1Y");
        map.put("2y", "ytm2Y");
        map.put("3y", "ytm3Y");
        map.put("4y", "ytm4Y");
        map.put("5y", "ytm5Y");
        map.put("6y", "ytm6Y");
        map.put("7y", "ytm7Y");
        map.put("10y", "ytm10Y");
        map.put("15y", "ytm15Y");
        map.put("20y", "ytm20Y");
        map.put("30y", "ytm30Y");
        map.put("50y", "ytm50Y");
        return map;
    }

    private static List<IndustryResponseDTO> initIndustryResponseList() {
        List<IndustryResponseDTO> industryResponseList = new ArrayList<>();
        // 行业响应数据集
        for (Map.Entry<Long, String> entry : INDU_UNICODE_MAP.entrySet()) {
            industryResponseList.add(new IndustryResponseDTO(entry.getKey(), entry.getValue()));
        }
        return industryResponseList;
    }

    private static Map<Integer, Integer> initBondImpliedRatingTagMapping() {
        Map<Integer, Integer> bondImpliedRatingTagMap = new ConcurrentHashMap<>(10);
        bondImpliedRatingTagMap.put(10, BondImpliedRatingMappingTagTypeEnum.AAA.getValue());
        bondImpliedRatingTagMap.put(20, BondImpliedRatingMappingTagTypeEnum.AAA.getValue());
        bondImpliedRatingTagMap.put(30, BondImpliedRatingMappingTagTypeEnum.AAA.getValue());
        bondImpliedRatingTagMap.put(40, BondImpliedRatingMappingTagTypeEnum.AA.getValue());
        bondImpliedRatingTagMap.put(50, BondImpliedRatingMappingTagTypeEnum.AA.getValue());
        bondImpliedRatingTagMap.put(55, BondImpliedRatingMappingTagTypeEnum.AA.getValue());
        bondImpliedRatingTagMap.put(60, BondImpliedRatingMappingTagTypeEnum.AA.getValue());
        bondImpliedRatingTagMap.put(70, BondImpliedRatingMappingTagTypeEnum.A.getValue());
        bondImpliedRatingTagMap.put(80, BondImpliedRatingMappingTagTypeEnum.A.getValue());
        bondImpliedRatingTagMap.put(90, BondImpliedRatingMappingTagTypeEnum.A.getValue());
        return bondImpliedRatingTagMap;
    }

    private static Map<Integer, Integer> initComYyRatingTagMapping() {
        Map<Integer, Integer> comYyRatingTagMap = new ConcurrentHashMap<>(8);
        comYyRatingTagMap.put(1, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(2, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(3, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(4, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(5, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(6, ComYyRatingMappingTagTypeEnum.SPECULATES_LEVEL.getValue());
        comYyRatingTagMap.put(7, ComYyRatingMappingTagTypeEnum.SPECULATES_LEVEL.getValue());
        comYyRatingTagMap.put(8, ComYyRatingMappingTagTypeEnum.SPECULATES_LEVEL.getValue());
        return comYyRatingTagMap;
    }

    private static Map<Integer, Integer> initUdicComYyRatingTagMapping() {
        Map<Integer, Integer> comYyRatingTagMap = new ConcurrentHashMap<>(8);
        comYyRatingTagMap.put(1, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(2, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(3, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(4, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(5, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(6, ComYyRatingMappingTagTypeEnum.INVEST_LEVEL.getValue());
        comYyRatingTagMap.put(7, ComYyRatingMappingTagTypeEnum.SPECULATES_LEVEL.getValue());
        comYyRatingTagMap.put(8, ComYyRatingMappingTagTypeEnum.SPECULATES_LEVEL.getValue());
        return comYyRatingTagMap;
    }


    private static Set<String> initChangeOrderPropertySet() {
        Set<String> changeOrderPropertySet = new HashSet<>();
        changeOrderPropertySet.add(getPropertyName(ComYieldSpreadChangeDO::getCreditSpreadChange3M));
        changeOrderPropertySet.add(getPropertyName(ComYieldSpreadChangeDO::getCreditSpreadChange6M));
        changeOrderPropertySet.add(getPropertyName(ComYieldSpreadChangeDO::getExcessSpreadChange3M));
        changeOrderPropertySet.add(getPropertyName(ComYieldSpreadChangeDO::getExcessSpreadChange6M));
        changeOrderPropertySet.add(getPropertyName(ComYieldSpreadChangeDO::getCreditSpreadQuantile3Y));
        changeOrderPropertySet.add(getPropertyName(ComYieldSpreadChangeDO::getCreditSpreadQuantile5Y));
        changeOrderPropertySet.add(getPropertyName(ComYieldSpreadChangeDO::getExcessSpreadQuantile3Y));
        changeOrderPropertySet.add(getPropertyName(ComYieldSpreadChangeDO::getExcessSpreadQuantile5Y));
        return changeOrderPropertySet;
    }

    /**
     * 年时间切片
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return SpreadDateRange
     */
    public static List<AbstractRatingRouter.SpreadDateRange> splitDateRange(LocalDate startDate, LocalDate endDate) {
        List<AbstractRatingRouter.SpreadDateRange> dateRangeList = Lists.newArrayList();
        for (LocalDate localDate = startDate; !localDate.isAfter(endDate); localDate = localDate.plusMonths(SPLIT_MONTH).with(TemporalAdjusters.firstDayOfMonth())) {
            //获取当前月底时间
            LocalDate lastDay = localDate.plusMonths(SPLIT_MONTH - 1L).with(TemporalAdjusters.lastDayOfMonth());
            if (lastDay.isAfter(endDate)) {
                lastDay = endDate;
            }
            AbstractRatingRouter.SpreadDateRange dateRange = new AbstractRatingRouter.SpreadDateRange(Date.valueOf(localDate), Date.valueOf(lastDay));
            dateRangeList.add(dateRange);
        }
        return dateRangeList;
    }

    /**
     * 转换属性为表字段
     *
     * @param propertyName 属性名
     * @param clazz        目标对象
     * @return 表字段
     */
    public static String convertSortPropertyToTableColumName(String propertyName, Class<?> clazz) {
        return QUERY_HELPER.getQueryColumnByProperty(CHANGE_ORDER_PROPERTY_SET.contains(propertyName) ? ComYieldSpreadChangeDO.class : clazz, propertyName);
    }

    /**
     * 转换属性为表字段
     *
     * @param sort  排序字段对象
     * @param clazz 目标对象
     * @return 排序对象
     */
    public static SortDTO convertSortPropertyToTableColumName(SortDTO sort, Class<?> clazz) {
        if (Objects.nonNull(sort)) {
            return new SortDTO(convertSortPropertyToTableColumName(sort.getPropertyName(), clazz), sort.getSortDirection());
        }
        return null;
    }

    /**
     * 根据数据列表获取每个期限的平均值
     *
     * @param datalist         数据列表
     * @param periodConfigList 期限配置列表
     * @return 最小 最大 中位数 统计值
     */
    public static <T> SpreadStatisticsBO statisticsSpreadByPeriod(List<T> datalist, List<Integer> periodConfigList) {
        SpreadStatisticsBO spreadStatisticsBO = new SpreadStatisticsBO();
        if (CollectionUtils.isEmpty(datalist) || CollectionUtils.isEmpty(periodConfigList)) {
            return spreadStatisticsBO;
        }
        List<BigDecimal> yields = Lists.newArrayList();

        // 获取 datalist 中对象的所有变量名称
        // 获取第一个对象的类
        Class<?> clazz = datalist.get(0).getClass();
        List<String> fieldNames = getAllFields(clazz).stream().map(Field::getName).distinct().collect(Collectors.toList());
        List<String> filterFieldNames = extractPeriodValidFields(fieldNames, periodConfigList);
        for (T obj : datalist) {
            for (String filterFieldName : filterFieldNames) {
                try {
                    getValueByGetMethod(filterFieldName, obj).ifPresent(fieldValue -> {
                        if (fieldValue instanceof BigDecimal) {
                            yields.add((BigDecimal) fieldValue);
                        }
                    });
                } catch (Exception e) {
                    logger.error("反射执行get方法报错,错误信息:{}", e.getMessage());
                }

            }
        }
        yields.stream().min(BigDecimal::compareTo).ifPresent(spreadStatisticsBO::setMinYield);
        yields.stream().max(BigDecimal::compareTo).ifPresent(spreadStatisticsBO::setMaxYield);
        CalculationHelper.calcMedian(yields.toArray(new BigDecimal[0])).ifPresent(spreadStatisticsBO::setMedianYield);
        return spreadStatisticsBO;
    }

    /**
     * 转换属性为表字段
     *
     * @param datalist 数据列表
     * @return 每个期限的平均值
     */
    public static <T> Optional<T> statisticsSubAvgSpreadOfPeriod(List<T> datalist) {
        if (CollectionUtils.isEmpty(datalist)) {
            return Optional.empty();
        }
        // 获取 datalist 中对象的所有变量名称
        // 获取第一个对象的类
        T firstObj = datalist.get(0);
        Class<?> clazz = datalist.get(0).getClass();
        List<String> fieldNames = getAllFields(clazz).stream().map(Field::getName).distinct().collect(Collectors.toList());
        List<String> filterFieldNames = extractPeriodValidFields(fieldNames);
        for (int i = 1; i < datalist.size(); i++) {
            T currentObj = datalist.get(i);
            for (String filterFieldName : filterFieldNames) {
                List<BigDecimal> valueList = Lists.newArrayList();
                try {
                    getValueByGetMethod(filterFieldName, firstObj).ifPresent((fieldValue -> {
                        if (fieldValue instanceof BigDecimal) {
                            valueList.add((BigDecimal) fieldValue);
                        }
                    }));
                    getValueByGetMethod(filterFieldName, currentObj).ifPresent((fieldValue -> {
                        if (fieldValue instanceof BigDecimal) {
                            valueList.add((BigDecimal) fieldValue);
                        }
                    }));
                    setValueBySetMethod(firstObj, filterFieldName, BigDecimalUtils.safeAdd(valueList).orElse(null), BigDecimal.class);
                } catch (Exception e) {
                    logger.error("反射统计各期限平均值时报错,错误信息:{}", e.getMessage());
                }

            }
        }
        for (String filterFieldName : filterFieldNames) {
            try {
                BigDecimal sum = (BigDecimal) getValueByGetMethod(filterFieldName, firstObj).orElse(null);
                BigDecimal avg = BigDecimalUtils.safeDivideWithScale(sum, BigDecimal.valueOf(datalist.size()), TWO_SCALE, RoundingMode.HALF_UP).orElse(null);
                setValueBySetMethod(firstObj, filterFieldName, avg, BigDecimal.class);
            } catch (NoSuchMethodException e) {
                logger.error("反射执行get set方法报NoSuchMethodException异常", e);
            }


        }

        return Optional.of(firstObj);
    }

    /**
     * 获取所有 是以 PeriodEnum 的text为结尾的字段 {@link PeriodEnum}
     *
     * @param fieldNames       字段名列表
     * @param periodConfigList 期限配置列表
     * @return 字段名列表
     */
    private static List<String> extractPeriodValidFields(List<String> fieldNames, List<Integer> periodConfigList) {
        List<String> validFields = new ArrayList<>();
        if (CollectionUtils.isEmpty(fieldNames) || CollectionUtils.isEmpty(periodConfigList)) {
            return validFields;
        }
        List<String> textsList = PeriodEnum.getTextsByValues(periodConfigList);
        // 正则表达式：字母后跟数字，再跟 Y 或 M
        Pattern pattern = Pattern.compile(PERIOD_FIELD_REGEX);
        for (String fieldName : fieldNames) {
            Matcher matcher = pattern.matcher(fieldName);
            if (matcher.find()) {
                // 截取数字和 Y 或 M
                String result = matcher.group(2) + matcher.group(3);
                if (textsList.contains(result)) {
                    validFields.add(fieldName);
                }
            }
        }
        return validFields;
    }

    /**
     * 获取所有 是以 PeriodEnum 的text为结尾的字段 {@link PeriodEnum}
     *
     * @param fieldNames 字段名列表
     * @return 字段名列表
     */
    private static List<String> extractPeriodValidFields(List<String> fieldNames) {
        List<String> validFields = new ArrayList<>();
        if (CollectionUtils.isEmpty(fieldNames)) {
            return validFields;
        }
        // 正则表达式：字母后跟数字，再跟 Y 或 M
        Pattern pattern = Pattern.compile(PERIOD_FIELD_REGEX);
        for (String fieldName : fieldNames) {
            Matcher matcher = pattern.matcher(fieldName);
            if (matcher.find()) {
                validFields.add(fieldName);
            }
        }
        return validFields;
    }

    /**
     * 获取一个类及其所有父类中的所有字段
     *
     * @param clazz 要操作的类
     * @return 类及其所有父类中的所有字段
     */
    public static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * 反射 执行对象的get方法获取对象值
     *
     * @param fieldName 字段名称
     * @param obj       当前对象
     * @return 字段值
     * @throws NoSuchMethodException 无此方法
     */
    public static Optional<Object> getValueByGetMethod(String fieldName, Object obj) throws NoSuchMethodException {
        if (StringUtils.isBlank(fieldName) && Objects.isNull(obj)) {
            return Optional.empty();
        }
        String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        Method method = obj.getClass().getMethod(methodName);
        return Optional.ofNullable(ReflectionUtils.invokeMethod(method, obj));

    }

    /**
     * 反射 执行对象的set方法获取对象值
     *
     * @param obj           当前对象
     * @param fieldName     字段名称
     * @param value         待设置的值
     * @param parameterType 参数类型
     * @throws NoSuchMethodException 无此方法
     */
    public static void setValueBySetMethod(Object obj, String fieldName, Object value, Class<?> parameterType) throws NoSuchMethodException {
        String setterName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);

        Method setter = obj.getClass().getMethod(setterName, parameterType);
        ReflectionUtils.invokeMethod(setter, obj, value);

    }
}
