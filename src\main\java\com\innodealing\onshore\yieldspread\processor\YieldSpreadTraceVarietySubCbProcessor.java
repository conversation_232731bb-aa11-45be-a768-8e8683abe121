package com.innodealing.onshore.yieldspread.processor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.onshore.bondmetadata.enums.CurveCode;
import com.innodealing.onshore.yieldspread.enums.CurveUniCodeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.INTEREST_RATE_BOND;

/**
 * 利差追踪-品种利差(减国债)处理器
 *
 * <AUTHOR>
 */
@Component
public class YieldSpreadTraceVarietySubCbProcessor implements YieldSpreadTraceProcessor, InitializingBean {

    private static final Set<YieldPanoramaBondTypeEnum> SUPPORT_BOND_TYPES = EnumSet.of(INTEREST_RATE_BOND);

    private final Map<Integer, Integer> curveCodeMap = Maps.newHashMap();

    @Override
    public void afterPropertiesSet() {
        curveCodeMap.put(CurveCode.CHINA_BOND_KAI.getValue(), CurveCode.CHINA_BOND.getValue());
        curveCodeMap.put(CurveUniCodeEnum.CHINA_BOND_IMPORT.getCurveCode(), CurveCode.CHINA_BOND.getValue());
        curveCodeMap.put(CurveUniCodeEnum.CHINA_BOND_NO.getCurveCode(), CurveCode.CHINA_BOND.getValue());
        curveCodeMap.put(CurveCode.CHINA_BOND_CITY.getValue(), CurveCode.CHINA_BOND.getValue());


    }

    @Override
    public Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum) {
        return SUPPORT_BOND_TYPES.contains(traceBondTypeEnum);
    }

    @Override
    public List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context) {
        YieldPanoramaBondTypeEnum bondTypeEnum = context.getBondTypeEnum();
        List<PgBondYieldPanoramaAbsDO> referentBondMidYieldPanoramas = context.getReferentBondYieldPanoramas();
        if (CollectionUtils.isEmpty(referentBondMidYieldPanoramas)) {
            return Collections.emptyList();
        }
        Map<Integer, PgBondYieldPanoramaAbsDO> referentCurveCodeMap =
                referentBondMidYieldPanoramas.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        Map<Integer, PgBondYieldPanoramaAbsDO> absCurveCodeMap =
                context.getAbsBondYieldPanoramas().stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        List<PgBondYieldSpreadTraceAbsDO> dataList = Lists.newArrayListWithExpectedSize(absCurveCodeMap.size());
        for (Map.Entry<Integer, PgBondYieldPanoramaAbsDO> absEntry : absCurveCodeMap.entrySet()) {
            PgBondYieldPanoramaAbsDO absBondYieldPanorama = absEntry.getValue();
            Integer curveCodeMapping = curveCodeMap.get(absEntry.getKey());
            if (Objects.isNull(curveCodeMapping)) {
                continue;
            }

            PgBondYieldPanoramaAbsDO referentAbs = referentCurveCodeMap.getOrDefault(curveCodeMapping, new PgBondYieldPanoramaAbsDO());

            PgBondYieldSpreadTraceAbsDO pgBondYieldSpreadTraceAbsDO = convertAbsSubtractToTrace(absBondYieldPanorama, referentAbs, bondTypeEnum,
                    context.getIssueDate(), absEntry.getKey(), YieldSpreadChartTypeEnum.VARIETY_SPREAD_SUB_CB);
            dataList.add(pgBondYieldSpreadTraceAbsDO);
        }
        return dataList;
    }
}
