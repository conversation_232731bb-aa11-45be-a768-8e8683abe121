package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 城投利差-导出作图数据请求参数
 *
 * <AUTHOR>
 */
public class UdicCurveExportRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("利差曲线类型 1. 信用利差，2. 超额利差，3. 估值收益率")
    private Integer spreadCurveType;
    @ApiModelProperty("开始利差日期,格式:yyyy-MM-dd")
    private Date startSpreadDate;
    @ApiModelProperty("结束利差日期,格式:yyyy-MM-dd")
    private Date endSpreadDate;
    @ApiModelProperty("组合曲线查询集合")
    private List<UdicCurveCompositionConditionDTO> compositionConditions;
    @ApiModelProperty("主体曲线查询集合")
    private List<CurveComSpreadDTO> curveComSpreads;
    @ApiModelProperty("债券曲线查询集合")
    private List<CurveBondSpreadDTO> curveBondSpreads;

    public Integer getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(Integer spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public List<UdicCurveCompositionConditionDTO> getCompositionConditions() {
        return Objects.isNull(compositionConditions) ? new ArrayList<>() : new ArrayList<>(compositionConditions);
    }

    public void setCompositionConditions(List<UdicCurveCompositionConditionDTO> compositionConditions) {
        this.compositionConditions = Objects.isNull(compositionConditions) ? new ArrayList<>() : new ArrayList<>(compositionConditions);
    }

    public List<CurveComSpreadDTO> getCurveComSpreads() {
        return Objects.isNull(curveComSpreads) ? new ArrayList<>() : new ArrayList<>(curveComSpreads);
    }

    public void setCurveComSpreads(List<CurveComSpreadDTO> curveComSpreads) {
        this.curveComSpreads = Objects.isNull(curveComSpreads) ? new ArrayList<>() : new ArrayList<>(curveComSpreads);
    }

    public List<CurveBondSpreadDTO> getCurveBondSpreads() {
        return Objects.isNull(curveBondSpreads) ? new ArrayList<>() : new ArrayList<>(curveBondSpreads);
    }

    public void setCurveBondSpreads(List<CurveBondSpreadDTO> curveBondSpreads) {
        this.curveBondSpreads = Objects.isNull(curveBondSpreads) ? new ArrayList<>() : new ArrayList<>(curveBondSpreads);
    }

    public Date getStartSpreadDate() {
        return Objects.isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public void setStartSpreadDate(Date startSpreadDate) {
        this.startSpreadDate = Objects.isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public Date getEndSpreadDate() {
        return Objects.isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public void setEndSpreadDate(Date endSpreadDate) {
        this.endSpreadDate = Objects.isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }
}
