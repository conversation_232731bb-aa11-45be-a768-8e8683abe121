package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * 证券债利差
 *
 * <AUTHOR>
 **/
@Table(name = "secu_bond_yield_spread")
public class PgSecuBondYieldSpreadGroupDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;
    /**
     * 债券统一编码
     */
    @Column
    private Long bondUniCode;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 一级行业编码
     */
    @Column
    private Long induLevel1Code;
    /**
     * 二级行业编码
     */
    @Column
    private Long induLevel2Code;
    /**
     * 债券信用利差;单位(BP)
     */
    @Column(name = "((median(bond_credit_spread)::double precision)::DECIMAL)")
    private BigDecimal bondCreditSpread;
    /**
     * 债券超额利差;单位(BP)
     */
    @Column(name = "((median(bond_excess_spread)::double precision)::DECIMAL)")
    private BigDecimal bondExcessSpread;
    /**
     * 中债收益率;单位(%)
     */
    @Column(name = "((median(cb_yield)::double precision)::DECIMAL)")
    private BigDecimal cbYield;
    /**
     * 国开插值收益率;单位(%)
     */
    @Column(name = "((median(cdb_lerp_yield) :: double precision)::DECIMAL)")
    private BigDecimal cdbLerpYield;

    /**
     * 信用利差平均数;单位(BP)
     */
    @Column(name = "AVG(bond_credit_spread)")
    private BigDecimal avgBondCreditSpread;

    /**
     * 超额利差平均数;单位(BP)
     */
    @Column(name = "AVG(bond_excess_spread)")
    private BigDecimal avgBondExcessSpread;

    /**
     * 中债收益率平均数;单位(BP)
     */
    @Column(name = "AVG(cb_yield)")
    private BigDecimal avgCbYield;
    /**
     * 利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer spreadBondType;
    /**
     * 信用利差不为空的债券样本数量
     */
    @Column(name = "COUNT(bond_credit_spread)")
    private Integer bondCreditSpreadCount;
    /**
     * 超额利差不为空的债券样本数量
     */
    @Column(name = "COUNT(bond_excess_spread)")
    private Integer bondExcessSpreadCount;
    /**
     * 中债收益率不为空的债券样本数量
     */
    @Column(name = "COUNT(cb_yield)")
    private Integer cbYieldCount;
    /**
     * 国开插值收益率不为空的债券样本数量
     */
    @Column(name = "COUNT(cdb_lerp_yield)")
    private Integer cdbLerpYieldCount;
    /**
     * 债券样本数量
     */
    @Column(name = "COUNT(*)")
    private Integer spreadCount;

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }

    public Integer getSpreadCount() {
        return spreadCount;
    }

    public void setSpreadCount(Integer spreadCount) {
        this.spreadCount = spreadCount;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public BigDecimal getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(BigDecimal cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getCbYieldCount() {
        return cbYieldCount;
    }

    public void setCbYieldCount(Integer cbYieldCount) {
        this.cbYieldCount = cbYieldCount;
    }

    public Integer getCdbLerpYieldCount() {
        return cdbLerpYieldCount;
    }

    public void setCdbLerpYieldCount(Integer cdbLerpYieldCount) {
        this.cdbLerpYieldCount = cdbLerpYieldCount;
    }

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }

    public Long getInduLevel2Code() {
        return induLevel2Code;
    }

    public void setInduLevel2Code(Long induLevel2Code) {
        this.induLevel2Code = induLevel2Code;
    }
}