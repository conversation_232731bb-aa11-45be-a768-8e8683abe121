package com.innodealing.onshore.yieldspread.model.entity.yieldspread.group;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;
import java.util.Objects;

/**
 * 保险债利差
 *
 * <AUTHOR>
 **/
@Table(name = "insu_bond_yield_spread")
public class InsuBondYieldSpreadGroupDO {

    @Column
    private Long comUniCode;

    /**
     * 债券统一编码
     */
    @Column
    private Long bondUniCode;
    /**
     * 债券编码
     */
    @Column
    private String bondCode;

    /**
     * 一级行业编码
     */
    @Column
    private Long induLevel1Code;
    /**
     * 一级行业名称
     */
    @Column
    private String induLevel1Name;
    /**
     * 二级行业编码
     */
    @Column
    private Long induLevel2Code;
    /**
     * 二级行业名称
     */
    @Column
    private String induLevel2Name;

    /**
     * 企业类型(性质)
     */
    @Column
    private Integer businessNature;

    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;


    /**
     * 主体外部评级映射
     */
    @Column
    private Integer comExtRatingMapping;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }

    public String getInduLevel1Name() {
        return induLevel1Name;
    }

    public void setInduLevel1Name(String induLevel1Name) {
        this.induLevel1Name = induLevel1Name;
    }

    public Long getInduLevel2Code() {
        return induLevel2Code;
    }

    public void setInduLevel2Code(Long induLevel2Code) {
        this.induLevel2Code = induLevel2Code;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public Integer getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(Integer businessNature) {
        this.businessNature = businessNature;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }
}