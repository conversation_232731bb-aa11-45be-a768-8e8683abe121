package com.innodealing.onshore.yieldspread.enums;

/**
 * 地方债利差-剩余期限挡位枚举
 *
 * <AUTHOR>
 * @create: 2024-10-25
 */
public enum LgBondYieldSpreadTenorEnum {
    /**
     * 1M
     */
    GRADE_1M(PeriodEnum.ONE_MONTH, 0, 60),
    GRADE_3M(PeriodEnum.THREE_MONTHS, 60, 120),
    GRADE_6M(PeriodEnum.SIX_MONTHS, 120, 210),
    GRADE_9M(PeriodEnum.NINE_MONTHS, 210, 300),
    GRADE_1Y(PeriodEnum.ONE_YEAR, 300, 548),
    GRADE_2Y(PeriodEnum.TWO_YEARS, 548, 913),
    GRADE_3Y(PeriodEnum.THREE_YEARS, 913, 1460),
    GRADE_5Y(PeriodEnum.FIVE_YEARS, 1460, 2190),
    GRADE_7Y(PeriodEnum.SEVEN_YEARS, 2190, 3103),
    GRADE_10Y(PeriodEnum.TEN_YEARS, 3103, 4563),
    GRADE_15Y(PeriodEnum.FIFTEEN_YEARS, 4563, 6388),
    GRADE_20Y(PeriodEnum.TWENTY_YEARS, 6388, 9125),
    GRADE_30Y(PeriodEnum.THIRTY_YEARS, 9125, Integer.MAX_VALUE),

    ;
    private final PeriodEnum periodEnum;
    private final int minDay;
    private final int maxDay;

    LgBondYieldSpreadTenorEnum(PeriodEnum periodEnum, int minDay, int maxDay) {
        this.periodEnum = periodEnum;
        this.minDay = minDay;
        this.maxDay = maxDay;
    }

    /**
     * 获取 地方债对应期限挡位
     *
     * @param remainingTenorDay 剩余期限天数
     * @return 地方债对应期限挡位
     */
    public static LgBondYieldSpreadTenorEnum getLgBondYieldSpreadTenor(int remainingTenorDay) {
        for (LgBondYieldSpreadTenorEnum lgBondYieldSpreadTenorEnum : LgBondYieldSpreadTenorEnum.values()) {
            int minDay = lgBondYieldSpreadTenorEnum.getMinDay();
            int maxDay = lgBondYieldSpreadTenorEnum.getMaxDay();
            if (remainingTenorDay >= minDay && remainingTenorDay < maxDay) {
                return lgBondYieldSpreadTenorEnum;
            }
        }
        return LgBondYieldSpreadTenorEnum.GRADE_30Y;
    }

    public PeriodEnum getPeriodEnum() {
        return periodEnum;
    }

    public int getMinDay() {
        return minDay;
    }

    public int getMaxDay() {
        return maxDay;
    }
}
