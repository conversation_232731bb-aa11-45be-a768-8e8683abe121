package com.innodealing.onshore.yieldspread.enums;


import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 地方利差曲线类型枚举类
 *
 * <AUTHOR>
 */
public enum LgSpreadCurveTypeEnum {
    /**
     * 信用利差
     * 默认-信用利差(减国开)
     */
    CREDIT_SPREAD_CB(SpreadCurveTypeEnum.CREDIT_SPREAD, "信用利差(减国开)"),
    /**
     * 估值收益率
     */
    CB_YIELD_SPREAD(SpreadCurveTypeEnum.CB_YIELD_SPREAD, "到期收益率"),
    /**
     * 信用利差(减国债)
     */
    CREDIT_SPREAD_TB(SpreadCurveTypeEnum.CREDIT_SPREAD_TB, "信用利差(减国债)"),
    ;
    private final SpreadCurveTypeEnum spreadCurveType;
    private final String name;

    private static final Map<SpreadCurveTypeEnum, LgSpreadCurveTypeEnum> SPREAD_CURVE_TYPE_TO_LG_MAP = Arrays.stream(LgSpreadCurveTypeEnum.values())
                    .collect(Collectors.toMap(LgSpreadCurveTypeEnum::getSpreadCurveType, x -> x));

    LgSpreadCurveTypeEnum(SpreadCurveTypeEnum spreadCurveTypeEnum, String name) {
        this.spreadCurveType = spreadCurveTypeEnum;
        this.name = name;
    }

    /**
     * 获取 地方债-利差曲线类型枚举类
     *
     * @param spreadCurveType 利差曲线类型枚举类-通用
     * @return 地方债-利差曲线类型枚举类
     */
    public static Optional<LgSpreadCurveTypeEnum> getLgSpreadCurveTypeEnum(SpreadCurveTypeEnum spreadCurveType){
        if (Objects.isNull(spreadCurveType)) {
            return Optional.empty();
        }
        LgSpreadCurveTypeEnum lgSpreadCurveTypeEnum = SPREAD_CURVE_TYPE_TO_LG_MAP.get(spreadCurveType);
        if (Objects.isNull(lgSpreadCurveTypeEnum)) {
            return Optional.empty();
        }
        return Optional.of(lgSpreadCurveTypeEnum);
    }

    /**
     * 获取 地方债利差excel导出时字段名描述
     *
     * @return 地方债利差excel导出时字段名
     */
    public String getExcelFieldName(){
        switch (this) {
            case CREDIT_SPREAD_CB:
            case CREDIT_SPREAD_TB:
                return this.getName().concat("BP");
            default:
                return this.getName().concat("%");
        }
    }

    /**
     * 获取通用利差曲线类型
     *
     * @return 通用利差曲线类型
     */
    public SpreadCurveTypeEnum getSpreadCurveType() {
        return spreadCurveType;
    }

    /**
     * 获取 地方债利差曲线类型-描述
     *
     * @return name
     */
    public String getName() {
        return name;
    }
}
