package com.innodealing.onshore.yieldspread.service;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.InduBondYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InduCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InduPanoramaResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.IndustryResponseDTO;
import org.springframework.lang.Nullable;

import java.sql.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产业债利差 Service
 *
 * <AUTHOR>
 **/
public interface InduBondYieldSpreadService {

    /**
     * 产业债利差计算
     *
     * @param onshoreBondInfoDTOs 债券信息
     * @param bondYieldCurveMap   收益率曲线
     * @param spreadDate          利差日期
     * @param isEnableOldData     是否启用老数据
     * @return InduBondYieldSpreadDO
     */
    int calcInduBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs,
                                             Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                             Date spreadDate, Boolean isEnableOldData);

    /**
     * 行业利差-全景图列表查询
     *
     * @param request 行业利差全景图请求参数
     * @return {@link List}<{@link InduPanoramaResponseDTO}> 行业利差全景图响应集合
     */
    List<InduPanoramaResponseDTO> listPanoramas(InduPanoramaRequestDTO request);

    /**
     * 获取最大利差日期
     *
     * @return {@link String} 利差日期
     */
    Date getMaxSpreadDate();

    /**
     * 刷新行业利差全景物化视图
     */
    void refreshMvInduBondYieldSpreadPanorama();

    /**
     * 查询行业列表
     *
     * @return {@link List}<{@link IndustryResponseDTO}> 行业列表响应集合
     */
    List<IndustryResponseDTO> listIndustries();

    /**
     * 行业单券利差分页查询
     *
     * @param request 行业单券利差分页查询请求参数
     * @return {@link NormPagingResult}<{@link InduBondYieldSpreadResponseDTO}> 行业单券利差分页查询响应数据集
     */
    NormPagingResult<InduBondYieldSpreadResponseDTO> getBondYieldSpreadPaging(InduListRequestDTO request);

    /**
     * 查询行业利差曲线数据集
     *
     * @param request 查询行业利差曲线数据集请求参数
     * @return <{@link List}<{@link InduCurveResponseDTO}>> 查询行业利差曲线响应数据集
     */
    List<InduCurveResponseDTO> listCurves(InduCurveRequestDTO request);

    /**
     * 刷新行业利差曲线物化视图
     */
    void refreshMvInduBondYieldSpreadCurve();

    /**
     * 刷新行业利差曲线历史物化视图
     *
     * @param isTableRefresh 是否刷新表结构
     */
    void refreshMvInduBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh);

    /**
     * 刷新行业利差曲线历史物化视图 by 时间范围
     *
     * @param param 开始时间-结束时间
     */
    void refreshMvInduBondYieldSpreadRatingCurve(RefreshYieldCurveParam param);

    /**
     * 根据昨日行业利差曲线数据
     */
    void refreshCurveYesterday();

    /**
     * 保存产业曲线
     *
     * @param userid       用户ID
     * @param curveGroupId 曲线组id
     * @param request      请求参数
     * @return 执行结果
     */
    boolean saveCurve(Long userid, Long curveGroupId, InduCurveGenerateConditionReqDTO request);

    /**
     * 更新曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @param request 更新参数
     * @return 执行结果
     */
    boolean updateCurve(Long userid, Long curveId, InduCurveGenerateConditionReqDTO request);

    /**
     * 获取单券利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 单券利差数据
     */
    NormPagingResult<InduBondYieldSpreadResponseDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 查询曲线数据集
     *
     * @param bondUniCode 债券唯一编码
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveDTO}>
     */
    List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate);

    /**
     * 查询债券数据集合
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link InduBondYieldSpreadResponseDTO}>
     */
    List<InduBondYieldSpreadResponseDTO> listBonds(Date spreadDate, @Nullable Set<Long> bondUniCodes);
}
