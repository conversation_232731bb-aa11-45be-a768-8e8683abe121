package com.innodealing.onshore.yieldspread.service.dmdc;

import java.sql.Date;

/**
 * 利差数据迁移 Service
 *
 * <AUTHOR>
 **/
public interface InterestDataMigrateService {

    /**
     * 城投债利差数据迁移
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 影响行数
     */
    int bondInterestCtzMigrate(Date startDate, Date endDate);

    /**
     * 行业债利差数据迁移
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 影响行数
     */
    int bondInterestInduMigrate(Date startDate, Date endDate);

    /**
     * 城投主体利差数据迁移
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 影响行数
     */
    int comInterestCtzHistMigrate(Date startDate, Date endDate);

    /**
     * 行业主体利差数据迁移
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 影响行数
     */
    int comInterestInduHistMigrate(Date startDate, Date endDate);
}
