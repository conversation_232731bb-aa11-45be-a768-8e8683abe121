package com.innodealing.onshore.yieldspread.model.dto.response;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 自定义曲线生成详情
 *
 * <AUTHOR>
 */
public class GenerateDetailResDTO {

    private Integer availablePermit;

    private Integer permit;

    private List<CustomCurveGenerateDetailResDTO> customCurves;

    public Integer getAvailablePermit() {
        return availablePermit;
    }

    public void setAvailablePermit(Integer availablePermit) {
        this.availablePermit = availablePermit;
    }

    public Integer getPermit() {
        return permit;
    }

    public void setPermit(Integer permit) {
        this.permit = permit;
    }

    public List<CustomCurveGenerateDetailResDTO> getCustomCurves() {
        return Objects.isNull(customCurves) ? new ArrayList<>() : new ArrayList<>(customCurves);
    }

    public void setCustomCurves(List<CustomCurveGenerateDetailResDTO> customCurves) {
        this.customCurves = Objects.isNull(customCurves) ? new ArrayList<>() : new ArrayList<>(customCurves);
    }

}
