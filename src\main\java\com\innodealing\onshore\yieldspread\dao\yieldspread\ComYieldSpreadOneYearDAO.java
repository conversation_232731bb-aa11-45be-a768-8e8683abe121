package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.UpdateQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.ComYieldSpreadOneYearMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComCreditSpreadBO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.ComYieldSpreadOneYearDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 主体利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class ComYieldSpreadOneYearDAO {

    @Resource
    private ComYieldSpreadOneYearMapper comYieldSpreadOneYearMapper;

    @Resource(name = YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource(name = YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;


    /**
     * 查询主体信用利差列表
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param comUniCodeList 主体唯一编码集合
     * @return {@link List}<{@link ComCreditSpreadBO}> 主体信用利差列表
     */
    public List<ComCreditSpreadBO> listComCreditSpreads(Date startDate, Date endDate, Set<Long> comUniCodeList) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate) || CollectionUtils.isEmpty(comUniCodeList)) {
            return Collections.emptyList();
        }
        Long minId = ShardingUtils.getMinPkOfDate(startDate);
        Long maxId = ShardingUtils.getMaxPkOfDate(endDate);
        DynamicQuery<ComYieldSpreadOneYearDO> groupQuery = DynamicQuery.createQuery(ComYieldSpreadOneYearDO.class)
                .select(ComYieldSpreadOneYearDO::getComUniCode, ComYieldSpreadOneYearDO::getSpreadDate, ComYieldSpreadOneYearDO::getComCreditSpread)
                .and(ComYieldSpreadOneYearDO::getId, between(minId, maxId))
                .and(ComYieldSpreadOneYearDO::getComUniCode, in(comUniCodeList));
        List<ComYieldSpreadOneYearDO> comYieldSpreadList = comYieldSpreadOneYearMapper.selectByDynamicQuery(groupQuery);
        if (CollectionUtils.isEmpty(comUniCodeList)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(comYieldSpreadList, ComCreditSpreadBO.class);
    }

    /**
     * 批量或更新主体利差数据
     *
     * @param spreadDate           利差日期
     * @param comYieldSpreadOneYearDOList 主体利差数据集合
     * @return int 保存成功数
     */
    public int saveComYieldSpreadDOList(Date spreadDate, List<ComYieldSpreadOneYearDO> comYieldSpreadOneYearDOList) {
        if (CollectionUtils.isEmpty(comYieldSpreadOneYearDOList)) {
            return 0;
        }
        Long minId = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxId = ShardingUtils.getMaxPkOfDate(spreadDate);
        Set<Long> comUniCodes = comYieldSpreadOneYearDOList.stream().map(ComYieldSpreadOneYearDO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        AtomicInteger effectRows = new AtomicInteger();
        // 批量查询已存在的数据
        DynamicQuery<ComYieldSpreadOneYearDO> query = DynamicQuery.createQuery(ComYieldSpreadOneYearDO.class)
                .select(ComYieldSpreadOneYearDO::getId, ComYieldSpreadOneYearDO::getComUniCode, ComYieldSpreadOneYearDO::getSpreadDate)
                .and(ComYieldSpreadOneYearDO::getId, between(minId, maxId))
                .and(ComYieldSpreadOneYearDO::getSpreadDate, isEqual(spreadDate))
                .and(ComYieldSpreadOneYearDO::getComUniCode, in(comUniCodes));
        List<ComYieldSpreadOneYearDO> existDataList = comYieldSpreadOneYearMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            return effectRows.addAndGet(doBatchInsert(comYieldSpreadOneYearDOList));
        }
        Map<String, ComYieldSpreadOneYearDO> existComYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(this::getKey, Function.identity(), (x1, x2) -> x2));
        List<ComYieldSpreadOneYearDO> insertList = new ArrayList<>();
        List<ComYieldSpreadOneYearDO> updateList = new ArrayList<>();
        for (ComYieldSpreadOneYearDO comYieldSpreadOneYearDO : comYieldSpreadOneYearDOList) {
            ComYieldSpreadOneYearDO existComYieldSpreadOneYearDO = existComYieldSpreadDOMap.get(this.getKey(comYieldSpreadOneYearDO));
            if (Objects.isNull(existComYieldSpreadOneYearDO)) {
                insertList.add(comYieldSpreadOneYearDO);
            } else {
                comYieldSpreadOneYearDO.setId(existComYieldSpreadOneYearDO.getId());
                updateList.add(comYieldSpreadOneYearDO);
            }
        }
        // 开启事务执行
        transactionTemplate.execute(transactionStatus -> {
            // 批量操作
            effectRows.addAndGet(doBatchUpdate(updateList));
            effectRows.addAndGet(doBatchInsert(insertList));
            return true;
        });
        return effectRows.get();
    }

    private String getKey(ComYieldSpreadOneYearDO comYieldSpreadOneYearDO) {
        return String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, comYieldSpreadOneYearDO.getComUniCode(), comYieldSpreadOneYearDO.getSpreadDate().getTime());
    }

    /**
     * 批量更新
     *
     * @param updateList 主体利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<ComYieldSpreadOneYearDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<ComYieldSpreadOneYearMapper> updateBatchAction =
                MapperBatchAction.create(ComYieldSpreadOneYearMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (ComYieldSpreadOneYearDO comYieldSpreadOneYearDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                UpdateQuery<ComYieldSpreadOneYearDO> updateQuery = UpdateQuery.createQuery(ComYieldSpreadOneYearDO.class)
                        .set(comYieldSpreadOneYearDO, o -> o.ignore(ComYieldSpreadOneYearDO::getId, ComYieldSpreadOneYearDO::getSpreadDate,
                                ComYieldSpreadOneYearDO::getCreateTime, ComYieldSpreadOneYearDO::getComUniCode))
                        .and(ComYieldSpreadOneYearDO::getId, isEqual(comYieldSpreadOneYearDO.getId()));
                mapper.updateByUpdateQuery(updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 主体利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<ComYieldSpreadOneYearDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<ComYieldSpreadOneYearMapper> insertBatchAction =
                MapperBatchAction.create(ComYieldSpreadOneYearMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (ComYieldSpreadOneYearDO comYieldSpreadOneYearDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(comYieldSpreadOneYearDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 清除主体利差数据
     *
     * @param clearDate 清除日期
     * @return int 清除行数
     */
    @Transactional(transactionManager = YieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int clearComYieldSpread(Date clearDate) {
        DynamicQuery<ComYieldSpreadOneYearDO> query = DynamicQuery.createQuery(ComYieldSpreadOneYearDO.class)
                .and(ComYieldSpreadOneYearDO::getSpreadDate, lessThan(clearDate));
        return comYieldSpreadOneYearMapper.deleteByDynamicQuery(query);
    }
}
