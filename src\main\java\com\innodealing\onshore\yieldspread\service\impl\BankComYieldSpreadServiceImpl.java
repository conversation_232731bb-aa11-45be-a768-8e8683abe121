package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComCurrentBondsBalanceDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondfinance.response.ComFinanceSheetResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.financialinstitution.BankFinIndicatorResponseDTO;
import com.innodealing.onshore.bondmetadata.enums.BankTypeEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgBankBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.BankComYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.BankComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.view.BankComYieldSpreadViewDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.model.bo.BankComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.MixYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.dto.BankYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.BankCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BankComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgBankBondYieldSpreadGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.view.BankComYieldSpreadDynamicView;
import com.innodealing.onshore.yieldspread.service.BankComYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.internal.BondFinanceService;
import com.innodealing.onshore.yieldspread.service.internal.BondInfoService;
import com.innodealing.onshore.yieldspread.service.internal.FinancialInstitutionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.innodealing.onshore.bondmetadata.enums.BankSeniorityRankingEnum.*;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.WORK_THREAD_NUM;

/**
 * 银行主体利差 Service
 *
 * <AUTHOR>
 **/
@Service
public class BankComYieldSpreadServiceImpl extends AbstractComYieldSpreadService implements BankComYieldSpreadService {

    private final ExecutorService executorService;

    protected BankComYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("BankComYieldSpreadServiceImpl-pool-").build());
    }

    @Resource
    private BankComYieldSpreadDAO bankComYieldSpreadDAO;

    @Resource
    private PgBankBondYieldSpreadDAO pgBankBondYieldSpreadDAO;

    @Resource
    private BondFinanceService bondFinanceService;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private FinancialInstitutionService financialInstitutionService;

    @Resource
    private BankComYieldSpreadViewDAO bankComYieldSpreadViewDAO;

    @Resource
    private BankComYieldSpreadRedisDAO bankComYieldSpreadRedisDAO;

    @Override
    public Integer calcBankComYieldSpreadsBySpreadDate(List<BankComYieldSpreadDO> comYieldSpreadDOs, Date spreadDate) {
        if (CollectionUtils.isEmpty(comYieldSpreadDOs)) {
            return 0;
        }
        Set<Long> comUniCodes = comYieldSpreadDOs.stream().map(BankComYieldSpreadDO::getComUniCode).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        CompletableFuture<Map<Long, ComCurrentBondsBalanceDTO>> submitComCurrentBondsBalanceDTO =
                of.submit(() -> bondInfoService.getComCurrentBondsBalanceDTOMap(comUniCodes));
        CompletableFuture<Map<Long, ComFinanceSheetResponseDTO>> submitComFinanceSheetResponseDTO =
                of.submit(() -> bondFinanceService.getComFinanceLatestYearReportMap(spreadDate, comUniCodes));
        CompletableFuture<Map<Long, BankFinIndicatorResponseDTO>> submitBankFinancialIndicatorDTO =
                of.submit(() -> financialInstitutionService.getBankFinancialIndicatorMap(comUniCodes, spreadDate));

        // 计算中位数(全部)
        CompletableFuture<Map<Long, PgBankBondYieldSpreadGroupDO>> submitYieldSpread = of.submit(() ->
                pgBankBondYieldSpreadDAO.getBankBondYieldSpreadMap(comUniCodes, null, spreadDate));
        // 计算中位数(普通)
        CompletableFuture<Map<Long, PgBankBondYieldSpreadGroupDO>> submitYieldSpreadSenior =
                of.submit(() -> pgBankBondYieldSpreadDAO.getBankBondYieldSpreadMap(comUniCodes, NORMAL.getValue(), spreadDate));
        // 计算中位数(二级资本债)
        CompletableFuture<Map<Long, PgBankBondYieldSpreadGroupDO>> submitYieldSpreadTier2CapitalBond =
                of.submit(() -> pgBankBondYieldSpreadDAO.getBankBondYieldSpreadMap(comUniCodes, TIER_2_BOND.getValue(), spreadDate));
        // 计算中位数(永续)
        CompletableFuture<Map<Long, PgBankBondYieldSpreadGroupDO>> submitYieldSpreadPerpetual =
                of.submit(() -> pgBankBondYieldSpreadDAO.getBankBondYieldSpreadMap(comUniCodes, PERPETUA.getValue(), spreadDate));
        of.doWorks(submitComCurrentBondsBalanceDTO, submitComFinanceSheetResponseDTO, submitBankFinancialIndicatorDTO, submitYieldSpread, submitYieldSpreadSenior,
                submitYieldSpreadTier2CapitalBond, submitYieldSpreadPerpetual);
        // 获取主体财报
        Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap = submitComFinanceSheetResponseDTO.join();
        //获取采集数据
        Map<Long, BankFinIndicatorResponseDTO> financialIndicatorResponseDTOMap = submitBankFinancialIndicatorDTO.join();
        // 获取估值中位数(全部、普通、次级、永续)
        Map<Long, PgBankBondYieldSpreadGroupDO> yieldSpreadMap = submitYieldSpread.join();
        Map<Long, PgBankBondYieldSpreadGroupDO> yieldSpreadSeniorMap = submitYieldSpreadSenior.join();
        Map<Long, PgBankBondYieldSpreadGroupDO> yieldSpreadTier2CapitalBondMap = submitYieldSpreadTier2CapitalBond.join();
        Map<Long, PgBankBondYieldSpreadGroupDO> yieldSpreadPerpetualMap = submitYieldSpreadPerpetual.join();

        List<BankComYieldSpreadDO> comYieldSpreadDOSaves = new ArrayList<>();
        for (BankComYieldSpreadDO comYieldSpreadDO : comYieldSpreadDOs) {
            Long comUniCode = comYieldSpreadDO.getComUniCode();
            comYieldSpreadDO = fillFinanceColumn(comYieldSpreadDO, comFinanceSheetResponseDTOMap.get(comUniCode));
            comYieldSpreadDO = fillFinancialIndicatorColumn(comYieldSpreadDO, financialIndicatorResponseDTOMap.get(comUniCode));
            comYieldSpreadDO = fillComSpreadColumn(comYieldSpreadDO, yieldSpreadMap.get(comUniCode), yieldSpreadSeniorMap.get(comUniCode),
                    yieldSpreadTier2CapitalBondMap.get(comUniCode), yieldSpreadPerpetualMap.get(comUniCode));
            comYieldSpreadDO.setDeleted(0);
            comYieldSpreadDOSaves.add(comYieldSpreadDO);
        }
        return bankComYieldSpreadDAO.saveBankComYieldSpreadDOList(spreadDate, comYieldSpreadDOSaves);
    }

    @Override
    public List<BankComYieldSpreadResDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        BankCurveGenerateConditionReqDTO curveGenerateCondition = super.getCurveGenerateCondition(
                userid, request.getCurveId(), BankCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), curveGenerateCondition.getComOrBondCondition())) {
            return Collections.emptyList();
        }
        boolean isToday = super.isToday(request.getSpreadDate());
        BankYieldSearchParam param = super.buildComYieldSearchParam(request, curveGenerateCondition, BankYieldSearchParam.class, BankComYieldSpreadDO.class);
        List<BankComYieldSpreadBO> bankComYieldSpreads = bankComYieldSpreadDAO.listComYieldSpreads(isToday, param);
        if (CollectionUtils.isEmpty(bankComYieldSpreads)) {
            return Collections.emptyList();
        }
        Set<Long> comUniCodes = bankComYieldSpreads.stream().map(BankComYieldSpreadBO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return bankComYieldSpreads.stream().map(com -> {
            BankComYieldSpreadResDTO response = BeanCopyUtils.copyProperties(com, BankComYieldSpreadResDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRatingMappingStr(RatingUtils.getRating(response.getComExtRatingMapping()));
            EnumUtils.getEnumByValue(response.getBankType(), BankTypeEnum.class).ifPresent(v -> response.setBankTypeStr(v.getText()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getNetProfit()).ifPresent(response::setNetProfit);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request) {
        BankCurveGenerateConditionReqDTO curveGenerateCondition = super.getCurveGenerateCondition(
                userid, request.getCurveId(), BankCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), curveGenerateCondition.getComOrBondCondition())) {
            return 0L;
        }
        return super.getComCountFromRedis(request, SPREAD_BANK_COM_SPREAD_COUNT_KEY, () -> {
            BankYieldSearchParam param = super.buildComYieldSearchParam(request, curveGenerateCondition, BankYieldSearchParam.class, null);
            return bankComYieldSpreadDAO.countComYieldSpread(param);
        });
    }

    @Override
    public List<BankComYieldSpreadResDTO> listComs(Date spreadDate, Set<Long> bankComs) {
        if (org.springframework.util.CollectionUtils.isEmpty(bankComs)) {
            return Collections.emptyList();
        }
        // 今天的话要加上变动数据
        List<BankComYieldSpreadResDTO> bankResponseList;
        if (super.isToday(spreadDate)) {
            spreadDate = this.getMaxSpreadDate();
            List<BankComYieldSpreadDynamicView> bankComYieldSpreads = bankComYieldSpreadViewDAO.listComYieldSpreads(spreadDate, bankComs);
            bankResponseList = BeanCopyUtils.copyList(bankComYieldSpreads, BankComYieldSpreadResDTO.class);
        } else {
            List<BankComYieldSpreadDO> bankComYieldSpreads = bankComYieldSpreadDAO.listComYieldSpreads(spreadDate, bankComs);
            bankResponseList = BeanCopyUtils.copyList(bankComYieldSpreads, BankComYieldSpreadResDTO.class);
        }
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(bankComs);
        return bankResponseList.stream().map(com -> {
            BankComYieldSpreadResDTO response = BeanCopyUtils.copyProperties(com, BankComYieldSpreadResDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRatingMappingStr(RatingUtils.getRating(response.getComExtRatingMapping()));
            EnumUtils.getEnumByValue(response.getBankType(), BankTypeEnum.class).ifPresent(v -> response.setBankTypeStr(v.getText()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getNetProfit()).ifPresent(response::setNetProfit);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer bankSeniorityRanking, Date startDate, Date endDate) {
        return bankComYieldSpreadRedisDAO.listCurves(comUniCode, bankSeniorityRanking, startDate, endDate);
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.BANK;
    }

    @Override
    public Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_BANK_COM_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = bankComYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_BANK_COM_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }

    private BankComYieldSpreadDO fillFinanceColumn(BankComYieldSpreadDO bankComYieldSpreadDO, ComFinanceSheetResponseDTO comFinanceSheetResponseDTO) {
        BankComYieldSpreadDO result = BeanCopyUtils.copyProperties(bankComYieldSpreadDO, BankComYieldSpreadDO.class);
        Optional.ofNullable(comFinanceSheetResponseDTO).ifPresent(dto -> {
            result.setTotalAssets(dto.getTotalAssets());
            result.setNetProfit(dto.getNetProfit());
        });
        return result;
    }

    private BankComYieldSpreadDO fillFinancialIndicatorColumn(BankComYieldSpreadDO bankComYieldSpreadDO,
                                                              BankFinIndicatorResponseDTO financialIndicatorResponseDTO) {
        BankComYieldSpreadDO result = BeanCopyUtils.copyProperties(bankComYieldSpreadDO, BankComYieldSpreadDO.class);
        Optional.ofNullable(financialIndicatorResponseDTO)
                .ifPresent(dto -> {
                    result.setNplRatio(dto.getNplRatio());
                    result.setCar(dto.getCar());
                    result.setLeverageRatio(dto.getLeverageRatio());
                    result.setLoanProvisRatio(dto.getLoanProvRatio());
                });
        return result;
    }

    private BankComYieldSpreadDO fillComSpreadColumn(BankComYieldSpreadDO bankComYieldSpreadDO,
                                                     PgBankBondYieldSpreadGroupDO yieldSpreadDTO,
                                                     PgBankBondYieldSpreadGroupDO yieldSpreadSeniorDTO,
                                                     PgBankBondYieldSpreadGroupDO yieldSpreadTier2CapitalBondDTO,
                                                     PgBankBondYieldSpreadGroupDO yieldSpreadPerpetualDTO) {
        BankComYieldSpreadDO result = BeanCopyUtils.copyProperties(bankComYieldSpreadDO, BankComYieldSpreadDO.class);
        Optional.ofNullable(yieldSpreadDTO).ifPresent(dto -> {
            result.setComCbYield(dto.getCbYield());
            result.setComCreditSpread(dto.getBondCreditSpread());
            result.setComExcessSpread(dto.getBondExcessSpread());
        });
        Optional.ofNullable(yieldSpreadSeniorDTO).ifPresent(dto -> {
            result.setComSeniorCbYield(dto.getCbYield());
            result.setComSeniorCreditSpread(dto.getBondCreditSpread());
            result.setComSeniorExcessSpread(dto.getBondExcessSpread());
        });
        Optional.ofNullable(yieldSpreadTier2CapitalBondDTO).ifPresent(dto -> {
            result.setComTier2CbYield(dto.getCbYield());
            result.setComTier2CreditSpread(dto.getBondCreditSpread());
            result.setComTier2ExcessSpread(dto.getBondExcessSpread());
        });
        Optional.ofNullable(yieldSpreadPerpetualDTO).ifPresent(dto -> {
            result.setComPerpetualCbYield(dto.getCbYield());
            result.setComPerpetualCreditSpread(dto.getBondCreditSpread());
            result.setComPerpetualExcessSpread(dto.getBondExcessSpread());
        });
        return result;
    }


    @Override
    protected List<MixYieldSpreadShortBO> listAllYieldSpreads(@NonNull List<Long> comUniCodes) {
        return bankComYieldSpreadDAO.listAllYieldSpreads(comUniCodes);
    }
}

