package com.innodealing.onshore.yieldspread.model.dto;

import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.enums.SpreadCodeTypeEnum;

import java.util.Objects;

/**
 * 通用的利差查询参数
 *
 * <AUTHOR>
 */
public class UniversalYieldSpreadSearchParam extends AbstractShardParamDTO {

    private Integer spreadBondType;

    /**
     * 主体唯一编码
     */
    private Long comUniCode;

    /**
     * 债券唯一编码
     */
    private Long bondUniCode;

    /**
     * 剩余期限
     */
    private Integer remainingTenor;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页数据量
     */
    private Integer pageSize;

    /**
     * 分页查询开始索引
     */
    private Integer startIndex;

    private SortDTO sort;

    /**
     * 查哪一年的数据，也就是哪张表
     */
    private Integer year;

    /**
     * 设置主体或债券code
     *
     * @param spreadCodeType 主体/债券
     * @param uniCode        code
     */
    public void setComOrBondUniCode(Integer spreadCodeType, Long uniCode) {
        if (Objects.isNull(spreadCodeType) || Objects.isNull(uniCode)) {
            return;
        }
        boolean isCom = EnumUtils.getEnum(SpreadCodeTypeEnum.class, spreadCodeType) == SpreadCodeTypeEnum.COM_CODE;
        if (isCom) {
            this.setComUniCode(uniCode);
        } else {
            this.setBondUniCode(uniCode);
        }
    }

    public Integer getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(Integer startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }

    public Integer getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(Integer remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

}
