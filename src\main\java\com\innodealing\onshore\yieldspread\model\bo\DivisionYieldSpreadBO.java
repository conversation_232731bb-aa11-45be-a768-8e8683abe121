package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;

/**
 * 行政级别对应的利差
 *
 * <AUTHOR>
 */
public class DivisionYieldSpreadBO {

    /**
     * 全平台债券信用利差
     */
    private BigDecimal bondCreditSpread;

    /**
     * 省级债券信用利差
     */
    private BigDecimal provinceBondCreditSpread;

    /**
     * 市级债券信用利差
     */
    private BigDecimal cityBondCreditSpread;

    /**
     * 区县级债券信用利差
     */
    private BigDecimal districtBondCreditSpread;

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getProvinceBondCreditSpread() {
        return provinceBondCreditSpread;
    }

    public void setProvinceBondCreditSpread(BigDecimal provinceBondCreditSpread) {
        this.provinceBondCreditSpread = provinceBondCreditSpread;
    }

    public BigDecimal getCityBondCreditSpread() {
        return cityBondCreditSpread;
    }

    public void setCityBondCreditSpread(BigDecimal cityBondCreditSpread) {
        this.cityBondCreditSpread = cityBondCreditSpread;
    }

    public BigDecimal getDistrictBondCreditSpread() {
        return districtBondCreditSpread;
    }

    public void setDistrictBondCreditSpread(BigDecimal districtBondCreditSpread) {
        this.districtBondCreditSpread = districtBondCreditSpread;
    }

}
