package com.innodealing.onshore.yieldspread.dao.dmdc;

import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.innodealing.onshore.yieldspread.mapper.dmdc.ComInterestCtzHistMapper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.ComInterestCtzHistDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.group.ComInterestCtzHistGroupDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;
import java.util.Set;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 城投主体利差(老表) DAO
 *
 * <AUTHOR>
 **/
@Repository
public class ComInterestCtzHistDAO {

    @Resource
    private ComInterestCtzHistMapper comInterestCtzHistMapper;

    /**
     * 城投主体数据
     *
     * @param interestDate 利差日期
     * @param comUniCodes  主体code
     * @return 城投主体数据
     */
    public List<ComInterestCtzHistDO> listComInterestCtzHistDOByInterestDate(Date interestDate, Set<Long> comUniCodes) {
        GroupedQuery<ComInterestCtzHistGroupDO, ComInterestCtzHistDO> groupedQuery =
                GroupByQuery.createQuery(ComInterestCtzHistGroupDO.class, ComInterestCtzHistDO.class)
                        .select(ComInterestCtzHistDO::getId, ComInterestCtzHistDO::getComUniCode,
                                ComInterestCtzHistDO::getInterestDate, ComInterestCtzHistDO::getAreaUniCode1,
                                ComInterestCtzHistDO::getAreaName1, ComInterestCtzHistDO::getAreaUniCode2,
                                ComInterestCtzHistDO::getAreaName2, ComInterestCtzHistDO::getAreaLevelId,
                                ComInterestCtzHistDO::getRealCtrlName, ComInterestCtzHistDO::getAreaName)
                        .and(ComInterestCtzHistGroupDO::getInterestDate, isEqual(interestDate))
                        .and(CollectionUtils.isNotEmpty(comUniCodes), ComInterestCtzHistGroupDO::getComUniCode, in(comUniCodes))
                        .groupBy(ComInterestCtzHistGroupDO::getComUniCode, ComInterestCtzHistGroupDO::getInterestDate);
        return comInterestCtzHistMapper.selectByGroupedQuery(groupedQuery);
    }
}
