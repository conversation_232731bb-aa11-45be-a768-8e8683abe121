package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.InternalKeyValueRequestDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDate;
import java.util.Optional;

/**
 * 内部 key value 键值对
 *
 * <AUTHOR>
 **/
@FeignClient(name = "internalKeyValueService", url = "${bond.basic.api.url}", path = "/internal/keyValue")
public interface InternalKeyValueService {

    /**
     * 保存key value
     *
     * @param internalKeyValueRequestDTO 保存key value 键值对
     * @return 影响行数
     */
    @PostMapping
    Integer saveKeyValue(@RequestBody InternalKeyValueRequestDTO internalKeyValueRequestDTO);

    /**
     * 获取 value
     *
     * @param key 键
     * @return value
     */
    @GetMapping("{key}")
    String getValue(@PathVariable String key);

    /**
     * 获取 value
     *
     * @param key 键
     * @return value
     */
    default Optional<Long> getLongValue(String key) {
        String value = this.getValue(key);
        if (StringUtils.isBlank(value)) {
            return Optional.empty();
        }
        return Optional.of(Long.valueOf(value));
    }

    /**
     * 获取 value
     *
     * @param key 键
     * @return value
     */
    default Optional<LocalDate> getLocalDateValue(String key) {
        String value = this.getValue(key);
        if (StringUtils.isBlank(value)) {
            return Optional.empty();
        }
        return Optional.of(LocalDate.parse(value));
    }

}
