package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgInduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadCbYieldGroupDO;

/**
 * pg indu债券利差计算中债收益率中位数Mapper
 *
 * <AUTHOR>
 */
public interface PgInduBondYieldSpreadCbYieldGroupMapper extends SelectByGroupedQueryMapper<PgInduBondYieldSpreadDO,
        PgInduBondYieldSpreadCbYieldGroupDO> {
}
