package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.model.dto.request.CustomCurveGenerateReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondAnalysisResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CustomCurveBasicInfoResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CustomSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.GenerateDetailResDTO;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 自定义曲线
 *
 * <AUTHOR>
 */
public interface CustomCurveService {

    /**
     * 曲线名称是否重复
     *
     * @param userid    用户id
     * @param curveName 曲线名称
     * @return 是/否
     */
    boolean curveNameIsDuplicate(Long userid, String curveName);

    /**
     * 保存自定义曲线
     *
     * @param userid                    用户id
     * @param curveGroupId              曲线组id
     * @param customCurveGenerateReqDTO 自定义曲线保存数据
     * @return 操作结果
     */
    Boolean saveCurve(Long userid, Long curveGroupId, CustomCurveGenerateReqDTO customCurveGenerateReqDTO);

    /**
     * 获取自定义曲线列表
     *
     * @param userid         用户id
     * @param generateStatus 生成状态
     * @return 自定义曲线列表
     */
    List<CustomCurveBasicInfoResDTO> listCustomCurves(Long userid, Set<Integer> generateStatus);

    /**
     * 重新生成自定义曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @return 执行结果
     */
    boolean regenerate(Long userid, Long curveId);

    /**
     * 取消生成曲线 -直接逻辑删除
     *
     * @param userid  用户的id
     * @param curveId 曲线id
     * @return 执行结果
     */
    boolean cancelGenerate(Long userid, Long curveId);

    /**
     * 更新自定义曲线
     *
     * @param userid         用户id
     * @param curveId        曲线id
     * @param generateReqDTO 自定义曲线更新数据
     * @return 执行结果
     */
    boolean update(Long userid, Long curveId, CustomCurveGenerateReqDTO generateReqDTO);

    /**
     * 生成曲线
     *
     * @param curveId 曲线id
     * @throws InterruptedException 被打断异常
     */
    void generateCurve(Long curveId) throws InterruptedException;

    /**
     * 解析债券
     *
     * @param bondNameOrCodeList 债券名字或code
     * @return 债券简单信息
     */
    BondAnalysisResDTO analysisBonds(Set<String> bondNameOrCodeList);

    /**
     * 查询自定义曲线单券利差
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 单券利差
     */
    List<CustomSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 自定义曲线单券利差条数
     *
     * @param userid  用户id
     * @param request 请求参数
     * @return 自定义曲线单券利差条数
     */
    Long countSingleBondYieldSpread(Long userid, YieldSpreadSearchReqDTO request);

    /**
     * 生成情况
     *
     * @return 生成情况
     */
    GenerateDetailResDTO generateDetail();

    /**
     * 加减令牌数
     *
     * @param permitCount 令牌数
     */
    void addPermits(Integer permitCount);

    /**
     * 设置总令牌数
     *
     * @param permitCount 令牌数
     */
    void setPermits(Integer permitCount);

    /**
     * 生成昨日曲线数据
     */
    void generateYesterdayCurveData();

    /**
     * 生成曲线数据
     *
     * @param spreadDate 日期
     */
    void generateCurveData(Date spreadDate);

}
