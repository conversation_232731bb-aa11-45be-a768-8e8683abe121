package com.innodealing.onshore.yieldspread.mapper.pgsharding.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.mv.MvBankShardBondYieldSpreadCurveDO;

import javax.persistence.Table;

/**
 * 银行利差曲线分片查询
 *
 * <AUTHOR>
 */

public interface MvBankShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<MvBankShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     * @return tableName
     */
    default String getLogicTable() {
        return MvBankShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }


}
