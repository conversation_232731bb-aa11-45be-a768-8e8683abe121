package com.innodealing.onshore.yieldspread.service.impl;

import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.helper.CommonsHelper;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationShortInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondImpliedRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComYyRatingDTO;
import com.innodealing.onshore.bondmetadata.enums.BusinessNatureEnum;
import com.innodealing.onshore.bondmetadata.enums.EmbeddedOption;
import com.innodealing.onshore.bondmetadata.enums.ImplicitRatingTagEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgSecuBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvSecuBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition.PgSecuBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.SecuBondYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.SecuBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SecuritySeniorityRankingEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.CommonUtils;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.SecuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveDataResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.SecuSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgSecuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.EmptyRouter;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import com.innodealing.onshore.yieldspread.service.RedisService;
import com.innodealing.onshore.yieldspread.service.SecuBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.internal.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.CACHE_ONE_DAYS;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.MAX_SECU_BOND_SPREAD_DATE_KEY;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.*;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.splitDateRange;

/**
 * 证券债利差 Service
 *
 * <AUTHOR>
 **/
@SuppressWarnings({"squid:S1854", "squid:S1481", "java:S107", "squid:S00107"})
@Service
public class SecuBondYieldSpreadServiceImpl extends AbstractBondCurveService implements SecuBondYieldSpreadService {

    private final ExecutorService executorService;

    private static final SortDTO DEFAULT_SORT = new SortDTO(CommonsHelper.getPropertyName(SecuBondYieldSpreadDO::getBondCreditSpread), SortDirection.DESC);

    protected SecuBondYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("SecuBondYieldSpreadServiceImpl-pool-").build());
    }

    @Resource
    private ComService comService;

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private BondRatingService bondRatingService;

    @Resource
    private SecuBondYieldSpreadDAO secuBondYieldSpreadDAO;

    @Resource
    private PgSecuBondYieldSpreadDAO pgSecuBondYieldSpreadDAO;

    @Resource
    private RedisService redisService;

    @Value("${sharding.yield.spread}")
    private Date initStartDate;

    @Resource
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;

    @Resource
    private MvSecuBondYieldSpreadCurveDAO mvSecuBondYieldSpreadCurveDAO;

    @Resource
    private PgSecuBondYieldSpreadCurveDAO pgSecuBondYieldSpreadCurveDAO;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private SecuBondYieldSpreadRedisDAO secuBondYieldSpreadRedisDAO;

    @Resource
    private UserService userService;

    public static final String YIELD_SPREAD_SECU_BOND_YIELD_SPREAD_FLOW_ID = "yieldSpread:secuBondYieldSpreadFlowId";

    /**
     * 检查分片表
     */
    //@Scheduled(initialDelay = 1000, fixedDelay = 3_600_000)
    public void checkShardingTables() {
        Date currentDate = new Date(System.currentTimeMillis());
        Date endDate = new Date(DateUtils.addDays(currentDate, 1).getTime());
        createShardingTables(initStartDate, endDate);
    }

    private void createShardingTables(Date startDate, Date endDate) {
        Collection<String> shardingTableNames = ShardingUtils.getYearShardingTableNames(SECU_TABLE_NAME, startDate, endDate);
        for (String shardingTableName : shardingTableNames) {
            secuBondYieldSpreadDAO.createShardingTable(shardingTableName);
        }
    }

    @Override
    public int calcSecuBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs, Map<Integer, BondYieldCurveDTO> bondYieldCurveMap, Date spreadDate) {
        if (CollectionUtils.isEmpty(onshoreBondInfoDTOs)) {
            return 0;
        }
        Set<Long> bondUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getBondUniCode).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> comUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getComUniCode).filter(Objects::nonNull).collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        CompletableFuture<Map<Long, ComShortInfoDTO>> submitComShortInfoDTO = of.submit(() -> comService.getComShortInfoByUniCodeMap(comUniCodes));
        CompletableFuture<Map<Long, CbValuationShortInfoResponseDTO>> submitCbValuationShortInfo =
                of.submit(() -> bondPriceService.getCbValuationShortInfoMap(spreadDate, bondUniCodes));
        CompletableFuture<Map<Long, BondImpliedRatingDTO>> submitBondImpliedRatingDTO =
                of.submit(() -> bondRatingService.getBondImpliedRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, BondExternalCreditRatingDTO>> submitBondExternalCreditRatingDTO =
                of.submit(() -> bondRatingService.getBondExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, ComYyRatingDTO>> submitComYyRatingDTO =
                of.submit(() -> bondRatingService.getComYyRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        CompletableFuture<Map<Long, ComExternalCreditRatingDTO>> submitComExternalCreditRatingDTO =
                of.submit(() -> bondRatingService.getComExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        of.doWorks(submitComShortInfoDTO, submitCbValuationShortInfo, submitBondImpliedRatingDTO,
                submitBondExternalCreditRatingDTO, submitComYyRatingDTO, submitComExternalCreditRatingDTO);
        // 获取主体信息
        Map<Long, ComShortInfoDTO> comShortInfoDTOMap = submitComShortInfoDTO.join();
        // 获取中债估值信息
        Map<Long, CbValuationShortInfoResponseDTO> cbMap = submitCbValuationShortInfo.join();
        // 获取评级信息
        Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap = submitBondImpliedRatingDTO.join();
        Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap = submitBondExternalCreditRatingDTO.join();
        Map<Long, ComYyRatingDTO> comYyRatingMap = submitComYyRatingDTO.join();
        Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap = submitComExternalCreditRatingDTO.join();
        return parseSecuBondYieldSpreadDO(onshoreBondInfoDTOs, comShortInfoDTOMap, cbMap, bondImpliedRatingMap, bondExternalCreditRatingMap,
                comExternalCreditRatingMap, comYyRatingMap, bondYieldCurveMap, spreadDate);
    }

    private int parseSecuBondYieldSpreadDO(List<OnshoreBondInfoDTO> onshoreBondInfos, Map<Long, ComShortInfoDTO> comShortInfoMap, Map<Long, CbValuationShortInfoResponseDTO> cbMap,
                                           Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap, Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                           Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap, Map<Long, ComYyRatingDTO> comYyRatingMap,
                                           Map<Integer, BondYieldCurveDTO> bondYieldCurveMap, Date spreadDate) {
        List<SecuBondYieldSpreadDO> secuBondYieldSpreadDOs = new ArrayList<>();
        List<YieldSpreadBondDO> yieldSpreadBonds = Lists.newArrayList();
        for (OnshoreBondInfoDTO onshoreBondInfoDTO : onshoreBondInfos) {
            SecuBondYieldSpreadDO secuBondYieldSpreadDO = BeanCopyUtils.copyProperties(onshoreBondInfoDTO, SecuBondYieldSpreadDO.class);
            secuBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getPublicOffering());
            secuBondYieldSpreadDO.setSpreadDate(spreadDate);
            if (!DateExtensionUtils.isSameDay(spreadDate, YieldSpreadHelper.getYesterDay())) {
                secuBondYieldSpreadDO.setLatestCouponRate(null);
                secuBondYieldSpreadDO.setBondBalance(null);
            }
            if (Objects.equals(onshoreBondInfoDTO.getEmbeddedOption(), EmbeddedOption.PERPETUAL.getValue())) {
                secuBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getEmbeddedOption());
            }
            // 利差剩余期限标签
            secuBondYieldSpreadDO.setSpreadRemainingTenorTag(YieldSpreadHelper.getSpreadRemainingTenorTag(secuBondYieldSpreadDO.getRemainingTenorDay()));
            secuBondYieldSpreadDO = fillComInfoColumn(secuBondYieldSpreadDO, comShortInfoMap);
            secuBondYieldSpreadDO = fillRatingColumn(secuBondYieldSpreadDO, bondImpliedRatingMap, bondExternalCreditRatingMap, comExternalCreditRatingMap, comYyRatingMap);
            secuBondYieldSpreadDO = fillCbColumn(secuBondYieldSpreadDO, cbMap);
            secuBondYieldSpreadDO = fillLerpYieldColumn(secuBondYieldSpreadDO, bondYieldCurveMap);
            secuBondYieldSpreadDO = fillSpreadColumn(secuBondYieldSpreadDO, bondYieldCurveMap);
            //这里如果超额利差和信用利差都为空的情况下 过滤掉
            if (Objects.nonNull(secuBondYieldSpreadDO.getBondCreditSpread()) || Objects.nonNull(secuBondYieldSpreadDO.getBondExcessSpread())) {
                secuBondYieldSpreadDO.setId(redisService.generatePk(YIELD_SPREAD_SECU_BOND_YIELD_SPREAD_FLOW_ID, secuBondYieldSpreadDO.getSpreadDate()));
                secuBondYieldSpreadDO.setDeleted(0);
                secuBondYieldSpreadDOs.add(secuBondYieldSpreadDO);
                YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(secuBondYieldSpreadDO, YieldSpreadBondDO.class);
                yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.SECU.getValue());
                yieldSpreadBonds.add(yieldSpreadBondDO);
            }
        }
        secuBondYieldSpreadDAO.saveSecuBondYieldSpreadDOList(spreadDate, spreadDate, secuBondYieldSpreadDOs);
        super.saveYieldSpreadBonds(yieldSpreadBonds);
        return savePgSecuBondYieldSpreadDOLists(spreadDate, secuBondYieldSpreadDOs);
    }

    private int savePgSecuBondYieldSpreadDOLists(Date spreadDate, List<SecuBondYieldSpreadDO> secuBondYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(secuBondYieldSpreadDOs)) {
            return 0;
        }
        List<PgSecuBondYieldSpreadDO> pgSecuBondYieldSpreadDOs = secuBondYieldSpreadDOs.stream().map(x -> {
            PgSecuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(x, PgSecuBondYieldSpreadDO.class);
            if (Objects.isNull(result.getGuaranteedStatus())) {
                // gp表的担保字段 null 转换为0
                result.setGuaranteedStatus(0);
            }
            return result;
        }).collect(Collectors.toList());
        return pgSecuBondYieldSpreadDAO.savePgSecuBondYieldSpreadDOList(spreadDate, pgSecuBondYieldSpreadDOs);

    }

    /**
     * 填充发行人信息
     *
     * @param secuBondYieldSpreadDO 产业债利差
     * @param comShortInfoMap       主体信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO
     * <AUTHOR>
     */
    private SecuBondYieldSpreadDO fillComInfoColumn(SecuBondYieldSpreadDO secuBondYieldSpreadDO, Map<Long, ComShortInfoDTO> comShortInfoMap) {
        SecuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(secuBondYieldSpreadDO, SecuBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comShortInfoMap)) {
            return result;
        }
        ComShortInfoDTO comShortInfoDTO = comShortInfoMap.get(result.getComUniCode());
        if (Objects.nonNull(comShortInfoDTO)) {
            //主体基础信息
            result.setBusinessNature(comShortInfoDTO.getBusinessNature());
            result.setInduLevel1Code(comShortInfoDTO.getInduLevel1Code());
            result.setInduLevel1Name(comShortInfoDTO.getInduLevel1Name());
            result.setInduLevel2Code(comShortInfoDTO.getInduLevel2Code());
            result.setInduLevel2Name(comShortInfoDTO.getInduLevel2Name());
        }
        return result;
    }

    /**
     * 填充中债估值相关信息
     *
     * @param secuBondYieldSpreadDO 产业债利差
     * @param cbMap                 中债估值
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO
     * <AUTHOR>
     */
    private SecuBondYieldSpreadDO fillCbColumn(SecuBondYieldSpreadDO secuBondYieldSpreadDO, Map<Long, CbValuationShortInfoResponseDTO> cbMap) {
        SecuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(secuBondYieldSpreadDO, SecuBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(cbMap)) {
            return result;
        }
        CbValuationShortInfoResponseDTO cb = cbMap.get(secuBondYieldSpreadDO.getBondUniCode());
        if (Objects.nonNull(cb)) {
            result.setCbYield(cb.getYield());
        }
        return result;
    }

    /**
     * 填充评级相关信息
     *
     * @param secuBondYieldSpreadDO       产业债利差
     * @param bondImpliedRatingMap        债券隐含评级
     * @param bondExternalCreditRatingMap 主体外部评级
     * @param comExternalCreditRatingMap  主体YY评级
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO
     * <AUTHOR>
     */
    private SecuBondYieldSpreadDO fillRatingColumn(SecuBondYieldSpreadDO secuBondYieldSpreadDO, Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap,
                                                   Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                                   Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap, Map<Long, ComYyRatingDTO> comYyRatingMap) {
        SecuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(secuBondYieldSpreadDO, SecuBondYieldSpreadDO.class);
        if (ObjectUtils.isNotEmpty(bondImpliedRatingMap)) {
            BondImpliedRatingDTO bondImpliedRatingDTO = bondImpliedRatingMap.get(secuBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondImpliedRatingDTO)) {
                result.setBondImpliedRatingMapping(bondImpliedRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(bondExternalCreditRatingMap)) {
            BondExternalCreditRatingDTO bondExternalCreditRatingDTO = bondExternalCreditRatingMap.get(secuBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondExternalCreditRatingDTO)) {
                result.setBondExtRatingMapping(bondExternalCreditRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(comExternalCreditRatingMap)) {
            ComExternalCreditRatingDTO comExternalCreditRatingDTO = comExternalCreditRatingMap.get(secuBondYieldSpreadDO.getComUniCode());
            if (Objects.nonNull(comExternalCreditRatingDTO)) {
                result.setComExtRatingMapping(comExternalCreditRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(comYyRatingMap)) {
            ComYyRatingDTO comYyRatingDTO = comYyRatingMap.get(secuBondYieldSpreadDO.getComUniCode());
            if (Objects.nonNull(comYyRatingDTO)) {
                result.setComYyRatingMapping(comYyRatingDTO.getRatingMapping());
            }
        }
        return result;
    }

    /**
     * 填充插值收益率
     *
     * @param secuBondYieldSpreadDO 产业债利差
     * @param bondYieldCurveMap     收益率曲线信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO
     * <AUTHOR>
     */
    private SecuBondYieldSpreadDO fillLerpYieldColumn(SecuBondYieldSpreadDO secuBondYieldSpreadDO, Map<Integer, BondYieldCurveDTO> bondYieldCurveMap) {
        SecuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(secuBondYieldSpreadDO, SecuBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(bondYieldCurveMap)) {
            return result;
        }
        //国开插值收益率;单位(%)
        BondYieldCurveDTO bondYieldCurveDTO = bondYieldCurveMap.get(YieldSpreadHelper.CDB_YIELD_CURVE);
        if (Objects.nonNull(bondYieldCurveDTO)) {
            BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(bondYieldCurveDTO, BondYieldCurveBO.class);
            result.setCdbLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
        }
        //隐含评级对应曲线插值收益率;单位(%)
        if (Objects.nonNull(result.getBondImpliedRatingMapping())) {
            BondYieldCurveDTO impliedRatingLerpYieldCurveDTO = bondYieldCurveMap.get(YieldSpreadHelper.getBondYieldCurveSecuMap().get(result.getBondImpliedRatingMapping()));
            if (Objects.nonNull(impliedRatingLerpYieldCurveDTO)) {
                BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(impliedRatingLerpYieldCurveDTO, BondYieldCurveBO.class);
                result.setImpliedRatingLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
            }
        }
        return result;
    }

    /**
     * 填充利差数据
     *
     * @param secuBondYieldSpreadDO 产业债利差
     * @param bondYieldCurveMap     收益率曲线信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO
     * <AUTHOR>
     */
    private SecuBondYieldSpreadDO fillSpreadColumn(SecuBondYieldSpreadDO secuBondYieldSpreadDO, Map<Integer, BondYieldCurveDTO> bondYieldCurveMap) {
        SecuBondYieldSpreadDO result = BeanCopyUtils.copyProperties(secuBondYieldSpreadDO, SecuBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(bondYieldCurveMap)) {
            return result;
        }
        if (Objects.nonNull(result.getCbYield())) {
            if (Objects.nonNull(result.getCdbLerpYield()) && result.getCdbLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondCreditSpread(result.getCbYield().subtract(result.getCdbLerpYield()).multiply(YieldSpreadHelper.BP_WEIGHT)
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.nonNull(result.getImpliedRatingLerpYield()) && result.getImpliedRatingLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondExcessSpread(result.getCbYield().subtract(result.getImpliedRatingLerpYield()).multiply(YieldSpreadHelper.BP_WEIGHT)
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                result.setExcessSpreadStatus(0);
            } else {
                result.setExcessSpreadStatus(1);
            }
        }
        return result;
    }

    @Override
    public void refreshMvSecuBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh) {
        final List<AbstractRatingRouter.SpreadDateRange> dateRangeList = splitDateRange(LocalDate.parse("2019-06-01"), LocalDate.now().minusDays(1));
        for (AbstractRatingRouter.SpreadDateRange dateRange : dateRangeList) {
            final RefreshYieldCurveParam param = RefreshYieldCurveParam.builder().dateRange(dateRange).initRefresh(true, isTableRefresh).build();
            this.refreshMvSecuBondYieldSpreadBondAllCurve(param);
            this.refreshMvSecuBondYieldSpreadBondImpliedRatingMappingCurve(param);
        }
    }

    @Override
    public void refreshMvSecuBondYieldSpreadRatingCurve(RefreshYieldCurveParam param) {
        this.refreshMvSecuBondYieldSpreadBondAllCurve(param);
        this.refreshMvSecuBondYieldSpreadBondImpliedRatingMappingCurve(param);
    }

    @Override
    public void refreshMvSecuBondYieldSpreadBondAllCurve(RefreshYieldCurveParam param) {
        EmptyRouter router = new EmptyRouter();
        try {
            Optional.ofNullable(param)
                    .ifPresent(p -> router.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
            mvSecuBondYieldSpreadCurveDAO.createOrRefreshSecuCurveMv(router, param);
            pgSecuBondYieldSpreadCurveDAO.syncCurveShardSecuForMv(router, param);
        } finally {
            mvSecuBondYieldSpreadCurveDAO.droTempMv(router);
        }
    }

    private void refreshMvSecuBondYieldSpreadBondImpliedRatingMappingCurve(RefreshYieldCurveParam param) {
        Set<String> secuImplicitRatingCombination = RatingCombinationHelper.getSecuImplicitRatingCombination();
        Set<ImplicitRatingRouter> implicitRatingRouters = implicitRatingRouterFactory.newRatingRouterList(secuImplicitRatingCombination);
        for (ImplicitRatingRouter implicitRatingRouter : implicitRatingRouters) {
            try {
                Optional.ofNullable(param)
                        .ifPresent(p -> implicitRatingRouter.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
                mvSecuBondYieldSpreadCurveDAO.createOrRefreshSecuCurveMv(implicitRatingRouter, param);
                pgSecuBondYieldSpreadCurveDAO.syncCurveShardSecuForMv(implicitRatingRouter, param);
            } finally {
                mvSecuBondYieldSpreadCurveDAO.droTempMv(implicitRatingRouter);
            }
        }
    }

    @Override
    public boolean saveCurve(Long userid, Long curveGroupId, SecuCurveGenerateConditionReqDTO params) {
        return super.generalSaveCurve(userid, curveGroupId, params);
    }

    @Override
    public boolean updateCurve(Long userid, Long curveId, SecuCurveGenerateConditionReqDTO request) {
        return super.generalUpdateCurve(userid, curveId, request);
    }

    @Override
    public NormPagingResult<SecuSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        SecuCurveGenerateConditionReqDTO generateRequest = super.getCurveGenerateCondition(userid, request.getCurveId(), SecuCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return new NormPagingResult<>();
        }
        SecuYieldSearchParam param = super
                .buildBondYieldSearchParam(request, generateRequest, SecuYieldSearchParam.class, ObjectExtensionUtils.getOrDefault(request.getSort(), DEFAULT_SORT));
        NormPagingResult<SecuBondYieldSpreadDO> pagingResult = secuBondYieldSpreadDAO.listSecuSingleBondYieldSpreads(param);
        if (CollectionUtils.isEmpty(pagingResult.getList())) {
            return pagingResult.convert(bond -> BeanCopyUtils.copyProperties(bond, SecuSingleBondYieldSpreadResDTO.class));
        }
        Long[] bondUniCodes = pagingResult.getList().stream().map(SecuBondYieldSpreadDO::getBondUniCode).distinct().toArray(Long[]::new);
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        NormPagingResult<SecuSingleBondYieldSpreadResDTO> result = pagingResult.convert(bond -> {
            SecuSingleBondYieldSpreadResDTO response = BeanCopyUtils.copyProperties(bond, SecuSingleBondYieldSpreadResDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            response.setCdbLerpYield(CommonUtils.formatDecimal(bond.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            String ratingStr = RatingUtils.getRating(bond.getComExtRatingMapping()) + "/" + RatingUtils.getRating(bond.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            EnumUtils.getEnumByValue(bond.getBondImpliedRatingMapping(), ImplicitRatingTagEnum.class)
                    .ifPresent(v -> response.setBondImpliedRatingMappingStr(v.getText()));
            EnumUtils.getEnumByValue(bond.getSecuritySeniorityRanking(), SecuritySeniorityRankingEnum.class)
                    .ifPresent(v -> response.setSecuritySeniorityRankingStr(v.getText()));
            EnumUtils.getEnumByValue(bond.getBusinessNature(), BusinessNatureEnum.class)
                    .ifPresent(v -> response.setBusinessNatureStr(v.getText()));
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            return response;
        });
        // 权限控制
        result.setList(this.permissionProcessing(userid, request.getSpreadDate(), result.getList()));
        return result;
    }

    private List<SecuSingleBondYieldSpreadResDTO> permissionProcessing(Long userid, Date spreadDate, List<SecuSingleBondYieldSpreadResDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(SecuSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toList()));
            for (SecuSingleBondYieldSpreadResDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRatingMappingStr(CommonUtils.desensitized(yieldSpread.getBondImpliedRatingMappingStr(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();

        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (SecuSingleBondYieldSpreadResDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
        }
        return list;
    }

    @Override
    public List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate) {
        return secuBondYieldSpreadRedisDAO.listCurves(bondUniCode, startDate, endDate);
    }

    @Override
    public List<SecuSingleBondYieldSpreadResDTO> listBonds(Date spreadDate, @Nullable Set<Long> bondUniCodes) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        spreadDate = super.isToday(spreadDate) ? this.getMaxSpreadDate() : spreadDate;
        List<SecuBondYieldSpreadDO> secuBondYieldSpreadList = secuBondYieldSpreadDAO.listSecuBondYieldSpreads(spreadDate, bondUniCodes);
        if (CollectionUtils.isEmpty(secuBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes.toArray(new Long[0])).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        List<SecuSingleBondYieldSpreadResDTO> responseList = Lists.newArrayListWithExpectedSize(secuBondYieldSpreadList.size());
        for (SecuBondYieldSpreadDO secuBondYieldSpread : secuBondYieldSpreadList) {
            SecuSingleBondYieldSpreadResDTO response = BeanCopyUtils.copyProperties(secuBondYieldSpread, SecuSingleBondYieldSpreadResDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            response.setCdbLerpYield(CommonUtils.formatDecimal(secuBondYieldSpread.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            String ratingStr = RatingUtils.getRating(secuBondYieldSpread.getComExtRatingMapping()) + "/" + RatingUtils.getRating(secuBondYieldSpread.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            EnumUtils.getEnumByValue(secuBondYieldSpread.getBondImpliedRatingMapping(), ImplicitRatingTagEnum.class)
                    .ifPresent(v -> response.setBondImpliedRatingMappingStr(v.getText()));
            EnumUtils.getEnumByValue(secuBondYieldSpread.getSecuritySeniorityRanking(), SecuritySeniorityRankingEnum.class)
                    .ifPresent(v -> response.setSecuritySeniorityRankingStr(v.getText()));
            EnumUtils.getEnumByValue(secuBondYieldSpread.getBusinessNature(), BusinessNatureEnum.class)
                    .ifPresent(v -> response.setBusinessNatureStr(v.getText()));
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            responseList.add(response);
        }
        return responseList;
    }

    @Override
    public Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_SECU_BOND_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = pgSecuBondYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_SECU_BOND_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }

    @Override
    public List<CurveDataResDTO> listCurveData(Long curveId) {
        SecuCurveGenerateConditionReqDTO secuCurveGenerateCondition = super.getCurveGenerateCondition(curveId, SecuCurveGenerateConditionReqDTO.class);
        return listCurveData(secuCurveGenerateCondition);
    }

    @Override
    protected List<CurveDataResDTO> listCurveData(ICurveGenerateReq curveGenerateParam) {
        SecuCurveGenerateConditionReqDTO secuCurveGenerateCondition = (SecuCurveGenerateConditionReqDTO) curveGenerateParam;
        ComOrBondConditionReqDTO comOrBondCondition = secuCurveGenerateCondition.getComOrBondCondition();
        boolean isComOrBond = Objects.nonNull(comOrBondCondition) && Objects.nonNull(comOrBondCondition.getConditionType());
        SecuYieldSearchParam params = BeanCopyUtils.copyProperties(secuCurveGenerateCondition, SecuYieldSearchParam.class);
        List<BondYieldSpreadBO> yieldSpreads;
        if (isComOrBond) {
            params.setComOrBondUniCode(comOrBondCondition.getConditionType(), comOrBondCondition.getUniCode());
            yieldSpreads = pgSecuBondYieldSpreadDAO.listBondYieldSpreads(params);
        } else {
            yieldSpreads = pgSecuBondYieldSpreadCurveDAO.listSecuYieldSpreads(params);
        }
        return super.convertToCurveDataResDTOsAndFilterData(yieldSpreads, super.getMinSampleBondSize(isComOrBond));
    }

    @Override
    public CurveTypeEnum getCurveType() {
        return CurveTypeEnum.SECURITY;
    }

}
