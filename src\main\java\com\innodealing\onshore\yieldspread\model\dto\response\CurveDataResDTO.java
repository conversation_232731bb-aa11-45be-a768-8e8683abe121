package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 曲线数据
 *
 * <AUTHOR>
 */
public class CurveDataResDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("利差日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date spreadDate;

    @ApiModelProperty("信用利差中位数")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondCreditSpread;

    @ApiModelProperty("超额利差中位数")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondExcessSpread;

    @ApiModelProperty("估值收益率中位数")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal cbYield;

    @ApiModelProperty("信用利差平均数")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal avgBondCreditSpread;

    @ApiModelProperty("超额利差平均数")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal avgBondExcessSpread;

    @ApiModelProperty("估值收益率平均数")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal avgCbYield;

    @ApiModelProperty("到期收益率-国开债")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal ytm;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }

    public BigDecimal getYtm() {
        return ytm;
    }

    public void setYtm(BigDecimal ytm) {
        this.ytm = ytm;
    }

    @Override
    public String toString() {
        return "CurveDataResDTO{" +
                "spreadDate=" + spreadDate +
                ", bondCreditSpread=" + bondCreditSpread +
                ", bondExcessSpread=" + bondExcessSpread +
                ", cbYield=" + cbYield +
                ", avgBondCreditSpread=" + avgBondCreditSpread +
                ", avgBondExcessSpread=" + avgBondExcessSpread +
                ", avgCbYield=" + avgCbYield +
                ", ytm=" + ytm +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CurveDataResDTO that = (CurveDataResDTO) o;
        return Objects.equals(spreadDate, that.spreadDate) &&
                Objects.equals(bondCreditSpread, that.bondCreditSpread) &&
                Objects.equals(bondExcessSpread, that.bondExcessSpread) &&
                Objects.equals(cbYield, that.cbYield) &&
                Objects.equals(avgBondCreditSpread, that.avgBondCreditSpread) &&
                Objects.equals(avgBondExcessSpread, that.avgBondExcessSpread) &&
                Objects.equals(avgCbYield, that.avgCbYield) &&
                Objects.equals(ytm, that.ytm);
    }

    @Override
    public int hashCode() {
        return Objects.hash(spreadDate, bondCreditSpread, bondExcessSpread, cbYield, avgBondCreditSpread, avgBondExcessSpread, avgCbYield, ytm);
    }

}
