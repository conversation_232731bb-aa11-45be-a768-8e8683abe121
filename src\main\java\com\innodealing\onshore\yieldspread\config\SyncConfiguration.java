package com.innodealing.onshore.yieldspread.config;

import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 同步配置类 - 使用建造者模式支持灵活的参数配置
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class SyncConfiguration {
    
    /**
     * 开始日期
     */
    private Date startDate;
    
    /**
     * 结束日期
     */
    private Date endDate;
    
    /**
     * 指定的债券类型列表，为空表示所有类型
     */
    private List<YieldPanoramaBondTypeEnum> bondTypes;
    
    /**
     * 指定的曲线代码列表，为空表示所有曲线
     */
    private List<Integer> curveCodes;
    
    /**
     * 是否并行执行
     */
    @Builder.Default
    private boolean parallel = true;
    
    /**
     * 并行度（线程数）
     */
    @Builder.Default
    private int parallelism = Runtime.getRuntime().availableProcessors();
    
    /**
     * 是否启用物化视图刷新控制
     */
    @Builder.Default
    private boolean enableMvRefreshControl = true;
    
    /**
     * 批处理大小
     */
    @Builder.Default
    private int batchSize = 10;
    
    /**
     * 是否同步绝对值数据
     */
    @Builder.Default
    private boolean syncAbs = true;
    
    /**
     * 是否同步分位数据
     */
    @Builder.Default
    private boolean syncQuantile = true;
    
    /**
     * 是否同步变动数据
     */
    @Builder.Default
    private boolean syncChange = true;
    
    /**
     * 是否更新缓存
     */
    @Builder.Default
    private boolean updateCache = true;
    
    /**
     * 获取有效的债券类型集合
     */
    public Set<YieldPanoramaBondTypeEnum> getEffectiveBondTypes() {
        if (bondTypes == null || bondTypes.isEmpty()) {
            return Set.of(YieldPanoramaBondTypeEnum.getTraceBondTypeEnums());
        }
        return Set.copyOf(bondTypes);
    }
    
    /**
     * 获取有效的曲线代码集合
     */
    public Set<Integer> getEffectiveCurveCodes() {
        if (curveCodes == null || curveCodes.isEmpty()) {
            return Set.of(); // 空集合表示所有曲线
        }
        return Set.copyOf(curveCodes);
    }
    
    /**
     * 检查是否需要处理指定的债券类型
     */
    public boolean shouldProcessBondType(YieldPanoramaBondTypeEnum bondType) {
        if (bondTypes == null || bondTypes.isEmpty()) {
            return true;
        }
        return bondTypes.contains(bondType);
    }
    
    /**
     * 检查是否需要处理指定的曲线代码
     */
    public boolean shouldProcessCurveCode(Integer curveCode) {
        if (curveCodes == null || curveCodes.isEmpty()) {
            return true;
        }
        return curveCodes.contains(curveCode);
    }
    
    /**
     * 创建默认配置
     */
    public static SyncConfiguration defaultConfig(Date startDate, Date endDate) {
        return SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .build();
    }
    
    /**
     * 创建单债券类型配置
     */
    public static SyncConfiguration forBondType(Date startDate, Date endDate, YieldPanoramaBondTypeEnum bondType) {
        return SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .bondTypes(List.of(bondType))
                .build();
    }
    
    /**
     * 创建单曲线代码配置
     */
    public static SyncConfiguration forCurveCode(Date startDate, Date endDate, Integer curveCode) {
        return SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .curveCodes(List.of(curveCode))
                .build();
    }
    
    /**
     * 创建串行执行配置
     */
    public static SyncConfiguration serialConfig(Date startDate, Date endDate) {
        return SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .parallel(false)
                .build();
    }
}
