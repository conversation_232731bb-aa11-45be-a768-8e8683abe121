package com.innodealing.onshore.yieldspread.config;

import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveCodeEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步配置类 - 使用建造者模式支持灵活的参数配置
 *
 * <AUTHOR>
 */
public class SyncConfiguration {
    
    /**
     * 开始日期
     */
    private Date startDate;
    
    /**
     * 结束日期
     */
    private Date endDate;
    
    /**
     * 指定的债券类型列表，为空表示所有类型
     */
    private List<YieldPanoramaBondTypeEnum> bondTypes;
    
    /**
     * 指定的曲线代码列表，为空表示所有曲线
     */
    private List<Integer> curveCodes;
    
    /**
     * 是否并行执行
     */
    private boolean parallel = true;

    /**
     * 并行度（线程数）
     */
    private boolean parallelism = true;

    /**
     * 是否启用物化视图刷新控制
     */
    private boolean enableMvRefreshControl = true;


    /**
     * 是否同步绝对值数据
     */
    private boolean syncAbs = true;

    /**
     * 是否同步分位数据
     */
    private boolean syncQuantile = true;

    /**
     * 是否同步变动数据
     */
    private boolean syncChange = true;

    /**
     * 是否更新缓存
     */
    private boolean updateCache = true;
    
    // 构造函数
    public SyncConfiguration() {
    }

    private SyncConfiguration(Builder builder) {
        this.startDate = builder.startDate;
        this.endDate = builder.endDate;
        this.bondTypes = builder.bondTypes;
        this.curveCodes = builder.curveCodes;
        this.parallel = builder.parallel;
        this.parallelism = builder.parallelism;
        this.enableMvRefreshControl = builder.enableMvRefreshControl;
        this.syncAbs = builder.syncAbs;
        this.syncQuantile = builder.syncQuantile;
        this.syncChange = builder.syncChange;
        this.updateCache = builder.updateCache;
    }

    // Getter 方法
    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public List<YieldPanoramaBondTypeEnum> getBondTypes() {
        return bondTypes;
    }

    public List<Integer> getCurveCodes() {
        return curveCodes;
    }

    public boolean isParallel() {
        return parallel;
    }

    public boolean getParallelism() {
        return parallelism;
    }

    public boolean isEnableMvRefreshControl() {
        return enableMvRefreshControl;
    }


    public boolean isSyncAbs() {
        return syncAbs;
    }

    public boolean isSyncQuantile() {
        return syncQuantile;
    }

    public boolean isSyncChange() {
        return syncChange;
    }

    public boolean isUpdateCache() {
        return updateCache;
    }

    // Setter 方法
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public void setBondTypes(List<YieldPanoramaBondTypeEnum> bondTypes) {
        this.bondTypes = bondTypes;
    }

    public void setCurveCodes(List<Integer> curveCodes) {
        this.curveCodes = curveCodes;
    }

    public void setParallel(boolean parallel) {
        this.parallel = parallel;
    }

    public void setParallelism(boolean parallelism) {
        this.parallelism = parallelism;
    }

    public void setEnableMvRefreshControl(boolean enableMvRefreshControl) {
        this.enableMvRefreshControl = enableMvRefreshControl;
    }

    public void setSyncAbs(boolean syncAbs) {
        this.syncAbs = syncAbs;
    }

    public void setSyncQuantile(boolean syncQuantile) {
        this.syncQuantile = syncQuantile;
    }

    public void setSyncChange(boolean syncChange) {
        this.syncChange = syncChange;
    }

    public void setUpdateCache(boolean updateCache) {
        this.updateCache = updateCache;
    }

    /**
     * 获取有效的债券类型集合
     */
    public Set<YieldPanoramaBondTypeEnum> getEffectiveBondTypes() {
        if (bondTypes == null || bondTypes.isEmpty()) {
            return YieldPanoramaBondTypeEnum.getTraceBondTypeEnums();
        }
        return new HashSet<YieldPanoramaBondTypeEnum>(bondTypes);
    }

    /**
     * 获取有效的曲线代码集合
     */
    public Set<Integer> getEffectiveCurveCodes() {
        if (curveCodes == null || curveCodes.isEmpty()) {
            return Collections.emptySet();
        }
        return new HashSet<Integer>(curveCodes);
    }
    
    /**
     * 检查是否需要处理指定的债券类型
     */
    public boolean shouldProcessBondType(YieldPanoramaBondTypeEnum bondType) {
        if (bondTypes == null || bondTypes.isEmpty()) {
            return true;
        }
        return bondTypes.contains(bondType);
    }
    
    /**
     * 检查是否需要处理指定的曲线代码
     */
    public boolean shouldProcessCurveCode(Integer curveCode) {
        if (curveCodes == null || curveCodes.isEmpty()) {
            return true;
        }
        return curveCodes.contains(curveCode);
    }
    
    /**
     * 创建Builder实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 创建默认配置
     */
    public static SyncConfiguration defaultConfig(Date startDate, Date endDate) {
        return SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .build();
    }

    /**
     * 创建单债券类型配置
     */
    public static SyncConfiguration forBondType(Date startDate, Date endDate, YieldPanoramaBondTypeEnum bondType) {
        return SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .bondTypes(Collections.singletonList(bondType))
                .build();
    }

    /**
     * 创建单曲线代码配置
     */
    public static SyncConfiguration forCurveCode(Date startDate, Date endDate, Integer curveCode) {
        return SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .curveCodes(Collections.singletonList(curveCode))
                .build();
    }

    /**
     * 创建串行执行配置
     */
    public static SyncConfiguration serialConfig(Date startDate, Date endDate) {
        return SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .parallel(false)
                .build();
    }

    @Override
    public String toString() {
        return "SyncConfiguration{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", bondTypes=" + bondTypes +
                ", curveCodes=" + curveCodes +
                ", parallel=" + parallel +
                ", parallelism=" + parallelism +
                ", enableMvRefreshControl=" + enableMvRefreshControl +
                ", batchSize=" + batchSize +
                ", syncAbs=" + syncAbs +
                ", syncQuantile=" + syncQuantile +
                ", syncChange=" + syncChange +
                ", updateCache=" + updateCache +
                '}';
    }

    /**
     * Builder类
     */
    public static class Builder {
        private Date startDate;
        private Date endDate;
        private List<YieldPanoramaBondTypeEnum> bondTypes;
        private List<Integer> curveCodes;
        private boolean parallel = true;
        private boolean parallelism = false;
        private boolean enableMvRefreshControl = true;
        private boolean syncAbs = true;
        private boolean syncQuantile = true;
        private boolean syncChange = true;
        private boolean updateCache = true;

        public Builder startDate(Date startDate) {
            this.startDate = startDate;
            return this;
        }

        public Builder endDate(Date endDate) {
            this.endDate = endDate;
            return this;
        }

        public Builder bondTypes(List<YieldPanoramaBondTypeEnum> bondTypes) {
            this.bondTypes = bondTypes;
            return this;
        }

        public Builder curveCodes(List<Integer> curveCodes) {
            this.curveCodes = curveCodes;
            return this;
        }

        public Builder parallel(boolean parallel) {
            this.parallel = parallel;
            return this;
        }

        public Builder parallelism(boolean parallelism) {
            this.parallelism = parallelism;
            return this;
        }

        public Builder enableMvRefreshControl(boolean enableMvRefreshControl) {
            this.enableMvRefreshControl = enableMvRefreshControl;
            return this;
        }

        public Builder syncAbs(boolean syncAbs) {
            this.syncAbs = syncAbs;
            return this;
        }

        public Builder syncQuantile(boolean syncQuantile) {
            this.syncQuantile = syncQuantile;
            return this;
        }

        public Builder syncChange(boolean syncChange) {
            this.syncChange = syncChange;
            return this;
        }

        public Builder updateCache(boolean updateCache) {
            this.updateCache = updateCache;
            return this;
        }

        public SyncConfiguration build() {
            if (CollectionUtils.isEmpty(bondTypes) && CollectionUtils.isEmpty(curveCodes)) {
                this.bondTypes = new ArrayList<>(YieldPanoramaBondTypeEnum.getTraceBondTypeEnums());
                this.curveCodes = Arrays.stream(YieldSpreadCurveCodeEnum.values()).map(YieldSpreadCurveCodeEnum::getValue).collect(Collectors.toList());
            }
            return new SyncConfiguration(this);
        }
    }
}
