package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.consts.TipsConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveDefinitionResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CurvePoolListResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveResDTO;
import com.innodealing.onshore.yieldspread.service.CurvePoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.hibernate.validator.constraints.Length;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.sql.Date;
import java.util.List;

/**
 * (内部)曲线池
 *
 * <AUTHOR>
 */
@Api(tags = "(内部)曲线池")
@RestController
@RequestMapping("internal/curve-pool")
public class InternalCurvePoolController {

    @Resource
    private CurvePoolService curvePoolService;

    @GetMapping("curve-definition/list-definition")
    @ApiOperation(value = "曲线池曲线列表")
    public CurvePoolListResDTO listCurveDefinitions(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveName", value = "曲线名称") @RequestParam(required = false) String curveName,
            @ApiParam(name = "justLookSelected", value = "只看已选") @RequestParam(defaultValue = "false") Boolean justLookSelected) {
        return curvePoolService.listCurveDefinitions(userid, curveName, justLookSelected);
    }

    @GetMapping("curve-definition/get-definition")
    @ApiOperation(value = "曲线定义数据详情")
    public CurveDefinitionResDTO getCurveDefinition(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam Long curveId) {
        return curvePoolService.getCurveDefinition(userid, curveId);
    }

    @PostMapping("selected/set-up")
    @ApiOperation(value = "选择或去除选择某条曲线")
    public Boolean setUpSelected(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam Long curveId) {
        return curvePoolService.setUpSelected(userid, curveId);
    }

    @PostMapping("selected-curves")
    @ApiOperation(value = "选择曲线")
    public RestResponse<Boolean> selectedCurves(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveIds", value = "曲线id") @RequestBody List<Long> curveIds) {
        return RestResponse.Success(curvePoolService.selectedCurves(userid, curveIds));
    }

    @PostMapping("selected/clear-all")
    @ApiOperation(value = "清空已选")
    public Boolean clearAllSelected(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid) {
        return curvePoolService.clearAllSelected(userid);
    }

    @GetMapping("curve/get-curve")
    @ApiOperation(value = "获取曲线数据")
    public CurveResDTO getCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam Long curveId,
            @ApiParam(name = "startDate", value = "起始日期") @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam Date endDate) {
        return curvePoolService.getCurve(userid, curveId, startDate, endDate);
    }

    @PostMapping("curve/remove-curve")
    @ApiOperation(value = "移除曲线")
    public Boolean removeCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam Long curveId) {
        return curvePoolService.removeCurve(userid, curveId);
    }

    @PostMapping("curve/batch/remove-curve")
    @ApiOperation(value = "批量移除曲线")
    public RestResponse<Integer> batchRemoveCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveIds", value = "曲线id列表") @RequestBody List<Long> curveIds) {
        return RestResponse.Success(curvePoolService.batchRemoveCurve(userid, curveIds));
    }

    @PostMapping("curve/clear-cache")
    @ApiOperation(value = "清除曲线缓存，传了userid就清除该用户下所有曲线缓存，传了curveId就只清除这一条曲线的缓存，curveId优先级更高")
    public Boolean clearCurveCache(
            @ApiParam(name = "userid", value = "用户") @RequestParam(required = false) Long userid,
            @ApiParam(name = "curveId", value = "曲线id") @RequestParam(required = false) Long curveId) {
        return curvePoolService.clearCurveCache(userid, curveId);
    }

    @PostMapping("curve/clear-benchmark-cache")
    @ApiOperation(value = "清除基准曲线缓存")
    public Boolean clearBenchmarkCurveCache() {
        return curvePoolService.clearBenchmarkCurveCache();
    }

    @PostMapping("group/initialize")
    @ApiOperation(value = "初始化曲线组")
    public Boolean initializeGroup() {
        return curvePoolService.initializeGroup();
    }

    @PostMapping("/curve/move-location")
    @ApiOperation(value = "移动曲线位置")
    public Boolean moveCurveLocation(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "当前曲线ID") @RequestParam Long curveId,
            @ApiParam(name = "targetGroupId", value = "目标曲线组id") @RequestParam Long targetGroupId,
            @ApiParam(name = "targetLocationPreCurveId", value = "目标位置前一个曲线id，如果移动到第一位，那么就是null，如果移动到最后一位，那么就是最后一条曲线id")
            @RequestParam(required = false) Long targetLocationPreCurveId) {
        return curvePoolService.moveCurveLocation(userid, curveId, targetGroupId, targetLocationPreCurveId);
    }

    @PostMapping("/group/add")
    @ApiOperation(value = "添加曲线组")
    public Long addGroup(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @NotBlank(message = "曲线组名称不能为空") @Length(message = TipsConst.GROUP_NAME_REMIND_UPPER_LIMIT, max = YieldSpreadConst.GROUP_NAME_MAX_LENGTH)
            @ApiParam(name = "groupName", value = "组名称") @RequestParam String groupName) {
        return curvePoolService.addGroup(userid, groupName);
    }

    @PostMapping("/group/update")
    @PutMapping(value = "更新曲线组")
    public Boolean updateGroup(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "groupId", value = "曲线组id") @RequestParam Long groupId,
            @NotBlank(message = "曲线组名称不能为空") @Length(message = TipsConst.GROUP_NAME_REMIND_UPPER_LIMIT, max = YieldSpreadConst.GROUP_NAME_MAX_LENGTH)
            @ApiParam(name = "groupName", value = "组名称") @RequestParam String groupName) {
        return curvePoolService.updateGroup(userid, groupId, groupName);
    }

    @DeleteMapping("/group/delete")
    @ApiOperation(value = "删除曲线组")
    public Boolean deleteGroup(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "groupId", value = "曲线组id") @RequestParam Long groupId) {
        return curvePoolService.deleteGroup(userid, groupId);
    }

    @PostMapping("/group/move-location")
    @ApiOperation(value = "移动曲线组位置")
    public Boolean moveGroupLocation(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "groupId", value = "当前曲线组id") @RequestParam Long groupId,
            @ApiParam(name = "targetLocationPreGroupId", value = "目标位置前一个曲线组id，如果移动到第一位，那么就是null，如果移动到最后一位，那么就是最后一个曲线组id")
            @RequestParam(required = false) Long targetLocationPreGroupId) {
        return curvePoolService.moveGroupLocation(userid, groupId, targetLocationPreGroupId);
    }

}
