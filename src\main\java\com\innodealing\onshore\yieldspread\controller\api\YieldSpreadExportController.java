package com.innodealing.onshore.yieldspread.controller.api;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.international.common.template.actuator.EasyExcelTemplateActuator;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.controller.ExportController;
import com.innodealing.onshore.yieldspread.enums.SpreadYearSpanEnum;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondBaseDataExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.CurveDataExportRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.*;
import com.innodealing.onshore.yieldspread.service.ExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 利差分析导出接口
 *
 * <AUTHOR>
 */
@Api(tags = "(API)利差分析-导出")
@RestController
@Validated
@RequestMapping("api/yield-spread/export")
public class YieldSpreadExportController extends ExportController {

    @Resource
    private ExportService exportService;

    @ApiOperation(value = "行业-导出利差全景图", produces = "application/octet-stream")
    @PostMapping(value = "/indu/panorama")
    public void exportInduPanorama(@RequestBody InduPanoramaExportRequestDTO request) throws IOException {
        List<InduPanoramaExportExcelDTO> panoramaList = exportService.listInduPanoramaExcels(request);
        StringBuilder sb = new StringBuilder("产业利差全景");
        EnumUtils.getEnumByValue(request.getYearSpan(), SpreadYearSpanEnum.class).ifPresent(span -> sb.append(request.getYear()).append(span.getText()));
        String fileName = sb.toString();
        String title = request.buildExportTitle();
        EasyExcelTemplateActuator.exportForDmByDefaultTemplate(fileName, title, InduPanoramaExportExcelDTO.class, panoramaList, response);
    }

    @ApiOperation(value = "行业-导出某天利差全景图", produces = "application/octet-stream")
    @PostMapping(value = "/indu/day-panorama")
    public void exportInduDayPanorama(@RequestBody InduDayPanoramaExportRequestDTO request) throws IOException {
        List<InduPanoramaExportExcelDTO> panoramaList = exportService.listInduDayPanoramaExcels(request);
        String fileName = "产业利差全景" + request.getSelectDate().toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
        String title = request.buildExportTitle();
        EasyExcelTemplateActuator.exportForDmByDefaultTemplate(fileName, title, InduPanoramaExportExcelDTO.class, panoramaList, response);
    }

    @ApiOperation(value = "城投-导出利差全景图", produces = "application/octet-stream")
    @PostMapping(value = "/udic/panorama")
    public void udicSpreadPanorama(@ApiParam(name = "requestDTO", value = "城投利差-全景请求参数", required = true)
                                   @RequestBody UdicPanoramaExportRequestDTO request) throws IOException {
        List<UdicSpreadPanoramaExportExcelDTO> panoramaList = exportService.listUdicPanoramasExcels(request);
        StringBuilder sb = new StringBuilder("城投利差全景");
        EnumUtils.getEnumByValue(request.getYearSpan(), SpreadYearSpanEnum.class).ifPresent(span -> sb.append(request.getYear()).append(span.getText()));
        String fileName = sb.toString();
        String title = request.buildExportTitle();
        EasyExcelTemplateActuator.exportForDmByDefaultTemplate(fileName, title, UdicSpreadPanoramaExportExcelDTO.class, panoramaList, response);
    }

    @ApiOperation(value = "城投-导出某天利差全景图", produces = "application/octet-stream")
    @PostMapping(value = "/udic/day-panorama")
    public void udicSpreadDayPanorama(@ApiParam(name = "requestDTO", value = "城投利差-全景请求参数", required = true)
                                      @RequestBody UdicDayPanoramaExportRequestDTO request) throws IOException {
        List<UdicSpreadPanoramaExportExcelDTO> panoramaList = exportService.listUdicDayPanoramasExcels(request);
        String fileName = "城投利差全景" + request.getSelectDate().toLocalDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
        String title = request.buildExportTitle();
        EasyExcelTemplateActuator.exportForDmByDefaultTemplate(fileName, title, UdicSpreadPanoramaExportExcelDTO.class, panoramaList, response);
    }

    @ApiOperation(value = "行业-导出作图数据", produces = "application/octet-stream")
    @PostMapping(value = "/indu/curve")
    public void exportInduCurve(@RequestBody InduCurveExportRequestDTO request) throws IOException {
        List<InduCurveExportExcelDTO> exportExcelList = exportService.listInduCurveListExcels(request);
        final String fileName = "产业利差曲线" + LocalDate.now().toString();
        super.exportExcel(fileName, "产业利差曲线", exportExcelList, InduCurveExportExcelDTO.class);
    }

    @ApiOperation(value = "行业-导出主体成分数据", produces = "application/octet-stream")
    @PostMapping(value = "/indu/com-list")
    public void exportInduComList(@RequestBody InduListExportRequestDTO request) throws IOException {
        List<InduComSpreadExportExcelDTO> exportExcelList = exportService.listInduComListExcels(request);
        final String fileName = "产业成分主体利差" + LocalDate.now().toString();
        super.exportExcel(fileName, "产业成分主体利差", exportExcelList, InduComSpreadExportExcelDTO.class);
    }

    @ApiOperation(value = "行业-导出债券成分数据", produces = "application/octet-stream")
    @PostMapping(value = "/indu/bond-list")
    public void exportInduBondList(@RequestBody InduListExportRequestDTO request) throws IOException {
        List<InduBondSpreadExportExcelDTO> exportExcelList = exportService.listInduBondListExcels(request);
        String fileName = "产业成分债券利差" + LocalDate.now().toString();
        super.exportExcel(fileName, "产业成分债券利差", exportExcelList, InduBondSpreadExportExcelDTO.class);
    }

    @ApiOperation(value = "城投-导出作图数据", produces = "application/octet-stream")
    @PostMapping(value = "/udic/curve")
    public void udicSpreadCurve(@ApiParam(name = "requestDTO", value = "城投利差-导出请求参数", required = true)
                                @RequestBody UdicCurveExportRequestDTO request) throws IOException {
        List<UdicSpreadCurveExcelDTO> curveList = exportService.listUdicCurvesExcels(request);
        String fileName = "城投利差曲线" + LocalDate.now().toString();
        super.exportExcel(fileName, "城投利差曲线", curveList, UdicSpreadCurveExcelDTO.class);
    }

    @ApiOperation(value = "城投-导出主体成分数据", produces = "application/octet-stream")
    @PostMapping(value = "/udic/com-list")
    public void udicComSpreadExcels(@ApiParam(name = "requestDTO", value = "城投利差-导出请求参数", required = true)
                                    @RequestBody UdicListExportRequestDTO request) throws IOException {
        String spreadDate = Objects.nonNull(request.getSpreadDate()) ? request.getSpreadDate().toString() : LocalDate.now().toString();
        List<UdicComSpreadExportExcelDTO> comSpreadList = exportService.listUdicComSpreadExcels(request);
        String fileName = "城投成分主体利差" + spreadDate;
        super.exportExcel(fileName, "城投成分主体利差", comSpreadList, UdicComSpreadExportExcelDTO.class);
    }

    @ApiOperation(value = "城投-导出债券成分数据", produces = "application/octet-stream")
    @PostMapping(value = "/udic/bond-list")
    public void udicBondSpreadExcels(@ApiParam(name = "requestDTO", value = "城投利差-导出请求参数", required = true)
                                     @RequestBody UdicListExportRequestDTO request) throws IOException {
        String spreadDate = Objects.nonNull(request.getSpreadDate()) ? request.getSpreadDate().toString() : LocalDate.now().toString();
        List<UdicBondSpreadExportExcelDTO> bondSpreadList = exportService.listUdicBondSpreadExcels(request);
        String fileName = "城投成分债券利差" + spreadDate;
        super.exportExcel(fileName, "城投成分债券利差", bondSpreadList, UdicBondSpreadExportExcelDTO.class);
    }

    @ApiOperation("导出债券基础信息")
    @PostMapping("/bond-base-data")
    public void exportBondBaseData(@RequestBody @NotEmpty(message = "债券集合不能为空")
                                   @Size(message = "最多 {max} 只债券，请调整后再导出", max = YieldSpreadConst.IMPORT_BOND_SIZE_UPPER_LIMIT)
                                   List<BondBaseDataExportReqDTO> bondBaseData) throws IOException {
        List<BondBaseDataExcelDTO> bondBaseDataExcelDTOList = BeanCopyUtils.copyList(bondBaseData, BondBaseDataExcelDTO.class);
        String fileName = "自定义曲线样本";
        super.exportExcel(fileName, fileName, bondBaseDataExcelDTOList, BondBaseDataExcelDTO.class);
    }

    @ApiOperation("导出曲线数据")
    @PostMapping("/curve-data")
    public void exportCurveData(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveIds", value = "曲线id") @NotEmpty(message = "请选择曲线") @RequestParam List<Long> curveIds,
            @ApiParam(name = "arithmeticType", value = "曲线算法类型 1：中位数,2：平均数") @NotNull(message = "请选择曲线算法类型") @RequestParam Integer arithmeticType,
            @ApiParam(name = "startDate", value = "起始日期") @NotNull(message = "请选择起始时间") @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @NotNull(message = "请选择结束时间") @RequestParam Date endDate) throws IOException {
        CurveDataExportRequestDTO request = new CurveDataExportRequestDTO();
        request.setCurveIds(curveIds);
        request.setArithmeticType(arithmeticType);
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        List<CurveExportExcelDTO> curveExportExcels = exportService.exportCurveData(userid, request);
        String fileName = YieldSpreadConst.EXPORT_CURVE_PREFIX + LocalDate.now();
        super.exportExcel(fileName, YieldSpreadConst.EXPORT_CURVE_PREFIX, curveExportExcels, CurveExportExcelDTO.class);
    }

    @ApiOperation("导出曲线数据-v2")
    @PostMapping("/curve-data/v2")
    public void exportCurveDataV2(@ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
                                  @RequestBody @Validated CurveDataExportRequestDTO request) throws IOException {
        List<CurveExportExcelDTO> curveExportExcels = exportService.exportCurveData(userid, request);
        String fileName = YieldSpreadConst.EXPORT_CURVE_PREFIX + LocalDate.now();
        super.exportExcel(fileName, YieldSpreadConst.EXPORT_CURVE_PREFIX, curveExportExcels, CurveExportExcelDTO.class);
    }

    @ApiOperation("导出曲线数据-v3")
    @PostMapping("/curve-data/v3")
    public void exportCurveDataV3(@ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
                                  @RequestBody @Validated CurveDataExportRequestDTO request) throws IOException {
        List<CurveExportExcelDTO> curveExportExcels = exportService.exportCurveData(userid, request);
        FixCurveExportExcelDTO<CurveExportExcelDTO> fixCurveExportExcelDTO
                = new FixCurveExportExcelDTO<>(YieldSpreadConst.EXPORT_CURVE_PREFIX, curveExportExcels, CurveExportExcelDTO.class);
        List<DynCurveExportExcelDTO> dynCurveExportExcelDTOS = exportService.dynExportCurveData(curveExportExcels);
        String fileName = YieldSpreadConst.EXPORT_CURVE_PREFIX + LocalDate.now();
        super.mixExportExcelMultipleSheet(fileName, Collections.singletonList(fixCurveExportExcelDTO), dynCurveExportExcelDTOS);
    }

    @ApiOperation("导出单券利差数据")
    @PostMapping("/single-bond")
    public void exportSingleBondYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveIds", value = "曲线id") @NotEmpty(message = "请选择曲线") @RequestParam List<Long> curveIds,
            @ApiParam(name = "spreadDate", value = "利差日期") @NotNull(message = "请选择利差日期") @RequestParam Date spreadDate) throws IOException {
        Map<String, List<BaseCurveYieldSpreadExcelDTO>> excelDataMap = exportService.exportSingleBondYieldSpread(userid, curveIds, spreadDate);
        String fileName = "单券利差数据" + spreadDate;
        super.exportExcelMultipleSheet(fileName, excelDataMap);
    }

    @ApiOperation("导出主体利差数据")
    @PostMapping("/com")
    public void exportComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveIds", value = "曲线id") @NotEmpty(message = "请选择曲线") @RequestParam List<Long> curveIds,
            @ApiParam(name = "spreadDate", value = "利差日期") @NotNull(message = "请选择利差日期") Date spreadDate) throws IOException {
        Map<String, List<BaseCurveYieldSpreadExcelDTO>> excelDataMap = exportService.exportComYieldSpread(userid, curveIds, spreadDate);
        String fileName = "主体利差数据" + spreadDate;
        super.exportExcelMultipleSheet(fileName, excelDataMap);
    }

}
