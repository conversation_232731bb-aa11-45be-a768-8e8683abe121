package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 主体基础信息远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "comService", url = "${com.service.api.url}", path = "/internal/com")
public interface ComService {
    /**
     * 获取发行人精简信息
     *
     * @param comUniCodes 发行人唯一代码
     * @return 发行人精简信息
     */
    @PostMapping("info/short/getByUnicode")
    List<ComShortInfoDTO> listComShortInfoByUniCodes(@RequestBody Long[] comUniCodes);

    /**
     * 获取发行人精简信息
     *
     * @param comUniCodes 发行人唯一代码
     * @return key 发行人唯一代码,value 发行人精简信息
     */
    default Map<Long, ComShortInfoDTO> getComShortInfoByUniCodeMap(Set<Long> comUniCodes) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return Collections.emptyMap();
        }
        return listComShortInfoByUniCodes(comUniCodes.stream().toArray(Long[]::new)).stream()
                .collect(Collectors.toMap(ComShortInfoDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }
}
