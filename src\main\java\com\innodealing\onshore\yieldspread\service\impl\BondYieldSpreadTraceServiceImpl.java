package com.innodealing.onshore.yieldspread.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.zip.ZipUtils;
import com.innodealing.international.common.template.utils.ExcelUtils;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.builder.BondYieldSpreadBuilder;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.*;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.view.PgBondYieldSpreadTraceQuantileViewDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserSpreadConfigDAO;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.CommonUtils;
import com.innodealing.onshore.yieldspread.model.bo.PeriodExcelFillDataBO;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceAbsBO;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceBO;
import com.innodealing.onshore.yieldspread.model.bo.UserSpreadConfigBO;
import com.innodealing.onshore.yieldspread.model.dto.*;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldTraceExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.*;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgBondYieldSpreadTraceQuantileViewDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgSpreadTraceQuantileStatisticsViewDO;
import com.innodealing.onshore.yieldspread.processor.YieldSpreadTraceContext;
import com.innodealing.onshore.yieldspread.processor.YieldSpreadTraceProcessor;
import com.innodealing.onshore.yieldspread.service.BondYieldSpreadTraceService;
import com.innodealing.onshore.yieldspread.service.YieldSpreadCommonService;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import com.innodealing.onshore.yieldspread.service.internal.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.ONE_HUNDRED;
import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.*;

/**
 * 利差追踪服务
 *
 * <AUTHOR>
 */
@Service
public class BondYieldSpreadTraceServiceImpl implements BondYieldSpreadTraceService, InitializingBean {

    private static final int DEFAULT_CODE = 0;


    private static final int TRACE_QUANTILE_SCALE = 2;

    private static final int TRACE_INTERVAL_CHANGE_SCALE = 2;

    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d";

    private static final String FILE_NAME = "利差追踪-%s-%s";

    private static final String TRACE_SPREAD_DATE_KEY = "yield-spread:bond_yield_spread_trace:%s";

    private static final String RATING_LINE_CHART_KEY = "yield-spread:spread_trace:line_chart:rating:%d:%d:%d";

    private static final String VARIETIES_LINE_CHART_KEY = "yield-spread:spread_trace:line_chart:varieties:%d:%d:%d";

    private static final String PERIOD_LINE_CHART_KEY = "yield-spread:spread_trace:line_chart:period:%d:%d:%d";

    /**
     * 利差追踪导出文件填充
     */
    private static final String TRACE_EXPORT_FILL = "%s%sFill";

    private final List<Integer> curveCodes = new ArrayList<>();

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final Map<YieldPanoramaBondTypeEnum, List<YieldSpreadTraceProcessor>> processorMap = new ConcurrentHashMap<>();

    private final List<TraceLineChartKey> traceLineChartKeys = new ArrayList<>();

    private final Map<PeriodEnum, Function<PgBondYieldSpreadTraceAbsBO, BigDecimal>> periodFunctionMap = new ConcurrentHashMap<>();

    /**
     * 没有评级的 债券类型 集合
     */
    private final Set<YieldPanoramaBondTypeEnum> noRatingBondTypes = EnumSet.of(SECURITIES_SUB_BOND, SECURITIES_PERPETUAL_BOND,
            CHINA_BOND, CD_BOND);

    /**
     * 展示品种组的 债券类型 集合
     */
    private final Set<YieldPanoramaBondTypeEnum> varietiesBondTypes = EnumSet.of(INTEREST_RATE_BOND);


    @Resource
    private PgBondYieldSpreadTraceChangeDAO pgBondYieldSpreadTraceChangeDAO;

    @Resource
    private PgBondYieldSpreadTraceQuantileDAO pgBondYieldSpreadTraceQuantileDAO;

    @Resource
    private PgBondYieldPanoramaAbsDAO pgBondYieldPanoramaAbsDAO;

    @Resource
    private PgBondYieldPanoramaQuantileDAO pgBondYieldPanoramaQuantileDAO;

    @Resource
    private PgBondYieldPanoramaChangeDAO pgBondYieldPanoramaChangeDAO;

    @Resource
    private PgBondYieldSpreadTraceAbsDAO pgBondYieldSpreadTraceAbsDAO;

    @Resource
    private PgBondYieldSpreadTraceQuantileViewDAO pgBondYieldSpreadTraceQuantileViewDAO;

    @Resource
    private HolidayService holidayService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private UserService userService;

    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;

    @Resource
    private UserSpreadConfigDAO userSpreadConfigDAO;

    @Override
    public void afterPropertiesSet() {
        curveCodes.addAll(Arrays.stream(YieldSpreadCurveCodeEnum.values()).map(YieldSpreadCurveCodeEnum::getValue).collect(Collectors.toList()));
        Collection<YieldSpreadTraceProcessor> processors = applicationContext.getBeansOfType(YieldSpreadTraceProcessor.class).values();
        for (YieldPanoramaBondTypeEnum bondTypeEnum : values()) {
            processorMap.put(bondTypeEnum, processors.stream().filter(processor -> processor.supportBondType(bondTypeEnum)).collect(Collectors.toList()));
        }
        List<TraceLineChartKey> lineChartKeys = Arrays.stream(values())
                .filter(bondTypeEnum -> CollectionUtils.isNotEmpty(bondTypeEnum.getChartTypes()))
                .flatMap(bondTypeEnum -> bondTypeEnum.getChartTypes().stream()
                        .map(chartType -> EnumUtils.ofNullable(YieldSpreadChartTypeEnum.class, chartType)).filter(Optional::isPresent).map(Optional::get)
                        .map(chart -> TraceLineChartKey.of(bondTypeEnum, chart)))
                .collect(Collectors.toList());
        traceLineChartKeys.addAll(lineChartKeys);
        periodFunctionMap.put(PeriodEnum.ONE_MONTH, PgBondYieldSpreadTraceAbsBO::getYtm1M);
        periodFunctionMap.put(PeriodEnum.THREE_MONTHS, PgBondYieldSpreadTraceAbsBO::getYtm3M);
        periodFunctionMap.put(PeriodEnum.SIX_MONTHS, PgBondYieldSpreadTraceAbsBO::getYtm6M);
        periodFunctionMap.put(PeriodEnum.NINE_MONTHS, PgBondYieldSpreadTraceAbsBO::getYtm9M);
        periodFunctionMap.put(PeriodEnum.ONE_YEAR, PgBondYieldSpreadTraceAbsBO::getYtm1Y);
        periodFunctionMap.put(PeriodEnum.TWO_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm2Y);
        periodFunctionMap.put(PeriodEnum.THREE_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm3Y);
        periodFunctionMap.put(PeriodEnum.FIVE_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm5Y);
        periodFunctionMap.put(PeriodEnum.SEVEN_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm7Y);
        periodFunctionMap.put(PeriodEnum.TEN_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm10Y);
        periodFunctionMap.put(PeriodEnum.FIFTEEN_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm15Y);
        periodFunctionMap.put(PeriodEnum.TWENTY_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm20Y);
        periodFunctionMap.put(PeriodEnum.THIRTY_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm30Y);
        periodFunctionMap.put(PeriodEnum.FIFTY_YEARS, PgBondYieldSpreadTraceAbsBO::getYtm50Y);
    }

    /**
     * 获取利差追踪基础数据
     *
     * @param userId     用户id
     * @param bondType   债券类型
     * @param spreadDate 利率日期
     * @return 利差追踪基础数据返回前端
     */
    @Override
    public List<YieldSpreadTraceTabResponseDTO> listYieldSpreadTraceAbs(Long userId, Integer bondType, Date spreadDate) {
        PgBondYieldSpreadTraceDTO pgBondYieldSpreadTraceDTO = this.listPgBondYieldSpreadTraceBOs(BondYieldTableTypeEnum.ABS.getValue(),
                bondType, spreadDate, null, null, null);
        List<PgBondYieldSpreadTraceBO> pgBondYieldTraceBOList = pgBondYieldSpreadTraceDTO.getPgBondYieldSpreadTraceBOList();
        return convertToTraceTabResponse(userId, bondType, spreadDate, pgBondYieldTraceBOList, null, null);
    }

    private List<YieldSpreadTraceTabResponseDTO> convertToTraceTabResponse(Long userId, Integer bondType,
                                                                           Date spreadDate, List<PgBondYieldSpreadTraceBO> pgBondYieldPanoramaBOList,
                                                                           Date startDate, Date endDate) {
        Map<Integer, List<PgBondYieldSpreadTraceBO>> chartTypeToTraceBOMap =
                pgBondYieldPanoramaBOList.stream().collect(Collectors.groupingBy(PgBondYieldSpreadTraceBO::getChartType));
        YieldPanoramaBondTypeEnum yieldPanoramaBondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
        UserSpreadConfigBO<Integer> userSpreadConfigBO = userSpreadConfigDAO.getUserSpreadConfigByConfigId(userId,
                (long) yieldPanoramaBondTypeEnum.getConfigEnum().getValue(),
                Integer.class).orElseGet(UserSpreadConfigBO::new);
        return chartTypeToTraceBOMap.entrySet().stream().map(x -> {
                    boolean hasCbPermission = true;
                    YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
                    if (!YieldPanoramaBondTypeEnum.getHasPermissionBondTypeEnums().contains(bondTypeEnum)) {
                        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
                        hasCbPermission = spreadDate.before(cbPermissionDividingDate) ?
                                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                                userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
                    }
                    return BondYieldSpreadBuilder.builder(hasCbPermission, bondType, x.getKey(), x.getValue(), startDate, endDate, userSpreadConfigBO.getConfigDetails());
                })
                .sorted(Comparator.comparing(YieldSpreadTraceTabResponseDTO::getTypeSort)).collect(Collectors.toList());
    }

    /**
     * 获取利差追踪历史分位
     *
     * @param userId       用户id
     * @param bondType     债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充
     * @param spreadDate   利差日期
     * @param quantileType 分位类型 1:3年，2:5年
     * @param startDate    自定义开始时间
     * @param endDate      自定义结束时间
     * @return 利差追踪历史分位数据返回前端
     */
    @Override
    public List<YieldSpreadTraceTabResponseDTO> listYieldSpreadTraceQuantile(Long userId, Integer bondType, Date spreadDate, Integer quantileType, Date startDate, Date endDate) {
        PgBondYieldSpreadTraceDTO pgBondYieldSpreadTraceDTO = this.listPgBondYieldSpreadTraceBOs(BondYieldTableTypeEnum.HIST_QUANTILE.getValue(),
                bondType, spreadDate, quantileType, startDate, endDate);
        List<PgBondYieldSpreadTraceBO> pgBondYieldTraceBOList = pgBondYieldSpreadTraceDTO.getPgBondYieldSpreadTraceBOList();
        return convertToTraceTabResponse(userId, bondType, spreadDate, pgBondYieldTraceBOList, pgBondYieldSpreadTraceDTO.getStartDate(), pgBondYieldSpreadTraceDTO.getEndDate());
    }

    @Override
    public Date maxSpreadDate(){
        BondYieldPanoramaTraceSpreadDateDTO maxSpreadDate = getMaxSpreadDate();
        return maxSpreadDate.maxSpreadDate().orElseGet(() -> {
            logger.warn("[利差追踪] maxSpreadDate_empty");
            return Date.valueOf(LocalDate.now().minusDays(1));
        });
    }

    private BondYieldPanoramaTraceSpreadDateDTO getMaxSpreadDate() {
        String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String redisKey = String.format(TRACE_SPREAD_DATE_KEY, now);
        String spreadDateJson = stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(spreadDateJson)) {
            return JSON.parseObject(spreadDateJson, BondYieldPanoramaTraceSpreadDateDTO.class);
        }
        BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO = new BondYieldPanoramaTraceSpreadDateDTO();
        pgBondYieldSpreadTraceAbsDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
        pgBondYieldSpreadTraceQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
        pgBondYieldSpreadTraceChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
        if (ObjectUtils.anyNotNull(spreadDateDTO.getAbsSpreadDate(), spreadDateDTO.getChangeSpreadDate(), spreadDateDTO.getQuantileSpreadDate())) {
            stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(spreadDateDTO), YieldSpreadConst.TWO_DAYS, TimeUnit.DAYS);
        }
        return spreadDateDTO;
    }

    /**
     * 利差跟踪区间变动
     *
     * @param userId     用户id
     * @param bondType   债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充
     * @param spreadDate 利率时间
     * @param changeType 变动类型(1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动 6 一日变动 7 两周变动)
     * @param startDate  自定义开始时间
     * @param endDate    自定义结束时间
     * @return 利差追踪区间变动返回前端
     */
    @Override
    public List<YieldSpreadTraceTabResponseDTO> listYieldSpreadTraceChange(Long userId, Integer bondType, Date spreadDate, Integer changeType, Date startDate, Date endDate) {
        PgBondYieldSpreadTraceDTO pgBondYieldSpreadTraceDTO = this.listPgBondYieldSpreadTraceBOs(BondYieldTableTypeEnum.INTERVAL_CHANGE.getValue(),
                bondType, spreadDate, changeType, startDate, endDate);
        List<PgBondYieldSpreadTraceBO> pgBondYieldTraceBOList = pgBondYieldSpreadTraceDTO.getPgBondYieldSpreadTraceBOList();
        return convertToTraceTabResponse(userId, bondType, spreadDate, pgBondYieldTraceBOList, pgBondYieldSpreadTraceDTO.getStartDate(), pgBondYieldSpreadTraceDTO.getEndDate());
    }

    /**
     * 根据自定义时间范围实时获取计算 利差追踪 区间变动
     *
     * @param bondType  债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充
     * @param startDate 自定义时间范围开始时间
     * @param endDate   自定义时间范围结束时间
     * @return 利差追踪 区间变动 数据
     */
    private List<PgBondYieldSpreadTraceBO> listPgBondYieldSpreadTraceOfChange(Integer bondType, Date startDate, Date endDate) {
        List<PgBondYieldSpreadTraceChangeDO> changeList = Lists.newArrayList();


        List<Integer> chartTypes = Arrays.stream(YieldSpreadChartTypeEnum.values()).map(YieldSpreadChartTypeEnum::getValue).collect(Collectors.toList());
        // 区间变动不需要国债数据
        // 结束日期数据
        List<PgBondYieldSpreadTraceAbsDO> endBondYieldSpreadTraceList =
                pgBondYieldSpreadTraceAbsDAO.listBondYieldSpreadTraces(endDate, chartTypes, bondType);
        if (CollectionUtils.isEmpty(endBondYieldSpreadTraceList)) {
            return Lists.newArrayList();
        }
        Map<String, PgBondYieldSpreadTraceAbsDO> endTraceAbsMap =
                endBondYieldSpreadTraceList.stream().collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        // 开始日期数据
        List<PgBondYieldSpreadTraceAbsDO> beforeBondYieldSpreadTraceList = pgBondYieldSpreadTraceAbsDAO.listBondYieldSpreadTraces(startDate, chartTypes, bondType);
        if (CollectionUtils.isEmpty(beforeBondYieldSpreadTraceList)) {
            return Lists.newArrayList();
        }
        Map<String, PgBondYieldSpreadTraceAbsDO> beforeTraceAbsMap =
                beforeBondYieldSpreadTraceList.stream().collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        for (Map.Entry<String, PgBondYieldSpreadTraceAbsDO> endEntry : endTraceAbsMap.entrySet()) {
            PgBondYieldSpreadTraceAbsDO beforeTraceAbs = beforeTraceAbsMap.get(endEntry.getKey());
            PgBondYieldSpreadTraceAbsDO endTraceAbs = endEntry.getValue();
            Optional<PgBondYieldSpreadTraceChangeDO> change =
                    buildBondYieldSpreadTraceChange(endTraceAbs, beforeTraceAbs, null, null, getSafeSubtractFunction(endTraceAbs.getChartType()));
            change.ifPresent(changeList::add);
        }

        return BeanCopyUtils.copyList(changeList, PgBondYieldSpreadTraceBO.class);
    }

    private BiFunction<BigDecimal, BigDecimal, Optional<BigDecimal>> getSafeSubtractFunction(Integer chartType) {
        if (Integer.valueOf(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue()).equals(chartType)) {
            return this::safeSubtractToBp;
        } else {
            return this::safeSubtract;
        }
    }

    private Date getChangeStartDate(Date issueDate, BondYieldIntervalChangeTypeEnum changeType) {
        LocalDate startDate = changeType.getIntervalStartDate(issueDate.toLocalDate());
        // 获取最近一天的工作日，包含自己
        Date workDate = holidayService.latestWorkDay(Date.valueOf(startDate), 0);
        Date minStartDate = changeType.getIntervalMinStartDate(startDate);
        return workDate.before(minStartDate) ? minStartDate : workDate;
    }

    /**
     * 获取利差追踪数据
     *
     * @param tableType      类型 1 绝对值 2 历史分位 3 区间变动
     * @param bondType       债券品种  1 国债, 2 国开债, 3 地方政府债, 4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险
     * @param issueDate      利率时间
     * @param tableTypeParam tableType=2时,为 BondYieldTableTypeEnum 枚举value值;tableType=3时,为 BondYieldIntervalChangeTypeEnum 枚举value值;
     * @param startDate      tableType=2 或3 自定义时间范围 开始时间
     * @param endDate        tableType=2 或3 自定义时间范围 结束时间
     * @return 利差追踪数据封装实体类
     */
    private PgBondYieldSpreadTraceDTO listPgBondYieldSpreadTraceBOs(Integer tableType, Integer bondType,
                                                                    Date issueDate, Integer tableTypeParam, Date startDate, Date endDate) {
        BondYieldTableTypeEnum spreadTypeEnum = ITextValueEnum.getEnum(BondYieldTableTypeEnum.class, tableType);
        YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
        List<YieldSpreadCurveCodeEnum> spreadCurveCodeEnumList = YieldSpreadCurveCodeEnum.getCurveCodesEnumByBondType(bondTypeEnum);

        List<PgBondYieldSpreadTraceBO> pgBondYieldSpreadTraceBOList = Lists.newArrayList();

        if (Objects.equals(spreadTypeEnum, BondYieldTableTypeEnum.INTERVAL_CHANGE)) {
            //区间变动
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                pgBondYieldSpreadTraceBOList = this.listPgBondYieldSpreadTraceOfChange(bondType, startDate, endDate);
            } else {
                BondYieldIntervalChangeTypeEnum changeTypeEnum = ITextValueEnum.getEnum(BondYieldIntervalChangeTypeEnum.class, tableTypeParam);
                pgBondYieldSpreadTraceBOList =
                        pgBondYieldSpreadTraceChangeDAO.listYieldSpreadTraces(bondTypeEnum, changeTypeEnum, issueDate);
                startDate = this.getChangeStartDate(issueDate, changeTypeEnum);
                endDate = issueDate;
            }
            pgBondYieldSpreadTraceBOList = pgBondYieldSpreadTraceBOList.stream()
                    .peek(change -> change.setTableType(BondYieldTableTypeEnum.INTERVAL_CHANGE.getValue())).collect(Collectors.toList());
        } else if (Objects.equals(spreadTypeEnum, BondYieldTableTypeEnum.ABS)) {
            List<PgBondYieldSpreadTraceBO> absTraceList =
                    pgBondYieldSpreadTraceAbsDAO.listYieldSpreadTraces(bondTypeEnum, issueDate);
            pgBondYieldSpreadTraceBOList = absTraceList.stream().peek(abs -> abs.setTableType(BondYieldTableTypeEnum.ABS.getValue())).collect(Collectors.toList());
        } else if (Objects.equals(spreadTypeEnum, BondYieldTableTypeEnum.HIST_QUANTILE)) {
            //历史分位
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                pgBondYieldSpreadTraceBOList = this.listPgBondYieldSpreadTraceOfQuantile(bondType, issueDate, startDate, endDate);
            } else {
                SpreadQuantileTypeEnum quantileTypeEnum = EnumUtils.ofNullable(SpreadQuantileTypeEnum.class, tableTypeParam).orElse(SpreadQuantileTypeEnum.THREE_YEARS_QUANTILE);
                List<PgBondYieldSpreadTraceBO> quantileTraceList =
                        pgBondYieldSpreadTraceQuantileDAO.listYieldSpreadTraces(bondTypeEnum, issueDate, quantileTypeEnum.getValue());
                pgBondYieldSpreadTraceBOList = quantileTraceList.stream()
                        .peek(quantile -> quantile.setTableType(BondYieldTableTypeEnum.HIST_QUANTILE.getValue())).collect(Collectors.toList());
                startDate = yieldSpreadCommonService.getQuantileStartDate(issueDate, quantileTypeEnum);
                endDate = issueDate;
            }
        }
        PgBondYieldSpreadTraceDTO pgBondYieldSpreadTraceDTO = new PgBondYieldSpreadTraceDTO();
        pgBondYieldSpreadTraceDTO.setPgBondYieldSpreadTraceBOList(this.fillInMissingData(spreadCurveCodeEnumList, pgBondYieldSpreadTraceBOList));
        pgBondYieldSpreadTraceDTO.setStartDate(startDate);
        pgBondYieldSpreadTraceDTO.setEndDate(endDate);
        return pgBondYieldSpreadTraceDTO;
    }

    /**
     * 根据自定义时间范围实时获取计算 利差追踪 历史分位
     *
     * @param bondType  债券类型(4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单, 14 保险资本补充
     * @param issueDate 利率时间
     * @param startDate 自定义时间范围开始时间
     * @param endDate   自定义时间范围结束时间
     * @return 利差追踪 历史分位 数据
     */
    private List<PgBondYieldSpreadTraceBO> listPgBondYieldSpreadTraceOfQuantile(Integer bondType, final Date issueDate, Date startDate, Date endDate) {
        List<PgSpreadTraceQuantileStatisticsViewDO> pgSpreadTraceQuantileStatisticsViewDOS =
                pgBondYieldSpreadTraceAbsDAO.listTraceQuantileViews(startDate, endDate, issueDate, bondType);
        return pgSpreadTraceQuantileStatisticsViewDOS.stream()
                .map(view -> {
                    PgBondYieldSpreadTraceBO pgBondYieldSpreadTraceBO = new PgBondYieldSpreadTraceBO();
                    pgBondYieldSpreadTraceBO.setBondType(bondType);
                    pgBondYieldSpreadTraceBO.setTableType(BondYieldTableTypeEnum.HIST_QUANTILE.getValue());
                    pgBondYieldSpreadTraceBO.setIssueDate(issueDate);
                    pgBondYieldSpreadTraceBO.setStartDate(startDate);
                    fillSpreadTraceBOByStatisticsView(view, pgBondYieldSpreadTraceBO);
                    return pgBondYieldSpreadTraceBO;
                }).collect(Collectors.toList());
    }

    private void fillSpreadTraceBOByStatisticsView(PgSpreadTraceQuantileStatisticsViewDO pgSpreadTraceQuantileStatisticsViewDO, PgBondYieldSpreadTraceBO pgBondYieldSpreadTraceBO) {
        pgBondYieldSpreadTraceBO.setCurveCode(pgSpreadTraceQuantileStatisticsViewDO.getCurveCode());
        pgBondYieldSpreadTraceBO.setChartType(pgSpreadTraceQuantileStatisticsViewDO.getChartType());
        // 1月
        Integer ytm1MLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm1MLessIssueCount();
        Integer ytm1MCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm1MCount();
        pgBondYieldSpreadTraceBO.setYtm1M(CalculationHelper.safeCalPercentRankIgnore(ytm1MLessIssueCount, ytm1MCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 3月
        Integer ytm3MLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm3MLessIssueCount();
        Integer ytm3MCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm3MCount();
        pgBondYieldSpreadTraceBO.setYtm3M(CalculationHelper.safeCalPercentRankIgnore(ytm3MLessIssueCount, ytm3MCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 6月
        Integer ytm6MLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm6MLessIssueCount();
        Integer ytm6MCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm6MCount();
        pgBondYieldSpreadTraceBO.setYtm6M(CalculationHelper.safeCalPercentRankIgnore(ytm6MLessIssueCount, ytm6MCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 9月
        Integer ytm9MLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm9MLessIssueCount();
        Integer ytm9MCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm9MCount();
        pgBondYieldSpreadTraceBO.setYtm9M(CalculationHelper.safeCalPercentRankIgnore(ytm9MLessIssueCount, ytm9MCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 1年
        Integer ytm1YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm1YLessIssueCount();
        Integer ytm1YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm1YCount();
        pgBondYieldSpreadTraceBO.setYtm1Y(CalculationHelper.safeCalPercentRankIgnore(ytm1YLessIssueCount, ytm1YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 2年
        Integer ytm2YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm2YLessIssueCount();
        Integer ytm2YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm2YCount();
        pgBondYieldSpreadTraceBO.setYtm2Y(CalculationHelper.safeCalPercentRankIgnore(ytm2YLessIssueCount, ytm2YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 3年
        Integer ytm3YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm3YLessIssueCount();
        Integer ytm3YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm3YCount();
        pgBondYieldSpreadTraceBO.setYtm3Y(CalculationHelper.safeCalPercentRankIgnore(ytm3YLessIssueCount, ytm3YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 5年
        Integer ytm5YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm5YLessIssueCount();
        Integer ytm5YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm5YCount();
        pgBondYieldSpreadTraceBO.setYtm5Y(CalculationHelper.safeCalPercentRankIgnore(ytm5YLessIssueCount, ytm5YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 7年
        Integer ytm7YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm7YLessIssueCount();
        Integer ytm7YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm7YCount();
        pgBondYieldSpreadTraceBO.setYtm7Y(CalculationHelper.safeCalPercentRankIgnore(ytm7YLessIssueCount, ytm7YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 10年
        Integer ytm10YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm10YLessIssueCount();
        Integer ytm10YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm10YCount();
        pgBondYieldSpreadTraceBO.setYtm10Y(CalculationHelper.safeCalPercentRankIgnore(ytm10YLessIssueCount, ytm10YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 15年
        Integer ytm15YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm15YLessIssueCount();
        Integer ytm15YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm15YCount();
        pgBondYieldSpreadTraceBO.setYtm15Y(CalculationHelper.safeCalPercentRankIgnore(ytm15YLessIssueCount, ytm15YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 20年
        Integer ytm20YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm20YLessIssueCount();
        Integer ytm20YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm20YCount();
        pgBondYieldSpreadTraceBO.setYtm20Y(CalculationHelper.safeCalPercentRankIgnore(ytm20YLessIssueCount, ytm20YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 30年
        Integer ytm30YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm30YLessIssueCount();
        Integer ytm30YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm30YCount();
        pgBondYieldSpreadTraceBO.setYtm30Y(CalculationHelper.safeCalPercentRankIgnore(ytm30YLessIssueCount, ytm30YCount, TRACE_QUANTILE_SCALE).orElse(null));
        // 50年
        Integer ytm50YLessIssueCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm50YLessIssueCount();
        Integer ytm50YCount = pgSpreadTraceQuantileStatisticsViewDO.getYtm50YCount();
        pgBondYieldSpreadTraceBO.setYtm50Y(CalculationHelper.safeCalPercentRankIgnore(ytm50YLessIssueCount, ytm50YCount, TRACE_QUANTILE_SCALE).orElse(null));
    }

    private List<PgBondYieldSpreadTraceBO> fillInMissingData(
            List<YieldSpreadCurveCodeEnum> spreadCurveCodeEnumList, List<PgBondYieldSpreadTraceBO> pgBondYieldPanoramaBOList) {
        // 因为等级利差没有最高等级-最高等级，所以补充错差数据时要排除最高评级
        Optional<YieldSpreadCurveCodeEnum> minCurveCodeEnum = spreadCurveCodeEnumList.stream().min(Comparator.comparing(Enum::ordinal));
        List<YieldSpreadCurveCodeEnum> gradeCodeEnumList =
                spreadCurveCodeEnumList.stream().filter(x -> !minCurveCodeEnum.get().equals(x)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pgBondYieldPanoramaBOList) && spreadCurveCodeEnumList.size() < pgBondYieldPanoramaBOList.size()) {
            List<PgBondYieldSpreadTraceBO> finalPgBondYieldPanoramaBOList = BeanCopyUtils.copyList(pgBondYieldPanoramaBOList, PgBondYieldSpreadTraceBO.class);
            pgBondYieldPanoramaBOList.stream().collect(Collectors.groupingBy(PgBondYieldSpreadTraceBO::getChartType))
                    .forEach((key, value) -> {
                        List<YieldSpreadCurveCodeEnum> yieldSpreadCurveCodeEnumList = YieldSpreadChartTypeEnum.GRADE_SPREAD.getValue() == key ?
                                gradeCodeEnumList : spreadCurveCodeEnumList;
                        List<Integer> curveCodeByDBList = value.stream().map(PgBondYieldSpreadTraceBO::getCurveCode).collect(Collectors.toList());
                        PgBondYieldSpreadTraceBO bondYieldSpreadTraceBO = value.get(0);
                        List<PgBondYieldSpreadTraceBO> curveCodeTraceBOList = yieldSpreadCurveCodeEnumList.stream()
                                .filter(x -> !curveCodeByDBList.contains(x.getValue())).map(x -> {
                                    PgBondYieldSpreadTraceBO pgBondYieldSpreadTraceBO = new PgBondYieldSpreadTraceBO();
                                    pgBondYieldSpreadTraceBO.setCurveCode(x.getValue());
                                    pgBondYieldSpreadTraceBO.setBondType(bondYieldSpreadTraceBO.getBondType());
                                    pgBondYieldSpreadTraceBO.setTableType(bondYieldSpreadTraceBO.getTableType());
                                    pgBondYieldSpreadTraceBO.setChartType(key);
                                    pgBondYieldSpreadTraceBO.setIssueDate(bondYieldSpreadTraceBO.getIssueDate());
                                    return pgBondYieldSpreadTraceBO;
                                }).collect(Collectors.toList());
                        finalPgBondYieldPanoramaBOList.addAll(curveCodeTraceBOList);
                    });
            pgBondYieldPanoramaBOList = finalPgBondYieldPanoramaBOList;
        }
        // 城投的品种利差：AA（2）基本没有任何值所以忽略
        if (minCurveCodeEnum.isPresent() && minCurveCodeEnum.get().getBondType().equals(YieldPanoramaBondTypeEnum.URBAN_BOND)) {
            pgBondYieldPanoramaBOList = pgBondYieldPanoramaBOList.stream().filter(x -> !(Objects.equals(x.getChartType(), YieldSpreadChartTypeEnum.VARIETY_SPREAD.getValue())
                    && Objects.equals(x.getCurveCode(), YieldSpreadCurveCodeEnum.CHINA_BOND_CT_AA_TWO.getValue()))).collect(Collectors.toList());
        }
        return pgBondYieldPanoramaBOList;
    }

    private void dealExelWriter(boolean hasCbPermission, Map<String, Object> excelShow,
                                YieldPanoramaBondTypeEnum bondTypeEnum, ExcelWriter excelWriter,
                                List<PgBondYieldSpreadTraceBO> spreadTraceBOList,
                                WriteSheet writeSheet, YieldSpreadChartTypeEnum yieldSpreadChartTypeEnum,
                                List<PeriodEnum> periodEnums) {
        if (CollectionUtils.isEmpty(spreadTraceBOList)) {
            excelShow.put(yieldSpreadChartTypeEnum.getExcelDataName() + "Name", yieldSpreadChartTypeEnum.getText());
            return;
        }
        List<PeriodExcelFillDataBO> periodExcelFillDataBOList = listPeriodExcelFillData(bondTypeEnum, yieldSpreadChartTypeEnum, periodEnums);


        Map<Integer, List<PgBondYieldSpreadTraceBO>> tableTypeTpSpreadTraceBOMap = spreadTraceBOList.stream()
                .collect(Collectors.groupingBy(PgBondYieldSpreadTraceBO::getTableType));
        // 特殊类型标题填充处理
        getExcelDataNameByBondType(bondTypeEnum, yieldSpreadChartTypeEnum).
                ifPresent(name -> excelShow.put(yieldSpreadChartTypeEnum.getExcelDataName() + "Name", name));
        List<YieldSpreadCurveCodeEnum> spreadCurveCodeEnumList = YieldSpreadCurveCodeEnum.getCurveCodesEnumByBondType(bondTypeEnum);
        // 数据填充处理
        for (Map.Entry<Integer, List<PgBondYieldSpreadTraceBO>> tableTypeToBOEntry : tableTypeTpSpreadTraceBOMap.entrySet()) {
            List<YieldSpreadTraceDataExportExcelDTO> excelDTOList = tableTypeToBOEntry.getValue().stream()
                    .map(x -> BondYieldSpreadBuilder.excelBuilder(hasCbPermission, x))
                    .sorted(Comparator.comparing(YieldSpreadTraceDataExportExcelDTO::getSort))
                    .collect(Collectors.toList());
            if (YieldSpreadChartTypeEnum.GRADE_SPREAD.equals(yieldSpreadChartTypeEnum)) {
                String maxRating = spreadCurveCodeEnumList.stream().min(Comparator.comparing(Enum::ordinal))
                        .map(YieldSpreadCurveCodeEnum::getText).orElse(StringUtils.EMPTY);
                excelDTOList.stream().filter(x -> !Objects.equals(maxRating, x.getTypeName()))
                        .forEach(x -> x.setTypeName(String.format("(%s)-(%s)", x.getTypeName(), maxRating)));
            } else if (YieldSpreadChartTypeEnum.VARIETY_SPREAD_SUB_CB.equals(yieldSpreadChartTypeEnum)) {
                excelDTOList.forEach(x ->
                        x.setTypeName(String.format(YieldSpreadChartTypeEnum.VARIETY_SPREAD_SUB_CB.getExcelDataDec(), x.getTypeName()))
                );
            }
            fillExcelTraceData(excelWriter, writeSheet, yieldSpreadChartTypeEnum, tableTypeToBOEntry.getKey(), excelDTOList, periodExcelFillDataBOList);
        }
    }

    private List<PeriodExcelFillDataBO> listPeriodExcelFillData(YieldPanoramaBondTypeEnum bondTypeEnum,
                                                                YieldSpreadChartTypeEnum yieldSpreadChartTypeEnum,
                                                                List<PeriodEnum> periodEnums) {
        List<PeriodExcelFillDataBO> periodExcelFillDataBOList = Lists.newArrayList();
        if (Objects.isNull(bondTypeEnum) || Objects.isNull(yieldSpreadChartTypeEnum) || CollectionUtils.isEmpty(periodEnums)) {
            return periodExcelFillDataBOList;
        }
        if (YieldSpreadChartTypeEnum.TENOR_SPREAD.equals(yieldSpreadChartTypeEnum)) {
            periodExcelFillDataBOList = PeriodEnum.listPeriodTraceBy(periodEnums, bondTypeEnum).stream().map(periodEnum -> {
                PeriodExcelFillDataBO periodExcelFillDataBO = new PeriodExcelFillDataBO();
                String tracePeriodDesc = ITracePeriodCommonEnum.getTracePeroidByPeroidEnum(bondTypeEnum.getTracePeriodEnum().orElse(null), periodEnum)
                        .map(ITracePeriodCommonEnum::getDesc).orElse(null);
                periodExcelFillDataBO.setPeriod(tracePeriodDesc);
                periodExcelFillDataBO.setYieldPeriodFill(String.format(periodEnum.getPeriodExcelFill(), yieldSpreadChartTypeEnum.getExcelDataName() + "%s"));
                return periodExcelFillDataBO;
            }).collect(Collectors.toList());
        } else {
            periodExcelFillDataBOList = periodEnums.stream().map(periodEnum -> {
                PeriodExcelFillDataBO periodExcelFillDataBO = new PeriodExcelFillDataBO();
                periodExcelFillDataBO.setPeriod(periodEnum.getText());
                periodExcelFillDataBO.setYieldPeriodFill(String.format(periodEnum.getPeriodExcelFill(), yieldSpreadChartTypeEnum.getExcelDataName() + "%s"));
                return periodExcelFillDataBO;
            }).collect(Collectors.toList());
        }
        return periodExcelFillDataBOList;
    }

    private void fillExcelTraceData(ExcelWriter excelWriter, WriteSheet writeSheet,
                                    YieldSpreadChartTypeEnum yieldSpreadChartTypeEnum, Integer tableType,
                                    List<YieldSpreadTraceDataExportExcelDTO> excelDTOList, List<PeriodExcelFillDataBO> periodExcelFillDataBOList) {
        if (!ObjectUtils.allNotNull(excelWriter, writeSheet, yieldSpreadChartTypeEnum, tableType, excelDTOList, periodExcelFillDataBOList)) {
            return;
        }
        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
        List<PeriodExcelFillDataBO> curPeriodExcelFillDataBOList = periodExcelFillDataBOList.stream().map(fillDataBO -> {
            PeriodExcelFillDataBO curDataBO = new PeriodExcelFillDataBO();
            curDataBO.setPeriod(fillDataBO.getPeriod());
            curDataBO.setYieldPeriodFill(String.format(fillDataBO.getYieldPeriodFill(),
                    ITextValueEnum.getEnum(BondYieldTableTypeEnum.class, tableType).getExcelPlaceholder()));
            return curDataBO;
        }).collect(Collectors.toList());
        if (tableType == BondYieldTableTypeEnum.ABS.getValue()) {
            excelWriter.fill(new FillWrapper(String.format(TRACE_EXPORT_FILL, yieldSpreadChartTypeEnum.getExcelDataName(), ""),
                    curPeriodExcelFillDataBOList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper(yieldSpreadChartTypeEnum.getExcelDataName(), excelDTOList), writeSheet);
            excelWriter.fill(new FillWrapper(yieldSpreadChartTypeEnum.getExcelDataName() + "Type", excelDTOList), writeSheet);
        } else if (tableType == BondYieldTableTypeEnum.HIST_QUANTILE.getValue()) {
            String excelPlaceholder = BondYieldTableTypeEnum.HIST_QUANTILE.getExcelPlaceholder();
            excelWriter.fill(new FillWrapper(String.format(TRACE_EXPORT_FILL, yieldSpreadChartTypeEnum.getExcelDataName(), excelPlaceholder),
                    curPeriodExcelFillDataBOList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper(yieldSpreadChartTypeEnum.getExcelDataName() + excelPlaceholder
                    , excelDTOList), writeSheet);
            excelWriter.fill(new FillWrapper(yieldSpreadChartTypeEnum.getExcelDataName()
                    + excelPlaceholder + "Type", excelDTOList), writeSheet);
        } else {
            String excelPlaceholder = BondYieldTableTypeEnum.INTERVAL_CHANGE.getExcelPlaceholder();
            excelWriter.fill(new FillWrapper(String.format(TRACE_EXPORT_FILL, yieldSpreadChartTypeEnum.getExcelDataName(), excelPlaceholder),
                    curPeriodExcelFillDataBOList), fillConfig, writeSheet);
            excelWriter.fill(new FillWrapper(yieldSpreadChartTypeEnum.getExcelDataName() + excelPlaceholder, excelDTOList), writeSheet);
            excelWriter.fill(new FillWrapper(yieldSpreadChartTypeEnum.getExcelDataName() + excelPlaceholder + "Type", excelDTOList), writeSheet);
        }
    }

    private Optional<String> getExcelDataNameByBondType(YieldPanoramaBondTypeEnum bondTypeEnum, YieldSpreadChartTypeEnum yieldSpreadChartTypeEnum) {
        if (Objects.isNull(bondTypeEnum) || Objects.isNull(yieldSpreadChartTypeEnum)) {
            return Optional.empty();
        }
        String excelDataName;
        if (Objects.equals(YieldPanoramaBondTypeEnum.SECURITIES_SUB_BOND, bondTypeEnum)
                && Objects.equals(yieldSpreadChartTypeEnum, YieldSpreadChartTypeEnum.TERM_SPREAD)) {
            excelDataName = String.format(yieldSpreadChartTypeEnum.getExcelDataDec(), bondTypeEnum.getText(), "证券公司");
        } else if (Arrays.asList(YieldPanoramaBondTypeEnum.BANK_SECONDARY_CAPITAL_BOND, YieldPanoramaBondTypeEnum.BANK_PERPETUAL_BOND)
                .contains(bondTypeEnum) && Objects.equals(yieldSpreadChartTypeEnum, YieldSpreadChartTypeEnum.TERM_SPREAD)) {
            excelDataName = String.format(yieldSpreadChartTypeEnum.getExcelDataDec(), bondTypeEnum.getText(), "银行");
        } else if (Objects.equals(YieldPanoramaBondTypeEnum.INTEREST_RATE_BOND, bondTypeEnum)) {
            excelDataName = yieldSpreadChartTypeEnum.getText();
        } else {
            excelDataName = String.format(yieldSpreadChartTypeEnum.getExcelDataDec(), bondTypeEnum.getText());
        }
        return Optional.ofNullable(excelDataName);
    }

    @Override
    public int syncHistBondYieldSpreadTrace(Date startDate) {
        Date endDate = Date.valueOf(LocalDate.now());
        return this.syncBondYieldSpreadTrace(startDate, endDate);
    }

    @Override
    public int syncBondYieldSpreadTraceChange(Date startDate) {
        AtomicInteger effectRows = new AtomicInteger();
        Date endDate = Date.valueOf(LocalDate.now());
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            effectRows.addAndGet(this.calBondYieldSpreadTraceChange(Date.valueOf(beginDate)));
        }
        this.cacheLineChart();
        return effectRows.get();
    }

    @Override
    public int syncBondYieldSpreadTraceQuantile(Date startDate) {
        AtomicInteger effectRows = new AtomicInteger();
        Date endDate = Date.valueOf(LocalDate.now());
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
            effectRows.addAndGet(this.calBondYieldSpreadTraceQuantile(Date.valueOf(beginDate)));
            logger.info("syncBondYieldSpreadTraceQuantile 同步利差追踪分位日期:{}", beginDate);
        }
        this.cacheLineChart();
        return effectRows.get();
    }

    @Override
    public YieldSpreadTraceLineChartPeriodResponseDTO lineChartWithPeriod(@NonNull Integer bondType, @NonNull Integer chartType,
                                                                          @NonNull Integer periodType, @NonNull Date startDate,
                                                                          @NonNull Date endDate, Long userid) {
        String key = String.format(PERIOD_LINE_CHART_KEY, bondType, chartType, periodType);
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(value)) {
            return new YieldSpreadTraceLineChartPeriodResponseDTO();
        }
        YieldSpreadTraceLineChartDTO yieldSpreadTraceLineChartDTO = JSON.parseObject(ZipUtils.unzipBase64(value), YieldSpreadTraceLineChartDTO.class);
        Date[] issueDates = yieldSpreadTraceLineChartDTO.getIssueDates();
        Integer startIndex = getStartDateIndex(issueDates, startDate);
        Integer endIndex = getEndDateIndex(issueDates, endDate);

        YieldSpreadTraceLineChartPeriodResponseDTO response = this.convertToPeriodResponse(yieldSpreadTraceLineChartDTO, startIndex, endIndex);
        // 权限控制,不是产业债 & 不是保险资本补充 &是到期收益率
        YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
        if (!YieldPanoramaBondTypeEnum.getHasPermissionBondTypeEnums().contains(bondTypeEnum)) {
            this.permissionProcessingWithPeriod(userid, response);
        }
        return response;
    }

    /**
     * 利差追踪-品种类折线图(同品种)
     *
     * @param bondType  债券类型(17利率债)
     * @param chartType 图类型，1 到期收益率 2 信用利差 3 等级利差 4 期限利差 5 品种利差 6 条款利差
     * @param curveCode 利差追踪:1 国债 2 国开 4国开 203 农发 204 进出口 51 地方债
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userid    userid
     * @return {@link YieldSpreadTraceLineChartRatingResponseDTO}
     */
    @Override
    public YieldSpreadTraceLineChartRatingResponseDTO lineChartVarietyWithVarieties(Integer bondType, Integer chartType, Integer curveCode,
                                                                                    Date startDate, Date endDate, Long userid) {
        String key = String.format(VARIETIES_LINE_CHART_KEY, bondType, chartType, curveCode);
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(value)) {
            return new YieldSpreadTraceLineChartRatingResponseDTO();
        }
        YieldSpreadTraceLineChartDTO yieldSpreadTraceLineChartDTO = JSON.parseObject(ZipUtils.unzipBase64(value), YieldSpreadTraceLineChartDTO.class);
        Date[] issueDates = yieldSpreadTraceLineChartDTO.getIssueDates();
        Integer startIndex = getStartDateIndex(issueDates, startDate);
        Integer endIndex = getEndDateIndex(issueDates, endDate);
        YieldSpreadTraceLineChartRatingResponseDTO response = this.convertToRatingResponse(yieldSpreadTraceLineChartDTO, startIndex, endIndex);
        // 权限控制,不是产业债
        YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
        if (!YieldPanoramaBondTypeEnum.getHasPermissionBondTypeEnums().contains(bondTypeEnum)) {
            this.permissionProcessingWithRating(userid, response);
        }
        return response;
    }

    /**
     * 利差追踪-品种类折线图(同期限)
     *
     * @param bondType   债券类型(17利率债)
     * @param chartType  图类型，1 到期收益率 2 信用利差 3 等级利差 4 期限利差 5 品种利差 6 条款利差
     * @param periodType 期限类型，期限类型，期限类型，1:1M 3:3M 6:6M 9:9M 12:1Y 24:2Y 36:3Y 60:5Y 84:7Y 120:10Y 180:15Y 240:20Y 360:30Y  600:50Y0,因为只有一个评级，展示所有期限
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param userid     userid
     * @return {@link YieldTraceLineCommonResponseDTO}
     */
    @Override
    public YieldTraceLineCommonResponseDTO lineChartVarietyWithPeriod(Integer bondType, Integer chartType, Integer periodType,
                                                                      Date startDate, Date endDate, Long userid) {
        String key = String.format(PERIOD_LINE_CHART_KEY, bondType, chartType, periodType);
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(value)) {
            return new YieldTraceLineCommonResponseDTO();
        }
        YieldSpreadTraceLineCommonDTO yieldSpreadTraceLineCommonDTO = JSON.parseObject(ZipUtils.unzipBase64(value), YieldSpreadTraceLineCommonDTO.class);
        Date[] issueDates = yieldSpreadTraceLineCommonDTO.getIssueDates();
        Integer startIndex = getStartDateIndex(issueDates, startDate);
        Integer endIndex = getEndDateIndex(issueDates, endDate);

        YieldTraceLineCommonResponseDTO response = this.convertToPeriodOfVarietyResponse(yieldSpreadTraceLineCommonDTO, startIndex, endIndex);
        // 权限控制,不是产业债 & 不是保险资本补充 &是到期收益率
        YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
        if (!YieldPanoramaBondTypeEnum.getHasPermissionBondTypeEnums().contains(bondTypeEnum)) {
            this.permissionProcessingWithPeriodOfVariety(userid, response);
        }
        return response;
    }

    /**
     * 获取时间范围 开始日期的位置
     *
     * @param issueDates 日期范围
     * @param startDate  开始日期
     * @return 开始日期实际对应最近的日期的索引位置
     */
    private Integer getStartDateIndex(Date[] issueDates, Date startDate) {
        Integer startIndex = null;
        if (Objects.nonNull(issueDates)) {
            startIndex = issueDates.length;

            //这样处理原因是可能存在东八区 时区不一致造成的问题
            final Date startIssueDate = Date.valueOf(startDate.toLocalDate());
            for (int i = 0; i < issueDates.length; i++) {
                if (issueDates[i].compareTo(startIssueDate) >= 0) {
                    startIndex = i;
                    break;
                }
            }
        }
        return startIndex;
    }

    /**
     * 获取时间范围 结束日期的位置
     *
     * @param issueDates 日期范围
     * @param endDate    开始日期
     * @return 结束日期实际对应最近的日期的索引位置
     */
    private Integer getEndDateIndex(Date[] issueDates, Date endDate) {
        Integer endIndex = null;
        if (Objects.nonNull(issueDates)) {
            endIndex = 0;
            //这样处理原因是可能存在东八区 时区不一致造成的问题
            final Date endIssueDate = Date.valueOf(endDate.toLocalDate());
            for (int i = issueDates.length - 1; i >= 0; i--) {
                if (issueDates[i].compareTo(endIssueDate) <= 0) {
                    //含头不含尾,所以+1
                    endIndex = i + 1;
                    break;
                }
            }
        }
        return endIndex;
    }

    private YieldSpreadTraceLineChartPeriodResponseDTO convertToPeriodResponse(YieldSpreadTraceLineChartDTO chartDTO, Integer startIndex, Integer endIndex) {
        YieldSpreadTraceLineChartPeriodResponseDTO response = new YieldSpreadTraceLineChartPeriodResponseDTO();
        Optional.ofNullable(chartDTO.getIssueDates()).ifPresent(v -> response.setIssueDates(Arrays.stream(v, startIndex, endIndex).collect(Collectors.toList())));
        Optional.ofNullable(chartDTO.getYtm1Ys())
                .ifPresent(v -> response.setYtm1Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm2Ys())
                .ifPresent(v -> response.setYtm2Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm3Ys())
                .ifPresent(v -> response.setYtm3Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm5Ys())
                .ifPresent(v -> response.setYtm5Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm7Ys())
                .ifPresent(v -> response.setYtm7Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getRatingAAAPluses())
                .ifPresent(v -> response.setRatingAAAPluses(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getRatingAAAs())
                .ifPresent(v -> response.setRatingAAAs(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getRatingAAASubs())
                .ifPresent(v -> response.setRatingAAASubs(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getRatingAAPluses())
                .ifPresent(v -> response.setRatingAAPluses(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getRatingAAs())
                .ifPresent(v -> response.setRatingAAs(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getRatingAASubs())
                .ifPresent(v -> response.setRatingAASubs(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getRatingAATwos())
                .ifPresent(v -> response.setRatingAATwos(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        return response;
    }

    private YieldTraceLineCommonResponseDTO convertToPeriodOfVarietyResponse(YieldSpreadTraceLineCommonDTO chartDTO, Integer startIndex, Integer endIndex) {
        YieldTraceLineCommonResponseDTO response = new YieldTraceLineCommonResponseDTO();
        Optional.ofNullable(chartDTO.getIssueDates()).ifPresent(v -> response.setIssueDates(Arrays.stream(v, startIndex, endIndex).collect(Collectors.toList())));
        List<YieldSpreadTraceLineDataDTO> dataDTOLists = chartDTO.getDataDTOLists();
        if (CollectionUtils.isEmpty(dataDTOLists)) {
            return response;
        }
        List<YieldTraceLineDataResponseDTO> dataResponseList = dataDTOLists.stream().map(dataDTO -> {
            YieldTraceLineDataResponseDTO responseDataDTO = new YieldTraceLineDataResponseDTO();
            responseDataDTO.setTypeCode(dataDTO.getTypeCode());
            responseDataDTO.setTypeName(dataDTO.getTypeName());
            Optional.ofNullable(dataDTO.getYields())
                    .ifPresent(v -> responseDataDTO.setYields(convertDecimalArrayToStrList(v, startIndex, endIndex)));
            return responseDataDTO;
        }).collect(Collectors.toList());
        response.setDataDTOLists(dataResponseList);
        return response;
    }

    private List<String> convertDecimalArrayToStrList(BigDecimal[] array, int startInclusive, int endExclusive) {
        if (Objects.isNull(array) || array.length == 0) {
            return Lists.newArrayList();
        }
        return Arrays.stream(array, startInclusive, endExclusive).map(this::bigDecimalToStringWithNull).collect(Collectors.toList());
    }

    @SuppressWarnings({"squid:MethodCyclomaticComplexity", "squid:S3776"})
    private YieldSpreadTraceLineChartPeriodResponseDTO permissionProcessingWithPeriod(Long userid, YieldSpreadTraceLineChartPeriodResponseDTO response) {
        boolean hasCurrentCbPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        boolean hasHistoryCbPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE);
        // 如果都有权限，则不用做处理
        if (hasHistoryCbPermission && hasCurrentCbPermission) {
            return response;
        }
        // 中债权限分割时间点
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        for (int i = 0; i < response.getIssueDates().size(); i++) {
            Date issueDate = response.getIssueDates().get(i);
            // 历史数据&没历史权限  或者   当前数据&没有当前权限
            boolean isHistoryAndHasCbPermission = issueDate.before(cbPermissionDividingDate) && hasHistoryCbPermission;
            boolean isCurrentAndHasCbPermission = cbPermissionDividingDate.before(issueDate) && hasCurrentCbPermission;
            boolean hasPermission = isHistoryAndHasCbPermission || isCurrentAndHasCbPermission;
            final int index = i;
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm1Ys()) ? null : response.getYtm1Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm2Ys()) ? null : response.getYtm2Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm3Ys()) ? null : response.getYtm3Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm5Ys()) ? null : response.getYtm5Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm7Ys()) ? null : response.getYtm7Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getRatingAAAPluses()) ? null : response.getRatingAAAPluses())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getRatingAAAs()) ? null : response.getRatingAAAs())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getRatingAAASubs()) ? null : response.getRatingAAASubs())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getRatingAAPluses()) ? null : response.getRatingAAPluses())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(org.springframework.util.CollectionUtils.isEmpty(response.getRatingAAs()) ? null : response.getRatingAAs())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getRatingAATwos()) ? null : response.getRatingAATwos())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getRatingAASubs()) ? null : response.getRatingAASubs())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
        }
        return response;
    }


    @SuppressWarnings({"squid:MethodCyclomaticComplexity", "squid:S3776"})
    private YieldTraceLineCommonResponseDTO permissionProcessingWithPeriodOfVariety(Long userid, YieldTraceLineCommonResponseDTO response) {
        boolean hasCurrentCbPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        boolean hasHistoryCbPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE);
        // 如果都有权限，则不用做处理
        if (hasHistoryCbPermission && hasCurrentCbPermission) {
            return response;
        }
        // 中债权限分割时间点
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        for (int i = 0; i < response.getIssueDates().size(); i++) {
            Date issueDate = response.getIssueDates().get(i);
            // 历史数据&没历史权限  或者   当前数据&没有当前权限
            boolean isHistoryAndHasCbPermission = issueDate.before(cbPermissionDividingDate) && hasHistoryCbPermission;
            boolean isCurrentAndHasCbPermission = cbPermissionDividingDate.before(issueDate) && hasCurrentCbPermission;
            boolean hasPermission = isHistoryAndHasCbPermission || isCurrentAndHasCbPermission;
            final int index = i;
            List<YieldTraceLineDataResponseDTO> dataDTOLists = response.getDataDTOLists();
            dataDTOLists.forEach(dataDTO ->
                    Optional.ofNullable(CollectionUtils.isEmpty(dataDTO.getYields()) ? null : dataDTO.getYields())
                            .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission))));
        }
        return response;
    }

    @Override
    public YieldSpreadTraceLineChartRatingResponseDTO lineChartWithRating(@NonNull Integer bondType, @NonNull Integer chartType,
                                                                          @NonNull Integer ratingType, @NonNull Date startDate,
                                                                          @NonNull Date endDate, @NonNull Long userid) {
        String key = String.format(RATING_LINE_CHART_KEY, bondType, chartType, ratingType);
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(value)) {
            return new YieldSpreadTraceLineChartRatingResponseDTO();
        }
        YieldSpreadTraceLineChartDTO yieldSpreadTraceLineChartDTO = JSON.parseObject(ZipUtils.unzipBase64(value), YieldSpreadTraceLineChartDTO.class);
        Date[] issueDates = yieldSpreadTraceLineChartDTO.getIssueDates();
        Integer startIndex = getStartDateIndex(issueDates, startDate);
        Integer endIndex = getEndDateIndex(issueDates, endDate);
        YieldSpreadTraceLineChartRatingResponseDTO response = this.convertToRatingResponse(yieldSpreadTraceLineChartDTO, startIndex, endIndex);
        // 权限控制,不是产业债
        YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
        if (!YieldPanoramaBondTypeEnum.getHasPermissionBondTypeEnums().contains(bondTypeEnum)) {
            this.permissionProcessingWithRating(userid, response);
        }
        return response;
    }

    private YieldSpreadTraceLineChartRatingResponseDTO convertToRatingResponse(YieldSpreadTraceLineChartDTO chartDTO, Integer startIndex, Integer endIndex) {
        YieldSpreadTraceLineChartRatingResponseDTO response = new YieldSpreadTraceLineChartRatingResponseDTO();
        Optional.ofNullable(chartDTO.getIssueDates()).ifPresent(v -> response.setIssueDates(Arrays.stream(v, startIndex, endIndex).collect(Collectors.toList())));
        Optional.ofNullable(chartDTO.getYtm1Ms())
                .ifPresent(v -> response.setYtm1Ms(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm3Ms())
                .ifPresent(v -> response.setYtm3Ms(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm6Ms())
                .ifPresent(v -> response.setYtm6Ms(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm9Ms())
                .ifPresent(v -> response.setYtm9Ms(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm1Ys())
                .ifPresent(v -> response.setYtm1Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm2Ys())
                .ifPresent(v -> response.setYtm2Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm3Ys())
                .ifPresent(v -> response.setYtm3Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm5Ys())
                .ifPresent(v -> response.setYtm5Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm7Ys())
                .ifPresent(v -> response.setYtm7Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm10Ys())
                .ifPresent(v -> response.setYtm10Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm15Ys())
                .ifPresent(v -> response.setYtm15Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm20Ys())
                .ifPresent(v -> response.setYtm20Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm30Ys())
                .ifPresent(v -> response.setYtm30Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        Optional.ofNullable(chartDTO.getYtm50Ys())
                .ifPresent(v -> response.setYtm50Ys(convertDecimalArrayToStrList(v, startIndex, endIndex)));
        return response;
    }

    private String bigDecimalToStringWithNull(BigDecimal value) {
        return Objects.isNull(value) ? null : value.toString();
    }

    @SuppressWarnings({"squid:MethodCyclomaticComplexity", "squid:S3776"})
    private YieldSpreadTraceLineChartRatingResponseDTO permissionProcessingWithRating(Long userid, YieldSpreadTraceLineChartRatingResponseDTO response) {
        boolean hasCurrentCbPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        boolean hasHistoryCbPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE);
        // 如果都有权限，则不用做处理
        if (hasHistoryCbPermission && hasCurrentCbPermission) {
            return response;
        }
        // 中债权限分割时间点
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        for (int i = 0; i < response.getIssueDates().size(); i++) {
            Date issueDate = response.getIssueDates().get(i);
            // 历史数据&没历史权限  或者   当前数据&没有当前权限
            boolean isHistoryAndHasCbPermission = issueDate.before(cbPermissionDividingDate) && hasHistoryCbPermission;
            boolean isCurrentAndHasCbPermission = cbPermissionDividingDate.before(issueDate) && hasCurrentCbPermission;
            boolean hasPermission = isHistoryAndHasCbPermission || isCurrentAndHasCbPermission;
            final int index = i;
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm1Ms()) ? null : response.getYtm1Ms())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm3Ms()) ? null : response.getYtm3Ms())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm6Ms()) ? null : response.getYtm6Ms())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm9Ms()) ? null : response.getYtm9Ms())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm1Ys()) ? null : response.getYtm1Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm2Ys()) ? null : response.getYtm2Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm3Ys()) ? null : response.getYtm3Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm5Ys()) ? null : response.getYtm5Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm7Ys()) ? null : response.getYtm7Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm10Ys()) ? null : response.getYtm10Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm15Ys()) ? null : response.getYtm15Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm20Ys()) ? null : response.getYtm20Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm30Ys()) ? null : response.getYtm30Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
            Optional.ofNullable(CollectionUtils.isEmpty(response.getYtm50Ys()) ? null : response.getYtm50Ys())
                    .ifPresent(v -> v.set(index, CommonUtils.desensitized(v.get(index), hasPermission)));
        }
        return response;
    }

    /**
     * 缓存利差追踪折线图
     */
    @Override
    public void cacheLineChart() {
        logger.info("【BondYieldSpreadTraceService#cacheLineChart】 ===> start....");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        final LocalDate localEndDate = LocalDate.now().minusDays(YieldSpreadConst.ONE_DAY);
        final Date startDate = Date.valueOf(localEndDate.minusYears(YieldSpreadConst.FIVE_YEARS));
        final Date endDate = Date.valueOf(localEndDate);
        final Map<String, String> ratingCacheMap = Maps.newHashMap();
        final Map<String, String> periodCacheMap = Maps.newHashMap();
        for (TraceLineChartKey traceLineChartKey : traceLineChartKeys) {
            YieldPanoramaBondTypeEnum bondTypeEnum = traceLineChartKey.getBondTypeEnum();
            YieldSpreadChartTypeEnum chartTypeEnum = traceLineChartKey.getChartTypeEnum();
            Map<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> collectResult =
                    this.collectAbsTraces(startDate, endDate, bondTypeEnum, chartTypeEnum);
            ratingCacheMap.putAll(this.processCacheWithRating(bondTypeEnum, chartTypeEnum, collectResult));
            ratingCacheMap.putAll(this.processCacheWithVarieties(bondTypeEnum, chartTypeEnum, collectResult));
            periodCacheMap.putAll(this.processCacheWithPeriodOfRating(bondTypeEnum, chartTypeEnum, collectResult));
            periodCacheMap.putAll(this.processCacheWithPeriodOfVarieties(bondTypeEnum, chartTypeEnum, collectResult));
        }
        stringRedisTemplate.opsForValue().multiSet(ratingCacheMap);
        stringRedisTemplate.opsForValue().multiSet(periodCacheMap);
        stopWatch.stop();
        logger.info("【BondYieldSpreadTraceService#cacheLineChart】 ===> end....spend time: {} ms", stopWatch.getTotalTimeMillis());
    }

    /**
     * 导出利差追踪
     *
     * @param httpServletResponse        响应体
     * @param userId                     用户id
     * @param bondYieldTraceExportReqDTO 请求参数
     * @throws IOException Exception
     */
    @Override
    public void exportYieldSpreadTrace(HttpServletResponse httpServletResponse, Long userId
            , BondYieldTraceExportReqDTO bondYieldTraceExportReqDTO) throws IOException {
        ServletOutputStream outputStream = httpServletResponse.getOutputStream();
        ExcelWriter excelWriter = null;
        try {
            Date spreadDate = bondYieldTraceExportReqDTO.getSpreadDate();
            Integer bondType = bondYieldTraceExportReqDTO.getBondType();
            // 获取模板写入工具
            YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
            //获取用户配置期限信息
            List<Integer> periodCodes = userSpreadConfigDAO.getUserSpreadConfigByConfigId(userId, (long) bondTypeEnum.getConfigEnum().getValue(), Integer.class)
                    .map(UserSpreadConfigBO::getConfigDetails).orElseGet(Lists::newArrayList);
            periodCodes.sort(Integer::compareTo);
            List<PeriodEnum> periodEnums = PeriodEnum.getPeriodEnumByValues(periodCodes);

            InputStream inputStream = new ClassPathResource(bondTypeEnum.getTemplatePath()).getInputStream();
            excelWriter = EasyExcelFactory.write(outputStream).withTemplate(inputStream).excelType(ExcelTypeEnum.XLSX).build();
            // 查询写入数据
            List<PgBondYieldSpreadTraceBO> pgBondYieldSpreadTraceBOList = Lists.newArrayList();
            //获取abs数据
            PgBondYieldSpreadTraceDTO pgBondYieldSpreadTraceAbsDTO = this.listPgBondYieldSpreadTraceBOs(BondYieldTableTypeEnum.ABS.getValue(),
                    bondType, spreadDate, null, null, null);

            pgBondYieldSpreadTraceBOList.addAll(pgBondYieldSpreadTraceAbsDTO.getPgBondYieldSpreadTraceBOList());
            //获取历史分位数据
            Date quantileStartDate = bondYieldTraceExportReqDTO.getQuantileStartDate();
            Date quantileEndDate = bondYieldTraceExportReqDTO.getQuantileEndDate();
            Integer quantileType = bondYieldTraceExportReqDTO.getQuantileType();

            PgBondYieldSpreadTraceDTO pgBondYieldSpreadTraceQuantileDTO = this.listPgBondYieldSpreadTraceBOs(BondYieldTableTypeEnum.HIST_QUANTILE.getValue(),
                    bondType, spreadDate, quantileType, quantileStartDate, quantileEndDate);
            List<PgBondYieldSpreadTraceBO> pgBondYielTraceQuantileBOList = pgBondYieldSpreadTraceQuantileDTO.getPgBondYieldSpreadTraceBOList();
            quantileStartDate = pgBondYieldSpreadTraceQuantileDTO.getStartDate();
            quantileEndDate = pgBondYieldSpreadTraceQuantileDTO.getEndDate();
            pgBondYieldSpreadTraceBOList.addAll(pgBondYielTraceQuantileBOList);

            //获取区间变动数据
            Date changeStartDate = bondYieldTraceExportReqDTO.getChangeStartDate();
            Date changeEndDate = bondYieldTraceExportReqDTO.getChangeEndDate();
            Integer changeType = bondYieldTraceExportReqDTO.getChangeType();

            PgBondYieldSpreadTraceDTO pgBondYieldSpreadTraceChangeDTO = this.listPgBondYieldSpreadTraceBOs(BondYieldTableTypeEnum.INTERVAL_CHANGE.getValue(),
                    bondType, spreadDate, changeType, changeStartDate, changeEndDate);

            pgBondYieldSpreadTraceBOList.addAll(pgBondYieldSpreadTraceChangeDTO.getPgBondYieldSpreadTraceBOList());
            changeStartDate = pgBondYieldSpreadTraceChangeDTO.getStartDate();
            changeEndDate = pgBondYieldSpreadTraceChangeDTO.getEndDate();

            ExcelUtils.setExportResponse(httpServletResponse,
                    String.format(FILE_NAME, bondTypeEnum.getText(), spreadDate.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE)));
            WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
            // 获取填充excel标题数据
            Map<String, Object> excelShow = Maps.newHashMap();
            // 填充表格
            Map<Integer, List<PgBondYieldSpreadTraceBO>> chartTypeTpSpreadTraceBOMap = pgBondYieldSpreadTraceBOList
                    .stream().collect(Collectors.groupingBy(PgBondYieldSpreadTraceBO::getChartType));
            bondTypeEnum.getChartTypes().stream().filter(chartType -> !chartTypeTpSpreadTraceBOMap.containsKey(chartType))
                    .forEach(chartType -> chartTypeTpSpreadTraceBOMap.put(chartType, Collections.emptyList()));
            ExcelWriter finalExcelWriter = excelWriter;
            // 获取权限
            Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
            boolean hasCbPermission = spreadDate.before(cbPermissionDividingDate) ?
                    userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                    userService.hasCbPermission(userId, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);

            chartTypeTpSpreadTraceBOMap.forEach((key1, value) -> {
                YieldSpreadChartTypeEnum chartTypeEnum = ITextValueEnum.getEnum(YieldSpreadChartTypeEnum.class, key1);
                boolean shouldDesensitization = !YieldPanoramaBondTypeEnum.getHasPermissionBondTypeEnums().contains(bondTypeEnum);
                dealExelWriter(!shouldDesensitization || hasCbPermission, excelShow, bondTypeEnum, finalExcelWriter, value,
                        writeSheet, chartTypeEnum, periodEnums);
            });
            // 填充标题
            excelShow.put("threeDateRange", String.format(BondYieldSpreadBuilder.STATISTICAL_DATE_RANGE,
                    quantileStartDate.toLocalDate().format(DateTimeFormatter.ISO_DATE), quantileEndDate.toLocalDate().format(DateTimeFormatter.ISO_DATE)));
            excelShow.put("changeDateRange", String.format(BondYieldSpreadBuilder.STATISTICAL_DATE_RANGE,
                    changeStartDate.toLocalDate().format(DateTimeFormatter.ISO_DATE), changeEndDate.toLocalDate().format(DateTimeFormatter.ISO_DATE)));
            excelShow.put("title", "利差追踪");
            FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();
            excelWriter.fill(excelShow, fillConfig, writeSheet);
        } catch (RuntimeException exception) {
            logger.error(exception.getMessage());
            throw exception;
        } finally {
            Optional.ofNullable(excelWriter).ifPresent(ExcelWriter::finish);
            outputStream.flush();
        }
    }

    private Map<String, String> processCacheWithPeriodOfRating(YieldPanoramaBondTypeEnum bondTypeEnum, YieldSpreadChartTypeEnum chartTypeEnum,
                                                               Map<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> collectResult) {
        Map<String, String> cacheMap = Maps.newHashMap();
        if (varietiesBondTypes.contains(bondTypeEnum)) {
            return cacheMap;
        }
        List<PgBondYieldSpreadTraceAbsBO> allTraceList = collectResult.values()
                .stream().flatMap(Collection::stream).sorted(Comparator.comparing(PgBondYieldSpreadTraceAbsBO::getIssueDate)).collect(Collectors.toList());
        if (noRatingBondTypes.contains(bondTypeEnum)) {
            Collection<PgBondYieldSpreadTraceAbsBO> dateGroupAbsMap = allTraceList.stream()
                    .collect(Collectors.toMap(PgBondYieldSpreadTraceAbsBO::getIssueDate, Function.identity(), (o, v) -> o, LinkedHashMap::new)).values();
            YieldSpreadTraceLineChartDTO.Builder builder = YieldSpreadTraceLineChartDTO.builder(dateGroupAbsMap.size());
            dateGroupAbsMap.forEach(builder::withRating);
            String key = String.format(PERIOD_LINE_CHART_KEY, bondTypeEnum.getValue(), chartTypeEnum.getValue(), DEFAULT_CODE);
            cacheMap.put(key, ZipUtils.zipBase64(JSON.toJSONString(builder.build())));
            return cacheMap;
        }
        LinkedHashMap<Date, List<PgBondYieldSpreadTraceAbsBO>> dateGroupAbsMap =
                allTraceList.stream().collect(Collectors.groupingBy(PgBondYieldSpreadTraceAbsBO::getIssueDate, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<PeriodEnum, Function<PgBondYieldSpreadTraceAbsBO, BigDecimal>> functionEntry : periodFunctionMap.entrySet()) {
            YieldSpreadTraceLineChartDTO.Builder builder = YieldSpreadTraceLineChartDTO.builder(dateGroupAbsMap.keySet().size());
            for (Map.Entry<Date, List<PgBondYieldSpreadTraceAbsBO>> absEntry : dateGroupAbsMap.entrySet()) {
                builder.withPeriod(absEntry, functionEntry.getValue(), collectResult.keySet());
            }
            String key = String.format(PERIOD_LINE_CHART_KEY, bondTypeEnum.getValue(), chartTypeEnum.getValue(), functionEntry.getKey().getValue());
            cacheMap.put(key, ZipUtils.zipBase64(JSON.toJSONString(builder.build())));
        }
        return cacheMap;
    }

    private Map<String, String> processCacheWithPeriodOfVarieties(YieldPanoramaBondTypeEnum bondTypeEnum, YieldSpreadChartTypeEnum chartTypeEnum,
                                                                  Map<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> collectResult) {
        Map<String, String> cacheMap = Maps.newHashMap();
        if (!varietiesBondTypes.contains(bondTypeEnum)) {
            return cacheMap;
        }
        List<YieldSpreadCurveCodeEnum> curveCodeEnums = bondTypeEnum.getCurveCodes().stream()
                .map(curveCode -> ITextValueEnum.getEnum(YieldSpreadCurveCodeEnum.class, curveCode))
                .collect(Collectors.toList());

        List<PgBondYieldSpreadTraceAbsBO> allTraceList = collectResult.values()
                .stream().flatMap(Collection::stream).sorted(Comparator.comparing(PgBondYieldSpreadTraceAbsBO::getIssueDate)).collect(Collectors.toList());

        LinkedHashMap<Date, List<PgBondYieldSpreadTraceAbsBO>> dateGroupAbsMap =
                allTraceList.stream().collect(Collectors.groupingBy(PgBondYieldSpreadTraceAbsBO::getIssueDate, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<PeriodEnum, Function<PgBondYieldSpreadTraceAbsBO, BigDecimal>> functionEntry : periodFunctionMap.entrySet()) {
            YieldSpreadTraceLineCommonDTO yieldSpreadTraceLineCommonDTO = new YieldSpreadTraceLineCommonDTO();

            Map<YieldSpreadCurveCodeEnum, List<BigDecimal>> curveCodeMap =
                    curveCodeEnums.stream().collect(Collectors.toMap(key -> key, value -> new ArrayList<>()));
            List<Date> issueDateList = new ArrayList<>(dateGroupAbsMap.keySet().size());
            for (Map.Entry<Date, List<PgBondYieldSpreadTraceAbsBO>> absEntry : dateGroupAbsMap.entrySet()) {
                issueDateList.add(absEntry.getKey());
                for (YieldSpreadCurveCodeEnum curveCodeEnum : curveCodeEnums) {
                    List<BigDecimal> dataList = curveCodeMap.get(curveCodeEnum);
                    Optional<PgBondYieldSpreadTraceAbsBO> pgTraceAbsBOopt = absEntry.getValue().stream()
                            .filter(bondYieldSpreadTraceAbsBO -> bondYieldSpreadTraceAbsBO.getCurveCode().equals(curveCodeEnum.getValue()))
                            .findAny();
                    if (pgTraceAbsBOopt.isPresent()) {
                        dataList.add(functionEntry.getValue().apply(pgTraceAbsBOopt.get()));
                    } else {
                        dataList.add(null);
                    }
                }
            }
            String typeNameFormat = "%s";
            if (YieldSpreadChartTypeEnum.VARIETY_SPREAD_SUB_CB.equals(chartTypeEnum)) {
                curveCodeMap.remove(YieldSpreadCurveCodeEnum.CHINA_BOND);
                typeNameFormat = YieldSpreadChartTypeEnum.VARIETY_SPREAD_SUB_CB.getExcelDataDec();
            }
            final String typeNameFormatFinal = typeNameFormat;
            yieldSpreadTraceLineCommonDTO.setIssueDates(issueDateList.toArray(new Date[0]));
            List<YieldSpreadTraceLineDataDTO> dataDTOLists = curveCodeMap.entrySet().stream()
                    .sorted(Comparator.comparingInt(entry -> entry.getKey().ordinal()))
                    .map(entry -> {
                        YieldSpreadTraceLineDataDTO yieldSpreadTraceLineDataDTO = new YieldSpreadTraceLineDataDTO();
                        yieldSpreadTraceLineDataDTO.setTypeCode(entry.getKey().getValue());
                        yieldSpreadTraceLineDataDTO.setTypeName(String.format(typeNameFormatFinal, entry.getKey().getText()));
                        yieldSpreadTraceLineDataDTO.setYields(entry.getValue().toArray(new BigDecimal[0]));
                        return yieldSpreadTraceLineDataDTO;
                    }).collect(Collectors.toList());
            yieldSpreadTraceLineCommonDTO.setDataDTOLists(dataDTOLists);
            String key = String.format(PERIOD_LINE_CHART_KEY, bondTypeEnum.getValue(), chartTypeEnum.getValue(), functionEntry.getKey().getValue());
            cacheMap.put(key, ZipUtils.zipBase64(JSON.toJSONString(yieldSpreadTraceLineCommonDTO)));
        }

        return cacheMap;
    }

    private Map<String, String> processCacheWithRating(YieldPanoramaBondTypeEnum bondTypeEnum, YieldSpreadChartTypeEnum chartTypeEnum,
                                                       Map<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> collectResult) {
        //没有评级的债券
        final boolean noRateBond = noRatingBondTypes.contains(bondTypeEnum);
        Map<String, String> ratingCacheMap = Maps.newHashMap();
        if (varietiesBondTypes.contains(bondTypeEnum)) {
            return ratingCacheMap;
        }
        for (Map.Entry<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> entry : collectResult.entrySet()) {
            List<PgBondYieldSpreadTraceAbsBO> traces = entry.getValue().stream()
                    .sorted(Comparator.comparing(PgBondYieldSpreadTraceAbsBO::getIssueDate)).collect(Collectors.toList());
            YieldSpreadTraceLineChartDTO.Builder builder = YieldSpreadTraceLineChartDTO.builder(traces.size());
            traces.forEach(builder::withRating);
            Integer rating = noRateBond ? DEFAULT_CODE : YieldSpreadRatingEnum.getRatingByCurveCode(entry.getKey()).getValue();
            String key = String.format(RATING_LINE_CHART_KEY, bondTypeEnum.getValue(), chartTypeEnum.getValue(), rating);
            ratingCacheMap.put(key, ZipUtils.zipBase64(JSON.toJSONString(builder.build())));
        }
        return ratingCacheMap;
    }

    private Map<String, String> processCacheWithVarieties(YieldPanoramaBondTypeEnum bondTypeEnum, YieldSpreadChartTypeEnum chartTypeEnum,
                                                          Map<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> collectResult) {
        Map<String, String> ratingCacheMap = Maps.newHashMap();
        if (!varietiesBondTypes.contains(bondTypeEnum)) {
            return ratingCacheMap;
        }
        for (Map.Entry<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> entry : collectResult.entrySet()) {
            List<PgBondYieldSpreadTraceAbsBO> traces = entry.getValue().stream()
                    .sorted(Comparator.comparing(PgBondYieldSpreadTraceAbsBO::getIssueDate)).collect(Collectors.toList());
            YieldSpreadTraceLineChartDTO.Builder builder = YieldSpreadTraceLineChartDTO.builder(traces.size());
            traces.forEach(builder::withRating);
            String key = String.format(VARIETIES_LINE_CHART_KEY, bondTypeEnum.getValue(), chartTypeEnum.getValue(), entry.getKey().getValue());
            ratingCacheMap.put(key, ZipUtils.zipBase64(JSON.toJSONString(builder.build())));
        }
        return ratingCacheMap;
    }

    private Map<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> collectAbsTraces(Date startDate, Date endDate, YieldPanoramaBondTypeEnum bondTypeEnum,
                                                                                              YieldSpreadChartTypeEnum chartTypeEnum) {
        List<YieldSpreadCurveCodeEnum> curveCodeEnums = YieldSpreadCurveCodeEnum.toEnums(bondTypeEnum.getCurveCodes());
        if (CollectionUtils.isEmpty(curveCodeEnums)) {
            return Collections.emptyMap();
        }
        Map<YieldSpreadCurveCodeEnum, List<PgBondYieldSpreadTraceAbsBO>> collectResult = Maps.newHashMap();
        curveCodeEnums.forEach(curveCodeEnum -> collectResult.put(curveCodeEnum,
                pgBondYieldSpreadTraceAbsDAO.listYieldSpreadTraces(startDate, endDate, bondTypeEnum, chartTypeEnum, curveCodeEnum)));
        return collectResult;
    }

    @Override
    public int syncBondYieldSpreadTrace(Date startDate, Date endDate) {
        AtomicInteger effectRows = new AtomicInteger();
        startDate = Objects.isNull(startDate) ? Date.valueOf(LocalDate.now().minusDays(1)) : startDate;
        endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
        for (LocalDate localStartDate = startDate.toLocalDate(); !localStartDate.isAfter(endDate.toLocalDate()); localStartDate = localStartDate.plusDays(1)) {
            Date issueDate = Date.valueOf(localStartDate);
            effectRows.addAndGet(this.calBondYieldSpreadTraceAbs(issueDate));
            effectRows.addAndGet(this.calBondYieldSpreadTraceQuantile(issueDate));
            effectRows.addAndGet(this.calBondYieldSpreadTraceChange(issueDate));
            logger.info("syncBondYieldSpreadTrace同步利差追踪利差日期:{}", issueDate);
        }
        String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        String redisKey = String.format(TRACE_SPREAD_DATE_KEY, now);
        BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO = new BondYieldPanoramaTraceSpreadDateDTO();
        pgBondYieldSpreadTraceAbsDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
        pgBondYieldSpreadTraceQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
        pgBondYieldSpreadTraceChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
        stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(spreadDateDTO), YieldSpreadConst.TWO_DAYS, TimeUnit.DAYS);
        this.cacheLineChart();
        return effectRows.get();
    }

    private int calBondYieldSpreadTraceAbs(Date issueDate) {
        // 查询基础数据，为后续做准备
        Optional<YieldSpreadTraceContext> parameter = this.prepareParameter(issueDate);
        if (!parameter.isPresent()) {
            return 0;
        }
        List<PgBondYieldSpreadTraceAbsDO> absBondYieldSpreadTraceList = Lists.newArrayList();
        for (YieldPanoramaBondTypeEnum bondType : YieldPanoramaBondTypeEnum.getTraceBondTypeEnums()) {
            YieldSpreadTraceContext context = BeanCopyUtils.copyProperties(parameter.get(), YieldSpreadTraceContext.class);
            context.setBondTypeEnum(bondType);
            List<PgBondYieldPanoramaAbsDO> matchAbs = context.getAbsBondYieldPanoramas().stream()
                    .filter(abs -> bondType.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
            context.setAbsBondYieldPanoramas(matchAbs);
            List<YieldSpreadTraceProcessor> processors = processorMap.getOrDefault(bondType, Collections.emptyList());
            processors.forEach(processor -> absBondYieldSpreadTraceList.addAll(processor.processYieldSpreadTraceYtm(context)));
        }
        return pgBondYieldSpreadTraceAbsDAO.saveBondYieldSpreadTraceAbsList(issueDate, absBondYieldSpreadTraceList);
    }

    private int calBondYieldSpreadTraceChange(Date issueDate) {
        List<PgBondYieldSpreadTraceChangeDO> changeList = Lists.newArrayList();
        //同一个曲线同一个变动类型不会重复
        List<PgBondYieldPanoramaChangeDO> pgBondYieldPanoramaChanges =
                pgBondYieldPanoramaChangeDAO.listBondYieldPanoramaChanges(new ArrayList<>(curveCodes), issueDate);

        Map<Integer, List<PgBondYieldPanoramaChangeDO>> curveMap = pgBondYieldPanoramaChanges.stream()
                .collect(Collectors.groupingBy(PgBondYieldPanoramaChangeDO::getCurveCode));
        for (YieldPanoramaBondTypeEnum bondType : YieldPanoramaBondTypeEnum.getTraceBondTypeEnums()) {
            Set<Integer> curCurveCodes = bondType.getCurveCodes();
            if (CollectionUtils.isEmpty(curCurveCodes)) {
                continue;
            }
            for (Integer curCurveCode : curCurveCodes) {
                List<PgBondYieldPanoramaChangeDO> bondYieldPanoramaChangeDOList = curveMap.get(curCurveCode);
                if (CollectionUtils.isEmpty(bondYieldPanoramaChangeDOList)) {
                    continue;
                }
                List<PgBondYieldSpreadTraceChangeDO> traceChangeDOList = bondYieldPanoramaChangeDOList.stream().map(bondYieldPanoramaChange -> {
                    PgBondYieldSpreadTraceChangeDO pgBondYieldSpreadTraceChangeDO = BeanCopyUtils.copyProperties(bondYieldPanoramaChange, PgBondYieldSpreadTraceChangeDO.class);
                    pgBondYieldSpreadTraceChangeDO.setBondType(bondType.getValue());
                    pgBondYieldSpreadTraceChangeDO.setChartType(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue());
                    pgBondYieldSpreadTraceChangeDO.setCurveCode(curCurveCode);
                    pgBondYieldSpreadTraceChangeDO.setIssueDate(issueDate);
                    return pgBondYieldSpreadTraceChangeDO;
                }).collect(Collectors.toList());

                changeList.addAll(traceChangeDOList);
            }
        }

        List<Integer> chartTypes = Arrays.stream(YieldSpreadChartTypeEnum.values()).map(YieldSpreadChartTypeEnum::getValue)
                .filter(chart -> YieldSpreadChartTypeEnum.MATU_SPREAD.getValue() != chart).collect(Collectors.toList());
        // 区间变动不需要国债数据
        List<PgBondYieldSpreadTraceAbsDO> currentBondYieldSpreadTraceList =
                pgBondYieldSpreadTraceAbsDAO.listBondYieldSpreadTraces(issueDate, chartTypes, null);
        Map<String, PgBondYieldSpreadTraceAbsDO> currentTraceAbsMap =
                currentBondYieldSpreadTraceList.stream().collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        for (BondYieldIntervalChangeTypeEnum changeTypeEnum : BondYieldIntervalChangeTypeEnum.values()) {
            // 获取最近一天的工作日，包含自己
            Date startDate = this.getChangeStartDate(issueDate, changeTypeEnum);
            List<PgBondYieldSpreadTraceAbsDO> beforeBondYieldSpreadTraceList = pgBondYieldSpreadTraceAbsDAO.listBondYieldSpreadTraces(startDate, chartTypes, null);
            if (CollectionUtils.isEmpty(beforeBondYieldSpreadTraceList)) {
                continue;
            }
            Map<String, PgBondYieldSpreadTraceAbsDO> beforeTraceAbsMap =
                    beforeBondYieldSpreadTraceList.stream().collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
            for (Map.Entry<String, PgBondYieldSpreadTraceAbsDO> curentEntry : currentTraceAbsMap.entrySet()) {
                PgBondYieldSpreadTraceAbsDO beforeTraceAbs = beforeTraceAbsMap.get(curentEntry.getKey());
                PgBondYieldSpreadTraceAbsDO endTraceAbs = curentEntry.getValue();
                Optional<PgBondYieldSpreadTraceChangeDO> change =
                        buildBondYieldSpreadTraceChange(endTraceAbs, beforeTraceAbs, changeTypeEnum.getValue(), startDate,
                                getSafeSubtractFunction(endTraceAbs.getChartType()));
                change.ifPresent(changeList::add);
            }
        }
        return pgBondYieldSpreadTraceChangeDAO.saveBondYieldSpreadTraceChangeList(issueDate, changeList);
    }

    private Optional<PgBondYieldSpreadTraceChangeDO> buildBondYieldSpreadTraceChange(PgBondYieldSpreadTraceAbsDO currentAbsTrace, PgBondYieldSpreadTraceAbsDO beforeAbsTrace,
                                                                                     Integer changeType, Date startDate,
                                                                                     BiFunction<BigDecimal, BigDecimal, Optional<BigDecimal>> safeSubtraceFunction) {
        if (Objects.isNull(currentAbsTrace) || Objects.isNull(beforeAbsTrace)) {
            return Optional.empty();
        }
        PgBondYieldSpreadTraceChangeDO pgBondYieldTraceChangeDO = new PgBondYieldSpreadTraceChangeDO();
        pgBondYieldTraceChangeDO.setBondType(currentAbsTrace.getBondType());
        Integer chartType = currentAbsTrace.getChartType();
        pgBondYieldTraceChangeDO.setChartType(chartType);
        pgBondYieldTraceChangeDO.setCurveCode(currentAbsTrace.getCurveCode());
        pgBondYieldTraceChangeDO.setYtm1M(safeSubtraceFunction.apply(currentAbsTrace.getYtm1M(), beforeAbsTrace.getYtm1M()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm3M(safeSubtraceFunction.apply(currentAbsTrace.getYtm3M(), beforeAbsTrace.getYtm3M()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm6M(safeSubtraceFunction.apply(currentAbsTrace.getYtm6M(), beforeAbsTrace.getYtm6M()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm9M(safeSubtraceFunction.apply(currentAbsTrace.getYtm9M(), beforeAbsTrace.getYtm9M()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm1Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm1Y(), beforeAbsTrace.getYtm1Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm2Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm2Y(), beforeAbsTrace.getYtm2Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm3Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm3Y(), beforeAbsTrace.getYtm3Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm5Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm5Y(), beforeAbsTrace.getYtm5Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm7Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm7Y(), beforeAbsTrace.getYtm7Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm10Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm10Y(), beforeAbsTrace.getYtm10Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm15Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm15Y(), beforeAbsTrace.getYtm15Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm20Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm20Y(), beforeAbsTrace.getYtm20Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm30Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm30Y(), beforeAbsTrace.getYtm30Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm50Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm50Y(), beforeAbsTrace.getYtm50Y()).orElse(null));

        pgBondYieldTraceChangeDO.setIssueDate(currentAbsTrace.getIssueDate());
        pgBondYieldTraceChangeDO.setDeleted(Deleted.NO_DELETED.getValue());
        pgBondYieldTraceChangeDO.setChangeType(changeType);
        pgBondYieldTraceChangeDO.setStartDate(startDate);
        return Optional.of(pgBondYieldTraceChangeDO);
    }

    private int calBondYieldSpreadTraceQuantile(Date issueDate) {
        List<PgBondYieldSpreadTraceQuantileDO> quantileList = Lists.newArrayList();
        //同一个曲线同一个分位类型不会重复
        List<PgBondYieldPanoramaQuantileDO> bondYieldPanoramaQuantiles =
                pgBondYieldPanoramaQuantileDAO.listBondYieldPanoramaQuantiles(new ArrayList<>(curveCodes), issueDate);
        Map<Integer, List<PgBondYieldPanoramaQuantileDO>> curveMap = bondYieldPanoramaQuantiles.stream()
                .collect(Collectors.groupingBy(PgBondYieldPanoramaQuantileDO::getCurveCode));
        for (YieldPanoramaBondTypeEnum bondType : YieldPanoramaBondTypeEnum.getTraceBondTypeEnums()) {
            Set<Integer> curCurveCodes = bondType.getCurveCodes();
            if (CollectionUtils.isEmpty(curCurveCodes)) {
                continue;
            }
            for (Integer curCurveCode : curCurveCodes) {
                List<PgBondYieldPanoramaQuantileDO> bondYieldPanoramaQuantileList = curveMap.get(curCurveCode);
                if (CollectionUtils.isEmpty(bondYieldPanoramaQuantileList)) {
                    continue;
                }
                List<PgBondYieldSpreadTraceQuantileDO> traceQuantileDOList = bondYieldPanoramaQuantileList.stream().map(bondYieldPanoramaQuantile -> {
                    PgBondYieldSpreadTraceQuantileDO pgBondYieldSpreadTraceQuantileDO
                            = BeanCopyUtils.copyProperties(bondYieldPanoramaQuantile, PgBondYieldSpreadTraceQuantileDO.class);
                    pgBondYieldSpreadTraceQuantileDO.setBondType(bondType.getValue());
                    pgBondYieldSpreadTraceQuantileDO.setChartType(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue());
                    pgBondYieldSpreadTraceQuantileDO.setCurveCode(curCurveCode);
                    pgBondYieldSpreadTraceQuantileDO.setIssueDate(issueDate);
                    pgBondYieldSpreadTraceQuantileDO.setDeleted(Deleted.NO_DELETED.getValue());
                    pgBondYieldSpreadTraceQuantileDO.setStartDate(bondYieldPanoramaQuantile.getStartDate());
                    return pgBondYieldSpreadTraceQuantileDO;
                }).collect(Collectors.toList());

                quantileList.addAll(traceQuantileDOList);
            }
        }

        for (SpreadQuantileTypeEnum quantileTypeEnum : SpreadQuantileTypeEnum.values()) {
            Date startDate = yieldSpreadCommonService.getQuantileStartDate(issueDate, quantileTypeEnum);
            pgBondYieldSpreadTraceQuantileViewDAO.createTraceQuantileView(startDate, issueDate);
            List<PgBondYieldSpreadTraceQuantileViewDO> bondYieldSpreadTraceQuantileViewList = pgBondYieldSpreadTraceQuantileViewDAO.listBondYieldSpreadTraceQuantiles(issueDate);
            for (PgBondYieldSpreadTraceQuantileViewDO pgBondYieldPanoramaQuantileViewDO : bondYieldSpreadTraceQuantileViewList) {
                PgBondYieldSpreadTraceQuantileDO bondYieldSpreadTraceQuantile =
                        BeanCopyUtils.copyProperties(pgBondYieldPanoramaQuantileViewDO, PgBondYieldSpreadTraceQuantileDO.class);
                bondYieldSpreadTraceQuantile.setStartDate(startDate);
                bondYieldSpreadTraceQuantile.setQuantileType(quantileTypeEnum.getValue());
                bondYieldSpreadTraceQuantile.setDeleted(Deleted.NO_DELETED.getValue());
                quantileList.add(bondYieldSpreadTraceQuantile);
            }
        }
        return pgBondYieldSpreadTraceQuantileDAO.saveBondYieldSpreadTraceQuantileList(issueDate, quantileList);
    }

    private Optional<BigDecimal> safeSubtract(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return Optional.empty();
        }
        return Optional.of(value1.subtract(value2).setScale(TRACE_INTERVAL_CHANGE_SCALE, RoundingMode.HALF_UP));
    }

    private Optional<BigDecimal> safeSubtractToBp(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return Optional.empty();
        }
        return Optional.of(value1.subtract(value2).multiply(ONE_HUNDRED).setScale(TRACE_INTERVAL_CHANGE_SCALE, RoundingMode.HALF_UP));
    }

    private String getKey(PgBondYieldSpreadTraceAbsDO abs) {
        return String.format(NAMESPACE_KEY_PLACEHOLDER, abs.getBondType(), abs.getChartType(), abs.getCurveCode());
    }

    private Optional<YieldSpreadTraceContext> prepareParameter(Date issueDate) {
        // 查询基础数据,包含国债
        List<PgBondYieldPanoramaAbsDO> bondYieldPanoramaAbsList = pgBondYieldPanoramaAbsDAO.listBondYieldPanoramaQuantiles(null, issueDate);
        if (CollectionUtils.isEmpty(bondYieldPanoramaAbsList)) {
            return Optional.empty();
        }
        PgBondYieldPanoramaAbsDO chinaBond = bondYieldPanoramaAbsList.stream()
                .filter(abs -> CHINA_BOND.getCurveCodes().contains(abs.getCurveCode())).findFirst().orElse(null);
        PgBondYieldPanoramaAbsDO chinaBondKai = bondYieldPanoramaAbsList.stream()
                .filter(abs -> CD_BOND.getCurveCodes().contains(abs.getCurveCode())).findFirst().orElse(null);
        List<PgBondYieldPanoramaAbsDO> chinaBondMidYieldPanoramas = bondYieldPanoramaAbsList.stream()
                .filter(abs -> MEDIUM_AND_SHORT_TERMS_NOTE.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
        List<PgBondYieldPanoramaAbsDO> generalBankBondYieldPanoramas = bondYieldPanoramaAbsList.stream()
                .filter(abs -> GENERAL_BANK_BOND.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
        List<PgBondYieldPanoramaAbsDO> securitiesBondYieldPanoramas = bondYieldPanoramaAbsList.stream()
                .filter(abs -> SECURITIES_BOND.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
        YieldSpreadTraceContext context = new YieldSpreadTraceContext();
        context.setIssueDate(issueDate);
        context.setChinaBondYieldPanorama(chinaBond);
        context.setChinaBondKaiYieldPanorama(chinaBondKai);
        context.setChinaBondMidYieldPanoramas(chinaBondMidYieldPanoramas);
        context.setGeneralBankBondYieldPanoramas(generalBankBondYieldPanoramas);
        context.setSecuritiesBondYieldPanoramas(securitiesBondYieldPanoramas);
        context.setAbsBondYieldPanoramas(bondYieldPanoramaAbsList);
        return Optional.of(context);
    }

    /**
     * 折线图key
     *
     * <AUTHOR>
     */
    private static final class TraceLineChartKey {

        private final YieldPanoramaBondTypeEnum bondTypeEnum;

        private final YieldSpreadChartTypeEnum chartTypeEnum;

        private TraceLineChartKey(YieldPanoramaBondTypeEnum bondTypeEnum, YieldSpreadChartTypeEnum chartTypeEnum) {
            this.bondTypeEnum = bondTypeEnum;
            this.chartTypeEnum = chartTypeEnum;
        }

        /**
         * 创建TraceLineChartKey对象静态方法
         *
         * @param bondTypeEnum  债券类型
         * @param chartTypeEnum 图类型
         * @return {@link TraceLineChartKey}
         */
        public static TraceLineChartKey of(YieldPanoramaBondTypeEnum bondTypeEnum, YieldSpreadChartTypeEnum chartTypeEnum) {
            return new TraceLineChartKey(bondTypeEnum, chartTypeEnum);
        }

        public YieldPanoramaBondTypeEnum getBondTypeEnum() {
            return bondTypeEnum;
        }

        public YieldSpreadChartTypeEnum getChartTypeEnum() {
            return chartTypeEnum;
        }

    }

}
