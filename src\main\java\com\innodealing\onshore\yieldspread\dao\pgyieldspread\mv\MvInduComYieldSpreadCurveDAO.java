package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvInduComYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.MvInduBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvInduComYieldSpreadCurveDO;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.persistence.Table;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;

/**
 * 行业利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class MvInduComYieldSpreadCurveDAO extends AbstractMvComYieldSpreadCurveDAO<MvInduBondYieldSpreadCurveParameter> {
    private static final String TABLE_NAME = MvInduComYieldSpreadCurveDO.class.getAnnotation(Table.class).name();

    private MvInduComYieldSpreadCurveMapper mvInduComYieldSpreadCurveMapper;

    /**
     * 构造函数
     *
     * @param mvInduComYieldSpreadCurveMapper mapper
     */
    protected MvInduComYieldSpreadCurveDAO(MvInduComYieldSpreadCurveMapper mvInduComYieldSpreadCurveMapper) {
        super(mvInduComYieldSpreadCurveMapper);
        this.mvInduComYieldSpreadCurveMapper = mvInduComYieldSpreadCurveMapper;
    }

    @Override
    public List<ComYieldSpreadCurveBO> list(@NonNull Long comUniCode, Integer spreadBondValue) {
        DynamicQuery<MvInduComYieldSpreadCurveDO> query = DynamicQuery.createQuery(MvInduComYieldSpreadCurveDO.class)
                .and(MvInduComYieldSpreadCurveDO::getComUniCode, x -> x.isEqual(comUniCode))
                .and(Objects.nonNull(spreadBondValue), MvInduComYieldSpreadCurveDO::getSpreadBondType, x -> x.isEqual(spreadBondValue))
                .and(Objects.isNull(spreadBondValue), MvInduComYieldSpreadCurveDO::getUsingSpreadBondType, x -> x.isEqual(UNUSED_FIELD_GROUP.getValue()))
                .orderBy(MvInduComYieldSpreadCurveDO::getSpreadDate, SortDirections::asc);
        List<MvInduComYieldSpreadCurveDO> mvInduComYieldSpreadCurveList = mvInduComYieldSpreadCurveMapper.selectByDynamicQuery(query);
        return mvInduComYieldSpreadCurveList.stream().map(super::handlePrecision).collect(Collectors.toList());
    }

    @Override
    protected String tableName() {
        return TABLE_NAME;
    }
}
