package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 利差变动
 *
 * <AUTHOR>
 */
public class YieldSpreadTraceResponseDTO {

    @ApiModelProperty("图类型，1:到期收益率 2:信用利差(债券类型-国开) 3:等级利差 4:期限利差 5:品种利差(债券类型-中短期票据) 6:条款利差(债券类型-普通债)")
    private Integer chartType;
    @ApiModelProperty("行名")
    private String typeName;
    @ApiModelProperty("行排行")
    private Integer typeSort;
    @ApiModelProperty("最大收益率")
    private BigDecimal maxTraceYield;
    @ApiModelProperty("最小收益率")
    private BigDecimal minTraceYield;
    @ApiModelProperty("中位数收益率")
    private BigDecimal medianTraceYield;
    @ApiModelProperty("收益率")
    private List<YieldSpreadTraceDataResponseDTO> yieldSpreadTraceData;
    @ApiModelProperty("最大3年历史分位收益率")
    private BigDecimal maxThreeYearYield;
    @ApiModelProperty("最小3年历史分位收益率")
    private BigDecimal minThreeYearYield;
    @ApiModelProperty("中位数3年历史分位收益率")
    private BigDecimal medianThreeYearYield;
    @ApiModelProperty("3年历史分位时间区间")
    private String threeYearHisRangeDate;
    @ApiModelProperty("3年历史分位")
    private List<YieldSpreadTraceDataResponseDTO> threeYearHistorical;
    @ApiModelProperty("最大区间变动收益率")
    private BigDecimal maxIntervalVarYield;
    @ApiModelProperty("最小区间变动收益率")
    private BigDecimal minIntervalVarYield;
    @ApiModelProperty("中位数区间变动收益率")
    private BigDecimal medianIntervalVarYield;
    @ApiModelProperty("区间变动时间区间")
    private String intervalVarRangeDate;
    @ApiModelProperty("区间变动")
    private List<YieldSpreadTraceDataResponseDTO> intervalVariation;

    public Integer getChartType() {
        return chartType;
    }

    public void setChartType(Integer chartType) {
        this.chartType = chartType;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getTypeSort() {
        return typeSort;
    }

    public void setTypeSort(Integer typeSort) {
        this.typeSort = typeSort;
    }

    public List<YieldSpreadTraceDataResponseDTO> getYieldSpreadTraceData() {
        return Objects.isNull(yieldSpreadTraceData) ? null : new ArrayList<>(yieldSpreadTraceData);
    }

    public void setYieldSpreadTraceData(List<YieldSpreadTraceDataResponseDTO> yieldSpreadTraceData) {
        this.yieldSpreadTraceData = Objects.isNull(yieldSpreadTraceData) ? null : new ArrayList<>(yieldSpreadTraceData);
    }

    public List<YieldSpreadTraceDataResponseDTO> getThreeYearHistorical() {
        return Objects.isNull(threeYearHistorical) ? null : new ArrayList<>(threeYearHistorical);
    }

    public void setThreeYearHistorical(List<YieldSpreadTraceDataResponseDTO> threeYearHistorical) {
        this.threeYearHistorical = Objects.isNull(threeYearHistorical) ? null : new ArrayList<>(threeYearHistorical);
    }

    public List<YieldSpreadTraceDataResponseDTO> getIntervalVariation() {
        return Objects.isNull(intervalVariation) ? null : new ArrayList<>(intervalVariation);
    }

    public void setIntervalVariation(List<YieldSpreadTraceDataResponseDTO> intervalVariation) {
        this.intervalVariation = Objects.isNull(intervalVariation) ? null : new ArrayList<>(intervalVariation);
    }

    public String getThreeYearHisRangeDate() {
        return threeYearHisRangeDate;
    }

    public void setThreeYearHisRangeDate(String threeYearHisRangeDate) {
        this.threeYearHisRangeDate = threeYearHisRangeDate;
    }

    public String getIntervalVarRangeDate() {
        return intervalVarRangeDate;
    }

    public void setIntervalVarRangeDate(String intervalVarRangeDate) {
        this.intervalVarRangeDate = intervalVarRangeDate;
    }

    public BigDecimal getMaxTraceYield() {
        return maxTraceYield;
    }

    public void setMaxTraceYield(BigDecimal maxTraceYield) {
        this.maxTraceYield = maxTraceYield;
    }

    public BigDecimal getMinTraceYield() {
        return minTraceYield;
    }

    public void setMinTraceYield(BigDecimal minTraceYield) {
        this.minTraceYield = minTraceYield;
    }

    public BigDecimal getMaxThreeYearYield() {
        return maxThreeYearYield;
    }

    public void setMaxThreeYearYield(BigDecimal maxThreeYearYield) {
        this.maxThreeYearYield = maxThreeYearYield;
    }

    public BigDecimal getMinThreeYearYield() {
        return minThreeYearYield;
    }

    public void setMinThreeYearYield(BigDecimal minThreeYearYield) {
        this.minThreeYearYield = minThreeYearYield;
    }

    public BigDecimal getMaxIntervalVarYield() {
        return maxIntervalVarYield;
    }

    public void setMaxIntervalVarYield(BigDecimal maxIntervalVarYield) {
        this.maxIntervalVarYield = maxIntervalVarYield;
    }

    public BigDecimal getMinIntervalVarYield() {
        return minIntervalVarYield;
    }

    public void setMinIntervalVarYield(BigDecimal minIntervalVarYield) {
        this.minIntervalVarYield = minIntervalVarYield;
    }

    public BigDecimal getMedianTraceYield() {
        return medianTraceYield;
    }

    public void setMedianTraceYield(BigDecimal medianTraceYield) {
        this.medianTraceYield = medianTraceYield;
    }

    public BigDecimal getMedianThreeYearYield() {
        return medianThreeYearYield;
    }

    public void setMedianThreeYearYield(BigDecimal medianThreeYearYield) {
        this.medianThreeYearYield = medianThreeYearYield;
    }

    public BigDecimal getMedianIntervalVarYield() {
        return medianIntervalVarYield;
    }

    public void setMedianIntervalVarYield(BigDecimal medianIntervalVarYield) {
        this.medianIntervalVarYield = medianIntervalVarYield;
    }
}
