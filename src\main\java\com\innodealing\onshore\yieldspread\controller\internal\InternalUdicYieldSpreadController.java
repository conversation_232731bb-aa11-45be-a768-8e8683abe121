package com.innodealing.onshore.yieldspread.controller.internal;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.AreaYieldSpreadDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.enums.UdicRegionEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.ValidationUtil;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicBondYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicComYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicSpreadPanoramaResponseDTO;
import com.innodealing.onshore.yieldspread.service.UdicBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.UdicComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * (内部)城投利差分析接口
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部)利差分析-城投")
@RestController
@RequestMapping("internal/udic/yield-spread")
@Validated
public class InternalUdicYieldSpreadController {

    @Resource
    private UdicBondYieldSpreadService udicBondYieldSpreadService;

    @Resource
    private UdicComYieldSpreadService udicComYieldSpreadService;

    @ApiOperation("城投利差分析-全景图")
    @PostMapping("panoramas")
    public RestResponse<List<UdicSpreadPanoramaResponseDTO>> listPanoramas(
            @ApiParam(name = "requestDTO", value = "城投利差-全景请求参数", required = true)
            @RequestBody UdicPanoramaRequestDTO requestDTO) {
        return RestResponse.Success(udicBondYieldSpreadService.listUdicPanoramas(requestDTO));
    }

    @ApiOperation(value = "城投利差分析-利差曲线")
    @PostMapping(value = "/list/curves")
    public RestResponse<List<UdicCurveResponseDTO>> listCurves(@RequestBody UdicCurveRequestDTO request) {
        return RestResponse.Success(udicBondYieldSpreadService.listCurves(request));
    }

    @ApiOperation(value = "城投利差分析-主体利差")
    @PostMapping(value = "/paging/com-yield-spread")
    public RestResponse<NormPagingResult<UdicComYieldSpreadResponseDTO>> getComYieldSpreadPaging(
            @RequestBody UdicListRequestDTO request) {
        return RestResponse.Success(udicComYieldSpreadService.getComYieldSpreadPaging(request));
    }

    @ApiOperation(value = "城投利差分析-主体利差(exists)")
    @PostMapping(value = "/paging/com-yield-spread-exists")
    public RestResponse<NormPagingResult<UdicComYieldSpreadResponseDTO>> getComYieldSpreadPagingByExists(
            @RequestBody UdicListRequestDTO request) {
        return RestResponse.Success(udicComYieldSpreadService.getComYieldSpreadPagingByExists(request));
    }

    @ApiOperation(value = "城投利差分析-单券利差")
    @PostMapping(value = "/paging/bond-yield-spread")
    public RestResponse<NormPagingResult<UdicBondYieldSpreadResponseDTO>> getBondYieldSpreadPaging(
            @RequestBody UdicListRequestDTO request) {
        return RestResponse.Success(udicBondYieldSpreadService.getBondYieldSpreadPaging(request));
    }

    @ApiOperation(value = "城投利差分析-刷新城投利差曲线物化视图")
    @GetMapping(value = "/refresh/mv-spread-curve")
    public void refreshMvSpreadCurve(
            @ApiParam(name = "udicRegion", value = "城投区域等级,1:所有区域,2:省,3:市,4:区县 不传值四种都刷新")
            @RequestParam(value = "udicRegion", required = false) Integer udicRegion) {
        UdicRegionEnum udicRegionEnum = EnumUtils.getEnumNullable(UdicRegionEnum.class, udicRegion);
        if (Objects.nonNull(udicRegion) && Objects.isNull(udicRegionEnum)) {
            throw new TipsException("udicRegion传参错误,参考值：1-所有区域,2-省,3-市,4-区县 不传值四种都刷新");
        }
        udicBondYieldSpreadService.refreshMvUdicBondYieldSpreadCurve(udicRegionEnum);
    }

    @ApiOperation(value = "城投利差分析-刷新城投利差全景物化视图")
    @GetMapping(value = "/refresh/mv-spread-panorama")
    public void refreshMvSpreadPanorama() {
        udicBondYieldSpreadService.refreshMvUdicBondYieldSpreadPanorama();
    }

    @ApiOperation(value = "城投利差分析-刷新上一天城投利差曲线数据")
    @GetMapping("/refresh/curve/yesterday")
    public void refreshCurveYesterday() {
        udicBondYieldSpreadService.refreshCurveYesterday();
    }

    @ApiOperation(value = "城投利差分析-刷新指定行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard")
    public void refreshCurveRatingShard(@RequestBody RefreshYieldCurveParam param) {
        ValidationUtil.valid(param);
        udicBondYieldSpreadService.refreshMvUdicBondYieldSpreadRatingCurve(param);
    }

    @ApiOperation(value = "城投利差分析-刷新昨日行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard-yesterday")
    public void refreshCurveRatingShardYesterday() {
        RefreshYieldCurveParam param = new RefreshYieldCurveParam();
        param.setMvRefresh(true);
        param.setStartDate(Date.valueOf(LocalDate.now().minusDays(1)));
        param.setEndDate(Date.valueOf(LocalDate.now().minusDays(1)));
        udicBondYieldSpreadService.refreshMvUdicBondYieldSpreadRatingCurve(param);
    }

    @ApiOperation(value = "城投利差分析-刷新历史行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard-history")
    public void refreshCurveRatingShardHistory(@RequestParam(value = "isTableRefresh") Boolean isTableRefresh) {
        udicBondYieldSpreadService.refreshMvUdicBondYieldSpreadRatingCurveHistory(isTableRefresh);
    }

    @ApiOperation(value = "根据日期计算城投区域利差(不传日期默认跑前一天的数据)")
    @PostMapping("/calcUdicAreaYieldSpreadsBySpreadDate")
    public void calcUdicAreaYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "shardingHindParam", value = "分片参数")
            @RequestParam(required = false) String shardingHindParam) {
        udicBondYieldSpreadService.calcUdicAreaYieldSpreadsBySpreadDate(startDate, endDate);
    }

    @ApiOperation(value = "城投利差分析-查询主体利差总数")
    @PostMapping(value = "/paging/com-yield-spread/count")
    public RestResponse<Long> getComYieldSpreadPagingCount(@RequestBody UdicListRequestDTO request) {
        return RestResponse.Success(udicComYieldSpreadService.getComYieldSpreadPagingCount(request));
    }

    @PostMapping("curve-save")
    @ApiOperation(value = "保存曲线")
    public Boolean saveCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated UdicCurveGenerateConditionReqDTO request) {
        return udicBondYieldSpreadService.saveCurve(userid, curveGroupId, request);
    }

    @PostMapping("curve-update")
    @ApiOperation(value = "更新曲线")
    public Boolean updateCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线名称") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Validated UdicCurveGenerateConditionReqDTO request) {
        return udicBondYieldSpreadService.updateCurve(userid, curveId, request);
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public NormPagingResult<UdicBondYieldSpreadResponseDTO> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return udicBondYieldSpreadService.pagingSingleBondYieldSpreads(userid, request);
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差 ")
    public List<UdicComYieldSpreadResponseDTO> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return udicComYieldSpreadService.listComYieldSpreads(userid, request);
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public Long countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return udicComYieldSpreadService.countComYieldSpread(userid, request);
    }

    @PostMapping("/area-spread")
    @ApiOperation(value = "区域各行政级别利差")
    public AreaYieldSpreadDTO areaYieldSpread(
            @ApiParam(name = "spreadDate", value = "利差日期，不传默认是最近一天工作日") @RequestParam(value = "spreadDate", required = false) Date spreadDate,
            @NotNull(message = "区域code不能为空") @ApiParam(name = "areaCode", value = "区域code") @RequestParam(value = "areaCode") Long areaCode) {
        return udicBondYieldSpreadService.areaYieldSpread(spreadDate, areaCode);
    }

    @PostMapping("/list/com-credit-spread")
    @ApiOperation(value = "最新的主体信用利差,上限200")
    public List<ComCreditSpreadDTO> listCreditSpreadByComUniCodes(
            @ApiParam(name = "comUniCodes", value = "发行人代码", required = true) @RequestBody Set<Long> comUniCodes) {
        return udicComYieldSpreadService.listCreditSpreadByComUniCodes(comUniCodes);
    }
}
