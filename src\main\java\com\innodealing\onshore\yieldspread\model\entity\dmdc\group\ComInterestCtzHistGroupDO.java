package com.innodealing.onshore.yieldspread.model.entity.dmdc.group;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 城投主体利差(老表)
 *
 * <AUTHOR>
 * @date 2022/08/17
 **/
@Table(name = "com_interest_ctz_hist")
public class ComInterestCtzHistGroupDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 公司统一编码
     */
    @Column
    private Long comUniCode;
    /**
     * 公司名称
     */
    @Column
    private String comUniName;
    /**
     * 企业类型(性质)  1央企,2国企,6民企 t_pub_par.par_sys_code=1062
     */
    @Column
    private Integer enterpriseType;
    /**
     * 省分编码
     */
    @Column
    private Long areaUniCode1;
    /**
     * 省名称
     */
    @Column
    private String areaName1;
    /**
     * 市编码
     */
    @Column
    private Long areaUniCode2;
    /**
     * 市名称
     */
    @Column
    private String areaName2;
    /**
     * 利差日期
     */
    @Column
    private Date interestDate;
    /**
     * 实际控制人
     */
    @Column
    private String realCtrlName;
    /**
     * 行政级别名称
     */
    @Column
    private String areaLevelName;
    /**
     * 行政级别 t_area_level.id
     */
    @Column
    private Integer areaLevelId;
    /**
     * 所属区域名称
     */
    @Column
    private String areaName;
    /**
     * 主体评级
     */
    @Column
    private String comRating;
    /**
     * 信用利差(公募)
     */
    @Column
    private BigDecimal creditInterest;
    /**
     * 超额利差(公募)
     */
    @Column
    private BigDecimal excessInterest;
    /**
     * 信用利差(私募)
     */
    @Column
    private BigDecimal privateCreditInterest;
    /**
     * 超额利差(私募)
     */
    @Column
    private BigDecimal privateExcessInterest;
    /**
     * 信用利差(永续)
     */
    @Column
    private BigDecimal foreverCreditInterest;
    /**
     * 超额利差(永续)
     */
    @Column
    private BigDecimal foreverExcessInterest;
    /**
     * 删除标记 1删除 0正常
     */
    @Column
    private Integer isDeleted;
    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public Integer getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(Integer enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public Long getAreaUniCode1() {
        return areaUniCode1;
    }

    public void setAreaUniCode1(Long areaUniCode1) {
        this.areaUniCode1 = areaUniCode1;
    }

    public String getAreaName1() {
        return areaName1;
    }

    public void setAreaName1(String areaName1) {
        this.areaName1 = areaName1;
    }

    public Long getAreaUniCode2() {
        return areaUniCode2;
    }

    public void setAreaUniCode2(Long areaUniCode2) {
        this.areaUniCode2 = areaUniCode2;
    }

    public String getAreaName2() {
        return areaName2;
    }

    public void setAreaName2(String areaName2) {
        this.areaName2 = areaName2;
    }

    public Date getInterestDate() {
        return interestDate == null ? null : new Date(interestDate.getTime());
    }

    public void setInterestDate(Date interestDate) {
        this.interestDate = interestDate == null ? null : new Date(interestDate.getTime());
    }

    public String getRealCtrlName() {
        return realCtrlName;
    }

    public void setRealCtrlName(String realCtrlName) {
        this.realCtrlName = realCtrlName;
    }

    public String getAreaLevelName() {
        return areaLevelName;
    }

    public void setAreaLevelName(String areaLevelName) {
        this.areaLevelName = areaLevelName;
    }

    public Integer getAreaLevelId() {
        return areaLevelId;
    }

    public void setAreaLevelId(Integer areaLevelId) {
        this.areaLevelId = areaLevelId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getComRating() {
        return comRating;
    }

    public void setComRating(String comRating) {
        this.comRating = comRating;
    }

    public BigDecimal getCreditInterest() {
        return creditInterest;
    }

    public void setCreditInterest(BigDecimal creditInterest) {
        this.creditInterest = creditInterest;
    }

    public BigDecimal getExcessInterest() {
        return excessInterest;
    }

    public void setExcessInterest(BigDecimal excessInterest) {
        this.excessInterest = excessInterest;
    }

    public BigDecimal getPrivateCreditInterest() {
        return privateCreditInterest;
    }

    public void setPrivateCreditInterest(BigDecimal privateCreditInterest) {
        this.privateCreditInterest = privateCreditInterest;
    }

    public BigDecimal getPrivateExcessInterest() {
        return privateExcessInterest;
    }

    public void setPrivateExcessInterest(BigDecimal privateExcessInterest) {
        this.privateExcessInterest = privateExcessInterest;
    }

    public BigDecimal getForeverCreditInterest() {
        return foreverCreditInterest;
    }

    public void setForeverCreditInterest(BigDecimal foreverCreditInterest) {
        this.foreverCreditInterest = foreverCreditInterest;
    }

    public BigDecimal getForeverExcessInterest() {
        return foreverExcessInterest;
    }

    public void setForeverExcessInterest(BigDecimal foreverExcessInterest) {
        this.foreverExcessInterest = foreverExcessInterest;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCreateTime() {
        return createTime == null ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime == null ? null : new Timestamp(createTime.getTime());
    }

    public Timestamp getUpdateTime() {
        return updateTime == null ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime == null ? null : new Timestamp(updateTime.getTime());
    }

}