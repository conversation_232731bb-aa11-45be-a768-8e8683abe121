package com.innodealing.onshore.yieldspread.model.dto.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.international.common.template.source.excel.convert.ExcelDateConvert;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayStringJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 行业利差全景导出DTO
 *
 * <AUTHOR>
 */
@ColumnWidth(15)
@HeadFontStyle(fontHeightInPoints = 11)
public class InduPanoramaExportExcelDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 利差日期
     */
    @ApiModelProperty("利差日期")
    @ExcelProperty(value = "日期", converter = ExcelDateConvert.class)
    private Date spreadDate;
    /**
     * 一级行业名称
     */
    @ApiModelProperty("一级行业")
    @ExcelProperty("一级行业")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String industryName1;
    /**
     * 二级行业名称
     */
    @ApiModelProperty("二级行业")
    @ExcelProperty("二级行业")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String industryName2;
    /**
     * 信用利差
     */
    @ApiModelProperty("信用利差")
    @ExcelProperty("信用利差")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondCreditSpread;
    /**
     * 近3月变动
     */
    @ApiModelProperty("信用利差近三月变动")
    @ExcelProperty("近3月变动")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondCreditSpreadChange90;
    /**
     * 近6月变动
     */
    @ApiModelProperty("信用利差近六月变动")
    @ExcelProperty("近6月变动")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondCreditSpreadChange180;
    /**
     * 超额利差
     */
    @ApiModelProperty("超额利差")
    @ExcelProperty("超额利差")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondExcessSpread;
    /**
     * 近3月超额利差变动
     */
    @ApiModelProperty("近3月超额利差变动")
    @ExcelProperty("近3月变动")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondExcessSpreadChange90;

    /**
     * 近6月超额利差变动
     */
    @ApiModelProperty("近3月超额利差变动")
    @ExcelProperty("近6月变动")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal bondExcessSpreadChange180;


    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public String getIndustryName1() {
        return industryName1;
    }

    public void setIndustryName1(String industryName1) {
        this.industryName1 = industryName1;
    }

    public String getIndustryName2() {
        return industryName2;
    }

    public void setIndustryName2(String industryName2) {
        this.industryName2 = industryName2;
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondCreditSpreadChange90() {
        return bondCreditSpreadChange90;
    }

    public void setBondCreditSpreadChange90(BigDecimal bondCreditSpreadChange90) {
        this.bondCreditSpreadChange90 = bondCreditSpreadChange90;
    }

    public BigDecimal getBondCreditSpreadChange180() {
        return bondCreditSpreadChange180;
    }

    public void setBondCreditSpreadChange180(BigDecimal bondCreditSpreadChange180) {
        this.bondCreditSpreadChange180 = bondCreditSpreadChange180;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getBondExcessSpreadChange90() {
        return bondExcessSpreadChange90;
    }

    public void setBondExcessSpreadChange90(BigDecimal bondExcessSpreadChange90) {
        this.bondExcessSpreadChange90 = bondExcessSpreadChange90;
    }

    public BigDecimal getBondExcessSpreadChange180() {
        return bondExcessSpreadChange180;
    }

    public void setBondExcessSpreadChange180(BigDecimal bondExcessSpreadChange180) {
        this.bondExcessSpreadChange180 = bondExcessSpreadChange180;
    }
}
