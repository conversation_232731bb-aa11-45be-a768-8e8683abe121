package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 曲线定义数据详情
 *
 * <AUTHOR>
 */
public class CurveDefinitionResDTO {

    @ApiModelProperty("曲线id")
    private Long id;

    @ApiModelProperty("曲线名称")
    private String spreadCurveName;

    @ApiModelProperty("曲线类型：产业=1，城投=2，银行=3，证券=4，自选债=5，基准曲线=6")
    private Integer spreadCurveType;

    @ApiModelProperty("筛选条件Json字符串，只有curveType=1,2,3,4时才有")
    private String filterCondition;

    @ApiModelProperty("导入的债券,只有curveType=5时才有")
    private List<BondBasicInfoResDTO> importedBond;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSpreadCurveName() {
        return spreadCurveName;
    }

    public void setSpreadCurveName(String spreadCurveName) {
        this.spreadCurveName = spreadCurveName;
    }

    public Integer getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(Integer spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public String getFilterCondition() {
        return filterCondition;
    }

    public void setFilterCondition(String filterCondition) {
        this.filterCondition = filterCondition;
    }

    public List<BondBasicInfoResDTO> getImportedBond() {
        return Objects.isNull(importedBond) ? new ArrayList<>() : new ArrayList<>(importedBond);
    }

    public void setImportedBond(List<BondBasicInfoResDTO> importedBond) {
        this.importedBond = Objects.isNull(importedBond) ? new ArrayList<>() : new ArrayList<>(importedBond);
    }

}
