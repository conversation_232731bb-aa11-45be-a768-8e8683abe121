package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

/**
 * 自定义曲线生成情况
 *
 * <AUTHOR>
 */
public class CustomCurveGenerateDetailResDTO {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("曲线名称")
    private String spreadCurveName;

    @ApiModelProperty("曲线生成状态：待生成=1，生成中=2，已生成=3，生成失败=4")
    private Integer generateStatus;

    @ApiModelProperty("生成开始时间")
    private LocalDateTime generateStartTime;

    @ApiModelProperty("生成结束时间")
    private LocalDateTime generateEndTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSpreadCurveName() {
        return spreadCurveName;
    }

    public void setSpreadCurveName(String spreadCurveName) {
        this.spreadCurveName = spreadCurveName;
    }

    public Integer getGenerateStatus() {
        return generateStatus;
    }

    public void setGenerateStatus(Integer generateStatus) {
        this.generateStatus = generateStatus;
    }

    public LocalDateTime getGenerateStartTime() {
        return generateStartTime;
    }

    public void setGenerateStartTime(LocalDateTime generateStartTime) {
        this.generateStartTime = generateStartTime;
    }

    public LocalDateTime getGenerateEndTime() {
        return generateEndTime;
    }

    public void setGenerateEndTime(LocalDateTime generateEndTime) {
        this.generateEndTime = generateEndTime;
    }

}
