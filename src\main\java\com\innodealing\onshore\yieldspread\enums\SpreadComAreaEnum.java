package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 利差发行人区域枚举
 *
 * <AUTHOR>
 * @date 2024/11/13 10:42
 **/
public enum SpreadComAreaEnum implements ITextValueEnum {
    /**
     * 发行人区域枚举,顺序极为展示顺序
     */
    NATION(-1, "全国"),
    BEI_JING(10_010_888, "北京"),
    TIAN_JIN(10_011_158, "天津"),
    HE_BEI(10_010_893, "河北"),
    SHAN_XI(10_010_999, "山西"),
    NEI_MENG_GU(10_010_894, "内蒙古"),
    LIAO_NING(10_011_087, "辽宁"),
    DA_LIAN(10_011_105, "大连"),
    JI_LIN(10_010_902, "吉林"),
    HEI_LONG_JIANG(10_010_953, "黑龙江"),
    SHANG_HAI(10_011_231, "上海"),
    JIANG_SU(10_010_896, "江苏"),
    ZHE_JIANG(10_011_099, "浙江"),
    NING_BO(10_011_144, "宁波"),
    AN_HUI(10_011_228, "安徽"),
    FU_JIAN(10_011_178, "福建"),
    XIA_MEN(10_011_234, "厦门"),
    JIANG_XI(10_011_233, "江西"),
    SHAN_DONG(10_011_049, "山东"),
    QING_DAO(10_011_151, "青岛"),
    HE_NAN(10_011_029, "河南"),
    HU_BEI(10_011_082, "湖北"),
    HU_NAN(10_011_091, "湖南"),
    GUANG_DONG(10_011_088, "广东"),
    SHEN_ZHEN(10_011_235, "深圳"),
    GUANG_XI(10_011_232, "广西"),
    HAI_NAN(10_011_093, "海南"),
    CHONG_QING(10_004_523, "重庆"),
    SI_CHUAN(10_011_132, "四川"),
    GUI_ZHOU(10_011_133, "贵州"),
    YUN_NAN(10_011_230, "云南"),
    XI_ZANG(10_012_355, "西藏"),
    SHAAN_XI(10_011_050, "陕西"),
    GAN_SU(10_011_018, "甘肃"),
    QING_HAI(10_011_175, "青海"),
    NING_XIA(10_011_164, "宁夏"),
    XIN_JIANG(10_011_227, "新疆"),
    BING_TUAN(10_012_367, "兵团");


    /**
     * 地区枚举值
     */
    private final int value;

    /**
     * 区域名称
     */
    private final String text;

    SpreadComAreaEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }
}
