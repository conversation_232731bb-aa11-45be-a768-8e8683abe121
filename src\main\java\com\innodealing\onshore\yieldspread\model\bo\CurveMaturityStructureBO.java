package com.innodealing.onshore.yieldspread.model.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 曲线期限结构
 *
 * <AUTHOR>
 */
public class CurveMaturityStructureBO implements Comparable<BigDecimal> {

    @ApiModelProperty("曲线唯一编码")
    private Long curveUniCode;

    @ApiModelProperty("剩余期限(年)")
    private BigDecimal remainingTenor;

    @ApiModelProperty("收益率(%)")
    private BigDecimal yield;

    public Long getCurveUniCode() {
        return curveUniCode;
    }

    public void setCurveUniCode(Long curveUniCode) {
        this.curveUniCode = curveUniCode;
    }

    public BigDecimal getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(BigDecimal remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public BigDecimal getYield() {
        return yield;
    }

    public void setYield(BigDecimal yield) {
        this.yield = yield;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CurveMaturityStructureBO that = (CurveMaturityStructureBO) o;
        return Objects.equals(curveUniCode, that.curveUniCode) && Objects.equals(remainingTenor, that.remainingTenor) && Objects.equals(yield, that.yield);
    }

    @Override
    public int hashCode() {
        return Objects.hash(curveUniCode, remainingTenor, yield);
    }

    @Override
    public int compareTo(BigDecimal remainingTenor) {
        if (Objects.isNull(remainingTenor)) {
            return 1;
        }
        if (Objects.isNull(this.remainingTenor)) {
            return -1;
        }
        return this.remainingTenor.compareTo(remainingTenor);
    }

}
