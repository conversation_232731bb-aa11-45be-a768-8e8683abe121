package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 单券利差请求参数
 *
 * <AUTHOR>
 */
public class CurveBondRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("债券唯一编码")
    @NotNull(message = "债券唯一编码不能为空")
    private Long bondUniCode;
    @ApiModelProperty("曲线名称(导出时前端出入)")
    private String curveName;
    @ApiModelProperty("曲线类型 1:产业 2:城投 3:银行 4:证券 5:自定义 7：保险")
    @NotNull(message = "曲线类型不能为空")
    private Integer curveType;

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

    public Integer getCurveType() {
        return curveType;
    }

    public void setCurveType(Integer curveType) {
        this.curveType = curveType;
    }
}
