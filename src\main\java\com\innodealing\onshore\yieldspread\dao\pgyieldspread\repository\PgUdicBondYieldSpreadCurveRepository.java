package com.innodealing.onshore.yieldspread.dao.pgyieldspread.repository;

import com.github.wz2cool.dynamic.*;
import com.innodealing.onshore.yieldspread.dao.pgshard.partition.UdicBondShardYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.enums.UdicRegionEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveShardEnum;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgUdicBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.*;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.UdicPanoramaRequestDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgUdicBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgUdicBondYieldSpreadGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.*;
import com.innodealing.onshore.yieldspread.router.annotation.ShardYieldSpreadCurve;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.github.wz2cool.dynamic.helper.CommonsHelper.getPropertyName;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.MIN_BOND_SIZE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 城投利差曲线数据源
 *
 * <AUTHOR>
 */
@Repository
public class PgUdicBondYieldSpreadCurveRepository implements PgUdicBondYieldSpreadCurveMapper {

    @Resource
    private PgUdicBondYieldSpreadCurveAllMapper pgUdicBondYieldSpreadCurveAllMapper;

    @Resource
    private PgUdicBondYieldSpreadCurveCityMapper pgUdicBondYieldSpreadCurveCityMapper;

    @Resource
    private PgUdicBondYieldSpreadCurveProvinceMapper pgUdicBondYieldSpreadCurveProvinceMapper;

    @Resource
    private PgUdicBondYieldSpreadCurveDistrictMapper pgUdicBondYieldSpreadCurveDistrictMapper;

    @Resource
    private PgUdicBondYieldSpreadCurveMapper pgUdicBondYieldSpreadCurveMapper;

    @Resource
    private PgUdicBondYieldSpreadGroupMapper pgUdicBondYieldSpreadGroupMapper;

    /**
     * 城投利差曲线查询
     *
     * @param request request
     * @return list
     */
    public List<PgUdicBondYieldSpreadGroupDO> listUdicBondYieldInduSpreads(UdicPanoramaRequestDTO request) {
        GroupByQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> and = GroupByQuery.createQuery(PgUdicBondYieldSpreadDO.class,
                        PgUdicBondYieldSpreadGroupDO.class)
                .select(PgUdicBondYieldSpreadGroupDO::getSpreadDate,
                        PgUdicBondYieldSpreadGroupDO::getBondCreditSpread, PgUdicBondYieldSpreadGroupDO::getBondExcessSpread,
                        PgUdicBondYieldSpreadGroupDO::getBondCreditSpreadCount, PgUdicBondYieldSpreadGroupDO::getBondExcessSpreadCount)
                .and(PgUdicBondYieldSpreadDO::getSpreadDate, isEqual(request.getSpreadDate()))
                .and(Objects.nonNull(request.getBondExtRatingMapping()), PgUdicBondYieldSpreadDO::getBondExtRatingMapping, isEqual(request.getBondExtRatingMapping()))
                .and(Objects.nonNull(request.getBondImpliedRatingMappingTag()), PgUdicBondYieldSpreadDO::getBondImpliedRatingMappingTag,
                        isEqual(request.getBondImpliedRatingMappingTag()))
                .and(ArrayUtils.isNotEmpty(request.getBondImpliedRatingMappings()),
                        PgUdicBondYieldSpreadDO::getBondImpliedRatingMapping, in(request.getBondImpliedRatingMappings()))
                .and(ArrayUtils.isNotEmpty(request.getComYyRatingMappings()), PgUdicBondYieldSpreadDO::getComYyRatingMapping, in(request.getComYyRatingMappings()))
                .and(Objects.nonNull(request.getComYyRatingMappingTag()), PgUdicBondYieldSpreadDO::getComYyRatingMappingTag, isEqual(request.getComYyRatingMappingTag()))
                .and(Objects.nonNull(request.getSpreadBondType()), PgUdicBondYieldSpreadDO::getSpreadBondType, isEqual(request.getSpreadBondType()))
                .and(Objects.nonNull(request.getSpreadRemainingTenorTag()), PgUdicBondYieldSpreadDO::getSpreadRemainingTenorTag,
                        isEqual(request.getSpreadRemainingTenorTag()))
                .and(Objects.nonNull(request.getGuaranteeStatus()), PgUdicBondYieldSpreadDO::getGuaranteedStatus, isEqual(request.getGuaranteeStatus()))
                .and(Objects.nonNull(request.getAdministrativeDivision()), PgUdicBondYieldSpreadDO::getAdministrativeDivision,
                        isEqual(request.getAdministrativeDivision()))
                .and(Objects.nonNull(request.getProvinceUniCode()), PgUdicBondYieldSpreadDO::getProvinceUniCode, isEqual(request.getProvinceUniCode()));
        GroupedQuery<PgUdicBondYieldSpreadDO, PgUdicBondYieldSpreadGroupDO> groupedQuery;
        if (Objects.isNull(request.getProvinceUniCode())) {
            //这里如果省份编码为null 就查询所有省份的中位数集合
            and.addSelectedProperties(getPropertyName(PgUdicBondYieldSpreadGroupDO::getProvinceUniCode));
            and.and(PgUdicBondYieldSpreadDO::getProvinceUniCode, notEqual(null));
            groupedQuery = and.groupBy(PgUdicBondYieldSpreadDO::getProvinceUniCode, PgUdicBondYieldSpreadDO::getSpreadDate);
        } else {
            //这里如果省份编码不为null 就查询指定省份所有城市的中位数集合
            and.addSelectedProperties(getPropertyName(PgUdicBondYieldSpreadGroupDO::getCityUniCode));
            and.and(PgUdicBondYieldSpreadDO::getCityUniCode, notEqual(null));
            groupedQuery = and.groupBy(PgUdicBondYieldSpreadDO::getSpreadDate, PgUdicBondYieldSpreadDO::getCityUniCode);
        }
        groupedQuery.and(PgUdicBondYieldSpreadGroupDO::getSpreadCount, greaterThanOrEqual(MIN_BOND_SIZE));
        return pgUdicBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 城投利差曲线查询
     *
     * @param searchParameter request
     * @return Optional
     */
    @ShardYieldSpreadCurve(type = UdicBondShardYieldSpreadCurveRepository.class, method = "firstUdicShardBondYieldSpreadCurve")
    public Optional<PgUdicBondYieldSpreadCurveAllDO> getInduBondYieldSpreadPanorama(UdicBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveAllDO>[] baseFilterDescriptors = listFilters(searchParameter, PgUdicBondYieldSpreadCurveAllDO.class).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveAllDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveAllDO.class)
                .select(PgUdicBondYieldSpreadCurveAllDO::getSpreadDate,
                        PgUdicBondYieldSpreadCurveAllDO::getBondCreditSpread,
                        PgUdicBondYieldSpreadCurveAllDO::getBondExcessSpread,
                        PgUdicBondYieldSpreadCurveAllDO::getBondCreditSpreadCount,
                        PgUdicBondYieldSpreadCurveAllDO::getBondExcessSpreadCount).and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveAllMapper.selectFirstByDynamicQuery(query);
    }

    /**
     * 城投利差曲线查询
     *
     * @param searchParameter request
     * @return Optional
     */
    @ShardYieldSpreadCurve(type = UdicBondShardYieldSpreadCurveRepository.class, method = "listUdicShardBondYieldSpreadCurves")
    public List<PgUdicBondYieldSpreadCurveAllDO> listInduBondYieldSpreadPanoramas(UdicBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveAllDO>[] baseFilterDescriptors = listFilters(searchParameter, PgUdicBondYieldSpreadCurveAllDO.class).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveAllDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveAllDO.class)
                .select(PgUdicBondYieldSpreadCurveAllDO::getSpreadDate,
                        PgUdicBondYieldSpreadCurveAllDO::getBondCreditSpread,
                        PgUdicBondYieldSpreadCurveAllDO::getBondExcessSpread,
                        PgUdicBondYieldSpreadCurveAllDO::getBondCreditSpreadCount,
                        PgUdicBondYieldSpreadCurveAllDO::getBondExcessSpreadCount).and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveAllMapper.selectByDynamicQuery(query);
    }

    /**
     * 同步数据
     */
    public void syncCurveIncrFromMV() {
        pgUdicBondYieldSpreadCurveAllMapper.syncCurveIncrFromMV();
        pgUdicBondYieldSpreadCurveCityMapper.syncCurveIncrFromMV();
        pgUdicBondYieldSpreadCurveProvinceMapper.syncCurveIncrFromMV();
        pgUdicBondYieldSpreadCurveDistrictMapper.syncCurveIncrFromMV();
    }

    /**
     * 刷新物化视图
     *
     * @param udicRegionEnum 城投区域
     */
    public void refreshUdicBondYieldSpreadCurveFromMV(UdicRegionEnum udicRegionEnum) {
        if (Objects.isNull(udicRegionEnum) || udicRegionEnum == UdicRegionEnum.ALL) {
            pgUdicBondYieldSpreadCurveAllMapper.refreshUdicBondYieldSpreadCurveFromMV();
        }
        if (Objects.isNull(udicRegionEnum) || udicRegionEnum == UdicRegionEnum.PROVINCE) {
            pgUdicBondYieldSpreadCurveProvinceMapper.refreshUdicBondYieldSpreadCurveFromMV();
        }
        if (Objects.isNull(udicRegionEnum) || udicRegionEnum == UdicRegionEnum.CITY) {
            pgUdicBondYieldSpreadCurveCityMapper.refreshUdicBondYieldSpreadCurveFromMV();
        }
        if (Objects.isNull(udicRegionEnum) || udicRegionEnum == UdicRegionEnum.DISTRICT) {
            pgUdicBondYieldSpreadCurveDistrictMapper.refreshUdicBondYieldSpreadCurveFromMV();
        }
    }

    /**
     * 城投利差曲线查询
     *
     * @param searchParameter request
     * @return list
     */
    @ShardYieldSpreadCurve(type = UdicBondShardYieldSpreadCurveRepository.class, method = "listUdicShardBondYieldSpreadCurves")
    public List<PgUdicBondYieldSpreadCurveAllDO> listYieldSpreadCurvesForAll(UdicBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveAllDO>[] baseFilterDescriptors = listFilters(searchParameter, PgUdicBondYieldSpreadCurveAllDO.class).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveAllDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveAllDO.class)
                .select(PgUdicBondYieldSpreadCurveAllDO::getSpreadDate,
                        PgUdicBondYieldSpreadCurveAllDO::getBondCreditSpread,
                        PgUdicBondYieldSpreadCurveAllDO::getBondExcessSpread,
                        PgUdicBondYieldSpreadCurveAllDO::getCbYield,
                        PgUdicBondYieldSpreadCurveAllDO::getBondCreditSpreadCount,
                        PgUdicBondYieldSpreadCurveAllDO::getBondExcessSpreadCount,
                        PgUdicBondYieldSpreadCurveAllDO::getCbYieldCount).and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveAllMapper.selectByDynamicQuery(query);
    }

    /**
     * 城投利差曲线查询
     *
     * @param searchParameter request
     * @return List
     */
    @ShardYieldSpreadCurve(type = UdicBondShardYieldSpreadCurveRepository.class, method = "listUdicShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.OPERATOR_CITY)
    public List<PgUdicBondYieldSpreadCurveCityDO> listYieldSpreadCurvesForCity(UdicBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveCityDO>[] baseFilterDescriptors = this.listFilters(searchParameter, PgUdicBondYieldSpreadCurveCityDO.class)
                .and(PgUdicBondYieldSpreadCurveCityDO::getCityUniCode, isEqual(searchParameter.getCityUniCode())).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveCityDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveCityDO.class)
                .select(PgUdicBondYieldSpreadCurveCityDO::getSpreadDate,
                        PgUdicBondYieldSpreadCurveCityDO::getBondCreditSpread,
                        PgUdicBondYieldSpreadCurveCityDO::getBondExcessSpread,
                        PgUdicBondYieldSpreadCurveCityDO::getCbYield,
                        PgUdicBondYieldSpreadCurveCityDO::getBondCreditSpreadCount,
                        PgUdicBondYieldSpreadCurveCityDO::getBondExcessSpreadCount,
                        PgUdicBondYieldSpreadCurveCityDO::getCbYieldCount)
                .and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveCityMapper.selectByDynamicQuery(query);
    }

    /**
     * 城投利差曲线查询
     *
     * @param searchParameter request
     * @return List
     */
    @ShardYieldSpreadCurve(type = UdicBondShardYieldSpreadCurveRepository.class, method = "listUdicShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.OPERATOR_PROVINCE)
    public List<PgUdicBondYieldSpreadCurveProvinceDO> listYieldSpreadCurvesForProvince(UdicBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveProvinceDO>[] baseFilterDescriptors = this.listFilters(searchParameter, PgUdicBondYieldSpreadCurveProvinceDO.class)
                .and(PgUdicBondYieldSpreadCurveProvinceDO::getProvinceUniCode, isEqual(searchParameter.getProvinceUniCode())).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveProvinceDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveProvinceDO.class)
                .select(PgUdicBondYieldSpreadCurveProvinceDO::getSpreadDate,
                        PgUdicBondYieldSpreadCurveProvinceDO::getBondCreditSpread,
                        PgUdicBondYieldSpreadCurveProvinceDO::getBondExcessSpread,
                        PgUdicBondYieldSpreadCurveProvinceDO::getCbYield,
                        PgUdicBondYieldSpreadCurveProvinceDO::getBondCreditSpreadCount,
                        PgUdicBondYieldSpreadCurveProvinceDO::getBondExcessSpreadCount,
                        PgUdicBondYieldSpreadCurveProvinceDO::getCbYieldCount)
                .and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveProvinceMapper.selectByDynamicQuery(query);
    }

    private <T extends BasePgUdicBondYieldSpreadCurveDO> FilterGroupDescriptor<T> listFilters(UdicBondYieldSpreadParamDTO request, Class<T> clazz) {
        return FilterGroupDescriptor.create(clazz)
                .and(nonNull(request.getSpreadBondType()), T::getSpreadBondType, isEqual(request.getSpreadBondType()))
                .and(isNull(request.getSpreadBondType()), T::getUsingSpreadBondType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getBondExtRatingMapping()), T::getBondExtRatingMapping, isEqual(request.getBondExtRatingMapping()))
                .and(isNull(request.getBondExtRatingMapping()), T::getUsingBondExtRatingMapping, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getGuaranteeStatus()), T::getGuaranteedStatus, isEqual(request.getGuaranteeStatus()))
                .and(isNull(request.getGuaranteeStatus()), T::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getSpreadRemainingTenorTag()), T::getSpreadRemainingTenorTag, isEqual(request.getSpreadRemainingTenorTag()))
                .and(isNull(request.getSpreadRemainingTenorTag()), T::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getAdministrativeDivision()), T::getAdministrativeDivision, isEqual(request.getAdministrativeDivision()))
                .and(isNull(request.getAdministrativeDivision()), T::getUsingAdministrativeDivision, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getStartSpreadDate()), T::getSpreadDate, greaterThanOrEqual(request.getStartSpreadDate()))
                .and(nonNull(request.getEndSpreadDate()), T::getSpreadDate, lessThanOrEqual(request.getEndSpreadDate()))
                .and(nonNull(request.getSpreadDate()), T::getSpreadDate, isEqual(request.getSpreadDate()));
    }

    public void createTableRatingRouter(UdicBondYieldSpreadCurveParameter parameter) {
        pgUdicBondYieldSpreadCurveMapper.createTableRatingRouter(parameter);
    }

    public void syncCurveIncrFromMV(String tableName, String mvTableName) {
        pgUdicBondYieldSpreadCurveMapper.syncCurveIncrFromMV(tableName, mvTableName);
    }

    public Boolean dropMv(String tableName) {
        return pgUdicBondYieldSpreadCurveMapper.dropMv(tableName);
    }

    public Boolean dropTable(String tableName) {
        return pgUdicBondYieldSpreadCurveMapper.dropTable(tableName);
    }

    @Override
    public Boolean tableIsExists(String tableName) {
        return pgUdicBondYieldSpreadCurveMapper.tableIsExists(tableName);
    }

    @Override
    public void refreshMvInduBondYieldSpreadCurve(String tableName) {
        pgUdicBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurve(tableName);
    }

    /**
     * 城投利差查询
     *
     * @param searchParam request
     * @return list
     */
    @ShardYieldSpreadCurve(type = UdicBondShardYieldSpreadCurveRepository.class, method = "listUdicShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.ALL)
    public List<PgUdicBondYieldSpreadCurveAllDO> listYieldSpreadsFromAll(UdicYieldSearchParam searchParam) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveAllDO>[] baseFilterDescriptors = listFiltersForAll(searchParam, PgUdicBondYieldSpreadCurveAllDO.class).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveAllDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveAllDO.class).and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveAllMapper.selectByDynamicQuery(query);
    }

    /**
     * 城投利差查询
     *
     * @param searchParam request
     * @return list
     */
    @ShardYieldSpreadCurve(type = UdicBondShardYieldSpreadCurveRepository.class, method = "listUdicShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.OPERATOR_CITY)
    public List<PgUdicBondYieldSpreadCurveCityDO> listYieldSpreadsFromCity(UdicYieldSearchParam searchParam) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveCityDO>[] baseFilterDescriptors = listFiltersForAll(searchParam, PgUdicBondYieldSpreadCurveCityDO.class)
                .and(PgUdicBondYieldSpreadCurveCityDO::getCityUniCode, isEqual(searchParam.getCityUniCode())).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveCityDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveCityDO.class).and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveCityMapper.selectByDynamicQuery(query);
    }

    /**
     * 城投利差查询
     *
     * @param searchParam request
     * @return list
     */
    @ShardYieldSpreadCurve(type = UdicBondShardYieldSpreadCurveRepository.class, method = "listUdicShardBondYieldSpreadCurves", level = YieldSpreadCurveShardEnum.OPERATOR_PROVINCE)
    public List<PgUdicBondYieldSpreadCurveProvinceDO> listYieldSpreadsFromProvince(UdicYieldSearchParam searchParam) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveProvinceDO>[] baseFilterDescriptors = listFiltersForAll(searchParam, PgUdicBondYieldSpreadCurveProvinceDO.class)
                .and(PgUdicBondYieldSpreadCurveProvinceDO::getProvinceUniCode, isEqual(searchParam.getProvinceUniCode())).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveProvinceDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveProvinceDO.class).and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveProvinceMapper.selectByDynamicQuery(query);
    }

    private <T extends BasePgUdicBondYieldSpreadCurveDO> FilterGroupDescriptor<T> listFiltersForAll(UdicYieldSearchParam request, Class<T> clazz) {
        return FilterGroupDescriptor.create(clazz)
                .and(nonNull(request.getSpreadBondType()), T::getSpreadBondType, isEqual(request.getSpreadBondType()))
                .and(isNull(request.getSpreadBondType()), T::getUsingSpreadBondType, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getBondExtRatingMapping()), T::getBondExtRatingMapping, isEqual(request.getBondExtRatingMapping()))
                .and(isNull(request.getBondExtRatingMapping()), T::getUsingBondExtRatingMapping, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getGuaranteedStatus()), T::getGuaranteedStatus, isEqual(request.getGuaranteedStatus()))
                .and(isNull(request.getGuaranteedStatus()), T::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getRemainingTenor()), T::getSpreadRemainingTenorTag, isEqual(request.getRemainingTenor()))
                .and(isNull(request.getRemainingTenor()), T::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getAdministrativeDivision()), T::getAdministrativeDivision, isEqual(request.getAdministrativeDivision()))
                .and(isNull(request.getAdministrativeDivision()), T::getUsingAdministrativeDivision, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(request.getStartSpreadDate()), T::getSpreadDate, greaterThanOrEqual(request.getStartSpreadDate()))
                .and(nonNull(request.getEndSpreadDate()), T::getSpreadDate, lessThanOrEqual(request.getEndSpreadDate()))
                .and(nonNull(request.getSpreadDate()), T::getSpreadDate, isEqual(request.getSpreadDate()));
    }

    /**
     * 城投利差查询(区县)
     *
     * @param searchParam request
     * @return list
     */
    public List<PgUdicBondYieldSpreadCurveDistrictDO> listYieldSpreadsFromDistrict(UdicYieldSearchParam searchParam) {
        BaseFilterDescriptor<PgUdicBondYieldSpreadCurveDistrictDO>[] baseFilterDescriptors = FilterGroupDescriptor.create(PgUdicBondYieldSpreadCurveDistrictDO.class)
                .and(nonNull(searchParam.getGuaranteedStatus()), PgUdicBondYieldSpreadCurveDistrictDO::getGuaranteedStatus, isEqual(searchParam.getGuaranteedStatus()))
                .and(isNull(searchParam.getGuaranteedStatus()), PgUdicBondYieldSpreadCurveDistrictDO::getUsingGuaranteedStatus, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(searchParam.getStartSpreadDate()), PgUdicBondYieldSpreadCurveDistrictDO::getSpreadDate, greaterThanOrEqual(searchParam.getStartSpreadDate()))
                .and(nonNull(searchParam.getEndSpreadDate()), PgUdicBondYieldSpreadCurveDistrictDO::getSpreadDate, lessThanOrEqual(searchParam.getEndSpreadDate()))
                .and(nonNull(searchParam.getSpreadDate()), PgUdicBondYieldSpreadCurveDistrictDO::getSpreadDate, isEqual(searchParam.getSpreadDate()))
                .and(PgUdicBondYieldSpreadCurveDistrictDO::getDistrictUniCode, isEqual(searchParam.getDistrictUniCode())).getFilters();
        DynamicQuery<PgUdicBondYieldSpreadCurveDistrictDO> query = DynamicQuery.createQuery(PgUdicBondYieldSpreadCurveDistrictDO.class).and(baseFilterDescriptors);
        return pgUdicBondYieldSpreadCurveDistrictMapper.selectByDynamicQuery(query);

    }

}
