package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicAreaYieldSpreadDO;

/**
 * dm城投口径区域利差表DynamicQueryMapper层 {@link UdicAreaYieldSpreadDO}
 *
 * <AUTHOR>
 */
public interface UdicAreaYieldSpreadMapper extends DynamicQueryMapper<UdicAreaYieldSpreadDO> {

}

