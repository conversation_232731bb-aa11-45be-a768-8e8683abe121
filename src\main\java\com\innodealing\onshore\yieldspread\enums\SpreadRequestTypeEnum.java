package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 产业债利差/城投债利差tab页中的请求类型
 *
 * <AUTHOR>
 */
public enum SpreadRequestTypeEnum implements ITextValueEnum {
    /**
     * 雷达关注组
     */
    FAVORITE_GROUP(1, "雷达关注组"),
    /**
     * 组合条件
     */
    GROUP_CONDITION(2, "组合条件"),
    /**
     * 主体利差
     */
    COM_SPREAD(3, "主体利差"),
    /**
     * 单券利差
     */
    BOND_SPREAD(4, "单券利差");

    private final Integer code;
    private final String text;

    SpreadRequestTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }
}
