package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.BondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BondYieldSpreadDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 债券利差
 *
 * <AUTHOR>
 */
@Repository
public class BondYieldSpreadDAO {

    @Resource
    private BondYieldSpreadMapper bondYieldSpreadMapper;

    @Resource(name = YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 根据日期和BondUniCode获取利差数据
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券code
     * @return 债券利差数据
     */
    public List<BondYieldSpreadDO> list(Date spreadDate, List<Long> bondUniCodes) {
        if (Objects.isNull(spreadDate) || CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        Long minId = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxId = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<BondYieldSpreadDO> query = DynamicQuery.createQuery(BondYieldSpreadDO.class)
                .and(BondYieldSpreadDO::getId, between(minId, maxId))
                .and(BondYieldSpreadDO::getBondUniCode, DynamicQueryBuilderHelper.in(bondUniCodes))
                .and(BondYieldSpreadDO::getSpreadDate, DynamicQueryBuilderHelper.isEqual(spreadDate));
        return bondYieldSpreadMapper.selectByDynamicQuery(query);
    }

    /**
     * 根据日期和BondUniCode获取利差数据
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券code
     * @return 债券利差数据
     */
    public List<BondYieldSpreadShortBO> listBondsYieldSpread(Date spreadDate, List<Long> bondUniCodes) {
        if (Objects.isNull(spreadDate) || CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        Long minId = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxId = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<BondYieldSpreadDO> query = DynamicQuery.createQuery(BondYieldSpreadDO.class)
                .select(BondYieldSpreadDO::getId,
                        BondYieldSpreadDO::getBondUniCode,
                        BondYieldSpreadDO::getSpreadDate,
                        BondYieldSpreadDO::getBondCreditSpread,
                        BondYieldSpreadDO::getBondExcessSpread)
                .and(BondYieldSpreadDO::getBondUniCode, DynamicQueryBuilderHelper.in(bondUniCodes))
                .and(BondYieldSpreadDO::getSpreadDate, DynamicQueryBuilderHelper.isEqual(spreadDate))
                .and(BondYieldSpreadDO::getId, between(minId, maxId));
        return BeanCopyUtils.copyList(bondYieldSpreadMapper.selectByDynamicQuery(query), BondYieldSpreadShortBO.class);
    }

    /**
     * 获取单个债券的时间范围内的利差数据，按利差日期从小到大排序
     *
     * @param bondUniCodes 债券code
     * @param startDate    开始时间
     * @param endDate      结束时间
     * @return 债券利差
     */
    public List<BondYieldSpreadShortBO> listBondsYieldSpreads(List<Long> bondUniCodes, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(bondUniCodes) || Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Collections.emptyList();
        }
        Long minId = ShardingUtils.getMinPkOfDate(startDate);
        Long maxId = ShardingUtils.getMaxPkOfDate(endDate);
        DynamicQuery<BondYieldSpreadDO> query = DynamicQuery.createQuery(BondYieldSpreadDO.class)
                .select(BondYieldSpreadDO::getId,
                        BondYieldSpreadDO::getBondUniCode,
                        BondYieldSpreadDO::getSpreadDate,
                        BondYieldSpreadDO::getBondCreditSpread,
                        BondYieldSpreadDO::getBondExcessSpread)
                .and(BondYieldSpreadDO::getBondUniCode, in(bondUniCodes))
                .and(BondYieldSpreadDO::getId, between(minId, maxId))
                .orderBy(BondYieldSpreadDO::getSpreadDate, SortDirections::asc);
        return BeanCopyUtils.copyList(bondYieldSpreadMapper.selectByDynamicQuery(query), BondYieldSpreadShortBO.class);
    }

    /**
     * 物理删除
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes BondUniCode
     */
    public void physicalDelete(Date spreadDate, List<Long> bondUniCodes) {
        if (Objects.isNull(spreadDate) || CollectionUtils.isEmpty(bondUniCodes)) {
            return;
        }
        Long minId = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxId = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<BondYieldSpreadDO> query = DynamicQuery.createQuery(BondYieldSpreadDO.class)
                .and(BondYieldSpreadDO::getBondUniCode, in(bondUniCodes))
                .and(BondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(BondYieldSpreadDO::getId, between(minId, maxId));
        bondYieldSpreadMapper.deleteByDynamicQuery(query);
    }

    /**
     * 新增
     *
     * @param insertList 债券利差实体
     */
    public void batchInsert(List<BondYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }
        MapperBatchAction<BondYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(BondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (BondYieldSpreadDO bondYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(bondYieldSpreadDO));
        }
        insertBatchAction.doBatchActions();
    }

    /**
     * 创建分片表
     *
     * @param shardingTableName 表名
     */
    public void createShardingTable(String shardingTableName) {
        bondYieldSpreadMapper.createShardingTable(shardingTableName);
    }

}
