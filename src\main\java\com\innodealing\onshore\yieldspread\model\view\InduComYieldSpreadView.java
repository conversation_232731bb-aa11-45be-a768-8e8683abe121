package com.innodealing.onshore.yieldspread.model.view;

import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO;

import javax.persistence.Column;
import java.math.BigDecimal;


/**
 * 行业主体利差视图
 *
 * <AUTHOR>
 * @date 2022-10-08
 */
public class InduComYieldSpreadView extends InduComYieldSpreadDO {
    /**
     * 信用利差近3个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_change_3m")
    private BigDecimal creditSpreadChange3M;
    /**
     * 信用利差近6个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_change_6m")
    private BigDecimal creditSpreadChange6M;
    /**
     * 超额利差近3个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_change_3m")
    private BigDecimal excessSpreadChange3M;
    /**
     * 超额利差近6个月变动;单位(BP)
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_change_6m")
    private BigDecimal excessSpreadChange6M;
    /**
     * 信用利差3年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_quantile_3y")
    private BigDecimal creditSpreadQuantile3Y;

    /**
     * 信用利差5年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "credit_spread_quantile_5y")
    private BigDecimal creditSpreadQuantile5Y;

    /**
     * 超额利差3年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_quantile_3y")
    private BigDecimal excessSpreadQuantile3Y;

    /**
     * 超额利差5年历史分位
     */
    @Column(table = "com_yield_spread_change", name = "excess_spread_quantile_5y")
    private BigDecimal excessSpreadQuantile5Y;

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }
}
