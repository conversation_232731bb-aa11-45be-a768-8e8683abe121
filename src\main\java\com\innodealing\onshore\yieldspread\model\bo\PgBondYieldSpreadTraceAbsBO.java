package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;


/**
 * 利差追踪绝对值DO
 *
 * <AUTHOR>
 */
public class PgBondYieldSpreadTraceAbsBO {

    /**
     * 1月到期收益率
     */
    private BigDecimal ytm1M;
    /**
     * 3月到期收益率
     */
    private BigDecimal ytm3M;
    /**
     * 6月到期收益率
     */
    private BigDecimal ytm6M;
    /**
     * 9月到期收益率
     */
    private BigDecimal ytm9M;

    /**
     * 1年到期收益率
     */
    private BigDecimal ytm1Y;

    /**
     * 2年到期收益率
     */
    private BigDecimal ytm2Y;

    /**
     * 3年到期收益率
     */
    private BigDecimal ytm3Y;

    /**
     * 5年到期收益率
     */
    private BigDecimal ytm5Y;

    /**
     * 7年到期收益率
     */
    private BigDecimal ytm7Y;

    /**
     * 10年到期收益率
     */
    private BigDecimal ytm10Y;
    /**
     * 15年到期收益率
     */
    private BigDecimal ytm15Y;
    /**
     * 20年到期收益率
     */
    private BigDecimal ytm20Y;
    /**
     * 30年到期收益率
     */
    private BigDecimal ytm30Y;
    /**
     * 50年到期收益率
     */
    private BigDecimal ytm50Y;


    /**
     * 发布日期
     */
    private Date issueDate;
    /**
     * 曲线代码
     */
    private Integer curveCode;

    public BigDecimal getYtm1Y() {
        return ytm1Y;
    }

    public void setYtm1Y(BigDecimal ytm1Y) {
        this.ytm1Y = ytm1Y;
    }

    public BigDecimal getYtm2Y() {
        return ytm2Y;
    }

    public void setYtm2Y(BigDecimal ytm2Y) {
        this.ytm2Y = ytm2Y;
    }

    public BigDecimal getYtm3Y() {
        return ytm3Y;
    }

    public void setYtm3Y(BigDecimal ytm3Y) {
        this.ytm3Y = ytm3Y;
    }

    public BigDecimal getYtm5Y() {
        return ytm5Y;
    }

    public void setYtm5Y(BigDecimal ytm5Y) {
        this.ytm5Y = ytm5Y;
    }

    public BigDecimal getYtm7Y() {
        return ytm7Y;
    }

    public void setYtm7Y(BigDecimal ytm7Y) {
        this.ytm7Y = ytm7Y;
    }

    public Date getIssueDate() {
        return Objects.isNull(issueDate) ? null : new Date(issueDate.getTime());
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = Objects.isNull(issueDate) ? null : new Date(issueDate.getTime());
    }

    public Integer getCurveCode() {
        return curveCode;
    }

    public void setCurveCode(Integer curveCode) {
        this.curveCode = curveCode;
    }

    public BigDecimal getYtm1M() {
        return ytm1M;
    }

    public void setYtm1M(BigDecimal ytm1M) {
        this.ytm1M = ytm1M;
    }

    public BigDecimal getYtm3M() {
        return ytm3M;
    }

    public void setYtm3M(BigDecimal ytm3M) {
        this.ytm3M = ytm3M;
    }

    public BigDecimal getYtm6M() {
        return ytm6M;
    }

    public void setYtm6M(BigDecimal ytm6M) {
        this.ytm6M = ytm6M;
    }

    public BigDecimal getYtm9M() {
        return ytm9M;
    }

    public void setYtm9M(BigDecimal ytm9M) {
        this.ytm9M = ytm9M;
    }

    public BigDecimal getYtm10Y() {
        return ytm10Y;
    }

    public void setYtm10Y(BigDecimal ytm10Y) {
        this.ytm10Y = ytm10Y;
    }

    public BigDecimal getYtm15Y() {
        return ytm15Y;
    }

    public void setYtm15Y(BigDecimal ytm15Y) {
        this.ytm15Y = ytm15Y;
    }

    public BigDecimal getYtm20Y() {
        return ytm20Y;
    }

    public void setYtm20Y(BigDecimal ytm20Y) {
        this.ytm20Y = ytm20Y;
    }

    public BigDecimal getYtm30Y() {
        return ytm30Y;
    }

    public void setYtm30Y(BigDecimal ytm30Y) {
        this.ytm30Y = ytm30Y;
    }

    public BigDecimal getYtm50Y() {
        return ytm50Y;
    }

    public void setYtm50Y(BigDecimal ytm50Y) {
        this.ytm50Y = ytm50Y;
    }
}