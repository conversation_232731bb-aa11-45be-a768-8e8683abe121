package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;

/**
 * pg库查到的 dm城投口径区域利差表实体对象
 *
 * <AUTHOR>
 */
public class PgUdicAreaYieldSpreadBO {

    /**
     * 区域编码
     */
    private Long areaUniCode;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    private BigDecimal bondCreditSpread;


    public Long getAreaUniCode() {
        return areaUniCode;
    }

    public void setAreaUniCode(Long areaUniCode) {
        this.areaUniCode = areaUniCode;
    }


    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

}

