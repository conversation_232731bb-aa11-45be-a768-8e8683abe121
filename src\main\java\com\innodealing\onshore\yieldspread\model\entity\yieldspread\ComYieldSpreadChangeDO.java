package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * 主体利差变动
 *
 * <AUTHOR>
 **/
@Table(name = "com_yield_spread_change")
public class ComYieldSpreadChangeDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 信用利差近3个月变动;单位(BP)
     */
    @Column(name = "credit_spread_change_3m")
    private BigDecimal creditSpreadChange3M;
    /**
     * 信用利差近6个月变动;单位(BP)
     */
    @Column(name = "credit_spread_change_6m")
    private BigDecimal creditSpreadChange6M;
    /**
     * 超额利差近3个月变动;单位(BP)
     */
    @Column(name = "excess_spread_change_3m")
    private BigDecimal excessSpreadChange3M;
    /**
     * 超额利差近6个月变动;单位(BP)
     */
    @Column(name = "excess_spread_change_6m")
    private BigDecimal excessSpreadChange6M;

    /**
     * 信用利差3年历史分位
     */
    @Column(name = "credit_spread_quantile_3y")
    private BigDecimal creditSpreadQuantile3Y;
    /**
     * 信用利差5年历史分位
     */
    @Column(name = "credit_spread_quantile_5y")
    private BigDecimal creditSpreadQuantile5Y;
    /**
     * 超额利差3年历史分位
     */
    @Column(name = "excess_spread_quantile_3y")
    private BigDecimal excessSpreadQuantile3Y;
    /**
     * 超额利差5年历史分位
     */
    @Column(name = "excess_spread_quantile_5y")
    private BigDecimal excessSpreadQuantile5Y;

    /**
     * 是否城投主体: 0: 否；1：是
     */
    @Column
    private Integer udicStatus;

    /**
     * 主体利差板块
     */
    @Column
    private Integer comSpreadSector;
    /**
     * 是否删除
     */
    @Column
    private Integer deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }

    public Integer getUdicStatus() {
        return udicStatus;
    }

    public void setUdicStatus(Integer udicStatus) {
        this.udicStatus = udicStatus;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getComSpreadSector() {
        return comSpreadSector;
    }

    public void setComSpreadSector(Integer comSpreadSector) {
        this.comSpreadSector = comSpreadSector;
    }
}