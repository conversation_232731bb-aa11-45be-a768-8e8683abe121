package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 城投区域
 *
 * <AUTHOR>
 */
public enum UdicRegionEnum implements ITextValueEnum {
    /**
     * 所有区域
     */
    ALL(1, "所有区域"),
    /**
     * 省
     */
    PROVINCE(2, "省"),
    /**
     * 所有区域
     */
    CITY(3, "市"),
    /**
     * 所有区域
     */
    DISTRICT(4, "区县");

    private final Integer code;

    private final String text;

    UdicRegionEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }

}
