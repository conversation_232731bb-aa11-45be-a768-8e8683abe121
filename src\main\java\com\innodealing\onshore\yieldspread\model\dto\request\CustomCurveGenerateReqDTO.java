package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 自定义曲线保存
 *
 * <AUTHOR>
 */
public class CustomCurveGenerateReqDTO implements ICurveGenerateReq {

    @ApiModelProperty("曲线名称")
    @NotBlank(message = "曲线名称不能为空")
    @Length(message = "自定义曲线名称不能超过 {max} 个字符", max = YieldSpreadConst.CUSTOM_CURVE_NAME_MAX_LENGTH)
    private String spreadCurveName;

    @ApiModelProperty("bondUniCodes")
    @NotEmpty(message = "导入的债券集合不能为空")
    @Size(message = "单条曲线最多能上传 {max} 只债券，请调整后再上传", max = YieldSpreadConst.IMPORT_BOND_SIZE_UPPER_LIMIT)
    private Set<Long> bondUniCodes;

    public String getSpreadCurveName() {
        return spreadCurveName;
    }

    public void setSpreadCurveName(String spreadCurveName) {
        this.spreadCurveName = spreadCurveName;
    }

    public Set<Long> getBondUniCodes() {
        return Objects.isNull(bondUniCodes) ? new HashSet<>() : new HashSet<>(bondUniCodes);
    }

    public void setBondUniCodes(Set<Long> bondUniCodes) {
        this.bondUniCodes = Objects.isNull(bondUniCodes) ? new HashSet<>() : new HashSet<>(bondUniCodes);
    }

    @Override
    public String getCurveName() {
        return getSpreadCurveName();
    }

}
