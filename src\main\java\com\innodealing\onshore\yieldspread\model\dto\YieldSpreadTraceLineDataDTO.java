package com.innodealing.onshore.yieldspread.model.dto;


import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 利差追踪折线图
 *
 * <AUTHOR>
 */
public class YieldSpreadTraceLineDataDTO {

    @ApiModelProperty("类型code")
    private Integer typeCode;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("数据值")
    private BigDecimal[] yields;

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public BigDecimal[] getYields() {
        return Objects.isNull(yields) ? new BigDecimal[0] : yields.clone();
    }

    public void setYields(BigDecimal[] yields) {
        this.yields = Objects.isNull(yields) ? new BigDecimal[0] : yields.clone();
    }
}
