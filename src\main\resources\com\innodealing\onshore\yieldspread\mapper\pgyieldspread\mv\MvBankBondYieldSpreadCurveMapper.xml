<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvBankBondYieldSpreadCurveMapper">


    <sql id="ratingRouterQuerySql">
        SELECT max(bank_bond_yield_spread.id) AS id,
        MEDIAN((bank_bond_yield_spread.bond_credit_spread * 100000::numeric)::bigint) AS bond_credit_spread,
        MEDIAN((bank_bond_yield_spread.bond_excess_spread * 100000::numeric)::bigint) AS bond_excess_spread,
        MEDIAN((bank_bond_yield_spread.cb_yield * 100000::numeric)::bigint) AS cb_yield,
        AVG(((bank_bond_yield_spread.bond_credit_spread * (100000)::numeric))::bigint) AS avg_bond_credit_spread,
        AVG(((bank_bond_yield_spread.bond_excess_spread * (100000)::numeric))::bigint) AS avg_bond_excess_spread,
        AVG(((bank_bond_yield_spread.cb_yield * (100000)::numeric))::bigint) AS avg_cb_yield,
        count(bank_bond_yield_spread.bond_credit_spread) AS bond_credit_spread_count,
        count(bank_bond_yield_spread.bond_excess_spread) AS bond_excess_spread_count,
        count(bank_bond_yield_spread.cb_yield) AS cb_yield_count,
        bank_bond_yield_spread.spread_date,
        bank_bond_yield_spread.bank_seniority_ranking,
        bank_bond_yield_spread.bank_type,
        bank_bond_yield_spread.spread_remaining_tenor_tag,
        grouping(bank_bond_yield_spread.bank_seniority_ranking) AS using_bank_seniority_ranking,
        grouping(bank_bond_yield_spread.bank_type) AS using_bank_type,
        grouping(bank_bond_yield_spread.spread_remaining_tenor_tag) AS using_spread_remaining_tenor_tag
        FROM yield_spread.bank_bond_yield_spread
        where 1=1
        <if test="parameter.spreadDateRange == null">
            AND bank_bond_yield_spread.spread_date = (('now'::text)::date - 1)
        </if>
        <if test="parameter.spreadDateRange != null">
            AND bank_bond_yield_spread.spread_date BETWEEN '${parameter.spreadDateRange.startDate}' and
            '${parameter.spreadDateRange.endDate}'
        </if>
        <if test="parameter.impliedRatingMappings != null and parameter.impliedRatingMappings.size() > 0">
            AND bank_bond_yield_spread.bond_implied_rating_mapping in
            <foreach collection="parameter.impliedRatingMappings" item="item" open="(" close=")" separator=",">
                '${item}'
            </foreach>
        </if>
        GROUP BY bank_bond_yield_spread.spread_date, CUBE (
        bank_bond_yield_spread.bank_seniority_ranking,bank_bond_yield_spread.bank_type,
        bank_bond_yield_spread.spread_remaining_tenor_tag)
    </sql>

    <update id="createMvRatingRouter">
        create MATERIALIZED view yield_spread.${parameter.tableName}
        WITH (appendoptimized= true, orientation = column)
        as
        <include refid="ratingRouterQuerySql"></include>
    </update>

</mapper>