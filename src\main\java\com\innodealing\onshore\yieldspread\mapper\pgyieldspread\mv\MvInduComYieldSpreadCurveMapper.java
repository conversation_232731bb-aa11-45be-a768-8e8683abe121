package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvInduBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvInduComYieldSpreadCurveDO;

/**
 * 行业利差曲线-物化视图
 *
 * <AUTHOR>
 */

public interface MvInduComYieldSpreadCurveMapper extends PgBaseMapper<MvInduBondYieldSpreadCurveParameter>, DynamicQueryMapper<MvInduComYieldSpreadCurveDO> {

}
