package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 主体利差曲线-证券
 *
 * <AUTHOR>
 */
@Table(name = "mv_secu_com_yield_spread_curve")
public class MvSecuComYieldSpreadCurveDO extends BaseMvComYieldSpreadCurveDO {
    /**
     * 证券公司债求偿顺序   1:普通（剔除永续）、2:次级（剔除永续）、 3:永续
     */
    @Column
    private Integer securitySeniorityRanking;
    /**
     * 是否使用证券公司债求偿顺序，0:是 1:否
     */
    @Column
    private Integer usingSecuritySeniorityRanking;

    public Integer getSecuritySeniorityRanking() {
        return securitySeniorityRanking;
    }

    public void setSecuritySeniorityRanking(Integer securitySeniorityRanking) {
        this.securitySeniorityRanking = securitySeniorityRanking;
    }

    public Integer getUsingSecuritySeniorityRanking() {
        return usingSecuritySeniorityRanking;
    }

    public void setUsingSecuritySeniorityRanking(Integer usingSecuritySeniorityRanking) {
        this.usingSecuritySeniorityRanking = usingSecuritySeniorityRanking;
    }
}
