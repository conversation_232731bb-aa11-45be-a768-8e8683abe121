package com.innodealing.onshore.yieldspread.model.dto;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 证券利差曲线参数
 * <AUTHOR>
 */
public class SecuBondYieldSpreadCurveParameter {

    private String tableName;

    private Date startDate;

    private Date endDate;

    private List<Integer> impliedRatingMappings;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public Date getEndDate() {
        return Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public void setEndDate(Date endDate) {
        this.endDate = Objects.isNull(endDate) ? null : new Date(endDate.getTime());
    }

    public List<Integer> getImpliedRatingMappings() {
        return Objects.isNull(impliedRatingMappings) ? new ArrayList<>() : new ArrayList<>(impliedRatingMappings);
    }

    public void setImpliedRatingMappings(List<Integer> impliedRatingMappings) {
        this.impliedRatingMappings = Objects.isNull(impliedRatingMappings) ? new ArrayList<>() : new ArrayList<>(impliedRatingMappings);
    }
}
