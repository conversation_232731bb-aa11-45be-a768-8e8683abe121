package com.innodealing.onshore.yieldspread.service.impl;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.dto.area.AreaInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.enums.AreaTypeEnum;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgUdicBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition.PgInduBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserCurveDAO;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionBO;
import com.innodealing.onshore.yieldspread.model.bo.InduSpreadPanoramaBO;
import com.innodealing.onshore.yieldspread.model.bo.UdicSpreadPanoramaBO;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.SpreadDateDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.CurveDataExportRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.*;
import com.innodealing.onshore.yieldspread.service.*;
import com.innodealing.onshore.yieldspread.service.internal.AreaService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.*;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.subtract;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.MIN_BOND_SIZE;

/**
 * 导出服务
 *
 * <AUTHOR>
 */
@Service
public class ExportServiceImpl implements ExportService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private InduBondYieldSpreadService induBondYieldSpreadService;

    @Resource
    private InduComYieldSpreadService induComYieldSpreadService;

    @Resource
    private UdicBondYieldSpreadService udicBondYieldSpreadService;

    @Resource
    private InsuBondYieldSpreadService insuBondYieldSpreadService;

    @Resource
    private UdicComYieldSpreadService udicComYieldSpreadService;

    @Resource
    private AreaService areaService;

    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;

    @Resource
    private PgUdicBondYieldSpreadDAO pgUdicBondYieldSpreadDAO;

    @Resource
    private PgInduBondYieldSpreadCurveDAO pgInduBondYieldSpreadCurveDAO;

    @Resource
    private CurvePoolService curvePoolService;

    @Resource
    private UserCurveDAO userCurveDAO;

    @Resource
    private BankBondYieldSpreadService bankBondYieldSpreadService;

    @Resource
    private SecuBondYieldSpreadService secuBondYieldSpreadService;

    @Resource
    private CustomCurveService customCurveService;

    @Resource
    private CustomComYieldSpreadService customComYieldSpreadService;

    @Resource
    private BankComYieldSpreadService bankComYieldSpreadService;

    @Resource
    private InsuComYieldSpreadService insuComYieldSpreadService;

    @Resource
    private SecuComYieldSpreadService secuComYieldSpreadService;

    private static final int EXPORT_SEARCH_BATCH_SIZE = 1000;

    @Resource
    private BondYieldSpreadService bondYieldSpreadService;

    @Resource
    private ComYieldSpreadService comYieldSpreadService;

    private final ThreadPoolExecutor executorService = new ThreadPoolExecutor(EXPORT_WORK_THREAD_NUM, EXPORT_WORK_THREAD_NUM, 0,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("ExportServiceImpl-pool-").build());

    @Override
    public List<InduCurveExportExcelDTO> listInduCurveListExcels(InduCurveExportRequestDTO request) {
        if (Objects.isNull(request)) {
            return Collections.emptyList();
        }
        List<InduCurveExportExcelDTO> exportList = Lists.newArrayList();
        exportList.addAll(this.fillInduCurveByCompositionConditions(request));
        exportList.addAll(this.fillInduCurveByComSpreads(request));
        exportList.addAll(this.fillInduCurveByBondSpreads(request));
        return exportList;
    }

    List<InduCurveExportExcelDTO> fillInduCurveByCompositionConditions(InduCurveExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getCompositionConditions())) {
            return Collections.emptyList();
        }
        List<InduCurveExportExcelDTO> exportList = Lists.newArrayList();
        for (InduCurveCompositionConditionDTO compositionCondition : request.getCompositionConditions()) {
            InduCurveRequestDTO induCurveRequest = BeanCopyUtils.copyProperties(request, InduCurveRequestDTO.class);
            induCurveRequest.setCompositionCondition(compositionCondition);
            induCurveRequest.setSpreadRequestType(SpreadRequestTypeEnum.GROUP_CONDITION.getValue());
            List<InduCurveResponseDTO> response = null;
            try {
                response = induBondYieldSpreadService.listCurves(induCurveRequest);
            } catch (TipsException e) {
                logger.warn("fillInduCurveByCompositionConditions error: {}", e.getMessage());
            }
            if (CollectionUtils.isEmpty(response)) {
                continue;
            }
            for (InduCurveResponseDTO curve : response) {
                InduCurveExportExcelDTO excel = BeanCopyUtils.copyProperties(curve, InduCurveExportExcelDTO.class);
                excel.setCurveName(compositionCondition.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    List<InduCurveExportExcelDTO> fillInduCurveByComSpreads(InduCurveExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getComSpreads())) {
            return Collections.emptyList();
        }
        List<InduCurveExportExcelDTO> exportList = Lists.newArrayList();
        for (CurveComSpreadDTO comSpread : request.getComSpreads()) {
            InduCurveRequestDTO induCurveRequest = BeanCopyUtils.copyProperties(request, InduCurveRequestDTO.class);
            induCurveRequest.setComSpread(comSpread);
            induCurveRequest.setSpreadRequestType(SpreadRequestTypeEnum.COM_SPREAD.getValue());
            List<InduCurveResponseDTO> response = null;
            try {
                response = induBondYieldSpreadService.listCurves(induCurveRequest);
            } catch (TipsException e) {
                logger.warn("fillInduCurveByComSpreads error: {}", e.getMessage());
            }
            if (CollectionUtils.isEmpty(response)) {
                continue;
            }
            for (InduCurveResponseDTO curve : response) {
                InduCurveExportExcelDTO excel = BeanCopyUtils.copyProperties(curve, InduCurveExportExcelDTO.class);
                excel.setCurveName(comSpread.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    List<InduCurveExportExcelDTO> fillInduCurveByBondSpreads(InduCurveExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getBondSpreads())) {
            return Collections.emptyList();
        }
        List<InduCurveExportExcelDTO> exportList = Lists.newArrayList();
        for (CurveBondSpreadDTO bondSpread : request.getBondSpreads()) {
            InduCurveRequestDTO induCurveRequest = BeanCopyUtils.copyProperties(request, InduCurveRequestDTO.class);
            induCurveRequest.setBondSpread(bondSpread);
            induCurveRequest.setSpreadRequestType(SpreadRequestTypeEnum.BOND_SPREAD.getValue());
            List<InduCurveResponseDTO> response = induBondYieldSpreadService.listCurves(induCurveRequest);
            if (CollectionUtils.isEmpty(response)) {
                continue;
            }
            for (InduCurveResponseDTO curve : response) {
                InduCurveExportExcelDTO excel = BeanCopyUtils.copyProperties(curve, InduCurveExportExcelDTO.class);
                excel.setCurveName(bondSpread.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    @Override
    public List<InduComSpreadExportExcelDTO> listInduComListExcels(InduListExportRequestDTO request) {
        if (Objects.isNull(request)) {
            return Collections.emptyList();
        }
        List<InduComSpreadExportExcelDTO> exportList = Lists.newArrayList();
        exportList.addAll(this.fillInduComListByCompositionConditions(request));
        return exportList;
    }

    private List<InduComSpreadExportExcelDTO> fillInduComListByCompositionConditions(InduListExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getCompositionConditions())) {
            return Collections.emptyList();
        }
        List<InduComSpreadExportExcelDTO> exportList = Lists.newArrayList();
        for (InduCurveCompositionConditionDTO compositionCondition : request.getCompositionConditions()) {
            InduListRequestDTO induListRequest = BeanCopyUtils.copyProperties(request, InduListRequestDTO.class);
            induListRequest.setCompositionCondition(compositionCondition);
            induListRequest.setPageNum(EXPORT_START_PAGE);
            induListRequest.setPageSize(EXPORT_MAX_ROW);
            NormPagingResult<InduComYieldSpreadResponseDTO> pagingResult = induComYieldSpreadService.getComYieldSpreadPaging(induListRequest);
            for (InduComYieldSpreadResponseDTO response : pagingResult.getList()) {
                InduComSpreadExportExcelDTO excel = BeanCopyUtils.copyProperties(response, InduComSpreadExportExcelDTO.class);
                excel.setCurveName(compositionCondition.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    @Override
    public List<InduBondSpreadExportExcelDTO> listInduBondListExcels(InduListExportRequestDTO request) {
        if (Objects.isNull(request)) {
            return Collections.emptyList();
        }
        List<InduBondSpreadExportExcelDTO> exportList = Lists.newArrayList();
        exportList.addAll(this.fillInduBondListByCompositionConditions(request));
        return exportList;
    }

    private List<InduBondSpreadExportExcelDTO> fillInduBondListByCompositionConditions(InduListExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getCompositionConditions())) {
            return Collections.emptyList();
        }
        List<InduBondSpreadExportExcelDTO> exportList = Lists.newArrayList();
        for (InduCurveCompositionConditionDTO compositionCondition : request.getCompositionConditions()) {
            InduListRequestDTO induListRequest = BeanCopyUtils.copyProperties(request, InduListRequestDTO.class);
            induListRequest.setCompositionCondition(compositionCondition);
            induListRequest.setPageNum(EXPORT_START_PAGE);
            induListRequest.setPageSize(EXPORT_MAX_ROW);
            NormPagingResult<InduBondYieldSpreadResponseDTO> pagingResult = induBondYieldSpreadService.getBondYieldSpreadPaging(induListRequest);
            for (InduBondYieldSpreadResponseDTO response : pagingResult.getList()) {
                InduBondSpreadExportExcelDTO excel = BeanCopyUtils.copyProperties(response, InduBondSpreadExportExcelDTO.class);
                excel.setCurveName(compositionCondition.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    @Override
    public List<UdicSpreadCurveExcelDTO> listUdicCurvesExcels(UdicCurveExportRequestDTO request) {
        if (Objects.isNull(request)) {
            return Collections.emptyList();
        }
        List<UdicSpreadCurveExcelDTO> udicCurveExcelList = new ArrayList<>();
        udicCurveExcelList.addAll(this.fillUdicCurveByCompositionConditions(request));
        udicCurveExcelList.addAll(this.fillUdicCurveByComSpreads(request));
        udicCurveExcelList.addAll(this.fillUdicCurveByBondSpreads(request));
        return udicCurveExcelList;
    }

    private List<UdicSpreadCurveExcelDTO> fillUdicCurveByCompositionConditions(UdicCurveExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getCompositionConditions())) {
            return Collections.emptyList();
        }
        List<UdicSpreadCurveExcelDTO> exportList = Lists.newArrayList();
        for (UdicCurveCompositionConditionDTO compositionCondition : request.getCompositionConditions()) {
            UdicCurveRequestDTO udicCurveRequest = BeanCopyUtils.copyProperties(request, UdicCurveRequestDTO.class);
            udicCurveRequest.setCompositionCondition(compositionCondition);
            udicCurveRequest.setSpreadCurveType(request.getSpreadCurveType());
            udicCurveRequest.setSpreadRequestType(SpreadRequestTypeEnum.GROUP_CONDITION.getValue());
            List<UdicCurveResponseDTO> response = null;
            try {
                response = udicBondYieldSpreadService.listCurves(udicCurveRequest);
            } catch (TipsException e) {
                logger.warn("fillUdicCurveByCompositionConditions error: {}", e.getMessage());
            }
            if (CollectionUtils.isEmpty(response)) {
                continue;
            }
            for (UdicCurveResponseDTO curve : response) {
                UdicSpreadCurveExcelDTO excel = BeanCopyUtils.copyProperties(curve, UdicSpreadCurveExcelDTO.class);
                excel.setCurveName(compositionCondition.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    private List<UdicSpreadCurveExcelDTO> fillUdicCurveByComSpreads(UdicCurveExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getCurveComSpreads())) {
            return Collections.emptyList();
        }
        List<UdicSpreadCurveExcelDTO> exportList = Lists.newArrayList();
        for (CurveComSpreadDTO comSpread : request.getCurveComSpreads()) {
            UdicCurveRequestDTO udicCurveRequest = BeanCopyUtils.copyProperties(request, UdicCurveRequestDTO.class);
            udicCurveRequest.setComSpread(comSpread);
            udicCurveRequest.setSpreadCurveType(request.getSpreadCurveType());
            udicCurveRequest.setSpreadRequestType(SpreadRequestTypeEnum.COM_SPREAD.getValue());
            List<UdicCurveResponseDTO> response = null;
            try {
                response = udicBondYieldSpreadService.listCurves(udicCurveRequest);
            } catch (TipsException e) {
                logger.warn("fillUdicCurveByComSpreads error: {}", e.getMessage());
            }
            if (CollectionUtils.isEmpty(response)) {
                continue;
            }
            for (UdicCurveResponseDTO curve : response) {
                UdicSpreadCurveExcelDTO excel = BeanCopyUtils.copyProperties(curve, UdicSpreadCurveExcelDTO.class);
                excel.setCurveName(comSpread.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    private List<UdicSpreadCurveExcelDTO> fillUdicCurveByBondSpreads(UdicCurveExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getCurveBondSpreads())) {
            return Collections.emptyList();
        }
        List<UdicSpreadCurveExcelDTO> exportList = Lists.newArrayList();
        for (CurveBondSpreadDTO bondSpread : request.getCurveBondSpreads()) {
            UdicCurveRequestDTO udicCurveRequest = BeanCopyUtils.copyProperties(request, UdicCurveRequestDTO.class);
            udicCurveRequest.setBondSpread(bondSpread);
            udicCurveRequest.setSpreadCurveType(request.getSpreadCurveType());
            udicCurveRequest.setSpreadRequestType(SpreadRequestTypeEnum.BOND_SPREAD.getValue());
            List<UdicCurveResponseDTO> response = udicBondYieldSpreadService.listCurves(udicCurveRequest);
            if (CollectionUtils.isEmpty(response)) {
                continue;
            }
            for (UdicCurveResponseDTO curve : response) {
                UdicSpreadCurveExcelDTO excel = BeanCopyUtils.copyProperties(curve, UdicSpreadCurveExcelDTO.class);
                excel.setCurveName(bondSpread.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    @Override
    public List<UdicComSpreadExportExcelDTO> listUdicComSpreadExcels(UdicListExportRequestDTO request) {
        if (Objects.isNull(request)) {
            return Collections.emptyList();
        }
        List<UdicComSpreadExportExcelDTO> exportList = Lists.newArrayList();
        exportList.addAll(this.fillUdicComSpreadByCompositionConditions(request));
        return exportList;
    }

    private List<UdicComSpreadExportExcelDTO> fillUdicComSpreadByCompositionConditions(UdicListExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getCompositionConditions())) {
            return Collections.emptyList();
        }
        List<UdicComSpreadExportExcelDTO> exportList = Lists.newArrayList();
        for (UdicCurveCompositionConditionDTO compositionCondition : request.getCompositionConditions()) {
            UdicListRequestDTO udicListRequest = BeanCopyUtils.copyProperties(request, UdicListRequestDTO.class);
            udicListRequest.setCompositionCondition(compositionCondition);
            udicListRequest.setPageNum(EXPORT_START_PAGE);
            udicListRequest.setPageSize(EXPORT_MAX_ROW);
            NormPagingResult<UdicComYieldSpreadResponseDTO> pagingResult = udicComYieldSpreadService.getComYieldSpreadPaging(udicListRequest);
            for (UdicComYieldSpreadResponseDTO response : pagingResult.getList()) {
                UdicComSpreadExportExcelDTO excel = BeanCopyUtils.copyProperties(response, UdicComSpreadExportExcelDTO.class);
                excel.setCurveName(compositionCondition.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

    @Override
    public List<UdicBondSpreadExportExcelDTO> listUdicBondSpreadExcels(UdicListExportRequestDTO request) {
        if (Objects.isNull(request)) {
            return Collections.emptyList();
        }
        List<UdicBondSpreadExportExcelDTO> exportList = Lists.newArrayList();
        exportList.addAll(this.fillUdicBondSpreadByCompositionConditions(request));
        return exportList;
    }

    /**
     * 行业利差单日全景数据导出
     *
     * @param request 行业利差单日全景数据导出请求参数
     * @return {@link List}<{@link InduPanoramaExportExcelDTO}> 行业利差全景数据导出响应数据
     */
    @Override
    public List<InduPanoramaExportExcelDTO> listInduDayPanoramaExcels(InduDayPanoramaExportRequestDTO request) {
        Date selectDate = request.getSelectDate();
        if (Objects.isNull(selectDate) || DateExtensionUtils.isSameDay(selectDate, new Date(System.currentTimeMillis()))) {
            selectDate = induBondYieldSpreadService.getMaxSpreadDate();
        }
        //解决日期比较时是否东八区日期的问题
        selectDate = Date.valueOf(selectDate.toLocalDate());
        return this.listInduPanoramaExportExcelDTOS(request, selectDate, selectDate);
    }

    @Override
    public List<InduPanoramaExportExcelDTO> listInduPanoramaExcels(InduPanoramaExportRequestDTO request) {
        final SpreadYearSpanEnum yearSpanEnum = ITextValueEnum.getEnum(SpreadYearSpanEnum.class, request.getYearSpan());
        final Date startDate = yearSpanEnum.getStartDate(request.getYear());
        final Date endDate = yearSpanEnum.getEndDate(request.getYear());
        return this.listInduPanoramaExportExcelDTOS(request, startDate, endDate);
    }

    /**
     * 获取某个时间段产业利差全景导出所需数据
     *
     * @param induPanoramaRequestDTO 筛选参数
     * @param startDate              范围开始时间
     * @param endDate                范围结束时间
     * @return {@link List}<{@link InduPanoramaExportExcelDTO}> 行业利差全景数据响应数据
     */
    private List<InduPanoramaExportExcelDTO> listInduPanoramaExportExcelDTOS(InduPanoramaRequestDTO induPanoramaRequestDTO, Date startDate, Date endDate) {
        // 获取三月，六月日期集合
        Map<Date, SpreadDateDTO> spreadDateMap = yieldSpreadCommonService.getSpreadDateMap(startDate, endDate);
        Date before90StartDate = spreadDateMap.get(startDate).getBefore90Date();
        Date before90EndDate = spreadDateMap.get(endDate).getBefore90Date();
        Date before180StartDate = spreadDateMap.get(startDate).getBefore180Date();
        Date before180EndDate = spreadDateMap.get(endDate).getBefore180Date();
        InduCurveCompositionConditionDTO compositionConditionDTO = BeanCopyUtils.copyProperties(induPanoramaRequestDTO, InduCurveCompositionConditionDTO.class);
        InduBondYieldSpreadParamDTO parameter = InduBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO).spanSpreadDate(startDate, endDate).build();
        SwThreadPoolWorker<Object> swThreadPoolWorker = SwThreadPoolWorker.of(executorService);
        // 查询行业一数据
        CompletableFuture<List<InduSpreadPanoramaBO>> indu1ListFuture = swThreadPoolWorker.submit(() ->
                pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu1(parameter));
        // 查询行业一90天前数据
        InduBondYieldSpreadParamDTO before90Parameter =
                InduBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO).spanSpreadDate(before90StartDate, before90EndDate).build();
        CompletableFuture<List<InduSpreadPanoramaBO>> before90Indu1ListFuture = swThreadPoolWorker.submit(() ->
                pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu1(before90Parameter));
        // 查询行业一180天前数据
        InduBondYieldSpreadParamDTO before180Parameter =
                InduBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO).spanSpreadDate(before180StartDate, before180EndDate).build();
        CompletableFuture<List<InduSpreadPanoramaBO>> before180Indu1ListFuture = swThreadPoolWorker.submit(() ->
                pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu1(before180Parameter));
        // 查询行业二数据
        Long[] indu2Unicodes = YieldSpreadHelper.getIndu2UnicodeList().toArray(new Long[0]);
        CompletableFuture<List<InduSpreadPanoramaBO>> indu2ListFuture = swThreadPoolWorker.submit(() ->
                pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu2(parameter, indu2Unicodes));
        // 查询行业二90天前数据
        CompletableFuture<List<InduSpreadPanoramaBO>> before90Indu2ListFuture = swThreadPoolWorker.submit(() ->
                pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu2(before90Parameter, indu2Unicodes));
        // 查询行业二180天前数据
        CompletableFuture<List<InduSpreadPanoramaBO>> before180Indu2ListFuture = swThreadPoolWorker.submit(() ->
                pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu2(before180Parameter, indu2Unicodes));
        // 查询城投数据作为行业
        CompletableFuture<List<InduPanoramaExportExcelDTO>> udicAsInduFuture = swThreadPoolWorker.submit(() ->
                udicBondYieldSpreadService.listInduPanoramaExcels(induPanoramaRequestDTO, startDate, endDate));
        swThreadPoolWorker.doWorks(indu1ListFuture, before90Indu1ListFuture, before180Indu1ListFuture, indu2ListFuture,
                before90Indu2ListFuture, before180Indu2ListFuture, udicAsInduFuture);
        List<InduSpreadPanoramaBO> indu1List = indu1ListFuture.join();
        List<InduSpreadPanoramaBO> indu2List = indu2ListFuture.join();
        if (CollectionUtils.isEmpty(indu1List) && CollectionUtils.isEmpty(indu2List)) {
            return Collections.emptyList();
        }
        List<InduPanoramaExportExcelDTO> exportList = Lists.newArrayList();
        Map<Date, Map<Long, InduSpreadPanoramaBO>> dateIndu1Map = indu1List.stream()
                .collect(Collectors.groupingBy(InduSpreadPanoramaBO::getSpreadDate, Collectors.toMap(InduSpreadPanoramaBO::getInduLevel1Code, Function.identity(), (o, v) -> o)));
        Map<Date, Map<Long, InduSpreadPanoramaBO>> before90DateIndu1Map = before90Indu1ListFuture.join().stream()
                .collect(Collectors.groupingBy(InduSpreadPanoramaBO::getSpreadDate, Collectors.toMap(InduSpreadPanoramaBO::getInduLevel1Code, Function.identity(), (o, v) -> o)));
        Map<Date, Map<Long, InduSpreadPanoramaBO>> before180DateIndu1Map = before180Indu1ListFuture.join().stream()
                .collect(Collectors.groupingBy(InduSpreadPanoramaBO::getSpreadDate, Collectors.toMap(InduSpreadPanoramaBO::getInduLevel1Code, Function.identity(), (o, v) -> o)));
        Map<Date, Map<Long, InduSpreadPanoramaBO>> dateIndu2Map = indu2List.stream()
                .collect(Collectors.groupingBy(InduSpreadPanoramaBO::getSpreadDate, Collectors.toMap(InduSpreadPanoramaBO::getInduLevel2Code, Function.identity(), (o, v) -> o)));
        Map<Date, Map<Long, InduSpreadPanoramaBO>> before90DateIndu2Map = before90Indu2ListFuture.join().stream()
                .collect(Collectors.groupingBy(InduSpreadPanoramaBO::getSpreadDate, Collectors.toMap(InduSpreadPanoramaBO::getInduLevel2Code, Function.identity(), (o, v) -> o)));
        Map<Date, Map<Long, InduSpreadPanoramaBO>> before180DateIndu2Map = before180Indu2ListFuture.join().stream()
                .collect(Collectors.groupingBy(InduSpreadPanoramaBO::getSpreadDate, Collectors.toMap(InduSpreadPanoramaBO::getInduLevel2Code, Function.identity(), (o, v) -> o)));
        // 行业一
        exportList.addAll(this.convertToInduPanoramaExportList(spreadDateMap, dateIndu1Map, before90DateIndu1Map, before180DateIndu1Map, true));
        // 行业二
        exportList.addAll(this.convertToInduPanoramaExportList(spreadDateMap, dateIndu2Map, before90DateIndu2Map, before180DateIndu2Map, false));
        // 整个城投作为行业
        exportList.addAll(udicAsInduFuture.join());
        exportList.sort(Comparator.comparing(InduPanoramaExportExcelDTO::getSpreadDate).reversed());
        return exportList;
    }

    private List<InduPanoramaExportExcelDTO> convertToInduPanoramaExportList(Map<Date, SpreadDateDTO> spreadDateMap,
                                                                             Map<Date, Map<Long, InduSpreadPanoramaBO>> dateInduMap,
                                                                             Map<Date, Map<Long, InduSpreadPanoramaBO>> before90DateInduMap,
                                                                             Map<Date, Map<Long, InduSpreadPanoramaBO>> before180DateInduMap, Boolean isIndu1) {
        List<InduPanoramaExportExcelDTO> exportList = Lists.newArrayList();
        // Map.Entry<Date, Map<Long, InduSpreadPanoramaBO>> 按照 日期 + 行业 进行分组
        for (Map.Entry<Date, Map<Long, InduSpreadPanoramaBO>> dateMapEntry : dateInduMap.entrySet()) {
            Date date = dateMapEntry.getKey();
            SpreadDateDTO spreadDateDTO = spreadDateMap.get(date);
            Date before90Date = spreadDateDTO.getBefore90Date();
            Date before180Date = spreadDateDTO.getBefore180Date();
            // 按照 行业 进行分组,转换为导出数据
            List<InduPanoramaExportExcelDTO> singleExportList = dateMapEntry.getValue().entrySet().stream().map(induEntry -> {
                Long induCode = induEntry.getKey();
                InduSpreadPanoramaBO before90Indu = before90DateInduMap.getOrDefault(before90Date, Collections.emptyMap()).get(induCode);
                InduSpreadPanoramaBO before180Indu = before180DateInduMap.getOrDefault(before180Date, Collections.emptyMap()).get(induCode);
                return convertToInduPanoramaExportDTO(date, before90Indu, before180Indu, induEntry, isIndu1);
            }).collect(Collectors.toList());
            exportList.addAll(singleExportList);
        }
        return exportList;
    }

    private InduPanoramaExportExcelDTO convertToInduPanoramaExportDTO(Date date, InduSpreadPanoramaBO before90Indu, InduSpreadPanoramaBO before180Indu,
                                                                      Map.Entry<Long, InduSpreadPanoramaBO> induMapEntry, Boolean isIndu1) {
        InduPanoramaExportExcelDTO excel = new InduPanoramaExportExcelDTO();
        Long induCode = induMapEntry.getKey();
        InduSpreadPanoramaBO indu = induMapEntry.getValue();
        excel.setSpreadDate(date);
        if (Boolean.TRUE.equals(isIndu1)) {
            excel.setIndustryName1(YieldSpreadHelper.getInduUnicodeMap().get(induCode));
        } else {
            excel.setIndustryName2(YieldSpreadHelper.getInduUnicodeMap().get(induCode));
            Map<Long, Long> indu2Indu1Mapping = YieldSpreadHelper.getIndu2Indu1Mapping();
            Long indu1Code = indu2Indu1Mapping.get(induCode);
            excel.setIndustryName1(YieldSpreadHelper.getInduUnicodeMap().get(indu1Code));
        }
        if (Objects.nonNull(indu.getBondCreditSpreadCount()) && indu.getBondCreditSpreadCount() >= MIN_BOND_SIZE) {
            excel.setBondCreditSpread(indu.getBondCreditSpread());
        }
        if (Objects.nonNull(indu.getBondExcessSpreadCount()) && indu.getBondExcessSpreadCount() >= MIN_BOND_SIZE) {
            excel.setBondExcessSpread(indu.getBondExcessSpread());
        }
        if (Objects.nonNull(before90Indu)) {
            BigDecimal creditChange90 = subtract(excel.getBondCreditSpread(), before90Indu.getBondCreditSpread()).orElse(null);
            excel.setBondCreditSpreadChange90(creditChange90);
            BigDecimal excessChange90 = subtract(excel.getBondExcessSpread(), before90Indu.getBondExcessSpread()).orElse(null);
            excel.setBondExcessSpreadChange90(excessChange90);
        }
        if (Objects.nonNull(before180Indu)) {
            BigDecimal creditChange180 = subtract(excel.getBondCreditSpread(), before180Indu.getBondCreditSpread()).orElse(null);
            excel.setBondCreditSpreadChange180(creditChange180);
            BigDecimal excessChange180 = subtract(excel.getBondExcessSpread(), before180Indu.getBondExcessSpread()).orElse(null);
            excel.setBondExcessSpreadChange180(excessChange180);
        }
        return excel;
    }

    /**
     * 城投利差单日全景数据导出
     *
     * @param request 城投利差单日全景数据导出请求参数
     * @return {@link List}<{@link UdicSpreadPanoramaExportExcelDTO}> 城投利差全景数据导出响应数据
     */
    @Override
    public List<UdicSpreadPanoramaExportExcelDTO> listUdicDayPanoramasExcels(UdicDayPanoramaExportRequestDTO request) {
        Date selectDate = request.getSelectDate();
        if (Objects.isNull(selectDate) || DateExtensionUtils.isSameDay(selectDate, new Date(System.currentTimeMillis()))) {
            selectDate = udicBondYieldSpreadService.getMaxSpreadDate();
        }
        //解决日期比较时是否东八区日期的问题
        selectDate = Date.valueOf(selectDate.toLocalDate());
        Integer areaType = request.getAreaType();
        return this.listUdicSpreadPanoramaExportExcelDTOS(request, areaType, selectDate, selectDate);
    }

    @Override
    public List<UdicSpreadPanoramaExportExcelDTO> listUdicPanoramasExcels(UdicPanoramaExportRequestDTO request) {
        if (Objects.isNull(request)) {
            return Collections.emptyList();
        }
        Integer areaType = request.getAreaType();
        final SpreadYearSpanEnum yearSpanEnum = ITextValueEnum.getEnum(SpreadYearSpanEnum.class, request.getYearSpan());
        final Date startDate = yearSpanEnum.getStartDate(request.getYear());
        final Date endDate = yearSpanEnum.getEndDate(request.getYear());
        return listUdicSpreadPanoramaExportExcelDTOS(request, areaType, startDate, endDate);
    }

    /**
     * 获取某个时间段 区域类型 城投利差全景导出所需数据
     *
     * @param request   筛选参数
     * @param areaType  区域类型1:省级，2:市级
     * @param startDate 时间范围开始时间
     * @param endDate   时间范围结束时间
     * @return {@link List}<{@link UdicSpreadPanoramaExportExcelDTO}> 城投利差全景数据导出响应数据
     */
    private List<UdicSpreadPanoramaExportExcelDTO> listUdicSpreadPanoramaExportExcelDTOS(UdicPanoramaRequestDTO request, Integer areaType, Date startDate, Date endDate) {
        UdicPanoramaExportParameter parameter = this.buildPanoramaExportParameter(request, areaType, startDate, endDate);
        List<UdicSpreadPanoramaExportExcelDTO> exportList;
        // 导出省
        if (AreaTypeEnum.PROVINCE.equals(parameter.areaType)) {
            exportList = listUdicPanoramasExcelsForProvince(parameter);
        } else {// 导出市
            exportList = listUdicPanoramasExcelsForCity(parameter);
        }
        return exportList;
    }

    @Override
    public List<CurveExportExcelDTO> exportCurveData(Long userId, CurveDataExportRequestDTO request) {
        final Date startDate = request.getStartDate();
        final Date endDate = request.getEndDate();
        final Integer arithmeticType = request.getArithmeticType();
        List<CurveExportExcelDTO> excelDTOList = new ArrayList<>();
        // 1. curveIds
        List<CurveDefinitionBO> definitionBOList = request.getCurveIds().stream()
                .map(curveId -> userCurveDAO.getCurveDefinitionBO(curveId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(curve -> Objects.equals(curve.getUserId(), BENCHMARK_CURVE_USER_ID) || Objects.equals(userId, curve.getUserId()))
                .collect(Collectors.toList());
        for (CurveDefinitionBO definition : definitionBOList) {
            List<CurveDataResDTO> curveDataList = curvePoolService.listDesensitizationCurveData(userId, definition.getId(), startDate, endDate);
            excelDTOList.addAll(this.convertToCurveExportExcel(definition, curveDataList, arithmeticType));
        }
        List<CurveBondRequestDTO> bondRequestList = request.getBondSpreads();
        List<CurveComRequestDTO> comRequestList = request.getComSpreads();
        // 2. bond spread
        for (CurveBondRequestDTO bond : bondRequestList) {
            List<BondYieldSpreadCurveResponseDTO> bondCurveResponses = bondYieldSpreadService.listCurves(bond.getBondUniCode(), bond.getCurveType(), startDate, endDate);
            List<CurveDataResDTO> curveDataResList = BeanCopyUtils.copyList(bondCurveResponses, CurveDataResDTO.class);
            excelDTOList.addAll(this.convertToCurveExportExcel(bond.getCurveName(), bond.getCurveType(), curveDataResList, arithmeticType));
        }
        // 3. com spread
        for (CurveComRequestDTO com : comRequestList) {
            List<ComYieldSpreadCurveResponseDTO> comCurveResponses =
                    comYieldSpreadService.listCurves(com.getComUniCode(), com.getCurveType(), com.getSpreadBondRanking(), startDate, endDate);
            List<CurveDataResDTO> curveDataResList = BeanCopyUtils.copyList(comCurveResponses, CurveDataResDTO.class);
            excelDTOList.addAll(this.convertToCurveExportExcel(com.getCurveName(), com.getCurveType(), curveDataResList, arithmeticType));
        }
        return excelDTOList;
    }

    @Override
    public List<DynCurveExportExcelDTO> dynExportCurveData(List<CurveExportExcelDTO> curveExportExcelDTOS){
        // 日期 曲线名称1 曲线名称2 曲线名称3 ... 第一列固定 后面动态
        List<List<String>> headList = Lists.newArrayList();
        List<String> head0 = Collections.singletonList("日期");
        headList.add(head0);
        List<String> curveNameList = curveExportExcelDTOS.stream().map(CurveExportExcelDTO::getCurveName).distinct().collect(Collectors.toList());
        for (String curveName : curveNameList) {
            List<String> head = Collections.singletonList(curveName);
            headList.add(head);
        }
        Map<java.util.Date, Map<String, List<CurveExportExcelDTO>>> dateToNameToDataMap = curveExportExcelDTOS.stream()
                .collect(Collectors.groupingBy(CurveExportExcelDTO::getSpreadDate, Collectors.groupingBy(CurveExportExcelDTO::getCurveName)));
        List<java.util.Date> spreadDateList = curveExportExcelDTOS.stream().map(CurveExportExcelDTO::getSpreadDate).distinct().sorted().collect(Collectors.toList());
        List<DynCurveExportExcelDTO> result = Lists.newArrayList();
        for (SpreadCurveTypeEnum spreadCurveType : YieldSpreadConst.CURVE_EXPORT_TYPE_LIST) {
            List<List<Object>> dataList = getDataList(spreadDateList, curveNameList, dateToNameToDataMap, spreadCurveType);
            DynCurveExportExcelDTO dynCurveExportExcelDTO = new DynCurveExportExcelDTO();
            dynCurveExportExcelDTO.setSheetName(spreadCurveType.getText());
            dynCurveExportExcelDTO.setHeadList(headList);
            dynCurveExportExcelDTO.setDataList(dataList);
            result.add(dynCurveExportExcelDTO);
        }
        return result;
    }

    private List<List<Object>> getDataList(List<java.util.Date> spreadDateList, List<String> curveNameList, Map<java.util.Date, Map<String,
            List<CurveExportExcelDTO>>> dateToNameToDataMap, SpreadCurveTypeEnum spreadCurveTypeEnum) {
        List<List<Object>> dataList = Lists.newArrayListWithExpectedSize(spreadDateList.size());
        for (java.util.Date spreadDate : spreadDateList) {
            List<Object> colList = Lists.newArrayListWithExpectedSize(curveNameList.size() + 1);
            // 日期
            colList.add(DateFormatUtils.format(spreadDate, DateExtensionUtils.FORMAT_DATE_SIMPLE));
            curveNameList.forEach(x -> colList.add(getColVal(dateToNameToDataMap, spreadDate, x, spreadCurveTypeEnum)));
            dataList.add(colList);
        }
        return dataList;
    }

    private Object getColVal(Map<java.util.Date, Map<String, List<CurveExportExcelDTO>>> dateToNameToDataMap,
                             java.util.Date spreadDate, String curveName, SpreadCurveTypeEnum spreadCurveTypeEnum){
        Map<String, List<CurveExportExcelDTO>> nameToDataMap = dateToNameToDataMap.get(spreadDate);
        if (ObjectUtils.isEmpty(nameToDataMap)) {
            return null;
        }
        List<CurveExportExcelDTO> curveExportExcelDTOS = nameToDataMap.get(curveName);
        if (CollectionUtils.isEmpty(curveExportExcelDTOS)) {
            return null;
        }
        CurveExportExcelDTO curveExportExcelDTO = curveExportExcelDTOS.get(0);
        if (Objects.equals(spreadCurveTypeEnum, SpreadCurveTypeEnum.CREDIT_SPREAD)) {
            return curveExportExcelDTO.getCreditSpread();
        }
        if (Objects.equals(spreadCurveTypeEnum, SpreadCurveTypeEnum.EXCESS_SPREAD)) {
            return curveExportExcelDTO.getExcessSpread();
        }
        if (Objects.equals(spreadCurveTypeEnum, SpreadCurveTypeEnum.CB_YIELD_SPREAD)) {
            return curveExportExcelDTO.getCbYield();
        }
        return null;
    }

    @Override
    public Map<String, List<BaseCurveYieldSpreadExcelDTO>> exportSingleBondYieldSpread(Long userid, List<Long> curveIds, Date spreadDate) {
        Map<String, List<BaseCurveYieldSpreadExcelDTO>> excelDataMap = new LinkedHashMap<>();
        for (Long curveId : curveIds) {
            userCurveDAO.getCurveDefinitionBO(curveId).ifPresent(v -> {
                Long curveUserId = v.getUserId();
                if (!Objects.equals(curveUserId, BENCHMARK_CURVE_USER_ID) && !Objects.equals(userid, curveUserId)) {
                    return;
                }
                List<BaseCurveYieldSpreadExcelDTO> spreads = this.listSingleBondYieldSpreadExcelDTO(userid, v, spreadDate);
                if (CollectionUtils.isEmpty(spreads)) {
                    return;
                }
                EnumUtils.getEnumByValue(v.getSpreadCurveType(), CurveTypeEnum.class).ifPresent(type -> {
                    String key = type.getText();
                    if (excelDataMap.containsKey(key)) {
                        excelDataMap.get(key).addAll(spreads);
                    } else {
                        excelDataMap.put(key, spreads);
                    }
                });
            });
        }
        return excelDataMap;
    }

    @Override
    public Map<String, List<BaseCurveYieldSpreadExcelDTO>> exportComYieldSpread(Long userid, List<Long> curveIds, Date spreadDate) {
        Map<String, List<BaseCurveYieldSpreadExcelDTO>> excelDataMap = new LinkedHashMap<>();
        for (Long curveId : curveIds) {
            userCurveDAO.getCurveDefinitionBO(curveId).ifPresent(v -> {
                Long curveUserId = v.getUserId();
                if (!Objects.equals(curveUserId, BENCHMARK_CURVE_USER_ID) && !Objects.equals(userid, curveUserId)) {
                    return;
                }
                List<BaseCurveYieldSpreadExcelDTO> spreads = this.listComYieldSpreadExcelDTO(userid, v, spreadDate);
                if (CollectionUtils.isEmpty(spreads)) {
                    return;
                }
                EnumUtils.getEnumByValue(v.getSpreadCurveType(), CurveTypeEnum.class).ifPresent(type -> {
                    String key = type.getText();
                    if (excelDataMap.containsKey(key)) {
                        excelDataMap.get(key).addAll(spreads);
                    } else {
                        excelDataMap.put(key, spreads);
                    }
                });
            });
        }
        return excelDataMap;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listSingleBondYieldSpreadExcelDTO(Long userid, CurveDefinitionBO curve, Date spreadDate) {
        List<BaseCurveYieldSpreadExcelDTO> result = new ArrayList<>();
        int pageNum = 1;
        while (true) {
            List<BaseCurveYieldSpreadExcelDTO> excelDTOs;
            if (curve.getSpreadCurveType().equals(CurveTypeEnum.BANK.getValue())) {
                excelDTOs = this.listSingleBondYieldSpreadForBank(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.SECURITY.getValue())) {
                excelDTOs = this.listSingleBondYieldSpreadForSecu(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.CUSTOMIZATION.getValue())) {
                excelDTOs = this.listSingleBondYieldSpreadForCustom(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.INDU.getValue())) {
                excelDTOs = this.listSingleBondYieldSpreadForIndu(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.UDIC.getValue())) {
                excelDTOs = this.listSingleBondYieldSpreadForUdic(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.INSURANCE.getValue())) {
                excelDTOs = this.listSingleBondYieldSpreadForInsu(userid, curve, spreadDate, pageNum);
            } else {
                excelDTOs = new ArrayList<>();
            }
            result.addAll(excelDTOs);
            if (CollectionUtils.isEmpty(excelDTOs) || excelDTOs.size() < EXPORT_SEARCH_BATCH_SIZE) {
                break;
            }
            pageNum++;
        }
        return result;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listSingleBondYieldSpreadForUdic(Long userid, CurveDefinitionBO curve, Date spreadDate, int pageNum) {
        NormPagingResult<UdicBondYieldSpreadResponseDTO> pagingResult = udicBondYieldSpreadService
                .pagingSingleBondYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<UdicBondYieldSpreadResponseDTO> list = pagingResult.getList();
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (UdicBondYieldSpreadResponseDTO yieldSpread : list) {
            UdicBondSpreadExportExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, UdicBondSpreadExportExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listSingleBondYieldSpreadForInsu(Long userid, CurveDefinitionBO curve, Date spreadDate, Integer pageNum) {
        NormPagingResult<InsuSingleBondYieldSpreadResDTO> pagingResult = insuBondYieldSpreadService
                .pagingSingleBondYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<InsuSingleBondYieldSpreadResDTO> list = pagingResult.getList();
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (InsuSingleBondYieldSpreadResDTO yieldSpread : list) {
            InsuSingleBondYieldSpreadExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, InsuSingleBondYieldSpreadExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listSingleBondYieldSpreadForIndu(Long userid, CurveDefinitionBO curve, Date spreadDate, int pageNum) {
        NormPagingResult<InduBondYieldSpreadResponseDTO> pagingResult = induBondYieldSpreadService
                .pagingSingleBondYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<InduBondYieldSpreadResponseDTO> list = pagingResult.getList();
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (InduBondYieldSpreadResponseDTO yieldSpread : list) {
            InduBondSpreadExportExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, InduBondSpreadExportExcelDTO.class);
            CalculationHelper.divideTenThousand(yieldSpread.getBondBalance()).ifPresent(yieldSpread::setBondBalance);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listSingleBondYieldSpreadForCustom(Long userid, CurveDefinitionBO curve, Date spreadDate, int pageNum) {
        List<CustomSingleBondYieldSpreadResDTO> result = customCurveService
                .pagingSingleBondYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (CustomSingleBondYieldSpreadResDTO yieldSpread : result) {
            CustomSingleBondYieldSpreadExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, CustomSingleBondYieldSpreadExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listSingleBondYieldSpreadForSecu(Long userid, CurveDefinitionBO curve, Date spreadDate, int pageNum) {
        NormPagingResult<SecuSingleBondYieldSpreadResDTO> pagingResult = secuBondYieldSpreadService
                .pagingSingleBondYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<SecuSingleBondYieldSpreadResDTO> list = pagingResult.getList();
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (SecuSingleBondYieldSpreadResDTO yieldSpread : list) {
            SecuSingleBondYieldSpreadExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, SecuSingleBondYieldSpreadExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listSingleBondYieldSpreadForBank(Long userid, CurveDefinitionBO curve, Date spreadDate, Integer pageNum) {
        NormPagingResult<BankSingleBondYieldSpreadResDTO> pagingResult = bankBondYieldSpreadService
                .pagingSingleBondYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<BankSingleBondYieldSpreadResDTO> list = pagingResult.getList();
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (BankSingleBondYieldSpreadResDTO yieldSpread : list) {
            BankSingleBondYieldSpreadExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, BankSingleBondYieldSpreadExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listComYieldSpreadExcelDTO(Long userid, CurveDefinitionBO curve, Date spreadDate) {
        List<BaseCurveYieldSpreadExcelDTO> result = new ArrayList<>();
        int pageNum = 1;
        while (true) {
            List<BaseCurveYieldSpreadExcelDTO> excelDTOs;
            if (curve.getSpreadCurveType().equals(CurveTypeEnum.BANK.getValue())) {
                excelDTOs = this.listComYieldSpreadForBank(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.SECURITY.getValue())) {
                excelDTOs = this.listComYieldSpreadForSecu(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.CUSTOMIZATION.getValue())) {
                excelDTOs = this.listComYieldSpreadForCustom(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.INDU.getValue())) {
                excelDTOs = this.listComYieldSpreadForIndu(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.UDIC.getValue())) {
                excelDTOs = this.listComYieldSpreadForUdic(userid, curve, spreadDate, pageNum);
            } else if (curve.getSpreadCurveType().equals(CurveTypeEnum.INSURANCE.getValue())) {
                excelDTOs = this.listComYieldSpreadForInsu(userid, curve, spreadDate, pageNum);
            } else {
                excelDTOs = new ArrayList<>();
            }
            result.addAll(excelDTOs);
            if (CollectionUtils.isEmpty(excelDTOs) || excelDTOs.size() < EXPORT_SEARCH_BATCH_SIZE) {
                break;
            }
            pageNum++;
        }
        return result;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listComYieldSpreadForUdic(Long userid, CurveDefinitionBO curve, Date spreadDate, int pageNum) {
        List<UdicComYieldSpreadResponseDTO> list = udicComYieldSpreadService
                .listComYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (UdicComYieldSpreadResponseDTO yieldSpread : list) {
            UdicComSpreadExportExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, UdicComSpreadExportExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listComYieldSpreadForIndu(Long userid, CurveDefinitionBO curve, Date spreadDate, int pageNum) {
        List<InduComYieldSpreadResponseDTO> list = induComYieldSpreadService
                .listComYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (InduComYieldSpreadResponseDTO yieldSpread : list) {
            InduComSpreadExportExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, InduComSpreadExportExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listComYieldSpreadForCustom(Long userid, CurveDefinitionBO curve, Date spreadDate, int pageNum) {
        List<CustomComYieldSpreadResDTO> list = customComYieldSpreadService
                .listComYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (CustomComYieldSpreadResDTO yieldSpread : list) {
            CustomComYieldSpreadExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, CustomComYieldSpreadExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listComYieldSpreadForSecu(Long userid, CurveDefinitionBO curve, Date spreadDate, int pageNum) {
        List<SecuComYieldSpreadResDTO> list = secuComYieldSpreadService
                .listComYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (SecuComYieldSpreadResDTO yieldSpread : list) {
            SecuComYieldSpreadExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, SecuComYieldSpreadExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listComYieldSpreadForBank(Long userid, CurveDefinitionBO curve, Date spreadDate, Integer pageNum) {
        List<BankComYieldSpreadResDTO> list = bankComYieldSpreadService
                .listComYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (BankComYieldSpreadResDTO yieldSpread : list) {
            BankComYieldSpreadExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, BankComYieldSpreadExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private List<BaseCurveYieldSpreadExcelDTO> listComYieldSpreadForInsu(Long userid, CurveDefinitionBO curve, Date spreadDate, Integer pageNum) {
        List<InsuComYieldSpreadResDTO> list = insuComYieldSpreadService
                .listComYieldSpreads(userid, this.buildYieldSpreadSearchReqDTO(curve.getId(), spreadDate, pageNum));
        List<BaseCurveYieldSpreadExcelDTO> excelDTOs = new ArrayList<>();
        for (InsuComYieldSpreadResDTO yieldSpread : list) {
            InsuComYieldSpreadExcelDTO excelDTO = BeanCopyUtils.copyProperties(yieldSpread, InsuComYieldSpreadExcelDTO.class);
            excelDTO.setCurveName(curve.getSpreadCurveName());
            excelDTOs.add(excelDTO);
        }
        return excelDTOs;
    }

    private YieldSpreadSearchReqDTO buildYieldSpreadSearchReqDTO(Long curveId, Date spreadDate, int pageNum) {
        YieldSpreadSearchReqDTO searchReq = new YieldSpreadSearchReqDTO();
        searchReq.setCurveId(curveId);
        searchReq.setPageSize(EXPORT_SEARCH_BATCH_SIZE);
        searchReq.setPageNum(pageNum);
        searchReq.setSpreadDate(spreadDate);
        return searchReq;
    }

    private List<CurveExportExcelDTO> convertToCurveExportExcel(String curveName, Integer curveType, List<CurveDataResDTO> curveDataList, Integer arithmeticType) {
        CurveDefinitionBO curveDefinition = new CurveDefinitionBO();
        curveDefinition.setSpreadCurveName(curveName);
        curveDefinition.setSpreadCurveType(curveType);
        return convertToCurveExportExcel(curveDefinition, curveDataList, arithmeticType);
    }

    private List<CurveExportExcelDTO> convertToCurveExportExcel(CurveDefinitionBO curveDefinition, List<CurveDataResDTO> curveDataList, Integer arithmeticType) {
        List<CurveExportExcelDTO> excelDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(curveDataList)) {
            return excelDTOList;
        }
        curveDataList.forEach(curveData -> excelDTOList.add(this.buildExportExcelDTO(curveDefinition, curveData, arithmeticType)));
        return excelDTOList;
    }

    private CurveExportExcelDTO buildExportExcelDTO(CurveDefinitionBO curveDefinition, CurveDataResDTO curveData, Integer arithmeticType) {
        CurveExportExcelDTO curveExportExcelDTO = new CurveExportExcelDTO();
        curveExportExcelDTO.setSpreadDate(curveData.getSpreadDate());
        curveExportExcelDTO.setCurveName(curveDefinition.getSpreadCurveName());
        if (Objects.equals(CurveTypeEnum.CB.getValue(), curveDefinition.getSpreadCurveType())) {
            curveExportExcelDTO.setCbYield(curveData.getYtm());
        } else {
            if (CurveArithmeticTypeEnum.MEDIAN.getValue() == arithmeticType) {
                curveExportExcelDTO.setCbYield(curveData.getCbYield());
                curveExportExcelDTO.setCreditSpread(curveData.getBondCreditSpread());
                curveExportExcelDTO.setExcessSpread(curveData.getBondExcessSpread());
            } else {
                curveExportExcelDTO.setCbYield(curveData.getAvgCbYield());
                curveExportExcelDTO.setCreditSpread(curveData.getAvgBondCreditSpread());
                curveExportExcelDTO.setExcessSpread(curveData.getAvgBondExcessSpread());
            }
        }
        return curveExportExcelDTO;
    }

    private List<UdicSpreadPanoramaExportExcelDTO> listUdicPanoramasExcelsForCity(UdicPanoramaExportParameter parameter) {
        SwThreadPoolWorker<Object> swThreadPoolWorker = SwThreadPoolWorker.of(executorService);
        // 查询市数据
        CompletableFuture<List<UdicSpreadPanoramaBO>> cityListFuture = swThreadPoolWorker.submit(() ->
                pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreadsForCity(parameter.startDate, parameter.endDate, parameter.bondExtRatingMapping,
                        parameter.bondImpliedRatingMappingTag, parameter.yyRatingMappingTag, parameter.spreadBondType, parameter.spreadRemainingTenorTag,
                        parameter.guaranteeStatus, parameter.administrativeDivision));
        // 查询市90天前数据
        CompletableFuture<List<UdicSpreadPanoramaBO>> before90CityListFuture = swThreadPoolWorker.submit(() ->
                pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreadsForCity(parameter.before90StartDate, parameter.before90EndDate, parameter.bondExtRatingMapping,
                        parameter.bondImpliedRatingMappingTag, parameter.yyRatingMappingTag, parameter.spreadBondType, parameter.spreadRemainingTenorTag,
                        parameter.guaranteeStatus, parameter.administrativeDivision));
        // 查询市180天前数据
        CompletableFuture<List<UdicSpreadPanoramaBO>> before180CityListFuture = swThreadPoolWorker.submit(() ->
                pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreadsForCity(parameter.before180StartDate, parameter.before180EndDate, parameter.bondExtRatingMapping,
                        parameter.bondImpliedRatingMappingTag, parameter.yyRatingMappingTag, parameter.spreadBondType, parameter.spreadRemainingTenorTag,
                        parameter.guaranteeStatus, parameter.administrativeDivision));
        swThreadPoolWorker.doWorks(cityListFuture, before90CityListFuture, before180CityListFuture);
        List<UdicSpreadPanoramaBO> cityList = cityListFuture.join();
        if (CollectionUtils.isEmpty(cityList)) {
            return Collections.emptyList();
        }
        Map<Date, Map<Long, Map<Long, UdicSpreadPanoramaBO>>> dateProvinceCityMap =
                cityList.stream().collect(Collectors.groupingBy(UdicSpreadPanoramaBO::getSpreadDate, LinkedHashMap::new,
                        Collectors.groupingBy(UdicSpreadPanoramaBO::getProvinceUniCode, Collectors.toMap(UdicSpreadPanoramaBO::getCityUniCode, Function.identity(), (o, v) -> o))));
        Map<Date, Map<Long, Map<Long, UdicSpreadPanoramaBO>>> before90DateProvinceCityMap =
                before90CityListFuture.join().stream().collect(Collectors.groupingBy(UdicSpreadPanoramaBO::getSpreadDate, LinkedHashMap::new,
                        Collectors.groupingBy(UdicSpreadPanoramaBO::getProvinceUniCode, Collectors.toMap(UdicSpreadPanoramaBO::getCityUniCode, Function.identity(), (o, v) -> o))));
        Map<Date, Map<Long, Map<Long, UdicSpreadPanoramaBO>>> before180DateProvinceCityMap =
                before180CityListFuture.join().stream().collect(Collectors.groupingBy(UdicSpreadPanoramaBO::getSpreadDate, LinkedHashMap::new,
                        Collectors.groupingBy(UdicSpreadPanoramaBO::getProvinceUniCode, Collectors.toMap(UdicSpreadPanoramaBO::getCityUniCode, Function.identity(), (o, v) -> o))));
        Set<Long> cityCodes = cityList.stream().map(UdicSpreadPanoramaBO::getCityUniCode).collect(Collectors.toSet());
        Map<Long, AreaInfoResponseDTO> cityCodeAreaInfoMap = areaService.getAreaInfoMap(cityCodes);
        Map<Date, SpreadDateDTO> spreadDateMap = parameter.spreadDateMap;
        List<UdicSpreadPanoramaExportExcelDTO> exportList = Lists.newArrayList();
        // Map.Entry<Date, Map<Long, Map<Long, UdicSpreadPanoramaBO>>> 日期 + 省 + 市 进行分组
        for (Map.Entry<Date, Map<Long, Map<Long, UdicSpreadPanoramaBO>>> dateProvinceMapEntry : dateProvinceCityMap.entrySet()) {
            Date date = dateProvinceMapEntry.getKey();
            SpreadDateDTO spreadDateDTO = spreadDateMap.get(date);
            Date before90Date = spreadDateDTO.getBefore90Date();
            Date before180Date = spreadDateDTO.getBefore180Date();
            // Map.Entry<Long, Map<Long, UdicSpreadPanoramaBO>> 省 + 市 进行分组
            for (Map.Entry<Long, Map<Long, UdicSpreadPanoramaBO>> provinceMapEntry : dateProvinceMapEntry.getValue().entrySet()) {
                Long province = provinceMapEntry.getKey();
                // 市 进行分组,转换为导出数据
                List<UdicSpreadPanoramaExportExcelDTO> singleExportList = provinceMapEntry.getValue().entrySet().stream().map(cityEntry -> {
                    AreaInfoResponseDTO areaInfo = cityCodeAreaInfoMap.get(cityEntry.getKey());
                    UdicSpreadPanoramaBO before90Panorama =
                            before90DateProvinceCityMap.getOrDefault(before90Date, Collections.emptyMap()).getOrDefault(province, Collections.emptyMap()).get(cityEntry.getKey());
                    UdicSpreadPanoramaBO before180Panorama =
                            before180DateProvinceCityMap.getOrDefault(before180Date, Collections.emptyMap()).getOrDefault(province, Collections.emptyMap()).get(cityEntry.getKey());
                    return convertToUdicPanoramaExportDTO(date, areaInfo, before90Panorama, before180Panorama, cityEntry);
                }).collect(Collectors.toList());
                exportList.addAll(singleExportList);
            }
        }
        return exportList;
    }

    private List<UdicSpreadPanoramaExportExcelDTO> listUdicPanoramasExcelsForProvince(UdicPanoramaExportParameter parameter) {
        SwThreadPoolWorker<Object> swThreadPoolWorker = SwThreadPoolWorker.of(executorService);
        // 查询省数据
        CompletableFuture<List<UdicSpreadPanoramaBO>> provinceListFuture = swThreadPoolWorker.submit(() ->
                pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreadsForProvince(parameter.startDate, parameter.endDate, parameter.bondExtRatingMapping,
                        parameter.bondImpliedRatingMappingTag, parameter.yyRatingMappingTag, parameter.spreadBondType, parameter.spreadRemainingTenorTag,
                        parameter.guaranteeStatus, parameter.administrativeDivision, parameter.bondImpliedRatingMappings, parameter.comYyRatingMappings));
        // 查询省90天前数据
        CompletableFuture<List<UdicSpreadPanoramaBO>> before90ProvinceListFuture = swThreadPoolWorker.submit(() ->
                pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreadsForProvince(parameter.before90StartDate, parameter.before90EndDate, parameter.bondExtRatingMapping,
                        parameter.bondImpliedRatingMappingTag, parameter.yyRatingMappingTag, parameter.spreadBondType, parameter.spreadRemainingTenorTag, parameter.guaranteeStatus,
                        parameter.administrativeDivision, parameter.bondImpliedRatingMappings, parameter.comYyRatingMappings));
        // 查询省180天前数据
        CompletableFuture<List<UdicSpreadPanoramaBO>> before180ProvinceListFuture = swThreadPoolWorker.submit(() ->
                pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreadsForProvince(parameter.before180StartDate, parameter.before180EndDate,
                        parameter.bondExtRatingMapping, parameter.bondImpliedRatingMappingTag, parameter.yyRatingMappingTag, parameter.spreadBondType,
                        parameter.spreadRemainingTenorTag, parameter.guaranteeStatus, parameter.administrativeDivision, parameter.bondImpliedRatingMappings,
                        parameter.comYyRatingMappings));
        swThreadPoolWorker.doWorks(provinceListFuture, before90ProvinceListFuture, before180ProvinceListFuture);
        List<UdicSpreadPanoramaBO> provinceList = provinceListFuture.join();
        if (CollectionUtils.isEmpty(provinceList)) {
            return Collections.emptyList();
        }
        List<UdicSpreadPanoramaExportExcelDTO> exportList = Lists.newArrayList();
        Set<Long> provinces = provinceList.stream().map(UdicSpreadPanoramaBO::getProvinceUniCode).collect(Collectors.toSet());
        Map<Long, AreaInfoResponseDTO> provinceAreaInfoMap = areaService.getAreaInfoMap(provinces);
        // 根据日期和省进行分组
        Map<Date, Map<Long, UdicSpreadPanoramaBO>> dateProvinceMap =
                provinceList.stream().collect(Collectors.groupingBy(UdicSpreadPanoramaBO::getSpreadDate, LinkedHashMap::new,
                        Collectors.toMap(UdicSpreadPanoramaBO::getProvinceUniCode, Function.identity(), (o, v) -> o)));
        Map<Date, Map<Long, UdicSpreadPanoramaBO>> before90DateProvinceMap =
                before90ProvinceListFuture.join().stream().collect(Collectors.groupingBy(UdicSpreadPanoramaBO::getSpreadDate, LinkedHashMap::new,
                        Collectors.toMap(UdicSpreadPanoramaBO::getProvinceUniCode, Function.identity(), (o, v) -> o)));
        Map<Date, Map<Long, UdicSpreadPanoramaBO>> before180DateProvinceMap =
                before180ProvinceListFuture.join().stream().collect(Collectors.groupingBy(UdicSpreadPanoramaBO::getSpreadDate, LinkedHashMap::new,
                        Collectors.toMap(UdicSpreadPanoramaBO::getProvinceUniCode, Function.identity(), (o, v) -> o)));
        Map<Date, SpreadDateDTO> spreadDateMap = parameter.spreadDateMap;
        // Map.Entry<Date, Map<Long, UdicSpreadPanoramaBO>> 日期 + 省 进行分组
        for (Map.Entry<Date, Map<Long, UdicSpreadPanoramaBO>> dateProvinceMapEntry : dateProvinceMap.entrySet()) {
            Date date = dateProvinceMapEntry.getKey();
            SpreadDateDTO spreadDateDTO = spreadDateMap.get(date);
            Date before90Date = spreadDateDTO.getBefore90Date();
            Date before180Date = spreadDateDTO.getBefore180Date();
            Map<Long, UdicSpreadPanoramaBO> before90ProvinceMap = before90DateProvinceMap.getOrDefault(before90Date, Collections.emptyMap());
            Map<Long, UdicSpreadPanoramaBO> before180ProvinceMap = before180DateProvinceMap.getOrDefault(before180Date, Collections.emptyMap());
            // 省 进行分组,转换为导出数据
            List<UdicSpreadPanoramaExportExcelDTO> singleDateExportList = dateProvinceMapEntry.getValue().entrySet().stream().map(provinceMapEntry -> {
                Long province = provinceMapEntry.getKey();
                AreaInfoResponseDTO areaInfo = provinceAreaInfoMap.get(province);
                return convertToUdicPanoramaExportDTO(date, areaInfo, before90ProvinceMap.get(province), before180ProvinceMap.get(province), provinceMapEntry);
            }).collect(Collectors.toList());
            exportList.addAll(singleDateExportList);
        }
        return exportList;
    }

    /**
     * 构建全景导出参数
     *
     * @param request   城投全景请求dto
     * @param areaType  区域类型
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     */
    private UdicPanoramaExportParameter buildPanoramaExportParameter(UdicPanoramaRequestDTO request, Integer areaType, Date startDate, Date endDate) {
        UdicPanoramaExportParameter parameter = new UdicPanoramaExportParameter();
        parameter.areaType = ITextValueEnum.getEnum(AreaTypeEnum.class, areaType);
        parameter.startDate = startDate;
        parameter.endDate = endDate;
        parameter.administrativeDivision = request.getAdministrativeDivision();
        parameter.bondExtRatingMapping = request.getBondExtRatingMapping();
        parameter.bondImpliedRatingMappingTag = request.getBondImpliedRatingMappingTag();
        parameter.yyRatingMappingTag = request.getComYyRatingMappingTag();
        parameter.guaranteeStatus = request.getGuaranteeStatus();
        parameter.spreadBondType = request.getSpreadBondType();
        parameter.spreadRemainingTenorTag = request.getSpreadRemainingTenorTag();
        parameter.bondImpliedRatingMappings = request.getBondImpliedRatingMappings();
        parameter.comYyRatingMappings = request.getComYyRatingMappings();
        // 获取三月，六月日期集合
        Map<Date, SpreadDateDTO> spreadDateMap = yieldSpreadCommonService.getSpreadDateMap(startDate, endDate);
        parameter.spreadDateMap = spreadDateMap;
        parameter.before90StartDate = spreadDateMap.get(startDate).getBefore90Date();
        parameter.before90EndDate = spreadDateMap.get(endDate).getBefore90Date();
        parameter.before180StartDate = spreadDateMap.get(startDate).getBefore180Date();
        parameter.before180EndDate = spreadDateMap.get(endDate).getBefore180Date();
        return parameter;
    }

    /**
     * 城投全景导出参数
     *
     * <AUTHOR>
     */
    private static class UdicPanoramaExportParameter {

        private AreaTypeEnum areaType;

        private Integer administrativeDivision;

        private Integer bondExtRatingMapping;

        private Integer bondImpliedRatingMappingTag;

        private Integer yyRatingMappingTag;

        private Integer guaranteeStatus;

        private Integer spreadBondType;

        private Integer spreadRemainingTenorTag;

        private Map<Date, SpreadDateDTO> spreadDateMap;

        private Date startDate;

        private Date endDate;

        private Date before90StartDate;

        private Date before90EndDate;

        private Date before180StartDate;

        private Date before180EndDate;

        private Integer[] bondImpliedRatingMappings;

        private Integer[] comYyRatingMappings;

    }

    private UdicSpreadPanoramaExportExcelDTO convertToUdicPanoramaExportDTO(Date date, AreaInfoResponseDTO areaInfo,
                                                                            UdicSpreadPanoramaBO before90Province, UdicSpreadPanoramaBO before180Province,
                                                                            Map.Entry<Long, UdicSpreadPanoramaBO> provinceMapEntry) {
        UdicSpreadPanoramaBO spreadPanorama = provinceMapEntry.getValue();
        UdicSpreadPanoramaExportExcelDTO excel = new UdicSpreadPanoramaExportExcelDTO();
        excel.setSpreadDate(date);
        if (Objects.nonNull(spreadPanorama) && spreadPanorama.getBondCreditSpreadCount() >= MIN_BOND_SIZE) {
            excel.setBondCreditSpread(spreadPanorama.getBondCreditSpread());
        }
        if (Objects.nonNull(spreadPanorama) && spreadPanorama.getBondExcessSpreadCount() >= MIN_BOND_SIZE) {
            excel.setBondExcessSpread(spreadPanorama.getBondExcessSpread());
        }
        if (Objects.nonNull(before90Province)) {
            BigDecimal bondCreditSpreadChange90 = subtract(excel.getBondCreditSpread(), before90Province.getBondCreditSpread()).orElse(null);
            excel.setBondCreditSpreadChange90(bondCreditSpreadChange90);
            BigDecimal bondExcessSpreadChange90 = subtract(excel.getBondExcessSpread(), before90Province.getBondExcessSpread()).orElse(null);
            excel.setBondExcessSpreadChange90(bondExcessSpreadChange90);
        }
        if (Objects.nonNull(before180Province)) {
            BigDecimal bondCreditSpreadChange180 = subtract(excel.getBondCreditSpread(), before180Province.getBondCreditSpread()).orElse(null);
            excel.setBondCreditSpreadChange180(bondCreditSpreadChange180);
            BigDecimal bondExcessSpreadChange180 = subtract(excel.getBondExcessSpread(), before180Province.getBondExcessSpread()).orElse(null);
            excel.setBondExcessSpreadChange180(bondExcessSpreadChange180);
        }
        if (Objects.nonNull(areaInfo)) {
            excel.setProvinceName(areaInfo.getProvinceName());
            excel.setCityName(areaInfo.getCityName());
        }
        return excel;
    }

    private List<UdicBondSpreadExportExcelDTO> fillUdicBondSpreadByCompositionConditions(UdicListExportRequestDTO request) {
        if (CollectionUtils.isEmpty(request.getCompositionConditions())) {
            return Collections.emptyList();
        }
        List<UdicBondSpreadExportExcelDTO> exportList = Lists.newArrayList();
        for (UdicCurveCompositionConditionDTO compositionCondition : request.getCompositionConditions()) {
            UdicListRequestDTO udicListRequest = BeanCopyUtils.copyProperties(request, UdicListRequestDTO.class);
            udicListRequest.setCompositionCondition(compositionCondition);
            udicListRequest.setPageNum(EXPORT_START_PAGE);
            udicListRequest.setPageSize(EXPORT_MAX_ROW);
            NormPagingResult<UdicBondYieldSpreadResponseDTO> pagingResult = udicBondYieldSpreadService.getBondYieldSpreadPaging(udicListRequest);
            for (UdicBondYieldSpreadResponseDTO response : pagingResult.getList()) {
                UdicBondSpreadExportExcelDTO excel = BeanCopyUtils.copyProperties(response, UdicBondSpreadExportExcelDTO.class);
                excel.setCurveName(compositionCondition.getCurveName());
                exportList.add(excel);
            }
        }
        return exportList;
    }

}
