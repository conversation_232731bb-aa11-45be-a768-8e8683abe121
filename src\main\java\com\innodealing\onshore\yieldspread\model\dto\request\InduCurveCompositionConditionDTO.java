package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;
import java.util.Objects;


/**
 * 行业曲线请求DTO-组合条件请求参数
 *
 * <AUTHOR>
 */
public class InduCurveCompositionConditionDTO extends CurveCompositionConditionDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("行业编码")
    private Long industryCode;
    @ApiModelProperty("企业性质 1:央企, 2:国企, 3:民企")
    protected Integer[] businessFilterNatures;

    public Long getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(Long industryCode) {
        this.industryCode = industryCode;
    }

    public Integer[] getBusinessFilterNatures() {
        return Objects.isNull(businessFilterNatures) ? new Integer[0] : businessFilterNatures.clone();
    }

    public void setBusinessFilterNatures(Integer[] businessFilterNatures) {
        this.businessFilterNatures = Objects.isNull(businessFilterNatures) ? new Integer[0] : businessFilterNatures.clone();
    }

    @Override
    public String toString() {
        return "InduCurveCompositionConditionDTO{" +
                "industryCode=" + industryCode +
                ", businessFilterNatures=" + Arrays.toString(businessFilterNatures) +
                ", spreadBondType=" + spreadBondType +
                ", bondExtRatingMapping=" + bondExtRatingMapping +
                ", bondImpliedRatingMappingTag=" + bondImpliedRatingMappingTag +
                ", comYyRatingMappingTag=" + comYyRatingMappingTag +
                ", spreadRemainingTenorTag=" + spreadRemainingTenorTag +
                ", guaranteeStatus=" + guaranteeStatus +
                '}';
    }
}
