
package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Date;
import java.util.Objects;

/**
 * 行业曲线利差请求DTO
 *
 * <AUTHOR>
 */
public class InduCurveRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("利差请求类型, 2. 组合条件, 3. 主体利差, 4. 单券利差")
    private Integer spreadRequestType;
    @ApiModelProperty("开始利差日期,格式:yyyy-MM-dd")
    private Date startSpreadDate;
    @ApiModelProperty("结束利差日期,格式:yyyy-MM-dd")
    private Date endSpreadDate;
    @ApiModelProperty("2. 组合查询")
    private InduCurveCompositionConditionDTO compositionCondition;
    @ApiModelProperty("3. 主体利差查询")
    private CurveComSpreadDTO comSpread;
    @ApiModelProperty("4. 单券利差查询")
    private CurveBondSpreadDTO bondSpread;

    public Integer getSpreadRequestType() {
        return spreadRequestType;
    }

    public void setSpreadRequestType(Integer spreadRequestType) {
        this.spreadRequestType = spreadRequestType;
    }


    public Date getStartSpreadDate() {
        return Objects.isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public void setStartSpreadDate(Date startSpreadDate) {
        this.startSpreadDate = Objects.isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public Date getEndSpreadDate() {
        return Objects.isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public void setEndSpreadDate(Date endSpreadDate) {
        this.endSpreadDate = Objects.isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public InduCurveCompositionConditionDTO getCompositionCondition() {
        return compositionCondition;
    }

    public void setCompositionCondition(InduCurveCompositionConditionDTO compositionCondition) {
        this.compositionCondition = compositionCondition;
    }

    public CurveComSpreadDTO getComSpread() {
        return comSpread;
    }

    public void setComSpread(CurveComSpreadDTO comSpread) {
        this.comSpread = comSpread;
    }

    public CurveBondSpreadDTO getBondSpread() {
        return bondSpread;
    }

    public void setBondSpread(CurveBondSpreadDTO bondSpread) {
        this.bondSpread = bondSpread;
    }

    @Override
    public String toString() {
        return "InduCurveRequestDTO{" +
                "spreadRequestType=" + spreadRequestType +
                ", startSpreadDate=" + startSpreadDate +
                ", endSpreadDate=" + endSpreadDate +
                ", compositionCondition=" + compositionCondition +
                ", comSpread=" + comSpread +
                ", bondSpread=" + bondSpread +
                '}';
    }
}