package com.innodealing.onshore.yieldspread.service.impl;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.BenchmarkCurveResponseDTO;
import com.innodealing.onshore.yieldspread.consts.TipsConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserCurveDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.model.bo.BenchmarkCurveDefinitionBO;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionBO;
import com.innodealing.onshore.yieldspread.model.dto.request.ICurveGenerateReq;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveDataResDTO;
import com.innodealing.onshore.yieldspread.service.internal.BondPriceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;

/**
 * 中债曲线
 *
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"squid:S109"})
public class ChinaBondCurveService extends AbstractBondCurveService {

    /**
     * 基准曲线映射
     */
    private static final Map<String, BenchmarkCurveDefinitionBO> BENCHMARK_MAPPING_MAP = initBenchmarkCurveMapping();

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private UserCurveDAO userCurveDAO;

    /**
     * 组名：[曲线名]
     * order从低到高排，页面列表是order倒序
     */
    private static final Map<String, List<String>> BENCHMARK_GROUP_CURVE_MAP = new LinkedHashMap<>();

    /**
     * 曲线名：组名
     */
    private static final Map<String, String> BENCHMARK_CURVE_GROUP_NAME_MAP = new HashMap<>();

    static {
        List<String> udicCurves = new ArrayList<>();
        BENCHMARK_GROUP_CURVE_MAP.put("DM城投曲线", udicCurves);
        udicCurves.add("DM经济低债务高云贵");
        udicCurves.add("DM经济高债务高苏津");
        udicCurves.add("DM经济低债务低东三省");
        udicCurves.add("DM强省弱县江浙区县");
        udicCurves.add("DM弱省强市昆明贵阳");
        udicCurves.add("DM重要平台");
        udicCurves.add("DM核心平台");
        udicCurves.add("DM次要平台");
        udicCurves.add("DM城投未涉房");
        udicCurves.add("DM城投涉房");
        udicCurves.add("DM基建");
        udicCurves.add("DM文旅");
        udicCurves.add("DM金控");
        udicCurves.add("DM产控");
        udicCurves.add("DM城投评分预警级");
        udicCurves.add("DM城投评分投机级");
        udicCurves.add("DM城投评分投资级");

        List<String> cdbCurves = new ArrayList<>();
        BENCHMARK_GROUP_CURVE_MAP.put("中债国开债到期收益率", cdbCurves);
        cdbCurves.add("中债国开债到期收益率10Y");
        cdbCurves.add("中债国开债到期收益率7Y");
        cdbCurves.add("中债国开债到期收益率5Y");
        cdbCurves.add("中债国开债到期收益率3Y");
        cdbCurves.add("中债国开债到期收益率1Y");

        List<String> nationalDebtCurves = new ArrayList<>();
        BENCHMARK_GROUP_CURVE_MAP.put("中债国债到期收益率", nationalDebtCurves);
        nationalDebtCurves.add("中债国债到期收益率10Y");
        nationalDebtCurves.add("中债国债到期收益率7Y");
        nationalDebtCurves.add("中债国债到期收益率5Y");
        nationalDebtCurves.add("中债国债到期收益率3Y");
        nationalDebtCurves.add("中债国债到期收益率1Y");

        for (Map.Entry<String, List<String>> entry : BENCHMARK_GROUP_CURVE_MAP.entrySet()) {
            List<String> curveNames = entry.getValue();
            for (String curveName : curveNames) {
                BENCHMARK_CURVE_GROUP_NAME_MAP.put(curveName, entry.getKey());
            }
        }
    }

    public static Map<String, BenchmarkCurveDefinitionBO> getBenchmarkCurveDefinitions() {
        return BENCHMARK_MAPPING_MAP;
    }

    public static Map<String, List<String>> getBenchmarkGroupCurveMap() {
        return BENCHMARK_GROUP_CURVE_MAP;
    }

    public static Map<String, String> getBenchmarkCurveGroupNameMap() {
        return BENCHMARK_CURVE_GROUP_NAME_MAP;
    }

    @Override
    protected Date getMaxSpreadDate() {
        return Date.valueOf(LocalDate.now());
    }

    @Override
    public List<CurveDataResDTO> listCurveData(Long curveId) {
        Optional<CurveDefinitionBO> curveBaseDataOptional = userCurveDAO.getCurveDefinitionBO(curveId);
        CurveDefinitionBO curveDefinitionBO = curveBaseDataOptional.orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        BenchmarkCurveDefinitionBO benchmarkCurveMetadata = BENCHMARK_MAPPING_MAP.get(curveDefinitionBO.getSpreadCurveName());
        if (Objects.isNull(benchmarkCurveMetadata)) {
            throw new TipsException(TipsConst.CURVE_NOT_EXIST);
        }
        Integer curveCode = benchmarkCurveMetadata.getCurveCode();
        String ytm = benchmarkCurveMetadata.getYtm();
        Date startDate = YieldSpreadConst.CURVE_START_DATE;
        Date endDate = Date.valueOf(LocalDate.now());
        List<BenchmarkCurveResponseDTO> bondYields = bondPriceService.listBenchmarkYields(curveCode, ytm, startDate, endDate);
        List<CurveDataResDTO> curveData = new ArrayList<>();
        Optional.ofNullable(bondYields).ifPresent(ds -> ds.forEach(d -> {
            CurveDataResDTO curveDataResDTO = new CurveDataResDTO();
            curveDataResDTO.setSpreadDate(d.getIssueDate());
            curveDataResDTO.setYtm(benchmarkCurveMetadata.getFunction().apply(d));
            curveData.add(curveDataResDTO);
        }));
        return curveData;
    }

    @Override
    protected List<CurveDataResDTO> listCurveData(ICurveGenerateReq curveGenerateParam) {
        return Collections.emptyList();
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.CB;
    }

    private static Map<String, BenchmarkCurveDefinitionBO> initBenchmarkCurveMapping() {
        Map<String, BenchmarkCurveDefinitionBO> benchmarkCurveMetadataMap = new LinkedHashMap<>(10);
        benchmarkCurveMetadataMap.put("中债国债到期收益率1Y", new BenchmarkCurveDefinitionBO(1, "ytm1Y", BenchmarkCurveResponseDTO::getYtm1Y));
        benchmarkCurveMetadataMap.put("中债国债到期收益率3Y", new BenchmarkCurveDefinitionBO(1, "ytm3Y", BenchmarkCurveResponseDTO::getYtm3Y));
        benchmarkCurveMetadataMap.put("中债国债到期收益率5Y", new BenchmarkCurveDefinitionBO(1, "ytm5Y", BenchmarkCurveResponseDTO::getYtm5Y));
        benchmarkCurveMetadataMap.put("中债国债到期收益率7Y", new BenchmarkCurveDefinitionBO(1, "ytm7Y", BenchmarkCurveResponseDTO::getYtm7Y));
        benchmarkCurveMetadataMap.put("中债国债到期收益率10Y", new BenchmarkCurveDefinitionBO(1, "ytm10Y", BenchmarkCurveResponseDTO::getYtm10Y));
        benchmarkCurveMetadataMap.put("中债国开债到期收益率1Y", new BenchmarkCurveDefinitionBO(4, "ytm1Y", BenchmarkCurveResponseDTO::getYtm1Y));
        benchmarkCurveMetadataMap.put("中债国开债到期收益率3Y", new BenchmarkCurveDefinitionBO(4, "ytm3Y", BenchmarkCurveResponseDTO::getYtm3Y));
        benchmarkCurveMetadataMap.put("中债国开债到期收益率5Y", new BenchmarkCurveDefinitionBO(4, "ytm5Y", BenchmarkCurveResponseDTO::getYtm5Y));
        benchmarkCurveMetadataMap.put("中债国开债到期收益率7Y", new BenchmarkCurveDefinitionBO(4, "ytm7Y", BenchmarkCurveResponseDTO::getYtm7Y));
        benchmarkCurveMetadataMap.put("中债国开债到期收益率10Y", new BenchmarkCurveDefinitionBO(4, "ytm10Y", BenchmarkCurveResponseDTO::getYtm10Y));
        return benchmarkCurveMetadataMap;
    }

}
