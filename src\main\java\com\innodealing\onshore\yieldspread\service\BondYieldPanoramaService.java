package com.innodealing.onshore.yieldspread.service;


import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldPanoramaExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldPanoramaResponseDTO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;
import java.util.List;

/**
 * 债券收益率全景服务
 *
 * <AUTHOR>
 */
public interface BondYieldPanoramaService {

    /**
     * 获取到期收益率全景绝对值
     *
     * @param userId     用户id
     * @param spreadDate 利率时间
     * @return 收益率全景绝对值
     */
    YieldPanoramaResponseDTO getYieldPanoramaAbs(Long userId, Date spreadDate);

    /**
     * 获取到期收益率全景历史分位
     *
     * @param userId       用户id
     * @param spreadDate   利率时间
     * @param quantileType 分位类型 1:3年，2:5年
     * @param startDate    自定义开始时间
     * @param endDate      自定义结束时间
     * @return 收益率全景绝对值
     */
    YieldPanoramaResponseDTO getYieldPanoramaQuantile(Long userId, Date spreadDate, Integer quantileType, Date startDate, Date endDate);

    /**
     * 获取到期收益率全景区间变动
     *
     * @param userId     用户id
     * @param spreadDate 利率时间
     * @param changeType 变动类型(1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动 6 一日变动 7 两周变动)
     * @param startDate  自定义开始时间
     * @param endDate    自定义结束时间
     * @return 收益率全景绝对值
     */
    YieldPanoramaResponseDTO getYieldPanoramaChange(Long userId, Date spreadDate, Integer changeType, Date startDate, Date endDate);

    /**
     * 导出收益率全景
     *
     * @param httpServletResponse           响应体
     * @param userId                        用户id
     * @param bondYieldPanoramaExportReqDTO 请求参数
     * @throws IOException Exception
     */
    void exportYieldPanorama(HttpServletResponse httpServletResponse, Long userId, BondYieldPanoramaExportReqDTO bondYieldPanoramaExportReqDTO) throws IOException;

    /**
     * 获取全景利差有数据的最大日期
     *
     * @return 利差日期
     */
    Date maxSpreadDate();

    /**
     * 同步历史收益率全景
     *
     * @param startDate 开始日期
     * @return int 同步行数
     */
    int syncHistBondYieldPanorama(Date startDate);

    /**
     * 同步收益率全景(增量),不传日期默认同步上一天数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return int 同步行数
     */
    int syncBondYieldPanorama(Date startDate, Date endDate);


    /**
     * 同步历史区间变动数据
     *
     * @param startDate 开始日期
     * @return int
     */
    int syncHistIntervalChange(Date startDate);

    /**
     * 同步历史分位
     *
     * @param startDate 开始日期
     * @return int
     */
    int syncHistQuantile(Date startDate);

    /**
     * 同步历史收益率全景-曲线
     *
     * @param startDate 开始日期
     * @return int 同步行数
     */
    int syncBondYieldSpreadPanoramaByCurveCodes(Date startDate, Date endDate, List<Integer> curveCodeList);
}
