#spring.profiles.active=dev
server.servlet.context-path=/onshore-yield-spread
spring.jackson.serialization.write-dates-as-timestamps=true
# Druid
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.allow=
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.web-stat-filter.enabled=true
# yield_spread
yieldspread.datasource.url=jdbc:mysql://${PUBLIC_MYSQL_HOST_PRICE}:${PUBLIC_MYSQL_PORT_PRICE}/yield_spread?rewriteBatchedStatements\=true&useUnicode\=true&characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull&useSSL\=false&allowMultiQueries\=true
yieldspread.datasource.username=${PUBLIC_MYSQL_USER_PRICE}
yieldspread.datasource.password=${PUBLIC_MYSQL_PASSWD_PRICE}
yieldspread.datasource.driver-class-name=com.mysql.jdbc.Driver
yieldspread.datasource.db-type=mysql
yieldspread.datasource.use-global-data-source-stat=true
yieldspread.datasource.pool-prepared-statements=true
yieldspread.datasource.filters=stat,wall
yieldspread.datasource.test-while-idle=true
yieldspread.datasource.initial-size=20
yieldspread.datasource.min-idle=5
yieldspread.datasource.max-active=100
yieldspread.datasource.time-between-eviction-runs-millis=60000
yieldspread.datasource.min-evictable-idle-time-millis=300000
yieldspread.datasource.validation-query=SELECT 1
# dmdc
dmdc.datasource.url=jdbc:mysql://${PUBLIC_MYSQL_HOST}:${PUBLIC_MYSQL_PORT}/dmdc?useUnicode\=true&characterEncoding\=UTF-8&zeroDateTimeBehavior\=convertToNull
dmdc.datasource.username=${PUBLIC_MYSQL_USER}
dmdc.datasource.password=${PUBLIC_MYSQL_PASSWD}
dmdc.datasource.driver-class-name=com.mysql.jdbc.Driver
dmdc.datasource.db-type=mysql
dmdc.datasource.use-global-data-source-stat=true
dmdc.datasource.pool-prepared-statements=true
dmdc.datasource.filters=stat,wall
dmdc.datasource.test-while-idle=true
dmdc.datasource.initial-size=2
dmdc.datasource.min-idle=2
dmdc.datasource.max-active=20
dmdc.datasource.time-between-eviction-runs-millis=60000
dmdc.datasource.min-evictable-idle-time-millis=300000
dmdc.datasource.validation-query=SELECT 1
# postgresql
pgyieldspread.datasource.url=jdbc:postgresql://${PUBLIC_GREENPLUM_HOST}:${PUBLIC_GREENPLUM_PORT}/${PUBLIC_GREENPLUM_DATABASE}?currentSchema=yield_spread&reWriteBatchedInserts\=true&allowMultiQueries\=true
pgyieldspread.datasource.username=${PUBLIC_GREENPLUM_USER}
pgyieldspread.datasource.password=${PUBLIC_GREENPLUM_PASSWD}
pgyieldspread.datasource.driver-class-name=org.postgresql.Driver
pgyieldspread.datasource.db-type=postgresql
pgyieldspread.datasource.use-global-data-source-stat=true
pgyieldspread.datasource.pool-prepared-statements=true
pgyieldspread.datasource.filters=stat
pgyieldspread.datasource.initial-size=5
pgyieldspread.datasource.min-idle=5
pgyieldspread.datasource.max-active=50
pgyieldspread.datasource.time-between-eviction-runs-millis=60000
pgyieldspread.datasource.validation-query=SELECT 'x'
# test connection is while idle
pgyieldspread.datasource.test-while-idle=true
#30 min
pgyieldspread.datasource.min-evictable-idle-time-millis=1800000
#1 hour     this default value is 7 hour\uFF0C bug we pgsql server config is 6 hour\u3002 so throw "server closed the connection unexpectedly"
pgyieldspread.datasource.max-evictable-idle-time-millis=3600000
# default value
sharding.show.sql=${SHARDING_SHOW_SQL}
sharding.yield.spread=${SHARDING_YIELD_SPREAD_START_DATE}
# feign
feign.httpclient.enabled=true
feign.client.config.default.readTimeout=15000
# http
bond.basic.api.url=${ONSHORE_BOND_BASIC_URL}
com.service.api.url=${ONSHORE_COM_SERVICE_URL}
udic.service.api.url=${ONSHORE_UDIC_SERVICE_URL}
bond.price.api.url=${ONSHORE_BOND_PRICE_URL}
bond.price.apollo.api.url=${ONSHORE_BOND_PRICE_APOLLO_URL}
bond.rating.api.url=${ONSHORE_BOND_RATING_URL}
area.service.api.url=${ONSHORE_AREA_SERVICE_URL}
bond.finance.api.url=${ONSHORE_BOND_FINANCE_URL}
bond.financial.institution.api.url=${ONSHORE_BOND_FINANCIAL_INSTITUTION_URL}
user.service.api.url=${ONSHORE_USER_SERVICE_URL}
# redis
spring.redis.host=${PUBLIC_CODIS_HOST}
spring.redis.port=${PUBLIC_CODIS_PORT}
spring.redis.password=${PUBLIC_CODIS_PASSWD}
spring.redis.database=${PUBLIC_CODIS_DATABASE}
#rocketmq
rocketmq.producer.group=onshore-yield-spread
rocketmq.name-server=${PUBLIC_ROCKETMQ_NAME_SERVER}
rocketmq.producer.access-key=${PUBLIC_ROCKETMQ_ACCESS_KEY}
rocketmq.producer.secret-key=${PUBLIC_ROCKETMQ_SECRET_KEY}