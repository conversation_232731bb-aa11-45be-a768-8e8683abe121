<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvUdicBondYieldSpreadCurveMapper">

    <sql id="ratingRouterQuerySql">
        SELECT MAX( udic_bond_yield_spread.ID ) AS ID,
        udic_bond_yield_spread.spread_date,
        <choose>
            <when test="parameter.operatorLevel == 'province'">
                udic_bond_yield_spread.province_uni_code as operator_level,
            </when>
            <when test="parameter.operatorLevel == 'city'">
                udic_bond_yield_spread.city_uni_code as operator_level,
            </when>
            <otherwise>
                000000 AS operator_level,
            </otherwise>
        </choose>
        MEDIAN ((( udic_bond_yield_spread.bond_credit_spread * ( 100000 ) :: NUMERIC )) :: BIGINT) AS
        bond_credit_spread,
        MEDIAN ((( udic_bond_yield_spread.bond_excess_spread * ( 100000 ) :: NUMERIC )) :: BIGINT) AS
        bond_excess_spread,
        MEDIAN ((( udic_bond_yield_spread.cb_yield * ( 100000 ) :: NUMERIC )) :: BIGINT) AS cb_yield,
        AVG(((udic_bond_yield_spread.bond_credit_spread * (100000)::numeric))::bigint) AS avg_bond_credit_spread,
        AVG(((udic_bond_yield_spread.bond_excess_spread * (100000)::numeric))::bigint) AS avg_bond_excess_spread,
        AVG(((udic_bond_yield_spread.cb_yield * (100000)::numeric))::bigint) AS avg_cb_yield,
        udic_bond_yield_spread.spread_bond_type,
        udic_bond_yield_spread.bond_ext_rating_mapping,
        udic_bond_yield_spread.administrative_division,
        udic_bond_yield_spread.spread_remaining_tenor_tag,
        udic_bond_yield_spread.guaranteed_status,
        COUNT ( udic_bond_yield_spread.bond_credit_spread ) AS bond_credit_spread_count,
        COUNT ( udic_bond_yield_spread.bond_excess_spread ) AS bond_excess_spread_count,
        COUNT ( udic_bond_yield_spread.cb_yield ) AS cb_yield_count,
        GROUPING ( udic_bond_yield_spread.spread_bond_type ) AS using_spread_bond_type,
        GROUPING ( udic_bond_yield_spread.bond_ext_rating_mapping ) AS using_bond_ext_rating_mapping,
        GROUPING ( udic_bond_yield_spread.administrative_division ) AS using_administrative_division,
        GROUPING ( udic_bond_yield_spread.spread_remaining_tenor_tag ) AS using_spread_remaining_tenor_tag,
        GROUPING ( udic_bond_yield_spread.guaranteed_status ) AS using_guaranteed_status
        FROM
        yield_spread.udic_bond_yield_spread as udic_bond_yield_spread
        where 1=1
        <if test="parameter.spreadDateRange == null">
            AND udic_bond_yield_spread.spread_date = (('now'::text)::date - 1)
        </if>
        <if test="parameter.spreadDateRange != null">
            AND udic_bond_yield_spread.spread_date BETWEEN '${parameter.spreadDateRange.startDate}' and
            '${parameter.spreadDateRange.endDate}'
        </if>
        <if test="parameter.impliedRatingMappings != null and parameter.impliedRatingMappings.size() > 0">
            AND udic_bond_yield_spread.bond_implied_rating_mapping in
            <foreach collection="parameter.impliedRatingMappings" item="item" open="(" close=")" separator=",">
                '${item}'
            </foreach>
        </if>
        <if test="parameter.yyRatingMappings != null and parameter.yyRatingMappings.size() > 0">
            AND udic_bond_yield_spread.com_yy_rating_mapping in
            <foreach collection="parameter.yyRatingMappings" item="item" open="(" close=")" separator=",">
                '${item}'
            </foreach>
        </if>
        GROUP BY
        udic_bond_yield_spread.spread_date,
        <if test="parameter.operatorLevel == 'city'">
            udic_bond_yield_spread.city_uni_code,
        </if>
        <if test="parameter.operatorLevel == 'province'">
            udic_bond_yield_spread.province_uni_code,
        </if>
        CUBE (
        udic_bond_yield_spread.spread_bond_type,
        udic_bond_yield_spread.bond_ext_rating_mapping,
        udic_bond_yield_spread.administrative_division,
        udic_bond_yield_spread.spread_remaining_tenor_tag,
        udic_bond_yield_spread.guaranteed_status
        )
    </sql>
    <update id="createMvRatingRouter">
        create MATERIALIZED view yield_spread.${parameter.tableName}
        WITH (appendoptimized= true, orientation = column)
        as
        <include refid="ratingRouterQuerySql"></include>
    </update>

    <update id="refreshMvUdicBondYieldSpreadCurveAll">
        REFRESH
        MATERIALIZED VIEW yield_spread.mv_udic_bond_yield_spread_curve_all;
    </update>

    <update id="refreshMvUdicBondYieldSpreadCurveProvince">
        REFRESH
        MATERIALIZED VIEW yield_spread.mv_udic_bond_yield_spread_curve_province;
    </update>

    <update id="refreshMvUdicBondYieldSpreadCurveCity">
        REFRESH
        MATERIALIZED VIEW yield_spread.mv_udic_bond_yield_spread_curve_city;
    </update>

    <update id="refreshMvUdicBondYieldSpreadCurveDistrict">
        REFRESH
        MATERIALIZED VIEW yield_spread.mv_udic_bond_yield_spread_curve_district;
    </update>

    <update id="refreshMvUdicBondYieldSpreadCurveAllYesterday">
        REFRESH
        MATERIALIZED VIEW yield_spread.mv_udic_bond_yield_spread_curve_all_yesterday;
    </update>

    <update id="refreshMvUdicBondYieldSpreadCurveProvinceYesterday">
        REFRESH
        MATERIALIZED VIEW yield_spread.mv_udic_bond_yield_spread_curve_province_yesterday;
    </update>

    <update id="refreshMvUdicBondYieldSpreadCurveCityYesterday">
        REFRESH
        MATERIALIZED VIEW yield_spread.mv_udic_bond_yield_spread_curve_city_yesterday;
    </update>

    <update id="refreshMvUdicBondYieldSpreadCurveDistrictYesterday">
        REFRESH
        MATERIALIZED VIEW yield_spread.mv_udic_bond_yield_spread_curve_district_yesterday;
    </update>
</mapper>