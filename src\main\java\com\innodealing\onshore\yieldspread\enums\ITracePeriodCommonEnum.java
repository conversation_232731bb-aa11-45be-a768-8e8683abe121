package com.innodealing.onshore.yieldspread.enums;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 利差追踪期限利差公共枚举
 *
 * <AUTHOR>
 * @date 2024/10/22 10:04
 **/
public interface ITracePeriodCommonEnum {
    /**
     * 对应期限类型
     *
     * @return {@link PeriodEnum}
     */
    PeriodEnum getPeriodEnum();

    /**
     * 期限利差描述
     *
     * @return 期限利差描述
     */
    String getDesc();

    /**
     * 减数
     *
     * @return {@link PeriodEnum}
     */
    PeriodEnum getSubtrahendEnum();

    /**
     * 被减数
     *
     * @return {@link PeriodEnum}
     */
    PeriodEnum getMinuendEnum();


    /**
     * 获取所有枚举数组
     *
     * @param iTracePeriodCommonEnumClass 枚举类型
     * @param <T>                         泛型
     * @return 枚举值
     */
    static <T extends ITracePeriodCommonEnum> T[] getAllEnums(Class<T> iTracePeriodCommonEnumClass) {
        return iTracePeriodCommonEnumClass.getEnumConstants();
    }


    /**
     * 获取所有枚举列表
     *
     * @param iTracePeriodCommonEnumClass 枚举类型
     * @param <T>                         泛型
     * @return 枚举值
     */
    static <T extends ITracePeriodCommonEnum> List<T> listAllEnums(Class<T> iTracePeriodCommonEnumClass) {
        return Arrays.asList(iTracePeriodCommonEnumClass.getEnumConstants());
    }

    /**
     * 获取期限利差枚举值
     *
     * @param iTracePeriodCommonEnumClass 枚举类型
     * @param periodEnum                  期限枚举类型
     * @param <T>                         泛型
     * @return 期限利差类型所对应的 期限利差值
     */
    static <T extends ITracePeriodCommonEnum> Optional<T> getTracePeroidByPeroidEnum(Class<T> iTracePeriodCommonEnumClass, PeriodEnum periodEnum) {
        if (Objects.isNull(periodEnum) || Objects.isNull(iTracePeriodCommonEnumClass)) {
            return Optional.empty();
        }
        return Arrays.stream(iTracePeriodCommonEnumClass.getEnumConstants()).filter(tracePeriodCommonEnum -> periodEnum.equals(tracePeriodCommonEnum.getPeriodEnum())).findAny();
    }

    /**
     * 获取期限利差字段(需要根据desc去重)
     *
     * @param iTracePeriodCommonEnumClass 枚举类型
     * @param periodEnumList              用户选择的期限列表
     * @param <T>                         泛型
     * @return 期限利差类型所对应的 期限利差值
     */
    static <T extends ITracePeriodCommonEnum> List<PeriodEnum> listPeroidByDistinctDesc(Class<T> iTracePeriodCommonEnumClass, List<PeriodEnum> periodEnumList) {
        if (CollectionUtils.isEmpty(periodEnumList)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(iTracePeriodCommonEnumClass.getEnumConstants())
                .filter(tracePeriodEnum -> periodEnumList.contains(tracePeriodEnum.getPeriodEnum()))
                .collect(Collectors.toMap(ITracePeriodCommonEnum::getDesc, ITracePeriodCommonEnum::getPeriodEnum, (o, v) -> o))
                .values().stream().sorted(Comparator.comparing(Enum::ordinal)).collect(Collectors.toList());
    }
}
