package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;
import java.util.Objects;

/**
 * 地方债区域利差历史分位 DO
 * <AUTHOR>
 * @create: 2024-11-05
 */
@Table(name = "bond_spread_lg_quantile")
public class PgLgBondYieldSpreadQuantileDO extends PgBaseLgBondYieldSpreadDO{

    /**
     * 历史分位类型, 1: 3年 2: 5年
     */
    @Column
    private Integer quantileType;

    /**
     * 开始日期
     */
    @Column
    private Date startDate;

    public Integer getQuantileType() {
        return quantileType;
    }

    public void setQuantileType(Integer quantileType) {
        this.quantileType = quantileType;
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }
}
