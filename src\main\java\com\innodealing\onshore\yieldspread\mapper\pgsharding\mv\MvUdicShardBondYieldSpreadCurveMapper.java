package com.innodealing.onshore.yieldspread.mapper.pgsharding.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.mv.MvUdicShardBondYieldSpreadCurveDO;

import javax.persistence.Table;

/**
 * 城投利差曲线-物化视图
 *
 * <AUTHOR>
 */

public interface MvUdicShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<MvUdicShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     * @return tableName
     */
    default String getLogicTable() {
        return MvUdicShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }


}
