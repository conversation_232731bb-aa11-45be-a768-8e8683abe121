package com.innodealing.onshore.yieldspread.config.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.innodealing.onshore.bondmetadata.sharding.YearTablePreciseShardingAlgorithm;
import com.innodealing.onshore.bondmetadata.sharding.YearTableRangeShardingAlgorithm;
import com.innodealing.onshore.yieldspread.config.shardingsphere.CaliberYearTablePreciseShardingAlgorithm;
import com.innodealing.onshore.yieldspread.config.shardingsphere.CaliberYearTableRangeShardingAlgorithm;
import com.innodealing.onshore.yieldspread.config.shardingsphere.StrParamHintShardingAlgorithm;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BondYieldSpreadDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.shardingsphere.api.config.sharding.ShardingRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.TableRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.HintShardingStrategyConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.ShardingStrategyConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.StandardShardingStrategyConfiguration;
import org.apache.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.spring.annotation.MapperScan;

import javax.persistence.Table;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * MysqlDataSourceConfig
 *
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.innodealing.onshore.yieldspread.mapper.yieldspread"},
        sqlSessionFactoryRef = YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
public class YieldSpreadDataSourceConfig extends BaseSourceConfig {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String TRANSACTION_NAME = "yieldspreadTransactionManager";

    private static final String DATA_SOURCE_NAME = "yieldspreadDataSource";

    private static final String DATA_SOURCE_PREFIX = "yieldspread.datasource";

    private static final String SHARDING_DATA_SOURCE_NAME = "shardingYieldspreadDataSource";

    public static final String SESSION_FACTORY_NAME = "yieldspreadSqlSessionFactory";

    public static final String TRANSACTION_TEMPLATE_NAME = "yieldspreadTransactionTemplate";

    protected static final String[] ALIAS_PACKAGES = {"com.innodealing.onshore.yieldspread.model.entity.yieldspread"};

    private static final String DATABASE_NAME = "yield_spread";

    private static final String DATABASE_TABLE_PLACEHOLDER = "%s.%s";

    private Boolean shardingShowSql;

    private static final int INITIAL_CAPACITY = 8;

    /**
     * 构造方法
     *
     * @param shardingShowSql 是否显示sharding sql
     */
    @Autowired
    public YieldSpreadDataSourceConfig(@Value("${sharding.show.sql}") Boolean shardingShowSql) {
        this.shardingShowSql = shardingShowSql;
    }

    /**
     * 创建数据源
     *
     * @return 返回数据源
     */
    @Bean(name = DATA_SOURCE_NAME, initMethod = "init", destroyMethod = "close")
    @ConfigurationProperties(prefix = DATA_SOURCE_PREFIX)
    public DruidDataSource dataSource() {
        DruidDataSource build = DruidDataSourceBuilder.create().build();
        logger.info("[创建分片数据源:{}]", build.getUrl());
        return build;
    }

    /**
     * 分片数据源
     *
     * @return 返回分片数据源
     * @throws SQLException sql 异常
     */
    @Bean(name = SHARDING_DATA_SOURCE_NAME)
    public DataSource shardingDataSource() throws SQLException {
        // 配置分片规则
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        Map<String, DataSource> dataSourceMap = new HashMap<>(INITIAL_CAPACITY);
        String udicBondYieldSpread = "udic_bond_yield_spread";
        String induBondYieldSpread = "indu_bond_yield_spread";
        String secuBondYieldSpread = "secu_bond_yield_spread";
        String lgBondYieldSpread = "lg_bond_yield_spread";
        String bankBondYieldSpread = "bank_bond_yield_spread";
        String insuBondYieldSpread = "insu_bond_yield_spread";
        String udicAreaYieldSpread = "udic_area_yield_spread";
        dataSourceMap.put(DATABASE_NAME, dataSource());

        TableRuleConfiguration udicBondYieldSpreadTableRuleConfig = new TableRuleConfiguration(
                udicBondYieldSpread, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, udicBondYieldSpread));
        udicBondYieldSpreadTableRuleConfig.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration(
                "id", new CaliberYearTablePreciseShardingAlgorithm(), new CaliberYearTableRangeShardingAlgorithm()));

        TableRuleConfiguration induBondYieldSpreadTableRuleConfig = new TableRuleConfiguration(
                induBondYieldSpread, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, induBondYieldSpread));
        induBondYieldSpreadTableRuleConfig.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration(
                "id", new YearTablePreciseShardingAlgorithm(), new YearTableRangeShardingAlgorithm()));

        TableRuleConfiguration secuBondYieldSpreadTableRuleConfig = new TableRuleConfiguration(
                secuBondYieldSpread, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, induBondYieldSpread));
        secuBondYieldSpreadTableRuleConfig.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration(
                "id", new YearTablePreciseShardingAlgorithm(), new YearTableRangeShardingAlgorithm()));

        TableRuleConfiguration lgBondYieldSpreadTableRuleConfig = new TableRuleConfiguration(
                lgBondYieldSpread, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, induBondYieldSpread));
        lgBondYieldSpreadTableRuleConfig.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration(
                "id", new YearTablePreciseShardingAlgorithm(), new YearTableRangeShardingAlgorithm()));

        TableRuleConfiguration bankBondYieldSpreadTableRuleConfig = new TableRuleConfiguration(
                bankBondYieldSpread, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, induBondYieldSpread));
        bankBondYieldSpreadTableRuleConfig.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration(
                "id", new YearTablePreciseShardingAlgorithm(), new YearTableRangeShardingAlgorithm()));

        TableRuleConfiguration insuBondYieldSpreadTableRuleConfig = new TableRuleConfiguration(
                insuBondYieldSpread, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, insuBondYieldSpread));
        insuBondYieldSpreadTableRuleConfig.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration(
                "id", new YearTablePreciseShardingAlgorithm(), new YearTableRangeShardingAlgorithm()));

        shardingRuleConfig.getTableRuleConfigs().add(this.getHintShardingTableRuleConfiguration(udicAreaYieldSpread));
        shardingRuleConfig.getTableRuleConfigs().add(induBondYieldSpreadTableRuleConfig);
        shardingRuleConfig.getTableRuleConfigs().add(udicBondYieldSpreadTableRuleConfig);
        shardingRuleConfig.getTableRuleConfigs().add(secuBondYieldSpreadTableRuleConfig);
        shardingRuleConfig.getTableRuleConfigs().add(lgBondYieldSpreadTableRuleConfig);
        shardingRuleConfig.getTableRuleConfigs().add(bankBondYieldSpreadTableRuleConfig);
        shardingRuleConfig.getTableRuleConfigs().add(insuBondYieldSpreadTableRuleConfig);
        shardingRuleConfig.getTableRuleConfigs().add(this.getTableRuleConfig(BondYieldSpreadDO.class));

        Properties props = new Properties();
        if (this.shardingShowSql) {
            props.setProperty("sql.show", "true");
        }
        //创建分片数据源
        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, props);
    }

    /**
     * 获取路由配置 会使用默认分片策略 getIdYearShardingStrategy()
     *
     * @param tableEntityClass 表对应实体
     * @param <T>              表对应实体
     * @return 路由配置
     */
    private <T> TableRuleConfiguration getTableRuleConfig(Class<T> tableEntityClass) {
        return getTableRuleConfig(tableEntityClass, null);
    }

    /**
     * 获取路由配置
     *
     * @param tableEntityClass 表对应实体
     * @param strategy         分片策略
     * @param <T>              表对应实体
     * @return 路由配置
     */
    private <T> TableRuleConfiguration getTableRuleConfig(Class<T> tableEntityClass, ShardingStrategyConfiguration strategy) {
        if (Objects.isNull(tableEntityClass)) {
            throw new IllegalArgumentException("getTableRuleConfig,table entity class can't be null.");
        }
        String tableName = tableEntityClass.getAnnotation(Table.class).name();
        TableRuleConfiguration config = new TableRuleConfiguration(tableName, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, tableName));
        config.setTableShardingStrategyConfig(strategy == null ? getIdYearShardingStrategy() : strategy);
        return config;
    }

    /**
     * 获取默认分片策略 按年分
     *
     * @return 分片策略
     */
    private ShardingStrategyConfiguration getIdYearShardingStrategy() {
        return new StandardShardingStrategyConfiguration("id", new YearTablePreciseShardingAlgorithm(), new YearTableRangeShardingAlgorithm());
    }

    /**
     * 使用Hint进行虚拟的逻辑分片
     *
     * @param logicTableName 逻辑表名
     * @return 分片规则配置
     * @see StrParamHintShardingAlgorithm
     */
    private TableRuleConfiguration getHintShardingTableRuleConfiguration(String logicTableName) {
        TableRuleConfiguration tableRuleConfiguration = new TableRuleConfiguration(logicTableName, String.format(DATABASE_TABLE_PLACEHOLDER, DATABASE_NAME, logicTableName));
        tableRuleConfiguration.setTableShardingStrategyConfig(new HintShardingStrategyConfiguration(new StrParamHintShardingAlgorithm()));
        return tableRuleConfiguration;
    }

    /**
     * 配置事务
     *
     * @return 事务
     */
    @Bean(name = TRANSACTION_NAME)
    public DataSourceTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }

    /**
     * 创建SqlSessionFactory对象
     *
     * @param dataSource 数据源
     * @return SqlSessionFactory对象
     * @throws Exception 异常
     */
    @Bean(name = SESSION_FACTORY_NAME)
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Qualifier(SHARDING_DATA_SOURCE_NAME) DataSource dataSource) throws Exception {
        return super.getSessionFactory(dataSource, ALIAS_PACKAGES);
    }

    /**
     * 创建TransactionTemplate对象
     *
     * @param transactionManager 事务管理器
     * @return 事务模板
     */
    @Bean(name = TRANSACTION_TEMPLATE_NAME)
    @Primary
    public TransactionTemplate transactionTemplate(@Qualifier(TRANSACTION_NAME) DataSourceTransactionManager transactionManager) {
        return new TransactionTemplate(transactionManager);
    }

}
