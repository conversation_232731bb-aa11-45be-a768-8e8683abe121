package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComCurrentBondsBalanceDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondfinance.response.ComFinanceSheetResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.financialinstitution.FinancialIndicatorResponseDTO;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgSecuBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.SecuComYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.SecuComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.view.SecuComYieldSpreadViewDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SecuritySeniorityRankingEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.model.bo.MixYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.bo.SecuComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.SecuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.SecuCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.SecuComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.view.SecuComYieldSpreadDynamicView;
import com.innodealing.onshore.yieldspread.service.SecuComYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.internal.BondFinanceService;
import com.innodealing.onshore.yieldspread.service.internal.BondInfoService;
import com.innodealing.onshore.yieldspread.service.internal.FinancialInstitutionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.WORK_THREAD_NUM;

/**
 * 证券主体利差 Service
 *
 * <AUTHOR>
 **/
@Service
public class SecuComYieldSpreadServiceImpl extends AbstractComYieldSpreadService implements SecuComYieldSpreadService {

    private final ExecutorService executorService;

    protected SecuComYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("SecuComYieldSpreadServiceImpl-pool-").build());
    }

    @Resource
    private SecuComYieldSpreadDAO secuComYieldSpreadDAO;

    @Resource
    private PgSecuBondYieldSpreadDAO pgSecuBondYieldSpreadDAO;

    @Resource
    private BondFinanceService bondFinanceService;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private FinancialInstitutionService financialInstitutionService;

    @Resource
    private SecuComYieldSpreadViewDAO secuComYieldSpreadViewDAO;

    @Resource
    private SecuComYieldSpreadRedisDAO secuComYieldSpreadRedisDAO;

    @Override
    public Integer calcSecuComYieldSpreadsBySpreadDate(List<SecuComYieldSpreadDO> comYieldSpreadDOs, Date spreadDate) {
        if (CollectionUtils.isEmpty(comYieldSpreadDOs)) {
            return 0;
        }
        Set<Long> comUniCodes = comYieldSpreadDOs.stream().map(SecuComYieldSpreadDO::getComUniCode).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        CompletableFuture<Map<Long, ComCurrentBondsBalanceDTO>> submitComCurrentBondsBalanceDTO = of.submit(() ->
                bondInfoService.getComCurrentBondsBalanceDTOMap(comUniCodes));
        CompletableFuture<Map<Long, ComFinanceSheetResponseDTO>> submitComFinanceSheetResponseDTO = of.submit(() ->
                bondFinanceService.getComFinanceLatestYearReportMap(spreadDate, comUniCodes));
        CompletableFuture<Map<Long, FinancialIndicatorResponseDTO>> submitFinancialIndicatorDTO = of.submit(() ->
                financialInstitutionService.getFinancialIndicatorMap(comUniCodes, spreadDate));
        // 计算中位数(全部)
        CompletableFuture<Map<Long, PgSecuBondYieldSpreadGroupDO>> submitYieldSpread = of.submit(() ->
                pgSecuBondYieldSpreadDAO.getSecuBondYieldSpreadMap(comUniCodes, null, spreadDate));
        // 计算中位数(普通)
        CompletableFuture<Map<Long, PgSecuBondYieldSpreadGroupDO>> submitYieldSpreadSenior = of
                .submit(() -> pgSecuBondYieldSpreadDAO.getSecuBondYieldSpreadMap(comUniCodes,
                        SecuritySeniorityRankingEnum.SENIOR.getValue(), spreadDate));
        // 计算中位数(次级)
        CompletableFuture<Map<Long, PgSecuBondYieldSpreadGroupDO>> submitYieldSpreadSubordinated = of
                .submit(() -> pgSecuBondYieldSpreadDAO.getSecuBondYieldSpreadMap(comUniCodes,
                        SecuritySeniorityRankingEnum.SUBORDINATED.getValue(), spreadDate));
        // 计算中位数(永续)
        CompletableFuture<Map<Long, PgSecuBondYieldSpreadGroupDO>> submitYieldSpreadPerpetual = of
                .submit(() -> pgSecuBondYieldSpreadDAO.getSecuBondYieldSpreadMap(comUniCodes,
                        SecuritySeniorityRankingEnum.PERPETUAL.getValue(), spreadDate));
        of.doWorks(submitComCurrentBondsBalanceDTO, submitComFinanceSheetResponseDTO, submitFinancialIndicatorDTO, submitYieldSpread, submitYieldSpreadSenior,
                submitYieldSpreadSubordinated, submitYieldSpreadPerpetual);
        // 获取主体财报
        Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap = submitComFinanceSheetResponseDTO.join();
        //获取采集数据
        Map<Long, FinancialIndicatorResponseDTO> financialIndicatorResponseDTOMap = submitFinancialIndicatorDTO.join();
        // 获取估值中位数(全部、普通、次级、永续)
        Map<Long, PgSecuBondYieldSpreadGroupDO> yieldSpreadMap = submitYieldSpread.join();
        Map<Long, PgSecuBondYieldSpreadGroupDO> yieldSpreadSeniorMap = submitYieldSpreadSenior.join();
        Map<Long, PgSecuBondYieldSpreadGroupDO> yieldSpreadSubordinatedMap = submitYieldSpreadSubordinated.join();
        Map<Long, PgSecuBondYieldSpreadGroupDO> yieldSpreadPerpetualMap = submitYieldSpreadPerpetual.join();

        List<SecuComYieldSpreadDO> comYieldSpreadDOSaves = new ArrayList<>();
        for (SecuComYieldSpreadDO comYieldSpreadDO : comYieldSpreadDOs) {
            comYieldSpreadDO = fillFinanceColumn(comYieldSpreadDO, comFinanceSheetResponseDTOMap);
            comYieldSpreadDO = fillFinancialIndicatorColumn(comYieldSpreadDO, financialIndicatorResponseDTOMap);
            comYieldSpreadDO = fillComSpreadColumn(comYieldSpreadDO, yieldSpreadMap, yieldSpreadSeniorMap,
                    yieldSpreadSubordinatedMap, yieldSpreadPerpetualMap);
            comYieldSpreadDO.setDeleted(0);
            comYieldSpreadDOSaves.add(comYieldSpreadDO);
        }
        return secuComYieldSpreadDAO.saveSecuComYieldSpreadDOList(spreadDate, comYieldSpreadDOSaves);
    }

    @Override
    public List<SecuComYieldSpreadResDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        SecuCurveGenerateConditionReqDTO curveGenerateCondition = super.getCurveGenerateCondition(
                userid, request.getCurveId(), SecuCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), curveGenerateCondition.getComOrBondCondition())) {
            return Collections.emptyList();
        }
        boolean isToday = super.isToday(request.getSpreadDate());
        SecuYieldSearchParam param = super.buildComYieldSearchParam(request, curveGenerateCondition, SecuYieldSearchParam.class, SecuComYieldSpreadDO.class);
        List<SecuComYieldSpreadBO> secuComYieldSpreads = secuComYieldSpreadDAO.listComYieldSpreads(isToday, param);
        if (CollectionUtils.isEmpty(secuComYieldSpreads)) {
            return Collections.emptyList();
        }
        Set<Long> comUniCodes = secuComYieldSpreads.stream().map(SecuComYieldSpreadBO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return secuComYieldSpreads.stream().map(com -> {
            SecuComYieldSpreadResDTO response = BeanCopyUtils.copyProperties(com, SecuComYieldSpreadResDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRatingMappingStr(RatingUtils.getRating(response.getComExtRatingMapping()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getNetProfit()).ifPresent(response::setNetProfit);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request) {
        SecuCurveGenerateConditionReqDTO curveGenerateCondition = super.getCurveGenerateCondition(
                userid, request.getCurveId(), SecuCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), curveGenerateCondition.getComOrBondCondition())) {
            return 0L;
        }
        return super.getComCountFromRedis(request, SPREAD_SECU_COM_SPREAD_COUNT_KEY, () -> {
            SecuYieldSearchParam param = super.buildComYieldSearchParam(request, curveGenerateCondition, SecuYieldSearchParam.class, null);
            return secuComYieldSpreadDAO.countComYieldSpread(param);
        });
    }

    @Override
    public List<SecuComYieldSpreadResDTO> listComs(Date spreadDate, Set<Long> secuComs) {
        if (org.springframework.util.CollectionUtils.isEmpty(secuComs)) {
            return Collections.emptyList();
        }
        // 今天的话要加上变动数据
        List<SecuComYieldSpreadResDTO> secuResponseList;
        if (super.isToday(spreadDate)) {
            spreadDate = this.getMaxSpreadDate();
            List<SecuComYieldSpreadDynamicView> secuComYieldSpreads = secuComYieldSpreadViewDAO.listComYieldSpreads(spreadDate, secuComs);
            secuResponseList = BeanCopyUtils.copyList(secuComYieldSpreads, SecuComYieldSpreadResDTO.class);
        } else {
            List<SecuComYieldSpreadDO> secuComYieldSpreads = secuComYieldSpreadDAO.listComYieldSpreads(spreadDate, secuComs);
            secuResponseList = BeanCopyUtils.copyList(secuComYieldSpreads, SecuComYieldSpreadResDTO.class);
        }
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(secuComs);
        return secuResponseList.stream().map(com -> {
            SecuComYieldSpreadResDTO response = BeanCopyUtils.copyProperties(com, SecuComYieldSpreadResDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRatingMappingStr(RatingUtils.getRating(response.getComExtRatingMapping()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getNetProfit()).ifPresent(response::setNetProfit);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer securitySeniorityRanking, Date startDate, Date endDate) {
        return secuComYieldSpreadRedisDAO.listCurves(comUniCode, securitySeniorityRanking, startDate, endDate);
    }

    private SecuComYieldSpreadDO fillFinanceColumn(SecuComYieldSpreadDO secuComYieldSpreadDO,
                                                   Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap) {
        SecuComYieldSpreadDO result = BeanCopyUtils.copyProperties(secuComYieldSpreadDO, SecuComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comFinanceSheetResponseDTOMap)) {
            return result;
        }
        ComFinanceSheetResponseDTO comFinanceSheetResponseDTO = comFinanceSheetResponseDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(comFinanceSheetResponseDTO)) {
            result.setTotalAssets(comFinanceSheetResponseDTO.getTotalAssets());
            result.setNetProfit(comFinanceSheetResponseDTO.getNetProfit());
        }
        return result;
    }

    private SecuComYieldSpreadDO fillFinancialIndicatorColumn(SecuComYieldSpreadDO secuComYieldSpreadDO,
                                                              Map<Long, FinancialIndicatorResponseDTO> financialIndicatorResponseDTOMap) {
        SecuComYieldSpreadDO result = BeanCopyUtils.copyProperties(secuComYieldSpreadDO, SecuComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(financialIndicatorResponseDTOMap)) {
            return result;
        }
        FinancialIndicatorResponseDTO financialIndicatorResponseDTO = financialIndicatorResponseDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(financialIndicatorResponseDTO)) {
            result.setCapitalLeverageRatio(financialIndicatorResponseDTO.getCapitalLeverageRatio());
        }
        return result;
    }

    private SecuComYieldSpreadDO fillComSpreadColumn(SecuComYieldSpreadDO secuComYieldSpreadDO,
                                                     Map<Long, PgSecuBondYieldSpreadGroupDO> yieldSpreadMap,
                                                     Map<Long, PgSecuBondYieldSpreadGroupDO> yieldSpreadSeniorMap,
                                                     Map<Long, PgSecuBondYieldSpreadGroupDO> yieldSpreadSubordinatedMap,
                                                     Map<Long, PgSecuBondYieldSpreadGroupDO> yieldSpreadPerpetualMap) {
        SecuComYieldSpreadDO result = BeanCopyUtils.copyProperties(secuComYieldSpreadDO, SecuComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(result)) {
            return result;
        }
        PgSecuBondYieldSpreadGroupDO yieldSpread = yieldSpreadMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpread)) {
            result.setComCbYield(yieldSpread.getCbYield());
            result.setComCreditSpread(yieldSpread.getBondCreditSpread());
            result.setComExcessSpread(yieldSpread.getBondExcessSpread());
        }
        PgSecuBondYieldSpreadGroupDO yieldSpreadSenior = yieldSpreadSeniorMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadSenior)) {
            result.setComSeniorCbYield(yieldSpreadSenior.getCbYield());
            result.setComSeniorCreditSpread(yieldSpreadSenior.getBondCreditSpread());
            result.setComSeniorExcessSpread(yieldSpreadSenior.getBondExcessSpread());
        }
        PgSecuBondYieldSpreadGroupDO yieldSpreadSubordinated = yieldSpreadSubordinatedMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadSubordinated)) {
            result.setComSubordinatedCbYield(yieldSpreadSubordinated.getCbYield());
            result.setComSubordinatedCreditSpread(yieldSpreadSubordinated.getBondCreditSpread());
            result.setComSubordinatedExcessSpread(yieldSpreadSubordinated.getBondExcessSpread());
        }
        PgSecuBondYieldSpreadGroupDO yieldSpreadPerpetual = yieldSpreadPerpetualMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadPerpetual)) {
            result.setComPerpetualCbYield(yieldSpreadPerpetual.getCbYield());
            result.setComPerpetualCreditSpread(yieldSpreadPerpetual.getBondCreditSpread());
            result.setComPerpetualExcessSpread(yieldSpreadPerpetual.getBondExcessSpread());
        }
        return result;
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.SECURITY;
    }

    @Override
    protected Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_SECU_COM_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = secuComYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_SECU_COM_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }


    @Override
    protected List<MixYieldSpreadShortBO> listAllYieldSpreads(@NonNull List<Long> comUniCodes) {
        return secuComYieldSpreadDAO.listAllYieldSpreads(comUniCodes);
    }

}

