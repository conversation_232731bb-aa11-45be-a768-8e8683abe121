package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.BondCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.BondYieldSpreadDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.BondYieldSpreadListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondCreditYieldCurvesResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondYieldSpreadCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondYieldSpreadListResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.ComYieldSpreadCurveResponseDTO;
import org.springframework.lang.Nullable;

import java.sql.Date;
import java.util.List;
import java.util.Set;

/**
 * 单券利差服务
 *
 * <AUTHOR>
 */
public interface BondYieldSpreadService {

    /**
     * 查询单券信用利差
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 单券唯一编码集合
     * @return {@link List}<{@link ComCreditSpreadDTO}> 信用利差数据集
     */
    List<BondCreditSpreadDTO> listBondCreditSpreads(Date spreadDate, Set<Long> bondUniCodes);

    /**
     * 查询曲线列表数据
     *
     * @param bondUniCode 债券唯一编码
     * @param curveType   曲线类型
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return {@link List}{@link ComYieldSpreadCurveResponseDTO}
     * @see com.innodealing.onshore.yieldspread.enums.CurveTypeEnum
     * @see com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum
     */
    List<BondYieldSpreadCurveResponseDTO> listCurves(Long bondUniCode, Integer curveType, Date startDate, Date endDate);

    /**
     * 查询列表债券数据集合，包含产业债、城投债、银行债、证券债、保险债、自选债
     *
     * @param request 查询列表债券数据集合请求参数
     * @param userid  用户id
     * @return {@link BondYieldSpreadListResponseDTO}
     */
    BondYieldSpreadListResponseDTO listBonds(BondYieldSpreadListRequestDTO request, Long userid);

    /**
     * 根据日期和BondUniCode获取利差数据，不传日期默认是最近工作日
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券code
     * @return 债券利差
     */
    List<BondYieldSpreadDTO> batchYieldSpreadByBondUniCodes(@Nullable Date spreadDate, List<Long> bondUniCodes);

    /**
     * 根据BondUniCode获取利差曲线数据，不传日期默认查最近一年
     *
     * @param bondUniCodes 债券code
     * @param startDate    利差查询开始时间
     * @param endDate      利差查询结束时间
     * @return 利差数据
     */
    BondCreditYieldCurvesResDTO listCreditSpreadCurves(List<Long> bondUniCodes,
                                                       @Nullable Date startDate,
                                                       @Nullable Date endDate);

    /**
     * 计算债券利差
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes BondUniCodes
     * @return 处理条数
     */
    int calcBondYieldSpread(Date spreadDate, List<Long> bondUniCodes);

}
