package com.innodealing.onshore.yieldspread.mapper.dmdc;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.ComInterestCtzHistDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.group.ComInterestCtzHistGroupDO;

/**
 * 城投主体利差(老表)
 *
 * <AUTHOR>
 **/
public interface ComInterestCtzHistMapper extends SelectByGroupedQueryMapper<ComInterestCtzHistGroupDO, ComInterestCtzHistDO> {
}
