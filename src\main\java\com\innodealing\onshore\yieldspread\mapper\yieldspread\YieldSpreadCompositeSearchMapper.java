package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.innodealing.onshore.yieldspread.model.bo.CustomComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.CustomSingleBondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.CustomYieldSearchParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 利差综合查询
 *
 * <AUTHOR>
 */
@Mapper
public interface YieldSpreadCompositeSearchMapper {

    /**
     * 自定义曲线单券利差分页查询
     *
     * @param param 查询参数
     * @return 自定义曲线单券利差
     */
    List<CustomSingleBondYieldSpreadBO> listSingleBondYieldSpreads(@Param("params") CustomYieldSearchParam param);

    /**
     * 自定义曲线单券利差总数
     *
     * @param param 查询参数
     * @return 自定义曲线单券利差总数
     */
    Long countSingleBondYieldSpread(@Param("params") CustomYieldSearchParam param);

    /**
     * 分页查询自定义曲线主体利差
     *
     * @param param 查询参数
     * @return 主体利差
     */
    List<CustomComYieldSpreadBO> listComYieldSpreads(@Param("params") CustomYieldSearchParam param);

    /**
     * 定义曲线主体利差数量
     *
     * @param param 查询参数
     * @return 主体利差数量
     */
    Long countComYieldSpread(@Param("params") CustomYieldSearchParam param);

}
