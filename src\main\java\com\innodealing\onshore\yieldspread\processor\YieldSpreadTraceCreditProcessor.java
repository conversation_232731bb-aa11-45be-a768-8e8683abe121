package com.innodealing.onshore.yieldspread.processor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.enums.SecuritySeniorityRankingEnum;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgInduBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgSecuBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.enums.BondImpliedRatingCurveCodeMappingEnum;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveCodeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadCreditYieldGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadCreditYieldGroupDO;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.BondImpliedRatingCurveCodeMappingEnum.getInduBondCurveCodeByRating;
import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.*;

/**
 * 利差追踪-信用利差处理器
 *
 * <AUTHOR>
 */
@Component
public class YieldSpreadTraceCreditProcessor implements YieldSpreadTraceProcessor, InitializingBean {

    private static final Long INDU1_REAL_ESTATE_CODE = 430_000L;
    private static final Set<YieldPanoramaBondTypeEnum> SUPPORT_BOND_TYPES = EnumSet.of(INDUSTRIAL_BOND, URBAN_BOND, MEDIUM_AND_SHORT_TERMS_NOTE, GENERAL_BANK_BOND,
            BANK_SECONDARY_CAPITAL_BOND, BANK_PERPETUAL_BOND, SECURITIES_BOND, SECURITIES_SUB_BOND, SECURITIES_PERPETUAL_BOND, INSU_CAPITAL_SUPPLEMENT);

    private Map<YieldPanoramaBondTypeEnum, Function<YieldSpreadTraceContext, List<PgBondYieldSpreadTraceAbsDO>>> functionMap = Maps.newHashMap();

    @Resource
    private PgInduBondYieldSpreadDAO pgInduBondYieldSpreadDAO;
    @Resource
    private PgSecuBondYieldSpreadDAO pgSecuBondYieldSpreadDAO;

    @Override
    public void afterPropertiesSet() {
        functionMap.put(INDUSTRIAL_BOND, this::processCreditSpreadForInduBond);
        functionMap.put(SECURITIES_PERPETUAL_BOND, this::processCreditSpreadForSecuritiesPerpetualBond);
        functionMap.put(SECURITIES_SUB_BOND, this::processCreditSpreadForSecuritiesSubBond);
    }

    @Override
    public Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum) {
        return SUPPORT_BOND_TYPES.contains(traceBondTypeEnum);
    }

    @Override
    public List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context) {
        if (Objects.isNull(context.getChinaBondKaiYieldPanorama())) {
            return Collections.emptyList();
        }
        YieldPanoramaBondTypeEnum bondTypeEnum = context.getBondTypeEnum();
        if (functionMap.containsKey(bondTypeEnum)) {
            Function<YieldSpreadTraceContext, List<PgBondYieldSpreadTraceAbsDO>> function = functionMap.get(bondTypeEnum);
            return function.apply(context);
        }
        // 减去同期限国开
        List<PgBondYieldPanoramaAbsDO> absBondYieldPanoramas = context.getAbsBondYieldPanoramas();
        PgBondYieldPanoramaAbsDO chinaBondKai = context.getChinaBondKaiYieldPanorama();
        Map<Integer, PgBondYieldPanoramaAbsDO> absCurveCodeMap =
                absBondYieldPanoramas.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        List<PgBondYieldSpreadTraceAbsDO> dataList = Lists.newArrayList();
        for (Map.Entry<Integer, PgBondYieldPanoramaAbsDO> absEntry : absCurveCodeMap.entrySet()) {
            PgBondYieldPanoramaAbsDO abs = absEntry.getValue();
            PgBondYieldSpreadTraceAbsDO bondYieldSpreadTraceAbsDO = convertAbsSubtractToTrace(abs, chinaBondKai, bondTypeEnum,
                    context.getIssueDate(), absEntry.getKey(), YieldSpreadChartTypeEnum.CREDIT_SPREAD);
            dataList.add(bondYieldSpreadTraceAbsDO);
        }
        return dataList;
    }

    private List<PgBondYieldSpreadTraceAbsDO> processCreditSpreadForSecuritiesSubBond(YieldSpreadTraceContext context) {
        return this.doProcessCreditSpreadForSecuritiesBond(context, SecuritySeniorityRankingEnum.SUBORDINATED.getValue());
    }

    private List<PgBondYieldSpreadTraceAbsDO> processCreditSpreadForSecuritiesPerpetualBond(YieldSpreadTraceContext context) {
        return this.doProcessCreditSpreadForSecuritiesBond(context, SecuritySeniorityRankingEnum.PERPETUA.getValue());
    }

    private List<PgBondYieldSpreadTraceAbsDO> doProcessCreditSpreadForSecuritiesBond(YieldSpreadTraceContext context, Integer securitySeniorityRanking) {
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        List<PgBondYieldSpreadTraceAbsDO> dataList = Lists.newArrayList();
        YieldPanoramaBondTypeEnum bondTypeEnum = context.getBondTypeEnum();
        List<PgSecuBondYieldSpreadCreditYieldGroupDO> secuBondYieldSpreadCreditYieldGroupList =
                pgSecuBondYieldSpreadDAO.listYieldSpreadCreditYieldMedians(context.getIssueDate(), Collections.singletonList(securitySeniorityRanking));
        for (PgSecuBondYieldSpreadCreditYieldGroupDO creditYieldGroup : secuBondYieldSpreadCreditYieldGroupList) {
            PgBondYieldSpreadTraceAbsDO bondYieldTraceAbsDO = BeanCopyUtils.copyProperties(creditYieldGroup, PgBondYieldSpreadTraceAbsDO.class);
            bondYieldTraceAbsDO.setBondType(context.getBondTypeEnum().getValue());
            bondYieldTraceAbsDO.setChartType(YieldSpreadChartTypeEnum.CREDIT_SPREAD.getValue());
            Integer curveCode = SECURITIES_SUB_BOND.equals(bondTypeEnum) ? YieldSpreadCurveCodeEnum.SS_BONDS.getValue() : YieldSpreadCurveCodeEnum.SP_BONDS.getValue();
            bondYieldTraceAbsDO.setCurveCode(curveCode);
            bondYieldTraceAbsDO.setIssueDate(context.getIssueDate());
            bondYieldTraceAbsDO.setDeleted(Deleted.NO_DELETED.getValue());
            bondYieldTraceAbsDO.setCreateTime(now);
            dataList.add(bondYieldTraceAbsDO);
        }
        return dataList;
    }

    private List<PgBondYieldSpreadTraceAbsDO> processCreditSpreadForInduBond(YieldSpreadTraceContext context) {
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        List<PgBondYieldSpreadTraceAbsDO> dataList = Lists.newArrayList();
        List<PgInduBondYieldSpreadCreditYieldGroupDO> induBondYieldSpreadCreditYieldList = pgInduBondYieldSpreadDAO.listYieldSpreadCreditYieldMedians(context.getIssueDate(),
                BondImpliedRatingCurveCodeMappingEnum.getBondImpliedRating(), INDU1_REAL_ESTATE_CODE);
        for (PgInduBondYieldSpreadCreditYieldGroupDO creditYieldGroup : induBondYieldSpreadCreditYieldList) {
            PgBondYieldSpreadTraceAbsDO bondYieldTraceAbsDO = BeanCopyUtils.copyProperties(creditYieldGroup, PgBondYieldSpreadTraceAbsDO.class);
            bondYieldTraceAbsDO.setBondType(context.getBondTypeEnum().getValue());
            bondYieldTraceAbsDO.setChartType(YieldSpreadChartTypeEnum.CREDIT_SPREAD.getValue());
            bondYieldTraceAbsDO.setCurveCode(getInduBondCurveCodeByRating(creditYieldGroup.getBondImpliedRatingMapping()));
            bondYieldTraceAbsDO.setIssueDate(context.getIssueDate());
            bondYieldTraceAbsDO.setDeleted(Deleted.NO_DELETED.getValue());
            bondYieldTraceAbsDO.setCreateTime(now);
            dataList.add(bondYieldTraceAbsDO);
        }
        return dataList;
    }
}
