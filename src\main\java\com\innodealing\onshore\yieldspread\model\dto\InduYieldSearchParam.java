package com.innodealing.onshore.yieldspread.model.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 行业利差查询参数
 *
 * <AUTHOR>
 */
public class InduYieldSearchParam extends UniversalYieldSpreadSearchParam {

    /**
     * 一级行业编码
     */
    private Long industryCode1;

    /**
     * 二级行业编码
     */
    private Long industryCode2;

    /**
     * 担保状态: 0: 无, 1: 有
     */
    private Integer guaranteedStatus;

    /**
     * 债券外部评级  1:AAA,2:AA+,3:AA
     */
    private Integer bondExtRatingMapping;

    /**
     * 企业性质 1:央企, 2:国企, 3:民企
     */
    private List<Integer> businessFilterNatures;

    public Long getIndustryCode1() {
        return industryCode1;
    }

    public void setIndustryCode1(Long industryCode1) {
        this.industryCode1 = industryCode1;
    }

    public Long getIndustryCode2() {
        return industryCode2;
    }

    public void setIndustryCode2(Long industryCode2) {
        this.industryCode2 = industryCode2;
    }

    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public List<Integer> getBusinessFilterNatures() {
        return Objects.isNull(businessFilterNatures) ? new ArrayList<>() : new ArrayList<>(businessFilterNatures);
    }

    public void setBusinessFilterNatures(List<Integer> businessFilterNatures) {
        this.businessFilterNatures = Objects.isNull(businessFilterNatures) ? new ArrayList<>() : new ArrayList<>(businessFilterNatures);
    }

}
