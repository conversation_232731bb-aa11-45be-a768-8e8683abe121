package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * 银行主体主体利差
 *
 * <AUTHOR>
 **/
@Table(name = "bank_com_yield_spread")
public class BankComYieldSpreadDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;
    /**
     * 一级行业编码
     */
    @Column
    private Long induLevel1Code;
    /**
     * 一级行业名称
     */
    @Column
    private String induLevel1Name;
    /**
     * 二级行业编码
     */
    @Column
    private Long induLevel2Code;
    /**
     * 二级行业名称
     */
    @Column
    private String induLevel2Name;
    /**
     * 银行类型
     */
    @Column
    private Integer bankType;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 主体外部评级映射
     */
    @Column
    private Integer comExtRatingMapping;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    @Column
    private BigDecimal comCreditSpread;
    /**
     * 超额利差(全部债券);单位(BP)
     */
    @Column
    private BigDecimal comExcessSpread;
    /**
     * 信用利差(普通);单位(BP)
     */
    @Column
    private BigDecimal comSeniorCreditSpread;
    /**
     * 超额利差(普通);单位(BP)
     */
    @Column
    private BigDecimal comSeniorExcessSpread;
    /**
     * 信用利差(二级资本债);单位(BP)
     */
    @Column
    private BigDecimal comTier2CreditSpread;
    /**
     * 超额利差(二级资本债);单位(BP)
     */
    @Column
    private BigDecimal comTier2ExcessSpread;
    /**
     * 信用利差(永续);单位(BP)
     */
    @Column
    private BigDecimal comPerpetualCreditSpread;
    /**
     * 超额利差(永续);单位(BP)
     */
    @Column
    private BigDecimal comPerpetualExcessSpread;
    /**
     * 估值收益率(全部债券);单位(%)
     */
    @Column
    private BigDecimal comCbYield;
    /**
     * 估值收益率(普通);单位(%)
     */
    @Column
    private BigDecimal comSeniorCbYield;
    /**
     * 估值收益率(二级资本债);单位(%)
     */
    @Column
    private BigDecimal comTier2CbYield;
    /**
     * 估值收益率(永续);单位(%)
     */
    @Column
    private BigDecimal comPerpetualCbYield;
    /**
     * 总资产(万元)
     */
    @Column
    private BigDecimal totalAssets;
    /**
     * 净利润(万元)
     */
    @Column
    private BigDecimal netProfit;
    /**
     * 不良贷款率(%)
     */
    @Column
    private BigDecimal nplRatio;
    /**
     * 杠杆率(%)
     */
    @Column
    private BigDecimal leverageRatio;
    /**
     * 资本充足率(%)
     */
    @Column
    private BigDecimal car;
    /**
     * 贷款拨备率(%)
     */
    @Column
    private BigDecimal loanProvisRatio;
    /**
     * 是否删除：0： 未删除;1：已删除
     */
    @Column
    private Integer deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }

    public String getInduLevel1Name() {
        return induLevel1Name;
    }

    public void setInduLevel1Name(String induLevel1Name) {
        this.induLevel1Name = induLevel1Name;
    }

    public Long getInduLevel2Code() {
        return induLevel2Code;
    }

    public void setInduLevel2Code(Long induLevel2Code) {
        this.induLevel2Code = induLevel2Code;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }


    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    public Integer getBankType() {
        return bankType;
    }

    public void setBankType(Integer bankType) {
        this.bankType = bankType;
    }

    public BigDecimal getComSeniorCreditSpread() {
        return comSeniorCreditSpread;
    }

    public void setComSeniorCreditSpread(BigDecimal comSeniorCreditSpread) {
        this.comSeniorCreditSpread = comSeniorCreditSpread;
    }

    public BigDecimal getComSeniorExcessSpread() {
        return comSeniorExcessSpread;
    }

    public void setComSeniorExcessSpread(BigDecimal comSeniorExcessSpread) {
        this.comSeniorExcessSpread = comSeniorExcessSpread;
    }

    public BigDecimal getComTier2CreditSpread() {
        return comTier2CreditSpread;
    }

    public void setComTier2CreditSpread(BigDecimal comTier2CreditSpread) {
        this.comTier2CreditSpread = comTier2CreditSpread;
    }

    public BigDecimal getComTier2ExcessSpread() {
        return comTier2ExcessSpread;
    }

    public void setComTier2ExcessSpread(BigDecimal comTier2ExcessSpread) {
        this.comTier2ExcessSpread = comTier2ExcessSpread;
    }

    public BigDecimal getComSeniorCbYield() {
        return comSeniorCbYield;
    }

    public void setComSeniorCbYield(BigDecimal comSeniorCbYield) {
        this.comSeniorCbYield = comSeniorCbYield;
    }

    public BigDecimal getComTier2CbYield() {
        return comTier2CbYield;
    }

    public void setComTier2CbYield(BigDecimal comTier2CbYield) {
        this.comTier2CbYield = comTier2CbYield;
    }

    public BigDecimal getNplRatio() {
        return nplRatio;
    }

    public void setNplRatio(BigDecimal nplRatio) {
        this.nplRatio = nplRatio;
    }

    public BigDecimal getLeverageRatio() {
        return leverageRatio;
    }

    public void setLeverageRatio(BigDecimal leverageRatio) {
        this.leverageRatio = leverageRatio;
    }

    public BigDecimal getCar() {
        return car;
    }

    public void setCar(BigDecimal car) {
        this.car = car;
    }

    public BigDecimal getLoanProvisRatio() {
        return loanProvisRatio;
    }

    public void setLoanProvisRatio(BigDecimal loanProvisRatio) {
        this.loanProvisRatio = loanProvisRatio;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

}