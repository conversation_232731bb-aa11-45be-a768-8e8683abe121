package com.innodealing.onshore.yieldspread.model.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 走势复盘利差查询
 *
 * <AUTHOR>
 */
public class TrendReplayYieldSpreadRequestDTO {


    @ApiModelProperty(value = "利差日期", example = "2020-04-07", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    @NotNull(message = "开始日期不能为空")
    protected Date spreadStartDate;

    @ApiModelProperty(value = "利差日期", example = "2020-04-07", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    @NotNull(message = "结束日期不能为空")
    protected Date spreadEndDate;

    @ApiModelProperty(value = "主体利差筛选")
    @Size(max = 4, message = "列表最多支持4个")
    private List<TrendReplayComYieldSpreadRequestDTO> comYieldSpreadRequestDTOList;

    public Date getSpreadStartDate() {
        return spreadStartDate;
    }

    public void setSpreadStartDate(Date spreadStartDate) {
        this.spreadStartDate = spreadStartDate;
    }

    public Date getSpreadEndDate() {
        return spreadEndDate;
    }

    public void setSpreadEndDate(Date spreadEndDate) {
        this.spreadEndDate = spreadEndDate;
    }

    public List<TrendReplayComYieldSpreadRequestDTO> getComYieldSpreadRequestDTOList() {
        return Objects.isNull(comYieldSpreadRequestDTOList) ? new ArrayList<>()
                : new ArrayList<>(comYieldSpreadRequestDTOList);
    }

    public void setComYieldSpreadRequestDTOList(
            List<TrendReplayComYieldSpreadRequestDTO> comYieldSpreadRequestDTOList) {
        this.comYieldSpreadRequestDTOList = Objects.isNull(comYieldSpreadRequestDTOList) ? new ArrayList<>()
                : new ArrayList<>(comYieldSpreadRequestDTOList);
    }
}