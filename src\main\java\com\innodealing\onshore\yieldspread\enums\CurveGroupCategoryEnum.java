package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 曲线组类别
 *
 * <AUTHOR>
 */
public enum CurveGroupCategoryEnum implements ITextValueEnum {

    /**
     * 我的曲线组
     */
    MY_GROUP(1, "我的曲线组"),
    BENCHMARK_GROUP(2, "基准曲线组");

    CurveGroupCategoryEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    private final int value;

    private final String text;

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }

}
