package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgUdicBondYieldSpreadCurveProvinceDO;


/**
 * pg城投债利差曲线-省Mapper
 *
 * <AUTHOR>
 **/
public interface PgUdicBondYieldSpreadCurveProvinceMapper extends DynamicQueryMapper<PgUdicBondYieldSpreadCurveProvinceDO> {

    /**
     * 从物化视图刷新城投利差曲线数据
     */
    void refreshUdicBondYieldSpreadCurveFromMV();

    /**
     * 从物化视图中同步昨日利差曲线数据
     */
    void syncCurveIncrFromMV();
}
