package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.model.dto.UserSpreadConfigDTO;

import java.util.List;

/**
 * 用户利差展示设置表Service层
 *
 * <AUTHOR>
 */
public interface UserSpreadConfigService {
    /**
     * 保存用户利差配置
     *
     * @param userId              用户id
     * @param userSpreadConfigDTO 用户利差配置信息
     */
    void saveUserSpreadConfig(Long userId, List<UserSpreadConfigDTO> userSpreadConfigDTO);

    /**
     * 根据配置id获取用户利差配置列表
     *
     * @param userId    用户id
     * @param configIds 用户利差配置
     * @return 用户利差配置列表 {@link UserSpreadConfigDTO}
     */
    List<UserSpreadConfigDTO> listUserSpreadConfigByConfigIds(Long userId, List<Long> configIds);

    /**
     * 用户利差配置列表
     *
     * @param configIds 配置id
     * @return 配置列表 {@link UserSpreadConfigDTO}
     */
    List<UserSpreadConfigDTO> listDefaultSpreadConfigByConfigIds(List<Long> configIds);
}
