package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 地方债 全国利差视图 DO
 *
 * <AUTHOR>
 * @create: 2024-11-04
 */
@Table(name = "v_lg_bond_yield_spread_all")
public class PgLgBondYieldSpreadAllViewDO {

    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;

    /**
     * 地方债类型： 1 一般债; 2 专项债;  99 其他
     */
    @Column
    private Integer lgBondType;

    /**
     * 提前还本状态 0: 不提前还本 1: 提前还本
     */
    @Column
    private Integer prepaymentStatus;

    /**
     * 资金用途性质: 1 新增; 2 再融资; 3 置换;  99 其他
     */
    @Column
    private Integer fundUseType;

    @Column
    private Integer usingLgBondType;

    @Column
    private Integer usingPrepaymentStatus;

    @Column
    private Integer usingFundUseType;

    /**
     * 1月到期利差
     */
    @Column(name = "credit_spread_1m")
    private BigDecimal creditSpread1m;

    /**
     * 1月到期收益率
     */
    @Column(name = "cb_yield_1m")
    private BigDecimal cbYield1m;

    /**
     * 3月到期利差
     */
    @Column(name = "credit_spread_3m")
    private BigDecimal creditSpread3m;

    /**
     * 3月到期收益率
     */
    @Column(name = "cb_yield_3m")
    private BigDecimal cbYield3m;

    /**
     * 6月到期利差
     */
    @Column(name = "credit_spread_6m")
    private BigDecimal creditSpread6m;

    /**
     * 6月到期收益率
     */
    @Column(name = "cb_yield_6m")
    private BigDecimal cbYield6m;

    /**
     * 9月到期利差
     */
    @Column(name = "credit_spread_9m")
    private BigDecimal creditSpread9m;

    /**
     * 9月到期收益率
     */
    @Column(name = "cb_yield_9m")
    private BigDecimal cbYield9m;

    /**
     * 1Y到期利差
     */
    @Column(name = "credit_spread_1y")
    private BigDecimal creditSpread1y;

    /**
     * 1Y到期收益率
     */
    @Column(name = "cb_yield_1y")
    private BigDecimal cbYield1y;

    /**
     * 2Y到期利差
     */
    @Column(name = "credit_spread_2y")
    private BigDecimal creditSpread2y;

    /**
     * 2Y到期收益率
     */
    @Column(name = "cb_yield_2y")
    private BigDecimal cbYield2y;

    /**
     * 3Y到期利差
     */
    @Column(name = "credit_spread_3y")
    private BigDecimal creditSpread3y;

    /**
     * 3Y到期收益率
     */
    @Column(name = "cb_yield_3y")
    private BigDecimal cbYield3y;

    /**
     * 5Y到期利差
     */
    @Column(name = "credit_spread_5y")
    private BigDecimal creditSpread5y;

    /**
     * 5Y到期收益率
     */
    @Column(name = "cb_yield_5y")
    private BigDecimal cbYield5y;

    /**
     * 7Y到期利差
     */
    @Column(name = "credit_spread_7y")
    private BigDecimal creditSpread7y;

    /**
     * 7Y到期收益率
     */
    @Column(name = "cb_yield_7y")
    private BigDecimal cbYield7y;

    /**
     * 10Y到期利差
     */
    @Column(name = "credit_spread_10y")
    private BigDecimal creditSpread10y;

    /**
     * 10Y到期收益率
     */
    @Column(name = "cb_yield_10y")
    private BigDecimal cbYield10y;

    /**
     * 15Y到期利差
     */
    @Column(name = "credit_spread_15y")
    private BigDecimal creditSpread15y;

    /**
     * 15Y到期收益率
     */
    @Column(name = "cb_yield_15y")
    private BigDecimal cbYield15y;

    /**
     * 20Y到期利差
     */
    @Column(name = "credit_spread_20y")
    private BigDecimal creditSpread20y;

    /**
     * 20Y到期收益率
     */
    @Column(name = "cb_yield_20y")
    private BigDecimal cbYield20y;

    /**
     * 30Y到期利差
     */
    @Column(name = "credit_spread_30y")
    private BigDecimal creditSpread30y;

    /**
     * 30Y到期收益率
     */
    @Column(name = "cb_yield_30y")
    private BigDecimal cbYield30y;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Integer getLgBondType() {
        return lgBondType;
    }

    public void setLgBondType(Integer lgBondType) {
        this.lgBondType = lgBondType;
    }

    public Integer getPrepaymentStatus() {
        return prepaymentStatus;
    }

    public void setPrepaymentStatus(Integer prepaymentStatus) {
        this.prepaymentStatus = prepaymentStatus;
    }

    public Integer getFundUseType() {
        return fundUseType;
    }

    public void setFundUseType(Integer fundUseType) {
        this.fundUseType = fundUseType;
    }

    public BigDecimal getCreditSpread1m() {
        return creditSpread1m;
    }

    public void setCreditSpread1m(BigDecimal creditSpread1m) {
        this.creditSpread1m = creditSpread1m;
    }

    public BigDecimal getCbYield1m() {
        return cbYield1m;
    }

    public void setCbYield1m(BigDecimal cbYield1m) {
        this.cbYield1m = cbYield1m;
    }

    public BigDecimal getCreditSpread3m() {
        return creditSpread3m;
    }

    public void setCreditSpread3m(BigDecimal creditSpread3m) {
        this.creditSpread3m = creditSpread3m;
    }

    public BigDecimal getCbYield3m() {
        return cbYield3m;
    }

    public void setCbYield3m(BigDecimal cbYield3m) {
        this.cbYield3m = cbYield3m;
    }

    public BigDecimal getCreditSpread6m() {
        return creditSpread6m;
    }

    public void setCreditSpread6m(BigDecimal creditSpread6m) {
        this.creditSpread6m = creditSpread6m;
    }

    public BigDecimal getCbYield6m() {
        return cbYield6m;
    }

    public void setCbYield6m(BigDecimal cbYield6m) {
        this.cbYield6m = cbYield6m;
    }

    public BigDecimal getCreditSpread9m() {
        return creditSpread9m;
    }

    public void setCreditSpread9m(BigDecimal creditSpread9m) {
        this.creditSpread9m = creditSpread9m;
    }

    public BigDecimal getCbYield9m() {
        return cbYield9m;
    }

    public void setCbYield9m(BigDecimal cbYield9m) {
        this.cbYield9m = cbYield9m;
    }

    public BigDecimal getCreditSpread1y() {
        return creditSpread1y;
    }

    public void setCreditSpread1y(BigDecimal creditSpread1y) {
        this.creditSpread1y = creditSpread1y;
    }

    public BigDecimal getCbYield1y() {
        return cbYield1y;
    }

    public void setCbYield1y(BigDecimal cbYield1y) {
        this.cbYield1y = cbYield1y;
    }

    public BigDecimal getCreditSpread2y() {
        return creditSpread2y;
    }

    public void setCreditSpread2y(BigDecimal creditSpread2y) {
        this.creditSpread2y = creditSpread2y;
    }

    public BigDecimal getCbYield2y() {
        return cbYield2y;
    }

    public void setCbYield2y(BigDecimal cbYield2y) {
        this.cbYield2y = cbYield2y;
    }

    public BigDecimal getCreditSpread3y() {
        return creditSpread3y;
    }

    public void setCreditSpread3y(BigDecimal creditSpread3y) {
        this.creditSpread3y = creditSpread3y;
    }

    public BigDecimal getCbYield3y() {
        return cbYield3y;
    }

    public void setCbYield3y(BigDecimal cbYield3y) {
        this.cbYield3y = cbYield3y;
    }

    public BigDecimal getCreditSpread5y() {
        return creditSpread5y;
    }

    public void setCreditSpread5y(BigDecimal creditSpread5y) {
        this.creditSpread5y = creditSpread5y;
    }

    public BigDecimal getCbYield5y() {
        return cbYield5y;
    }

    public void setCbYield5y(BigDecimal cbYield5y) {
        this.cbYield5y = cbYield5y;
    }

    public BigDecimal getCreditSpread7y() {
        return creditSpread7y;
    }

    public void setCreditSpread7y(BigDecimal creditSpread7y) {
        this.creditSpread7y = creditSpread7y;
    }

    public BigDecimal getCbYield7y() {
        return cbYield7y;
    }

    public void setCbYield7y(BigDecimal cbYield7y) {
        this.cbYield7y = cbYield7y;
    }

    public BigDecimal getCreditSpread10y() {
        return creditSpread10y;
    }

    public void setCreditSpread10y(BigDecimal creditSpread10y) {
        this.creditSpread10y = creditSpread10y;
    }

    public BigDecimal getCbYield10y() {
        return cbYield10y;
    }

    public void setCbYield10y(BigDecimal cbYield10y) {
        this.cbYield10y = cbYield10y;
    }

    public BigDecimal getCreditSpread15y() {
        return creditSpread15y;
    }

    public void setCreditSpread15y(BigDecimal creditSpread15y) {
        this.creditSpread15y = creditSpread15y;
    }

    public BigDecimal getCbYield15y() {
        return cbYield15y;
    }

    public void setCbYield15y(BigDecimal cbYield15y) {
        this.cbYield15y = cbYield15y;
    }

    public BigDecimal getCreditSpread20y() {
        return creditSpread20y;
    }

    public void setCreditSpread20y(BigDecimal creditSpread20y) {
        this.creditSpread20y = creditSpread20y;
    }

    public BigDecimal getCbYield20y() {
        return cbYield20y;
    }

    public void setCbYield20y(BigDecimal cbYield20y) {
        this.cbYield20y = cbYield20y;
    }

    public BigDecimal getCreditSpread30y() {
        return creditSpread30y;
    }

    public void setCreditSpread30y(BigDecimal creditSpread30y) {
        this.creditSpread30y = creditSpread30y;
    }

    public BigDecimal getCbYield30y() {
        return cbYield30y;
    }

    public void setCbYield30y(BigDecimal cbYield30y) {
        this.cbYield30y = cbYield30y;
    }

    public Integer getUsingLgBondType() {
        return usingLgBondType;
    }

    public void setUsingLgBondType(Integer usingLgBondType) {
        this.usingLgBondType = usingLgBondType;
    }

    public Integer getUsingPrepaymentStatus() {
        return usingPrepaymentStatus;
    }

    public void setUsingPrepaymentStatus(Integer usingPrepaymentStatus) {
        this.usingPrepaymentStatus = usingPrepaymentStatus;
    }

    public Integer getUsingFundUseType() {
        return usingFundUseType;
    }

    public void setUsingFundUseType(Integer usingFundUseType) {
        this.usingFundUseType = usingFundUseType;
    }
}
