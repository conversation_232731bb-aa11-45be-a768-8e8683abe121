package com.innodealing.onshore.yieldspread.model.entity.yieldspread.view;

/**
 * 主体利差分位 数值 do
 *
 * <AUTHOR>
 **/
public class ComYieldSpreadQuantileViewDO {
    /**
     * 发行人代码
     */
    private Long comUniCode;
    /**
     * 信用利差(全部债券) 小于的数量
     */
    private Integer comCreditSpreadLessIssueCount;

    /**
     * 信用利差(全部债券) 区间范围内的总数量
     */
    private Integer comCreditSpreadCount;

    /**
     * 超额利差(全部债券) 小于的数量
     */
    private Integer comExcessSpreadLessIssueCount;

    /**
     * 超额利差(全部债券) 区间范围内的总数量
     */
    private Integer comExcessSpreadCount;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getComCreditSpreadLessIssueCount() {
        return comCreditSpreadLessIssueCount;
    }

    public void setComCreditSpreadLessIssueCount(Integer comCreditSpreadLessIssueCount) {
        this.comCreditSpreadLessIssueCount = comCreditSpreadLessIssueCount;
    }

    public Integer getComCreditSpreadCount() {
        return comCreditSpreadCount;
    }

    public void setComCreditSpreadCount(Integer comCreditSpreadCount) {
        this.comCreditSpreadCount = comCreditSpreadCount;
    }

    public Integer getComExcessSpreadLessIssueCount() {
        return comExcessSpreadLessIssueCount;
    }

    public void setComExcessSpreadLessIssueCount(Integer comExcessSpreadLessIssueCount) {
        this.comExcessSpreadLessIssueCount = comExcessSpreadLessIssueCount;
    }

    public Integer getComExcessSpreadCount() {
        return comExcessSpreadCount;
    }

    public void setComExcessSpreadCount(Integer comExcessSpreadCount) {
        this.comExcessSpreadCount = comExcessSpreadCount;
    }
}