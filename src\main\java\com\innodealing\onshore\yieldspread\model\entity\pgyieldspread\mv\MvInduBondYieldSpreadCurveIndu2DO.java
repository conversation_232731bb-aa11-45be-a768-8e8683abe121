package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 行业债利差曲线物化视图(包含行业2)
 *
 * <AUTHOR>
 */
@Table(name = "mv_indu_bond_yield_spread_curve_indu2")
public class MvInduBondYieldSpreadCurveIndu2DO extends BaseMvInduBondYieldSpreadCurveDO {
    /**
     * 二级行业编码
     */
    @Column
    private Long induLevel2Code;

    public Long getInduLevel2Code() {
        return induLevel2Code;
    }

    public void setInduLevel2Code(Long induLevel2Code) {
        this.induLevel2Code = induLevel2Code;
    }
}