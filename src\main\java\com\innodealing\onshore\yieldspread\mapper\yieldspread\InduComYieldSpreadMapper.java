package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadQueryParameter;
import com.innodealing.onshore.yieldspread.model.dto.InduYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import com.innodealing.onshore.yieldspread.model.view.InduComYieldSpreadView;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.Collection;
import java.util.List;

/**
 * 产业主体利差Mapper
 *
 * <AUTHOR>
 **/
public interface InduComYieldSpreadMapper extends DynamicQueryMapper<InduComYieldSpreadDO> {

    /**
     * 分页查询主体利差数据
     *
     * @param comUniCodes   发行人唯一编码
     * @param spreadDate    利差日期
     * @param startIndex    开始索引
     * @param pageSize      页面大小
     * @param sortProperty  排序字段
     * @param sortDirection 排序方向
     * @return {@link List}<{@link InduComYieldSpreadDO}> 响应数据
     */
    List<InduComYieldSpreadDO> getComYieldSpreadPaging(@Param("comUniCodes") Collection<Long> comUniCodes, @Param("spreadDate") Date spreadDate,
                                                       @Param("startIndex") Integer startIndex, @Param("pageSize") Integer pageSize, @Param("sortProperty") String sortProperty,
                                                       @Param("sortDirection") String sortDirection);

    /**
     * 分页查询主体利差
     *
     * @param parameter 分页查询主体利差请求参数
     * @return {@link List}<{@link InduComYieldSpreadDO}> 分页查询主体利差响应数据
     */
    List<InduComYieldSpreadDO> getComYieldSpreadPagingByJoin(@Param("parameter") InduBondYieldSpreadQueryParameter parameter);

    /**
     * 分页查询主体利差-包含变动数据
     *
     * @param parameter 分页查询主体利差请求参数
     * @return {@link List}<{@link InduComYieldSpreadView}> 分页查询主体利差响应数据
     */
    List<InduComYieldSpreadView> getComYieldSpreadChangePagingByJoin(@Param("parameter") InduBondYieldSpreadQueryParameter parameter);

    /**
     * 分页查询主体利差总条数
     *
     * @param parameter 分页查询主体利差总条数请求参数
     * @return {@link Long} 总条数
     */
    Long getComYieldSpreadPagingCount(@Param("parameter") InduBondYieldSpreadQueryParameter parameter);

    /**
     * 分页查询主体利差
     *
     * @param parameter 分页查询主体利差请求参数
     * @return {@link List}<{@link InduComYieldSpreadDO}> 分页查询主体利差响应数据
     */
    List<InduComYieldSpreadDO> getComYieldSpreadPagingByExists(@Param("parameter") InduBondYieldSpreadQueryParameter parameter);

    /**
     * 分页查询主体利差-包含变动数据
     *
     * @param parameter 分页查询主体利差请求参数
     * @return {@link List}<{@link InduComYieldSpreadView}> 分页查询主体利差响应数据
     */
    List<InduComYieldSpreadView> getComYieldSpreadChangePagingByExists(@Param("parameter") InduBondYieldSpreadQueryParameter parameter);

    /**
     * 获取债券主体利差
     *
     * @param isNewest  是否最新数据
     * @param parameter 查询参数
     * @return 主体利差
     */
    List<InduComYieldSpreadView> listComYieldSpreads(@Param("isNewest") boolean isNewest, @Param("parameter") InduYieldSearchParam parameter);

    /**
     * 获取主体数量
     *
     * @param param 查询参数
     * @return 主体数量
     */
    Long countComYieldSpread(@Param("parameter") InduYieldSearchParam param);

    /**
     * 获取指定时间范围内的某个发行时间 对应的分位统计的数据
     *
     * @param startDate      时间范围开始
     * @param endDate        时间范围结束
     * @param issueDate      发行时间
     * @param comUniCodeList 指定主体列表
     * @return 分位 信用利差(全部) 超额利差(全部)统计的数据
     */
    List<ComYieldSpreadQuantileViewDO> listComYieldQuantileStatisticsViews(@Param("startDate") Date startDate,
                                                                           @Param("endDate") Date endDate,
                                                                           @Param("issueDate") Date issueDate,
                                                                           @Param("comUniCodeList") List<Long> comUniCodeList);

}
