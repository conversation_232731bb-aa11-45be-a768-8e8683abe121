package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.QueryHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.InsuBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.InsuBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.dto.InsuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.InsuBondYieldSpreadGroupDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 保险债利差 DAO
 *
 * <AUTHOR>
 * @date 2024/4/10 18:51
 **/
@Repository
public class InsuBondYieldSpreadDAO {

    @Resource
    private InsuBondYieldSpreadMapper insuBondYieldSpreadMapper;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private InsuBondYieldSpreadGroupMapper insuBondYieldSpreadGroupMapper;

    private final QueryHelper queryHelper = new QueryHelper();

    /**
     * 创建mysql分片表
     *
     * @param tableName 表名
     */
    public void createShardingTable(String tableName) {
        insuBondYieldSpreadMapper.createShardingTable(tableName);
    }

    /**
     * 批量保存mysql insu_bond_yield_spread 相应分片表数据
     *
     * @param startDate                 开始日期
     * @param endDate                   结束日期
     * @param insuBondYieldSpreadDOList 证券债利差列表
     * @return 受影响的行数
     */
    public int saveInsuBondYieldSpreadDOList(Date startDate, Date endDate, List<InsuBondYieldSpreadDO> insuBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(insuBondYieldSpreadDOList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        long startPk = ShardingUtils.getMinPkOfDate(startDate);
        long endPk = ShardingUtils.getMaxPkOfDate(endDate);
        Set<Long> bondUniCodes = insuBondYieldSpreadDOList.stream().map(InsuBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<InsuBondYieldSpreadDO> query = DynamicQuery.createQuery(InsuBondYieldSpreadDO.class)
                .select(InsuBondYieldSpreadDO::getId, InsuBondYieldSpreadDO::getBondUniCode,
                        InsuBondYieldSpreadDO::getSpreadDate, InsuBondYieldSpreadDO::getInduLevel1Code,
                        InsuBondYieldSpreadDO::getInduLevel1Name, InsuBondYieldSpreadDO::getInduLevel2Code,
                        InsuBondYieldSpreadDO::getInduLevel2Name, InsuBondYieldSpreadDO::getBusinessNature)
                .and(InsuBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(InsuBondYieldSpreadDO::getSpreadDate, isEqual(startDate))
                .and(InsuBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<InsuBondYieldSpreadDO> existDataList = insuBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(insuBondYieldSpreadDOList));
        } else {
            Map<String, InsuBondYieldSpreadDO> existInsuBondYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<InsuBondYieldSpreadDO> insertList = new ArrayList<>();
            List<InsuBondYieldSpreadDO> updateList = new ArrayList<>();
            for (InsuBondYieldSpreadDO insuBondYieldSpreadDO : insuBondYieldSpreadDOList) {
                InsuBondYieldSpreadDO existInsuBondYieldSpreadDO = existInsuBondYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, insuBondYieldSpreadDO.getBondUniCode(),
                                insuBondYieldSpreadDO.getSpreadDate().getTime()));
                if (isNull(existInsuBondYieldSpreadDO)) {
                    insertList.add(insuBondYieldSpreadDO);
                } else {
                    insuBondYieldSpreadDO.setId(existInsuBondYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existInsuBondYieldSpreadDO.getInduLevel1Code(),
                            insuBondYieldSpreadDO::setInduLevel1Code);
                    ObjectExtensionUtils.ifNonNull(existInsuBondYieldSpreadDO.getInduLevel1Name(),
                            insuBondYieldSpreadDO::setInduLevel1Name);
                    ObjectExtensionUtils.ifNonNull(existInsuBondYieldSpreadDO.getInduLevel2Code(),
                            insuBondYieldSpreadDO::setInduLevel2Code);
                    ObjectExtensionUtils.ifNonNull(existInsuBondYieldSpreadDO.getInduLevel2Name(),
                            insuBondYieldSpreadDO::setInduLevel2Name);
                    ObjectExtensionUtils.ifNonNull(existInsuBondYieldSpreadDO.getBusinessNature(),
                            insuBondYieldSpreadDO::setBusinessNature);
                    updateList.add(insuBondYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 直接批量更新数据库
     *
     * @param updateList 证券债利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<InsuBondYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<InsuBondYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(InsuBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (InsuBondYieldSpreadDO insuBondYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<InsuBondYieldSpreadDO> updateQuery = DynamicQuery.createQuery(InsuBondYieldSpreadDO.class)
                        .and(InsuBondYieldSpreadDO::getId, isEqual(insuBondYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(insuBondYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 直接批量插入数据库
     *
     * @param insertList 证券债利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<InsuBondYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<InsuBondYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(InsuBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (InsuBondYieldSpreadDO insuBondYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(insuBondYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 根据主体分组获取保险债利差
     *
     * @param spreadDate 开始日期
     * @return 证券债利差
     */
    public List<InsuBondYieldSpreadGroupDO> listInsuBondYieldSpreadGroupDOs(Date spreadDate) {
        long startPk = ShardingUtils.getMinPkOfDate(spreadDate);
        long endPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        GroupedQuery<InsuBondYieldSpreadDO, InsuBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(InsuBondYieldSpreadDO.class, InsuBondYieldSpreadGroupDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER)
                        .select(InsuBondYieldSpreadGroupDO::getComUniCode,
                                InsuBondYieldSpreadGroupDO::getInduLevel1Code, InsuBondYieldSpreadGroupDO::getInduLevel1Name,
                                InsuBondYieldSpreadGroupDO::getInduLevel2Code,
                                InsuBondYieldSpreadGroupDO::getInduLevel2Name, InsuBondYieldSpreadGroupDO::getBusinessNature,
                                InsuBondYieldSpreadGroupDO::getComExtRatingMapping, InsuBondYieldSpreadGroupDO::getSpreadDate)
                        .and(InsuBondYieldSpreadDO::getId, between(startPk, endPk))
                        .and(InsuBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(g -> g.and(InsuBondYieldSpreadDO::getBondCreditSpread, notEqual(null))
                                .or(InsuBondYieldSpreadDO::getBondExcessSpread, notEqual(null)))
                        .groupBy(InsuBondYieldSpreadDO::getComUniCode);
        return insuBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 查询保险单券利差
     *
     * @param param 查询参数
     * @return 单券利差
     */
    public NormPagingResult<InsuBondYieldSpreadDO> listInsuSingleBondYieldSpreads(InsuYieldSearchParam param) {
        NormPagingQuery<InsuBondYieldSpreadDO> query = NormPagingQuery
                .createQuery(InsuBondYieldSpreadDO.class, param.getPageNum(), param.getPageSize())
                .and(this.listCommonFilters(param));
        if (Objects.nonNull(param.getSort())) {
            SortDTO sort = param.getSort();
            String columnName = queryHelper.getQueryColumnByProperty(BankBondYieldSpreadDO.class, sort.getPropertyName());
            query.addSorts(new CustomSortDescriptor(columnName + " IS NULL"));
            query.addSorts(new SortDescriptor(sort.getPropertyName(), sort.getSortDirection()));
        }
        return insuBondYieldSpreadMapper.selectByNormalPaging(query);
    }

    private BaseFilterDescriptor<InsuBondYieldSpreadDO>[] listCommonFilters(InsuYieldSearchParam param) {
        long startPk = ShardingUtils.getMinPkOfDate(param.getSpreadDate());
        long endPk = ShardingUtils.getMaxPkOfDate(param.getSpreadDate());
        FilterGroupDescriptor<InsuBondYieldSpreadDO> filterGroup = FilterGroupDescriptor.create(InsuBondYieldSpreadDO.class)
                .and(InsuBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(InsuBondYieldSpreadDO::getSpreadDate, isEqual(param.getSpreadDate()))
                .and(nonNull(param.getSpreadBondType()), InsuBondYieldSpreadDO::getInsuranceSeniorityRanking, isEqual(param.getSpreadBondType()))
                .and(CollectionUtils.isNotEmpty(param.getBusinessFilterNatures()), InsuBondYieldSpreadDO::getBusinessFilterNature, in(param.getBusinessFilterNatures()))
                .and(nonNull(param.getRemainingTenor()), InsuBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(param.getRemainingTenor()))
                .and(nonNull(param.getComUniCode()), InsuBondYieldSpreadDO::getComUniCode, isEqual(param.getComUniCode()))
                .and(nonNull(param.getBondUniCode()), InsuBondYieldSpreadDO::getBondUniCode, isEqual(param.getBondUniCode()))
                .and(isNotEmpty(param.getBondImpliedRatingMappings()), InsuBondYieldSpreadDO::getBondImpliedRatingMapping,
                        in(param.getBondImpliedRatingMappings()))
                .and(InsuBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return filterGroup.getFilters();
    }

    /**
     * 查询保险单券利差数据集
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码
     * @return {@link List}<{@link InsuBondYieldSpreadDO}>
     */
    public List<InsuBondYieldSpreadDO> listInsuBondYieldSpreads(Date spreadDate, Set<Long> bondUniCodes) {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPkOfDate = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<InsuBondYieldSpreadDO> query = DynamicQuery.createQuery(InsuBondYieldSpreadDO.class)
                .and(InsuBondYieldSpreadDO::getId, between(minPkOfDate, maxPkOfDate))
                .and(InsuBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        return insuBondYieldSpreadMapper.selectByDynamicQuery(query);
    }

    /**
     * 获取保险
     *
     * @return 保险
     */
    public List<InsuBondYieldSpreadGroupDO> listBonds() {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(YieldSpreadConst.CURVE_START_DATE);
        Long manPkOfDate = ShardingUtils.getMaxPkOfDate(Date.valueOf(LocalDate.now()));
        GroupedQuery<InsuBondYieldSpreadDO, InsuBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(InsuBondYieldSpreadDO.class, InsuBondYieldSpreadGroupDO.class)
                        .select(InsuBondYieldSpreadGroupDO::getComUniCode,
                                InsuBondYieldSpreadGroupDO::getBondUniCode,
                                InsuBondYieldSpreadGroupDO::getBondCode)
                        .and(InsuBondYieldSpreadDO::getId, between(minPkOfDate, manPkOfDate))
                        .and(InsuBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                        .groupBy(InsuBondYieldSpreadDO::getBondUniCode);
        return insuBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }
}
