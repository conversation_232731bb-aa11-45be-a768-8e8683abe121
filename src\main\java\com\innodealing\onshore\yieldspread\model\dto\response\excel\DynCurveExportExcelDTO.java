package com.innodealing.onshore.yieldspread.model.dto.response.excel;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 动态曲线导出DTO
 *
 * <AUTHOR>
 * @create: 2024-12-18
 */
public class DynCurveExportExcelDTO {
    private String sheetName;

    private List<List<String>> headList;

    private List<List<Object>> dataList;

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public List<List<String>> getHeadList() {
        return Objects.isNull(headList) ? new ArrayList<>() : new ArrayList<>(headList);
    }

    public void setHeadList(List<List<String>> headList) {
        this.headList = Objects.isNull(headList) ? new ArrayList<>() : new ArrayList<>(headList);
    }

    public List<List<Object>> getDataList() {
        return Objects.isNull(dataList) ? new ArrayList<>() : new ArrayList<>(dataList);
    }

    public void setDataList(List<List<Object>> dataList) {
        this.dataList = Objects.isNull(dataList) ? new ArrayList<>() : new ArrayList<>(dataList);
    }
}
