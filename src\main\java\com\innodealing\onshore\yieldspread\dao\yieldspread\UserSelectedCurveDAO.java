package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.UserSelectedCurveMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserSelectedCurveDO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.util.Optional;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 用户勾选的曲线
 *
 * <AUTHOR>
 */
@Repository
public class UserSelectedCurveDAO {

    @Resource
    private UserSelectedCurveMapper userSelectedCurveMapper;


    /**
     * 获取用户勾选的曲线
     *
     * @param userid 用户id
     * @return 用户勾选的曲线数据
     */
    public Optional<UserSelectedCurveDO> getByUserid(Long userid) {
        DynamicQuery<UserSelectedCurveDO> query = DynamicQuery.createQuery(UserSelectedCurveDO.class)
                .and(UserSelectedCurveDO::getUserId, isEqual(userid))
                .and(UserSelectedCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userSelectedCurveMapper.selectByDynamicQuery(query).stream().findFirst();

    }

    /**
     * 新增用户勾选的曲线数据
     *
     * @param selectedCurveDO 用户勾选的曲线数据
     * @return 执行结果
     */
    public boolean insert(UserSelectedCurveDO selectedCurveDO) {
        return convertResult(userSelectedCurveMapper.insertSelective(selectedCurveDO));
    }

    /**
     * 更新用户勾选的曲线数据
     *
     * @param selectedCurveDO 用户勾选的曲线数据
     * @return 是否更新成功
     */
    public boolean updateByPrimaryKeySelective(UserSelectedCurveDO selectedCurveDO) {
        return convertResult(userSelectedCurveMapper.updateByPrimaryKeySelective(selectedCurveDO));
    }

    private boolean convertResult(int operateDataBaseResult) {
        return operateDataBaseResult != 0;
    }
}
