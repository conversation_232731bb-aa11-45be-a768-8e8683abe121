package com.innodealing.onshore.yieldspread.helper;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.innodealing.onshore.yieldspread.model.bo.DynamicTableNameBO;

/**
 * mybatis 动态表名参数 线程本地存储
 *
 * <AUTHOR>
 */
public final class DynamicTableNameParamUtil {

    private DynamicTableNameParamUtil() {
    }

    private static final TransmittableThreadLocal<DynamicTableNameBO> DYNAMIC_TABLE_NAME_PARAM_LOCAL = new TransmittableThreadLocal<>();

    /**
     * 设置 表名
     */
    public static void setDynamicTableNameParamLocal(DynamicTableNameBO dynamicTableNameBO) {
        DYNAMIC_TABLE_NAME_PARAM_LOCAL.set(dynamicTableNameBO);
    }

    /**
     * 移除 分片参数
     */
    public static void removeDynamicTableNameParam() {
        DYNAMIC_TABLE_NAME_PARAM_LOCAL.remove();
    }

    /**
     * 获取 threadlocal值
     */
    public static DynamicTableNameBO getDynamicTableNameParam() {
        return DYNAMIC_TABLE_NAME_PARAM_LOCAL.get();
    }
}
