package com.innodealing.onshore.yieldspread.router.annotation;


import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveShardEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 分片查询切面注解 兼容老接口
 */
@Target({TYPE, FIELD, METHOD})
@Retention(RUNTIME)
@Documented
public @interface ShardYieldSpreadCurve {
    /**
     * 产业与城投存在一级分类
     * @return YieldSpreadCurveShardEnum
     */
    YieldSpreadCurveShardEnum level() default YieldSpreadCurveShardEnum.ALL;

    /**
     * 代理类
     * @return Class
     */
    Class<?> type();

    /**
     * 代理方法
     * @return method
     */
    String method() default "";

}
