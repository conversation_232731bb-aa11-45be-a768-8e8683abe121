package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.LgBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.LgBondYieldSpreadDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static java.util.Objects.isNull;

/**
 * 地方债利差 DAO
 *
 * <AUTHOR>
 * @create: 2024-10-24
 */
@Repository
public class LgBondYieldSpreadDAO {
    @Resource
    private LgBondYieldSpreadMapper lgBondYieldSpreadMapper;
    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;
    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    public void createShardingTable(String tableName) {
        lgBondYieldSpreadMapper.createShardingTable(tableName);
    }

    /**
     * 批量更新
     *
     * @param startDate               开始日期
     * @param endDate                 结束日期
     * @param lgBondYieldSpreadDOList 证券债利差列表
     * @return 受影响的行数
     */
    public int saveLgBondYieldSpreadDOList(Date startDate, Date endDate, List<LgBondYieldSpreadDO> lgBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(lgBondYieldSpreadDOList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        long startPk = ShardingUtils.getMinPkOfDate(startDate);
        long endPk = ShardingUtils.getMaxPkOfDate(endDate);
        Set<Long> bondUniCodes = lgBondYieldSpreadDOList.stream().map(LgBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<LgBondYieldSpreadDO> query = DynamicQuery.createQuery(LgBondYieldSpreadDO.class)
                .select(LgBondYieldSpreadDO::getId, LgBondYieldSpreadDO::getBondUniCode, LgBondYieldSpreadDO::getSpreadDate)
                .and(LgBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(LgBondYieldSpreadDO::getSpreadDate, isEqual(startDate))
                .and(LgBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<LgBondYieldSpreadDO> existDataList = lgBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                effectRows.addAndGet(doBatchInsert(lgBondYieldSpreadDOList));
                return true;
            });
            return effectRows.get();
        }
        Map<String, LgBondYieldSpreadDO> existLgBondYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                        String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                Function.identity(), (x1, x2) -> x2));
        List<LgBondYieldSpreadDO> insertList = new ArrayList<>();
        List<LgBondYieldSpreadDO> updateList = new ArrayList<>();
        for (LgBondYieldSpreadDO lgBondYieldSpreadDO : lgBondYieldSpreadDOList) {
            LgBondYieldSpreadDO existLgBondYieldSpreadDO = existLgBondYieldSpreadDOMap.
                    get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, lgBondYieldSpreadDO.getBondUniCode(),
                            lgBondYieldSpreadDO.getSpreadDate().getTime()));
            if (isNull(existLgBondYieldSpreadDO)) {
                insertList.add(lgBondYieldSpreadDO);
                continue;
            }
            lgBondYieldSpreadDO.setId(existLgBondYieldSpreadDO.getId());
            updateList.add(lgBondYieldSpreadDO);

        }
        // 开启事务执行
        transactionTemplate.execute(transactionStatus -> {
            // 批量操作
            effectRows.addAndGet(doBatchUpdate(updateList));
            effectRows.addAndGet(doBatchInsert(insertList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 证券债利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<LgBondYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<LgBondYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(LgBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.MIN_BATCH_SIZE);
        for (LgBondYieldSpreadDO lgBondYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<LgBondYieldSpreadDO> updateQuery = DynamicQuery.createQuery(LgBondYieldSpreadDO.class)
                        .and(LgBondYieldSpreadDO::getId, isEqual(lgBondYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(lgBondYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 证券债利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<LgBondYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<LgBondYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(LgBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (LgBondYieldSpreadDO lgBondYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(lgBondYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }
}
