package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 行业-导出作图数据请求DTO
 *
 * <AUTHOR>
 * @date 2022-09-26
 */
public class InduCurveExportRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "利差曲线类型 1. 信用利差，2. 超额利差，3. 估值收益率", required = true)
    private Integer spreadCurveType;
    @ApiModelProperty(value = "开始利差日期,格式:yyyy-MM-dd", required = true)
    private Date startSpreadDate;
    @ApiModelProperty(value = "结束利差日期,格式:yyyy-MM-dd", required = true)
    private Date endSpreadDate;
    @ApiModelProperty("2. 组合查询集合")
    private List<InduCurveCompositionConditionDTO> compositionConditions;
    @ApiModelProperty("3. 主体利差查询")
    private List<CurveComSpreadDTO> comSpreads;
    @ApiModelProperty("4. 单券利差查询")
    private List<CurveBondSpreadDTO> bondSpreads;

    public Integer getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(Integer spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public List<InduCurveCompositionConditionDTO> getCompositionConditions() {
        return Objects.isNull(compositionConditions) ? new ArrayList<>() : new ArrayList<>(compositionConditions);
    }

    public void setCompositionConditions(List<InduCurveCompositionConditionDTO> compositionConditions) {
        this.compositionConditions = Objects.isNull(compositionConditions) ? new ArrayList<>() : new ArrayList<>(compositionConditions);
    }

    public List<CurveComSpreadDTO> getComSpreads() {
        return Objects.isNull(comSpreads) ? new ArrayList<>() : new ArrayList<>(comSpreads);
    }

    public void setComSpreads(List<CurveComSpreadDTO> comSpreads) {
        this.comSpreads = Objects.isNull(comSpreads) ? new ArrayList<>() : new ArrayList<>(comSpreads);
    }

    public List<CurveBondSpreadDTO> getBondSpreads() {
        return Objects.isNull(bondSpreads) ? new ArrayList<>() : new ArrayList<>(bondSpreads);
    }

    public void setBondSpreads(List<CurveBondSpreadDTO> bondSpreads) {
        this.bondSpreads = Objects.isNull(bondSpreads) ? new ArrayList<>() : new ArrayList<>(bondSpreads);
    }

    public Date getStartSpreadDate() {
        return Objects.isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public void setStartSpreadDate(Date startSpreadDate) {
        this.startSpreadDate = Objects.isNull(startSpreadDate) ? null : new Date(startSpreadDate.getTime());
    }

    public Date getEndSpreadDate() {
        return Objects.isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }

    public void setEndSpreadDate(Date endSpreadDate) {
        this.endSpreadDate = Objects.isNull(endSpreadDate) ? null : new Date(endSpreadDate.getTime());
    }
}
