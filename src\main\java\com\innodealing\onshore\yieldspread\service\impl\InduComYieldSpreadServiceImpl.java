package com.innodealing.onshore.yieldspread.service.impl;

import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComCurrentBondsBalanceDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondfinance.response.ComFinanceSheetResponseDTO;
import com.innodealing.onshore.bondmetadata.enums.BusinessNatureEnum;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.dao.dmdc.ComInterestInduHistDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgInduBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.InduComYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InduComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.view.InduComYieldSpreadViewDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.MixYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.InduYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.InduCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.InduListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.InduComYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.ComInterestInduHistDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.view.InduComYieldSpreadDynamicView;
import com.innodealing.onshore.yieldspread.model.view.InduComYieldSpreadView;
import com.innodealing.onshore.yieldspread.service.InduComYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.internal.BondFinanceService;
import com.innodealing.onshore.yieldspread.service.internal.BondInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.helper.CommonsHelper.getPropertyName;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.WORK_THREAD_NUM;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.getIndu2UnicodeList;

/**
 * 产业主体利差 Service
 *
 * <AUTHOR>
 **/
@SuppressWarnings({"squid:S1854", "squid:S1481", "squid:S138"})
@Service
public class InduComYieldSpreadServiceImpl extends AbstractComYieldSpreadService implements InduComYieldSpreadService {

    private final ExecutorService executorService;

    protected InduComYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("InduComYieldSpreadServiceImpl-pool-").build());
    }

    @Resource
    private InduComYieldSpreadDAO induComYieldSpreadDAO;

    @Resource
    private PgInduBondYieldSpreadDAO pgInduBondYieldSpreadDAO;

    @Resource
    private BondFinanceService bondFinanceService;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private ComInterestInduHistDAO comInterestInduHistDAO;

    @Resource
    private InduComYieldSpreadViewDAO induComYieldSpreadViewDAO;

    @Resource
    private InduComYieldSpreadRedisDAO induComYieldSpreadRedisDAO;

    private static final SortDTO DEFAULT_SORT =
            new SortDTO(getPropertyName(InduComYieldSpreadResponseDTO::getComCreditSpread), SortDirection.DESC);

    private static final Set<String> HISTORY_ORDER_FIELD_SET;

    static {
        HISTORY_ORDER_FIELD_SET = new HashSet<>();
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPublicCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPrivateCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPerpetualCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComExcessSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPublicExcessSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPrivateExcessSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPerpetualExcessSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComCbYield));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPublicCbYield));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPrivateCbYield));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduComYieldSpreadResponseDTO::getComPerpetualCbYield));
    }

    @Override
    public Integer calcInduComYieldSpreadsBySpreadDate(List<InduComYieldSpreadDO> comYieldSpreadDOs, Date spreadDate,
                                                       Boolean isEnableOldData) {
        if (CollectionUtils.isEmpty(comYieldSpreadDOs)) {
            return 0;
        }
        Set<Long> comUniCodes = comYieldSpreadDOs.stream().map(InduComYieldSpreadDO::getComUniCode).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        CompletableFuture<Map<Long, ComCurrentBondsBalanceDTO>> submitComCurrentBondsBalanceDTO = of.submit(() ->
                bondInfoService.getComCurrentBondsBalanceDTOMap(comUniCodes));
        CompletableFuture<Map<Long, ComFinanceSheetResponseDTO>> submitComFinanceSheetResponseDTO = of.submit(() ->
                bondFinanceService.getComFinanceLatestYearReportMap(spreadDate, comUniCodes));
        // 计算中位数(全部)
        CompletableFuture<Map<Long, PgInduBondYieldSpreadGroupDO>> submitYieldSpread = of.submit(() ->
                pgInduBondYieldSpreadDAO.getInduBondYieldSpreadMap(comUniCodes, null, spreadDate));
        // 计算中位数(公募)
        CompletableFuture<Map<Long, PgInduBondYieldSpreadGroupDO>> submitYieldSpreadIsPublicOffering = of
                .submit(() -> pgInduBondYieldSpreadDAO.getInduBondYieldSpreadMap(comUniCodes,
                        SpreadBondTypeEnum.SPREAD_IS_PUBLIC_OFFERING.getValue(), spreadDate));
        // 计算中位数(私募)
        CompletableFuture<Map<Long, PgInduBondYieldSpreadGroupDO>> submitYieldSpreadNotPublicOffering = of
                .submit(() -> pgInduBondYieldSpreadDAO.getInduBondYieldSpreadMap(comUniCodes,
                        SpreadBondTypeEnum.SPREAD_NOT_PUBLIC_OFFERING.getValue(), spreadDate));
        // 计算中位数(永续)
        CompletableFuture<Map<Long, PgInduBondYieldSpreadGroupDO>> submitYieldSpreadPerpetual = of
                .submit(() -> pgInduBondYieldSpreadDAO.getInduBondYieldSpreadMap(comUniCodes,
                        SpreadBondTypeEnum.SPREAD_PERPETUAL.getValue(), spreadDate));
        CompletableFuture<List<ComInterestInduHistDO>> submitComInterestInduHistDO = of
                .submit(() -> comInterestInduHistDAO.listComInterestInduHistDOByInterestDate(spreadDate, comUniCodes));
        of.doWorks(submitComCurrentBondsBalanceDTO, submitComFinanceSheetResponseDTO, submitYieldSpread, submitYieldSpreadIsPublicOffering,
                submitYieldSpreadNotPublicOffering, submitYieldSpreadPerpetual, submitComInterestInduHistDO);
        // 获取主体存续信息
        Map<Long, ComCurrentBondsBalanceDTO> comCurrentBondsBalanceDTOMap = submitComCurrentBondsBalanceDTO.join();
        // 获取主体财报
        Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap = submitComFinanceSheetResponseDTO.join();
        // 获取估值中位数(全部、公募、私募、永续)
        Map<Long, PgInduBondYieldSpreadGroupDO> yieldSpreadMap = submitYieldSpread.join();
        Map<Long, PgInduBondYieldSpreadGroupDO> yieldSpreadIsPublicOfferingMap = submitYieldSpreadIsPublicOffering.join();
        Map<Long, PgInduBondYieldSpreadGroupDO> yieldSpreadNotPublicOfferingMap = submitYieldSpreadNotPublicOffering.join();
        Map<Long, PgInduBondYieldSpreadGroupDO> yieldSpreadPerpetualMap = submitYieldSpreadPerpetual.join();
        Map<Long, ComInterestInduHistDO> comInterestInduHistDOMap = submitComInterestInduHistDO.join().stream()
                .collect(Collectors.toMap(ComInterestInduHistDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
        List<InduComYieldSpreadDO> comYieldSpreadDOSaves = new ArrayList<>();
        for (InduComYieldSpreadDO comYieldSpreadDO : comYieldSpreadDOs) {
            if (DateExtensionUtils.isSameDay(spreadDate, YieldSpreadHelper.getYesterDay())) {
                comYieldSpreadDO = fillComExistenceColumn(comYieldSpreadDO, comCurrentBondsBalanceDTOMap);
            }
            comYieldSpreadDO = fillFinanceColumn(comYieldSpreadDO, comFinanceSheetResponseDTOMap);
            comYieldSpreadDO = fillComSpreadColumn(comYieldSpreadDO, yieldSpreadMap, yieldSpreadIsPublicOfferingMap,
                    yieldSpreadNotPublicOfferingMap, yieldSpreadPerpetualMap);
            if (isEnableOldData) {
                comYieldSpreadDO = fillOldColumn(comYieldSpreadDO, comInterestInduHistDOMap);
            }
            comYieldSpreadDO.setDeleted(0);
            comYieldSpreadDOSaves.add(comYieldSpreadDO);
        }
        return induComYieldSpreadDAO.saveInduComYieldSpreadDOList(spreadDate, comYieldSpreadDOSaves);
    }

    @Override
    public NormPagingResult<InduComYieldSpreadResponseDTO> getComYieldSpreadPaging(InduListRequestDTO request) {
        Date spreadDate = request.getSpreadDate();
        boolean isToday = false;
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
            isToday = true;
        }
        InduBondYieldSpreadParamDTO searchParameter = InduBondYieldSpreadParamDTO.builder()
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now()))
                .sortOrDefault(request.getSort(), DEFAULT_SORT)
                .page(request.getPageNum(), request.getPageSize())
                .searchCode(request.getCodeType(), request.getSearchCode())
                .comUniCodes(request.getComUniCodes())
                .bondUniCodes(request.getBondUniCodes())
                .compositionCondition(request.getCompositionCondition()).build();
        NormPagingResult<InduComYieldSpreadResponseDTO> result = new NormPagingResult<>();
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());
        result.setList(listComYieldSpreads(searchParameter, isToday));
        return result;
    }

    private List<InduComYieldSpreadResponseDTO> listComYieldSpreads(InduBondYieldSpreadParamDTO searchParameter, boolean isToday) {
        SortDTO sort = searchParameter.getSort();
        // 所传日期为今天需要查询view视图数据，需要查询三月变动，六月变动数据
        List<? extends InduComYieldSpreadDO> result;
        if (isToday) {
            result = induComYieldSpreadDAO.getComYieldSpreadChangePagingByJoin(searchParameter);
        } else {
            searchParameter.setSort(sort != null && HISTORY_ORDER_FIELD_SET.contains(sort.getPropertyName()) ? sort : DEFAULT_SORT);
            result = induComYieldSpreadDAO.getComYieldSpreadPagingByJoin(searchParameter);
        }
        Set<Long> comUniCodes = result.stream().map(InduComYieldSpreadDO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return result.stream().map(com -> {
            InduComYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(com, InduComYieldSpreadResponseDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            BusinessNatureEnum.getBusinessNature(response.getBusinessNature()).ifPresent(nature -> response.setBusinessNatureName(nature.getText()));
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public NormPagingResult<InduComYieldSpreadResponseDTO> getComYieldSpreadPagingByExists(InduListRequestDTO request) {
        Date spreadDate = request.getSpreadDate();
        boolean isToday = false;
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
            isToday = true;
        }
        InduBondYieldSpreadParamDTO searchParameter = InduBondYieldSpreadParamDTO.builder()
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now()))
                .sortOrDefault(request.getSort(), DEFAULT_SORT)
                .page(request.getPageNum(), request.getPageSize())
                .searchCode(request.getCodeType(), request.getSearchCode())
                .comUniCodes(request.getComUniCodes())
                .bondUniCodes(request.getBondUniCodes())
                .compositionCondition(request.getCompositionCondition()).build();
        SortDTO sort = searchParameter.getSort();
        // 所传日期为今天需要查询view视图数据，需要查询三月变动，六月变动数据
        NormPagingResult<? extends InduComYieldSpreadDO> pagingResult;
        if (isToday) {
            pagingResult = induComYieldSpreadDAO.getComYieldSpreadChangePagingByExists(searchParameter);
        } else {
            searchParameter.setSort(HISTORY_ORDER_FIELD_SET.contains(sort.getPropertyName()) ? sort : DEFAULT_SORT);
            pagingResult = induComYieldSpreadDAO.getComYieldSpreadPagingByExists(searchParameter);
        }
        Set<Long> comUniCodes = pagingResult.getList().stream().map(InduComYieldSpreadDO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return pagingResult.convert(com -> {
            InduComYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(com, InduComYieldSpreadResponseDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            BusinessNatureEnum.getBusinessNature(response.getBusinessNature()).ifPresent(nature -> response.setBusinessNatureName(nature.getText()));
            return response;
        });
    }

    @Override
    public Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_INDU_COM_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = induComYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_INDU_COM_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }

    @Override
    public Long getComYieldSpreadPagingCount(InduListRequestDTO request) {
        Date spreadDate = request.getSpreadDate();
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
        }
        InduListRequestDTO copyRequest = BeanCopyUtils.copyProperties(request, InduListRequestDTO.class);
        copyRequest.setSpreadDate(spreadDate);
        String key = String.format(SPREAD_INDU_COM_SPREAD_COUNT_KEY, copyRequest.toString().hashCode());
        String cacheValue = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(cacheValue)) {
            return Long.parseLong(cacheValue);
        }
        InduBondYieldSpreadParamDTO searchParameter = InduBondYieldSpreadParamDTO.builder()
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now()))
                .sortOrDefault(request.getSort(), DEFAULT_SORT)
                .page(request.getPageNum(), request.getPageSize())
                .searchCode(request.getCodeType(), request.getSearchCode())
                .comUniCodes(request.getComUniCodes())
                .bondUniCodes(request.getBondUniCodes())
                .compositionCondition(request.getCompositionCondition()).build();
        Long count = induComYieldSpreadDAO.getComYieldSpreadPagingCount(searchParameter);
        stringRedisTemplate.opsForValue().set(key, String.valueOf(count), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return count;
    }

    @Override
    public List<InduComYieldSpreadResponseDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        InduCurveGenerateConditionReqDTO generateRequest = getCurveGenerateCondition(userid, request.getCurveId(), InduCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return Collections.emptyList();
        }
        boolean isToday = super.isToday(request.getSpreadDate());
        InduYieldSearchParam param = this.buildInduYieldSearchParam(request, generateRequest);
        List<InduComYieldSpreadView> induComYieldSpreads = induComYieldSpreadDAO.listComYieldSpreads(isToday, param);
        if (CollectionUtils.isEmpty(induComYieldSpreads)) {
            return Collections.emptyList();
        }
        Set<Long> comUniCodes = induComYieldSpreads.stream().map(InduComYieldSpreadDO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return induComYieldSpreads.stream().map(com -> {
            InduComYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(com, InduComYieldSpreadResponseDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            BusinessNatureEnum.getBusinessNature(response.getBusinessNature()).ifPresent(nature -> response.setBusinessNatureName(nature.getText()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getNetProfit()).ifPresent(response::setNetProfit);
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            CalculationHelper.divideTenThousand(response.getNetOperatingCashFlow()).ifPresent(response::setNetOperatingCashFlow);
            return response;
        }).collect(Collectors.toList());
    }

    private InduYieldSearchParam buildInduYieldSearchParam(YieldSpreadSearchReqDTO request, InduCurveGenerateConditionReqDTO generateRequest) {
        InduYieldSearchParam param = super.buildComYieldSearchParam(request, generateRequest, InduYieldSearchParam.class, InduComYieldSpreadDO.class);
        Long industryCode = generateRequest.getIndustryCode();
        if (Objects.nonNull(industryCode)) {
            if (getIndu2UnicodeList().contains(industryCode)) {
                param.setIndustryCode2(industryCode);
            } else {
                param.setIndustryCode1(industryCode);
            }
        }
        return param;
    }

    @Override
    public Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request) {
        InduCurveGenerateConditionReqDTO generateRequest = getCurveGenerateCondition(userid, request.getCurveId(), InduCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return 0L;
        }
        return super.getComCountFromRedis(request, SPREAD_INDU_COM_SPREAD_COUNT_KEY, () -> {
            InduYieldSearchParam param = this.buildInduYieldSearchParam(request, generateRequest);
            return induComYieldSpreadDAO.countComYieldSpread(param);
        });
    }

    @Override
    public List<InduComYieldSpreadResponseDTO> listComs(Date spreadDate, Set<Long> induComs) {
        if (org.springframework.util.CollectionUtils.isEmpty(induComs)) {
            return Collections.emptyList();
        }
        // 今天的话要加上变动数据
        List<InduComYieldSpreadResponseDTO> induResponseList;
        if (super.isToday(spreadDate)) {
            spreadDate = this.getMaxSpreadDate();
            List<InduComYieldSpreadDynamicView> induComYieldSpreads = induComYieldSpreadViewDAO.listComYieldSpreads(spreadDate, induComs);
            induResponseList = BeanCopyUtils.copyList(induComYieldSpreads, InduComYieldSpreadResponseDTO.class);
        } else {
            List<InduComYieldSpreadDO> induComYieldSpreads = induComYieldSpreadDAO.listComYieldSpreads(spreadDate, induComs);
            induResponseList = BeanCopyUtils.copyList(induComYieldSpreads, InduComYieldSpreadResponseDTO.class);
        }
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(induComs);
        return induResponseList.stream().map(com -> {
            InduComYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(com, InduComYieldSpreadResponseDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            BusinessNatureEnum.getBusinessNature(response.getBusinessNature()).ifPresent(nature -> response.setBusinessNatureName(nature.getText()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getNetProfit()).ifPresent(response::setNetProfit);
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            CalculationHelper.divideTenThousand(response.getNetOperatingCashFlow()).ifPresent(response::setNetOperatingCashFlow);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer spreadBondType, Date startDate, Date endDate) {
        return induComYieldSpreadRedisDAO.listCurves(comUniCode, spreadBondType, startDate, endDate);
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.INDU;
    }

    private InduComYieldSpreadDO fillComExistenceColumn(InduComYieldSpreadDO induComYieldSpreadDO,
                                                        Map<Long, ComCurrentBondsBalanceDTO> comCurrentBondsBalanceDTOMap) {
        InduComYieldSpreadDO result = BeanCopyUtils.copyProperties(induComYieldSpreadDO, InduComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comCurrentBondsBalanceDTOMap)) {
            return result;
        }
        ComCurrentBondsBalanceDTO comCurrentBondsBalanceDTO = comCurrentBondsBalanceDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(comCurrentBondsBalanceDTO)) {
            result.setBondBalance(comCurrentBondsBalanceDTO.getComCurrentBondsBalance());
        }
        return result;
    }

    private InduComYieldSpreadDO fillFinanceColumn(InduComYieldSpreadDO induComYieldSpreadDO,
                                                   Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap) {
        InduComYieldSpreadDO result = BeanCopyUtils.copyProperties(induComYieldSpreadDO, InduComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comFinanceSheetResponseDTOMap)) {
            return result;
        }
        ComFinanceSheetResponseDTO comFinanceSheetResponseDTO = comFinanceSheetResponseDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(comFinanceSheetResponseDTO)) {
            result.setTotalAssets(comFinanceSheetResponseDTO.getTotalAssets());
            result.setNetProfit(comFinanceSheetResponseDTO.getNetProfit());
            result.setAssetLiabilityRatio(comFinanceSheetResponseDTO.getAssetLiabilityRatio());
            result.setNetOperatingCashFlow(comFinanceSheetResponseDTO.getNetOperatingCashFlow());
        }
        return result;
    }

    private InduComYieldSpreadDO fillComSpreadColumn(InduComYieldSpreadDO induComYieldSpreadDO,
                                                     Map<Long, PgInduBondYieldSpreadGroupDO> yieldSpreadMap,
                                                     Map<Long, PgInduBondYieldSpreadGroupDO> yieldSpreadIsPublicOfferingMap,
                                                     Map<Long, PgInduBondYieldSpreadGroupDO> yieldSpreadNotPublicOfferingMap,
                                                     Map<Long, PgInduBondYieldSpreadGroupDO> yieldSpreadPerpetualMap) {
        InduComYieldSpreadDO result = BeanCopyUtils.copyProperties(induComYieldSpreadDO, InduComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(result)) {
            return result;
        }
        PgInduBondYieldSpreadGroupDO yieldSpread = yieldSpreadMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpread)) {
            result.setComCbYield(yieldSpread.getCbYield());
            result.setComCreditSpread(yieldSpread.getBondCreditSpread());
            result.setComExcessSpread(yieldSpread.getBondExcessSpread());
        }
        PgInduBondYieldSpreadGroupDO yieldSpreadIsPublicOffering = yieldSpreadIsPublicOfferingMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadIsPublicOffering)) {
            result.setComPublicCbYield(yieldSpreadIsPublicOffering.getCbYield());
            result.setComPublicCreditSpread(yieldSpreadIsPublicOffering.getBondCreditSpread());
            result.setComPublicExcessSpread(yieldSpreadIsPublicOffering.getBondExcessSpread());
        }
        PgInduBondYieldSpreadGroupDO yieldSpreadNotPublicOffering = yieldSpreadNotPublicOfferingMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadNotPublicOffering)) {
            result.setComPrivateCbYield(yieldSpreadNotPublicOffering.getCbYield());
            result.setComPrivateCreditSpread(yieldSpreadNotPublicOffering.getBondCreditSpread());
            result.setComPrivateExcessSpread(yieldSpreadNotPublicOffering.getBondExcessSpread());
        }
        PgInduBondYieldSpreadGroupDO yieldSpreadPerpetual = yieldSpreadPerpetualMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadPerpetual)) {
            result.setComPerpetualCbYield(yieldSpreadPerpetual.getCbYield());
            result.setComPerpetualCreditSpread(yieldSpreadPerpetual.getBondCreditSpread());
            result.setComPerpetualExcessSpread(yieldSpreadPerpetual.getBondExcessSpread());
        }
        return result;
    }

    private InduComYieldSpreadDO fillOldColumn(InduComYieldSpreadDO induComYieldSpreadDO,
                                               Map<Long, ComInterestInduHistDO> comInterestInduHistDOMap) {
        InduComYieldSpreadDO result = BeanCopyUtils.copyProperties(induComYieldSpreadDO, InduComYieldSpreadDO.class);
        if (MapUtils.isEmpty(comInterestInduHistDOMap)) {
            return result;
        }
        ComInterestInduHistDO comInterestInduHistDO = comInterestInduHistDOMap.get(result.getComUniCode());
        if (Objects.nonNull(comInterestInduHistDO)) {
            result.setInduLevel1Code(comInterestInduHistDO.getIndustryCode1());
            result.setInduLevel1Name(comInterestInduHistDO.getIndustryName1());
            result.setInduLevel2Code(comInterestInduHistDO.getIndustryCode2());
            result.setInduLevel2Name(comInterestInduHistDO.getIndustryName2());
            result.setBusinessNature(comInterestInduHistDO.getEnterpriseType());
        }
        return result;
    }

    @Override
    protected List<MixYieldSpreadShortBO> listAllYieldSpreads(@NonNull List<Long> comUniCodes) {
        return induComYieldSpreadDAO.listAllYieldSpreads(comUniCodes);
    }

}
