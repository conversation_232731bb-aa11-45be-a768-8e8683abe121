package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 保险公司债求偿顺序
 *
 * <AUTHOR>
 **/
public enum InsuSeniorityRankingEnum implements ITextValueEnum {
    /**
     * 资本补充
     */
    TIER2(1, "资本补充"),
    /**
     * 永续
     */
    PERPETUAL(2, "永续");

    private final int value;
    private final String text;

    InsuSeniorityRankingEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }


    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    /**
     * 根据value 获取text
     *
     * @param value 保险求偿编号
     * @return 保险公司债求偿顺序 名称
     */
    public static Optional<String> getTextByValue(Integer value) {
        return Arrays.stream(InsuSeniorityRankingEnum.values()).
                filter(insuSeniorityRankingEnum -> Objects.nonNull(value) && insuSeniorityRankingEnum.getValue() == value)
                .map(InsuSeniorityRankingEnum::getText).findFirst();
    }
}
