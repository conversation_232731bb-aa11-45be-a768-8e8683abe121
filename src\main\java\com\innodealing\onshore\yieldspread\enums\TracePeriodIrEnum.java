package com.innodealing.onshore.yieldspread.enums;

/**
 * 利差追踪-期限利差-利率债期限枚举
 *
 * <AUTHOR>
 * @date 2024/10/21 19:03
 **/
public enum TracePeriodIrEnum implements ITracePeriodCommonEnum {
    /**
     * 利率债-期限利差-1月期
     */
    TRACE_PERIOD_IR_1M(PeriodEnum.ONE_MONTH, "3M-1M", PeriodEnum.THREE_MONTHS, PeriodEnum.ONE_MONTH),
    TRACE_PERIOD_IR_3M(PeriodEnum.THREE_MONTHS, "6M-3M", PeriodEnum.SIX_MONTHS, PeriodEnum.THREE_MONTHS),
    TRACE_PERIOD_IR_6M(PeriodEnum.SIX_MONTHS, "9M-6M", PeriodEnum.NINE_MONTHS, PeriodEnum.SIX_MONTHS),
    TRACE_PERIOD_IR_9M(PeriodEnum.NINE_MONTHS, "1Y-9M", PeriodEnum.ONE_YEAR, PeriodEnum.NINE_MONTHS),
    TRACE_PERIOD_IR_1Y(PeriodEnum.ONE_YEAR, "2Y-1Y", PeriodEnum.TWO_YEARS, PeriodEnum.ONE_YEAR),
    TRACE_PERIOD_IR_2Y(PeriodEnum.TWO_YEARS, "3Y-2Y", PeriodEnum.THREE_YEARS, PeriodEnum.TWO_YEARS),
    TRACE_PERIOD_IR_3Y(PeriodEnum.THREE_YEARS, "5Y-3Y", PeriodEnum.FIVE_YEARS, PeriodEnum.THREE_YEARS),
    TRACE_PERIOD_IR_5Y(PeriodEnum.FIVE_YEARS, "7Y-5Y", PeriodEnum.SEVEN_YEARS, PeriodEnum.FIVE_YEARS),
    TRACE_PERIOD_IR_7Y(PeriodEnum.SEVEN_YEARS, "10Y-7Y", PeriodEnum.TEN_YEARS, PeriodEnum.SEVEN_YEARS),
    TRACE_PERIOD_IR_10Y(PeriodEnum.TEN_YEARS, "15Y-10Y", PeriodEnum.FIFTEEN_YEARS, PeriodEnum.TEN_YEARS),
    TRACE_PERIOD_IR_15Y(PeriodEnum.FIFTEEN_YEARS, "15Y-10Y", PeriodEnum.FIFTEEN_YEARS, PeriodEnum.TEN_YEARS),
    TRACE_PERIOD_IR_20Y(PeriodEnum.TWENTY_YEARS, "20Y-10Y", PeriodEnum.TWENTY_YEARS, PeriodEnum.TEN_YEARS),
    TRACE_PERIOD_IR_30Y(PeriodEnum.THIRTY_YEARS, "30Y-10Y", PeriodEnum.THIRTY_YEARS, PeriodEnum.TEN_YEARS),
    TRACE_PERIOD_IR_50Y(PeriodEnum.FIFTY_YEARS, "50Y-30Y", PeriodEnum.FIFTY_YEARS, PeriodEnum.THIRTY_YEARS);


    /**
     * 对应期限类型
     */
    private final PeriodEnum periodEnum;

    /**
     * 期限利差描述
     */
    private final String desc;

    /**
     * 减数
     */
    private final PeriodEnum subtrahendEnum;

    /**
     * 被减数
     */
    private final PeriodEnum minuendEnum;

    TracePeriodIrEnum(PeriodEnum periodEnum, String desc, PeriodEnum subtrahendEnum, PeriodEnum minuendEnum) {
        this.periodEnum = periodEnum;
        this.desc = desc;
        this.subtrahendEnum = subtrahendEnum;
        this.minuendEnum = minuendEnum;
    }

    @Override
    public PeriodEnum getPeriodEnum() {
        return periodEnum;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public PeriodEnum getSubtrahendEnum() {
        return subtrahendEnum;
    }

    @Override
    public PeriodEnum getMinuendEnum() {
        return minuendEnum;
    }
}
