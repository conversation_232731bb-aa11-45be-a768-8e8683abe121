package com.innodealing.onshore.yieldspread.dao.pgshard.partition;

import com.github.wz2cool.dynamic.BaseFilterDescriptor;
import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.InsuShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.InsuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.InsuShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 保险债券利差曲线分片数据源
 *
 * <AUTHOR>
 */
@Repository
public class InsuBondShardYieldSpreadCurveRepository {

    @Resource
    private InsuShardBondYieldSpreadCurveMapper insuShardBondYieldSpreadCurveMapper;

    /**
     * 保险债券利差分片查询
     *
     * @param params 查询参数
     * @param router 路由
     * @return 分片表结果集
     */
    public List<InsuShardBondYieldSpreadCurveDO> listInsuYieldSpreads(InsuYieldSearchParam params, AbstractRatingRouter router) {
        BaseFilterDescriptor<InsuShardBondYieldSpreadCurveDO>[] baseFilterDescriptors = this.listFilters(params).getFilters();
        DynamicQuery<InsuShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(InsuShardBondYieldSpreadCurveDO.class)
                .and(baseFilterDescriptors);
        return insuShardBondYieldSpreadCurveMapper.selectByDynamicQueryRouter(query, router);
    }

    private FilterGroupDescriptor<InsuShardBondYieldSpreadCurveDO> listFilters(InsuYieldSearchParam params) {
        return FilterGroupDescriptor.create(InsuShardBondYieldSpreadCurveDO.class)
                .and(nonNull(params.getSpreadBondType()), InsuShardBondYieldSpreadCurveDO::getInsuranceSeniorityRanking, isEqual(params.getSpreadBondType()))
                .and(isNull(params.getSpreadBondType()), InsuShardBondYieldSpreadCurveDO::getUsingInsuranceSeniorityRanking, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(CollectionUtils.isNotEmpty(params.getBusinessFilterNatures()), InsuShardBondYieldSpreadCurveDO::getBusinessFilterNature, in(params.getBusinessFilterNatures()))
                .and(CollectionUtils.isEmpty(params.getBusinessFilterNatures())
                        , InsuShardBondYieldSpreadCurveDO::getUsingBusinessFilterNature
                        , isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(params.getRemainingTenor()), InsuShardBondYieldSpreadCurveDO::getSpreadRemainingTenorTag, isEqual(params.getRemainingTenor()))
                .and(isNull(params.getRemainingTenor()), InsuShardBondYieldSpreadCurveDO::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                ;
    }

}