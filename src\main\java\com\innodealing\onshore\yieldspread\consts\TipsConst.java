package com.innodealing.onshore.yieldspread.consts;

/**
 * 提示
 *
 * <AUTHOR>
 */
public final class TipsConst {

    private TipsConst() {
    }

    public static final String SAVE_CURVE_UPPER_LIMIT_REMIND = "保存的曲线数量不能超过 " + YieldSpreadConst.SAVE_CURVE_UPPER_LIMIT;

    /**
     * 曲线不存在提示
     */
    public static final String CURVE_NOT_EXIST = "曲线不存在";

    public static final String GENERATING_CURVE_UPPER_LIMIT_REMIND = String.format("已有 %d 条曲线在生成，请等待生成完毕后再尝试", YieldSpreadConst.GENERATING_CURVE_UPPER_LIMIT);

    public static final String GROUP_NAME_REMIND_UPPER_LIMIT = "曲线组名称不能超过" + YieldSpreadConst.GROUP_NAME_MAX_LENGTH + "个字符";

    public static final String GROUP_NOT_EXIST = "曲线组不存在";

    public static final String GROUP_ALREADY_EXIST = "曲线组已经存在";

    public static final String GROUP_COUNT_REMIND_UPPER_LIMIT = "曲线组已达上限";

    public static final String DEFAULT_GROUP_CANNOT_DELETE = "默认曲线组不能删除";

    public static final String DEFAULT_GROUP_CANNOT_UPDATE = "默认曲线组不能更新";

    public static final String GROUP_ADD_FAIL = "添加曲线组失败";

}
