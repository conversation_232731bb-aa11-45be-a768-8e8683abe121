package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.model.dto.response.CurveDataResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveDefinitionResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CurvePoolListResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveResDTO;
import org.springframework.lang.Nullable;

import java.sql.Date;
import java.util.List;

/**
 * 曲线池
 *
 * <AUTHOR>
 */
public interface CurvePoolService {

    /**
     * 曲线池曲线列表
     *
     * @param userid           用户id
     * @param curveName        曲线名称
     * @param justLookSelected 是否只看已选
     * @return 曲线列表
     */
    CurvePoolListResDTO listCurveDefinitions(Long userid, String curveName, Boolean justLookSelected);

    /**
     * 获取曲线定义数据详情
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @return 曲线详情
     */
    CurveDefinitionResDTO getCurveDefinition(Long userid, Long curveId);

    /**
     * 选择或去除选择某条曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @return 执行结果
     */
    boolean setUpSelected(Long userid, Long curveId);

    /**
     * 选择曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @return 执行结果
     */
    boolean selectedCurves(Long userid, List<Long> curveId);

    /**
     * 清空已选
     *
     * @param userid 用户id
     * @return 执行结果
     */
    boolean clearAllSelected(Long userid);

    /**
     * 移除曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @return 执行结果
     */
    boolean removeCurve(Long userid, Long curveId);

    /**
     * 批量删除曲线
     *
     * @param userid   用户id
     * @param curveIds 曲线列表
     * @return 执行结果
     */
    Integer batchRemoveCurve(Long userid, List<Long> curveIds);

    /**
     * 获取曲线数据
     *
     * @param userid    用户id
     * @param curveId   曲线id
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 曲线数据
     */
    CurveResDTO getCurve(Long userid, Long curveId, Date startDate, Date endDate);

    /**
     * 获取曲线数据
     *
     * @param userid            用户id
     * @param curveId           曲线id
     * @param startDate         起始日期
     * @param endDate           结束日期
     * @param containPercentile 是否包含百分位数
     * @return 曲线数据
     */
    CurveResDTO getCurve(Long userid, Long curveId, Date startDate, Date endDate, Boolean containPercentile);

    /**
     * 获取曲线数据
     *
     * @param curveId   曲线id
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 曲线数据
     */
    List<CurveDataResDTO> listCurveData(Long curveId, Date startDate, Date endDate);

    /**
     * 获取脱敏曲线数据
     *
     * @param userid    userid
     * @param curveId   曲线id
     * @param startDate 起始日期
     * @param endDate   结束日期
     * @return 曲线数据
     */
    List<CurveDataResDTO> listDesensitizationCurveData(Long userid, Long curveId, Date startDate, Date endDate);

    /**
     * 清除曲线缓存
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @return 操作结果
     */
    boolean clearCurveCache(Long userid, Long curveId);

    /**
     * 清除基准曲线缓存
     *
     * @return 操作结果
     */
    boolean clearBenchmarkCurveCache();

    /**
     * 添加曲线组
     * 这个接口只能添加我的曲线组中的普通组
     *
     * @param userid    用户id
     * @param groupName 组名
     * @return 组id
     */
    Long addGroup(Long userid, String groupName);

    /**
     * 更新曲线组
     *
     * @param userid    用户id
     * @param groupId   组id
     * @param groupName 组名称
     * @return 处理结果
     */
    Boolean updateGroup(Long userid, Long groupId, String groupName);

    /**
     * 删除组
     *
     * @param userid  用户id
     * @param groupId 组id
     * @return 处理结果
     */
    Boolean deleteGroup(Long userid, Long groupId);

    /**
     * 获取默认组id，如果默认组不存在就添加
     *
     * @param userid 用户id
     * @return 默认组id
     */
    Long getDefaultGroupIdOrAddIfNotExist(Long userid);

    /**
     * 获取曲线组内下一个序号
     *
     * @param groupId 组id
     * @return 曲线下一个序号
     */
    Integer getCurveNextOrder(Long groupId);

    /**
     * 初始化组和曲线，因为以前没有曲线组和order的概念，所以这里要把以前数据初始化下
     *
     * @return true/false
     */
    Boolean initializeGroup();

    /**
     * 移动曲线
     *
     * @param userid                   用户ID
     * @param curveId                  当前曲线ID
     * @param targetGroupId            目标组id
     * @param targetLocationPreCurveId 目标位置前一个曲线id，如果移动到第一位，那么就是null，如果移动到最后一位，那么就是最后一条曲线id
     * @return true/false
     */
    Boolean moveCurveLocation(Long userid, Long curveId, Long targetGroupId, @Nullable Long targetLocationPreCurveId);

    /**
     * 移动组
     *
     * @param userid                   用户ID
     * @param groupId                  当前曲线组ID
     * @param targetLocationPreGroupId 目标位置前一个曲线组id，如果移动到第一位，那么就是null，如果移动到最后一位，那么就是最后一个曲线组id
     * @return true/false
     */
    Boolean moveGroupLocation(Long userid, Long groupId, @Nullable Long targetLocationPreGroupId);
}
