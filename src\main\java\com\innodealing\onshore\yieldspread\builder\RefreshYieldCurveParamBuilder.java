package com.innodealing.onshore.yieldspread.builder;

import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;

/**
 * 物化视图参数构造器
 * <AUTHOR>
 */
public class RefreshYieldCurveParamBuilder {

    private final RefreshYieldCurveParam refreshYieldCurveParam;

    /**
     * 构造方法
     */
    public RefreshYieldCurveParamBuilder() {
        refreshYieldCurveParam = new RefreshYieldCurveParam();
    }

    /**
     * 时间范围
     * @param dateRange date
     * @return this
     */
    public RefreshYieldCurveParamBuilder dateRange(AbstractRatingRouter.SpreadDateRange dateRange){
        refreshYieldCurveParam.setStartDate(dateRange.getStartDate());
        refreshYieldCurveParam.setEndDate(dateRange.getEndDate());
        return this;
    }

    /**
     * 是否刷新物化视图或表
     * @param mvSwitch mv
     * @param tableSwitch table
     * @return this
     */
    public RefreshYieldCurveParamBuilder initRefresh(Boolean mvSwitch, Boolean tableSwitch){
        refreshYieldCurveParam.setMvRefresh(mvSwitch);
        refreshYieldCurveParam.setTableRefresh(tableSwitch);
        return this;
    }

    /**
     * 构造器
     * @return refreshYieldCurveParam
     */
    public RefreshYieldCurveParam build() {
        return refreshYieldCurveParam;
    }


}
