package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgSpreadTraceQuantileStatisticsViewDO;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;

/**
 * 债券利差追踪-绝对值Mapper
 *
 * <AUTHOR>
 */
public interface PgBondYieldSpreadTraceAbsMapper extends DynamicQueryMapper<PgBondYieldSpreadTraceAbsDO> {

    /**
     * 获取指定时间范围的 全景分位数统计 数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param issueDate 发行时间
     * @param bondType  债券品种
     * @return 计算历史分位所需的基本数据
     */
    List<PgSpreadTraceQuantileStatisticsViewDO> listTraceQuantileStatisticsViews(@Param("startDate") Date startDate,
                                                                                 @Param("endDate") Date endDate,
                                                                                 @Param("issueDate") Date issueDate,
                                                                                 @Param("bondType") Integer bondType);
}
