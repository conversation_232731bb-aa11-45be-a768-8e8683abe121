package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.NormPagingQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.UpdateQuery;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.enums.CurveGenerateStatusEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.UserCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionBO;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionWithImportedBondBO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserCurveDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 曲线
 *
 * <AUTHOR>
 */
@Repository
public class UserCurveDAO {

    @Resource
    private UserCurveMapper userCurveMapper;

    /**
     * 是否存在
     *
     * @param curveId 曲线id
     * @return 是否存在
     */
    public boolean isExist(Long curveId) {
        return isExist(curveId, null, null);
    }

    /**
     * 是否存在
     *
     * @param curveId                 曲线id
     * @param userid                  用户id
     * @param containsBenchmarkCurves 是否包含基准曲线
     * @return 是否存在
     */
    public boolean isExist(Long curveId, Long userid, Boolean containsBenchmarkCurves) {
        containsBenchmarkCurves = ObjectExtensionUtils.getOrDefault(containsBenchmarkCurves, false);
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(Objects.nonNull(userid), UserCurveDO::getUserId, containsBenchmarkCurves ? in(userid, YieldSpreadConst.BENCHMARK_CURVE_USER_ID) : isEqual(userid))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.selectFirstByDynamicQuery(query).isPresent();
    }

    /**
     * 是否不存在
     *
     * @param curveId 曲线id
     * @return 是否不存在
     */
    public boolean isNonentity(Long curveId) {
        return !isExist(curveId);
    }

    /**
     * 是否存在
     *
     * @param userid    用户id
     * @param curveName 曲线名称
     * @return 是否存在
     */
    public boolean isExist(Long userid, String curveName) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId)
                .and(UserCurveDO::getUserId, isEqual(userid))
                .and(UserCurveDO::getSpreadCurveName, isEqual(curveName))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.selectFirstByDynamicQuery(query).isPresent();
    }

    /**
     * 新增用户曲线
     *
     * @param userCurveDO 用户曲线
     * @return 执行结果
     */
    public boolean insert(UserCurveDO userCurveDO) {
        return userCurveMapper.insertSelective(userCurveDO) != 0;
    }

    /**
     * 更新用户曲线
     *
     * @param userCurveDO 用户曲线
     * @return 影响行数
     */
    public boolean updateByPrimaryKeySelective(UserCurveDO userCurveDO) {
        return userCurveMapper.updateByPrimaryKeySelective(userCurveDO) != 0;
    }

    /**
     * 批量逻辑更新用户曲线
     *
     * @param ids     id 列表
     * @param deleted 删除状态枚举
     */
    public int updateDeletedByIds(List<Long> ids, Deleted deleted) {
        if (CollectionUtils.isEmpty(ids)) {
            return YieldSpreadConst.INT_ZERO;
        }
        UpdateQuery<UserCurveDO> updateQuery = UpdateQuery.createQuery(UserCurveDO.class)
                .set(UserCurveDO::getDeleted, deleted.getValue())
                .and(UserCurveDO::getId, c -> c.in(ids));
        return userCurveMapper.updateByUpdateQuery(updateQuery);
    }

    /**
     * 更新用户曲线
     *
     * @param userCurveDO 用户曲线
     * @return 影响行数
     */
    public boolean updateByPrimaryKey(UserCurveDO userCurveDO) {
        return userCurveMapper.updateByPrimaryKey(userCurveDO) != 0;
    }

    /**
     * 乐观更新，比较曲线生成状态
     *
     * @param userCurveDO       需更新的曲线元数据
     * @param curveId           曲线id
     * @param oldGenerateStatus 期望的生成状态
     * @return 更新是否成功
     */
    public boolean casByGenerateStatus(UserCurveDO userCurveDO, Long curveId, CurveGenerateStatusEnum oldGenerateStatus) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getGenerateStatus, isEqual(oldGenerateStatus.getValue()))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.updateSelectiveByDynamicQuery(userCurveDO, query) != 0;
    }

    /**
     * 乐观更新自定义曲线生成状态
     *
     * @param curveId           曲线id
     * @param newGenerateStatus 新状态
     * @param oldGenerateStatus 旧状态
     * @return 是否更新成功
     */
    public boolean casGenerateStatus(Long curveId, CurveGenerateStatusEnum newGenerateStatus, CurveGenerateStatusEnum oldGenerateStatus) {
        UserCurveDO userCurveDO = new UserCurveDO();
        userCurveDO.setGenerateStatus(newGenerateStatus.getValue());
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getGenerateStatus, isEqual(oldGenerateStatus.getValue()))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.updateSelectiveByDynamicQuery(userCurveDO, query) == 1;
    }

    /**
     * 获取曲线数据
     *
     * @param curveId 曲线id
     * @return 曲线数据UserCurveDO
     */
    public Optional<UserCurveDO> get(Long curveId) {
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.selectFirstByDynamicQuery(query);
    }

    /**
     * 获取曲线数据
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @return 曲线数据UserCurveDO
     */
    public Optional<UserCurveDO> get(Long userid, Long curveId) {
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .and(UserCurveDO::getUserId, isEqual(userid))
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.selectFirstByDynamicQuery(query);
    }

    /**
     * 获取曲线定义数据
     *
     * @param curveId 曲线id
     * @return 曲线数据CurveBaseDataWithFilterConditionBO
     */
    public Optional<CurveDefinitionBO> getCurveDefinitionBO(Long curveId) {
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId, UserCurveDO::getCurveGroupId, UserCurveDO::getUserId,
                        UserCurveDO::getSpreadCurveName, UserCurveDO::getSpreadCurveType, UserCurveDO::getGenerateStatus,
                        UserCurveDO::getGenerateStartTime, UserCurveDO::getGenerateEndTime, UserCurveDO::getFilterCondition,
                        UserCurveDO::getCurveOrder)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        Optional<UserCurveDO> userCurveOptional = userCurveMapper.selectFirstByDynamicQuery(query);
        return userCurveOptional.map(userCurveDO -> BeanCopyUtils.copyProperties(userCurveDO, CurveDefinitionBO.class));
    }

    /**
     * 获取曲线数据
     *
     * @param curveId 曲线id
     * @return 曲线数据CurveBaseDataWithImportedBondBO
     */
    public Optional<CurveDefinitionWithImportedBondBO> getCurveDefinitionWithImportedBondBO(Long curveId) {
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId, UserCurveDO::getCurveGroupId, UserCurveDO::getUserId,
                        UserCurveDO::getSpreadCurveName, UserCurveDO::getSpreadCurveType, UserCurveDO::getGenerateStatus,
                        UserCurveDO::getGenerateStartTime, UserCurveDO::getGenerateEndTime, UserCurveDO::getFilterCondition,
                        UserCurveDO::getCurveOrder, UserCurveDO::getImportedBond)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        Optional<UserCurveDO> userCurveOptional = userCurveMapper.selectFirstByDynamicQuery(query);
        return userCurveOptional.map(userCurveDO -> BeanCopyUtils.copyProperties(userCurveDO, CurveDefinitionWithImportedBondBO.class));
    }

    /**
     * 获取曲线数据 包含基准曲线
     *
     * @param userid  用户id
     * @param curveId 曲线id
     * @return 曲线数据CurveBaseDataWithImportedBondBO
     */
    public Optional<CurveDefinitionWithImportedBondBO> getCurveDefinitionWithImportedBondBO(Long userid, Long curveId) {
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId, UserCurveDO::getCurveGroupId, UserCurveDO::getUserId,
                        UserCurveDO::getSpreadCurveName, UserCurveDO::getSpreadCurveType, UserCurveDO::getGenerateStatus,
                        UserCurveDO::getGenerateStartTime, UserCurveDO::getGenerateEndTime, UserCurveDO::getFilterCondition,
                        UserCurveDO::getCurveOrder, UserCurveDO::getImportedBond)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getUserId, in(userid, YieldSpreadConst.BENCHMARK_CURVE_USER_ID))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        Optional<UserCurveDO> userCurveOptional = userCurveMapper.selectFirstByDynamicQuery(query);
        return userCurveOptional.map(userCurveDO -> BeanCopyUtils.copyProperties(userCurveDO, CurveDefinitionWithImportedBondBO.class));
    }

    /**
     * 获取曲线数据，包含已经删除的
     *
     * @param curveId 曲线id
     * @return 曲线数据CurveBaseDataWithFilterConditionBO
     */
    public Optional<CurveDefinitionBO> getCurveDefinitionBOContainDeleted(Long curveId) {
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId, UserCurveDO::getCurveGroupId, UserCurveDO::getUserId,
                        UserCurveDO::getSpreadCurveName, UserCurveDO::getSpreadCurveType, UserCurveDO::getGenerateStatus,
                        UserCurveDO::getGenerateStartTime, UserCurveDO::getGenerateEndTime, UserCurveDO::getFilterCondition,
                        UserCurveDO::getCurveOrder)
                .and(UserCurveDO::getId, isEqual(curveId));
        Optional<UserCurveDO> userCurveOptional = userCurveMapper.selectFirstByDynamicQuery(query);
        return userCurveOptional.map(userCurveDO -> BeanCopyUtils.copyProperties(userCurveDO, CurveDefinitionBO.class));
    }

    /**
     * 获取曲线数据
     *
     * @param curveId 曲线id
     * @return 曲线数据
     */
    public Optional<String> getSpreadCurveData(Long curveId) {
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getSpreadCurveData)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        Optional<UserCurveDO> userCurveOptional = userCurveMapper.selectFirstByDynamicQuery(query);
        return userCurveOptional.map(UserCurveDO::getSpreadCurveData);
    }

    /**
     * 获取曲线，按更新时间倒序
     *
     * @param userid    用户id
     * @param curveName 曲线名称
     * @return 曲线数据UserCurveBO
     */
    public List<CurveDefinitionBO> listCurves(Long userid, String curveName) {
        return listCurvesBasalQuery(userid, curveName, null);
    }

    /**
     * 根据组获取曲线
     *
     * @param groupIds  组id
     * @param curveType 曲线类型
     * @param curveName 曲线名
     * @return 曲线
     */
    public List<CurveDefinitionBO> listCurvesByGroupIds(List<Long> groupIds, @Nullable CurveTypeEnum curveType, @Nullable String curveName) {
        Integer curveTypeValue = Objects.isNull(curveType) ? null : curveType.getValue();
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId, UserCurveDO::getCurveGroupId, UserCurveDO::getUserId,
                        UserCurveDO::getSpreadCurveName, UserCurveDO::getSpreadCurveType, UserCurveDO::getGenerateStatus,
                        UserCurveDO::getGenerateStartTime, UserCurveDO::getGenerateEndTime, UserCurveDO::getFilterCondition,
                        UserCurveDO::getCurveOrder)
                .and(UserCurveDO::getCurveGroupId, in(groupIds))
                .and(StringUtils.isNotBlank(curveName), UserCurveDO::getSpreadCurveName, contains(curveName))
                .and(Objects.nonNull(curveTypeValue), UserCurveDO::getSpreadCurveType, isEqual(curveTypeValue))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        query.orderBy(UserCurveDO::getCurveOrder, SortDirections::desc);
        List<UserCurveDO> curves = userCurveMapper.selectByDynamicQuery(query);
        return CollectionUtils.isEmpty(curves) ? Collections.emptyList() : BeanCopyUtils.copyList(curves, CurveDefinitionBO.class);
    }

    /**
     * 获取曲线，按更新时间倒序
     *
     * @param userid    用户id
     * @param curveType 曲线类型
     * @return 曲线数据UserCurveBO
     */
    public List<CurveDefinitionBO> listCurvesByType(Long userid, CurveTypeEnum curveType) {
        return listCurvesBasalQuery(userid, null, curveType);
    }

    /**
     * 获取自定义曲线，按更新时间倒序
     *
     * @param userid              用户id
     * @param curveGenerateStatus 曲线生成状态
     * @return 曲线数据UserCurveBO
     */
    public List<CurveDefinitionBO> listCustomCurves(@Nullable Long userid, Set<CurveGenerateStatusEnum> curveGenerateStatus) {
        return listCustomCurveDefinitionBasalQuery(userid, null, curveGenerateStatus);
    }

    /**
     * 查询曲线数据，按更新时间倒序
     *
     * @param userid    用户id
     * @param curveName 曲线名称 模糊搜索
     * @param curveType 曲线类型
     * @return 曲线数据UserCurveBO
     */
    private List<CurveDefinitionBO> listCurvesBasalQuery(@Nullable Long userid, @Nullable String curveName, @Nullable CurveTypeEnum curveType) {
        Integer curveTypeValue = curveType == null ? null : curveType.getValue();
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId, UserCurveDO::getCurveGroupId, UserCurveDO::getUserId,
                        UserCurveDO::getSpreadCurveName, UserCurveDO::getSpreadCurveType, UserCurveDO::getGenerateStatus,
                        UserCurveDO::getGenerateStartTime, UserCurveDO::getGenerateEndTime, UserCurveDO::getFilterCondition,
                        UserCurveDO::getCurveOrder)
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                .and(StringUtils.isNotBlank(curveName), UserCurveDO::getSpreadCurveName, contains(curveName))
                .and(Objects.nonNull(userid), UserCurveDO::getUserId, isEqual(userid))
                .and(Objects.nonNull(curveTypeValue), UserCurveDO::getSpreadCurveType, isEqual(curveTypeValue));
        query.orderBy(UserCurveDO::getUpdateTime, SortDirections::desc);
        List<UserCurveDO> curves = userCurveMapper.selectByDynamicQuery(query);
        return CollectionUtils.isEmpty(curves) ? Collections.emptyList() : BeanCopyUtils.copyList(curves, CurveDefinitionBO.class);
    }

    private List<CurveDefinitionBO> listCustomCurveDefinitionBasalQuery(
            @Nullable Long userid, @Nullable String curveName, @Nullable Set<CurveGenerateStatusEnum> curveGenerateStatus) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId, UserCurveDO::getCurveGroupId, UserCurveDO::getUserId,
                        UserCurveDO::getSpreadCurveName, UserCurveDO::getSpreadCurveType, UserCurveDO::getGenerateStatus,
                        UserCurveDO::getGenerateStartTime, UserCurveDO::getGenerateEndTime, UserCurveDO::getFilterCondition,
                        UserCurveDO::getCurveOrder)
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                .and(Objects.nonNull(userid), UserCurveDO::getUserId, isEqual(userid))
                .and(StringUtils.isNotBlank(curveName), UserCurveDO::getSpreadCurveName, contains(curveName));
        if (CollectionUtils.isNotEmpty(curveGenerateStatus)) {
            query.and(UserCurveDO::getGenerateStatus, in(curveGenerateStatus.stream().map(CurveGenerateStatusEnum::getValue).collect(Collectors.toList())));
        }
        query.orderBy(UserCurveDO::getUpdateTime, SortDirections::desc);
        List<UserCurveDO> curves = userCurveMapper.selectByDynamicQuery(query);
        return CollectionUtils.isEmpty(curves) ? Collections.emptyList() : BeanCopyUtils.copyList(curves, CurveDefinitionBO.class);
    }

    /**
     * 统计用户曲线数量
     *
     * @param userid 用户id
     * @return 数量
     */
    public int countUserCurve(Long userid) {
        return this.countUserCurve(userid, null);
    }

    /**
     * 统计用户曲线数量
     *
     * @param userid              用户id
     * @param curveGenerateStatus 生成状态
     * @return 数量
     */
    public int countUserCurve(Long userid, @Nullable List<CurveGenerateStatusEnum> curveGenerateStatus) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .and(UserCurveDO::getUserId, isEqual(userid))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        if (CollectionUtils.isNotEmpty(curveGenerateStatus)) {
            query.and(UserCurveDO::getGenerateStatus, in(curveGenerateStatus.stream().map(CurveGenerateStatusEnum::getValue).collect(Collectors.toList())));
        }
        return userCurveMapper.selectCountByDynamicQuery(query);
    }

    /**
     * 获取曲线类型
     *
     * @param curveId 曲线id
     * @return 曲线类型
     */
    public Optional<CurveTypeEnum> getCurveType(Long curveId) {
        this.checkCurveId(curveId);
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getSpreadCurveType)
                .and(UserCurveDO::getId, isEqual(curveId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        Optional<UserCurveDO> userCurveOptional = userCurveMapper.selectFirstByDynamicQuery(query);
        return userCurveOptional.map(userCurveDO -> EnumUtils.getEnumNullable(CurveTypeEnum.class, userCurveDO.getSpreadCurveType()));
    }

    private void checkCurveId(Long curveId) {
        if (Objects.isNull(curveId)) {
            throw new IllegalArgumentException("curveId not allowed null.");
        }
    }

    /**
     * 获取最早更新的那条曲线
     *
     * @param curveIds 曲线id
     * @return 曲线
     */
    public Optional<CurveDefinitionBO> getOldestCurve(Set<Long> curveIds) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId, UserCurveDO::getCurveGroupId, UserCurveDO::getUserId,
                        UserCurveDO::getSpreadCurveName, UserCurveDO::getSpreadCurveType, UserCurveDO::getGenerateStatus,
                        UserCurveDO::getGenerateStartTime, UserCurveDO::getGenerateEndTime, UserCurveDO::getFilterCondition,
                        UserCurveDO::getCurveOrder)
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                .and(CollectionUtils.isNotEmpty(curveIds), UserCurveDO::getId, in(curveIds));
        query.orderBy(UserCurveDO::getUpdateTime, SortDirections::asc);
        List<UserCurveDO> curves = userCurveMapper.selectByDynamicQuery(query);
        return Optional.ofNullable(CollectionUtils.isEmpty(curves) ? null : BeanCopyUtils.copyProperties(curves.get(0), CurveDefinitionBO.class));
    }

    /**
     * 根据曲线组id删除曲线
     *
     * @param groupId 曲线组id
     * @return 删除结果
     */
    public boolean deleteByGroupId(Long groupId) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .and(UserCurveDO::getCurveGroupId, isEqual(groupId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        UserCurveDO userCurve = new UserCurveDO();
        userCurve.setDeleted(Deleted.DELETED.getValue());
        return userCurveMapper.updateSelectiveByDynamicQuery(userCurve, query) != 0;
    }

    /**
     * 根据组获取曲线id
     *
     * @param groupId 组id
     * @return 曲线id
     */
    public List<Long> listCurveIdsByGroupId(Long groupId) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getId)
                .and(UserCurveDO::getCurveGroupId, isEqual(groupId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.selectByDynamicQuery(query).stream().map(UserCurveDO::getId).collect(Collectors.toList());
    }

    /**
     * 分页获取曲线
     *
     * @param pageNum  页码
     * @param pageSize 一页条数
     * @return NormPagingResult<UserCurveDO>
     */
    public NormPagingResult<UserCurveDO> pageCurves(int pageNum, int pageSize) {
        NormPagingQuery<UserCurveDO> query = NormPagingQuery.createQuery(UserCurveDO.class, pageNum, pageSize)
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.selectByNormalPaging(query);
    }

    /**
     * 获取最大序号
     *
     * @param groupId 组id
     * @return 最大序号id
     */
    public Optional<Integer> getMaxOrder(Long groupId) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .and(UserCurveDO::getCurveGroupId, isEqual(groupId))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.selectMaxByDynamicQuery(UserCurveDO::getCurveOrder, query);
    }

    /**
     * 获取比这个order小的曲线order
     *
     * @param targetGroupId  目标组
     * @param prevCurveOrder 前一条曲线order 大的在前
     * @return order
     */
    public Optional<Integer> getLessCurveOrder(Long targetGroupId, Integer prevCurveOrder) {
        DynamicQuery<UserCurveDO> query = DynamicQuery.createQuery(UserCurveDO.class)
                .select(UserCurveDO::getCurveOrder)
                .and(UserCurveDO::getCurveGroupId, isEqual(targetGroupId))
                .and(UserCurveDO::getCurveOrder, lessThan(prevCurveOrder))
                .and(UserCurveDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return userCurveMapper.selectMaxByDynamicQuery(UserCurveDO::getCurveOrder, query);
    }

}
