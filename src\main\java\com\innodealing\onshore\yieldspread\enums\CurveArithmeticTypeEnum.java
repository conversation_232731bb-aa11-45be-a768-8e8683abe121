package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 曲线算法类型
 *
 * <AUTHOR>
 */
public enum CurveArithmeticTypeEnum implements ITextValueEnum {
    /**
     * 中位数
     */
    MEDIAN(1, "中位数"),
    /**
     * 平均数
     */
    AVG(2, "平均数");

    private final Integer code;

    private final String text;

    CurveArithmeticTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }
}
