package com.innodealing.onshore.yieldspread.controller.api;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldPanoramaExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldPanoramaResponseDTO;
import com.innodealing.onshore.yieldspread.service.BondYieldPanoramaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;

/**
 * 收益率全景
 *
 * <AUTHOR>
 */
@Api(tags = "(API)收益率全景")
@RestController
@RequestMapping("api/bond/yield/panorama")
public class BondYieldPanoramaController {

    @Resource
    private BondYieldPanoramaService bondYieldPanoramaService;
    @Resource
    private HttpServletResponse httpServletResponse;

    @ApiOperation(value = "收益率全景图展示-到期收益率绝对值")
    @GetMapping(value = "/chart/abs")
    public RestResponse<YieldPanoramaResponseDTO> getYieldPanoramaAbs(
            @ApiParam(value = "利率时间", name = "spreadDate") @RequestParam Date spreadDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        return RestResponse.Success(bondYieldPanoramaService.getYieldPanoramaAbs(userId, spreadDate));
    }

    @ApiOperation(value = "收益率全景图展示-历史分位")
    @GetMapping(value = "/chart/hist-quantile")
    public RestResponse<YieldPanoramaResponseDTO> getYieldPanoramaQuantile(
            @ApiParam(name = "quantileType", value = "分位类型 1:3年，2:5年")
            @RequestParam(required = false) Integer quantileType,
            @ApiParam(value = "自定义开始时间", name = "startDate")
            @RequestParam(required = false) Date startDate,
            @ApiParam(value = "自定义结束时间", name = "endDate")
            @RequestParam(required = false) Date endDate,
            @ApiParam(value = "利率时间", name = "spreadDate") @RequestParam Date spreadDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        return RestResponse.Success(bondYieldPanoramaService.getYieldPanoramaQuantile(userId, spreadDate, quantileType, startDate, endDate));
    }

    @ApiOperation(value = "收益率全景图展示-区间变动")
    @GetMapping(value = "/chart/interval-change")
    public RestResponse<YieldPanoramaResponseDTO> getYieldPanoramaChange(
            @ApiParam(value = "变动类型(1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动 6 一日变动 7 两周变动)", name = "changeType")
            @RequestParam(value = "changeType", required = false) Integer changeType,
            @ApiParam(value = "自定义开始时间", name = "startDate")
            @RequestParam(required = false) Date startDate,
            @ApiParam(value = "自定义结束时间", name = "endDate")
            @RequestParam(required = false) Date endDate,
            @ApiParam(value = "利率时间", name = "spreadDate") @RequestParam Date spreadDate,
            @ApiParam(name = "userid", value = "用户编号", hidden = true)
            @CookieValue("userid") Long userId) {
        return RestResponse.Success(bondYieldPanoramaService.getYieldPanoramaChange(userId, spreadDate, changeType, startDate, endDate));
    }

    @ApiOperation(value = "收益率全景图展示-导出")
    @PostMapping(value = "/chart/export")
    public void exportYieldPanorama(@Validated @RequestBody BondYieldPanoramaExportReqDTO bondYieldPanoramaExportReqDTO,
                                    @ApiParam(name = "userid", value = "用户编号", hidden = true)
                                    @CookieValue("userid") Long userId) throws IOException {
        bondYieldPanoramaService.exportYieldPanorama(httpServletResponse, userId, bondYieldPanoramaExportReqDTO);
    }

    @ApiOperation(value = "收益率全景图展示-最新数据日期")
    @GetMapping(value = "/max-spread-date")
    public RestResponse<String> maxSpreadDate(){
        return RestResponse.Success(bondYieldPanoramaService.maxSpreadDate().toString());
    }
}
