package com.innodealing.onshore.yieldspread.model.bo;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户利差展示设置表实体对象
 *
 * @param <E> 配置列表对象
 * <AUTHOR>
 */
public class UserSpreadConfigBO<E> {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 配置id
     */
    private Long tabConfigId;
    /**
     * json 配置
     */
    private List<E> configDetails;


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }


    public Long getTabConfigId() {
        return tabConfigId;
    }

    public void setTabConfigId(Long tabConfigId) {
        this.tabConfigId = tabConfigId;
    }

    public List<E> getConfigDetails() {
        return Objects.isNull(configDetails) ? new ArrayList<>() : new ArrayList<>(configDetails);
    }

    public void setConfigDetails(List<E> configDetails) {
        this.configDetails = Objects.isNull(configDetails) ? new ArrayList<>() : new ArrayList<>(configDetails);
    }
}

