package com.innodealing.onshore.yieldspread.controller.internal;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.yieldspread.helper.ValidationUtil;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.dto.request.SecuCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.SecuComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.SecuSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.service.SecuBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.SecuComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;

/**
 * (内部)证券利差分析接口
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部)利差分析-证券")
@RestController
@RequestMapping("internal/secu/yield-spread")
public class InternalSecuYieldSpreadController {

    @Resource
    private SecuBondYieldSpreadService secuBondYieldSpreadService;

    @Resource
    private SecuComYieldSpreadService secuComYieldSpreadService;

    @ApiOperation(value = "证券利差分析-刷新行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard-yesterday")
    public void refreshCurveRatingShardYesterday() {
        RefreshYieldCurveParam param = new RefreshYieldCurveParam();
        param.setMvRefresh(true);
        param.setStartDate(Date.valueOf(LocalDate.now().minusDays(1)));
        param.setEndDate(Date.valueOf(LocalDate.now().minusDays(1)));
        secuBondYieldSpreadService.refreshMvSecuBondYieldSpreadRatingCurve(param);
    }

    @ApiOperation(value = "证券利差分析-刷新昨日行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard")
    public void refreshCurveRatingShard(@RequestBody RefreshYieldCurveParam param) {
        ValidationUtil.valid(param);
        secuBondYieldSpreadService.refreshMvSecuBondYieldSpreadRatingCurve(param);
    }

    @ApiOperation(value = "证券利差分析-刷新历史行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard-history")
    public void refreshCurveRatingShardHistory(@RequestParam("isTableRefresh") Boolean isTableRefresh) {
        secuBondYieldSpreadService.refreshMvSecuBondYieldSpreadRatingCurveHistory(isTableRefresh);
    }

    @PostMapping("curve-save")
    @ApiOperation(value = "保存曲线")
    public Boolean saveCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated SecuCurveGenerateConditionReqDTO requestParams) {
        return secuBondYieldSpreadService.saveCurve(userid, curveGroupId, requestParams);
    }

    @PostMapping("curve-update")
    @ApiOperation(value = "更新曲线")
    public Boolean updateCurve(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线名称") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Validated SecuCurveGenerateConditionReqDTO request) {
        return secuBondYieldSpreadService.updateCurve(userid, curveId, request);
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public NormPagingResult<SecuSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return secuBondYieldSpreadService.pagingSingleBondYieldSpreads(userid, request);
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差")
    public List<SecuComYieldSpreadResDTO> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return secuComYieldSpreadService.listComYieldSpreads(userid, request);
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public Long countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号") @RequestParam("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return secuComYieldSpreadService.countComYieldSpread(userid, request);
    }

}
