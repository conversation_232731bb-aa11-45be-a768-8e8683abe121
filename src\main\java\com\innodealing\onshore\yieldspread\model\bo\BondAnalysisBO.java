package com.innodealing.onshore.yieldspread.model.bo;

/**
 * 债券解析BO
 *
 * <AUTHOR>
 */
public class BondAnalysisBO {

    /**
     * 最初的债券code或债券简称
     */
    private String originalBondKey;

    /**
     * 最初的债券code或债券简称 md5
     */
    private String originalBondKeyMd5;

    private String bondCode;

    private Long bondUniCode;

    public String getOriginalBondKey() {
        return originalBondKey;
    }

    public void setOriginalBondKey(String originalBondKey) {
        this.originalBondKey = originalBondKey;
    }

    public String getOriginalBondKeyMd5() {
        return originalBondKeyMd5;
    }

    public void setOriginalBondKeyMd5(String originalBondKeyMd5) {
        this.originalBondKeyMd5 = originalBondKeyMd5;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

}
