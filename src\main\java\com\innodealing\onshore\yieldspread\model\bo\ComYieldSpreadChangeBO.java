package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 主体利差变动BO
 *
 * <AUTHOR>
 **/
public class ComYieldSpreadChangeBO {
    /**
     * 发行人代码
     */
    private Long comUniCode;
    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    private BigDecimal comCreditSpread;
    /**
     * 超额利差(全部债券);单位(BP)
     */
    private BigDecimal comExcessSpread;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }
}