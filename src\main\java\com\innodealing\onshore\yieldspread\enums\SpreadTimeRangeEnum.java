package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.Date;
import java.time.LocalDate;

/**
 * 页面时间范围类型枚举
 *
 * <AUTHOR>
 * @create: 2024-12-24
 */
public enum SpreadTimeRangeEnum implements ITextValueEnum {

    /**
     * 月初至今
     */
    BEGINNING_OF_MONTH_TO_NOW(1, "月初至今") {
        @Override
        public Pair<Date, Date> getDefaultTimeRange(LocalDate localDate) {
            LocalDate beginningOfMonth = localDate.withDayOfMonth(1);
            return Pair.of(Date.valueOf(beginningOfMonth), Date.valueOf(localDate));
        }
    },
    /**
     * 年初至今
     */
    BEGINNING_OF_YEAR_TO_NOW(2, "年初至今") {
        @Override
        public Pair<Date, Date> getDefaultTimeRange(LocalDate localDate) {
            LocalDate beginningOfYear = localDate.withDayOfYear(1);
            return Pair.of(Date.valueOf(beginningOfYear), Date.valueOf(localDate));
        }
    },

    ;

    private Integer code;
    private String text;

    SpreadTimeRangeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.code;
    }

    /**
     * 获取默认时间范围
     * @param localDate 日期
     * @return 时间范围
     */
    public abstract Pair<Date, Date> getDefaultTimeRange(LocalDate localDate);
}
