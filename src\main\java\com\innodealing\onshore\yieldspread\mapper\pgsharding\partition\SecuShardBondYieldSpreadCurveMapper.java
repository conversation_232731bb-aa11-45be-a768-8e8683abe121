package com.innodealing.onshore.yieldspread.mapper.pgsharding.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.SecuShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.ibatis.annotations.Param;
import org.apache.shardingsphere.api.hint.HintManager;

import javax.persistence.Table;
import java.util.List;

/**
 * 证券利差曲线-物化视图
 *
 * <AUTHOR>
 */
public interface SecuShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<SecuShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     *
     * @return tableName
     */
    default String getLogicTable() {
        return SecuShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }

    /**
     * 证券分片查询 count
     *
     * @param dynamicQuery dynamicQuery
     * @param router       路由
     * @return count
     */
    default int selectCountByDynamicQuery(@Param("dynamicQuery") DynamicQuery<SecuShardBondYieldSpreadCurveDO> dynamicQuery, AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectCountByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 证券分片删除 count
     *
     * @param dynamicQuery dynamicQuery
     * @param router       路由
     * @return count
     */
    default int deleteByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<SecuShardBondYieldSpreadCurveDO> dynamicQuery, AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return deleteByDynamicQuery(dynamicQuery);
        }
    }

    /**
     * 证券利差曲线
     *
     * @param dynamicQuery 查询条件
     * @param router       路由
     * @return list
     */
    default List<SecuShardBondYieldSpreadCurveDO> selectByDynamicQueryRouter(@Param("dynamicQuery") DynamicQuery<SecuShardBondYieldSpreadCurveDO> dynamicQuery,
                                                                             AbstractRatingRouter router) {
        try (HintManager instance = HintManager.getInstance()) {
            instance.addTableShardingValue(getLogicTable(), router);
            return selectByDynamicQuery(dynamicQuery);
        }
    }
}
