package com.innodealing.onshore.yieldspread.mapper.pgsharding.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.mv.MvInduShardBondYieldSpreadCurveDO;

import javax.persistence.Table;

/**
 * 行业利差曲线-物化视图
 *
 * <AUTHOR>
 */

public interface MvInduShardBondYieldSpreadCurveMapper extends DynamicQueryMapper<MvInduShardBondYieldSpreadCurveDO> {

    /**
     * 获取shard基础表名
     * @return tableName
     */
    default String getLogicTable() {
        return MvInduShardBondYieldSpreadCurveDO.class.getAnnotation(Table.class).name();
    }


}
