package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.QueryHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.UdicBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.UdicBondYieldSpreadgroupMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondCreditSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.UdicBondYieldSpreadGroupDO;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 城投债利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class UdicBondYieldSpreadDAO {

    @Resource
    private UdicBondYieldSpreadMapper udicBondYieldSpreadMapper;

    @Resource
    private UdicBondYieldSpreadgroupMapper udicBondYieldSpreadgroupMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    private QueryHelper queryHelper = new QueryHelper();

    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    public void createShardingTable(String tableName) {
        udicBondYieldSpreadMapper.createShardingTable(tableName);
    }

    /**
     * 根据主体分组获取城投债利差
     *
     * @param spreadDate 开始日期
     * @return 城投债利差
     */
    public List<UdicBondYieldSpreadGroupDO> listUdicBondYieldSpreadGroupDOs(Date spreadDate) {
        long startPk = ShardingUtils.getMinPkOfDate(spreadDate);
        long endPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        GroupedQuery<UdicBondYieldSpreadDO, UdicBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(UdicBondYieldSpreadDO.class, UdicBondYieldSpreadGroupDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER)
                        .select(UdicBondYieldSpreadGroupDO::getComUniCode, UdicBondYieldSpreadGroupDO::getProvinceUniCode,
                                UdicBondYieldSpreadGroupDO::getProvinceName, UdicBondYieldSpreadGroupDO::getCityName,
                                UdicBondYieldSpreadGroupDO::getCityUniCode, UdicBondYieldSpreadGroupDO::getDistrictUniCode,
                                UdicBondYieldSpreadGroupDO::getDistrictName, UdicBondYieldSpreadGroupDO::getAdministrativeRegion,
                                UdicBondYieldSpreadGroupDO::getComExtRatingMapping, UdicBondYieldSpreadGroupDO::getSpreadDate)
                        .and(UdicBondYieldSpreadDO::getId, between(startPk, endPk))
                        .and(UdicBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(g -> g.and(UdicBondYieldSpreadDO::getBondCreditSpread, notEqual(null))
                                .or(UdicBondYieldSpreadDO::getBondExcessSpread, notEqual(null)))
                        .groupBy(UdicBondYieldSpreadDO::getComUniCode);
        return udicBondYieldSpreadgroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 批量更新
     *
     * @param startDate                 开始日期
     * @param endDate                   结束日期
     * @param udicBondYieldSpreadDOList 城投债利差列表
     * @return 受影响的行数
     */
    public int saveUdicBondYieldSpreadDOList(Date startDate, Date endDate, List<UdicBondYieldSpreadDO> udicBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(udicBondYieldSpreadDOList)) {
            return 0;
        }
        long startPk = ShardingUtils.getMinPkOfDate(startDate);
        long endPk = ShardingUtils.getMaxPkOfDate(endDate);
        AtomicInteger effectRows = new AtomicInteger();
        List<Long> bondUniCodes = udicBondYieldSpreadDOList.stream().map(UdicBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 批量查询已存在的数据
        DynamicQuery<UdicBondYieldSpreadDO> query = DynamicQuery.createQuery(UdicBondYieldSpreadDO.class)
                .select(UdicBondYieldSpreadDO::getId, UdicBondYieldSpreadDO::getBondUniCode,
                        UdicBondYieldSpreadDO::getSpreadDate, UdicBondYieldSpreadDO::getProvinceUniCode,
                        UdicBondYieldSpreadDO::getProvinceName, UdicBondYieldSpreadDO::getCityUniCode,
                        UdicBondYieldSpreadDO::getCityName, UdicBondYieldSpreadDO::getAdministrativeRegion,
                        UdicBondYieldSpreadDO::getDistrictName, UdicBondYieldSpreadDO::getDistrictUniCode,
                        UdicBondYieldSpreadDO::getAdministrativeDivision)
                .and(UdicBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(UdicBondYieldSpreadDO::getSpreadDate, isEqual(startDate))
                .and(UdicBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<UdicBondYieldSpreadDO> existDataList = udicBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(udicBondYieldSpreadDOList));
        } else {
            Map<String, UdicBondYieldSpreadDO> existUdicBondYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                    x.getBondUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<UdicBondYieldSpreadDO> insertList = new ArrayList<>();
            List<UdicBondYieldSpreadDO> updateList = new ArrayList<>();
            for (UdicBondYieldSpreadDO udicBondYieldSpreadDO : udicBondYieldSpreadDOList) {
                UdicBondYieldSpreadDO existUdicBondYieldSpreadDO = existUdicBondYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                udicBondYieldSpreadDO.getBondUniCode(), udicBondYieldSpreadDO.getSpreadDate().getTime()));
                if (Objects.isNull(existUdicBondYieldSpreadDO)) {
                    insertList.add(udicBondYieldSpreadDO);
                } else {
                    udicBondYieldSpreadDO.setId(existUdicBondYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existUdicBondYieldSpreadDO.getProvinceUniCode(),
                            udicBondYieldSpreadDO::setProvinceUniCode);
                    ObjectExtensionUtils.ifNonNull(existUdicBondYieldSpreadDO.getProvinceName(), udicBondYieldSpreadDO::setProvinceName);
                    ObjectExtensionUtils.ifNonNull(existUdicBondYieldSpreadDO.getCityUniCode(), udicBondYieldSpreadDO::setCityUniCode);
                    ObjectExtensionUtils.ifNonNull(existUdicBondYieldSpreadDO.getCityName(), udicBondYieldSpreadDO::setCityName);
                    ObjectExtensionUtils.ifNonNull(existUdicBondYieldSpreadDO.getDistrictName(), udicBondYieldSpreadDO::setDistrictName);
                    ObjectExtensionUtils.ifNonNull(existUdicBondYieldSpreadDO.getDistrictUniCode(), udicBondYieldSpreadDO::setDistrictUniCode);
                    ObjectExtensionUtils.ifNonNull(existUdicBondYieldSpreadDO.getAdministrativeRegion(),
                            udicBondYieldSpreadDO::setAdministrativeRegion);
                    ObjectExtensionUtils.ifNonNull(existUdicBondYieldSpreadDO.getAdministrativeDivision(),
                            udicBondYieldSpreadDO::setAdministrativeDivision);
                    updateList.add(udicBondYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 查询发行人唯一编码
     *
     * @param searchParameter 查询发行人唯一编码请求参数
     * @return {@link Set}<{@link Long}> 发行人唯一编码集合
     */
    public Set<Long> listComUniCodes(UdicBondYieldSpreadParamDTO searchParameter) {
        if (isNull(searchParameter)) {
            return Collections.emptySet();
        }
        if (Objects.nonNull(searchParameter.getComUniCode())) {
            return SetUtils.hashSet(searchParameter.getComUniCode());
        }
        if (ArrayUtils.isNotEmpty(searchParameter.getComUniCodes())) {
            return SetUtils.hashSet(searchParameter.getComUniCodes());
        }
        DynamicQuery<UdicBondYieldSpreadDO> query = DynamicQuery.createQuery(UdicBondYieldSpreadDO.class)
                .selectDistinct(UdicBondYieldSpreadDO::getComUniCode)
                .and(this.listCommonFilters(searchParameter));
        return udicBondYieldSpreadMapper.selectByDynamicQuery(query).stream().map(UdicBondYieldSpreadDO::getComUniCode).collect(Collectors.toSet());
    }

    /**
     * 城投单券利差分页查询
     *
     * @param searchParameter 城投单券利差分页查询请求参数
     * @return {@link NormPagingResult}<{@link UdicBondYieldSpreadDO}> 城投单券利差分页查询响应数据
     */
    public NormPagingResult<UdicBondYieldSpreadDO> getBondYieldSpreadPaging(UdicBondYieldSpreadParamDTO searchParameter) {
        if (Objects.isNull(searchParameter)) {
            return new NormPagingResult<>();
        }
        NormPagingQuery<UdicBondYieldSpreadDO> query = NormPagingQuery
                .createQuery(UdicBondYieldSpreadDO.class, searchParameter.getPageNum(), searchParameter.getPageSize())
                .and(this.listCommonFilters(searchParameter));
        if (Objects.nonNull(searchParameter.getSort())) {
            SortDTO sort = searchParameter.getSort();
            String columnName = queryHelper.getQueryColumnByProperty(UdicBondYieldSpreadDO.class, sort.getPropertyName());
            query.addSorts(new CustomSortDescriptor(columnName + " IS NULL"));
            query.addSorts(new SortDescriptor(sort.getPropertyName(), sort.getSortDirection()));
        }
        return udicBondYieldSpreadMapper.selectByNormalPaging(query);
    }

    /**
     * 批量更新
     *
     * @param updateList 城投债利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<UdicBondYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<UdicBondYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(UdicBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (UdicBondYieldSpreadDO udicBondYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<UdicBondYieldSpreadDO> updateQuery = DynamicQuery.createQuery(UdicBondYieldSpreadDO.class)
                        .and(UdicBondYieldSpreadDO::getId, isEqual(udicBondYieldSpreadDO.getId()));
                mapper.updateByDynamicQuery(udicBondYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 城投债利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<UdicBondYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<UdicBondYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(UdicBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (UdicBondYieldSpreadDO udicBondYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(udicBondYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    private BaseFilterDescriptor<UdicBondYieldSpreadDO>[] listCommonFilters(UdicBondYieldSpreadParamDTO searchParameter) {
        long startPk = ShardingUtils.getMinPkOfDate(searchParameter.getSpreadDate());
        long endPk = ShardingUtils.getMaxPkOfDate(searchParameter.getSpreadDate());
        FilterGroupDescriptor<UdicBondYieldSpreadDO> filterGroup = FilterGroupDescriptor.create(UdicBondYieldSpreadDO.class)
                .and(UdicBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(isNotEmpty(searchParameter.getComUniCodes()), UdicBondYieldSpreadDO::getComUniCode, in(searchParameter.getComUniCodes()))
                .and(isNotEmpty(searchParameter.getBondUniCodes()), UdicBondYieldSpreadDO::getBondUniCode, in(searchParameter.getBondUniCodes()))
                .and(UdicBondYieldSpreadDO::getSpreadDate, isEqual(searchParameter.getSpreadDate()))
                .and(nonNull(searchParameter.getComUniCode()), UdicBondYieldSpreadDO::getComUniCode, isEqual(searchParameter.getComUniCode()))
                .and(nonNull(searchParameter.getBondUniCode()), UdicBondYieldSpreadDO::getBondUniCode, isEqual(searchParameter.getBondUniCode()))
                .and(nonNull(searchParameter.getBondExtRatingMapping()), UdicBondYieldSpreadDO::getBondExtRatingMapping, isEqual(searchParameter.getBondExtRatingMapping()))
                .and(nonNull(searchParameter.getSpreadBondType()), UdicBondYieldSpreadDO::getSpreadBondType, isEqual(searchParameter.getSpreadBondType()))
                .and(nonNull(searchParameter.getGuaranteeStatus()) && 0 == searchParameter.getGuaranteeStatus(),
                        g -> g.and(UdicBondYieldSpreadDO::getGuaranteedStatus, isEqual(searchParameter.getGuaranteeStatus()))
                                .or(UdicBondYieldSpreadDO::getGuaranteedStatus, isEqual(null)))
                .and(nonNull(searchParameter.getGuaranteeStatus()) && 1 == searchParameter.getGuaranteeStatus(),
                        g -> g.and(UdicBondYieldSpreadDO::getGuaranteedStatus, isEqual(searchParameter.getGuaranteeStatus())))
                .and(nonNull(searchParameter.getProvinceUniCode()), UdicBondYieldSpreadDO::getProvinceUniCode, isEqual(searchParameter.getProvinceUniCode()))
                .and(nonNull(searchParameter.getCityUniCode()), UdicBondYieldSpreadDO::getCityUniCode, isEqual(searchParameter.getCityUniCode()))
                .and(nonNull(searchParameter.getDistrictUniCode()), UdicBondYieldSpreadDO::getDistrictUniCode, isEqual(searchParameter.getDistrictUniCode()))
                .and(nonNull(searchParameter.getSpreadRemainingTenorTag()), UdicBondYieldSpreadDO::getSpreadRemainingTenorTag,
                        isEqual(searchParameter.getSpreadRemainingTenorTag()))
                .and(isNotEmpty(searchParameter.getComYyRatingMappings()), UdicBondYieldSpreadDO::getComYyRatingMapping, in(searchParameter.getComYyRatingMappings()))
                .and(isNotEmpty(searchParameter.getBondImpliedRatingMappings()), UdicBondYieldSpreadDO::getBondImpliedRatingMapping,
                        in(searchParameter.getBondImpliedRatingMappings()))
                .and(nonNull(searchParameter.getAdministrativeDivision()), UdicBondYieldSpreadDO::getAdministrativeDivision, isEqual(searchParameter.getAdministrativeDivision()))
                .and(UdicBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return filterGroup.getFilters();
    }

    /**
     * 查询单券信用利差
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link BondCreditSpreadBO}> 信用利差数据集
     */
    public List<BondCreditSpreadBO> listBondCreditSpreads(Date spreadDate, Set<Long> bondUniCodes) {
        if (Objects.isNull(spreadDate) || CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        Long minId = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxId = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<UdicBondYieldSpreadDO> query = DynamicQuery.createQuery(UdicBondYieldSpreadDO.class)
                .select(UdicBondYieldSpreadDO::getBondUniCode, UdicBondYieldSpreadDO::getSpreadDate,
                        UdicBondYieldSpreadDO::getBondCreditSpread)
                .and(UdicBondYieldSpreadDO::getId, between(minId, maxId))
                .and(UdicBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(UdicBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<UdicBondYieldSpreadDO> udicBondYieldSpreadList = udicBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(udicBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(udicBondYieldSpreadList, BondCreditSpreadBO.class);
    }

    /**
     * 获取债券
     *
     * @return 债券
     */
    public List<UdicBondYieldSpreadGroupDO> listBonds() {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(YieldSpreadConst.CURVE_START_DATE);
        Long manPkOfDate = ShardingUtils.getMaxPkOfDate(Date.valueOf(LocalDate.now()));
        GroupedQuery<UdicBondYieldSpreadDO, UdicBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(UdicBondYieldSpreadDO.class, UdicBondYieldSpreadGroupDO.class)
                        .select(UdicBondYieldSpreadGroupDO::getComUniCode,
                                UdicBondYieldSpreadGroupDO::getBondUniCode,
                                UdicBondYieldSpreadGroupDO::getBondCode)
                        .and(UdicBondYieldSpreadDO::getId, between(minPkOfDate, manPkOfDate))
                        .and(UdicBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                        .groupBy(UdicBondYieldSpreadDO::getBondUniCode);
        return udicBondYieldSpreadgroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 查询城投利差数据集合
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}{@link UdicBondYieldSpreadDO}
     */
    public List<UdicBondYieldSpreadDO> listUdicBondYieldSpreads(@NonNull Date spreadDate, @NonNull Set<Long> bondUniCodes) {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPkOfDate = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<UdicBondYieldSpreadDO> query = DynamicQuery.createQuery(UdicBondYieldSpreadDO.class)
                .and(UdicBondYieldSpreadDO::getId, between(minPkOfDate, maxPkOfDate))
                .and(UdicBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        return udicBondYieldSpreadMapper.selectByDynamicQuery(query);
    }

}
