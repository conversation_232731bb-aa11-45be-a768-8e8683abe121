<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.yieldspread.InsuComYieldSpreadMapper">
    <select id="listComYieldSpreads"
            resultType="com.innodealing.onshore.yieldspread.model.bo.InsuComYieldSpreadBO">
        SELECT
        <if test="isNewest">
            com_change.credit_spread_change_3m,
            com_change.credit_spread_change_6m,
            com_change.excess_spread_change_3m,
            com_change.excess_spread_change_6m,
            com_change.credit_spread_quantile_3y,
            com_change.credit_spread_quantile_5y,
            com_change.excess_spread_quantile_3y,
            com_change.excess_spread_quantile_5y,
        </if>
        insu.com_uni_code,
        insu.business_nature ,
        insu.spread_date,
        insu.com_tier2_credit_spread,
        insu.com_tier2_excess_spread,
        insu.com_tier2_cb_yield,
        insu.com_ext_rating_mapping,
        insu.indu_level2_name,
        insu.total_assets,
        insu.net_profit,
        insu.com_credit_spread,
        insu.com_perpetual_credit_spread,
        insu.com_excess_spread,
        insu.com_perpetual_excess_spread,
        insu.com_cb_yield,
        insu.com_perpetual_cb_yield
        FROM insu_com_yield_spread insu
        INNER JOIN (
        select distinct com_uni_code from insu_bond_yield_spread_${params.year}
        where spread_date = #{params.spreadDate}
        <if test="params.spreadBondType != null">
            and insurance_seniority_ranking = #{params.spreadBondType}
        </if>
        <if test="params.businessFilterNatures != null and params.businessFilterNatures.size() > 0">
            and business_filter_nature in
            <foreach collection="params.businessFilterNatures" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.remainingTenor != null">
            and spread_remaining_tenor_tag = #{params.remainingTenor}
        </if>
        <if test="params.bondImpliedRatingMappings != null and params.bondImpliedRatingMappings.length >0">
            and bond_implied_rating_mapping in
            <foreach collection="params.bondImpliedRatingMappings" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.comUniCode != null">
            and com_uni_code = #{params.comUniCode}
        </if>
        <if test="params.bondUniCode != null">
            and bond_uni_code = #{params.bondUniCode}
        </if>
        ) tmp
        on insu.com_uni_code = tmp.com_uni_code
        <if test="isNewest">
            LEFT JOIN com_yield_spread_change com_change ON insu.com_uni_code =com_change.com_uni_code
            and insu.spread_date = com_change.spread_date
            AND com_change.deleted = 0
            and com_change.com_spread_sector = 7
        </if>
        WHERE insu.spread_date = #{params.spreadDate} AND insu.deleted = 0
        <if test="params.sort != null">
            order by ${params.sort.propertyName} ${params.sort.sortDirection}
        </if>
        limit #{params.startIndex},#{params.pageSize}
    </select>

    <select id="countComYieldSpread" resultType="long">
        SELECT COUNT(*)
        FROM insu_com_yield_spread
        INNER JOIN (
        select distinct com_uni_code from insu_bond_yield_spread_${params.year}
        where spread_date = #{params.spreadDate}
        <if test="params.spreadBondType != null">
            and insurance_seniority_ranking = #{params.spreadBondType}
        </if>
        <if test="params.businessFilterNatures != null and params.businessFilterNatures.size() > 0">
            and business_filter_nature in
            <foreach collection="params.businessFilterNatures" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.remainingTenor != null">
            and spread_remaining_tenor_tag = #{params.remainingTenor}
        </if>
        <if test="params.bondImpliedRatingMappings != null and params.bondImpliedRatingMappings.length >0">
            and bond_implied_rating_mapping in
            <foreach collection="params.bondImpliedRatingMappings" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.comUniCode != null">
            and com_uni_code = #{params.comUniCode}
        </if>
        <if test="params.bondUniCode != null">
            and bond_uni_code = #{params.bondUniCode}
        </if>
        ) tmp
        on insu_com_yield_spread.com_uni_code = tmp.com_uni_code
        WHERE (insu_com_yield_spread.spread_date = #{params.spreadDate} AND insu_com_yield_spread.deleted = 0)
    </select>

    <select id="listComYieldQuantileStatisticsViews"
            resultType="com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO">
        SELECT
        t1.com_uni_code,
        CASE WHEN t2.com_credit_spread is not null THEN COUNT(t1.com_credit_spread &lt; t2.com_credit_spread or null)
        ELSE NULL END comCreditSpreadLessIssueCount,
        CASE WHEN t2.com_credit_spread is not null THEN COUNT(t1.com_credit_spread) ELSE NULL END comCreditSpreadCount,
        CASE WHEN t2.com_excess_spread is not null THEN COUNT(t1.com_excess_spread &lt; t2.com_excess_spread or null)
        ELSE NULL END comExcessSpreadLessIssueCount,
        CASE WHEN t2.com_excess_spread is not null THEN COUNT(t1.com_excess_spread) ELSE NULL END comExcessSpreadCount
        FROM
        insu_com_yield_spread AS t1
        INNER JOIN insu_com_yield_spread t2 ON t1.com_uni_code = t2.com_uni_code
        WHERE
        t1.spread_date >= #{startDate}
        AND t1.spread_date &lt;= #{endDate}
        AND t1.deleted = 0
        AND t2.spread_date = #{issueDate}
        AND t2.deleted = 0
        <if test="comUniCodeList != null and comUniCodeList.size >0">
            AND t2.com_uni_code IN
            <foreach collection="comUniCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        t1.com_uni_code
    </select>
</mapper>