package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvInduBondYieldSpreadPanoramaDO;

/**
 * 行业利差全景-物化视图
 *
 * <AUTHOR>
 */
public interface MvInduBondYieldSpreadPanoramaMapper extends DynamicQueryMapper<MvInduBondYieldSpreadPanoramaDO> {

    /**
     * 刷新行业利差全景物化视图
     */
    void refreshMvInduBondYieldSpreadPanorama();
}
