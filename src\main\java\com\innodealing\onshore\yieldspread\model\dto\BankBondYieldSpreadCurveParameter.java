package com.innodealing.onshore.yieldspread.model.dto;

import java.sql.Date;
import java.util.List;

/**
 * 银行利差曲线物化视图创建参数
 * <AUTHOR>
 */
@SuppressWarnings({"squid:S2384"})

public class BankBondYieldSpreadCurveParameter {

    private String tableName;

    private Date startDate;

    private Date endDate;

    private List<Integer> impliedRatingMappings;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<Integer> getImpliedRatingMappings() {
        return impliedRatingMappings;
    }

    public void setImpliedRatingMappings(List<Integer> impliedRatingMappings) {
        this.impliedRatingMappings = impliedRatingMappings;
    }
}
