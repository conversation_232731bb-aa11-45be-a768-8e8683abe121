package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO;
import org.apache.ibatis.annotations.Param;

/**
 * 银行债利差Mapper
 *
 * <AUTHOR>
 **/
public interface BankBondYieldSpreadMapper extends DynamicQueryMapper<BankBondYieldSpreadDO> {
    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    void createShardingTable(@Param("tableName") String tableName);

}
