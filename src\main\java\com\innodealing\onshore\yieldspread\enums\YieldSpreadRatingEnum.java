package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import org.springframework.lang.NonNull;

import java.util.EnumSet;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 利差评级枚举
 *
 * <AUTHOR>
 */
public enum YieldSpreadRatingEnum implements ITextValueEnum {
    /**
     * 评级
     */
    AAA_PLUS(1, "AAA+", YieldSpreadCurveCodeEnum.getAAAPlusRatings()),
    AAA(2, "AAA", YieldSpreadCurveCodeEnum.getAAARatings()),
    AAA_SUB(3, "AAA-", YieldSpreadCurveCodeEnum.getAAASubRatings()),
    AA_PLUS(4, "AA+", YieldSpreadCurveCodeEnum.getAAPlusRatings()),
    AA(5, "AA", YieldSpreadCurveCodeEnum.getAARatings()),
    AA_TWO(6, "AA(2)", YieldSpreadCurveCodeEnum.getAATwoRatings()),
    AA_SUB(7, "AA-", YieldSpreadCurveCodeEnum.getAASubRatings());

    private final Integer code;
    private final String text;
    private final Set<YieldSpreadCurveCodeEnum> curveCodeEnums;

    /**
     * 构造函数
     *
     * @param code           code
     * @param text           text
     * @param curveCodeEnums curveCodeEnums
     */
    @SuppressWarnings("squid:UnusedPrivateMethod")
    YieldSpreadRatingEnum(Integer code, String text, Set<YieldSpreadCurveCodeEnum> curveCodeEnums) {
        this.code = code;
        this.text = text;
        this.curveCodeEnums = new HashSet<>(curveCodeEnums);
    }

    private static final Map<YieldSpreadCurveCodeEnum, YieldSpreadRatingEnum> CURVE_CODE_ENUM_YIELD_SPREAD_RATING_ENUM_MAP = new ConcurrentHashMap<>();

    static {
        for (YieldSpreadRatingEnum rating : values()) {
            rating.getCurveCodeEnums().forEach(curveCodeEnum -> CURVE_CODE_ENUM_YIELD_SPREAD_RATING_ENUM_MAP.put(curveCodeEnum, rating));
        }
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }

    public Set<YieldSpreadCurveCodeEnum> getCurveCodeEnums() {
        return EnumSet.copyOf(curveCodeEnums);
    }

    /**
     * 根据曲线代码获取评级数据
     *
     * @param curveCodeEnum 曲线代码枚举
     * @return {@link YieldSpreadRatingEnum}
     */
    public static YieldSpreadRatingEnum getRatingByCurveCode(@NonNull YieldSpreadCurveCodeEnum curveCodeEnum) {
        return CURVE_CODE_ENUM_YIELD_SPREAD_RATING_ENUM_MAP.get(curveCodeEnum);
    }
}
