package com.innodealing.onshore.yieldspread.processor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.onshore.bondmetadata.enums.CurveCode;
import com.innodealing.onshore.yieldspread.enums.BondImpliedRatingCurveCodeMappingEnum;
import com.innodealing.onshore.yieldspread.enums.CurveUniCodeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.*;

/**
 * 利差追踪-品种利差处理器
 *
 * <AUTHOR>
 */
@Component
public class YieldSpreadTraceVarietyProcessor implements YieldSpreadTraceProcessor, InitializingBean {

    private static final Set<YieldPanoramaBondTypeEnum> SUPPORT_BOND_TYPES = EnumSet.of(INDUSTRIAL_BOND, URBAN_BOND,
            GENERAL_BANK_BOND, BANK_SECONDARY_CAPITAL_BOND, BANK_PERPETUAL_BOND, SECURITIES_BOND,
            INSU_CAPITAL_SUPPLEMENT);

    private final Map<Integer, Integer> curveCodeMap = Maps.newHashMap();

    @Override
    public void afterPropertiesSet() {
        curveCodeMap.put(CurveCode.CHINA_CT_AAA.getValue(), CurveCode.CHINA_BOND_MID.getValue());
        curveCodeMap.put(CurveCode.CHINA_BOND_CT_AA_PLUS.getValue(), CurveCode.CHINA_BOND_MID_AA_PLUS.getValue());
        curveCodeMap.put(CurveCode.CHINA_BOND_CT_AA.getValue(), CurveCode.CHINA_BOND_MID_AA.getValue());
        curveCodeMap.put(CurveCode.CHINA_BOND_CT_AA_SUB.getValue(), CurveCode.CHINA_BOND_MID_AA_SUB.getValue());

        curveCodeMap.put(BondImpliedRatingCurveCodeMappingEnum.INDU_BOND_AAA.getCurveCode(), CurveCode.CHINA_BOND_MID.getValue());
        curveCodeMap.put(BondImpliedRatingCurveCodeMappingEnum.INDU_BOND_AA_PLUS.getCurveCode(), CurveCode.CHINA_BOND_MID_AA_PLUS.getValue());
        curveCodeMap.put(BondImpliedRatingCurveCodeMappingEnum.INDU_BOND_AA.getCurveCode(), CurveCode.CHINA_BOND_MID_AA.getValue());
        curveCodeMap.put(BondImpliedRatingCurveCodeMappingEnum.INDU_BOND_AAA_MINUS.getCurveCode(), CurveCode.CHINA_BOND_MID_AAA_SUB.getValue());

        curveCodeMap.put(CurveCode.CHINA_BOND_ORD.getValue(), CurveCode.CHINA_BOND_MID.getValue());
        curveCodeMap.put(CurveCode.GENERAL_BANK_BOND_AAA_SUB.getValue(), CurveCode.CHINA_BOND_MID_AAA_SUB.getValue());
        curveCodeMap.put(CurveCode.GENERAL_BANK_BOND_AA_PLUS.getValue(), CurveCode.CHINA_BOND_MID_AA_PLUS.getValue());
        curveCodeMap.put(CurveCode.GENERAL_BANK_BOND_AA.getValue(), CurveCode.CHINA_BOND_MID_AA.getValue());
        curveCodeMap.put(CurveCode.GENERAL_BANK_BOND_AA_SUB.getValue(), CurveCode.CHINA_BOND_MID_AA_SUB.getValue());

        curveCodeMap.put(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AAA_SUB.getValue(), CurveCode.CHINA_BOND_MID_AAA_SUB.getValue());
        curveCodeMap.put(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA_PLUS.getValue(), CurveCode.CHINA_BOND_MID_AA_PLUS.getValue());
        curveCodeMap.put(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA.getValue(), CurveCode.CHINA_BOND_MID_AA.getValue());
        curveCodeMap.put(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA_SUB.getValue(), CurveCode.CHINA_BOND_MID_AA_SUB.getValue());

        curveCodeMap.put(CurveCode.BANK_PERPETUAL_BOND_AAA_SUB.getValue(), CurveCode.CHINA_BOND_MID_AAA_SUB.getValue());
        curveCodeMap.put(CurveCode.BANK_PERPETUAL_BOND_AA_PLUS.getValue(), CurveCode.CHINA_BOND_MID_AA_PLUS.getValue());
        curveCodeMap.put(CurveCode.BANK_PERPETUAL_BOND_AA.getValue(), CurveCode.CHINA_BOND_MID_AA.getValue());
        curveCodeMap.put(CurveCode.BANK_PERPETUAL_BOND_AA_SUB.getValue(), CurveCode.CHINA_BOND_MID_AA_SUB.getValue());

        curveCodeMap.put(CurveCode.SECURITIES_BOND_AAA.getValue(), CurveCode.CHINA_BOND_MID.getValue());
        curveCodeMap.put(CurveCode.SECURITIES_BOND_AAA_SUB.getValue(), CurveCode.CHINA_BOND_MID_AAA_SUB.getValue());
        curveCodeMap.put(CurveCode.SECURITIES_BOND_AA_PLUS.getValue(), CurveCode.CHINA_BOND_MID_AA_PLUS.getValue());
        curveCodeMap.put(CurveCode.SECURITIES_BOND_AA.getValue(), CurveCode.CHINA_BOND_MID_AA.getValue());

        curveCodeMap.put(CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA_PLUS.getCurveCode(), CurveCode.CHINA_BOND_MID_AA_PLUS.getValue());
        curveCodeMap.put(CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA.getCurveCode(), CurveCode.CHINA_BOND_MID_AA.getValue());
        curveCodeMap.put(CurveUniCodeEnum.INSU_CAPITAL_SUPPLY_BOND_AA_SUB.getCurveCode(), CurveCode.CHINA_BOND_MID_AA_SUB.getValue());
    }

    @Override
    public Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum) {
        return SUPPORT_BOND_TYPES.contains(traceBondTypeEnum);
    }

    @Override
    public List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context) {
        YieldPanoramaBondTypeEnum bondTypeEnum = context.getBondTypeEnum();
        List<PgBondYieldPanoramaAbsDO> referentBondMidYieldPanoramas = context.getReferentBondYieldPanoramas();
        if (CollectionUtils.isEmpty(referentBondMidYieldPanoramas)) {
            return Collections.emptyList();
        }
        Map<Integer, PgBondYieldPanoramaAbsDO> referentCurveCodeMap =
                referentBondMidYieldPanoramas.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        Map<Integer, PgBondYieldPanoramaAbsDO> absCurveCodeMap =
                context.getAbsBondYieldPanoramas().stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        List<PgBondYieldSpreadTraceAbsDO> dataList = Lists.newArrayListWithExpectedSize(absCurveCodeMap.size());
        for (Map.Entry<Integer, PgBondYieldPanoramaAbsDO> absEntry : absCurveCodeMap.entrySet()) {
            PgBondYieldPanoramaAbsDO absBondYieldPanorama = absEntry.getValue();
            Integer curveCodeMapping = curveCodeMap.getOrDefault(absEntry.getKey(), -1);
            if (Objects.isNull(curveCodeMapping)) {
                continue;
            }
            PgBondYieldPanoramaAbsDO referentAbs = referentCurveCodeMap.getOrDefault(curveCodeMapping, new PgBondYieldPanoramaAbsDO());

            PgBondYieldSpreadTraceAbsDO pgBondYieldSpreadTraceAbsDO = convertAbsSubtractToTrace(absBondYieldPanorama, referentAbs, bondTypeEnum,
                    context.getIssueDate(), absEntry.getKey(), YieldSpreadChartTypeEnum.VARIETY_SPREAD);
            dataList.add(pgBondYieldSpreadTraceAbsDO);
        }
        return dataList;
    }
}
