<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgUdicBondYieldSpreadMapper">
    <select id="listAreaBondYieldSpreads"
            resultType="com.innodealing.onshore.yieldspread.model.bo.PgUdicAreaYieldSpreadBO">
        SELECT province_uni_code area_uni_code,
               (median(bond_credit_spread)::DOUBLE PRECISION) ::DECIMAL AS bond_credit_spread
        FROM udic_bond_yield_spread
        WHERE spread_date = #{spreadDate}
          AND province_uni_code IS NOT NULL
          AND province_uni_code!=712345678
        GROUP BY province_uni_code
        UNION ALL
        SELECT city_uni_code area_uni_code,
               (median(bond_credit_spread)::DOUBLE PRECISION) ::DECIMAL AS bond_credit_spread
        FROM udic_bond_yield_spread
        WHERE spread_date = #{spreadDate}
          AND city_uni_code IS NOT NULL
        GROUP BY city_uni_code
        UNION ALL
        SELECT district_uni_code area_uni_code,
               (median(bond_credit_spread)::DOUBLE PRECISION) ::DECIMAL AS bond_credit_spread
        FROM udic_bond_yield_spread
        WHERE spread_date = #{spreadDate}
          AND district_uni_code IS NOT NULL
        GROUP BY district_uni_code
    </select>
</mapper>