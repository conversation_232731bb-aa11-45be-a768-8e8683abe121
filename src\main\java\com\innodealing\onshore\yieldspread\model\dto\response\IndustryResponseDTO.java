package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 行业响应DTO
 *
 * <AUTHOR>
 */
public class IndustryResponseDTO  implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("行业代码")
    private Long industryCode;

    @ApiModelProperty("行业名称")
    private String industryName;

    /**
     * 行业响应DTO构造函数
     *
     * @param industryCode 行业代码
     * @param industryName 行业名称
     */
    public IndustryResponseDTO(Long industryCode, String industryName) {
        this.industryCode = industryCode;
        this.industryName = industryName;
    }

    public Long getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(Long industryCode) {
        this.industryCode = industryCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }
}
