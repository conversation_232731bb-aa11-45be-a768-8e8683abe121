package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 曲线分位线
 *
 * <AUTHOR>
 */
public class CurvePercentileResDTO {

    @ApiModelProperty("信用利差25分位线")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadPercentile25;

    @ApiModelProperty("信用利差75分位线")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadPercentile75;

    @ApiModelProperty("超额利差25分位线")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadPercentile25;

    @ApiModelProperty("超额利差75分位线")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadPercentile75;

    @ApiModelProperty("估值收益率25分位线")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal cbYieldPercentile25;

    @ApiModelProperty("估值收益率75分位线")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal cbYieldPercentile75;

    public BigDecimal getCreditSpreadPercentile25() {
        return creditSpreadPercentile25;
    }

    public void setCreditSpreadPercentile25(BigDecimal creditSpreadPercentile25) {
        this.creditSpreadPercentile25 = creditSpreadPercentile25;
    }

    public BigDecimal getCreditSpreadPercentile75() {
        return creditSpreadPercentile75;
    }

    public void setCreditSpreadPercentile75(BigDecimal creditSpreadPercentile75) {
        this.creditSpreadPercentile75 = creditSpreadPercentile75;
    }

    public BigDecimal getExcessSpreadPercentile25() {
        return excessSpreadPercentile25;
    }

    public void setExcessSpreadPercentile25(BigDecimal excessSpreadPercentile25) {
        this.excessSpreadPercentile25 = excessSpreadPercentile25;
    }

    public BigDecimal getExcessSpreadPercentile75() {
        return excessSpreadPercentile75;
    }

    public void setExcessSpreadPercentile75(BigDecimal excessSpreadPercentile75) {
        this.excessSpreadPercentile75 = excessSpreadPercentile75;
    }

    public BigDecimal getCbYieldPercentile25() {
        return cbYieldPercentile25;
    }

    public void setCbYieldPercentile25(BigDecimal cbYieldPercentile25) {
        this.cbYieldPercentile25 = cbYieldPercentile25;
    }

    public BigDecimal getCbYieldPercentile75() {
        return cbYieldPercentile75;
    }

    public void setCbYieldPercentile75(BigDecimal cbYieldPercentile75) {
        this.cbYieldPercentile75 = cbYieldPercentile75;
    }

}
