package com.innodealing.onshore.yieldspread.controller.api;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.yieldspread.model.dto.request.SecuCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.SecuComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.SecuSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.service.SecuBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.SecuComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 证券利差
 *
 * <AUTHOR>
 */
@Api(tags = "(API)证券利差")
@RestController
@Validated
@RequestMapping("api/secu/yield-spread")
public class SecurityYieldSpreadController {

    @Resource
    private SecuBondYieldSpreadService secuBondYieldSpreadService;

    @Resource
    private SecuComYieldSpreadService secuComYieldSpreadService;

    @PostMapping("curve-save")
    @ApiOperation(value = "保存曲线")
    public RestResponse<Boolean> saveCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveGroupId", value = "曲线组id")
            @RequestParam(name = "curveGroupId", required = false) Long curveGroupId,
            @RequestBody @Validated SecuCurveGenerateConditionReqDTO requestParams) {
        return RestResponse.Success(secuBondYieldSpreadService.saveCurve(userid, curveGroupId, requestParams));
    }

    @PostMapping("curve-update")
    @ApiOperation(value = "更新曲线")
    public RestResponse<Boolean> updateCurve(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @ApiParam(name = "curveId", value = "曲线名称") @RequestParam(name = "curveId") Long curveId,
            @RequestBody @Validated SecuCurveGenerateConditionReqDTO request) {
        return RestResponse.Success(secuBondYieldSpreadService.updateCurve(userid, curveId, request));
    }

    @PostMapping("/paging/single-bond")
    @ApiOperation(value = "表区-单券利差")
    public RestResponse<NormPagingResult<SecuSingleBondYieldSpreadResDTO>> pagingSingleBondYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(secuBondYieldSpreadService.pagingSingleBondYieldSpreads(userid, request));
    }

    @PostMapping("/list-com")
    @ApiOperation(value = "表区-主体利差")
    public RestResponse<List<SecuComYieldSpreadResDTO>> listComYieldSpreads(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(secuComYieldSpreadService.listComYieldSpreads(userid, request));
    }

    @PostMapping("/count-com")
    @ApiOperation(value = "表区-主体利差条数")
    public RestResponse<Long> countComYieldSpread(
            @ApiParam(name = "userid", value = "用户编号", hidden = true) @CookieValue("userid") Long userid,
            @RequestBody @Validated YieldSpreadSearchReqDTO request) {
        return RestResponse.Success(secuComYieldSpreadService.countComYieldSpread(userid, request));
    }

}
