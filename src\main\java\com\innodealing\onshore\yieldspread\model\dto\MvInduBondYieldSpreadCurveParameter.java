package com.innodealing.onshore.yieldspread.model.dto;

import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 产业利差曲线物化视图创建参数
 *
 * <AUTHOR>
 */
public class MvInduBondYieldSpreadCurveParameter {

    private String tableName;

    private AbstractRatingRouter.SpreadDateRange spreadDateRange;

    private List<Integer> impliedRatingMappings;

    private List<Integer> yyRatingMappings;

    private String induLevel;


    public String getInduLevel() {
        return induLevel;
    }

    public void setInduLevel(String induLevel) {
        this.induLevel = induLevel;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public AbstractRatingRouter.SpreadDateRange getSpreadDateRange() {
        return spreadDateRange;
    }

    public void setSpreadDateRange(AbstractRatingRouter.SpreadDateRange spreadDateRange) {
        this.spreadDateRange = spreadDateRange;
    }

    public List<Integer> getImpliedRatingMappings() {
        return Objects.isNull(impliedRatingMappings) ? new ArrayList<>() : new ArrayList<>(impliedRatingMappings);
    }

    public void setImpliedRatingMappings(List<Integer> impliedRatingMappings) {
        this.impliedRatingMappings = Objects.isNull(impliedRatingMappings) ? new ArrayList<>() : new ArrayList<>(impliedRatingMappings);
    }

    public List<Integer> getYyRatingMappings() {
        return Objects.isNull(yyRatingMappings) ? new ArrayList<>() : new ArrayList<>(yyRatingMappings);
    }

    public void setYyRatingMappings(List<Integer> yyRatingMappings) {
        this.yyRatingMappings = Objects.isNull(yyRatingMappings) ? new ArrayList<>() : new ArrayList<>(yyRatingMappings);
    }
}
