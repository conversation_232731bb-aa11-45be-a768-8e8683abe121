package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 利差历史分位类型
 *
 * <AUTHOR>
 */
public enum SpreadQuantileTypeEnum implements ITextValueEnum {
    /**
     * 3年历史分位
     */
    THREE_YEARS_QUANTILE(1, "3年历史分位", 3),
    /**
     * 5年分位数
     */
    FIVE_YEARS_QUANTILE(2, "5年历史分位", 5),
    /**
     * 1年分位数
     */
    ONE_YEARS_QUANTILE(3, "1年历史分位", 1),
    ;

    private final int value;
    private final String text;
    private final int year;

    SpreadQuantileTypeEnum(int value, String text, int year) {
        this.value = value;
        this.text = text;
        this.year = year;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }

    /**
     * 获取 年数
     * @return 年数
     */
    public int getYear() {
        return year;
    }
}
