package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO;
import org.apache.ibatis.annotations.Param;

/**
 * 证券债利差Mapper
 *
 * <AUTHOR>
 **/
public interface SecuBondYieldSpreadMapper extends DynamicQueryMapper<SecuBondYieldSpreadDO> {
    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    void createShardingTable(@Param("tableName") String tableName);

}
