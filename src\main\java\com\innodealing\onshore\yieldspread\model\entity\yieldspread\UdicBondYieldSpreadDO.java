package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * 城投债利差
 *
 * <AUTHOR>
 **/
@Table(name = "udic_bond_yield_spread")
public class UdicBondYieldSpreadDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;
    /**
     * 债券统一编码
     */
    @Column
    private Long bondUniCode;
    /**
     * 债券编码
     */
    @Column
    private String bondCode;
    /**
     * 省份编码
     */
    @Column
    private Long provinceUniCode;
    /**
     * 省份名称
     */
    @Column
    private String provinceName;
    /**
     * 地级市编码
     */
    @Column
    private Long cityUniCode;
    /**
     * 城市名称
     */
    @Column
    private String cityName;
    /**
     * 区县编码
     */
    @Column
    private Long districtUniCode;
    /**
     * 区县名称
     */
    @Column
    private String districtName;
    /**
     * 行政区域
     */
    @Column
    private Integer administrativeRegion;
    /**
     * 行政区划
     */
    @Column
    private Integer administrativeDivision;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 债券信用利差;单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpread;
    /**
     * 债券超额利差;单位(BP)
     */
    @Column
    private BigDecimal bondExcessSpread;
    /**
     * 中债收益率;单位(%)
     */
    @Column
    private BigDecimal cbYield;
    /**
     * 国开插值收益率;单位(%)
     */
    @Column
    private BigDecimal cdbLerpYield;
    /**
     * 隐含评级对应曲线插值收益率;单位(%)
     */
    @Column
    private BigDecimal impliedRatingLerpYield;
    /**
     * 超额利差数据状态 0有效 1没有评级曲线
     */
    @Column
    private Integer excessSpreadStatus;
    /**
     * 债券外部评级映射
     */
    @Column
    private Integer bondExtRatingMapping;
    /**
     * 债项隐含评级映射
     */
    @Column
    private Integer bondImpliedRatingMapping;
    /**
     * yy评级(投资级:1、2、3、4、5,投机级:6、7、8、风险级:9、10)
     */
    @Column
    private Integer comYyRatingMapping;
    /**
     * 主体外部评级映射
     */
    @Column
    private Integer comExtRatingMapping;
    /**
     * 利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer spreadBondType;
    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;
    /**
     * 剩余期限
     */
    @Column
    private String remainingTenor;
    /**
     * 剩余期限天数
     */
    @Column
    private Integer remainingTenorDay;
    /**
     * 担保状态: 0: 无; 1: 有
     */
    @Column
    private Integer guaranteedStatus;
    /**
     * 最新票面利率;单位(%)
     */
    @Column
    private BigDecimal latestCouponRate;
    /**
     * 债券余额(万)
     */
    @Column
    private BigDecimal bondBalance;
    /**
     * 是否删除：0： 未删除;1：已删除
     */
    @Column
    private Integer deleted;

    public Long getDistrictUniCode() {
        return districtUniCode;
    }

    public void setDistrictUniCode(Long districtUniCode) {
        this.districtUniCode = districtUniCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getAdministrativeRegion() {
        return administrativeRegion;
    }

    public void setAdministrativeRegion(Integer administrativeRegion) {
        this.administrativeRegion = administrativeRegion;
    }

    public Integer getAdministrativeDivision() {
        return administrativeDivision;
    }

    public void setAdministrativeDivision(Integer administrativeDivision) {
        this.administrativeDivision = administrativeDivision;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(BigDecimal cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

    public BigDecimal getImpliedRatingLerpYield() {
        return impliedRatingLerpYield;
    }

    public void setImpliedRatingLerpYield(BigDecimal impliedRatingLerpYield) {
        this.impliedRatingLerpYield = impliedRatingLerpYield;
    }

    public Integer getExcessSpreadStatus() {
        return excessSpreadStatus;
    }

    public void setExcessSpreadStatus(Integer excessSpreadStatus) {
        this.excessSpreadStatus = excessSpreadStatus;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer getBondImpliedRatingMapping() {
        return bondImpliedRatingMapping;
    }

    public void setBondImpliedRatingMapping(Integer bondImpliedRatingMapping) {
        this.bondImpliedRatingMapping = bondImpliedRatingMapping;
    }

    public Integer getComYyRatingMapping() {
        return comYyRatingMapping;
    }

    public void setComYyRatingMapping(Integer comYyRatingMapping) {
        this.comYyRatingMapping = comYyRatingMapping;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public Integer getRemainingTenorDay() {
        return remainingTenorDay;
    }

    public void setRemainingTenorDay(Integer remainingTenorDay) {
        this.remainingTenorDay = remainingTenorDay;
    }

    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }

    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

}