package com.innodealing.onshore.yieldspread.service.impl;

import com.github.wz2cool.dynamic.SortDirection;
import com.google.common.collect.Sets;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.utils.AssertUtils;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.consts.TipsConst;
import com.innodealing.onshore.yieldspread.dao.yieldspread.YieldSpreadCompositeSearchDAO;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadCodeTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.LocalCache;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionWithImportedBondBO;
import com.innodealing.onshore.yieldspread.model.bo.CustomComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.CustomYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CustomComYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.service.CustomComYieldSpreadService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.MAX_CUSTOM_COM_SPREAD_DATE_KEY;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.SPREAD_CUSTOM_COM_SPREAD_COUNT_KEY;

/**
 * 银行主体利差 Service
 *
 * <AUTHOR>
 **/
@Service
public class CustomComYieldSpreadServiceImpl extends AbstractComYieldSpreadService implements CustomComYieldSpreadService {

    @Resource
    private YieldSpreadCompositeSearchDAO yieldSpreadCompositeSearchDAO;

    @Resource
    private LocalCache localCache;

    @Override
    public List<CustomComYieldSpreadResDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        Optional<CurveDefinitionWithImportedBondBO> curveOptional = userCurveDAO.getCurveDefinitionWithImportedBondBO(userid, request.getCurveId());
        CurveDefinitionWithImportedBondBO curve = curveOptional.orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        Set<Long> bondUniCodes = new HashSet<>(JSON.parseArray(curve.getImportedBond(), Long.class));
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        Long searchBondUnicode = request.getUniCode();
        if (Objects.nonNull(searchBondUnicode) && request.getUniCodeType().equals(SpreadCodeTypeEnum.BOND_CODE.getValue())) {
            if (bondUniCodes.contains(searchBondUnicode)) {
                bondUniCodes = Sets.newHashSet(searchBondUnicode);
            } else {
                return Collections.emptyList();
            }
        }
        List<CustomComYieldSpreadBO> comYieldSpreads = yieldSpreadCompositeSearchDAO
                .listComYieldSpreads(this.buildTableAreaSearchParam(bondUniCodes, request));
        if (CollectionUtils.isEmpty(comYieldSpreads)) {
            return Collections.emptyList();
        }
        Set<Long> comUniCodes = comYieldSpreads.stream().map(CustomComYieldSpreadBO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return comYieldSpreads.stream().map(com -> {
            CustomComYieldSpreadResDTO response = BeanCopyUtils.copyProperties(com, CustomComYieldSpreadResDTO.class);
            response.setComUniName(comShortInfoMap.containsKey(response.getComUniCode()) ?
                    comShortInfoMap.get(response.getComUniCode()).getComChiName() : StringUtils.EMPTY);
            response.setComExtRatingMappingStr(RatingUtils.getRating(com.getComExtRatingMapping()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request) {
        AssertUtils.isTrue(userCurveDAO.isExist(request.getCurveId(), userid, Boolean.TRUE), TipsConst.CURVE_NOT_EXIST);
        return super.getComCountFromRedis(request, SPREAD_CUSTOM_COM_SPREAD_COUNT_KEY,
                () -> {
                    Set<Long> bondUniCodes = this.listBondUniCodes(userid, request);
                    if (CollectionUtils.isEmpty(bondUniCodes)) {
                        return 0L;
                    }
                    return yieldSpreadCompositeSearchDAO.countComYieldSpread(this.buildTableAreaSearchParam(bondUniCodes, request));
                });
    }

    private Set<Long> listBondUniCodes(Long userid, YieldSpreadSearchReqDTO request) {
        Optional<CurveDefinitionWithImportedBondBO> curveOptional = userCurveDAO.getCurveDefinitionWithImportedBondBO(userid, request.getCurveId());
        CurveDefinitionWithImportedBondBO curve = curveOptional.orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        Set<Long> bondUniCodes = new HashSet<>(JSON.parseArray(curve.getImportedBond(), Long.class));
        Long searchBondUnicode = request.getUniCode();
        if (Objects.nonNull(searchBondUnicode) && request.getUniCodeType().equals(SpreadCodeTypeEnum.BOND_CODE.getValue())) {
            if (bondUniCodes.contains(searchBondUnicode)) {
                bondUniCodes = Sets.newHashSet(searchBondUnicode);
            } else {
                return Collections.emptySet();
            }
        }
        return bondUniCodes;
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.CUSTOMIZATION;
    }

    @Override
    protected Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_CUSTOM_COM_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        return Date.valueOf(LocalDate.now());
    }

    private CustomYieldSearchParam buildTableAreaSearchParam(Set<Long> bondUniCodes, YieldSpreadSearchReqDTO request) {
        CustomYieldSearchParam searchParam = new CustomYieldSearchParam();
        searchParam.setStartIndex((request.getPageNum() - 1) * request.getPageSize());
        searchParam.setPageSize(request.getPageSize());
        searchParam.setPageNum(request.getPageNum());
        SortDTO sort = request.getSort();
        if (Objects.isNull(sort) || StringUtils.isBlank(sort.getPropertyName())) {
            searchParam.setSort(new SortDTO("com_credit_spread", SortDirection.DESC));
        } else {
            searchParam.setSort(YieldSpreadHelper.convertSortPropertyToTableColumName(sort, BankComYieldSpreadDO.class));
        }
        searchParam.setBondUniCodes(bondUniCodes);
        Map<Integer, Set<Long>> sectorBondUniCodesMap = localCache.convertToSectorBondUniCodesMap(bondUniCodes);
        searchParam.setUdicBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.UDIC.getValue()));
        searchParam.setInduBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.INDU.getValue()));
        searchParam.setBankBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.BANK.getValue()));
        searchParam.setSecuBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.SECU.getValue()));
        searchParam.setInsuBondUniCodes(sectorBondUniCodesMap.get(ComYieldSpreadSectorEnum.INSU.getValue()));
        if (Objects.isNull(request.getSpreadDate()) || DateExtensionUtils.isSameDay(request.getSpreadDate(), new Date(System.currentTimeMillis()))) {
            searchParam.setSpreadDate(this.getMaxSpreadDate());
        } else {
            searchParam.setSpreadDate(request.getSpreadDate());
        }
        if (Objects.nonNull(request.getUniCodeType()) && Objects.nonNull(request.getUniCode()) && request.getUniCodeType().equals(SpreadCodeTypeEnum.COM_CODE.getValue())) {
            searchParam.setComUniCode(request.getUniCode());
        }
        searchParam.setYear(searchParam.getSpreadDate().toLocalDate().getYear());
        return searchParam;
    }

}
