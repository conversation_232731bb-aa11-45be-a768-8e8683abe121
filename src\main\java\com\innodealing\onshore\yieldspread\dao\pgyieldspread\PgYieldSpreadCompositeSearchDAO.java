package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgYieldSpreadCompositeSearchMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;
import java.util.Set;

import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.divideHundredThousand;

/**
 * Pg利差综合查询
 *
 * <AUTHOR>
 */
@Repository
public class PgYieldSpreadCompositeSearchDAO {

    @Resource
    private PgYieldSpreadCompositeSearchMapper pgYieldSpreadCompositeSearchMapper;

    /**
     * 根据bondUniCode获取债券利差，会涉及到城投、产业、银行、证券 、保险
     *
     * @param bondUniCodes 债券code
     * @param spreadDate   利差日期 为空就是所有日期
     * @return 利差数据
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public List<BondYieldSpreadBO> listBondYieldSpreads(Set<Long> bondUniCodes, @Nullable Date spreadDate) {
        pgYieldSpreadCompositeSearchMapper.setStatementTimeout(YieldSpreadConst.GENERATE_CURVE_PERMIT_LEASE_TIME);
        List<BondYieldSpreadBO> bondYieldSpreads = pgYieldSpreadCompositeSearchMapper.listBondYieldSpreads(bondUniCodes, spreadDate);
        if (CollectionUtils.isNotEmpty(bondYieldSpreads)) {
            bondYieldSpreads.forEach(ys -> {
                ObjectExtensionUtils.ifNonNull(ys.getBondCreditSpread(), v -> divideHundredThousand(v).ifPresent(ys::setBondCreditSpread));
                ObjectExtensionUtils.ifNonNull(ys.getBondExcessSpread(), v -> divideHundredThousand(v).ifPresent(ys::setBondExcessSpread));
                ObjectExtensionUtils.ifNonNull(ys.getCbYield(), v -> divideHundredThousand(v, YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE).ifPresent(ys::setCbYield));
            });
        }
        return bondYieldSpreads;
    }

}
