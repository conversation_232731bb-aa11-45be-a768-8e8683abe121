package com.innodealing.onshore.yieldspread.controller.impl;

import com.innodealing.onshore.yieldspread.controller.MaterializedViewRefreshController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 基于Redis的物化视图刷新控制器实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisBasedMaterializedViewRefreshController implements MaterializedViewRefreshController {
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    /**
     * Redis键前缀
     */
    private static final String MV_REFRESH_PREFIX = "mv:refresh:control:";
    
    /**
     * 刷新状态键前缀
     */
    private static final String MV_STATUS_PREFIX = "mv:refresh:status:";
    
    /**
     * 默认TTL（24小时）
     */
    private static final long DEFAULT_TTL_SECONDS = 24 * 60 * 60;
    
    /**
     * 刷新间隔（1小时）
     */
    private static final long REFRESH_INTERVAL_SECONDS = 60 * 60;
    
    @Override
    public boolean shouldRefreshMaterializedView(String viewName) {
        try {
            String controlKey = MV_REFRESH_PREFIX + viewName;
            String statusKey = MV_STATUS_PREFIX + viewName;
            
            // 检查是否被禁用
            String controlValue = stringRedisTemplate.opsForValue().get(controlKey);
            if ("disabled".equals(controlValue)) {
                log.debug("物化视图刷新被禁用: {}", viewName);
                return false;
            }
            
            // 检查上次刷新时间
            String lastRefreshTime = stringRedisTemplate.opsForValue().get(statusKey);
            if (lastRefreshTime != null) {
                LocalDateTime lastRefresh = LocalDateTime.parse(lastRefreshTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                LocalDateTime now = LocalDateTime.now();
                
                // 如果距离上次刷新不足1小时，跳过刷新
                if (lastRefresh.plusSeconds(REFRESH_INTERVAL_SECONDS).isAfter(now)) {
                    log.debug("物化视图刷新间隔未到: {}, 上次刷新时间: {}", viewName, lastRefreshTime);
                    return false;
                }
            }
            
            // 如果被强制启用或者没有控制记录，则允许刷新
            return true;
            
        } catch (Exception e) {
            log.warn("检查物化视图刷新状态失败: {}, 默认允许刷新", viewName, e);
            return true;
        }
    }
    
    @Override
    public void markMaterializedViewRefreshed(String viewName) {
        try {
            String statusKey = MV_STATUS_PREFIX + viewName;
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            
            stringRedisTemplate.opsForValue().set(statusKey, currentTime, DEFAULT_TTL_SECONDS, TimeUnit.SECONDS);
            log.debug("标记物化视图刷新完成: {}, 时间: {}", viewName, currentTime);
            
        } catch (Exception e) {
            log.warn("标记物化视图刷新状态失败: {}", viewName, e);
        }
    }
    
    @Override
    public void enableMaterializedViewRefresh(String viewName, long ttlSeconds) {
        try {
            String controlKey = MV_REFRESH_PREFIX + viewName;
            stringRedisTemplate.opsForValue().set(controlKey, "enabled", ttlSeconds, TimeUnit.SECONDS);
            log.info("启用物化视图刷新: {}, TTL: {}秒", viewName, ttlSeconds);
            
        } catch (Exception e) {
            log.error("启用物化视图刷新失败: {}", viewName, e);
        }
    }
    
    @Override
    public void disableMaterializedViewRefresh(String viewName, long ttlSeconds) {
        try {
            String controlKey = MV_REFRESH_PREFIX + viewName;
            stringRedisTemplate.opsForValue().set(controlKey, "disabled", ttlSeconds, TimeUnit.SECONDS);
            log.info("禁用物化视图刷新: {}, TTL: {}秒", viewName, ttlSeconds);
            
        } catch (Exception e) {
            log.error("禁用物化视图刷新失败: {}", viewName, e);
        }
    }
    
    @Override
    public String getMaterializedViewRefreshStatus(String viewName) {
        try {
            String controlKey = MV_REFRESH_PREFIX + viewName;
            String statusKey = MV_STATUS_PREFIX + viewName;
            
            String controlValue = stringRedisTemplate.opsForValue().get(controlKey);
            String lastRefreshTime = stringRedisTemplate.opsForValue().get(statusKey);
            
            StringBuilder status = new StringBuilder();
            status.append("视图: ").append(viewName);
            status.append(", 控制状态: ").append(controlValue != null ? controlValue : "未设置");
            status.append(", 上次刷新: ").append(lastRefreshTime != null ? lastRefreshTime : "未知");
            
            return status.toString();
            
        } catch (Exception e) {
            log.warn("获取物化视图刷新状态失败: {}", viewName, e);
            return "状态获取失败: " + viewName;
        }
    }
    
    @Override
    public void clearMaterializedViewRefreshControl(String viewName) {
        try {
            String controlKey = MV_REFRESH_PREFIX + viewName;
            String statusKey = MV_STATUS_PREFIX + viewName;
            
            stringRedisTemplate.delete(controlKey);
            stringRedisTemplate.delete(statusKey);
            log.info("清除物化视图刷新控制: {}", viewName);
            
        } catch (Exception e) {
            log.error("清除物化视图刷新控制失败: {}", viewName, e);
        }
    }
    
    @Override
    public List<String> shouldRefreshMaterializedViews(List<String> viewNames) {
        return viewNames.stream()
                .filter(this::shouldRefreshMaterializedView)
                .collect(Collectors.toList());
    }
}
