package com.innodealing.onshore.yieldspread.service.impl;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComCurrentBondsBalanceDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondfinance.response.ComFinanceSheetResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.udic.UdicComInfoForSpreadDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.enums.AdministrativeRegionEnum;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.dao.dmdc.ComInterestCtzHistDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgUdicBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.UdicComYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UdicComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.view.UdicComYieldSpreadViewDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.ComCreditSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.MixYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.dto.ComYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.UdicYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.UdicCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.UdicListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicComYieldSpreadResponseDTO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.ComInterestCtzHistDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgUdicBondYieldSpreadGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.view.UdicComYieldSpreadDynamicView;
import com.innodealing.onshore.yieldspread.model.view.UdicComYieldSpreadView;
import com.innodealing.onshore.yieldspread.service.UdicComYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.internal.BondFinanceService;
import com.innodealing.onshore.yieldspread.service.internal.BondInfoService;
import com.innodealing.onshore.yieldspread.service.internal.UdicInfoService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.helper.CommonsHelper.getPropertyName;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;

/**
 * 城投主体利差 Service
 *
 * <AUTHOR>
 **/
@SuppressWarnings({"squid:S1854", "squid:S1481", "squid:S138"})
@Service
public class UdicComYieldSpreadServiceImpl extends AbstractComYieldSpreadService implements UdicComYieldSpreadService {

    private static final int WORK_THREAD_NUM = 8;

    private static final int COM_MAX_LIMIT = 200;

    private final ExecutorService executorService;

    @Resource
    private UdicComYieldSpreadDAO udicComYieldSpreadDAO;

    @Resource
    private PgUdicBondYieldSpreadDAO pgUdicBondYieldSpreadDAO;

    @Resource
    private BondFinanceService bondFinanceService;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private UdicInfoService udicInfoService;

    @Resource
    private ComInterestCtzHistDAO comInterestCtzHistDAO;

    @Resource
    private UdicComYieldSpreadViewDAO udicComYieldSpreadViewDAO;

    @Resource
    private UdicComYieldSpreadRedisDAO udicComYieldSpreadRedisDAO;

    private static final SortDTO DEFAULT_SORT =
            new SortDTO(getPropertyName(UdicComYieldSpreadResponseDTO::getComCreditSpread), SortDirection.DESC);

    protected UdicComYieldSpreadServiceImpl() {
        executorService = TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("UdicComYieldSpreadServiceImpl-pool-").build()));
    }

    private static final Set<String> HISTORY_ORDER_FIELD_SET;

    static {
        HISTORY_ORDER_FIELD_SET = new HashSet<>();
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPublicCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPrivateCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPerpetualCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComExcessSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPublicExcessSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPrivateExcessSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPerpetualExcessSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComCbYield));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPublicCbYield));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPrivateCbYield));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicComYieldSpreadResponseDTO::getComPerpetualCbYield));
    }

    @Override
    public Integer calcUdicComYieldSpreadsBySpreadDate(List<UdicComYieldSpreadDO> comYieldSpreadDOs, Date spreadDate,
                                                       Boolean isEnableOldData) {
        if (CollectionUtils.isEmpty(comYieldSpreadDOs)) {
            return 0;
        }
        Set<Long> comUniCodes = comYieldSpreadDOs.stream().map(UdicComYieldSpreadDO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        CompletableFuture<Map<Long, ComCurrentBondsBalanceDTO>> submitComCurrentBondsBalanceDTO = of.submit(() ->
                bondInfoService.getComCurrentBondsBalanceDTOMap(comUniCodes));
        CompletableFuture<Map<Long, ComFinanceSheetResponseDTO>> submitComFinanceSheetResponseDTO = of.submit(() ->
                bondFinanceService.getComFinanceLatestYearReportMap(spreadDate, comUniCodes));
        CompletableFuture<Map<Long, UdicComInfoForSpreadDTO>> submitUdicComInfoForSpreadDTO = of.submit(() ->
                udicInfoService.getComInfoForSpreadDTOMap(comUniCodes));
        // 计算中位数(全部)
        CompletableFuture<Map<Long, PgUdicBondYieldSpreadGroupDO>> submitYieldSpread = of.submit(() ->
                pgUdicBondYieldSpreadDAO.getUdicBondYieldSpreadMap(comUniCodes, null, spreadDate));
        // 计算中位数(公募)
        CompletableFuture<Map<Long, PgUdicBondYieldSpreadGroupDO>> submitYieldSpreadIsPublicOffering = of
                .submit(() -> pgUdicBondYieldSpreadDAO.
                        getUdicBondYieldSpreadMap(comUniCodes, SpreadBondTypeEnum.SPREAD_IS_PUBLIC_OFFERING.getValue(), spreadDate));
        // 计算中位数(私募)
        CompletableFuture<Map<Long, PgUdicBondYieldSpreadGroupDO>> submitYieldSpreadNotPublicOffering = of
                .submit(() -> pgUdicBondYieldSpreadDAO.getUdicBondYieldSpreadMap(comUniCodes,
                        SpreadBondTypeEnum.SPREAD_NOT_PUBLIC_OFFERING.getValue(), spreadDate));
        // 计算中位数(永续)
        CompletableFuture<Map<Long, PgUdicBondYieldSpreadGroupDO>> submitYieldSpreadPerpetual = of.submit(() -> pgUdicBondYieldSpreadDAO.
                getUdicBondYieldSpreadMap(comUniCodes, SpreadBondTypeEnum.SPREAD_PERPETUAL.getValue(), spreadDate));
        CompletableFuture<List<ComInterestCtzHistDO>> submitComInterestCtzHistDO = of.submit(() -> comInterestCtzHistDAO.
                listComInterestCtzHistDOByInterestDate(spreadDate, comUniCodes));
        of.doWorks(submitComCurrentBondsBalanceDTO, submitUdicComInfoForSpreadDTO,
                submitComFinanceSheetResponseDTO, submitYieldSpread, submitYieldSpreadIsPublicOffering,
                submitYieldSpreadNotPublicOffering, submitYieldSpreadPerpetual, submitComInterestCtzHistDO);
        // 获取主体存续信息
        Map<Long, ComCurrentBondsBalanceDTO> comCurrentBondsBalanceDTOMap = submitComCurrentBondsBalanceDTO.join();
        // 获取主体财报
        Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap = submitComFinanceSheetResponseDTO.join();
        // 获取城投主体信息
        Map<Long, UdicComInfoForSpreadDTO> udicComInfoForSpreadDTOMap = submitUdicComInfoForSpreadDTO.join();
        // 获取中位数(全部、公募、私募、永续)
        Map<Long, PgUdicBondYieldSpreadGroupDO> yieldSpreadMap = submitYieldSpread.join();
        Map<Long, PgUdicBondYieldSpreadGroupDO> yieldSpreadIsPublicOfferingMap = submitYieldSpreadIsPublicOffering.join();
        Map<Long, PgUdicBondYieldSpreadGroupDO> yieldSpreadNotPublicOfferingMap = submitYieldSpreadNotPublicOffering.join();
        Map<Long, PgUdicBondYieldSpreadGroupDO> yieldSpreadPerpetualMap = submitYieldSpreadPerpetual.join();
        Map<Long, ComInterestCtzHistDO> comInterestCtzHistDOMap = submitComInterestCtzHistDO.join().stream()
                .collect(Collectors.toMap(ComInterestCtzHistDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
        List<UdicComYieldSpreadDO> comYieldSpreadDOSaves = new ArrayList<>();
        for (UdicComYieldSpreadDO comYieldSpreadDO : comYieldSpreadDOs) {
            if (DateExtensionUtils.isSameDay(spreadDate, YieldSpreadHelper.getYesterDay())) {
                comYieldSpreadDO = fillUdicComExistenceColumn(comYieldSpreadDO, comCurrentBondsBalanceDTOMap);
            }
            comYieldSpreadDO = fillUdicFinanceColumn(comYieldSpreadDO, comFinanceSheetResponseDTOMap);
            comYieldSpreadDO = fillUdicComInfoColumn(comYieldSpreadDO, udicComInfoForSpreadDTOMap);
            comYieldSpreadDO = fillUdicComSpreadColumn(comYieldSpreadDO, yieldSpreadMap, yieldSpreadIsPublicOfferingMap,
                    yieldSpreadNotPublicOfferingMap, yieldSpreadPerpetualMap);
            if (isEnableOldData) {
                comYieldSpreadDO = fillOldColumn(comYieldSpreadDO, comInterestCtzHistDOMap);
            }
            comYieldSpreadDO.setDeleted(0);
            comYieldSpreadDOSaves.add(comYieldSpreadDO);
        }
        return udicComYieldSpreadDAO.saveUdicComYieldSpreadDOList(spreadDate, comYieldSpreadDOSaves);
    }

    @Override
    public NormPagingResult<UdicComYieldSpreadResponseDTO> getComYieldSpreadPaging(UdicListRequestDTO request) {
        Date spreadDate = request.getSpreadDate();
        boolean isToday = false;
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
            isToday = true;
        }
        UdicBondYieldSpreadParamDTO searchParameter = UdicBondYieldSpreadParamDTO.builder()
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now()))
                .sortOrDefault(request.getSort(), DEFAULT_SORT)
                .page(request.getPageNum(), request.getPageSize())
                .searchCode(request.getCodeType(), request.getSearchCode())
                .comUniCodes(request.getComUniCodes())
                .bondUniCodes(request.getBondUniCodes())
                .compositionCondition(request.getCompositionCondition()).build();
        NormPagingResult<UdicComYieldSpreadResponseDTO> result = new NormPagingResult<>();
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());
        result.setList(listComYieldSpreads(searchParameter, isToday));
        return result;
    }

    private List<UdicComYieldSpreadResponseDTO> listComYieldSpreads(UdicBondYieldSpreadParamDTO searchParameter, boolean isToday) {
        SortDTO sort = searchParameter.getSort();
        // 所传日期为今天需要查询view视图数据，需要查询三月变动，六月变动数据
        List<? extends UdicComYieldSpreadDO> result;
        if (isToday) {
            result = udicComYieldSpreadDAO.getComYieldSpreadChangePagingByJoin(searchParameter);
        } else {
            searchParameter.setSort(sort != null && HISTORY_ORDER_FIELD_SET.contains(sort.getPropertyName()) ? sort : DEFAULT_SORT);
            result = udicComYieldSpreadDAO.getComYieldSpreadPagingByJoin(searchParameter);
        }
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        Set<Long> comUniCodes = result.stream().map(UdicComYieldSpreadDO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return result.stream().map(com -> {
            UdicComYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(com, UdicComYieldSpreadResponseDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName()
                    : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            AdministrativeRegionEnum.getEnumByValue(response.getAdministrativeRegion())
                    .ifPresent(region -> response.setAdministrativeRegionName(region.getText()));
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public NormPagingResult<UdicComYieldSpreadResponseDTO> getComYieldSpreadPagingByExists(UdicListRequestDTO request) {
        Date spreadDate = request.getSpreadDate();
        boolean isToday = false;
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
            isToday = true;
        }
        UdicBondYieldSpreadParamDTO searchParameter = UdicBondYieldSpreadParamDTO.builder()
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now()))
                .sortOrDefault(request.getSort(), DEFAULT_SORT)
                .page(request.getPageNum(), request.getPageSize())
                .searchCode(request.getCodeType(), request.getSearchCode())
                .comUniCodes(request.getComUniCodes())
                .bondUniCodes(request.getBondUniCodes())
                .compositionCondition(request.getCompositionCondition()).build();
        SortDTO sort = searchParameter.getSort();
        // 所传日期为今天需要查询view视图数据，需要查询三月变动，六月变动数据
        NormPagingResult<? extends UdicComYieldSpreadDO> pagingResult;
        if (isToday) {
            pagingResult = udicComYieldSpreadDAO.getComYieldSpreadChangePagingByExists(searchParameter);
        } else {
            searchParameter.setSort(HISTORY_ORDER_FIELD_SET.contains(sort.getPropertyName()) ? sort : DEFAULT_SORT);
            pagingResult = udicComYieldSpreadDAO.getComYieldSpreadPagingByExists(searchParameter);
        }
        if (CollectionUtils.isEmpty(pagingResult.getList())) {
            return new NormPagingResult<>();
        }
        Set<Long> comUniCodes = pagingResult.getList().stream().map(UdicComYieldSpreadDO::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return pagingResult.convert(com -> {
            UdicComYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(com, UdicComYieldSpreadResponseDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName()
                    : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            AdministrativeRegionEnum.getEnumByValue(response.getAdministrativeRegion())
                    .ifPresent(region -> response.setAdministrativeRegionName(region.getText()));
            return response;
        });
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.UDIC;
    }

    @Override
    public Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_UDIC_COM_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = udicComYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_UDIC_COM_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }

    @Override
    public Long getComYieldSpreadPagingCount(UdicListRequestDTO request) {
        Date spreadDate = request.getSpreadDate();
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
        }
        UdicListRequestDTO copyRequest = BeanCopyUtils.copyProperties(request, UdicListRequestDTO.class);
        copyRequest.setSpreadDate(spreadDate);
        String key = String.format(SPREAD_UDIC_COM_SPREAD_COUNT_KEY, copyRequest.toString().hashCode());
        String cacheValue = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(cacheValue)) {
            return Long.parseLong(cacheValue);
        }
        UdicBondYieldSpreadParamDTO searchParameter = UdicBondYieldSpreadParamDTO.builder()
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now()))
                .sortOrDefault(request.getSort(), DEFAULT_SORT)
                .page(request.getPageNum(), request.getPageSize())
                .searchCode(request.getCodeType(), request.getSearchCode())
                .comUniCodes(request.getComUniCodes())
                .bondUniCodes(request.getBondUniCodes())
                .compositionCondition(request.getCompositionCondition()).build();
        Long count = udicComYieldSpreadDAO.getComYieldSpreadPagingCount(searchParameter);
        stringRedisTemplate.opsForValue().set(key, String.valueOf(count), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return count;
    }

    @Override
    public List<UdicComYieldSpreadResponseDTO> listComYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        UdicCurveGenerateConditionReqDTO generateRequest = getCurveGenerateCondition(userid, request.getCurveId(),
                UdicCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return Collections.emptyList();
        }
        boolean isToday = super.isToday(request.getSpreadDate());
        UdicYieldSearchParam param = this.buildUdicYieldSearchParam(request, generateRequest);
        List<UdicComYieldSpreadView> udicComYieldSpreads = udicComYieldSpreadDAO.listComYieldSpreads(isToday, param);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(udicComYieldSpreads)) {
            return Collections.emptyList();
        }
        Set<Long> comUniCodes = udicComYieldSpreads.stream().map(UdicComYieldSpreadView::getComUniCode).collect(Collectors.toSet());
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(comUniCodes);
        return udicComYieldSpreads.stream().map(com -> {
            UdicComYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(com, UdicComYieldSpreadResponseDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName()
                    : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            AdministrativeRegionEnum.getEnumByValue(response.getAdministrativeRegion())
                    .ifPresent(region -> response.setAdministrativeRegionName(region.getText()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getHideDebt()).ifPresent(response::setHideDebt);
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            return response;
        }).collect(Collectors.toList());
    }

    private UdicYieldSearchParam buildUdicYieldSearchParam(YieldSpreadSearchReqDTO request, UdicCurveGenerateConditionReqDTO generateRequest) {
        UdicYieldSearchParam param = super.buildComYieldSearchParam(request, generateRequest, UdicYieldSearchParam.class, UdicComYieldSpreadDO.class);
        Long areaCode = generateRequest.getAreaCode();
        return super.buildUdicYieldSearchParamForArea(param, areaCode);
    }

    @Override
    public Long countComYieldSpread(Long userid, YieldSpreadSearchReqDTO request) {
        UdicCurveGenerateConditionReqDTO generateRequest = getCurveGenerateCondition(userid, request.getCurveId(),
                UdicCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return 0L;
        }
        return super.getComCountFromRedis(request, SPREAD_UDIC_COM_SPREAD_COUNT_KEY, () -> {
            UdicYieldSearchParam param = this.buildUdicYieldSearchParam(request, generateRequest);
            return udicComYieldSpreadDAO.countComYieldSpread(param);
        });
    }

    @Override
    public List<UdicComYieldSpreadResponseDTO> listComs(Date spreadDate, @Nullable Set<Long> udicComs) {
        if (CollectionUtils.isEmpty(udicComs)) {
            return Collections.emptyList();
        }
        // 今天的话要加上变动数据
        List<UdicComYieldSpreadResponseDTO> udicResponseList;
        if (super.isToday(spreadDate)) {
            spreadDate = this.getMaxSpreadDate();
            List<UdicComYieldSpreadDynamicView> udicComYieldSpreads = udicComYieldSpreadViewDAO.listComYieldSpreads(spreadDate, udicComs);
            udicResponseList = BeanCopyUtils.copyList(udicComYieldSpreads, UdicComYieldSpreadResponseDTO.class);
        } else {
            List<UdicComYieldSpreadDO> udicComYieldSpreads = udicComYieldSpreadDAO.listComYieldSpreads(spreadDate, udicComs);
            udicResponseList = BeanCopyUtils.copyList(udicComYieldSpreads, UdicComYieldSpreadResponseDTO.class);
        }
        Map<Long, ComShortInfoDTO> comShortInfoMap = comService.getComShortInfoByUniCodeMap(udicComs);
        return udicResponseList.stream().map(com -> {
            UdicComYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(com, UdicComYieldSpreadResponseDTO.class);
            String comUniName = comShortInfoMap.containsKey(response.getComUniCode()) ? comShortInfoMap.get(response.getComUniCode()).getComChiName()
                    : StringUtils.EMPTY;
            response.setComUniName(comUniName);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            AdministrativeRegionEnum.getEnumByValue(response.getAdministrativeRegion())
                    .ifPresent(region -> response.setAdministrativeRegionName(region.getText()));
            CalculationHelper.divideTenThousand(response.getTotalAssets()).ifPresent(response::setTotalAssets);
            CalculationHelper.divideTenThousand(response.getHideDebt()).ifPresent(response::setHideDebt);
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ComYieldSpreadCurveDTO> curves(Long comUniCode, Integer spreadBondType, Date startDate, Date endDate) {
        return udicComYieldSpreadRedisDAO.listCurves(comUniCode, spreadBondType, startDate, endDate);
    }

    @Override
    public List<ComCreditSpreadDTO> listCreditSpreadByComUniCodes(Set<Long> comUniCodes) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return Collections.emptyList();
        }
        Set<Long> notNullComUniCodes = comUniCodes.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (notNullComUniCodes.size() > COM_MAX_LIMIT) {
            logger.warn("超过单次获取最大条数200，实际请求条数:{}", notNullComUniCodes.size());
            throw new BusinessException("超过单次获取最大条数200, 实际请求条数:" + notNullComUniCodes.size());
        }

        List<ComCreditSpreadBO> udicComYieldSpreadBOList = udicComYieldSpreadDAO.listComCreditSpreads(comUniCodes);
        return BeanCopyUtils.copyList(udicComYieldSpreadBOList, ComCreditSpreadDTO.class);
    }

    private UdicComYieldSpreadDO fillUdicComExistenceColumn(UdicComYieldSpreadDO udicComYieldSpreadDO,
                                                            Map<Long, ComCurrentBondsBalanceDTO> comCurrentBondsBalanceDTOMap) {
        UdicComYieldSpreadDO result = BeanCopyUtils.copyProperties(udicComYieldSpreadDO, UdicComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comCurrentBondsBalanceDTOMap)) {
            return result;
        }
        ComCurrentBondsBalanceDTO comCurrentBondsBalanceDTO = comCurrentBondsBalanceDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(comCurrentBondsBalanceDTO)) {
            result.setBondBalance(comCurrentBondsBalanceDTO.getComCurrentBondsBalance());
        }
        return result;
    }

    private UdicComYieldSpreadDO fillUdicFinanceColumn(UdicComYieldSpreadDO udicComYieldSpreadDO,
                                                       Map<Long, ComFinanceSheetResponseDTO> comFinanceSheetResponseDTOMap) {
        UdicComYieldSpreadDO result = BeanCopyUtils.copyProperties(udicComYieldSpreadDO, UdicComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comFinanceSheetResponseDTOMap)) {
            return result;
        }
        ComFinanceSheetResponseDTO comFinanceSheetResponseDTO = comFinanceSheetResponseDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(comFinanceSheetResponseDTO)) {
            result.setTotalAssets(comFinanceSheetResponseDTO.getTotalAssets());
            result.setAssetLiabilityRatio(comFinanceSheetResponseDTO.getAssetLiabilityRatio());
        }
        return result;
    }

    private UdicComYieldSpreadDO fillUdicComInfoColumn(UdicComYieldSpreadDO udicComYieldSpreadDO,
                                                       Map<Long, UdicComInfoForSpreadDTO> udicComInfoForSpreadDTOMap) {
        UdicComYieldSpreadDO result = BeanCopyUtils.copyProperties(udicComYieldSpreadDO, UdicComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(udicComInfoForSpreadDTOMap)) {
            return result;
        }
        UdicComInfoForSpreadDTO comInfoForSpreadDTO = udicComInfoForSpreadDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(comInfoForSpreadDTO)) {
            if (DateExtensionUtils.isSameDay(result.getSpreadDate(), YieldSpreadHelper.getYesterDay())) {
                result.setHideDebt(comInfoForSpreadDTO.getHideDebt());
            }
            result.setActualControllerFullName(comInfoForSpreadDTO.getActualControllerFullName());
            result.setAreaName(comInfoForSpreadDTO.getAreaName());
        }
        return result;
    }

    private UdicComYieldSpreadDO fillUdicComSpreadColumn(UdicComYieldSpreadDO udicComYieldSpreadDO,
                                                         Map<Long, PgUdicBondYieldSpreadGroupDO> yieldSpreadMap,
                                                         Map<Long, PgUdicBondYieldSpreadGroupDO> yieldSpreadIsPublicOfferingMap,
                                                         Map<Long, PgUdicBondYieldSpreadGroupDO> yieldSpreadNotPublicOfferingMap,
                                                         Map<Long, PgUdicBondYieldSpreadGroupDO> yieldSpreadPerpetualMap) {
        UdicComYieldSpreadDO result = BeanCopyUtils.copyProperties(udicComYieldSpreadDO, UdicComYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(result)) {
            return result;
        }
        PgUdicBondYieldSpreadGroupDO yieldSpread = yieldSpreadMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpread)) {
            result.setComCbYield(yieldSpread.getCbYield());
            result.setComCreditSpread(yieldSpread.getBondCreditSpread());
            result.setComExcessSpread(yieldSpread.getBondExcessSpread());
        }
        PgUdicBondYieldSpreadGroupDO yieldSpreadIsPublicOffering = yieldSpreadIsPublicOfferingMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadIsPublicOffering)) {
            result.setComPublicCbYield(yieldSpreadIsPublicOffering.getCbYield());
            result.setComPublicCreditSpread(yieldSpreadIsPublicOffering.getBondCreditSpread());
            result.setComPublicExcessSpread(yieldSpreadIsPublicOffering.getBondExcessSpread());
        }
        PgUdicBondYieldSpreadGroupDO yieldSpreadNotPublicOffering = yieldSpreadNotPublicOfferingMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadNotPublicOffering)) {
            result.setComPrivateCbYield(yieldSpreadNotPublicOffering.getCbYield());
            result.setComPrivateCreditSpread(yieldSpreadNotPublicOffering.getBondCreditSpread());
            result.setComPrivateExcessSpread(yieldSpreadNotPublicOffering.getBondExcessSpread());
        }
        PgUdicBondYieldSpreadGroupDO yieldSpreadPerpetual = yieldSpreadPerpetualMap.get(result.getComUniCode());
        if (Objects.nonNull(yieldSpreadPerpetual)) {
            result.setComPerpetualCbYield(yieldSpreadPerpetual.getCbYield());
            result.setComPerpetualCreditSpread(yieldSpreadPerpetual.getBondCreditSpread());
            result.setComPerpetualExcessSpread(yieldSpreadPerpetual.getBondExcessSpread());
        }
        return result;
    }

    private UdicComYieldSpreadDO fillOldColumn(UdicComYieldSpreadDO udicComYieldSpreadDO,
                                               Map<Long, ComInterestCtzHistDO> comInterestCtzHistDOMap) {
        UdicComYieldSpreadDO result = BeanCopyUtils.copyProperties(udicComYieldSpreadDO, UdicComYieldSpreadDO.class);
        if (CollectionUtils.isEmpty(comInterestCtzHistDOMap)) {
            return result;
        }
        ComInterestCtzHistDO comInterestCtzHistDO = comInterestCtzHistDOMap.get(result.getComUniCode());
        if (Objects.nonNull(comInterestCtzHistDO)) {
            result.setProvinceUniCode(comInterestCtzHistDO.getAreaUniCode1());
            result.setProvinceName(comInterestCtzHistDO.getAreaName1());
            result.setCityUniCode(comInterestCtzHistDO.getAreaUniCode2());
            result.setCityName(comInterestCtzHistDO.getAreaName2());
            result.setAdministrativeRegion(comInterestCtzHistDO.getAreaLevelId());
            result.setActualControllerFullName(comInterestCtzHistDO.getRealCtrlName());
            result.setAreaName(comInterestCtzHistDO.getAreaName());
        }
        return result;
    }

    @Override
    protected List<MixYieldSpreadShortBO> listAllYieldSpreads(@NonNull List<Long> comUniCodes) {
        return udicComYieldSpreadDAO.listAllYieldSpreads(comUniCodes);
    }
}
