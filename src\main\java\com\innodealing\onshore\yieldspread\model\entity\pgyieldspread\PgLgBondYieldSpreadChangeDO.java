package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;
import java.util.Objects;

/**
 * 地方债区域利差历变动 DO
 * <AUTHOR>
 * @create: 2024-11-05
 */
@Table(name = "bond_spread_lg_change")
public class PgLgBondYieldSpreadChangeDO extends PgBaseLgBondYieldSpreadDO{

    /**
     * 变动类型 1 一周变动，2 一月变动 3 三月变动 4 六月变动 5 一年变动 6 一日变动 7 两周变动
     */
    @Column
    private Integer changeType;

    /**
     * 开始日期
     */
    @Column
    private Date startDate;

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }
}
