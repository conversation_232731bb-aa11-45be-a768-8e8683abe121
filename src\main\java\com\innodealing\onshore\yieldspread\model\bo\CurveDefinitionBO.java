package com.innodealing.onshore.yieldspread.model.bo;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * 用户曲线
 *
 * <AUTHOR>
 */
public class CurveDefinitionBO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 曲线组id
     */
    private Long curveGroupId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 曲线名称
     */
    private String spreadCurveName;

    /**
     * 曲线类型：产业=1，城投=2，银行=3，证券=4，自选债=5，中债曲线=6, 保险=7
     */
    private Integer spreadCurveType;

    /**
     * 曲线生成状态：待生成=1，生成中=2，已生成=3，生成失败=4
     */
    private Integer generateStatus;

    /**
     * 生成开始时间
     */
    private Timestamp generateStartTime;

    /**
     * 生成结束时间
     */
    private Timestamp generateEndTime;

    /**
     * 债券筛选条件
     */
    private String filterCondition;

    /**
     * 曲线序号
     */
    private Integer curveOrder;

    public Long getCurveGroupId() {
        return curveGroupId;
    }

    public void setCurveGroupId(Long curveGroupId) {
        this.curveGroupId = curveGroupId;
    }

    public Integer getCurveOrder() {
        return curveOrder;
    }

    public void setCurveOrder(Integer curveOrder) {
        this.curveOrder = curveOrder;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSpreadCurveName() {
        return spreadCurveName;
    }

    public void setSpreadCurveName(String spreadCurveName) {
        this.spreadCurveName = spreadCurveName;
    }

    public Integer getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(Integer spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public Integer getGenerateStatus() {
        return generateStatus;
    }

    public void setGenerateStatus(Integer generateStatus) {
        this.generateStatus = generateStatus;
    }

    public Timestamp getGenerateStartTime() {
        return Objects.isNull(generateStartTime) ? null : new Timestamp(generateStartTime.getTime());
    }

    public void setGenerateStartTime(Timestamp generateStartTime) {
        this.generateStartTime = Objects.isNull(generateStartTime) ? null : new Timestamp(generateStartTime.getTime());
    }

    public Timestamp getGenerateEndTime() {
        return Objects.isNull(generateEndTime) ? null : new Timestamp(generateEndTime.getTime());
    }

    public void setGenerateEndTime(Timestamp generateEndTime) {
        this.generateEndTime = Objects.isNull(generateEndTime) ? null : new Timestamp(generateEndTime.getTime());
    }

    public String getFilterCondition() {
        return filterCondition;
    }

    public void setFilterCondition(String filterCondition) {
        this.filterCondition = filterCondition;
    }

    @Override
    public String toString() {
        return "CurveBaseDataBO{" +
                "id=" + id +
                ", userId=" + userId +
                ", spreadCurveName='" + spreadCurveName + '\'' +
                ", spreadCurveType=" + spreadCurveType +
                ", generateStatus=" + generateStatus +
                ", generateStartTime=" + generateStartTime +
                ", generateEndTime=" + generateEndTime +
                ", filterCondition='" + filterCondition + '\'' +
                '}';
    }

}
