package com.innodealing.onshore.yieldspread.model.dto.response;


import io.swagger.annotations.ApiModelProperty;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 利差追踪折线图
 *
 * <AUTHOR>
 */
public class YieldTraceLineCommonResponseDTO {

    @ApiModelProperty("日期")
    private List<Date> issueDates;

    @ApiModelProperty("数据对象列表")
    private List<YieldTraceLineDataResponseDTO> dataDTOLists;

    public List<Date> getIssueDates() {
        return issueDates;
    }

    public void setIssueDates(List<Date> issueDates) {
        this.issueDates = Objects.isNull(issueDates) ? new ArrayList<>() : new ArrayList<>(issueDates);
    }

    public List<YieldTraceLineDataResponseDTO> getDataDTOLists() {
        return dataDTOLists;
    }

    public void setDataDTOLists(List<YieldTraceLineDataResponseDTO> dataDTOLists) {
        this.dataDTOLists = Objects.isNull(dataDTOLists) ? new ArrayList<>() : new ArrayList<>(dataDTOLists);
    }
}
