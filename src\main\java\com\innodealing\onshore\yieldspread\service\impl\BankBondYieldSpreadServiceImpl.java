package com.innodealing.onshore.yieldspread.service.impl;

import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.helper.CommonsHelper;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondFilterV3DTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationShortInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondImpliedRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.enums.BankTypeEnum;
import com.innodealing.onshore.bondmetadata.enums.EmbeddedOption;
import com.innodealing.onshore.bondmetadata.enums.ImplicitRatingTagEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgBankBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvBankBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition.PgBankBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.BankBondYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.BankBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.enums.ComYieldSpreadSectorEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.CommonUtils;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.BankYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.BankSingleBondYieldSpreadResDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveDataResDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.EmptyRouter;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import com.innodealing.onshore.yieldspread.service.BankBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.RedisService;
import com.innodealing.onshore.yieldspread.service.internal.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.CACHE_ONE_DAYS;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.MAX_BANK_BOND_SPREAD_DATE_KEY;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.*;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.splitDateRange;

/**
 * 银行债利差 Service
 *
 * <AUTHOR>
 **/
@SuppressWarnings({"squid:S1854", "squid:S1481", "java:S107", "squid:S00107"})
@Service
public class BankBondYieldSpreadServiceImpl extends AbstractBondCurveService implements BankBondYieldSpreadService {

    private final ExecutorService executorService;

    private static final SortDTO DEFAULT_SORT = new SortDTO(CommonsHelper.getPropertyName(BankBondYieldSpreadDO::getBondCreditSpread), SortDirection.DESC);

    protected BankBondYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("BankBondYieldSpreadServiceImpl-pool-").build());
    }

    @Resource
    private ComService comService;

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private BondRatingService bondRatingService;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private BankBondYieldSpreadDAO bankBondYieldSpreadDAO;

    @Resource
    private PgBankBondYieldSpreadDAO pgBankBondYieldSpreadDAO;

    @Resource
    private RedisService redisService;

    @Resource
    private BankBondYieldSpreadRedisDAO bankBondYieldSpreadRedisDAO;

    @Value("${sharding.yield.spread}")
    private Date initStartDate;

    @Autowired
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;

    @Autowired
    private MvBankBondYieldSpreadCurveDAO mvBankBondYieldSpreadCurveDAO;

    @Autowired
    private PgBankBondYieldSpreadCurveDAO pgBankBondYieldSpreadCurveDAO;

    @Resource
    private UserService userService;

    public static final String YIELD_SPREAD_BANK_BOND_YIELD_SPREAD_FLOW_ID = "yieldSpread:bankBondYieldSpreadFlowId";

    @Override
    public int calcBankBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs, Map<Long, OnshoreBondFilterV3DTO> onshoreBondFilterV3DtoMap,
                                                    Map<Integer, BondYieldCurveDTO> bondYieldCurveMap, Date spreadDate) {
        if (CollectionUtils.isEmpty(onshoreBondInfoDTOs)) {
            return 0;
        }
        Set<Long> bondUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getBondUniCode).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> comUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getComUniCode).filter(Objects::nonNull).collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);

        CompletableFuture<Map<Long, ComShortInfoDTO>> submitComShortInfoDTO = of.submit(() -> comService.getComShortInfoByUniCodeMap(comUniCodes));
        CompletableFuture<Map<Long, CbValuationShortInfoResponseDTO>> submitCbValuationShortInfo =
                of.submit(() -> bondPriceService.getCbValuationShortInfoMap(spreadDate, bondUniCodes));
        CompletableFuture<Map<Long, BondImpliedRatingDTO>> submitBondImpliedRatingDTO =
                of.submit(() -> bondRatingService.getBondImpliedRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, BondExternalCreditRatingDTO>> submitBondExternalCreditRatingDTO =
                of.submit(() -> bondRatingService.getBondExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, ComExternalCreditRatingDTO>> submitComExternalCreditRatingDTO =
                of.submit(() -> bondRatingService.getComExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        of.doWorks(submitComShortInfoDTO, submitCbValuationShortInfo, submitBondImpliedRatingDTO,
                submitBondExternalCreditRatingDTO, submitComExternalCreditRatingDTO);
        // 获取主体信息
        Map<Long, ComShortInfoDTO> comShortInfoDTOMap = submitComShortInfoDTO.join();
        // 获取中债估值信息
        Map<Long, CbValuationShortInfoResponseDTO> cbMap = submitCbValuationShortInfo.join();
        // 获取评级信息
        Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap = submitBondImpliedRatingDTO.join();
        Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap = submitBondExternalCreditRatingDTO.join();
        Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap = submitComExternalCreditRatingDTO.join();
        return parseBankBondYieldSpreadDO(onshoreBondInfoDTOs, onshoreBondFilterV3DtoMap, comShortInfoDTOMap, cbMap, bondImpliedRatingMap, bondExternalCreditRatingMap,
                comExternalCreditRatingMap, bondYieldCurveMap, spreadDate);
    }

    private int parseBankBondYieldSpreadDO(List<OnshoreBondInfoDTO> onshoreBondInfos, Map<Long, OnshoreBondFilterV3DTO> onshoreBondFilterV3DtoMap,
                                           Map<Long, ComShortInfoDTO> comShortInfoMap, Map<Long, CbValuationShortInfoResponseDTO> cbMap,
                                           Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap, Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                           Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap,
                                           Map<Integer, BondYieldCurveDTO> bondYieldCurveMap, Date spreadDate) {
        List<BankBondYieldSpreadDO> bankBondYieldSpreadDOs = Lists.newArrayList();
        List<YieldSpreadBondDO> yieldSpreadBonds = Lists.newArrayList();
        for (OnshoreBondInfoDTO onshoreBondInfoDTO : onshoreBondInfos) {
            BankBondYieldSpreadDO bankBondYieldSpreadDO = BeanCopyUtils.copyProperties(onshoreBondInfoDTO, BankBondYieldSpreadDO.class);
            bankBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getPublicOffering());
            bankBondYieldSpreadDO.setSpreadDate(spreadDate);
            OnshoreBondFilterV3DTO onshoreBondFilterV3DTO = onshoreBondFilterV3DtoMap.get(bankBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(onshoreBondFilterV3DTO)) {
                bankBondYieldSpreadDO.setBankType(onshoreBondFilterV3DTO.getBankType());
            }
            if (!DateExtensionUtils.isSameDay(spreadDate, YieldSpreadHelper.getYesterDay())) {
                bankBondYieldSpreadDO.setLatestCouponRate(null);
                bankBondYieldSpreadDO.setBondBalance(null);
            }
            if (Objects.equals(onshoreBondInfoDTO.getEmbeddedOption(), EmbeddedOption.PERPETUAL.getValue())) {
                bankBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getEmbeddedOption());
            }
            // 利差剩余期限标签
            bankBondYieldSpreadDO.setSpreadRemainingTenorTag(YieldSpreadHelper.getSpreadRemainingTenorTag(bankBondYieldSpreadDO.getRemainingTenorDay()));
            bankBondYieldSpreadDO = fillComInfoColumn(bankBondYieldSpreadDO, comShortInfoMap.get(bankBondYieldSpreadDO.getComUniCode()));
            bankBondYieldSpreadDO = fillRatingColumn(bankBondYieldSpreadDO, bondImpliedRatingMap.get(bankBondYieldSpreadDO.getBondUniCode()),
                    bondExternalCreditRatingMap.get(bankBondYieldSpreadDO.getBondUniCode()), comExternalCreditRatingMap.get(bankBondYieldSpreadDO.getComUniCode()));
            bankBondYieldSpreadDO = fillCbColumn(bankBondYieldSpreadDO, cbMap.get(bankBondYieldSpreadDO.getBondUniCode()));
            bankBondYieldSpreadDO = fillLerpYieldColumn(bankBondYieldSpreadDO, bondYieldCurveMap.get(YieldSpreadHelper.CDB_YIELD_CURVE),
                    bondYieldCurveMap.get(YieldSpreadHelper.getBankYieldCurve(bankBondYieldSpreadDO.getBankSeniorityRanking(),
                            bankBondYieldSpreadDO.getBondImpliedRatingMapping())));
            bankBondYieldSpreadDO = fillSpreadColumn(bankBondYieldSpreadDO);
            //这里如果超额利差和信用利差都为空的情况下 过滤掉
            if (Objects.nonNull(bankBondYieldSpreadDO.getBondCreditSpread()) || Objects.nonNull(bankBondYieldSpreadDO.getBondExcessSpread())) {
                bankBondYieldSpreadDO.setId(redisService.generatePk(YIELD_SPREAD_BANK_BOND_YIELD_SPREAD_FLOW_ID, bankBondYieldSpreadDO.getSpreadDate()));
                bankBondYieldSpreadDO.setDeleted(0);
                bankBondYieldSpreadDOs.add(bankBondYieldSpreadDO);
                YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(bankBondYieldSpreadDO, YieldSpreadBondDO.class);
                yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.BANK.getValue());
                yieldSpreadBonds.add(yieldSpreadBondDO);
            }
        }
        bankBondYieldSpreadDAO.saveBankBondYieldSpreadDOList(spreadDate, spreadDate, bankBondYieldSpreadDOs);
        super.saveYieldSpreadBonds(yieldSpreadBonds);
        return savePgBankBondYieldSpreadDOLists(spreadDate, bankBondYieldSpreadDOs);
    }

    private int savePgBankBondYieldSpreadDOLists(Date spreadDate, List<BankBondYieldSpreadDO> bankBondYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(bankBondYieldSpreadDOs)) {
            return 0;
        }
        List<PgBankBondYieldSpreadDO> pgBankBondYieldSpreadDOs = bankBondYieldSpreadDOs.stream()
                .map(x -> BeanCopyUtils.copyProperties(x, PgBankBondYieldSpreadDO.class)).collect(Collectors.toList());
        return pgBankBondYieldSpreadDAO.savePgBankBondYieldSpreadDOList(spreadDate, pgBankBondYieldSpreadDOs);

    }

    /**
     * 填充利差数据
     *
     * @param bankBondYieldSpreadDO 银行债利差
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO
     * <AUTHOR>
     */
    private BankBondYieldSpreadDO fillSpreadColumn(BankBondYieldSpreadDO bankBondYieldSpreadDO) {
        BankBondYieldSpreadDO result = BeanCopyUtils.copyProperties(bankBondYieldSpreadDO, BankBondYieldSpreadDO.class);
        if (Objects.nonNull(result.getCbYield())) {
            if (Objects.nonNull(result.getCdbLerpYield()) && result.getCdbLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondCreditSpread(result.getCbYield().subtract(result.getCdbLerpYield()).multiply(YieldSpreadHelper.BP_WEIGHT)
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.nonNull(result.getImpliedRatingLerpYield()) && result.getImpliedRatingLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondExcessSpread(result.getCbYield().subtract(result.getImpliedRatingLerpYield()).multiply(YieldSpreadHelper.BP_WEIGHT)
                        .setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                result.setExcessSpreadStatus(0);
            } else {
                result.setExcessSpreadStatus(1);
            }
        }
        return result;
    }

    /**
     * 填充插值收益率
     *
     * @param bankBondYieldSpreadDO 银行债利差
     * @param bondYieldCurveDTO     收益率曲线信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO
     * <AUTHOR>
     */
    private BankBondYieldSpreadDO fillLerpYieldColumn(BankBondYieldSpreadDO bankBondYieldSpreadDO, BondYieldCurveDTO cdbBondYieldCurveDTO, BondYieldCurveDTO bondYieldCurveDTO) {
        BankBondYieldSpreadDO result = BeanCopyUtils.copyProperties(bankBondYieldSpreadDO, BankBondYieldSpreadDO.class);
        //国开插值收益率;单位(%)
        Optional.ofNullable(cdbBondYieldCurveDTO).ifPresent(cdbDTO -> {
            BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(cdbDTO, BondYieldCurveBO.class);
            result.setCdbLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
        });
        //隐含评级对应曲线插值收益率;单位(%)
        Optional.ofNullable(bondYieldCurveDTO).ifPresent(curveDTO -> {
            BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(curveDTO, BondYieldCurveBO.class);
            result.setImpliedRatingLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
        });
        return result;
    }

    /**
     * 填充中债估值相关信息
     *
     * @param bankBondYieldSpreadDO 银行债利差
     * @param cbDTO                 中债估值
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO
     * <AUTHOR>
     */
    private BankBondYieldSpreadDO fillCbColumn(BankBondYieldSpreadDO bankBondYieldSpreadDO, CbValuationShortInfoResponseDTO cbDTO) {
        BankBondYieldSpreadDO result = BeanCopyUtils.copyProperties(bankBondYieldSpreadDO, BankBondYieldSpreadDO.class);
        Optional.ofNullable(cbDTO).ifPresent(dto -> result.setCbYield(dto.getYield()));
        return result;
    }

    /**
     * 填充发行人信息
     *
     * @param bankBondYieldSpreadDO 银行债利差
     * @param comShortInfoDTO       主体信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO
     * <AUTHOR>
     */
    private BankBondYieldSpreadDO fillComInfoColumn(BankBondYieldSpreadDO bankBondYieldSpreadDO, ComShortInfoDTO comShortInfoDTO) {
        BankBondYieldSpreadDO result = BeanCopyUtils.copyProperties(bankBondYieldSpreadDO, BankBondYieldSpreadDO.class);
        Optional.ofNullable(comShortInfoDTO).ifPresent(dto -> {
            //主体基础信息
            result.setInduLevel1Code(dto.getInduLevel1Code());
            result.setInduLevel1Name(dto.getInduLevel1Name());
            result.setInduLevel2Code(dto.getInduLevel2Code());
            result.setInduLevel2Name(dto.getInduLevel2Name());
        });
        return result;
    }

    /**
     * 填充评级相关信息
     *
     * @param bankBondYieldSpreadDO       银行债利差
     * @param bondImpliedRatingDTO        债券隐含评级
     * @param bondExternalCreditRatingDTO 主体外部评级
     * @param comExternalCreditRatingDTO  主体YY评级
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankBondYieldSpreadDO
     * <AUTHOR>
     */
    private BankBondYieldSpreadDO fillRatingColumn(BankBondYieldSpreadDO bankBondYieldSpreadDO, BondImpliedRatingDTO bondImpliedRatingDTO,
                                                   BondExternalCreditRatingDTO bondExternalCreditRatingDTO,
                                                   ComExternalCreditRatingDTO comExternalCreditRatingDTO) {
        BankBondYieldSpreadDO result = BeanCopyUtils.copyProperties(bankBondYieldSpreadDO, BankBondYieldSpreadDO.class);
        Optional.ofNullable(bondImpliedRatingDTO)
                .ifPresent(dto -> result.setBondImpliedRatingMapping(dto.getRatingMapping()));
        Optional.ofNullable(bondExternalCreditRatingDTO)
                .ifPresent(dto -> result.setBondExtRatingMapping(dto.getRatingMapping()));
        Optional.ofNullable(comExternalCreditRatingDTO)
                .ifPresent(dto -> result.setComExtRatingMapping(dto.getRatingMapping()));
        return result;
    }

    /**
     * 检查分片
     */
    //@Scheduled(initialDelay = 1000, fixedDelay = 3_600_000)
    public void checkShardingTables() {
        Date currentDate = new Date(System.currentTimeMillis());
        Date endDate = new Date(DateUtils.addDays(currentDate, 1).getTime());
        createShardingTables(initStartDate, endDate);
    }

    private void createShardingTables(Date startDate, Date endDate) {
        Collection<String> shardingTableNames = ShardingUtils.getYearShardingTableNames(BANK_TABLE_NAME, startDate, endDate);
        for (String shardingTableName : shardingTableNames) {
            bankBondYieldSpreadDAO.createShardingTable(shardingTableName);
        }
    }

    @Override
    public void refreshMvBankBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh) {
        final List<AbstractRatingRouter.SpreadDateRange> dateRangeList = splitDateRange(LocalDate.parse("2019-06-01"), LocalDate.now().minusDays(1));
        for (AbstractRatingRouter.SpreadDateRange dateRange : dateRangeList) {
            final RefreshYieldCurveParam param = RefreshYieldCurveParam.builder().dateRange(dateRange).initRefresh(true, isTableRefresh).build();
            this.refreshMvBankBondYieldSpreadBondAllCurve(param);
            this.refreshMvBankBondYieldSpreadBondImpliedRatingMappingCurve(param);
        }
    }

    @Override
    public void refreshMvBankBondYieldSpreadRatingCurve(RefreshYieldCurveParam param) {
        this.refreshMvBankBondYieldSpreadBondAllCurve(param);
        this.refreshMvBankBondYieldSpreadBondImpliedRatingMappingCurve(param);
    }

    private void refreshMvBankBondYieldSpreadBondImpliedRatingMappingCurve(RefreshYieldCurveParam param) {
        Set<String> bankImplicitRatingCombination = RatingCombinationHelper.getBankImplicitRatingCombination();
        Set<ImplicitRatingRouter> implicitRatingRouters = implicitRatingRouterFactory.newRatingRouterList(bankImplicitRatingCombination);
        for (ImplicitRatingRouter implicitRatingRouter : implicitRatingRouters) {
            Optional.ofNullable(param)
                    .ifPresent(p -> implicitRatingRouter.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
            mvBankBondYieldSpreadCurveDAO.createOrRefreshBankCurveMv(implicitRatingRouter, param);
            pgBankBondYieldSpreadCurveDAO.syncCurveShardBankForMv(implicitRatingRouter, param);
            mvBankBondYieldSpreadCurveDAO.droTempMv(implicitRatingRouter);

        }
    }

    private void refreshMvBankBondYieldSpreadBondAllCurve(RefreshYieldCurveParam param) {
        EmptyRouter router = new EmptyRouter();
        try {
            Optional.ofNullable(param)
                    .ifPresent(p -> router.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
            mvBankBondYieldSpreadCurveDAO.createOrRefreshBankCurveMv(router, param);
            pgBankBondYieldSpreadCurveDAO.syncCurveShardBankForMv(router, param);
        } finally {
            mvBankBondYieldSpreadCurveDAO.droTempMv(router);
        }
    }

    @Override
    public boolean saveCurve(Long userid, Long curveGroupId, BankCurveGenerateConditionReqDTO params) {
        return this.generalSaveCurve(userid, curveGroupId, params);
    }

    @Override
    public boolean updateCurve(Long userid, Long curveId, BankCurveGenerateConditionReqDTO request) {
        return super.generalUpdateCurve(userid, curveId, request);
    }

    @Override
    public NormPagingResult<BankSingleBondYieldSpreadResDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        BankCurveGenerateConditionReqDTO generateRequest = super.getCurveGenerateCondition(userid, request.getCurveId(), BankCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return new NormPagingResult<>();
        }
        BankYieldSearchParam param = super
                .buildBondYieldSearchParam(request, generateRequest, BankYieldSearchParam.class, ObjectExtensionUtils.getOrDefault(request.getSort(), DEFAULT_SORT));
        NormPagingResult<BankBondYieldSpreadDO> pagingResult = bankBondYieldSpreadDAO.listBankSingleBondYieldSpreads(param);
        if (CollectionUtils.isEmpty(pagingResult.getList())) {
            return pagingResult.convert(bond -> BeanCopyUtils.copyProperties(bond, BankSingleBondYieldSpreadResDTO.class));
        }
        Long[] bondUniCodes = pagingResult.getList().stream().map(BankBondYieldSpreadDO::getBondUniCode).distinct().toArray(Long[]::new);
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        NormPagingResult<BankSingleBondYieldSpreadResDTO> result = pagingResult.convert(bond -> {
            BankSingleBondYieldSpreadResDTO response = BeanCopyUtils.copyProperties(bond, BankSingleBondYieldSpreadResDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            response.setCdbLerpYield(CommonUtils.formatDecimal(bond.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            String ratingStr = RatingUtils.getRating(bond.getComExtRatingMapping()) + "/" + RatingUtils.getRating(bond.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            EnumUtils.getEnumByValue(bond.getBondImpliedRatingMapping(), ImplicitRatingTagEnum.class)
                    .ifPresent(v -> response.setBondImpliedRatingMappingStr(v.getText()));
            EnumUtils.getEnumByValue(bond.getBankType(), BankTypeEnum.class).ifPresent(v -> response.setBankTypeStr(v.getText()));
            response.setBankSeniorityRankingStr(YieldSpreadHelper.getBankSeniorityRankingTextMap().get(bond.getBankSeniorityRanking()));
            CalculationHelper.divideTenThousand(bond.getBondBalance()).ifPresent(response::setBondBalance);
            return response;
        });
        // 权限控制
        result.setList(this.permissionProcessing(userid, request.getSpreadDate(), result.getList()));
        return result;
    }

    private List<BankSingleBondYieldSpreadResDTO> permissionProcessing(Long userid, Date spreadDate, List<BankSingleBondYieldSpreadResDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(BankSingleBondYieldSpreadResDTO::getBondUniCode).collect(Collectors.toList()));
            for (BankSingleBondYieldSpreadResDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRatingMappingStr(CommonUtils.desensitized(yieldSpread.getBondImpliedRatingMappingStr(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();

        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (BankSingleBondYieldSpreadResDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
        }

        return list;
    }

    @Override
    public List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate) {
        return bankBondYieldSpreadRedisDAO.listCurves(bondUniCode, startDate, endDate);
    }

    @Override
    public List<BankSingleBondYieldSpreadResDTO> listBonds(Date spreadDate, @Nullable Set<Long> bondUniCodes) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        spreadDate = super.isToday(spreadDate) ? this.getMaxSpreadDate() : spreadDate;
        List<BankBondYieldSpreadDO> bankBondYieldSpreadList = bankBondYieldSpreadDAO.listBankBondYieldSpreads(spreadDate, bondUniCodes);
        if (CollectionUtils.isEmpty(bankBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes.toArray(new Long[0])).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        List<BankSingleBondYieldSpreadResDTO> responseList = Lists.newArrayListWithExpectedSize(bankBondYieldSpreadList.size());
        for (BankBondYieldSpreadDO bankBondYieldSpread : bankBondYieldSpreadList) {
            BankSingleBondYieldSpreadResDTO response = BeanCopyUtils.copyProperties(bankBondYieldSpread, BankSingleBondYieldSpreadResDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            response.setCdbLerpYield(CommonUtils.formatDecimal(bankBondYieldSpread.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            String ratingStr = RatingUtils.getRating(bankBondYieldSpread.getComExtRatingMapping()) + "/" + RatingUtils.getRating(bankBondYieldSpread.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            EnumUtils.getEnumByValue(bankBondYieldSpread.getBondImpliedRatingMapping(), ImplicitRatingTagEnum.class)
                    .ifPresent(v -> response.setBondImpliedRatingMappingStr(v.getText()));
            EnumUtils.getEnumByValue(bankBondYieldSpread.getBankType(), BankTypeEnum.class).ifPresent(v -> response.setBankTypeStr(v.getText()));
            response.setBankSeniorityRankingStr(YieldSpreadHelper.getBankSeniorityRankingTextMap().get(bankBondYieldSpread.getBankSeniorityRanking()));
            CalculationHelper.divideTenThousand(bankBondYieldSpread.getBondBalance()).ifPresent(response::setBondBalance);
            responseList.add(response);
        }
        return responseList;
    }

    @Override
    public Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_BANK_BOND_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = pgBankBondYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_BANK_BOND_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }

    @Override
    public List<CurveDataResDTO> listCurveData(Long curveId) {
        BankCurveGenerateConditionReqDTO conditionDTO = super.getCurveGenerateCondition(curveId, BankCurveGenerateConditionReqDTO.class);
        return listCurveData(conditionDTO);
    }

    @Override
    protected List<CurveDataResDTO> listCurveData(ICurveGenerateReq curveGenerateParam) {
        BankCurveGenerateConditionReqDTO conditionDTO = (BankCurveGenerateConditionReqDTO) curveGenerateParam;
        ComOrBondConditionReqDTO comOrBondCondition = conditionDTO.getComOrBondCondition();
        boolean isComOrBond = Objects.nonNull(comOrBondCondition) && Objects.nonNull(comOrBondCondition.getConditionType());
        BankYieldSearchParam params = BeanCopyUtils.copyProperties(conditionDTO, BankYieldSearchParam.class);
        List<BondYieldSpreadBO> yieldSpreads;
        boolean isRealTimeCalculate = isComOrBond || CollectionUtils.isNotEmpty(conditionDTO.getBankTypes());
        if (isRealTimeCalculate) {
            if (isComOrBond) {
                params.setComOrBondUniCode(comOrBondCondition.getConditionType(), comOrBondCondition.getUniCode());
            }
            yieldSpreads = pgBankBondYieldSpreadDAO.listBondYieldSpreads(params);
        } else {
            yieldSpreads = pgBankBondYieldSpreadCurveDAO.listBankYieldSpreads(params);
        }
        return super.convertToCurveDataResDTOsAndFilterData(yieldSpreads, super.getMinSampleBondSize(isComOrBond));
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.BANK;
    }

}
