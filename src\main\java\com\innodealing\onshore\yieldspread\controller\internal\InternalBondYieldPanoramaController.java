package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.yieldspread.service.BondYieldPanoramaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.Date;

/**
 * (内部)收益率全景控制器
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部) 收益率全景")
@RestController
@RequestMapping("internal/bond/yield/panorama")
public class InternalBondYieldPanoramaController {
    @Resource
    private BondYieldPanoramaService bondYieldPanoramaService;
    @Resource
    private HttpServletResponse httpServletResponse;

    @ApiOperation(value = "同步收益率全景(历史)")
    @GetMapping("/sync/history")
    public int syncHistBondYieldPanorama(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate) {
        return bondYieldPanoramaService.syncHistBondYieldPanorama(startDate);
    }

    @ApiOperation(value = "同步收益率全景(增量),不传日期默认同步上一天数据")
    @GetMapping("/sync/incr")
    public int syncBondYieldPanorama(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam(required = false) Date endDate) {
        return bondYieldPanoramaService.syncBondYieldPanorama(startDate, endDate);
    }


    @ApiOperation(value = "同步收益率全景-区间变动(历史)")
    @GetMapping("/sync/history/interval-change")
    public int syncHistIntervalChange(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate) {
        return bondYieldPanoramaService.syncHistIntervalChange(startDate);
    }

    @ApiOperation(value = "同步收益率全景-历史分位(历史)")
    @GetMapping("/sync/history/quantile")
    public int syncHistQuantile(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate) {
        return bondYieldPanoramaService.syncHistQuantile(startDate);
    }
}
