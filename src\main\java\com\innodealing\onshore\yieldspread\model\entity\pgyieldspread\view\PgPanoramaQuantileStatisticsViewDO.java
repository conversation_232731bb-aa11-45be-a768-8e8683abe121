package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view;

import com.innodealing.onshore.bondmetadata.enums.CurveCode;

/**
 * 全景历史分位统计信息
 *
 * <AUTHOR>
 * @date 2024/5/13 11:29
 **/
public class PgPanoramaQuantileStatisticsViewDO {
    /**
     * 收益率曲线编码
     *
     * @see CurveCode
     */
    private Integer curveCode;

    /**
     * 1月 小于发行日的 数量
     */
    private Integer ytm1MLessIssueCount;
    /**
     * 1月 总数量
     */
    private Integer ytm1MCount;
    /**
     * 3月 小于发行日的 数量
     */
    private Integer ytm3MLessIssueCount;
    /**
     * 3月 总数量
     */
    private Integer ytm3MCount;
    /**
     * 6月 小于发行日的 数量
     */
    private Integer ytm6MLessIssueCount;
    /**
     * 6月 总数量
     */
    private Integer ytm6MCount;
    /**
     * 9月 小于发行日的 数量
     */
    private Integer ytm9MLessIssueCount;
    /**
     * 9月 总数量
     */
    private Integer ytm9MCount;
    /**
     * 1年 小于发行日的 数量
     */
    private Integer ytm1YLessIssueCount;
    /**
     * 1年 总数量
     */
    private Integer ytm1YCount;
    /**
     * 2年 小于发行日的 数量
     */
    private Integer ytm2YLessIssueCount;
    /**
     * 2年 总数量
     */
    private Integer ytm2YCount;
    /**
     * 3年 小于发行日的 数量
     */
    private Integer ytm3YLessIssueCount;
    /**
     * 3年 总数量
     */
    private Integer ytm3YCount;
    /**
     * 4年 小于发行日的 数量
     */
    private Integer ytm4YLessIssueCount;
    /**
     * 4年 总数量
     */
    private Integer ytm4YCount;
    /**
     * 5年 小于发行日的 数量
     */
    private Integer ytm5YLessIssueCount;
    /**
     * 5年 总数量
     */
    private Integer ytm5YCount;
    /**
     * 7年 小于发行日的 数量
     */
    private Integer ytm7YLessIssueCount;
    /**
     * 7年 总数量
     */
    private Integer ytm7YCount;
    /**
     * 10年 小于发行日的 数量
     */
    private Integer ytm10YLessIssueCount;
    /**
     * 10年 总数量
     */
    private Integer ytm10YCount;
    /**
     * 15年 小于发行日的 数量
     */
    private Integer ytm15YLessIssueCount;
    /**
     * 15年 总数量
     */
    private Integer ytm15YCount;
    /**
     * 20年 小于发行日的 数量
     */
    private Integer ytm20YLessIssueCount;
    /**
     * 20年 总数量
     */
    private Integer ytm20YCount;
    /**
     * 30年 小于发行日的 数量
     */
    private Integer ytm30YLessIssueCount;
    /**
     * 30年 总数量
     */
    private Integer ytm30YCount;
    /**
     * 50年 小于发行日的 数量
     */
    private Integer ytm50YLessIssueCount;
    /**
     * 50年 总数量
     */
    private Integer ytm50YCount;

    public Integer getCurveCode() {
        return curveCode;
    }

    public void setCurveCode(Integer curveCode) {
        this.curveCode = curveCode;
    }

    public Integer getYtm1MLessIssueCount() {
        return ytm1MLessIssueCount;
    }

    public void setYtm1MLessIssueCount(Integer ytm1MLessIssueCount) {
        this.ytm1MLessIssueCount = ytm1MLessIssueCount;
    }

    public Integer getYtm1MCount() {
        return ytm1MCount;
    }

    public void setYtm1MCount(Integer ytm1MCount) {
        this.ytm1MCount = ytm1MCount;
    }

    public Integer getYtm3MLessIssueCount() {
        return ytm3MLessIssueCount;
    }

    public void setYtm3MLessIssueCount(Integer ytm3MLessIssueCount) {
        this.ytm3MLessIssueCount = ytm3MLessIssueCount;
    }

    public Integer getYtm3MCount() {
        return ytm3MCount;
    }

    public void setYtm3MCount(Integer ytm3MCount) {
        this.ytm3MCount = ytm3MCount;
    }

    public Integer getYtm6MLessIssueCount() {
        return ytm6MLessIssueCount;
    }

    public void setYtm6MLessIssueCount(Integer ytm6MLessIssueCount) {
        this.ytm6MLessIssueCount = ytm6MLessIssueCount;
    }

    public Integer getYtm6MCount() {
        return ytm6MCount;
    }

    public void setYtm6MCount(Integer ytm6MCount) {
        this.ytm6MCount = ytm6MCount;
    }

    public Integer getYtm9MLessIssueCount() {
        return ytm9MLessIssueCount;
    }

    public void setYtm9MLessIssueCount(Integer ytm9MLessIssueCount) {
        this.ytm9MLessIssueCount = ytm9MLessIssueCount;
    }

    public Integer getYtm9MCount() {
        return ytm9MCount;
    }

    public void setYtm9MCount(Integer ytm9MCount) {
        this.ytm9MCount = ytm9MCount;
    }

    public Integer getYtm1YLessIssueCount() {
        return ytm1YLessIssueCount;
    }

    public void setYtm1YLessIssueCount(Integer ytm1YLessIssueCount) {
        this.ytm1YLessIssueCount = ytm1YLessIssueCount;
    }

    public Integer getYtm1YCount() {
        return ytm1YCount;
    }

    public void setYtm1YCount(Integer ytm1YCount) {
        this.ytm1YCount = ytm1YCount;
    }

    public Integer getYtm2YLessIssueCount() {
        return ytm2YLessIssueCount;
    }

    public void setYtm2YLessIssueCount(Integer ytm2YLessIssueCount) {
        this.ytm2YLessIssueCount = ytm2YLessIssueCount;
    }

    public Integer getYtm2YCount() {
        return ytm2YCount;
    }

    public void setYtm2YCount(Integer ytm2YCount) {
        this.ytm2YCount = ytm2YCount;
    }

    public Integer getYtm3YLessIssueCount() {
        return ytm3YLessIssueCount;
    }

    public void setYtm3YLessIssueCount(Integer ytm3YLessIssueCount) {
        this.ytm3YLessIssueCount = ytm3YLessIssueCount;
    }

    public Integer getYtm3YCount() {
        return ytm3YCount;
    }

    public void setYtm3YCount(Integer ytm3YCount) {
        this.ytm3YCount = ytm3YCount;
    }

    public Integer getYtm4YLessIssueCount() {
        return ytm4YLessIssueCount;
    }

    public void setYtm4YLessIssueCount(Integer ytm4YLessIssueCount) {
        this.ytm4YLessIssueCount = ytm4YLessIssueCount;
    }

    public Integer getYtm4YCount() {
        return ytm4YCount;
    }

    public void setYtm4YCount(Integer ytm4YCount) {
        this.ytm4YCount = ytm4YCount;
    }

    public Integer getYtm5YLessIssueCount() {
        return ytm5YLessIssueCount;
    }

    public void setYtm5YLessIssueCount(Integer ytm5YLessIssueCount) {
        this.ytm5YLessIssueCount = ytm5YLessIssueCount;
    }

    public Integer getYtm5YCount() {
        return ytm5YCount;
    }

    public void setYtm5YCount(Integer ytm5YCount) {
        this.ytm5YCount = ytm5YCount;
    }

    public Integer getYtm7YLessIssueCount() {
        return ytm7YLessIssueCount;
    }

    public void setYtm7YLessIssueCount(Integer ytm7YLessIssueCount) {
        this.ytm7YLessIssueCount = ytm7YLessIssueCount;
    }

    public Integer getYtm7YCount() {
        return ytm7YCount;
    }

    public void setYtm7YCount(Integer ytm7YCount) {
        this.ytm7YCount = ytm7YCount;
    }

    public Integer getYtm10YLessIssueCount() {
        return ytm10YLessIssueCount;
    }

    public void setYtm10YLessIssueCount(Integer ytm10YLessIssueCount) {
        this.ytm10YLessIssueCount = ytm10YLessIssueCount;
    }

    public Integer getYtm10YCount() {
        return ytm10YCount;
    }

    public void setYtm10YCount(Integer ytm10YCount) {
        this.ytm10YCount = ytm10YCount;
    }

    public Integer getYtm15YLessIssueCount() {
        return ytm15YLessIssueCount;
    }

    public void setYtm15YLessIssueCount(Integer ytm15YLessIssueCount) {
        this.ytm15YLessIssueCount = ytm15YLessIssueCount;
    }

    public Integer getYtm15YCount() {
        return ytm15YCount;
    }

    public void setYtm15YCount(Integer ytm15YCount) {
        this.ytm15YCount = ytm15YCount;
    }

    public Integer getYtm20YLessIssueCount() {
        return ytm20YLessIssueCount;
    }

    public void setYtm20YLessIssueCount(Integer ytm20YLessIssueCount) {
        this.ytm20YLessIssueCount = ytm20YLessIssueCount;
    }

    public Integer getYtm20YCount() {
        return ytm20YCount;
    }

    public void setYtm20YCount(Integer ytm20YCount) {
        this.ytm20YCount = ytm20YCount;
    }

    public Integer getYtm30YLessIssueCount() {
        return ytm30YLessIssueCount;
    }

    public void setYtm30YLessIssueCount(Integer ytm30YLessIssueCount) {
        this.ytm30YLessIssueCount = ytm30YLessIssueCount;
    }

    public Integer getYtm30YCount() {
        return ytm30YCount;
    }

    public void setYtm30YCount(Integer ytm30YCount) {
        this.ytm30YCount = ytm30YCount;
    }

    public Integer getYtm50YLessIssueCount() {
        return ytm50YLessIssueCount;
    }

    public void setYtm50YLessIssueCount(Integer ytm50YLessIssueCount) {
        this.ytm50YLessIssueCount = ytm50YLessIssueCount;
    }

    public Integer getYtm50YCount() {
        return ytm50YCount;
    }

    public void setYtm50YCount(Integer ytm50YCount) {
        this.ytm50YCount = ytm50YCount;
    }
}
