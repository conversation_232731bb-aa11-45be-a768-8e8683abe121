package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;


/**
 * 城投债券利差曲线物化视图DO(包含市)
 *
 * <AUTHOR>
 */
@Table(name = "mv_udic_bond_yield_spread_curve_city")
public class MvUdicBondYieldSpreadCurveCityDO extends BaseMvUdicBondYieldSpreadCurveDO {
    /**
     * 市级编码
     */
    @Column
    private Long cityUniCode;

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }
}