package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition;

import javax.persistence.Column;
import java.sql.Date;
import java.util.Objects;

/**
 * 基础城投债券利差曲线分区DO
 *
 * <AUTHOR>
 */
public class BasePgUdicBondYieldSpreadCurveDO extends BaseYieldSpreadDataDO {

    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;

    /**
     * 信用利差不为空的债券样本数量
     */
    @Column
    private Integer bondCreditSpreadCount;

    /**
     * 超额利差不为空的债券样本数量
     */
    @Column
    private Integer bondExcessSpreadCount;

    /**
     * 中债收益率不为空的债券样本数量
     */
    @Column
    private Integer cbYieldCount;

    /**
     * 利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer spreadBondType;

    /**
     * 债券外部评级映射
     */
    @Column
    private Integer bondExtRatingMapping;

    /**
     * 行政区划
     */
    @Column
    private Integer administrativeDivision;

    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;

    /**
     * 担保状态: 0: 无; 1: 有
     */
    @Column
    private Integer guaranteedStatus;

    /**
     * 是否根据利差债券类别进行分组: 0：是，1：否
     */
    @Column
    private Integer usingSpreadBondType;

    /**
     * 是否根据债券外部评级映射进行分组: 0：是，1：否
     */
    @Column
    private Integer usingBondExtRatingMapping;

    /**
     * 是否根据行政区划进行分组: 0：是，1：否
     */
    @Column
    private Integer usingAdministrativeDivision;

    /**
     * 是否根据利差剩余期限标签进行分组: 0：是，1：否
     */
    @Column
    private Integer usingSpreadRemainingTenorTag;

    /**
     * 是否根据担保状态进行分组: 0：是，1：否
     */
    @Column
    private Integer usingGuaranteedStatus;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer getAdministrativeDivision() {
        return administrativeDivision;
    }

    public void setAdministrativeDivision(Integer administrativeDivision) {
        this.administrativeDivision = administrativeDivision;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getCbYieldCount() {
        return cbYieldCount;
    }

    public void setCbYieldCount(Integer cbYieldCount) {
        this.cbYieldCount = cbYieldCount;
    }

    public Integer getUsingSpreadBondType() {
        return usingSpreadBondType;
    }

    public void setUsingSpreadBondType(Integer usingSpreadBondType) {
        this.usingSpreadBondType = usingSpreadBondType;
    }

    public Integer getUsingBondExtRatingMapping() {
        return usingBondExtRatingMapping;
    }

    public void setUsingBondExtRatingMapping(Integer usingBondExtRatingMapping) {
        this.usingBondExtRatingMapping = usingBondExtRatingMapping;
    }

    public Integer getUsingAdministrativeDivision() {
        return usingAdministrativeDivision;
    }

    public void setUsingAdministrativeDivision(Integer usingAdministrativeDivision) {
        this.usingAdministrativeDivision = usingAdministrativeDivision;
    }

    public Integer getUsingSpreadRemainingTenorTag() {
        return usingSpreadRemainingTenorTag;
    }

    public void setUsingSpreadRemainingTenorTag(Integer usingSpreadRemainingTenorTag) {
        this.usingSpreadRemainingTenorTag = usingSpreadRemainingTenorTag;
    }

    public Integer getUsingGuaranteedStatus() {
        return usingGuaranteedStatus;
    }

    public void setUsingGuaranteedStatus(Integer usingGuaranteedStatus) {
        this.usingGuaranteedStatus = usingGuaranteedStatus;
    }

}