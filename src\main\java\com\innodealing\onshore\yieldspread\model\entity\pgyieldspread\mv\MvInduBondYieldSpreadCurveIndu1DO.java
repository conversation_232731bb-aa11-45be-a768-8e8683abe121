package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * 行业债利差曲线物化视图(包含行业1)
 *
 * <AUTHOR>
 */
@Table(name = "mv_indu_bond_yield_spread_curve_indu1")
public class MvInduBondYieldSpreadCurveIndu1DO extends BaseMvInduBondYieldSpreadCurveDO {
    /**
     * 一级行业编码
     */
    @Column
    private Long induLevel1Code;

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }
}