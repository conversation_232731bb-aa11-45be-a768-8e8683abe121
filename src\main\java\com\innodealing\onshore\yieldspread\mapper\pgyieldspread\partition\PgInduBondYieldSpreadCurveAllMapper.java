package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveAllDO;

/**
 * 行业利差曲线-分区表(不包含行业一，行业二)
 *
 * <AUTHOR>
 */
public interface PgInduBondYieldSpreadCurveAllMapper extends DynamicQueryMapper<PgInduBondYieldSpreadCurveAllDO> {

    /**
     * 从物化视图刷新行业利差曲线数据
     */
    void refreshMvInduBondYieldSpreadCurveFromMV();

    /**
     * 从物化视图中同步昨日利差曲线数据
     */
    void syncCurveIncrFromMV();
}
