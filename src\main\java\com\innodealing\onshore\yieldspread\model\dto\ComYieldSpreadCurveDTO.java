package com.innodealing.onshore.yieldspread.model.dto;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 主体利差曲线DTO
 *
 * <AUTHOR>
 */
public class ComYieldSpreadCurveDTO {

    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 信用利差
     */
    private BigDecimal bondCreditSpread;
    /**
     * 超额利差
     */
    private BigDecimal bondExcessSpread;
    /**
     * 估值收益率
     */
    private BigDecimal cbYield;
    /**
     * 债券信用利差(平均数)
     */
    private BigDecimal avgBondCreditSpread;
    /**
     * 超额利差
     */
    private BigDecimal avgBondExcessSpread;
    /**
     * 估值收益率
     */
    private BigDecimal avgCbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }
}
