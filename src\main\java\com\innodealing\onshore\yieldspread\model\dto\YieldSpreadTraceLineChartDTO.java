package com.innodealing.onshore.yieldspread.model.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveCodeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadRatingEnum;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceAbsBO;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 利差追踪折线图
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class YieldSpreadTraceLineChartDTO {
    @ApiModelProperty("日期")
    private Date[] issueDates;

    @ApiModelProperty("1M")
    private BigDecimal[] ytm1Ms;

    @ApiModelProperty("3M")
    private BigDecimal[] ytm3Ms;

    @ApiModelProperty("6M")
    private BigDecimal[] ytm6Ms;

    @ApiModelProperty("9M")
    private BigDecimal[] ytm9Ms;
    @ApiModelProperty("1Y")
    private BigDecimal[] ytm1Ys;
    @ApiModelProperty("2Y")
    private BigDecimal[] ytm2Ys;
    @ApiModelProperty("3Y")
    private BigDecimal[] ytm3Ys;
    @ApiModelProperty("5Y")
    private BigDecimal[] ytm5Ys;
    @ApiModelProperty("7Y")
    private BigDecimal[] ytm7Ys;

    @ApiModelProperty("10Y")
    private BigDecimal[] ytm10Ys;
    @ApiModelProperty("15Y")
    private BigDecimal[] ytm15Ys;
    @ApiModelProperty("20Y")
    private BigDecimal[] ytm20Ys;
    @ApiModelProperty("30Y")
    private BigDecimal[] ytm30Ys;
    @ApiModelProperty("50Y")
    private BigDecimal[] ytm50Ys;
    @ApiModelProperty("AAA+")
    private BigDecimal[] ratingAAAPluses;
    @ApiModelProperty("AAA")
    private BigDecimal[] ratingAAAs;
    @ApiModelProperty("AAA-")
    private BigDecimal[] ratingAAASubs;
    @ApiModelProperty("AA+")
    private BigDecimal[] ratingAAPluses;
    @ApiModelProperty("AA")
    private BigDecimal[] ratingAAs;
    @ApiModelProperty("AA(2)")
    private BigDecimal[] ratingAATwos;
    @ApiModelProperty("AA-")
    private BigDecimal[] ratingAASubs;

    public Date[] getIssueDates() {
        return Objects.isNull(issueDates) ? new Date[0] : issueDates.clone();
    }

    public void setIssueDates(Date[] issueDates) {
        this.issueDates = Objects.isNull(issueDates) ? new Date[0] : issueDates.clone();
    }

    public BigDecimal[] getYtm1Ms() {
        return Objects.isNull(ytm1Ms) ? new BigDecimal[0] : ytm1Ms.clone();
    }

    public void setYtm1Ms(BigDecimal[] ytm1Ms) {
        this.ytm1Ms = Objects.isNull(ytm1Ms) ? new BigDecimal[0] : ytm1Ms.clone();
    }

    public BigDecimal[] getYtm3Ms() {
        return Objects.isNull(ytm3Ms) ? new BigDecimal[0] : ytm3Ms.clone();
    }

    public void setYtm3Ms(BigDecimal[] ytm3Ms) {
        this.ytm3Ms = Objects.isNull(ytm3Ms) ? new BigDecimal[0] : ytm3Ms.clone();
    }

    public BigDecimal[] getYtm6Ms() {
        return Objects.isNull(ytm6Ms) ? new BigDecimal[0] : ytm6Ms.clone();
    }

    public void setYtm6Ms(BigDecimal[] ytm6Ms) {
        this.ytm6Ms = Objects.isNull(ytm6Ms) ? new BigDecimal[0] : ytm6Ms.clone();
    }

    public BigDecimal[] getYtm9Ms() {
        return Objects.isNull(ytm9Ms) ? new BigDecimal[0] : ytm9Ms.clone();
    }

    public void setYtm9Ms(BigDecimal[] ytm9Ms) {
        this.ytm9Ms = Objects.isNull(ytm9Ms) ? new BigDecimal[0] : ytm9Ms.clone();
    }

    public BigDecimal[] getYtm1Ys() {
        return Objects.isNull(ytm1Ys) ? new BigDecimal[0] : ytm1Ys.clone();
    }

    public void setYtm1Ys(BigDecimal[] ytm1Ys) {
        this.ytm1Ys = Objects.isNull(ytm1Ys) ? new BigDecimal[0] : ytm1Ys.clone();
    }

    public BigDecimal[] getYtm2Ys() {
        return Objects.isNull(ytm2Ys) ? new BigDecimal[0] : ytm2Ys.clone();
    }

    public void setYtm2Ys(BigDecimal[] ytm2Ys) {
        this.ytm2Ys = Objects.isNull(ytm2Ys) ? new BigDecimal[0] : ytm2Ys.clone();
    }

    public BigDecimal[] getYtm3Ys() {
        return Objects.isNull(ytm3Ys) ? new BigDecimal[0] : ytm3Ys.clone();
    }

    public void setYtm3Ys(BigDecimal[] ytm3Ys) {
        this.ytm3Ys = Objects.isNull(ytm3Ys) ? new BigDecimal[0] : ytm3Ys.clone();
    }

    public BigDecimal[] getYtm5Ys() {
        return Objects.isNull(ytm5Ys) ? new BigDecimal[0] : ytm5Ys.clone();
    }

    public void setYtm5Ys(BigDecimal[] ytm5Ys) {
        this.ytm5Ys = Objects.isNull(ytm5Ys) ? new BigDecimal[0] : ytm5Ys.clone();
    }

    public BigDecimal[] getYtm7Ys() {
        return Objects.isNull(ytm7Ys) ? new BigDecimal[0] : ytm7Ys.clone();
    }

    public void setYtm7Ys(BigDecimal[] ytm7Ys) {
        this.ytm7Ys = Objects.isNull(ytm7Ys) ? new BigDecimal[0] : ytm7Ys.clone();
    }

    public BigDecimal[] getYtm10Ys() {
        return Objects.isNull(ytm10Ys) ? new BigDecimal[0] : ytm10Ys.clone();
    }

    public void setYtm10Ys(BigDecimal[] ytm10Ys) {
        this.ytm10Ys = Objects.isNull(ytm10Ys) ? new BigDecimal[0] : ytm10Ys.clone();
    }

    public BigDecimal[] getYtm15Ys() {
        return Objects.isNull(ytm15Ys) ? new BigDecimal[0] : ytm15Ys.clone();
    }

    public void setYtm15Ys(BigDecimal[] ytm15Ys) {
        this.ytm15Ys = Objects.isNull(ytm15Ys) ? new BigDecimal[0] : ytm15Ys.clone();
    }

    public BigDecimal[] getYtm20Ys() {
        return Objects.isNull(ytm20Ys) ? new BigDecimal[0] : ytm20Ys.clone();
    }

    public void setYtm20Ys(BigDecimal[] ytm20Ys) {
        this.ytm20Ys = Objects.isNull(ytm20Ys) ? new BigDecimal[0] : ytm20Ys.clone();
    }

    public BigDecimal[] getYtm30Ys() {
        return Objects.isNull(ytm30Ys) ? new BigDecimal[0] : ytm30Ys.clone();
    }

    public void setYtm30Ys(BigDecimal[] ytm30Ys) {
        this.ytm30Ys = Objects.isNull(ytm30Ys) ? new BigDecimal[0] : ytm30Ys.clone();
    }

    public BigDecimal[] getYtm50Ys() {
        return Objects.isNull(ytm50Ys) ? new BigDecimal[0] : ytm50Ys.clone();
    }

    public void setYtm50Ys(BigDecimal[] ytm50Ys) {
        this.ytm50Ys = Objects.isNull(ytm50Ys) ? new BigDecimal[0] : ytm50Ys.clone();
    }

    public BigDecimal[] getRatingAAAPluses() {
        return Objects.isNull(ratingAAAPluses) ? new BigDecimal[0] : ratingAAAPluses.clone();
    }

    public void setRatingAAAPluses(BigDecimal[] ratingAAAPluses) {
        this.ratingAAAPluses = Objects.isNull(ratingAAAPluses) ? new BigDecimal[0] : ratingAAAPluses.clone();
    }

    public BigDecimal[] getRatingAAAs() {
        return Objects.isNull(ratingAAAs) ? new BigDecimal[0] : ratingAAAs.clone();
    }

    public void setRatingAAAs(BigDecimal[] ratingAAAs) {
        this.ratingAAAs = Objects.isNull(ratingAAAs) ? new BigDecimal[0] : ratingAAAs.clone();
    }

    public BigDecimal[] getRatingAAASubs() {
        return Objects.isNull(ratingAAASubs) ? new BigDecimal[0] : ratingAAASubs.clone();
    }

    public void setRatingAAASubs(BigDecimal[] ratingAAASubs) {
        this.ratingAAASubs = Objects.isNull(ratingAAASubs) ? new BigDecimal[0] : ratingAAASubs.clone();
    }

    public BigDecimal[] getRatingAAPluses() {
        return Objects.isNull(ratingAAPluses) ? new BigDecimal[0] : ratingAAPluses.clone();
    }

    public void setRatingAAPluses(BigDecimal[] ratingAAPluses) {
        this.ratingAAPluses = Objects.isNull(ratingAAPluses) ? new BigDecimal[0] : ratingAAPluses.clone();
    }

    public BigDecimal[] getRatingAAs() {
        return Objects.isNull(ratingAAs) ? new BigDecimal[0] : ratingAAs.clone();
    }

    public void setRatingAAs(BigDecimal[] ratingAAs) {
        this.ratingAAs = Objects.isNull(ratingAAs) ? new BigDecimal[0] : ratingAAs.clone();
    }

    public BigDecimal[] getRatingAATwos() {
        return Objects.isNull(ratingAATwos) ? new BigDecimal[0] : ratingAATwos.clone();
    }

    public void setRatingAATwos(BigDecimal[] ratingAATwos) {
        this.ratingAATwos = Objects.isNull(ratingAATwos) ? new BigDecimal[0] : ratingAATwos.clone();
    }

    public BigDecimal[] getRatingAASubs() {
        return Objects.isNull(ratingAASubs) ? new BigDecimal[0] : ratingAASubs.clone();
    }

    public void setRatingAASubs(BigDecimal[] ratingAASubs) {
        this.ratingAASubs = Objects.isNull(ratingAASubs) ? new BigDecimal[0] : ratingAASubs.clone();
    }


    /**
     * 获取构建器
     *
     * @param size 初始化大小
     * @return {@link YieldSpreadTraceLineChartDTO.Builder}
     */
    public static YieldSpreadTraceLineChartDTO.Builder builder(int size) {
        return new YieldSpreadTraceLineChartDTO.Builder(size);
    }

    /**
     * 构建器
     *
     * <AUTHOR>
     */
    @SuppressWarnings("squid:S2972")
    public static class Builder {
        private final List<Date> issueDates;

        private final List<BigDecimal> ytm1Ms;

        private final List<BigDecimal> ytm3Ms;

        private final List<BigDecimal> ytm6Ms;

        private final List<BigDecimal> ytm9Ms;
        private final List<BigDecimal> ytm1Ys;
        private final List<BigDecimal> ytm2Ys;
        private final List<BigDecimal> ytm3Ys;
        private final List<BigDecimal> ytm5Ys;
        private final List<BigDecimal> ytm7Ys;

        private final List<BigDecimal> ytm10Ys;
        private final List<BigDecimal> ytm15Ys;
        private final List<BigDecimal> ytm20Ys;
        private final List<BigDecimal> ytm30Ys;
        private final List<BigDecimal> ytm50Ys;
        private final List<BigDecimal> ratingAAAPluses;
        private final List<BigDecimal> ratingAAAs;
        private final List<BigDecimal> ratingAAASubs;
        private final List<BigDecimal> ratingAAPluses;
        private final List<BigDecimal> ratingAAs;
        private final List<BigDecimal> ratingAATwos;
        private final List<BigDecimal> ratingAASubs;

        private static final Map<YieldSpreadRatingEnum, BiConsumer<Builder, BigDecimal>> CURVE_MAP = new ConcurrentHashMap<>();

        static {
            CURVE_MAP.put(YieldSpreadRatingEnum.AAA_PLUS, Builder::ratingAAAPlus);
            CURVE_MAP.put(YieldSpreadRatingEnum.AAA, Builder::ratingAAA);
            CURVE_MAP.put(YieldSpreadRatingEnum.AAA_SUB, Builder::ratingAAASub);
            CURVE_MAP.put(YieldSpreadRatingEnum.AA_PLUS, Builder::ratingAAPlus);
            CURVE_MAP.put(YieldSpreadRatingEnum.AA, Builder::ratingAA);
            CURVE_MAP.put(YieldSpreadRatingEnum.AA_TWO, Builder::ratingAATwo);
            CURVE_MAP.put(YieldSpreadRatingEnum.AA_SUB, Builder::ratingAASubs);
        }

        private static <T> ArrayList<T> init(int size) {
            return Lists.newArrayListWithCapacity(size);
        }

        /**
         * 构造函数
         *
         * @param size 初始化大小
         */
        public Builder(int size) {
            issueDates = init(size);
            ytm1Ms = init(size);
            ytm3Ms = init(size);
            ytm6Ms = init(size);
            ytm9Ms = init(size);
            ytm1Ys = init(size);
            ytm2Ys = init(size);
            ytm3Ys = init(size);
            ytm5Ys = init(size);
            ytm7Ys = init(size);
            ytm10Ys = init(size);
            ytm15Ys = init(size);
            ytm20Ys = init(size);
            ytm30Ys = init(size);
            ytm50Ys = init(size);
            ratingAAAPluses = init(size);
            ratingAAAs = init(size);
            ratingAAASubs = init(size);
            ratingAAPluses = init(size);
            ratingAAs = init(size);
            ratingAATwos = init(size);
            ratingAASubs = init(size);
        }

        /**
         * 添加AAA+评级数据
         *
         * @param ratingYield 收益率
         */
        public void ratingAAAPlus(BigDecimal ratingYield) {
            ratingAAAPluses.add(ratingYield);
        }

        /**
         * 添加AAA评级数据
         *
         * @param ratingYield 收益率
         */
        public void ratingAAA(BigDecimal ratingYield) {
            ratingAAAs.add(ratingYield);
        }

        /**
         * 添加AAA-评级数据
         *
         * @param ratingYield 收益率
         */
        public void ratingAAASub(BigDecimal ratingYield) {
            ratingAAASubs.add(ratingYield);
        }

        /**
         * 添加AA+评级数据
         *
         * @param ratingYield 收益率
         */
        public void ratingAAPlus(BigDecimal ratingYield) {
            ratingAAPluses.add(ratingYield);
        }

        /**
         * 添加AA评级数据
         *
         * @param ratingYield 收益率
         */
        public void ratingAA(BigDecimal ratingYield) {
            ratingAAs.add(ratingYield);
        }

        /**
         * 添加AA(2)评级数据
         *
         * @param ratingYield 收益率
         */
        public void ratingAATwo(BigDecimal ratingYield) {
            ratingAATwos.add(ratingYield);
        }

        /**
         * 添加AA-评级数据
         *
         * @param ratingYield 收益率
         */
        public void ratingAASubs(BigDecimal ratingYield) {
            ratingAASubs.add(ratingYield);
        }

        /**
         * 设置评级数据
         *
         * @param absEntry       利差追踪绝对值和发行日期的映射数据
         * @param function       设置期限函数
         * @param curveCodeEnums 曲线代码枚举
         * @return {@link YieldSpreadTraceLineChartDTO.Builder}
         */
        public YieldSpreadTraceLineChartDTO.Builder withPeriod(Map.Entry<Date, List<PgBondYieldSpreadTraceAbsBO>> absEntry,
                                                               Function<PgBondYieldSpreadTraceAbsBO, BigDecimal> function, Set<YieldSpreadCurveCodeEnum> curveCodeEnums) {
            List<PgBondYieldSpreadTraceAbsBO> bondYieldSpreadTraceAbsList = absEntry.getValue();
            Set<YieldSpreadCurveCodeEnum> yieldSpreadCurveCodeEnums = EnumSet.copyOf(curveCodeEnums);
            issueDates.add(absEntry.getKey());
            for (PgBondYieldSpreadTraceAbsBO bondYieldSpreadTraceAbsBO : bondYieldSpreadTraceAbsList) {
                BigDecimal periodYield = function.apply(bondYieldSpreadTraceAbsBO);
                YieldSpreadCurveCodeEnum curveCodeEnum = ITextValueEnum.getEnum(YieldSpreadCurveCodeEnum.class, bondYieldSpreadTraceAbsBO.getCurveCode());
                yieldSpreadCurveCodeEnums.remove(curveCodeEnum);
                YieldSpreadRatingEnum ratingByCurveCode = YieldSpreadRatingEnum.getRatingByCurveCode(curveCodeEnum);
                Optional<BiConsumer<Builder, BigDecimal>> setter = Optional.ofNullable(CURVE_MAP.get(ratingByCurveCode));
                setter.ifPresent(consumer -> consumer.accept(this, periodYield));
            }
            // 补全空数据
            for (YieldSpreadCurveCodeEnum curveCodeEnum : yieldSpreadCurveCodeEnums) {
                YieldSpreadRatingEnum ratingByCurveCode = YieldSpreadRatingEnum.getRatingByCurveCode(curveCodeEnum);
                Optional<BiConsumer<Builder, BigDecimal>> setter = Optional.ofNullable(CURVE_MAP.get(ratingByCurveCode));
                setter.ifPresent(consumer -> consumer.accept(this, null));
            }
            return this;
        }

        /**
         * 设置期限数据
         *
         * @param bondYieldSpreadTraceAbsBO 利差追踪绝对值数据
         * @return {@link YieldSpreadTraceLineChartDTO.Builder}
         */
        public YieldSpreadTraceLineChartDTO.Builder withRating(@NonNull PgBondYieldSpreadTraceAbsBO bondYieldSpreadTraceAbsBO) {
            ytm1Ms.add(bondYieldSpreadTraceAbsBO.getYtm1M());
            ytm3Ms.add(bondYieldSpreadTraceAbsBO.getYtm3M());
            ytm6Ms.add(bondYieldSpreadTraceAbsBO.getYtm6M());
            ytm9Ms.add(bondYieldSpreadTraceAbsBO.getYtm9M());
            ytm1Ys.add(bondYieldSpreadTraceAbsBO.getYtm1Y());
            ytm2Ys.add(bondYieldSpreadTraceAbsBO.getYtm2Y());
            ytm3Ys.add(bondYieldSpreadTraceAbsBO.getYtm3Y());
            ytm5Ys.add(bondYieldSpreadTraceAbsBO.getYtm5Y());
            ytm7Ys.add(bondYieldSpreadTraceAbsBO.getYtm7Y());
            ytm10Ys.add(bondYieldSpreadTraceAbsBO.getYtm10Y());
            ytm15Ys.add(bondYieldSpreadTraceAbsBO.getYtm15Y());
            ytm20Ys.add(bondYieldSpreadTraceAbsBO.getYtm20Y());
            ytm30Ys.add(bondYieldSpreadTraceAbsBO.getYtm30Y());
            ytm50Ys.add(bondYieldSpreadTraceAbsBO.getYtm50Y());
            issueDates.add(bondYieldSpreadTraceAbsBO.getIssueDate());
            return this;
        }

        /**
         * 创建YieldSpreadTraceChartRatingResponseDTO响应对象
         *
         * @return {@link YieldSpreadTraceLineChartDTO}
         */
        public YieldSpreadTraceLineChartDTO build() {
            YieldSpreadTraceLineChartDTO response = new YieldSpreadTraceLineChartDTO();
            response.setIssueDates(issueDates.toArray(new Date[0]));
            this.anyNotNull(ytm1Ms).ifPresent(response::setYtm1Ms);
            this.anyNotNull(ytm3Ms).ifPresent(response::setYtm3Ms);
            this.anyNotNull(ytm6Ms).ifPresent(response::setYtm6Ms);
            this.anyNotNull(ytm9Ms).ifPresent(response::setYtm9Ms);
            this.anyNotNull(ytm1Ys).ifPresent(response::setYtm1Ys);
            this.anyNotNull(ytm2Ys).ifPresent(response::setYtm2Ys);
            this.anyNotNull(ytm3Ys).ifPresent(response::setYtm3Ys);
            this.anyNotNull(ytm5Ys).ifPresent(response::setYtm5Ys);
            this.anyNotNull(ytm7Ys).ifPresent(response::setYtm7Ys);
            this.anyNotNull(ytm10Ys).ifPresent(response::setYtm10Ys);
            this.anyNotNull(ytm15Ys).ifPresent(response::setYtm15Ys);
            this.anyNotNull(ytm20Ys).ifPresent(response::setYtm20Ys);
            this.anyNotNull(ytm30Ys).ifPresent(response::setYtm30Ys);
            this.anyNotNull(ytm50Ys).ifPresent(response::setYtm50Ys);
            this.anyNotNull(ratingAAAPluses).ifPresent(response::setRatingAAAPluses);
            this.anyNotNull(ratingAAAs).ifPresent(response::setRatingAAAs);
            this.anyNotNull(ratingAAASubs).ifPresent(response::setRatingAAASubs);
            this.anyNotNull(ratingAAPluses).ifPresent(response::setRatingAAPluses);
            this.anyNotNull(ratingAAs).ifPresent(response::setRatingAAs);
            this.anyNotNull(ratingAATwos).ifPresent(response::setRatingAATwos);
            this.anyNotNull(ratingAASubs).ifPresent(response::setRatingAASubs);
            // 如果数据为空，则全都置空，包括日期
            if (isAllEmpty(response)) {
                response.setIssueDates(null);
            }
            return response;
        }

        private boolean isAllEmpty(YieldSpreadTraceLineChartDTO response) {
            final BigDecimal[] ytm1YArr = response.getYtm1Ys();
            final BigDecimal[] ytm2YArr = response.getYtm2Ys();
            final BigDecimal[] ytm3YArr = response.getYtm3Ys();
            final BigDecimal[] ytm5YArr = response.getYtm5Ys();
            final BigDecimal[] ytm7YArr = response.getYtm7Ys();
            final BigDecimal[] ratingAAAPlusArr = response.getRatingAAAPluses();
            final BigDecimal[] ratingAAAArr = response.getRatingAAAs();
            final BigDecimal[] ratingAAASubArr = response.getRatingAAASubs();
            final BigDecimal[] ratingAAPlusArr = response.getRatingAAPluses();
            final BigDecimal[] ratingAAArr = response.getRatingAAs();
            final BigDecimal[] ratingAATwoArr = response.getRatingAATwos();
            final BigDecimal[] ratingAASubArr = response.getRatingAASubs();
            final Boolean allEmpty = ObjectExtensionUtils.isAllEmpty(ytm1YArr, ytm2YArr, ytm3YArr, ytm5YArr, ytm7YArr, ratingAAAPlusArr,
                    ratingAAAArr, ratingAAASubArr, ratingAAPlusArr, ratingAAArr, ratingAATwoArr, ratingAASubArr);
            return Boolean.TRUE.equals(allEmpty);
        }

        private Optional<BigDecimal[]> anyNotNull(List<BigDecimal> values) {
            return values.stream().anyMatch(Objects::nonNull) ? Optional.of(values.toArray(new BigDecimal[0])) : Optional.empty();
        }
    }
}
