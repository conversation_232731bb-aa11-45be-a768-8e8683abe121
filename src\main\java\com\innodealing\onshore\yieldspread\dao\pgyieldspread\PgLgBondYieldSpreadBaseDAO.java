package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.github.wz2cool.dynamic.SortDescriptor;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.enums.SpreadComAreaEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadCurveTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgLgBondYieldSpreadBaseMapper;
import com.innodealing.onshore.yieldspread.model.bo.YieldLgDataBO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.LgSpreadBaseRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.LgSpreadSelectedRequestDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBaseLgBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadBaseDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgQuantileStatisticsViewDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;

/**
 * 地方债利差统计base DAO
 *
 * <AUTHOR>
 * @create: 2024-11-05
 */
@Repository
public class PgLgBondYieldSpreadBaseDAO {

    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%s:%s:%s:%d:%d:%d";
    private static final String LG_BOND_YIELD_SPREAD_BASE_PK = "yieldSpread:lgBondYieldSpreadBasePk";
    private static final String PLACEHOLDER = "-";

    /**
     * 地方区域债 历史分位小数点
     */
    private static final int LG_QUANTILE_SCALE = 2;

    @Resource
    private PgLgBondYieldSpreadBaseMapper pgLgBondYieldSpreadBaseMapper;
    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;
    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;


    /**
     * 保存地方债利差统计base-数据集
     *
     * @param spreadDate                利差日期
     * @param lgBondYieldSpreadBaseList 地方债利差统计base数据集
     * @return int 保存行数
     */
    public int saveLgBondYieldSpreadBaseDOList(@NonNull Date spreadDate, List<PgLgBondYieldSpreadBaseDO> lgBondYieldSpreadBaseList) {
        if (CollectionUtils.isEmpty(lgBondYieldSpreadBaseList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        DynamicQuery<PgLgBondYieldSpreadBaseDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadBaseDO.class)
                .and(PgLgBondYieldSpreadBaseDO::getSpreadDate, isEqual(spreadDate));
        Map<String, PgLgBondYieldSpreadBaseDO> curveCodeIssueDateMap = pgLgBondYieldSpreadBaseMapper.selectByDynamicQuery(query).stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        List<PgLgBondYieldSpreadBaseDO> insertList = new ArrayList<>();
        List<PgLgBondYieldSpreadBaseDO> updateList = new ArrayList<>();
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        for (PgLgBondYieldSpreadBaseDO lgBondYieldSpreadBaseDO : lgBondYieldSpreadBaseList) {
            String key = this.getKey(lgBondYieldSpreadBaseDO);
            if (curveCodeIssueDateMap.containsKey(key)) {
                PgLgBondYieldSpreadBaseDO existLgBondYieldSpreadBase = curveCodeIssueDateMap.get(key);
                lgBondYieldSpreadBaseDO.setId(existLgBondYieldSpreadBase.getId());
                lgBondYieldSpreadBaseDO.setCreateTime(null);
                lgBondYieldSpreadBaseDO.setUpdateTime(now);
                updateList.add(lgBondYieldSpreadBaseDO);
            } else {
                lgBondYieldSpreadBaseDO.setId(redisService.generatePk(LG_BOND_YIELD_SPREAD_BASE_PK, lgBondYieldSpreadBaseDO.getSpreadDate()));
                lgBondYieldSpreadBaseDO.setCreateTime(now);
                lgBondYieldSpreadBaseDO.setUpdateTime(now);
                insertList.add(lgBondYieldSpreadBaseDO);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param pgLgBondYieldSpreadBaseDO 地方债利差统计base
     * @return {@link String} key值
     */
    private String getKey(PgLgBondYieldSpreadBaseDO pgLgBondYieldSpreadBaseDO) {
        Date spreadDate = pgLgBondYieldSpreadBaseDO.getSpreadDate();
        Long comUniCode = pgLgBondYieldSpreadBaseDO.getComUniCode();
        String lgBondType = Optional.ofNullable(pgLgBondYieldSpreadBaseDO.getLgBondType()).map(Object::toString).orElse(PLACEHOLDER);
        String prepaymentStatus = Optional.ofNullable(pgLgBondYieldSpreadBaseDO.getPrepaymentStatus()).map(Object::toString).orElse(PLACEHOLDER);
        String fundUseType = Optional.ofNullable(pgLgBondYieldSpreadBaseDO.getFundUseType()).map(Object::toString).orElse(PLACEHOLDER);
        Integer usingLgBondType = pgLgBondYieldSpreadBaseDO.getUsingLgBondType();
        Integer usingPrepaymentStatus = pgLgBondYieldSpreadBaseDO.getUsingPrepaymentStatus();
        Integer usingFundUseType = pgLgBondYieldSpreadBaseDO.getUsingFundUseType();
        return String.format(NAMESPACE_KEY_PLACEHOLDER, spreadDate.getTime(), comUniCode, lgBondType, prepaymentStatus, fundUseType,
                usingLgBondType, usingPrepaymentStatus, usingFundUseType);
    }

    /**
     * 批量更新
     *
     * @param updateList 利差追踪-历史分位数据集
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgLgBondYieldSpreadBaseDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgLgBondYieldSpreadBaseMapper> updateBatchAction =
                MapperBatchAction.create(PgLgBondYieldSpreadBaseMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgLgBondYieldSpreadBaseDO quantile : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgLgBondYieldSpreadBaseDO> updateQuery = DynamicQuery.createQuery(PgLgBondYieldSpreadBaseDO.class)
                        .and(PgLgBondYieldSpreadBaseDO::getId, isEqual(quantile.getId()));
                mapper.updateSelectiveByDynamicQuery(quantile, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 利差追踪-历史分位数据集
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgLgBondYieldSpreadBaseDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgLgBondYieldSpreadBaseMapper> insertBatchAction =
                MapperBatchAction.create(PgLgBondYieldSpreadBaseMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgLgBondYieldSpreadBaseDO quantile : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(quantile));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 查询地方债区域利差数据集
     *
     * @param spreadDate 利差日期
     * @return 地方债区域利差数据集
     */
    public List<PgLgBondYieldSpreadBaseDO> listLgBondYieldSpreadBase(Date spreadDate) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<PgLgBondYieldSpreadBaseDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadBaseDO.class)
                .and(PgLgBondYieldSpreadBaseDO::getSpreadDate, isEqual(spreadDate));
        return pgLgBondYieldSpreadBaseMapper.selectByDynamicQuery(query);
    }

    /**
     * 获取地方债利差数据
     *
     * @param areaConfigList 区域配置
     * @param requestDTO     请求对象
     * @return 地方债利差基础数据
     */
    public List<YieldLgDataBO> listYieldSpreadLgBase(@NotEmpty List<Long> areaConfigList, @NotNull LgSpreadBaseRequestDTO requestDTO) {
        FilterGroupDescriptor<PgLgBondYieldSpreadBaseDO> descriptor = requestDTO.listLgSpreadBaseFilters(PgLgBondYieldSpreadBaseDO.class);
        DynamicQuery<PgLgBondYieldSpreadBaseDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadBaseDO.class).ignore(PgLgBondYieldSpreadBaseDO::getId,
                        PgLgBondYieldSpreadBaseDO::getDeleted, PgLgBondYieldSpreadBaseDO::getCreateTime, PgBaseLgBondYieldSpreadDO::getUpdateTime)
                .and(PgLgBondYieldSpreadBaseDO::getComUniCode, in(areaConfigList))
                .and(descriptor);
        List<PgLgBondYieldSpreadBaseDO> pgLgBondYieldSpreadBaseDOs = pgLgBondYieldSpreadBaseMapper.selectByDynamicQuery(query);
        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, requestDTO.getSpreadDataType());
        return pgLgBondYieldSpreadBaseDOs.stream()
                .map(baseDO -> baseDO.convertToBO(spreadCurveTypeEnum).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 实时获取地方债区域利差分位数据
     *
     * @param requestDTO     请求参数
     * @param areaConfigList 区域列表
     * @param startDate      开始时间
     * @param endDate        结束时间
     * @return 数据列表
     */
    public List<YieldLgDataBO> listStatisticsLgQuantileBO(@NotNull LgSpreadBaseRequestDTO requestDTO, @NotEmpty List<Long> areaConfigList,
                                                          @NonNull Date startDate, @NonNull Date endDate) {
        List<PgLgQuantileStatisticsViewDO> lgQuantileStatisticsViewDOs = listLgQuantileViews(requestDTO, areaConfigList, startDate, endDate);
        return lgQuantileStatisticsViewDOs.stream().map(this::getLgQuantileBOByStatisticsView).collect(Collectors.toList());
    }


    /**
     * 查询历史分位数据
     *
     * @param requestDTO     债券类型列表
     * @param areaConfigList 地区配置列表
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @return 历史分位统计数据
     */
    public List<PgLgQuantileStatisticsViewDO> listLgQuantileViews(@NotNull LgSpreadBaseRequestDTO requestDTO, @NotEmpty List<Long> areaConfigList,
                                                                  @NonNull Date startDate, @NonNull Date endDate) {
        LocalDate localStartDate = startDate.toLocalDate();
        LocalDate localEndDate = endDate.toLocalDate();
        LocalDate localIssueDate = requestDTO.getSpreadDate().toLocalDate();
        if (localStartDate.isAfter(localEndDate)) {
            throw new TipsException("开始时间不能大于结束时间");
        }
        if (localIssueDate.isBefore(localStartDate) || localIssueDate.isAfter(localEndDate)) {
            throw new TipsException("发行时间不在自定义时间范围内");
        }
        return pgLgBondYieldSpreadBaseMapper.listLgQuantileStatisticsViews(requestDTO, areaConfigList, startDate, endDate);
    }

    private YieldLgDataBO getLgQuantileBOByStatisticsView(PgLgQuantileStatisticsViewDO lgQuantileStatisticsViewDO) {
        YieldLgDataBO yieldLgDataBO = new YieldLgDataBO();
        yieldLgDataBO.setComUniCode(lgQuantileStatisticsViewDO.getComUniCode());
        yieldLgDataBO.setLgAreaName(EnumUtils.getEnumByValue(lgQuantileStatisticsViewDO.getComUniCode().intValue(), SpreadComAreaEnum.class)
                .map(SpreadComAreaEnum::getText).orElse(null));

        Integer ytm1MLessIssueCount = lgQuantileStatisticsViewDO.getYtm1MLessIssueCount();
        Integer ytm1MCount = lgQuantileStatisticsViewDO.getYtm1MCount();
        yieldLgDataBO.setYield1M(CalculationHelper.safeCalPercentRankIgnore(ytm1MLessIssueCount, ytm1MCount, LG_QUANTILE_SCALE).orElse(null));
        //3月
        Integer ytm3MLessIssueCount = lgQuantileStatisticsViewDO.getYtm3MLessIssueCount();
        Integer ytm3MCount = lgQuantileStatisticsViewDO.getYtm3MCount();
        yieldLgDataBO.setYield3M(CalculationHelper.safeCalPercentRankIgnore(ytm3MLessIssueCount, ytm3MCount, LG_QUANTILE_SCALE).orElse(null));
        //6月
        Integer ytm6MLessIssueCount = lgQuantileStatisticsViewDO.getYtm6MLessIssueCount();
        Integer ytm6MCount = lgQuantileStatisticsViewDO.getYtm6MCount();
        yieldLgDataBO.setYield6M(CalculationHelper.safeCalPercentRankIgnore(ytm6MLessIssueCount, ytm6MCount, LG_QUANTILE_SCALE).orElse(null));
        // 9月
        Integer ytm9MLessIssueCount = lgQuantileStatisticsViewDO.getYtm9MLessIssueCount();
        Integer ytm9MCount = lgQuantileStatisticsViewDO.getYtm9MCount();
        yieldLgDataBO.setYield9M(CalculationHelper.safeCalPercentRankIgnore(ytm9MLessIssueCount, ytm9MCount, LG_QUANTILE_SCALE).orElse(null));
        // 1年
        Integer ytm1YLessIssueCount = lgQuantileStatisticsViewDO.getYtm1YLessIssueCount();
        Integer ytm1YCount = lgQuantileStatisticsViewDO.getYtm1YCount();
        yieldLgDataBO.setYield1Y(CalculationHelper.safeCalPercentRankIgnore(ytm1YLessIssueCount, ytm1YCount, LG_QUANTILE_SCALE).orElse(null));
        // 2年
        Integer ytm2YLessIssueCount = lgQuantileStatisticsViewDO.getYtm2YLessIssueCount();
        Integer ytm2YCount = lgQuantileStatisticsViewDO.getYtm2YCount();
        yieldLgDataBO.setYield2Y(CalculationHelper.safeCalPercentRankIgnore(ytm2YLessIssueCount, ytm2YCount, LG_QUANTILE_SCALE).orElse(null));
        // 3年
        Integer ytm3YLessIssueCount = lgQuantileStatisticsViewDO.getYtm3YLessIssueCount();
        Integer ytm3YCount = lgQuantileStatisticsViewDO.getYtm3YCount();
        yieldLgDataBO.setYield3Y(CalculationHelper.safeCalPercentRankIgnore(ytm3YLessIssueCount, ytm3YCount, LG_QUANTILE_SCALE).orElse(null));
        // 5年
        Integer ytm5YLessIssueCount = lgQuantileStatisticsViewDO.getYtm5YLessIssueCount();
        Integer ytm5YCount = lgQuantileStatisticsViewDO.getYtm5YCount();
        yieldLgDataBO.setYield5Y(CalculationHelper.safeCalPercentRankIgnore(ytm5YLessIssueCount, ytm5YCount, LG_QUANTILE_SCALE).orElse(null));
        // 7年
        Integer ytm7YLessIssueCount = lgQuantileStatisticsViewDO.getYtm7YLessIssueCount();
        Integer ytm7YCount = lgQuantileStatisticsViewDO.getYtm7YCount();
        yieldLgDataBO.setYield7Y(CalculationHelper.safeCalPercentRankIgnore(ytm7YLessIssueCount, ytm7YCount, LG_QUANTILE_SCALE).orElse(null));
        // 10年
        Integer ytm10YLessIssueCount = lgQuantileStatisticsViewDO.getYtm10YLessIssueCount();
        Integer ytm10YCount = lgQuantileStatisticsViewDO.getYtm10YCount();
        yieldLgDataBO.setYield10Y(CalculationHelper.safeCalPercentRankIgnore(ytm10YLessIssueCount, ytm10YCount, LG_QUANTILE_SCALE).orElse(null));
        // 15年
        Integer ytm15YLessIssueCount = lgQuantileStatisticsViewDO.getYtm15YLessIssueCount();
        Integer ytm15YCount = lgQuantileStatisticsViewDO.getYtm15YCount();
        yieldLgDataBO.setYield15Y(CalculationHelper.safeCalPercentRankIgnore(ytm15YLessIssueCount, ytm15YCount, LG_QUANTILE_SCALE).orElse(null));
        // 20年
        Integer ytm20YLessIssueCount = lgQuantileStatisticsViewDO.getYtm20YLessIssueCount();
        Integer ytm20YCount = lgQuantileStatisticsViewDO.getYtm20YCount();
        yieldLgDataBO.setYield20Y(CalculationHelper.safeCalPercentRankIgnore(ytm20YLessIssueCount, ytm20YCount, LG_QUANTILE_SCALE).orElse(null));
        // 30年
        Integer ytm30YLessIssueCount = lgQuantileStatisticsViewDO.getYtm30YLessIssueCount();
        Integer ytm30YCount = lgQuantileStatisticsViewDO.getYtm30YCount();
        yieldLgDataBO.setYield30Y(CalculationHelper.safeCalPercentRankIgnore(ytm30YLessIssueCount, ytm30YCount, LG_QUANTILE_SCALE).orElse(null));
        // 50年
        Integer ytm50YLessIssueCount = lgQuantileStatisticsViewDO.getYtm50YLessIssueCount();
        Integer ytm50YCount = lgQuantileStatisticsViewDO.getYtm50YCount();
        yieldLgDataBO.setYield50Y(CalculationHelper.safeCalPercentRankIgnore(ytm50YLessIssueCount, ytm50YCount, LG_QUANTILE_SCALE).orElse(null));
        return yieldLgDataBO;
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgLgBondYieldSpreadBaseDO> dynamicQuery = DynamicQuery.createQuery(PgLgBondYieldSpreadBaseDO.class)
                .orderBy(PgLgBondYieldSpreadBaseDO::getSpreadDate, SortDirections::desc);
        return pgLgBondYieldSpreadBaseMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgLgBondYieldSpreadBaseDO::getSpreadDate);
    }

    /**
     * 获取地方债区域利差曲线所需数据
     *
     * @param startDate     开始时间
     * @param endDate       结束时间
     * @param lgSelectedDTO 筛选项
     * @param sort          排序对象
     * @return 相关数据
     */
    public List<PgLgBondYieldSpreadBaseDO> listSpreadLgLineData(@NotNull Date startDate,
                                                                @NotNull Date endDate,
                                                                @NotNull LgSpreadSelectedRequestDTO lgSelectedDTO,
                                                                SortDTO sort) {
        FilterGroupDescriptor<PgLgBondYieldSpreadBaseDO> descriptor = lgSelectedDTO.listLgSpreadSelectedFilters(PgLgBondYieldSpreadBaseDO.class);
        DynamicQuery<PgLgBondYieldSpreadBaseDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadBaseDO.class).ignore(PgLgBondYieldSpreadBaseDO::getId,
                        PgLgBondYieldSpreadBaseDO::getDeleted, PgLgBondYieldSpreadBaseDO::getCreateTime, PgBaseLgBondYieldSpreadDO::getUpdateTime)
                .and(PgLgBondYieldSpreadBaseDO::getSpreadDate, between(startDate, endDate))
                .and(descriptor);
        if (Objects.nonNull(sort)) {
            query.addSorts(new SortDescriptor(sort.getPropertyName(), sort.getSortDirection()));
        } else {
            query.orderBy(PgLgBondYieldSpreadBaseDO::getSpreadDate, SortDirections::asc);
        }
        return pgLgBondYieldSpreadBaseMapper.selectByDynamicQuery(query);
    }
}
