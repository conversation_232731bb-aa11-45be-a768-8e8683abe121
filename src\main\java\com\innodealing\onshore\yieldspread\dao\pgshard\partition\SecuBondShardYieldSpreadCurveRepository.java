package com.innodealing.onshore.yieldspread.dao.pgshard.partition;

import com.github.wz2cool.dynamic.BaseFilterDescriptor;
import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.SecuShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.SecuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.SecuShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 证券利差曲线分区
 *
 * <AUTHOR>
 */
@Repository
public class SecuBondShardYieldSpreadCurveRepository {

    @Resource
    private SecuShardBondYieldSpreadCurveMapper secuYieldSpreadCurveMapper;

    /**
     * 银行债券利差分片查询
     *
     * @param params 查询参数
     * @param router 路由
     * @return 分片表结果集
     */
    public List<SecuShardBondYieldSpreadCurveDO> listSecuYieldSpreads(SecuYieldSearchParam params, AbstractRatingRouter router) {
        BaseFilterDescriptor<SecuShardBondYieldSpreadCurveDO>[] baseFilterDescriptors = this.listFilters(params).getFilters();
        DynamicQuery<SecuShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(SecuShardBondYieldSpreadCurveDO.class)
                .and(baseFilterDescriptors);
        return secuYieldSpreadCurveMapper.selectByDynamicQueryRouter(query, router);
    }

    private FilterGroupDescriptor<SecuShardBondYieldSpreadCurveDO> listFilters(SecuYieldSearchParam params) {
        return FilterGroupDescriptor.create(SecuShardBondYieldSpreadCurveDO.class)
                .and(nonNull(params.getSpreadBondType()), SecuShardBondYieldSpreadCurveDO::getSecuritySeniorityRanking, isEqual(params.getSpreadBondType()))
                .and(isNull(params.getSpreadBondType()), SecuShardBondYieldSpreadCurveDO::getUsingSecuritySeniorityRanking, isEqual(UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(params.getRemainingTenor()), SecuShardBondYieldSpreadCurveDO::getSpreadRemainingTenorTag, isEqual(params.getRemainingTenor()))
                .and(isNull(params.getRemainingTenor()), SecuShardBondYieldSpreadCurveDO::getUsingSpreadRemainingTenorTag, isEqual(UNUSED_FIELD_GROUP.getValue()))
                ;
    }

}
