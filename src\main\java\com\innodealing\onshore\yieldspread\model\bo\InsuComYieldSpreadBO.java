package com.innodealing.onshore.yieldspread.model.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 保险主体利差
 *
 * <AUTHOR>
 */
public class InsuComYieldSpreadBO extends BaseFinanceComYieldSpreadBO {

    @ApiModelProperty("企业性质")
    private Integer businessNature;

    @ApiModelProperty("主体信用利差(资本补充);单位(BP)")
    private BigDecimal comTier2CreditSpread;

    @ApiModelProperty("主体超额利差(资本补充);单位(BP)")
    private BigDecimal comTier2ExcessSpread;

    @ApiModelProperty("主体估值收益率(资本补充);单位(%)")
    private BigDecimal comTier2CbYield;

    public Integer getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(Integer businessNature) {
        this.businessNature = businessNature;
    }

    public BigDecimal getComTier2CreditSpread() {
        return comTier2CreditSpread;
    }

    public void setComTier2CreditSpread(BigDecimal comTier2CreditSpread) {
        this.comTier2CreditSpread = comTier2CreditSpread;
    }

    public BigDecimal getComTier2ExcessSpread() {
        return comTier2ExcessSpread;
    }

    public void setComTier2ExcessSpread(BigDecimal comTier2ExcessSpread) {
        this.comTier2ExcessSpread = comTier2ExcessSpread;
    }

    public BigDecimal getComTier2CbYield() {
        return comTier2CbYield;
    }

    public void setComTier2CbYield(BigDecimal comTier2CbYield) {
        this.comTier2CbYield = comTier2CbYield;
    }
}
