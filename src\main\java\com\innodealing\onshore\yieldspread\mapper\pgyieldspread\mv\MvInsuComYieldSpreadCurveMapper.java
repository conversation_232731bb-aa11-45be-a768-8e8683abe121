package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvInsuBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvInsuComYieldSpreadCurveDO;

/**
 * 保险利差曲线物化视图
 *
 * <AUTHOR>
 */
public interface MvInsuComYieldSpreadCurveMapper extends PgBaseMapper<MvInsuBondYieldSpreadCurveParameter>, DynamicQueryMapper<MvInsuComYieldSpreadCurveDO> {


}
