package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * 曲线定义数据数据基础信息
 *
 * <AUTHOR>
 */
public class CurveDefinitionBasicInfoResDTO {

    @ApiModelProperty("曲线id")
    private Long id;

    @ApiModelProperty("曲线组id")
    private Long curveGroupId;

    @ApiModelProperty("曲线名称")
    private String spreadCurveName;

    @ApiModelProperty("曲线类型：产业=1，城投=2，银行=3，证券=4，自选债=5，基准曲线=6")
    private Integer spreadCurveType;

    @ApiModelProperty("曲线排序序号")
    private Integer curveOrder;

    @ApiModelProperty("是否勾选")
    private Boolean selected;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSpreadCurveName() {
        return spreadCurveName;
    }

    public void setSpreadCurveName(String spreadCurveName) {
        this.spreadCurveName = spreadCurveName;
    }

    public Integer getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(Integer spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public Long getCurveGroupId() {
        return curveGroupId;
    }

    public void setCurveGroupId(Long curveGroupId) {
        this.curveGroupId = curveGroupId;
    }

    public Integer getCurveOrder() {
        return curveOrder;
    }

    public void setCurveOrder(Integer curveOrder) {
        this.curveOrder = curveOrder;
    }

    @Override
    public String toString() {
        return "CurveDefinitionBasicInfoResDTO{" +
                "id=" + id +
                ", curveGroupId=" + curveGroupId +
                ", spreadCurveName='" + spreadCurveName + '\'' +
                ", spreadCurveType=" + spreadCurveType +
                ", curveOrder=" + curveOrder +
                ", selected=" + selected +
                '}';
    }

}
