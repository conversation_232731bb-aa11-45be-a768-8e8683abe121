package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * 资金综合屏-银行间资金历史流水列表响应对象
 *
 * <AUTHOR>
 */
public class CcsCfetsRepurchaseFlowResponseDTO {

    @ApiModelProperty("品种")
    private String bondCode;

    @ApiModelProperty("加权利率")
    private BigDecimal weightedYield;

    @ApiModelProperty("加权涨跌bp")
    private BigDecimal weightedYieldUpDownValueBp;

    @ApiModelProperty("最新价")
    private BigDecimal lastPrice;

    @ApiModelProperty("最新涨跌bp")
    private BigDecimal upDownValueBp;

    @ApiModelProperty("开盘")
    private BigDecimal openYield;
    @ApiModelProperty("最高")
    private BigDecimal highYield;

    @ApiModelProperty("最低")
    private BigDecimal lowYield;

    @ApiModelProperty("收盘")
    private BigDecimal closeYield;

    @ApiModelProperty("昨收价")
    private BigDecimal preClosePrice;

    @ApiModelProperty("前加权")
    private BigDecimal preWeightedYield;

    @ApiModelProperty("成交量(万)")
    private BigDecimal tradeAmount;

    @ApiModelProperty("发行时间")
    private Timestamp issueTime;

    @ApiModelProperty("流水id")
    private Long flowId;

    @ApiModelProperty("品种简称")
    private String bondShortName;

    @ApiModelProperty("排序编号")
    private Integer sortNo;

    @ApiModelProperty("回购来源 1:银存间(DR) 2:买断式(OR) 3:信用拆借(IBO) 4:银借贷 5:银行间回购(R) 6:基准利率")
    private Integer repurchaseSource;

    @ApiModelProperty("加权平均利率(利率债)(%)")
    private BigDecimal weightedAvgRate;

    @ApiModelProperty("平均回购期限(天)")
    private BigDecimal avgRepurchaseTenor;

    @ApiModelProperty("成交笔数(笔)")
    private Long tradingNum;

    @ApiModelProperty("前加权平均利率(利率债)(%)")
    private BigDecimal preWeightedAvgRate;

    @ApiModelProperty("5日平均")
    private BigDecimal yieldMa5;

    @ApiModelProperty("10日平均")
    private BigDecimal yieldMa10;

    @ApiModelProperty("20日平均")
    private BigDecimal yieldMa20;

    @ApiModelProperty("60日平均")
    private BigDecimal yieldMa60;

    @ApiModelProperty("120日平均")
    private BigDecimal yieldMa120;

    @ApiModelProperty("250日平均")
    private BigDecimal yieldMa250;

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public BigDecimal getWeightedYield() {
        return weightedYield;
    }

    public void setWeightedYield(BigDecimal weightedYield) {
        this.weightedYield = weightedYield;
    }

    public BigDecimal getWeightedYieldUpDownValueBp() {
        return weightedYieldUpDownValueBp;
    }

    public void setWeightedYieldUpDownValueBp(BigDecimal weightedYieldUpDownValueBp) {
        this.weightedYieldUpDownValueBp = weightedYieldUpDownValueBp;
    }

    public BigDecimal getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(BigDecimal lastPrice) {
        this.lastPrice = lastPrice;
    }

    public BigDecimal getUpDownValueBp() {
        return upDownValueBp;
    }

    public void setUpDownValueBp(BigDecimal upDownValueBp) {
        this.upDownValueBp = upDownValueBp;
    }

    public BigDecimal getOpenYield() {
        return openYield;
    }

    public void setOpenYield(BigDecimal openYield) {
        this.openYield = openYield;
    }

    public BigDecimal getHighYield() {
        return highYield;
    }

    public void setHighYield(BigDecimal highYield) {
        this.highYield = highYield;
    }

    public BigDecimal getLowYield() {
        return lowYield;
    }

    public void setLowYield(BigDecimal lowYield) {
        this.lowYield = lowYield;
    }

    public BigDecimal getPreWeightedYield() {
        return preWeightedYield;
    }

    public void setPreWeightedYield(BigDecimal preWeightedYield) {
        this.preWeightedYield = preWeightedYield;
    }

    public BigDecimal getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(BigDecimal tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public Timestamp getIssueTime() {
        return Objects.isNull(issueTime) ? null : new Timestamp(issueTime.getTime());
    }

    public void setIssueTime(Timestamp issueTime) {
        this.issueTime = Objects.isNull(issueTime) ? null : new Timestamp(issueTime.getTime());
    }

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

    public BigDecimal getCloseYield() {
        return closeYield;
    }

    public void setCloseYield(BigDecimal closeYield) {
        this.closeYield = closeYield;
    }

    public BigDecimal getPreClosePrice() {
        return preClosePrice;
    }

    public void setPreClosePrice(BigDecimal preClosePrice) {
        this.preClosePrice = preClosePrice;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public Integer getRepurchaseSource() {
        return repurchaseSource;
    }

    public void setRepurchaseSource(Integer repurchaseSource) {
        this.repurchaseSource = repurchaseSource;
    }

    public BigDecimal getWeightedAvgRate() {
        return weightedAvgRate;
    }

    public void setWeightedAvgRate(BigDecimal weightedAvgRate) {
        this.weightedAvgRate = weightedAvgRate;
    }

    public BigDecimal getAvgRepurchaseTenor() {
        return avgRepurchaseTenor;
    }

    public void setAvgRepurchaseTenor(BigDecimal avgRepurchaseTenor) {
        this.avgRepurchaseTenor = avgRepurchaseTenor;
    }

    public Long getTradingNum() {
        return tradingNum;
    }

    public void setTradingNum(Long tradingNum) {
        this.tradingNum = tradingNum;
    }

    public BigDecimal getPreWeightedAvgRate() {
        return preWeightedAvgRate;
    }

    public void setPreWeightedAvgRate(BigDecimal preWeightedAvgRate) {
        this.preWeightedAvgRate = preWeightedAvgRate;
    }

    public BigDecimal getYieldMa5() {
        return yieldMa5;
    }

    public void setYieldMa5(BigDecimal yieldMa5) {
        this.yieldMa5 = yieldMa5;
    }

    public BigDecimal getYieldMa10() {
        return yieldMa10;
    }

    public void setYieldMa10(BigDecimal yieldMa10) {
        this.yieldMa10 = yieldMa10;
    }

    public BigDecimal getYieldMa20() {
        return yieldMa20;
    }

    public void setYieldMa20(BigDecimal yieldMa20) {
        this.yieldMa20 = yieldMa20;
    }

    public BigDecimal getYieldMa60() {
        return yieldMa60;
    }

    public void setYieldMa60(BigDecimal yieldMa60) {
        this.yieldMa60 = yieldMa60;
    }

    public BigDecimal getYieldMa120() {
        return yieldMa120;
    }

    public void setYieldMa120(BigDecimal yieldMa120) {
        this.yieldMa120 = yieldMa120;
    }

    public BigDecimal getYieldMa250() {
        return yieldMa250;
    }

    public void setYieldMa250(BigDecimal yieldMa250) {
        this.yieldMa250 = yieldMa250;
    }
}
