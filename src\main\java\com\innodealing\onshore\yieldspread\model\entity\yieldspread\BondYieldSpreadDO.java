package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 债券利差
 *
 * <AUTHOR>
 */
@Table(name = "bond_yield_spread")
public class BondYieldSpreadDO {

    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;

    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;

    /**
     * 债券统一编码
     */
    @Column
    private Long bondUniCode;

    /**
     * 债券编码
     */
    @Column
    private String bondCode;

    /**
     * 剩余期限
     */
    @Column
    private String remainingTenor;

    /**
     * 剩余期限天数
     */
    @Column
    private Integer remainingTenorDay;

    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;

    /**
     * 债券信用利差;单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpread;

    /**
     * 债券超额利差;单位(BP)
     */
    @Column
    private BigDecimal bondExcessSpread;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public Integer getRemainingTenorDay() {
        return remainingTenorDay;
    }

    public void setRemainingTenorDay(Integer remainingTenorDay) {
        this.remainingTenorDay = remainingTenorDay;
    }

}
