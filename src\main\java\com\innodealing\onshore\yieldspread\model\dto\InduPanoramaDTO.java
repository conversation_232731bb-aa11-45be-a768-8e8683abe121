package com.innodealing.onshore.yieldspread.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 行业利差全景DTO
 *
 * <AUTHOR>
 */
public class InduPanoramaDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 行业编码
     */
    private Long industryCode;
    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 信用利差
     */
    private BigDecimal bondCreditSpread;

    /**
     * 90天前信用利差
     */
    private BigDecimal bondCreditSpreadBefore90;

    /**
     * 180天前信用利差
     */
    private BigDecimal bondCreditSpreadBefore180;

    /**
     * 90天前信用利差变动
     */
    private BigDecimal bondCreditSpreadChange90;

    /**
     * 180天前信用利差变动
     */
    private BigDecimal bondCreditSpreadChange180;

    /**
     * 超额利差
     */
    private BigDecimal bondExcessSpread;

    /**
     * 90天前超额利差
     */
    private BigDecimal bondExcessSpreadBefore90;

    /**
     * 180天前超额利差
     */
    private BigDecimal bondExcessSpreadBefore180;

    /**
     * 90天前超额利差变动
     */
    private BigDecimal bondExcessSpreadChange90;

    /**
     * 180天前超额利差变动
     */
    private BigDecimal bondExcessSpreadChange180;

    /**
     * 显示序号
     */
    private Integer showNo;

    public Long getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(Long industryCode) {
        this.industryCode = industryCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondCreditSpreadBefore90() {
        return bondCreditSpreadBefore90;
    }

    public void setBondCreditSpreadBefore90(BigDecimal bondCreditSpreadBefore90) {
        this.bondCreditSpreadBefore90 = bondCreditSpreadBefore90;
    }

    public BigDecimal getBondCreditSpreadBefore180() {
        return bondCreditSpreadBefore180;
    }

    public void setBondCreditSpreadBefore180(BigDecimal bondCreditSpreadBefore180) {
        this.bondCreditSpreadBefore180 = bondCreditSpreadBefore180;
    }

    public BigDecimal getBondCreditSpreadChange90() {
        return bondCreditSpreadChange90;
    }

    public void setBondCreditSpreadChange90(BigDecimal bondCreditSpreadChange90) {
        this.bondCreditSpreadChange90 = bondCreditSpreadChange90;
    }

    public BigDecimal getBondCreditSpreadChange180() {
        return bondCreditSpreadChange180;
    }

    public void setBondCreditSpreadChange180(BigDecimal bondCreditSpreadChange180) {
        this.bondCreditSpreadChange180 = bondCreditSpreadChange180;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getBondExcessSpreadBefore90() {
        return bondExcessSpreadBefore90;
    }

    public void setBondExcessSpreadBefore90(BigDecimal bondExcessSpreadBefore90) {
        this.bondExcessSpreadBefore90 = bondExcessSpreadBefore90;
    }

    public BigDecimal getBondExcessSpreadBefore180() {
        return bondExcessSpreadBefore180;
    }

    public void setBondExcessSpreadBefore180(BigDecimal bondExcessSpreadBefore180) {
        this.bondExcessSpreadBefore180 = bondExcessSpreadBefore180;
    }

    public BigDecimal getBondExcessSpreadChange90() {
        return bondExcessSpreadChange90;
    }

    public void setBondExcessSpreadChange90(BigDecimal bondExcessSpreadChange90) {
        this.bondExcessSpreadChange90 = bondExcessSpreadChange90;
    }

    public BigDecimal getBondExcessSpreadChange180() {
        return bondExcessSpreadChange180;
    }

    public void setBondExcessSpreadChange180(BigDecimal bondExcessSpreadChange180) {
        this.bondExcessSpreadChange180 = bondExcessSpreadChange180;
    }

    public Integer getShowNo() {
        return showNo;
    }

    public void setShowNo(Integer showNo) {
        this.showNo = showNo;
    }
}
