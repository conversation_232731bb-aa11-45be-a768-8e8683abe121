package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBankBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgBankBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgSecuBondYieldSpreadCbYieldGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgSecuBondYieldSpreadCreditYieldGroupMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.BankYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgBankBondYieldSpreadGroupDO;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.SPREAD_KEEP_SCALE;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE;
import static java.util.Objects.nonNull;

/**
 * 证券债利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class PgBankBondYieldSpreadDAO {

    @Resource
    private PgBankBondYieldSpreadMapper pgBankBondYieldSpreadMapper;

    @Resource
    private PgBankBondYieldSpreadGroupMapper pgBankBondYieldSpreadGroupMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private PgSecuBondYieldSpreadCbYieldGroupMapper pgSecuBondYieldSpreadCbYieldGroupMapper;

    @Resource
    private PgSecuBondYieldSpreadCreditYieldGroupMapper pgSecuBondYieldSpreadCreditYieldGroupMapper;

    /**
     * 计算中债估值中位数
     *
     * @param comUniCodes              发行人代码
     * @param securitySeniorityRanking 证券公司债求偿顺序   1:普通（剔除永续）、2:次级（剔除永续）、 3:永续"
     * @param spreadDate               利差日期
     * @return key 发行人代码,行业债利差
     */
    public Map<Long, PgBankBondYieldSpreadGroupDO> getBankBondYieldSpreadMap(Set<Long> comUniCodes, Integer securitySeniorityRanking,
                                                                             Date spreadDate) {
        GroupedQuery<PgBankBondYieldSpreadDO, PgBankBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgBankBondYieldSpreadDO.class, PgBankBondYieldSpreadGroupDO.class)
                        .select(PgBankBondYieldSpreadGroupDO::getComUniCode, PgBankBondYieldSpreadGroupDO::getCbYield,
                                PgBankBondYieldSpreadGroupDO::getBondCreditSpread, PgBankBondYieldSpreadGroupDO::getBondExcessSpread)
                        .and(PgBankBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(PgBankBondYieldSpreadDO::getComUniCode, in(comUniCodes))
                        .and(Objects.nonNull(securitySeniorityRanking), PgBankBondYieldSpreadDO::getBankSeniorityRanking, isEqual(securitySeniorityRanking))
                        .groupBy(PgBankBondYieldSpreadDO::getComUniCode);
        List<PgBankBondYieldSpreadGroupDO> pgInduBondYieldSpreadGroupDs = pgBankBondYieldSpreadGroupMapper.
                selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(pgInduBondYieldSpreadGroupDs)) {
            return Collections.emptyMap();
        }
        return pgInduBondYieldSpreadGroupDs.stream()
                .filter(Objects::nonNull)
                .map(x -> {
                    PgBankBondYieldSpreadGroupDO result = BeanCopyUtils.copyProperties(x, PgBankBondYieldSpreadGroupDO.class);
                    if (Objects.nonNull(x.getCbYield())) {
                        result.setCbYield(x.getCbYield().setScale(YIELD_SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondCreditSpread())) {
                        result.setBondCreditSpread(x.getBondCreditSpread()
                                .setScale(SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondExcessSpread())) {
                        result.setBondExcessSpread(x.getBondExcessSpread()
                                .setScale(SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    return result;
                }).collect(Collectors.toMap(PgBankBondYieldSpreadGroupDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 批量更新
     *
     * @param pgBankBondYieldSpreadDOList 证券债利差列表
     * @param spreadDate                  利差日期
     * @return 受影响的行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int savePgBankBondYieldSpreadDOList(Date spreadDate, List<PgBankBondYieldSpreadDO> pgBankBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(pgBankBondYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> bondUniCodes = pgBankBondYieldSpreadDOList.stream().map(PgBankBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<PgBankBondYieldSpreadDO> query = DynamicQuery.createQuery(PgBankBondYieldSpreadDO.class)
                .select(PgBankBondYieldSpreadDO::getId, PgBankBondYieldSpreadDO::getBondUniCode, PgBankBondYieldSpreadDO::getSpreadDate)
                .and(PgBankBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(PgBankBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<PgBankBondYieldSpreadDO> existDataList = pgBankBondYieldSpreadMapper.selectByDynamicQuery(query);
        Map<String, PgBankBondYieldSpreadDO> existPgBankBondYieldSpreadDO = existDataList.stream().collect(Collectors.toMap(x ->
                        String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                Function.identity(), (x1, x2) -> x2));
        MapperBatchAction<PgBankBondYieldSpreadMapper> batchAction = MapperBatchAction.
                create(PgBankBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgBankBondYieldSpreadDO pgBankBondYieldSpreadDO : pgBankBondYieldSpreadDOList) {
            PgBankBondYieldSpreadDO existData = existPgBankBondYieldSpreadDO.get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                    pgBankBondYieldSpreadDO.getBondUniCode(),
                    pgBankBondYieldSpreadDO.getSpreadDate().getTime()));
            batchAction.addAction(mapper -> {
                if (Objects.nonNull(existData)) {
                    pgBankBondYieldSpreadDO.setId(existData.getId());
                    mapper.updateByPrimaryKey(pgBankBondYieldSpreadDO);
                } else {
                    mapper.insert(pgBankBondYieldSpreadDO);
                }
            });
        }
        List<BatchResult> batchResults = batchAction.doBatchActionWithResults();
        return batchResults.stream().mapToInt(x -> {
            int effectRows = Arrays.stream(x.getUpdateCounts()).sum();
            if (effectRows > 0) {
                return effectRows;
            } else {
                return x.getParameterObjects().size();
            }
        }).sum();
    }

    /**
     * 查询银行利差
     *
     * @param params 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listBondYieldSpreads(BankYieldSearchParam params) {
        GroupedQuery<PgBankBondYieldSpreadDO, PgBankBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgBankBondYieldSpreadDO.class, PgBankBondYieldSpreadGroupDO.class)
                        .select(PgBankBondYieldSpreadGroupDO::getSpreadDate,
                                PgBankBondYieldSpreadGroupDO::getBondCreditSpread,
                                PgBankBondYieldSpreadGroupDO::getBondExcessSpread,
                                PgBankBondYieldSpreadGroupDO::getCbYield,
                                PgBankBondYieldSpreadGroupDO::getAvgBondCreditSpread,
                                PgBankBondYieldSpreadGroupDO::getAvgBondExcessSpread,
                                PgBankBondYieldSpreadGroupDO::getAvgCbYield,
                                PgBankBondYieldSpreadGroupDO::getBondCreditSpreadCount,
                                PgBankBondYieldSpreadGroupDO::getBondExcessSpreadCount,
                                PgBankBondYieldSpreadGroupDO::getCbYieldCount)
                        .and(Objects.nonNull(params.getSpreadBondType()), PgBankBondYieldSpreadDO::getBankSeniorityRanking, isEqual(params.getSpreadBondType()))
                        .and(!CollectionUtils.isEmpty(params.getBankTypes()), PgBankBondYieldSpreadDO::getBankType, in(params.getBankTypes()))
                        .and(Objects.nonNull(params.getRemainingTenor()), PgBankBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(params.getRemainingTenor()))
                        .and(ArrayUtils.isNotEmpty(params.getBondImpliedRatingMappings()),
                                PgBankBondYieldSpreadDO::getBondImpliedRatingMapping, in(params.getBondImpliedRatingMappings()))
                        .and(Objects.nonNull(params.getComUniCode()), PgBankBondYieldSpreadDO::getComUniCode, isEqual(params.getComUniCode()))
                        .and(Objects.nonNull(params.getBondUniCode()), PgBankBondYieldSpreadDO::getBondUniCode, isEqual(params.getBondUniCode()))
                        .groupBy(PgBankBondYieldSpreadDO::getSpreadDate)
                        .orderBy(PgBankBondYieldSpreadGroupDO::getSpreadDate, asc());
        List<PgBankBondYieldSpreadGroupDO> bondYieldSpreads = pgBankBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(bondYieldSpreads)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(bondYieldSpreads, BondYieldSpreadBO.class);
    }

    /**
     * 获取最新利差日期
     *
     * @return 最新利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgBankBondYieldSpreadDO> query = DynamicQuery.createQuery(PgBankBondYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt = pgBankBondYieldSpreadMapper.selectMaxByDynamicQuery(PgBankBondYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 查询并计算利差曲线数据-单券利差方式
     *
     * @param bondUniCode     债券唯一编码
     * @param startSpreadDate 利差开始日期
     * @param endSpreadDate   利差结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 债券每天中位数数据集合
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurvesByBond(Long bondUniCode, Date startSpreadDate, Date endSpreadDate) {
        if (Objects.isNull(bondUniCode)) {
            return Collections.emptyList();
        }
        DynamicQuery<PgBankBondYieldSpreadDO> query = DynamicQuery.createQuery(PgBankBondYieldSpreadDO.class)
                .select(PgBankBondYieldSpreadDO::getSpreadDate, PgBankBondYieldSpreadDO::getBondCreditSpread,
                        PgBankBondYieldSpreadDO::getBondExcessSpread, PgBankBondYieldSpreadDO::getCbYield)
                .and(PgBankBondYieldSpreadDO::getBondUniCode, isEqual(bondUniCode))
                .and(nonNull(startSpreadDate), PgBankBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startSpreadDate))
                .and(nonNull(endSpreadDate), PgBankBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(endSpreadDate))
                .orderBy(PgBankBondYieldSpreadDO::getSpreadDate, SortDirections::asc);
        List<PgBankBondYieldSpreadDO> pgUdicBondYieldSpreadList = pgBankBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> copyList = Lists.newArrayListWithExpectedSize(pgUdicBondYieldSpreadList.size());
        for (PgBankBondYieldSpreadDO pgBankBondYieldSpreadDO : pgUdicBondYieldSpreadList) {
            BondYieldSpreadCurveBO clone = BeanCopyUtils.copyProperties(pgBankBondYieldSpreadDO, BondYieldSpreadCurveBO.class);
            BigDecimalUtils.handlerPrecision(clone.getBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondCreditSpread);
            BigDecimalUtils.handlerPrecision(clone.getBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondExcessSpread);
            BigDecimalUtils.handlerPrecision(clone.getCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCbYield);
            BigDecimalUtils.handlerPrecision(clone.getCdbLerpYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCdbLerpYield);
            copyList.add(clone);
        }
        return copyList;
    }

}

