package com.innodealing.onshore.yieldspread.helper;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.InduShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.UdicShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgUdicBondYieldSpreadGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu1DO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition.PgInduBondYieldSpreadCurveIndu2DO;

import java.util.Objects;

/**
 * Sharding参数转换
 * <AUTHOR>
 */
public final class ShardConverter {

    private ShardConverter() {
    }

    /**
     * 城投对象转换
     * @param udicShardDo shardDo
     * @param responseClass  非shardDo
     * @return response
     */
    public static Object converterUdicToPg(UdicShardBondYieldSpreadCurveDO udicShardDo, Class<?> responseClass) {
        if (Objects.isNull(udicShardDo)){
            return null;
        }
        if (PgUdicBondYieldSpreadGroupDO.class.isAssignableFrom(responseClass)){
            PgUdicBondYieldSpreadGroupDO curveInduDO = BeanCopyUtils.copyProperties(udicShardDo, PgUdicBondYieldSpreadGroupDO.class);
            curveInduDO.setProvinceUniCode(udicShardDo.getOperatorLevel());
            curveInduDO.setCityUniCode(udicShardDo.getOperatorLevel());
            return curveInduDO;
        }
        return BeanCopyUtils.copyProperties(udicShardDo, responseClass);
    }

    /**
     * 产业对象转换
     * @param induShardDo shardDo
     * @param responseClass  非shardDo
     * @return response
     */
    public static Object converterInduToPg(InduShardBondYieldSpreadCurveDO induShardDo, Class<?> responseClass) {
        if (Objects.isNull(induShardDo)){
            return null;
        }
        // 原始方法放回类型
        if (PgInduBondYieldSpreadCurveIndu1DO.class.isAssignableFrom(responseClass)) {
            PgInduBondYieldSpreadCurveIndu1DO curveInduDO = BeanCopyUtils.copyProperties(induShardDo, PgInduBondYieldSpreadCurveIndu1DO.class);
            curveInduDO.setInduLevel1Code(induShardDo.getInduLevelCode());
            return curveInduDO;
        }
        if (PgInduBondYieldSpreadCurveIndu2DO.class.isAssignableFrom(responseClass)) {
            PgInduBondYieldSpreadCurveIndu2DO curveInduDO = BeanCopyUtils.copyProperties(induShardDo, PgInduBondYieldSpreadCurveIndu2DO.class);
            curveInduDO.setInduLevel2Code(induShardDo.getInduLevelCode());
            return curveInduDO;
        }
        return BeanCopyUtils.copyProperties(induShardDo, responseClass);
    }
}
