package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.AreaTypeEnum;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.yieldspread.enums.*;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.COMMA;

/**
 * 城投全景导出请求dto
 *
 * <AUTHOR>
 */
public class UdicPanoramaExportRequestDTO extends UdicPanoramaRequestDTO {
    private static final long serialVersionUID = 1L;
    /**
     * 行政区划映射
     */
    private static final Map<Integer, String> ADMINISTRATIVE_DIVISION_MAP = new HashMap<>();

    static {
        ADMINISTRATIVE_DIVISION_MAP.put(AreaTypeEnum.PROVINCE.getValue(), "省级");
        ADMINISTRATIVE_DIVISION_MAP.put(AreaTypeEnum.CITY.getValue(), "市级");
        ADMINISTRATIVE_DIVISION_MAP.put(AreaTypeEnum.DISTRICT.getValue(), "区/县级");
        ADMINISTRATIVE_DIVISION_MAP.put(AreaTypeEnum.ZONE.getValue(), "园区");
    }

    @ApiModelProperty(value = "年份", required = true)
    private Integer year;
    @ApiModelProperty(value = "年跨度，1:上半年, 2:下半年", required = true)
    private Integer yearSpan;
    @ApiModelProperty(value = "1:省级，2:市级", required = true)
    private Integer areaType;

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getYearSpan() {
        return yearSpan;
    }

    public void setYearSpan(Integer yearSpan) {
        this.yearSpan = yearSpan;
    }

    public Integer getAreaType() {
        return areaType;
    }

    public void setAreaType(Integer areaType) {
        this.areaType = areaType;
    }

    @Override
    public String toString() {
        return "UdicPanoramaExportRequestDTO{" +
                "year=" + year +
                ", yearSpan=" + yearSpan +
                ", areaType=" + areaType +
                ", administrativeDivision=" + administrativeDivision +
                ", bondExtRatingMapping=" + bondExtRatingMapping +
                ", bondImpliedRatingMappingTag=" + bondImpliedRatingMappingTag +
                ", comYyRatingMappingTag=" + comYyRatingMappingTag +
                ", spreadRemainingTenorTag=" + spreadRemainingTenorTag +
                ", spreadBondType=" + spreadBondType +
                ", guaranteeStatus=" + guaranteeStatus +
                '}';
    }

    /**
     * 构建导出标题
     *
     * @return {@link String} 导出标题
     */
    public String buildExportTitle() {
        StringBuilder sb = new StringBuilder("数据来源:DM,");
        EnumUtils.getEnumByValue(yearSpan, SpreadYearSpanEnum.class).ifPresent(span -> sb.append(year).append(span.getText()).append(COMMA));
        if (Objects.nonNull(bondExtRatingMapping)) {
            sb.append("外评").append(RatingUtils.getRating(bondExtRatingMapping)).append(COMMA);
        }
        if (Objects.nonNull(bondImpliedRatingMappingTag)) {
            sb.append("中债隐含").append(ITextValueEnum.getEnum(SpreadInduBondImpliedRatingMappingTagEnum.class, bondImpliedRatingMappingTag).getText()).append(COMMA);
        }
        if (Objects.nonNull(comYyRatingMappingTag)) {
            sb.append("YY").append(ITextValueEnum.getEnum(SpreadComYyRatingMappingTagEnum.class, comYyRatingMappingTag).getText()).append(COMMA);
        }
        if (Objects.nonNull(spreadRemainingTenorTag)) {
            sb.append(spreadRemainingTenorTag).append("Y").append(COMMA);
        }
        if (Objects.nonNull(spreadBondType)) {
            sb.append(ITextValueEnum.getEnum(SpreadBondTypeEnum.class, spreadBondType).getText()).append(COMMA);
        }
        if (Objects.nonNull(guaranteeStatus)) {
            sb.append(ITextValueEnum.getEnum(GuaranteedStatusEnum.class, guaranteeStatus).getDesc()).append(COMMA);
        }
        if (Objects.nonNull(administrativeDivision)) {
            sb.append(ADMINISTRATIVE_DIVISION_MAP.get(administrativeDivision)).append(COMMA);
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }
}
