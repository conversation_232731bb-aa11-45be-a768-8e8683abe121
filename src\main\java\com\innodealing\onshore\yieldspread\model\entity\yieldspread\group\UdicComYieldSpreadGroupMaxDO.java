package com.innodealing.onshore.yieldspread.model.entity.yieldspread.group;

import javax.persistence.Column;
import java.sql.Date;

/**
 * 按主体获得最新利差时间
 *
 * <AUTHOR>
 * @date 2024/4/3 11:24
 **/
public class UdicComYieldSpreadGroupMaxDO {

    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;


    @Column(name = "MAX(spread_date)")
    private Date maxSpreadDate;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }


    public Date getMaxSpreadDate() {
        return maxSpreadDate;
    }

    public void setMaxSpreadDate(Date maxSpreadDate) {
        this.maxSpreadDate = maxSpreadDate;
    }
}
