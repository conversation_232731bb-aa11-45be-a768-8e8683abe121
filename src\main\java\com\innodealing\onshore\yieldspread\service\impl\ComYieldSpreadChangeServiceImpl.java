package com.innodealing.onshore.yieldspread.service.impl;

import com.innodealing.onshore.yieldspread.dao.yieldspread.ComYieldSpreadChangeDAO;
import com.innodealing.onshore.yieldspread.service.ComYieldSpreadChangeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.Optional;

/**
 * 主体利差变动服务
 *
 * <AUTHOR>
 */
@Service
public class ComYieldSpreadChangeServiceImpl implements ComYieldSpreadChangeService {

    @Resource
    private ComYieldSpreadChangeDAO comYieldSpreadChangeDAO;

    @Override
    public int clearOldSpreadChanges() {
        // 只保留最新一天的数据
        Optional<Date> dateOpt = comYieldSpreadChangeDAO.getMaxSpreadDate();
        return dateOpt.map(date -> comYieldSpreadChangeDAO.clearOldSpreadChanges(date)).orElse(0);
    }
}
