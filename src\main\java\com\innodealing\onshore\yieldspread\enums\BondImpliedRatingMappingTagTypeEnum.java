package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 隐含评级标签标签
 *
 * <AUTHOR>
 */
public enum BondImpliedRatingMappingTagTypeEnum implements ITextValueEnum {
    /**
     * AAA级
     */
    AAA(1, "AAA"),
    /**
     * AA级
     */
    AA(2, "AA"),
    /**
     * A级
     */
    A(3, "A"),
    /**
     * 其他
     */
    OTHER(99, "其他");

    private Integer code;
    private String text;

    BondImpliedRatingMappingTagTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }
}
