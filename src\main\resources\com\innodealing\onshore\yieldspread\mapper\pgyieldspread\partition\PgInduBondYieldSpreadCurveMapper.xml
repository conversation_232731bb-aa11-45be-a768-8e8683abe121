<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgInduBondYieldSpreadCurveMapper">

    <update id="createTableRatingRouter">
        CREATE TABLE "yield_spread".${parameter.tableName}
        (
        "id" int8,
        "spread_date" date NOT NULL,
        "indu_level_code" int8,
        "bond_credit_spread" int8,
        "bond_excess_spread" int8,
        "cb_yield" int8,
        "avg_bond_credit_spread" int8,
        "avg_bond_excess_spread" int8,
        "avg_cb_yield" int8,
        "bond_credit_spread_count" int8,
        "bond_excess_spread_count" int8,
        "cb_yield_count" int8,
        "spread_bond_type" int2,
        "bond_ext_rating_mapping" int2,
        "spread_remaining_tenor_tag" int2,
        "guaranteed_status" int2,
        "using_spread_bond_type" int2,
        "using_bond_ext_rating_mapping" int2,
        "using_spread_remaining_tenor_tag" int2,
        "using_guaranteed_status" int2
        )
        DISTRIBUTED BY (spread_date)
        PARTITION BY RANGE (spread_date)
        ( START (date '2019-01-01') INCLUSIVE
        END (date '2030-01-01') EXCLUSIVE
        EVERY (INTERVAL '1 year') );
        <include refid="createIndex"/>
    </update>


    <sql id="createIndex">
        create index ${parameter.tableName}_idx_spread_date
        on yield_spread.${parameter.tableName} (spread_date);
        <if test="parameter.induLevel != null">
            create index ${parameter.tableName}_idx_indu_level_code
            on yield_spread.${parameter.tableName} (indu_level_code);
        </if>
        create index ${parameter.tableName}_idx_spread_bond_type
        on yield_spread.${parameter.tableName} (spread_bond_type);
        create index ${parameter.tableName}_idx_bond_ext_rating_mapping
        on yield_spread.${parameter.tableName} (bond_ext_rating_mapping);
        create index ${parameter.tableName}_idx_spread_remaining_tenor_tag
        on yield_spread.${parameter.tableName} (spread_remaining_tenor_tag);
        create index ${parameter.tableName}_idx_guaranteed_status
        on yield_spread.${parameter.tableName} (guaranteed_status);
    </sql>

    <update id="syncCurveIncrFromMV">
        INSERT INTO yield_spread.${tableName}
        SELECT *
        from yield_spread.${mvTableName};
    </update>
</mapper>