package com.innodealing.onshore.yieldspread.model.dto;

import java.sql.Date;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 自定义曲线表区数据查询参数
 *
 * <AUTHOR>
 */
public class CustomYieldSearchParam {

    /**
     * 分页查询开始索引
     */
    private Integer startIndex;

    /**
     * 每页数据量
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;

    private SortDTO sort;

    private Set<Long> bondUniCodes;

    private Set<Long> induBondUniCodes;

    private Set<Long> udicBondUniCodes;

    private Set<Long> bankBondUniCodes;

    private Set<Long> secuBondUniCodes;

    private Set<Long> insuBondUniCodes;

    private Long comUniCode;

    private Date spreadDate;

    private Integer year;

    public Set<Long> getInduBondUniCodes() {
        return Objects.isNull(induBondUniCodes) ? new HashSet<>() : new HashSet<>(induBondUniCodes);
    }

    public void setInduBondUniCodes(Set<Long> induBondUniCodes) {
        this.induBondUniCodes = Objects.isNull(induBondUniCodes) ? new HashSet<>() : new HashSet<>(induBondUniCodes);
    }

    public Set<Long> getUdicBondUniCodes() {
        return Objects.isNull(udicBondUniCodes) ? new HashSet<>() : new HashSet<>(udicBondUniCodes);
    }

    public void setUdicBondUniCodes(Set<Long> udicBondUniCodes) {
        this.udicBondUniCodes = Objects.isNull(udicBondUniCodes) ? new HashSet<>() : new HashSet<>(udicBondUniCodes);
    }

    public Set<Long> getBankBondUniCodes() {
        return Objects.isNull(bankBondUniCodes) ? new HashSet<>() : new HashSet<>(bankBondUniCodes);
    }

    public void setBankBondUniCodes(Set<Long> bankBondUniCodes) {
        this.bankBondUniCodes = Objects.isNull(bankBondUniCodes) ? new HashSet<>() : new HashSet<>(bankBondUniCodes);
    }

    public Set<Long> getSecuBondUniCodes() {
        return Objects.isNull(secuBondUniCodes) ? new HashSet<>() : new HashSet<>(secuBondUniCodes);
    }

    public void setSecuBondUniCodes(Set<Long> secuBondUniCodes) {
        this.secuBondUniCodes = Objects.isNull(secuBondUniCodes) ? new HashSet<>() : new HashSet<>(secuBondUniCodes);
    }

    public Set<Long> getInsuBondUniCodes() {
        return Objects.isNull(insuBondUniCodes) ? new HashSet<>() : new HashSet<>(insuBondUniCodes);
    }

    public void setInsuBondUniCodes(Set<Long> insuBondUniCodes) {
        this.insuBondUniCodes = Objects.isNull(insuBondUniCodes) ? new HashSet<>() : new HashSet<>(insuBondUniCodes);
    }

    public Integer getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(Integer startIndex) {
        this.startIndex = startIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public SortDTO getSort() {
        return sort;
    }

    public void setSort(SortDTO sort) {
        this.sort = sort;
    }

    public Set<Long> getBondUniCodes() {
        return Objects.isNull(bondUniCodes) ? new HashSet<>() : new HashSet<>(bondUniCodes);
    }

    public void setBondUniCodes(Set<Long> bondUniCodes) {
        this.bondUniCodes = Objects.isNull(bondUniCodes) ? new HashSet<>() : new HashSet<>(bondUniCodes);
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

}
