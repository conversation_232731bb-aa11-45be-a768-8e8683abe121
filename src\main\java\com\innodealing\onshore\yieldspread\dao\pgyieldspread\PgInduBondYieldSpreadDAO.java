package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgInduBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgInduBondYieldSpreadCbYieldGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgInduBondYieldSpreadCreditYieldGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgInduBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.InduYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgInduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadCbYieldGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadCreditYieldGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInduBondYieldSpreadGroupDO;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.*;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 产业债利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class PgInduBondYieldSpreadDAO {

    @Resource
    private PgInduBondYieldSpreadMapper pgInduBondYieldSpreadMapper;

    @Resource
    private PgInduBondYieldSpreadGroupMapper pgInduBondYieldSpreadGroupMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private PgInduBondYieldSpreadCbYieldGroupMapper pgInduBondYieldSpreadCbYieldGroupMapper;

    @Resource
    private PgInduBondYieldSpreadCreditYieldGroupMapper pgInduBondYieldSpreadCreditYieldGroupMapper;

    /**
     * 计算中债估值中位数
     *
     * @param comUniCodes    发行人代码
     * @param spreadBondType 利差债券类型
     * @param spreadDate     利差日期
     * @return key 发行人代码,行业债利差
     */
    public Map<Long, PgInduBondYieldSpreadGroupDO> getInduBondYieldSpreadMap(Set<Long> comUniCodes, Integer spreadBondType,
                                                                             Date spreadDate) {
        GroupedQuery<PgInduBondYieldSpreadDO, PgInduBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgInduBondYieldSpreadDO.class, PgInduBondYieldSpreadGroupDO.class)
                        .select(PgInduBondYieldSpreadGroupDO::getComUniCode, PgInduBondYieldSpreadGroupDO::getCbYield,
                                PgInduBondYieldSpreadGroupDO::getBondCreditSpread, PgInduBondYieldSpreadGroupDO::getBondExcessSpread)
                        .and(PgInduBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(PgInduBondYieldSpreadDO::getComUniCode, in(comUniCodes))
                        .and(Objects.nonNull(spreadBondType), PgInduBondYieldSpreadDO::getSpreadBondType, isEqual(spreadBondType))
                        .groupBy(PgInduBondYieldSpreadDO::getComUniCode);
        List<PgInduBondYieldSpreadGroupDO> pgInduBondYieldSpreadGroupDs = pgInduBondYieldSpreadGroupMapper.
                selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(pgInduBondYieldSpreadGroupDs)) {
            return Collections.emptyMap();
        }
        return pgInduBondYieldSpreadGroupDs.stream()
                .filter(Objects::nonNull)
                .map(x -> {
                    PgInduBondYieldSpreadGroupDO result = BeanCopyUtils.copyProperties(x, PgInduBondYieldSpreadGroupDO.class);
                    if (Objects.nonNull(x.getCbYield())) {
                        result.setCbYield(x.getCbYield().setScale(YIELD_SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondCreditSpread())) {
                        result.setBondCreditSpread(x.getBondCreditSpread()
                                .setScale(SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondExcessSpread())) {
                        result.setBondExcessSpread(x.getBondExcessSpread()
                                .setScale(SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                    }
                    return result;
                }).collect(Collectors.toMap(PgInduBondYieldSpreadGroupDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 批量更新
     *
     * @param pgInduBondYieldSpreadDOList 产业债利差列表
     * @param spreadDate                  利差日期
     * @return 受影响的行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int savePgInduBondYieldSpreadDOList(Date spreadDate, List<PgInduBondYieldSpreadDO> pgInduBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(pgInduBondYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> bondUniCodes = pgInduBondYieldSpreadDOList.stream().map(PgInduBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<PgInduBondYieldSpreadDO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadDO.class)
                .select(PgInduBondYieldSpreadDO::getId, PgInduBondYieldSpreadDO::getBondUniCode, PgInduBondYieldSpreadDO::getSpreadDate)
                .and(PgInduBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(PgInduBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<PgInduBondYieldSpreadDO> existDataList = pgInduBondYieldSpreadMapper.selectByDynamicQuery(query);
        Map<String, PgInduBondYieldSpreadDO> existPgInduBondYieldSpreadDO = existDataList.stream().collect(Collectors.toMap(x ->
                        String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                Function.identity(), (x1, x2) -> x2));
        MapperBatchAction<PgInduBondYieldSpreadMapper> batchAction = MapperBatchAction.
                create(PgInduBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgInduBondYieldSpreadDO pgInduBondYieldSpreadDO : pgInduBondYieldSpreadDOList) {
            PgInduBondYieldSpreadDO existData = existPgInduBondYieldSpreadDO.get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                    pgInduBondYieldSpreadDO.getBondUniCode(),
                    pgInduBondYieldSpreadDO.getSpreadDate().getTime()));
            batchAction.addAction(mapper -> {
                if (Objects.nonNull(existData)) {
                    pgInduBondYieldSpreadDO.setId(existData.getId());
                    mapper.updateByPrimaryKey(pgInduBondYieldSpreadDO);
                } else {
                    mapper.insert(pgInduBondYieldSpreadDO);
                }
            });
        }
        List<BatchResult> batchResults = batchAction.doBatchActionWithResults();
        return batchResults.stream().mapToInt(x -> {
            int effectRows = Arrays.stream(x.getUpdateCounts()).sum();
            if (effectRows > 0) {
                return effectRows;
            } else {
                return x.getParameterObjects().size();
            }
        }).sum();
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgInduBondYieldSpreadDO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt =
                pgInduBondYieldSpreadMapper.selectMaxByDynamicQuery(PgInduBondYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 获取所有利差日期
     *
     * @return {@link List}<{@link Date}> 利差日期集合
     */
    public List<Date> listAllSpreadDates() {
        DynamicQuery<PgInduBondYieldSpreadDO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadDO.class)
                .selectDistinct(PgInduBondYieldSpreadDO::getSpreadDate);
        List<PgInduBondYieldSpreadDO> spreadList = pgInduBondYieldSpreadMapper.selectByDynamicQuery(query);
        return spreadList.stream().map(PgInduBondYieldSpreadDO::getSpreadDate).collect(Collectors.toList());
    }

    /**
     * 查询并计算利差曲线数据-组合条件方式
     *
     * @param searchParameter 查询参数
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 查询利差曲线分组响应数据
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurvesByMultiCondition(InduBondYieldSpreadParamDTO searchParameter) {
        BaseFilterDescriptor<PgInduBondYieldSpreadDO>[] baseFilterDescriptors = this.listCommonFilters(searchParameter);
        GroupByQuery<PgInduBondYieldSpreadDO, PgInduBondYieldSpreadGroupDO> query = GroupByQuery.createQuery(PgInduBondYieldSpreadDO.class, PgInduBondYieldSpreadGroupDO.class)
                .select(PgInduBondYieldSpreadGroupDO::getSpreadDate, PgInduBondYieldSpreadGroupDO::getBondCreditSpread, PgInduBondYieldSpreadGroupDO::getBondCreditSpreadCount,
                        PgInduBondYieldSpreadGroupDO::getBondExcessSpread, PgInduBondYieldSpreadGroupDO::getBondExcessSpreadCount,
                        PgInduBondYieldSpreadGroupDO::getCbYield, PgInduBondYieldSpreadGroupDO::getCbYieldCount,
                        PgInduBondYieldSpreadGroupDO::getCdbLerpYield, PgInduBondYieldSpreadGroupDO::getCdbLerpYieldCount).and(baseFilterDescriptors);
        GroupedQuery<PgInduBondYieldSpreadDO, PgInduBondYieldSpreadGroupDO> groupQuery = query.groupBy(PgInduBondYieldSpreadDO::getSpreadDate);
        groupQuery.and(PgInduBondYieldSpreadGroupDO::getSpreadCount, greaterThanOrEqual(MIN_BOND_SIZE));
        List<PgInduBondYieldSpreadGroupDO> pgInduBondYieldSpreadGroupList = pgInduBondYieldSpreadGroupMapper.selectByGroupedQuery(groupQuery);
        if (CollectionUtils.isEmpty(pgInduBondYieldSpreadGroupList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> copyList = Lists.newArrayListWithExpectedSize(pgInduBondYieldSpreadGroupList.size());
        for (PgInduBondYieldSpreadGroupDO pgInduBondYieldSpreadGroupDO : pgInduBondYieldSpreadGroupList) {
            BondYieldSpreadCurveBO clone = BeanCopyUtils.copyProperties(pgInduBondYieldSpreadGroupDO, BondYieldSpreadCurveBO.class);
            BigDecimalUtils.handlerPrecision(clone.getBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondCreditSpread);
            BigDecimalUtils.handlerPrecision(clone.getBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondExcessSpread);
            BigDecimalUtils.handlerPrecision(clone.getCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCbYield);
            BigDecimalUtils.handlerPrecision(clone.getCdbLerpYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCdbLerpYield);
            copyList.add(clone);
        }
        return copyList;
    }

    /**
     * 查询并计算利差曲线数据-单券利差方式
     *
     * @param bondUniCode     债券唯一编码
     * @param startSpreadDate 利差开始日期
     * @param endSpreadDate   利差结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 债券每天中位数数据集合
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurvesByBond(Long bondUniCode, Date startSpreadDate, Date endSpreadDate) {
        if (Objects.isNull(bondUniCode)) {
            return Collections.emptyList();
        }
        DynamicQuery<PgInduBondYieldSpreadDO> query = DynamicQuery.createQuery(PgInduBondYieldSpreadDO.class)
                .select(PgInduBondYieldSpreadDO::getSpreadDate, PgInduBondYieldSpreadDO::getBondCreditSpread,
                        PgInduBondYieldSpreadDO::getBondExcessSpread, PgInduBondYieldSpreadDO::getCbYield)
                .and(PgInduBondYieldSpreadDO::getBondUniCode, isEqual(bondUniCode))
                .and(nonNull(startSpreadDate), PgInduBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startSpreadDate))
                .and(nonNull(endSpreadDate), PgInduBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(endSpreadDate))
                .orderBy(PgInduBondYieldSpreadDO::getSpreadDate, SortDirections::asc);
        List<PgInduBondYieldSpreadDO> pgInduBondYieldSpreadList = pgInduBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(pgInduBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> copyList = Lists.newArrayListWithExpectedSize(pgInduBondYieldSpreadList.size());
        for (PgInduBondYieldSpreadDO pgInduBondYieldSpreadDO : pgInduBondYieldSpreadList) {
            BondYieldSpreadCurveBO clone = BeanCopyUtils.copyProperties(pgInduBondYieldSpreadDO, BondYieldSpreadCurveBO.class);
            BigDecimalUtils.handlerPrecision(clone.getBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondCreditSpread);
            BigDecimalUtils.handlerPrecision(clone.getBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondExcessSpread);
            BigDecimalUtils.handlerPrecision(clone.getCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCbYield);
            BigDecimalUtils.handlerPrecision(clone.getCdbLerpYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCdbLerpYield);
            copyList.add(clone);
        }
        return copyList;
    }

    /**
     * 查询利差中债收益率中位数集合
     *
     * @param spreadDate         利差日期
     * @param bondImpBondRatings 债券隐含评级
     * @param notEqualIndu1      不包含的行业一code
     * @return {@link List}<{@link PgInduBondYieldSpreadCbYieldGroupDO}> 利差中债收益率中位数集合
     */
    public List<PgInduBondYieldSpreadCbYieldGroupDO> listYieldSpreadCbYieldMedians(@NonNull Date spreadDate, Collection<Integer> bondImpBondRatings, Long notEqualIndu1) {
        GroupedQuery<PgInduBondYieldSpreadDO, PgInduBondYieldSpreadCbYieldGroupDO> query =
                GroupByQuery.createQuery(PgInduBondYieldSpreadDO.class, PgInduBondYieldSpreadCbYieldGroupDO.class)
                        .and(PgInduBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(!CollectionUtils.isEmpty(bondImpBondRatings), PgInduBondYieldSpreadDO::getBondImpliedRatingMapping, in(bondImpBondRatings))
                        .and(Objects.nonNull(notEqualIndu1), PgInduBondYieldSpreadDO::getInduLevel1Code, notEqual(notEqualIndu1))
                        .groupBy(PgInduBondYieldSpreadDO::getSpreadDate, PgInduBondYieldSpreadDO::getBondImpliedRatingMapping);
        return pgInduBondYieldSpreadCbYieldGroupMapper.selectByGroupedQuery(query);
    }

    /**
     * 查询利差信用利差中位数集合
     *
     * @param spreadDate         利差日期
     * @param bondImpBondRatings 债券隐含评级
     * @param notEqualIndu1      不包含的行业一code
     * @return {@link List}<{@link PgInduBondYieldSpreadCreditYieldGroupDO}> 利差信用利差中位数集合
     */
    public List<PgInduBondYieldSpreadCreditYieldGroupDO> listYieldSpreadCreditYieldMedians(@NonNull Date spreadDate, Collection<Integer> bondImpBondRatings, Long notEqualIndu1) {
        if (CollectionUtils.isEmpty(bondImpBondRatings)) {
            return Collections.emptyList();
        }
        GroupedQuery<PgInduBondYieldSpreadDO, PgInduBondYieldSpreadCreditYieldGroupDO> query =
                GroupByQuery.createQuery(PgInduBondYieldSpreadDO.class, PgInduBondYieldSpreadCreditYieldGroupDO.class)
                        .and(PgInduBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(PgInduBondYieldSpreadDO::getBondImpliedRatingMapping, in(bondImpBondRatings))
                        .and(Objects.nonNull(notEqualIndu1), PgInduBondYieldSpreadDO::getInduLevel1Code, notEqual(notEqualIndu1))
                        .groupBy(PgInduBondYieldSpreadDO::getSpreadDate, PgInduBondYieldSpreadDO::getBondImpliedRatingMapping);
        return pgInduBondYieldSpreadCreditYieldGroupMapper.selectByGroupedQuery(query);
    }

    private BaseFilterDescriptor<PgInduBondYieldSpreadDO>[] listCommonFilters(InduBondYieldSpreadParamDTO request) {
        FilterGroupDescriptor<PgInduBondYieldSpreadDO> filterGroup = FilterGroupDescriptor.create(PgInduBondYieldSpreadDO.class)
                .and(nonNull(request.getIndustryCode1()), PgInduBondYieldSpreadDO::getInduLevel1Code, isEqual(request.getIndustryCode1()))
                .and(nonNull(request.getIndustryCode2()), PgInduBondYieldSpreadDO::getInduLevel2Code, isEqual(request.getIndustryCode2()))
                .and(nonNull(request.getSpreadBondType()), PgInduBondYieldSpreadDO::getSpreadBondType, isEqual(request.getSpreadBondType()))
                .and(nonNull(request.getBondExtRatingMapping()), PgInduBondYieldSpreadDO::getBondExtRatingMapping, isEqual(request.getBondExtRatingMapping()))
                .and(nonNull(request.getBondImpliedRatingMappingTag()), PgInduBondYieldSpreadDO::getBondImpliedRatingMappingTag, isEqual(request.getBondImpliedRatingMappingTag()))
                .and(nonNull(request.getComYyRatingMappingTag()), PgInduBondYieldSpreadDO::getComYyRatingMappingTag, in(request.getComYyRatingMappingTag()))
                .and(isNotEmpty(request.getBusinessFilterNatures()), PgInduBondYieldSpreadDO::getBusinessFilterNature, in(request.getBusinessFilterNatures()))
                .and(nonNull(request.getSpreadRemainingTenorTag()), PgInduBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(request.getSpreadRemainingTenorTag()))
                .and(nonNull(request.getGuaranteeStatus()), PgInduBondYieldSpreadDO::getGuaranteedStatus, isEqual(request.getGuaranteeStatus()))
                .and(nonNull(request.getComUniCode()), PgInduBondYieldSpreadDO::getComUniCode, isEqual(request.getComUniCode()))
                .and(nonNull(request.getBondUniCode()), PgInduBondYieldSpreadDO::getBondUniCode, isEqual(request.getBondUniCode()))
                .and(isNotEmpty(request.getComUniCodes()), PgInduBondYieldSpreadDO::getComUniCode, in(request.getComUniCodes()))
                .and(isNotEmpty(request.getBondUniCodes()), PgInduBondYieldSpreadDO::getBondUniCode, in(request.getBondUniCodes()))
                .and(nonNull(request.getStartSpreadDate()), PgInduBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(request.getStartSpreadDate()))
                .and(nonNull(request.getEndSpreadDate()), PgInduBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(request.getEndSpreadDate()));
        return filterGroup.getFilters();
    }

    /**
     * 查询行业利差
     *
     * @param params 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listBondYieldSpreads(InduYieldSearchParam params) {
        GroupedQuery<PgInduBondYieldSpreadDO, PgInduBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgInduBondYieldSpreadDO.class, PgInduBondYieldSpreadGroupDO.class)
                        .select(PgInduBondYieldSpreadGroupDO::getSpreadDate,
                                PgInduBondYieldSpreadGroupDO::getBondCreditSpread,
                                PgInduBondYieldSpreadGroupDO::getBondExcessSpread,
                                PgInduBondYieldSpreadGroupDO::getCbYield,
                                PgInduBondYieldSpreadGroupDO::getAvgBondCreditSpread,
                                PgInduBondYieldSpreadGroupDO::getAvgBondExcessSpread,
                                PgInduBondYieldSpreadGroupDO::getAvgCbYield,
                                PgInduBondYieldSpreadGroupDO::getBondCreditSpreadCount,
                                PgInduBondYieldSpreadGroupDO::getBondExcessSpreadCount,
                                PgInduBondYieldSpreadGroupDO::getCbYieldCount)
                        .and(Objects.nonNull(params.getSpreadBondType()), PgInduBondYieldSpreadDO::getSpreadBondType, isEqual(params.getSpreadBondType()))
                        .and(Objects.nonNull(params.getIndustryCode1()), PgInduBondYieldSpreadDO::getInduLevel1Code, isEqual(params.getIndustryCode1()))
                        .and(Objects.nonNull(params.getIndustryCode2()), PgInduBondYieldSpreadDO::getInduLevel2Code, isEqual(params.getIndustryCode2()))
                        .and(Objects.nonNull(params.getGuaranteedStatus()), PgInduBondYieldSpreadDO::getGuaranteedStatus, isEqual(params.getGuaranteedStatus()))
                        .and(Objects.nonNull(params.getBondExtRatingMapping()), PgInduBondYieldSpreadDO::getBondExtRatingMapping, isEqual(params.getBondExtRatingMapping()))
                        .and(!CollectionUtils.isEmpty(params.getBusinessFilterNatures()), PgInduBondYieldSpreadDO::getBusinessFilterNature, in(params.getBusinessFilterNatures()))
                        .and(Objects.nonNull(params.getRemainingTenor()), PgInduBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(params.getRemainingTenor()))
                        .and(ArrayUtils.isNotEmpty(params.getBondImpliedRatingMappings()),
                                PgInduBondYieldSpreadDO::getBondImpliedRatingMapping, in(params.getBondImpliedRatingMappings()))
                        .and(ArrayUtils.isNotEmpty(params.getComYyRatingMappings()), PgInduBondYieldSpreadDO::getComYyRatingMapping, in(params.getComYyRatingMappings()))
                        .and(Objects.nonNull(params.getComUniCode()), PgInduBondYieldSpreadDO::getComUniCode, isEqual(params.getComUniCode()))
                        .and(Objects.nonNull(params.getBondUniCode()), PgInduBondYieldSpreadDO::getBondUniCode, isEqual(params.getBondUniCode()))
                        .groupBy(PgInduBondYieldSpreadDO::getSpreadDate)
                        .orderBy(PgInduBondYieldSpreadGroupDO::getSpreadDate, asc());
        List<PgInduBondYieldSpreadGroupDO> bondYieldSpreads = pgInduBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(bondYieldSpreads)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(bondYieldSpreads, BondYieldSpreadBO.class);
    }

}

