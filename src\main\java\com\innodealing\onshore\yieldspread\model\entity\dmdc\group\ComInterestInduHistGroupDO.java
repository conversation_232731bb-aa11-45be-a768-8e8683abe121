package com.innodealing.onshore.yieldspread.model.entity.dmdc.group;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 行业主体利差(老表)
 *
 * <AUTHOR>
 * @date 2022/08/17
 **/
@Table(name = "com_interest_indu_hist")
public class ComInterestInduHistGroupDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 公司统一编码
     */
    @Column
    private Long comUniCode;
    /**
     * 公司名称
     */
    @Column
    private String comUniName;
    /**
     * 企业类型(性质)  1央企,2国企,6民企 t_pub_par.par_sys_code=1062
     */
    @Column
    private Integer enterpriseType;
    /**
     * 一级行业编码
     */
    @Column
    private Long industryCode1;
    /**
     * 一级行业名称
     */
    @Column
    private String industryName1;
    /**
     * 二级行业编码
     */
    @Column
    private Long industryCode2;
    /**
     * 二级行业名称
     */
    @Column
    private String industryName2;
    /**
     * 利差日期
     */
    @Column
    private Date interestDate;
    /**
     * 主体评级
     */
    @Column
    private String comRating;
    /**
     * 信用利差(公募)
     */
    @Column
    private BigDecimal creditInterest;
    /**
     * 超额利差(公募)
     */
    @Column
    private BigDecimal excessInterest;
    /**
     * 信用利差(私募)
     */
    @Column
    private BigDecimal privateCreditInterest;
    /**
     * 超额利差(私募)
     */
    @Column
    private BigDecimal privateExcessInterest;
    /**
     * 信用利差(永续)
     */
    @Column
    private BigDecimal foreverCreditInterest;
    /**
     * 超额利差(永续)
     */
    @Column
    private BigDecimal foreverExcessInterest;
    /**
     * 删除标记 1删除 0正常
     */
    @Column
    private Integer isDeleted;
    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public Integer getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(Integer enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public Long getIndustryCode1() {
        return industryCode1;
    }

    public void setIndustryCode1(Long industryCode1) {
        this.industryCode1 = industryCode1;
    }

    public String getIndustryName1() {
        return industryName1;
    }

    public void setIndustryName1(String industryName1) {
        this.industryName1 = industryName1;
    }

    public Long getIndustryCode2() {
        return industryCode2;
    }

    public void setIndustryCode2(Long industryCode2) {
        this.industryCode2 = industryCode2;
    }

    public String getIndustryName2() {
        return industryName2;
    }

    public void setIndustryName2(String industryName2) {
        this.industryName2 = industryName2;
    }

    public Date getInterestDate() {
        return interestDate == null ? null : new Date(interestDate.getTime());
    }

    public void setInterestDate(Date interestDate) {
        this.interestDate = interestDate == null ? null : new Date(interestDate.getTime());
    }

    public String getComRating() {
        return comRating;
    }

    public void setComRating(String comRating) {
        this.comRating = comRating;
    }

    public BigDecimal getCreditInterest() {
        return creditInterest;
    }

    public void setCreditInterest(BigDecimal creditInterest) {
        this.creditInterest = creditInterest;
    }

    public BigDecimal getExcessInterest() {
        return excessInterest;
    }

    public void setExcessInterest(BigDecimal excessInterest) {
        this.excessInterest = excessInterest;
    }

    public BigDecimal getPrivateCreditInterest() {
        return privateCreditInterest;
    }

    public void setPrivateCreditInterest(BigDecimal privateCreditInterest) {
        this.privateCreditInterest = privateCreditInterest;
    }

    public BigDecimal getPrivateExcessInterest() {
        return privateExcessInterest;
    }

    public void setPrivateExcessInterest(BigDecimal privateExcessInterest) {
        this.privateExcessInterest = privateExcessInterest;
    }

    public BigDecimal getForeverCreditInterest() {
        return foreverCreditInterest;
    }

    public void setForeverCreditInterest(BigDecimal foreverCreditInterest) {
        this.foreverCreditInterest = foreverCreditInterest;
    }

    public BigDecimal getForeverExcessInterest() {
        return foreverExcessInterest;
    }

    public void setForeverExcessInterest(BigDecimal foreverExcessInterest) {
        this.foreverExcessInterest = foreverExcessInterest;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCreateTime() {
        return createTime == null ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime == null ? null : new Timestamp(createTime.getTime());
    }

    public Timestamp getUpdateTime() {
        return updateTime == null ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime == null ? null : new Timestamp(updateTime.getTime());
    }

}