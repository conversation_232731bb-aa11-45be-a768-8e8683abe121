# syncHistBondYieldSpreadTrace 方法详细文档

## 1. 方法概述

### 1.1 方法签名
```java
@Override
public int syncHistBondYieldSpreadTrace(Date startDate)
```

### 1.2 功能描述
`syncHistBondYieldSpreadTrace` 是债券收益率利差追踪数据的历史同步方法，负责从指定开始日期到当前日期的所有利差追踪数据的同步计算和存储。该方法是利差追踪系统的核心数据同步功能。

### 1.3 所属类
- **类名**: `BondYieldSpreadTraceServiceImpl`
- **包路径**: `com.innodealing.onshore.yieldspread.service.impl`
- **接口**: `BondYieldSpreadTraceService`

## 2. 方法实现逻辑

### 2.1 核心实现
```java
@Override
public int syncHistBondYieldSpreadTrace(Date startDate) {
    Date endDate = Date.valueOf(LocalDate.now());
    return this.syncBondYieldSpreadTrace(startDate, endDate);
}
```

### 2.2 执行流程
1. **设置结束日期**: 将结束日期设置为当前日期
2. **委托执行**: 调用 `syncBondYieldSpreadTrace(startDate, endDate)` 方法执行具体的同步逻辑

## 3. 核心委托方法 - syncBondYieldSpreadTrace

### 3.1 方法签名
```java
public int syncBondYieldSpreadTrace(Date startDate, Date endDate)
```

### 3.2 详细实现逻辑

#### 3.2.1 参数处理
```java
AtomicInteger effectRows = new AtomicInteger();
startDate = Objects.isNull(startDate) ? Date.valueOf(LocalDate.now().minusDays(1)) : startDate;
endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
```

#### 3.2.2 日期循环处理
对从开始日期到结束日期的每一天执行以下操作：

```java
for (LocalDate localStartDate = startDate.toLocalDate(); !localStartDate.isAfter(endDate.toLocalDate()); localStartDate = localStartDate.plusDays(1)) {
    Date issueDate = Date.valueOf(localStartDate);
    effectRows.addAndGet(this.calBondYieldSpreadTraceAbs(issueDate));
    effectRows.addAndGet(this.calBondYieldSpreadTraceQuantile(issueDate));
    effectRows.addAndGet(this.calBondYieldSpreadTraceChange(issueDate));
    logger.info("syncBondYieldSpreadTrace同步利差追踪利差日期:{}", issueDate);
}
```

#### 3.2.3 缓存更新
```java
String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
String redisKey = String.format(TRACE_SPREAD_DATE_KEY, now);
BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO = new BondYieldPanoramaTraceSpreadDateDTO();
pgBondYieldSpreadTraceAbsDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
pgBondYieldSpreadTraceQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
pgBondYieldSpreadTraceChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(spreadDateDTO), YieldSpreadConst.TWO_DAYS, TimeUnit.DAYS);
this.cacheLineChart();
```

## 4. 子方法详细说明

### 4.1 calBondYieldSpreadTraceAbs(Date issueDate)
**功能**: 计算债券收益率利差追踪绝对值数据

**执行步骤**:
1. **准备基础数据**: 调用 `prepareParameter(issueDate)` 获取基础数据上下文
2. **遍历债券类型**: 对每种追踪债券类型执行处理
3. **构建上下文**: 为每种债券类型构建 `YieldSpreadTraceContext`
4. **筛选匹配数据**: 根据债券类型的曲线代码筛选对应的基础数据
5. **处理器执行**: 使用对应的处理器处理数据
6. **保存数据**: 调用 `pgBondYieldSpreadTraceAbsDAO.saveBondYieldSpreadTraceAbsList()` 保存

**关键代码**:
```java
for (YieldPanoramaBondTypeEnum bondType : YieldPanoramaBondTypeEnum.getTraceBondTypeEnums()) {
    YieldSpreadTraceContext context = BeanCopyUtils.copyProperties(parameter.get(), YieldSpreadTraceContext.class);
    context.setBondTypeEnum(bondType);
    List<PgBondYieldPanoramaAbsDO> matchAbs = context.getAbsBondYieldPanoramas().stream()
            .filter(abs -> bondType.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
    context.setAbsBondYieldPanoramas(matchAbs);
    List<YieldSpreadTraceProcessor> processors = processorMap.getOrDefault(bondType, Collections.emptyList());
    processors.forEach(processor -> absBondYieldSpreadTraceList.addAll(processor.processYieldSpreadTraceYtm(context)));
}
```

### 4.2 calBondYieldSpreadTraceQuantile(Date issueDate)
**功能**: 计算债券收益率利差追踪历史分位数据

**执行步骤**:
1. **获取基础分位数据**: 从全景分位表获取数据
2. **按曲线代码分组**: 将数据按曲线代码进行分组
3. **遍历债券类型**: 对每种追踪债券类型处理
4. **计算分位数据**: 为每个曲线代码和分位类型计算分位值
5. **保存数据**: 保存到利差追踪分位表

**关键逻辑**:
- 支持3年和5年历史分位计算
- 使用统计视图计算分位数排名
- 按债券类型和曲线代码组织数据

### 4.3 calBondYieldSpreadTraceChange(Date issueDate)
**功能**: 计算债券收益率利差追踪区间变动数据

**执行步骤**:
1. **获取全景变动数据**: 从全景变动表获取基础数据
2. **获取当前追踪数据**: 获取当前日期的追踪绝对值数据
3. **遍历变动类型**: 对每种区间变动类型处理
4. **计算变动值**: 计算当前值与历史值的差异
5. **保存变动数据**: 保存到利差追踪变动表

**支持的变动类型**:
- 一日变动、一周变动、一月变动
- 三月变动、六月变动、一年变动

## 5. 数据处理器架构

### 5.1 YieldSpreadTraceProcessor 接口
**核心方法**:
- `supportBondType()`: 判断是否支持特定债券类型
- `processYieldSpreadTraceYtm()`: 处理利差追踪到期收益率数据

### 5.2 处理器实现类

#### 5.2.1 DefaultYieldSpreadTraceProcessor
- **功能**: 默认处理器，处理期限利差
- **图表类型**: `MATU_SPREAD` (期限利差)
- **处理逻辑**: 直接复制全景数据到追踪数据

#### 5.2.2 YieldSpreadTraceCreditProcessor
- **功能**: 信用利差处理器
- **图表类型**: `CREDIT_SPREAD` (信用利差)
- **处理逻辑**: 减去同期限国开债收益率
- **支持债券**: 产业债、证券永续债、证券次级债等

#### 5.2.3 YieldSpreadTraceCreditSubChinaProcessor
- **功能**: 信用利差(减国债)处理器
- **图表类型**: `CREDIT_SPREAD_TB` (信用利差减国债)
- **处理逻辑**: 减去同期限国债收益率

#### 5.2.4 YieldSpreadTraceLevelProcessor
- **功能**: 等级利差处理器
- **图表类型**: `GRADE_SPREAD` (等级利差)
- **处理逻辑**: 减去同债券类型最高评级收益率

#### 5.2.5 YieldSpreadTraceTermsProcessor
- **功能**: 期限利差处理器
- **图表类型**: `TERMS_SPREAD` (期限利差)
- **处理逻辑**: 计算不同期限间的利差

#### 5.2.6 YieldSpreadTraceVarietyProcessor
- **功能**: 品种利差处理器
- **图表类型**: `VARIETY_SPREAD` (品种利差)
- **处理逻辑**: 计算不同品种间的利差

## 6. YieldSpreadTraceContext 上下文

### 6.1 核心属性
```java
private Date issueDate;                                          // 发行日期
private YieldPanoramaBondTypeEnum bondTypeEnum;                  // 债券类型枚举
private PgBondYieldPanoramaAbsDO chinaBondYieldPanorama;         // 国债数据
private PgBondYieldPanoramaAbsDO chinaBondKaiYieldPanorama;      // 国开债数据
private List<PgBondYieldPanoramaAbsDO> chinaBondMidYieldPanoramas;     // 中短期票据数据
private List<PgBondYieldPanoramaAbsDO> generalBankBondYieldPanoramas;  // 银行普通债数据
private List<PgBondYieldPanoramaAbsDO> securitiesBondYieldPanoramas;   // 证券公司债数据
private List<PgBondYieldPanoramaAbsDO> absBondYieldPanoramas;          // 全景绝对值数据
```

### 6.2 核心方法
- `getReferentBondYieldPanoramas()`: 获取所有参考利差收益率数据
- 各种 getter/setter 方法，确保数据安全性

## 7. 数据存储

### 7.1 主要数据表
- **pg_bond_yield_spread_trace_abs**: 利差追踪绝对值数据
- **pg_bond_yield_spread_trace_quantile**: 利差追踪历史分位数据
- **pg_bond_yield_spread_trace_change**: 利差追踪区间变动数据

### 7.2 数据分片策略
使用基于日期的分片策略，通过 `ShardingUtils` 工具类计算分片键。

## 8. 缓存机制

### 8.1 Redis缓存
- **缓存键**: `yield-spread:bond_yield_spread_trace:{date}`
- **缓存内容**: `BondYieldPanoramaTraceSpreadDateDTO`
- **过期时间**: 2天

### 8.2 折线图缓存
- **方法**: `cacheLineChart()`
- **功能**: 缓存5年历史的折线图数据
- **缓存类型**: 
  - 评级维度缓存
  - 期限维度缓存
- **数据压缩**: 使用 `ZipUtils.zipBase64()` 压缩存储

## 9. 处理器映射初始化

### 9.1 processorMap 初始化
```java
@Override
public void afterPropertiesSet() {
    curveCodes.addAll(Arrays.stream(YieldSpreadCurveCodeEnum.values()).map(YieldSpreadCurveCodeEnum::getValue).collect(Collectors.toList()));
    Collection<YieldSpreadTraceProcessor> processors = applicationContext.getBeansOfType(YieldSpreadTraceProcessor.class).values();
    for (YieldPanoramaBondTypeEnum bondTypeEnum : values()) {
        processorMap.put(bondTypeEnum, processors.stream().filter(processor -> processor.supportBondType(bondTypeEnum)).collect(Collectors.toList()));
    }
}
```

### 9.2 处理器支持的债券类型
- **DefaultYieldSpreadTraceProcessor**: 支持所有债券类型
- **YieldSpreadTraceCreditProcessor**: 产业债、证券永续债、证券次级债
- **YieldSpreadTraceLevelProcessor**: 有评级的债券类型
- **YieldSpreadTraceTermsProcessor**: 银行债、证券债等
- **YieldSpreadTraceVarietyProcessor**: 城投债、中短期票据等

## 10. prepareParameter 方法详解

### 10.1 方法功能
`prepareParameter` 方法负责准备利差追踪计算所需的基础数据上下文。

### 10.2 执行步骤
```java
private Optional<YieldSpreadTraceContext> prepareParameter(Date issueDate) {
    // 1. 查询基础数据,包含国债
    List<PgBondYieldPanoramaAbsDO> bondYieldPanoramaAbsList = pgBondYieldPanoramaAbsDAO.listBondYieldPanoramaQuantiles(null, issueDate);
    if (CollectionUtils.isEmpty(bondYieldPanoramaAbsList)) {
        return Optional.empty();
    }

    // 2. 筛选各类债券数据
    PgBondYieldPanoramaAbsDO chinaBond = bondYieldPanoramaAbsList.stream()
            .filter(abs -> CHINA_BOND.getCurveCodes().contains(abs.getCurveCode())).findFirst().orElse(null);
    PgBondYieldPanoramaAbsDO chinaBondKai = bondYieldPanoramaAbsList.stream()
            .filter(abs -> CHINA_DEVELOPMENT_BOND.getCurveCodes().contains(abs.getCurveCode())).findFirst().orElse(null);

    // 3. 构建上下文对象
    YieldSpreadTraceContext context = new YieldSpreadTraceContext();
    context.setIssueDate(issueDate);
    context.setChinaBondYieldPanorama(chinaBond);
    context.setChinaBondKaiYieldPanorama(chinaBondKai);
    // ... 设置其他数据

    return Optional.of(context);
}
```

### 10.3 数据分类
- **国债数据**: 作为信用利差(减国债)的基准
- **国开债数据**: 作为信用利差的基准
- **中短期票据数据**: 用于品种利差计算
- **银行普通债数据**: 用于期限利差计算
- **证券公司债数据**: 用于特殊处理逻辑

## 11. 图表类型详解

### 11.1 YieldSpreadChartTypeEnum 枚举
```java
MATU_SPREAD(1, "期限利差")           // 到期收益率
CREDIT_SPREAD(2, "信用利差")         // 信用利差(减国开)
GRADE_SPREAD(3, "等级利差")          // 等级利差
TERMS_SPREAD(4, "期限利差")          // 期限利差
VARIETY_SPREAD(5, "品种利差")        // 品种利差
CREDIT_SPREAD_TB(6, "信用利差(减国债)") // 信用利差(减国债)
```

### 11.2 图表类型与处理器映射
- **期限利差**: DefaultYieldSpreadTraceProcessor
- **信用利差**: YieldSpreadTraceCreditProcessor
- **等级利差**: YieldSpreadTraceLevelProcessor
- **期限利差**: YieldSpreadTraceTermsProcessor
- **品种利差**: YieldSpreadTraceVarietyProcessor
- **信用利差(减国债)**: YieldSpreadTraceCreditSubChinaProcessor

## 12. 债券类型支持矩阵

### 12.1 追踪债券类型
```java
// 支持利差追踪的债券类型
MEDIUM_AND_SHORT_TERMS_NOTE(4, "中短期票据")
INDUSTRIAL_BOND(5, "产业债")
URBAN_BOND(6, "城投")
BANK_ORDINARY_BOND(7, "银行普通债")
BANK_TIER2_BOND(8, "银行二级资本债")
BANK_PERPETUAL_BOND(9, "银行永续债")
SECURITIES_BOND(10, "证券公司债")
SECURITIES_SUB_BOND(11, "证券次级债")
SECURITIES_PERPETUAL_BOND(12, "证券永续债")
NCD(13, "同业存单")
INSURANCE_BOND(14, "保险")
INTEREST_RATE_BOND(17, "利率债")
```

### 12.2 特殊债券类型处理
- **无评级债券**: 证券次级债、证券永续债、国债、同业存单
- **品种组债券**: 利率债
- **需要特殊处理**: 产业债、证券债等

## 13. 数据流转图

```
syncHistBondYieldSpreadTrace(startDate)
    ↓
syncBondYieldSpreadTrace(startDate, endDate)
    ↓
日期循环 (startDate → endDate)
    ↓
并行执行三个计算任务:
    ├── calBondYieldSpreadTraceAbs(issueDate)
    │   ├── prepareParameter() → YieldSpreadTraceContext
    │   ├── 遍历债券类型 → YieldPanoramaBondTypeEnum.getTraceBondTypeEnums()
    │   ├── 筛选匹配数据 → bondType.getCurveCodes()
    │   ├── 处理器执行 → YieldSpreadTraceProcessor.processYieldSpreadTraceYtm()
    │   └── 保存数据 → pg_bond_yield_spread_trace_abs
    │
    ├── calBondYieldSpreadTraceQuantile(issueDate)
    │   ├── 获取全景分位数据 → pgBondYieldPanoramaQuantileDAO
    │   ├── 按曲线代码分组 → Map<Integer, List<PgBondYieldPanoramaQuantileDO>>
    │   ├── 计算分位排名 → CalculationHelper.safeCalPercentRankIgnore()
    │   └── 保存数据 → pg_bond_yield_spread_trace_quantile
    │
    └── calBondYieldSpreadTraceChange(issueDate)
        ├── 获取全景变动数据 → pgBondYieldPanoramaChangeDAO
        ├── 获取当前追踪数据 → pgBondYieldSpreadTraceAbsDAO
        ├── 遍历变动类型 → BondYieldIntervalChangeTypeEnum
        ├── 计算变动值 → safeSubtraceFunction.apply()
        └── 保存数据 → pg_bond_yield_spread_trace_change
    ↓
更新Redis缓存
    ├── 最大日期缓存 → BondYieldPanoramaTraceSpreadDateDTO
    └── 折线图缓存 → cacheLineChart()
    ↓
返回总影响行数
```

## 14. 错误处理和监控

### 14.1 日志记录
```java
logger.info("syncBondYieldSpreadTrace同步利差追踪利差日期:{}", issueDate);
logger.info("【BondYieldSpreadTraceService#cacheLineChart】 ===> start....");
logger.info("【BondYieldSpreadTraceService#cacheLineChart】 ===> end....spend time: {} ms", stopWatch.getTotalTimeMillis());
```

### 14.2 数据完整性检查
- 空集合检查: `CollectionUtils.isEmpty()`
- 空对象检查: `Objects.isNull()`, `ObjectUtils.allNotNull()`
- Optional 模式: `Optional.empty()`, `Optional.of()`

### 14.3 异常处理
- 事务回滚: `@Transactional(rollbackFor = Exception.class)`
- 安全计算: `safeSubtract()`, `safeCalPercentRankIgnore()`
- 数据验证: 分片键计算、日期范围检查

## 15. 性能优化

### 15.1 批量处理
- 批量插入减少数据库交互
- 分片存储提高查询性能
- 流式处理大数据集

### 15.2 缓存策略
- Redis 缓存热点数据
- 压缩存储节省内存
- 定期更新缓存数据

### 15.3 并发控制
- `AtomicInteger` 统计影响行数
- `ConcurrentHashMap` 线程安全映射
- 处理器并行执行

## 16. 相关方法对比

### 16.1 syncBondYieldSpreadTraceChange(Date startDate)
- **功能**: 仅同步利差追踪区间变动数据
- **实现**: 日期循环调用 `calBondYieldSpreadTraceChange()`

### 16.2 syncBondYieldSpreadTraceQuantile(Date startDate)
- **功能**: 仅同步利差追踪历史分位数据
- **实现**: 日期循环调用 `calBondYieldSpreadTraceQuantile()`

### 16.3 syncHistBondYieldSpreadTrace(Date startDate)
- **功能**: 全量同步所有类型的利差追踪数据
- **实现**: 调用 `syncBondYieldSpreadTrace()` 执行完整同步流程

## 17. 使用建议

### 17.1 调用时机
- **首次部署**: 使用较早的开始日期进行历史数据同步
- **日常维护**: 使用昨日日期进行增量同步
- **数据修复**: 指定特定日期范围进行重新同步

### 17.2 性能考虑
- 大批量历史数据同步建议分批执行
- 监控处理器执行时间和内存使用
- 考虑在业务低峰期执行同步任务

### 17.3 数据验证
- 同步完成后检查各表的数据完整性
- 验证缓存更新是否正确
- 确认折线图缓存生成成功

## 18. 返回值
返回同步影响的总行数 (`int`)，包括所有子操作的累计影响行数。
