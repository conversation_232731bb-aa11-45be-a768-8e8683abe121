package com.innodealing.onshore.yieldspread.controller;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.yieldspread.config.SyncConfiguration;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveCodeEnum;
import com.innodealing.onshore.yieldspread.service.MaterializedViewRefreshService;
import com.innodealing.onshore.yieldspread.service.OptimizedBondYieldSpreadTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 优化的债券收益率利差追踪控制器
 * 提供更灵活的同步参数和并行处理能力
 * 
 * <AUTHOR>
 */
@Api(tags = "优化的利差追踪同步")
@RestController
@RequestMapping("optimized/bond/yield/spread/trace")
public class OptimizedBondYieldSpreadTraceController {

    @Resource
    private OptimizedBondYieldSpreadTraceService optimizedBondYieldSpreadTraceService;
    
    @Resource
    private MaterializedViewRefreshService materializedViewRefreshService;
    
    @ApiOperation(value = "按债券类型同步利差追踪数据")
    @PostMapping("/sync/by-bond-types")
    public int syncByBondTypes(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "bondTypes", value = "债券类型列表，为空表示所有类型") @RequestBody(required = false) List<Integer> bondTypes) {
        List<YieldPanoramaBondTypeEnum> bondTypeList = null;
        if (CollectionUtils.isNotEmpty(bondTypes)) {
            bondTypeList = bondTypes.stream().map(value -> ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, value)).collect(Collectors.toList());
        }
        return optimizedBondYieldSpreadTraceService.syncBondYieldSpreadTraceByBondTypes(startDate, endDate, bondTypeList);
    }
    
    @ApiOperation(value = "按曲线代码同步利差追踪数据")
    @PostMapping("/sync/by-curve-codes")
    public int syncByCurveCodes(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "curveCodes", value = "曲线代码列表，为空表示所有曲线") @RequestParam(required = false) List<Integer> curveCodes) {
        
        List<Integer> curveCodeList = null;
        if (CollectionUtils.isNotEmpty(curveCodes)) {
            curveCodeList = YieldSpreadCurveCodeEnum.toEnums(curveCodes).stream().map(YieldSpreadCurveCodeEnum::getValue)
                    .collect(Collectors.toList());
        }
        return optimizedBondYieldSpreadTraceService.syncBondYieldSpreadTraceByCurveCodes(startDate, endDate, curveCodeList);
    }
    

    @ApiOperation(value = "同步单个债券类型")
    @PostMapping("/sync/single-bond-type")
    public int syncSingleBondType(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "bondType", value = "债券类型", required = true) @RequestParam Integer bondType) {
        
        YieldPanoramaBondTypeEnum bondTypeEnum = Arrays.stream(YieldPanoramaBondTypeEnum.values())
                .filter(type -> type.getValue() == bondType)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("无效的债券类型: " + bondType));
        
        return optimizedBondYieldSpreadTraceService.syncBondYieldSpreadTraceForSingleBondType(startDate, endDate, bondTypeEnum);
    }
    
    @ApiOperation(value = "同步单个曲线代码")
    @PostMapping("/sync/single-curve-code")
    public int syncSingleCurveCode(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "curveCode", value = "曲线代码", required = true) @RequestParam Integer curveCode) {
        
        return optimizedBondYieldSpreadTraceService.syncBondYieldSpreadTraceForSingleCurveCode(startDate, endDate, curveCode);
    }
    
    @ApiOperation(value = "仅同步绝对值数据")
    @PostMapping("/sync/abs-only")
    public int syncAbsOnly(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "parallel", value = "是否并行", required = false, defaultValue = "true") @RequestParam boolean parallel) {
        
        SyncConfiguration config = SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .parallel(parallel)
                .syncAbs(true)
                .syncQuantile(false)
                .syncChange(false)
                .build();
        
        return optimizedBondYieldSpreadTraceService.syncBondYieldSpreadTraceAbsOnly(config);
    }
    
    @ApiOperation(value = "仅同步分位数据")
    @PostMapping("/sync/quantile-only")
    public int syncQuantileOnly(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "parallel", value = "是否并行", required = false, defaultValue = "true") @RequestParam boolean parallel) {
        
        SyncConfiguration config = SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .parallel(parallel)
                .syncAbs(false)
                .syncQuantile(true)
                .syncChange(false)
                .build();
        
        return optimizedBondYieldSpreadTraceService.syncBondYieldSpreadTraceQuantileOnly(config);
    }
    
    @ApiOperation(value = "仅同步变动数据")
    @PostMapping("/sync/change-only")
    public int syncChangeOnly(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期", required = true) @RequestParam Date endDate,
            @ApiParam(name = "parallel", value = "是否并行", required = false, defaultValue = "true") @RequestParam boolean parallel) {
        
        SyncConfiguration config = SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .parallel(parallel)
                .syncAbs(false)
                .syncQuantile(false)
                .syncChange(true)
                .build();
        
        return optimizedBondYieldSpreadTraceService.syncBondYieldSpreadTraceChangeOnly(config);
    }
    



    // 物化视图刷新控制相关接口
    
    @ApiOperation(value = "禁用物化视图刷新")
    @PostMapping("/mv/disable-refresh")
    public String disableMvRefresh(
            @ApiParam(name = "viewName", value = "视图名称", required = true) @RequestParam String viewName,
            @ApiParam(name = "ttlSeconds", value = "生效时间（秒）", required = false, defaultValue = "3600") @RequestParam long ttlSeconds) {
        
        materializedViewRefreshService.disableMaterializedViewRefresh(viewName, ttlSeconds);
        return "已禁用物化视图刷新: " + viewName + ", 生效时间: " + ttlSeconds + "秒";
    }
    
    @ApiOperation(value = "启用物化视图刷新")
    @PostMapping("/mv/enable-refresh")
    public String enableMvRefresh(
            @ApiParam(name = "viewName", value = "视图名称", required = true) @RequestParam String viewName,
            @ApiParam(name = "ttlSeconds", value = "生效时间（秒）", required = false, defaultValue = "3600") @RequestParam long ttlSeconds) {
        
        materializedViewRefreshService.enableMaterializedViewRefresh(viewName, ttlSeconds);
        return "已启用物化视图刷新: " + viewName + ", 生效时间: " + ttlSeconds + "秒";
    }
    
    @ApiOperation(value = "获取物化视图刷新状态")
    @GetMapping("/mv/refresh-status")
    public String getMvRefreshStatus(
            @ApiParam(name = "viewName", value = "视图名称", required = true) @RequestParam String viewName) {
        
        return materializedViewRefreshService.getMaterializedViewRefreshStatus(viewName);
    }
    
    @ApiOperation(value = "清除物化视图刷新控制")
    @DeleteMapping("/mv/clear-refresh-control")
    public String clearMvRefreshControl(
            @ApiParam(name = "viewName", value = "视图名称", required = true) @RequestParam String viewName) {
        
        materializedViewRefreshService.clearMaterializedViewRefreshControl(viewName);
        return "已清除物化视图刷新控制: " + viewName;
    }
}
