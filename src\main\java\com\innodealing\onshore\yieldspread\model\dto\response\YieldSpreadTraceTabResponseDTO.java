package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 利差追踪
 *
 * <AUTHOR>
 */
public class YieldSpreadTraceTabResponseDTO {

    @ApiModelProperty("图类型，1:到期收益率 2:信用利差(债券类型-国开) 3:等级利差 4:期限利差 5:品种利差(债券类型-中短期票据) 6:条款利差(债券类型-普通债)")
    private Integer chartType;
    @ApiModelProperty("行名")
    private String typeName;
    @ApiModelProperty("行排行")
    private Integer typeSort;
    @ApiModelProperty("统计时间区间")
    private String statisticalDateRange;

    @ApiModelProperty("统计时间区间开始时间")
    private String statisticalDateStart;
    @ApiModelProperty("统计时间区间结束时间")
    private String statisticalDateEnd;
    @ApiModelProperty("最大收益率")
    private BigDecimal maxYield;
    @ApiModelProperty("最小收益率")
    private BigDecimal minYield;
    @ApiModelProperty("中位数收益率")
    private BigDecimal medianYield;
    @ApiModelProperty("利差追踪数据")
    private List<YieldSpreadTraceDataResponseDTO> yieldSpreadTraceDataList;

    public Integer getChartType() {
        return chartType;
    }

    public void setChartType(Integer chartType) {
        this.chartType = chartType;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getTypeSort() {
        return typeSort;
    }

    public void setTypeSort(Integer typeSort) {
        this.typeSort = typeSort;
    }

    public String getStatisticalDateRange() {
        return statisticalDateRange;
    }

    public void setStatisticalDateRange(String statisticalDateRange) {
        this.statisticalDateRange = statisticalDateRange;
    }

    public String getStatisticalDateStart() {
        return statisticalDateStart;
    }

    public void setStatisticalDateStart(String statisticalDateStart) {
        this.statisticalDateStart = statisticalDateStart;
    }

    public String getStatisticalDateEnd() {
        return statisticalDateEnd;
    }

    public void setStatisticalDateEnd(String statisticalDateEnd) {
        this.statisticalDateEnd = statisticalDateEnd;
    }

    public BigDecimal getMaxYield() {
        return maxYield;
    }

    public void setMaxYield(BigDecimal maxYield) {
        this.maxYield = maxYield;
    }

    public BigDecimal getMinYield() {
        return minYield;
    }

    public void setMinYield(BigDecimal minYield) {
        this.minYield = minYield;
    }

    public BigDecimal getMedianYield() {
        return medianYield;
    }

    public void setMedianYield(BigDecimal medianYield) {
        this.medianYield = medianYield;
    }

    public List<YieldSpreadTraceDataResponseDTO> getYieldSpreadTraceDataList() {
        return Objects.isNull(yieldSpreadTraceDataList) ? new ArrayList<>() : new ArrayList<>(yieldSpreadTraceDataList);
    }

    public void setYieldSpreadTraceDataList(List<YieldSpreadTraceDataResponseDTO> yieldSpreadTraceDataList) {
        this.yieldSpreadTraceDataList = Objects.isNull(yieldSpreadTraceDataList) ? new ArrayList<>() : new ArrayList<>(yieldSpreadTraceDataList);
    }
}
