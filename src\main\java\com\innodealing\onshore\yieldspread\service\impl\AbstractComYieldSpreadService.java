package com.innodealing.onshore.yieldspread.service.impl;

import com.github.wz2cool.dynamic.SortDirection;
import com.innodealing.commons.encrypt.MD5Utils;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.consts.TipsConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserCurveDAO;
import com.innodealing.onshore.yieldspread.enums.SpreadCodeTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadCurveType;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionBO;
import com.innodealing.onshore.yieldspread.model.bo.MixYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.dto.UniversalYieldSpreadSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.AbstractCurveGenerateConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.ComOrBondConditionReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.TrendReplayComYieldSpreadRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.YieldSpreadSearchReqDTO;
import com.innodealing.onshore.yieldspread.service.internal.ComService;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.lang.NonNull;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.EXPIRATION_HOUR;

/**
 * 主体抽象类
 *
 * <AUTHOR>
 */
public abstract class AbstractComYieldSpreadService extends AbstractBaseYieldSpreadService {

    protected static final int FOUR_HOURS = 4;
    protected static final int ZERO_MINUTE = 0;
    protected static final int ZERO_SECOND = 0;
    protected static final int ZERO_NANO = 0;
    protected static final long EARLY_EXPIRED_TIME = 60 * 1000 * 10L;
    protected static final int EARLY_EXPIRED_TIME_END_HOUR = 8;
    protected static final int EARLY_EXPIRED_TIME_START_HOUR = 4;
    private static final int INT_ONE = 1;
    private static final int INT_TWO = 2;
    private static final int INT_THREE =3;
    @Resource
    protected StringRedisTemplate stringRedisTemplate;
    @Resource
    protected ComService comService;
    @Resource
    protected UserCurveDAO userCurveDAO;
    @Resource
    protected HolidayService holidayService;


    protected <R extends AbstractCurveGenerateConditionReqDTO> R getCurveGenerateCondition(Long userid, Long curveId, Class<R> clazz) {
        Optional<CurveDefinitionBO> curveOptional = userCurveDAO.getCurveDefinitionBO(curveId);
        CurveDefinitionBO curveBO = curveOptional.orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        super.checkBelongAndCurveType(userid, curveBO.getUserId(), curveBO.getSpreadCurveType());
        String filterCondition = curveBO.getFilterCondition();
        try {
            return StringUtils.isBlank(filterCondition) ? clazz.newInstance() : JSON.parseObject(filterCondition, clazz);
        } catch (Exception e) {
            logger.error("newInstance CurveGenerateConditionReqDTO error,filterCondition is :{}", filterCondition, e);
            throw new BusinessException("实例化曲线生成条件对象失败");
        }
    }

    /**
     * 构建主体利差查询参数
     *
     * @param request           请求参数
     * @param generateRequest   曲线生成条件参数
     * @param clazz             具体查询参数
     * @param <R>               查询实体类型
     * @param sortPropertyClass 排序字段所属DO
     * @return UniversalYieldSpreadSearchParam
     */
    protected <R extends UniversalYieldSpreadSearchParam> R buildComYieldSearchParam(
            YieldSpreadSearchReqDTO request,
            AbstractCurveGenerateConditionReqDTO generateRequest,
            Class<R> clazz,
            Class<?> sortPropertyClass) {
        R searchParam = BeanCopyUtils.copyProperties(generateRequest, clazz);
        if (isToday(request.getSpreadDate())) {
            searchParam.setSpreadDate(this.getMaxSpreadDate());
        } else {
            searchParam.setSpreadDate(request.getSpreadDate());
        }
        ComOrBondConditionReqDTO comOrBondCondition = generateRequest.getComOrBondCondition();
        if (Objects.nonNull(comOrBondCondition)) {
            searchParam.setComOrBondUniCode(comOrBondCondition.getConditionType(), comOrBondCondition.getUniCode());
        }
        if (Objects.nonNull(request.getUniCodeType()) && Objects.nonNull(request.getUniCode())) {
            if (request.getUniCodeType().equals(SpreadCodeTypeEnum.COM_CODE.getValue())) {
                searchParam.setComUniCode(request.getUniCode());
            } else {
                searchParam.setBondUniCode(request.getUniCode());
            }
        }
        searchParam.setPageNum(request.getPageNum());
        searchParam.setPageSize(request.getPageSize());
        searchParam.setYear(searchParam.getSpreadDate().toLocalDate().getYear());
        searchParam.setStartIndex((request.getPageNum() - 1) * request.getPageSize());
        if (sortPropertyClass != null) {
            SortDTO sort = request.getSort();
            if (Objects.isNull(sort) || StringUtils.isBlank(sort.getPropertyName())) {
                searchParam.setSort(new SortDTO("com_credit_spread", SortDirection.DESC));
            } else {
                searchParam.setSort(YieldSpreadHelper.convertSortPropertyToTableColumName(sort, sortPropertyClass));
            }
        }
        return searchParam;
    }

    /**
     * 判断是否超出搜索范围，如果曲线筛选条件选了单券，表区筛选条件不是这条单券，就会返回true
     *
     * @param filterUniCode      表区筛选条件债券code
     * @param filterUniCodeType  筛选的code类型
     * @param comOrBondCondition 曲线筛选条件
     * @return 是否超出搜索范围
     */
    protected boolean isOutOfSearchRange(Long filterUniCode, Integer filterUniCodeType, ComOrBondConditionReqDTO comOrBondCondition) {
        SpreadCodeTypeEnum codeType = EnumUtils.getEnumNullable(SpreadCodeTypeEnum.class, filterUniCodeType);
        if (Objects.isNull(filterUniCode) || Objects.isNull(codeType) || Objects.isNull(comOrBondCondition)) {
            return false;
        }
        Integer storeConditionType = comOrBondCondition.getConditionType();
        Long storeUniCode = comOrBondCondition.getUniCode();
        if (Objects.isNull(storeConditionType) || Objects.isNull(storeUniCode)) {
            return false;
        }
        return filterUniCodeType.equals(comOrBondCondition.getConditionType()) && !filterUniCode.equals(comOrBondCondition.getUniCode());
    }

    /**
     * 获取Com count
     *
     * @param request             请求参数
     * @param keyPrefix           redis key  前缀
     * @param selectCountFunction 如果redis没找到，从这个function获取
     * @return
     */
    protected Long getComCountFromRedis(YieldSpreadSearchReqDTO request, String keyPrefix, Supplier<Long> selectCountFunction) {
        String redisKey = getComCountKey(request, keyPrefix);
        String count = stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(count)) {
            return Long.parseLong(count);
        }
        Long comCount = selectCountFunction.get();
        cacheComCount(redisKey, comCount);
        return comCount;
    }

    protected void cacheComCount(String key, Long count) {
        stringRedisTemplate.opsForValue().set(key, String.valueOf(count), getExpirationTimeMillis(), TimeUnit.MILLISECONDS);
    }

    private String getComCountKey(YieldSpreadSearchReqDTO request, String keyPrefix) {
        request.setSort(null);
        request.setPageNum(0);
        request.setPageSize(0);
        return String.format(keyPrefix, MD5Utils.getMD5String(JSON.toJSONString(request)));
    }

    private long getExpirationTimeMillis() {
        LocalDateTime midnight = LocalDateTime.now().plusDays(1).withHour(EXPIRATION_HOUR).withMinute(0).withSecond(0).withNano(0);
        return ChronoUnit.MILLIS.between(LocalDateTime.now(), midnight);
    }

    protected boolean isToday(Date spreadDate) {
        return Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()));
    }

    /**
     * 主体利差走势复盘 列表
     *
     * @param spreadStartDate                 开始时间
     * @param spreadEndDate                   结束时间
     * @param replayComYieldSpreadRequestDTOs 主体列表请求体
     * @return 利差曲线列表
     */
    @SuppressWarnings("squid:S3776")
    public List<ComCreditSpreadDTO> listTrendReplayComYieldSpreads(Date spreadStartDate, Date spreadEndDate,
                                                                   List<TrendReplayComYieldSpreadRequestDTO> replayComYieldSpreadRequestDTOs) {
        // 第一次查询时，默认查询最大的日期。确保数据的完整性
        final double minScore = this.getScore(spreadStartDate);
        final double maxScore = this.getScore(spreadEndDate);
        List<ComCreditSpreadDTO> rst = Lists.newArrayList();
        // 按照主体进行分组
        Map<Long, List<TrendReplayComYieldSpreadRequestDTO>> comMap = replayComYieldSpreadRequestDTOs.stream()
                .collect(Collectors.groupingBy(TrendReplayComYieldSpreadRequestDTO::getComUniCode));
        for (Entry<Long, List<TrendReplayComYieldSpreadRequestDTO>> entry : comMap.entrySet()) {
            Integer industry = entry.getValue().get(0).getComSpreadSector();
            String zSetKey = String.format(YieldSpreadCacheConst.RELAY_COM_YIELD_ZSET_KEY, industry, entry.getKey());
            if (Objects.equals(Boolean.FALSE, stringRedisTemplate.hasKey(zSetKey))) {
                // 第一次查询，应该都数据库
                // 查询数据库
                List<MixYieldSpreadShortBO> list = listAllYieldSpreads(Collections.singletonList(entry.getKey()));
                if (!CollectionUtils.isEmpty(list)) {
                    Set<ZSetOperations.TypedTuple<String>> typedTuples = list.stream()
                            .map(spreadYield -> new DefaultTypedTuple<>(JSON.toJSONString(spreadYield), this.getScore(spreadYield.getSpreadDate())))
                            .collect(Collectors.toSet());
                    stringRedisTemplate.opsForZSet().add(zSetKey, typedTuples);
                    stringRedisTemplate.expire(zSetKey, expiredTime(), TimeUnit.MILLISECONDS);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    List<MixYieldSpreadShortBO> collect = list.stream()
                            .filter(comYield -> comYield.getSpreadDate().compareTo(spreadStartDate) >= 0
                                    && comYield.getSpreadDate().compareTo(spreadEndDate) <= 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(collect)) {
                        return Collections.emptyList();
                    }
                    List<ComCreditSpreadDTO> targetList = convertColumnValue(collect, entry.getValue().get(0));
                    rst.addAll(targetList.stream().sorted(Comparator.comparing(ComCreditSpreadDTO::getSpreadDate)).collect(Collectors.toList()));
                }
            } else {
                // 不是第一次查询，直接从缓存中获取
                Set<String> cacheValues = stringRedisTemplate.opsForZSet().rangeByScore(zSetKey, minScore, maxScore);
                if (CollectionUtils.isNotEmpty(cacheValues)) {
                    List<MixYieldSpreadShortBO> collect = cacheValues.stream().map(cache -> JSON.parseObject(cache, MixYieldSpreadShortBO.class))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(collect)) {
                        return Collections.emptyList();
                    }
                    List<ComCreditSpreadDTO> targetList = convertColumnValue(collect, entry.getValue().get(0));
                    rst.addAll(targetList.stream().sorted(Comparator.comparing(ComCreditSpreadDTO::getSpreadDate)).collect(Collectors.toList()));
                }
            }
        }
        return rst;
    }

    /**
     * 根据日期获取分数
     *
     * @param date 日期
     * @return {@link Double}
     */
    protected double getScore(@NonNull Date date) {
        return Double.parseDouble(date.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE));
    }

    private List<ComCreditSpreadDTO> convertColumnValue(List<MixYieldSpreadShortBO> allColumnList, TrendReplayComYieldSpreadRequestDTO requestDTO) {
        if (CollectionUtils.isEmpty(allColumnList)){
            return Lists.newArrayList();
        }
        SpreadCurveType spreadCurveType = ITextValueEnum.getEnum(SpreadCurveType.class, requestDTO.getSpreadCurveType());
        Integer comYieldSpreadType = requestDTO.getComYieldSpreadType();
        return allColumnList.stream().map(e -> {
            ComCreditSpreadDTO dto = new ComCreditSpreadDTO();
            dto.setComUniCode(e.getComUniCode());
            dto.setSpreadDate(e.getSpreadDate());
            dto.setComCreditSpread(chooseColumnValue(e, spreadCurveType, comYieldSpreadType));
            return dto;
        }).collect(Collectors.toList());
    }

    @SuppressWarnings("squid:S3776")
    private BigDecimal chooseColumnValue(MixYieldSpreadShortBO mixYieldSpreadShortBO, SpreadCurveType spreadCurveType, Integer comYieldSpreadType) {
        // 默认值，超额利差or信用利差字段
        if (Objects.isNull(comYieldSpreadType) || Objects.equals(comYieldSpreadType, 0)) {
            // 超额利差
            if (Objects.equals(SpreadCurveType.FULL_PRICE, spreadCurveType)) {
                return mixYieldSpreadShortBO.getComExcessSpread();
            } else {
                return mixYieldSpreadShortBO.getComCreditSpread();
            }
        }
        // 二级分类 - 1
        if (Objects.equals(comYieldSpreadType, INT_ONE)) {
            // 超额利差
            if (Objects.equals(SpreadCurveType.FULL_PRICE, spreadCurveType)) {
                return mixYieldSpreadShortBO.getSecondExcessYieldOne();
            } else {
                return mixYieldSpreadShortBO.getSecondCreditYieldOne();
            }
        }
        // 二级分类 - 2
        if (Objects.equals(comYieldSpreadType, INT_TWO)) {
            // 超额利差
            if (Objects.equals(SpreadCurveType.FULL_PRICE, spreadCurveType)) {
                return mixYieldSpreadShortBO.getSecondExcessYieldTwo();
            } else {
                return mixYieldSpreadShortBO.getSecondCreditYieldTwo();
            }
        }
        // 二级分类 - 3
        if (Objects.equals(comYieldSpreadType, INT_THREE)) {
            // 超额利差
            if (Objects.equals(SpreadCurveType.FULL_PRICE, spreadCurveType)) {
                return mixYieldSpreadShortBO.getSecondExcessYieldTree();
            } else {
                return mixYieldSpreadShortBO.getSecondCreditYieldTree();
            }
        }
        return null;
    }

    /**
     * 查询根据主体列表，查询 利差一级分类(信用利差、超额利差) 和 二级分类(
     * 银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；
     * 证券主体：0 全部，1 普通，2：次级 ，3 永续 ;
     * 城投主体利差：0：全部，1 公募，2 私募，3 永续；
     * 行业：0：全部，1 公募，2 私募，3 永续
     * ) 的利差数据
     *
     * @param comUniCodes 主体列表
     * @return list
     */
    protected List<MixYieldSpreadShortBO> listAllYieldSpreads(@NonNull List<Long> comUniCodes) {
        return Collections.emptyList();
    }


    protected long expiredTime() {
        // 4-8点的时候缓存时间为10分钟一次，防止这个时间点增量数据进来以后，没有缓存进去
        int hour = LocalDateTime.now().getHour();
        if (hour >= EARLY_EXPIRED_TIME_START_HOUR && hour <= EARLY_EXPIRED_TIME_END_HOUR) {
            return EARLY_EXPIRED_TIME;
        }
        LocalDateTime midnight = LocalDateTime.of(holidayService.getNextWorkDay(Date.valueOf(LocalDate.now())).toLocalDate(),
                LocalTime.now().withHour(FOUR_HOURS).withMinute(ZERO_MINUTE).withSecond(ZERO_SECOND).withNano(ZERO_NANO));
        return ChronoUnit.MILLIS.between(LocalDateTime.now(), midnight);
    }
}
