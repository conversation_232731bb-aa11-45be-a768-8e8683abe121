package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition;

import javax.persistence.Column;
import javax.persistence.Table;


/**
 * 城投债券利差曲线分区表(包含市)
 *
 * <AUTHOR>
 */
@Table(name = "udic_bond_yield_spread_curve_city")
public class PgUdicBondYieldSpreadCurveCityDO extends BasePgUdicBondYieldSpreadCurveDO {
    /**
     * 市级编码
     */
    @Column
    private Long cityUniCode;

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }
}