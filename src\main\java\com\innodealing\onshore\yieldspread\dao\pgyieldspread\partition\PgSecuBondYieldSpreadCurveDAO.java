package com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.yieldspread.dao.pgshard.partition.SecuBondShardYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvSecuBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.SecuShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgSecuBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.SecuBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.SecuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.SecuShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.EmptyRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.between;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.divideHundredThousand;

/**
 * 证券利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgSecuBondYieldSpreadCurveDAO {

    @Resource
    private PgSecuBondYieldSpreadCurveMapper pgSecuBondYieldSpreadCurveMapper;

    @Resource
    private SecuShardBondYieldSpreadCurveMapper secuShardBondYieldSpreadCurveMapper;

    @Resource
    private MvSecuBondYieldSpreadCurveDAO mvSecuBondYieldSpreadCurveDAO;

    @Resource
    private SecuBondShardYieldSpreadCurveRepository secuYieldSpreadCurveRepository;

    @Resource
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;

    /**
     * 同步证券利差视图
     *
     * @param router 路由
     * @param param  创建条件
     */
    public void syncCurveShardSecuForMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        SecuBondYieldSpreadCurveParameter parameter = builderParameter(router);
        if (param.isTableRefresh()) {
            pgSecuBondYieldSpreadCurveMapper.refreshTable(parameter.getTableName(), parameter);
        }
        DynamicQuery<SecuShardBondYieldSpreadCurveDO> query = builderDynamicQuery(router);
        int count = secuShardBondYieldSpreadCurveMapper.selectCountByDynamicQuery(query, router);
        if (count > 0) {
            secuShardBondYieldSpreadCurveMapper.deleteByDynamicQueryRouter(query, router);
        }
        //同步最新数据
        pgSecuBondYieldSpreadCurveMapper.syncCurveIncrFromMV(parameter.getTableName(), mvSecuBondYieldSpreadCurveDAO.getMvName(router));
    }

    private SecuBondYieldSpreadCurveParameter builderParameter(AbstractRatingRouter router) {
        SecuBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, SecuBondYieldSpreadCurveParameter.class);
        parameter.setTableName(getTableName(router));
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (spreadDateRange != null) {
            parameter.setStartDate(spreadDateRange.getStartDate());
            parameter.setEndDate(spreadDateRange.getEndDate());
        }
        return parameter;
    }

    private DynamicQuery<SecuShardBondYieldSpreadCurveDO> builderDynamicQuery(AbstractRatingRouter router) {
        DynamicQuery<SecuShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(SecuShardBondYieldSpreadCurveDO.class);
        if (Objects.nonNull(router.getSpreadDateRange()) &&
                Objects.nonNull(router.getSpreadDateRange().getStartDate()) &&
                Objects.nonNull(router.getSpreadDateRange().getEndDate())) {
            query.and(Objects.nonNull(router.getSpreadDateRange()), SecuShardBondYieldSpreadCurveDO::getSpreadDate,
                    between(router.getSpreadDateRange().getStartDate(), router.getSpreadDateRange().getEndDate()));
        }
        return query;

    }

    private String getTableName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()), secuShardBondYieldSpreadCurveMapper.getLogicTable(),
                RatingCombinationHelper.getTableNameSuffix(router));
    }

    /**
     * 查询证券利差
     *
     * @param params 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listSecuYieldSpreads(SecuYieldSearchParam params) {
        List<SecuShardBondYieldSpreadCurveDO> yieldSpreadCurveList = secuYieldSpreadCurveRepository
                .listSecuYieldSpreads(params, this.getRatingRouter(params.getBondImpliedRatingMappings()));
        List<BondYieldSpreadBO> bondYieldSpreadBOList = BeanCopyUtils.copyList(yieldSpreadCurveList, BondYieldSpreadBO.class);
        if (CollectionUtils.isNotEmpty(bondYieldSpreadBOList)) {
            bondYieldSpreadBOList.forEach(ys -> {
                ObjectExtensionUtils.ifNonNull(ys.getBondCreditSpread(), v -> divideHundredThousand(v).ifPresent(ys::setBondCreditSpread));
                ObjectExtensionUtils.ifNonNull(ys.getBondExcessSpread(), v -> divideHundredThousand(v).ifPresent(ys::setBondExcessSpread));
                ObjectExtensionUtils.ifNonNull(ys.getCbYield(), v -> divideHundredThousand(v, YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE).ifPresent(ys::setCbYield));
                ObjectExtensionUtils.ifNonNull(ys.getAvgBondCreditSpread(), v -> divideHundredThousand(v).ifPresent(ys::setAvgBondCreditSpread));
                ObjectExtensionUtils.ifNonNull(ys.getAvgBondExcessSpread(), v -> divideHundredThousand(v).ifPresent(ys::setAvgBondExcessSpread));
                ObjectExtensionUtils.ifNonNull(ys.getAvgCbYield(), v -> divideHundredThousand(v, YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE).ifPresent(ys::setAvgCbYield));
            });
        }
        return bondYieldSpreadBOList;
    }

    private AbstractRatingRouter getRatingRouter(Integer[] bondImpliedRatingMappings) {
        return ArrayUtils.isEmpty(bondImpliedRatingMappings) ? new EmptyRouter() : implicitRatingRouterFactory.newRatingRouter(bondImpliedRatingMappings);
    }

}
