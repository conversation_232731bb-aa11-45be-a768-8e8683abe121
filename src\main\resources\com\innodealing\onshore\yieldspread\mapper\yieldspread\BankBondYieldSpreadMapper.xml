<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.yieldspread.BankBondYieldSpreadMapper">
    <update id="createShardingTable" parameterType="String">
        CREATE TABLE IF NOT EXISTS ${tableName} (
            `id` bigint(20) unsigned NOT NULL  COMMENT '主键id',
            `com_uni_code` bigint(20) DEFAULT NULL COMMENT '发行人代码',
            `bond_uni_code` bigint(20) NOT NULL COMMENT '债券统一编码',
            `bond_code` varchar(64) DEFAULT NULL COMMENT '债券编码',
            `indu_level1_code` bigint(20) DEFAULT NULL COMMENT '一级行业编码',
            `indu_level1_name` varchar(50) DEFAULT NULL COMMENT '一级行业名称',
            `indu_level2_code` bigint(20) DEFAULT NULL COMMENT '二级行业编码',
            `indu_level2_name` varchar(50) DEFAULT NULL COMMENT '二级行业名称',
            `bank_type` smallint(6) unsigned DEFAULT NULL COMMENT '银行类型1: 政策性银行;2: 国有商业银行;3: 股份制商业银行; 4:城市商业银行; 5: 农村商业银行; 6: 农村信用合作社;7:村镇银行',
            `bank_seniority_ranking` tinyint(4) unsigned DEFAULT NULL COMMENT '银行求偿顺序 普通; 二级资本债; 永续 ',
            `spread_date` date NOT NULL COMMENT '利差日期',
            `bond_credit_spread` decimal(12,4) DEFAULT NULL COMMENT '债券信用利差;单位(BP)',
            `bond_excess_spread` decimal(12,4) DEFAULT NULL COMMENT '债券超额利差;单位(BP)',
            `cb_yield` decimal(12,4) DEFAULT NULL COMMENT '中债收益率;单位(%)',
            `cdb_lerp_yield` decimal(12,4) DEFAULT NULL COMMENT '国开插值收益率;单位(%)',
            `implied_rating_lerp_yield` decimal(12,4) DEFAULT NULL COMMENT '隐含评级对应曲线插值收益率;单位(%)',
            `excess_spread_status` tinyint(3) unsigned DEFAULT '0' COMMENT '超额利差数据状态 0有效 1没有评级曲线',
            `bond_ext_rating_mapping` smallint(5) unsigned DEFAULT NULL COMMENT '债券外部评级映射',
            `bond_implied_rating_mapping` smallint(5) unsigned DEFAULT NULL COMMENT '债项隐含评级映射',
            `com_ext_rating_mapping` smallint(5) unsigned DEFAULT NULL COMMENT '主体外部评级映射',
            `spread_bond_type` smallint(5) unsigned DEFAULT NULL COMMENT '利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)',
            `spread_remaining_tenor_tag` smallint(5) unsigned DEFAULT NULL COMMENT '利差剩余期限标签',
            `remaining_tenor` varchar(20) DEFAULT NULL COMMENT '剩余期限',
            `remaining_tenor_day` int(11) DEFAULT NULL COMMENT '剩余期限天数',
            `latest_coupon_rate` decimal(8,4) DEFAULT NULL COMMENT '最新票面利率;单位(%)',
            `bond_balance` decimal(16,2) DEFAULT NULL COMMENT '债券余额(万)',
            `deleted` tinyint(3) unsigned DEFAULT '0' COMMENT '是否删除：0： 未删除;1：已删除',
            `create_time` datetime (3) DEFAULT CURRENT_TIMESTAMP(3)  COMMENT '创建时间',
            `update_time` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
            PRIMARY KEY (`id`),
            INDEX `idx_com_uni_code_spread_date` (`com_uni_code`, `spread_date`),
            INDEX `idx_spread_date_com_uni_code` (`spread_date`, `com_uni_code`),
            INDEX `idx_bond_uni_code_spread_date_credit` (`bond_uni_code`, `spread_date`, `bond_credit_spread`)
            )  COMMENT='银行债利差';
    </update>
</mapper>