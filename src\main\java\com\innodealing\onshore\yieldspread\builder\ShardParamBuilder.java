package com.innodealing.onshore.yieldspread.builder;

import com.alibaba.fastjson.JSONObject;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveShardEnum;
import com.innodealing.onshore.yieldspread.model.dto.AbstractShardParamDTO;


/**
 * 父类利差参数构造器
 *
 * <AUTHOR>
 */
public interface ShardParamBuilder {

    /**
     * 条件请求参数
     *
     * @param paramDTO 请求参数
     * @return {@link ShardParamBuilder} 债券利差参数构造器
     */
    ShardParamBuilder searchQueryParam(Object paramDTO);

    /**
     * 设置一级分片
     *
     * @param shardEnum 分片枚举
     * @return this 构造器
     */
    ShardParamBuilder level(YieldSpreadCurveShardEnum shardEnum);

    /**
     * 设置扩展属性 子类选择实现
     *
     * @param jsonObject json
     * @return this 构造器
     */
    default ShardParamBuilder extra(JSONObject jsonObject) {
        if (jsonObject == null) {
            return this;
        }
        return this;
    }

    /**
     * 构造器
     *
     * @return DTO实体
     */
    AbstractShardParamDTO build();
}