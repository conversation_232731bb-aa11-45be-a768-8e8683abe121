package com.innodealing.onshore.yieldspread.dao.pgyieldspread.view;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view.PgBondYieldPanoramaQuantileViewMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgBondYieldPanoramaQuantileViewDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 债券收益率全景分位数视图DAO
 *
 * <AUTHOR>
 */
@Component
public class PgBondYieldPanoramaQuantileViewDAO {

    @Resource
    private PgBondYieldPanoramaQuantileViewMapper pgBondYieldPanoramaQuantileViewMapper;

    /**
     * 创建或替换视图
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    public void createPanoramaQuantileView(Date startDate, Date endDate) {
        pgBondYieldPanoramaQuantileViewMapper.createPanoramaQuantileView(startDate.toString(), endDate.toString());
    }

    /**
     * 查询收益率全景分位数据
     *
     * @param issueDate 发行日期
     * @return {@link List}<{@link PgBondYieldPanoramaQuantileViewDO}> 收益率全景分位数据
     */
    public List<PgBondYieldPanoramaQuantileViewDO> listBondYieldPanoramaQuantiles(Date issueDate) {
        DynamicQuery<PgBondYieldPanoramaQuantileViewDO> query = DynamicQuery.createQuery(PgBondYieldPanoramaQuantileViewDO.class)
                .and(PgBondYieldPanoramaQuantileViewDO::getIssueDate, isEqual(issueDate));
        return pgBondYieldPanoramaQuantileViewMapper.selectByDynamicQuery(query);
    }
}