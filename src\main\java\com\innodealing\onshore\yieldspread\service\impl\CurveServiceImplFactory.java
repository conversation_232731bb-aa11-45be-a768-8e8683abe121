package com.innodealing.onshore.yieldspread.service.impl;

import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 各种曲线实现类工厂
 *
 * <AUTHOR>
 */
@Component
public final class CurveServiceImplFactory {

    @Resource
    private InduBondYieldSpreadServiceImpl induBondYieldSpreadService;

    @Resource
    private UdicBondYieldSpreadServiceImpl udicBondYieldSpreadService;

    @Resource
    private BankBondYieldSpreadServiceImpl bankBondYieldSpreadService;

    @Resource
    private SecuBondYieldSpreadServiceImpl secuBondYieldSpreadService;

    @Resource
    private InsuBondYieldSpreadServiceImpl insuBondYieldSpreadService;

    @Resource
    private CustomCurveServiceImpl customCurveService;

    @Resource
    private ChinaBondCurveService chinaBondCurveService;

    private final Map<CurveTypeEnum, AbstractBondCurveService> curveServiceMap = new ConcurrentHashMap<>(6);

    /**
     * 获取曲线实现类
     *
     * @param curveType 曲线类型
     * @return 曲线实现类
     */
    public AbstractBondCurveService getBondCurveService(CurveTypeEnum curveType) {
        return curveServiceMap.get(curveType);
    }

    @PostConstruct
    private void init() {
        curveServiceMap.put(CurveTypeEnum.INDU, induBondYieldSpreadService);
        curveServiceMap.put(CurveTypeEnum.UDIC, udicBondYieldSpreadService);
        curveServiceMap.put(CurveTypeEnum.BANK, bankBondYieldSpreadService);
        curveServiceMap.put(CurveTypeEnum.SECURITY, secuBondYieldSpreadService);
        curveServiceMap.put(CurveTypeEnum.CUSTOMIZATION, customCurveService);
        curveServiceMap.put(CurveTypeEnum.CB, chinaBondCurveService);
        curveServiceMap.put(CurveTypeEnum.INSURANCE, insuBondYieldSpreadService);
    }

}
