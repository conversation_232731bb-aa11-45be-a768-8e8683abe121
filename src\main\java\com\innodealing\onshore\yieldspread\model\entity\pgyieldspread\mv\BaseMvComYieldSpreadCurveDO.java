package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 基础主体利差曲线物化视图
 *
 * <AUTHOR>
 */
public class BaseMvComYieldSpreadCurveDO {

    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 主体唯一编码
     */
    @Column
    private Long comUniCode;
    /**
     * 信用利差;单位(BP)(中位数)
     */
    @Column
    private BigDecimal bondCreditSpread;
    /**
     * 超额利差;单位(BP)(中位数)
     */
    @Column
    private BigDecimal bondExcessSpread;
    /**
     * 中债收益率;单位(BP)(中位数)
     */
    @Column
    private BigDecimal cbYield;

    /**
     * 信用利差;单位(BP)(平均数)
     */
    @Column
    private BigDecimal avgBondCreditSpread;
    /**
     * 超额利差;单位(BP)(平均数)
     */
    @Column
    private BigDecimal avgBondExcessSpread;
    /**
     * 中债收益率;单位(BP)(平均数)
     */
    @Column
    private BigDecimal avgCbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getAvgBondCreditSpread() {
        return avgBondCreditSpread;
    }

    public void setAvgBondCreditSpread(BigDecimal avgBondCreditSpread) {
        this.avgBondCreditSpread = avgBondCreditSpread;
    }

    public BigDecimal getAvgBondExcessSpread() {
        return avgBondExcessSpread;
    }

    public void setAvgBondExcessSpread(BigDecimal avgBondExcessSpread) {
        this.avgBondExcessSpread = avgBondExcessSpread;
    }

    public BigDecimal getAvgCbYield() {
        return avgCbYield;
    }

    public void setAvgCbYield(BigDecimal avgCbYield) {
        this.avgCbYield = avgCbYield;
    }
}
