package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * 证券单券利差
 *
 * <AUTHOR>
 */
public class SecuSingleBondYieldSpreadResDTO extends BaseSingleBondYieldSpreadResDTO {

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.BusinessNatureEnum
     */
    @ApiModelProperty("企业性质")
    private Integer businessNature;

    @ApiModelProperty("企业性质")
    private String businessNatureStr;

    @ApiModelProperty("求偿顺序 1:普通,2:次级,3:永续")
    private Integer securitySeniorityRanking;

    @ApiModelProperty("求偿顺序 1:普通,2:次级,3:永续")
    private String securitySeniorityRankingStr;

    public String getSecuritySeniorityRankingStr() {
        return securitySeniorityRankingStr;
    }

    public void setSecuritySeniorityRankingStr(String securitySeniorityRankingStr) {
        this.securitySeniorityRankingStr = securitySeniorityRankingStr;
    }

    public String getBusinessNatureStr() {
        return businessNatureStr;
    }

    public void setBusinessNatureStr(String businessNatureStr) {
        this.businessNatureStr = businessNatureStr;
    }

    public Integer getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(Integer businessNature) {
        this.businessNature = businessNature;
    }

    public Integer getSecuritySeniorityRanking() {
        return securitySeniorityRanking;
    }

    public void setSecuritySeniorityRanking(Integer securitySeniorityRanking) {
        this.securitySeniorityRanking = securitySeniorityRanking;
    }

}
