<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view.PgLgBondYieldSpreadAreaViewMapper">

    <sql id="lgQueryFieldSql">
        , ((MEDIAN(case when lg_remaining_grade = 1 then bond_credit_spread end)::double precision) ::DECIMAL)   AS credit_spread_1m
             , ((MEDIAN(case when lg_remaining_grade = 1 then bond_credit_spread_tb end)::double precision) ::DECIMAL)   AS credit_spread_tb_1m
             , ((MEDIAN(case when lg_remaining_grade = 1 then cb_yield end)::double precision) ::DECIMAL)             AS cb_yield_1m

             , ((MEDIAN(case when lg_remaining_grade = 3 then bond_credit_spread end)::double precision) ::DECIMAL)   AS credit_spread_3m
             , ((MEDIAN(case when lg_remaining_grade = 3 then bond_credit_spread_tb end)::double precision) ::DECIMAL)   AS credit_spread_tb_3m
             , ((MEDIAN(case when lg_remaining_grade = 3 then cb_yield end)::double precision) ::DECIMAL)             AS cb_yield_3m

             , ((MEDIAN(case when lg_remaining_grade = 6 then bond_credit_spread end)::double precision) ::DECIMAL)   AS credit_spread_6m
             , ((MEDIAN(case when lg_remaining_grade = 6 then bond_credit_spread_tb end)::double precision) ::DECIMAL)   AS credit_spread_tb_6m
             , ((MEDIAN(case when lg_remaining_grade = 6 then cb_yield end)::double precision) ::DECIMAL)             AS cb_yield_6m

             , ((MEDIAN(case when lg_remaining_grade = 9 then bond_credit_spread end)::double precision) ::DECIMAL)   AS credit_spread_9m
             , ((MEDIAN(case when lg_remaining_grade = 9 then bond_credit_spread_tb end)::double precision) ::DECIMAL)   AS credit_spread_tb_9m
             , ((MEDIAN(case when lg_remaining_grade = 9 then cb_yield end)::double precision) ::DECIMAL)             AS cb_yield_9m

             , ((MEDIAN(case when lg_remaining_grade = 12 then bond_credit_spread end)::double precision) ::DECIMAL)  AS credit_spread_1y
             , ((MEDIAN(case when lg_remaining_grade = 12 then bond_credit_spread_tb end)::double precision) ::DECIMAL)  AS credit_spread_tb_1y
             , ((MEDIAN(case when lg_remaining_grade = 12 then cb_yield end)::double precision) ::DECIMAL)            AS cb_yield_1y

             , ((MEDIAN(case when lg_remaining_grade = 24 then bond_credit_spread end)::double precision) ::DECIMAL)  AS credit_spread_2y
             , ((MEDIAN(case when lg_remaining_grade = 24 then bond_credit_spread_tb end)::double precision) ::DECIMAL)  AS credit_spread_tb_2y
             , ((MEDIAN(case when lg_remaining_grade = 24 then cb_yield end)::double precision) ::DECIMAL)            AS cb_yield_2y

             , ((MEDIAN(case when lg_remaining_grade = 36 then bond_credit_spread end)::double precision) ::DECIMAL)  AS credit_spread_3y
             , ((MEDIAN(case when lg_remaining_grade = 36 then bond_credit_spread_tb end)::double precision) ::DECIMAL)  AS credit_spread_tb_3y
             , ((MEDIAN(case when lg_remaining_grade = 36 then cb_yield end)::double precision) ::DECIMAL)            AS cb_yield_3y

             , ((MEDIAN(case when lg_remaining_grade = 60 then bond_credit_spread end)::double precision) ::DECIMAL)  AS credit_spread_5y
             , ((MEDIAN(case when lg_remaining_grade = 60 then bond_credit_spread_tb end)::double precision) ::DECIMAL)  AS credit_spread_tb_5y
             , ((MEDIAN(case when lg_remaining_grade = 60 then cb_yield end)::double precision) ::DECIMAL)            AS cb_yield_5y

             , ((MEDIAN(case when lg_remaining_grade = 84 then bond_credit_spread end)::double precision) ::DECIMAL)  AS credit_spread_7y
             , ((MEDIAN(case when lg_remaining_grade = 84 then bond_credit_spread_tb end)::double precision) ::DECIMAL)  AS credit_spread_tb_7y
             , ((MEDIAN(case when lg_remaining_grade = 84 then cb_yield end)::double precision) ::DECIMAL)            AS cb_yield_7y

             , ((MEDIAN(case when lg_remaining_grade = 120 then bond_credit_spread end)::double precision) ::DECIMAL) AS credit_spread_10y
             , ((MEDIAN(case when lg_remaining_grade = 120 then bond_credit_spread_tb end)::double precision) ::DECIMAL) AS credit_spread_tb_10y
             , ((MEDIAN(case when lg_remaining_grade = 120 then cb_yield end)::double precision) ::DECIMAL)           AS cb_yield_10y

             , ((MEDIAN(case when lg_remaining_grade = 180 then bond_credit_spread end)::double precision) ::DECIMAL) AS credit_spread_15y
             , ((MEDIAN(case when lg_remaining_grade = 180 then bond_credit_spread_tb end)::double precision) ::DECIMAL) AS credit_spread_tb_15y
             , ((MEDIAN(case when lg_remaining_grade = 180 then cb_yield end)::double precision) ::DECIMAL)           AS cb_yield_15y

             , ((MEDIAN(case when lg_remaining_grade = 240 then bond_credit_spread end)::double precision) ::DECIMAL) AS credit_spread_20y
             , ((MEDIAN(case when lg_remaining_grade = 240 then bond_credit_spread_tb end)::double precision) ::DECIMAL) AS credit_spread_tb_20y
             , ((MEDIAN(case when lg_remaining_grade = 240 then cb_yield end)::double precision) ::DECIMAL)           AS cb_yield_20y

             , ((MEDIAN(case when lg_remaining_grade = 360 then bond_credit_spread end)::double precision) ::DECIMAL) AS credit_spread_30y
             , ((MEDIAN(case when lg_remaining_grade = 360 then bond_credit_spread_tb end)::double precision) ::DECIMAL) AS credit_spread_tb_30y
             , ((MEDIAN(case when lg_remaining_grade = 360 then cb_yield end)::double precision) ::DECIMAL)           AS cb_yield_30y
    </sql>

    <insert id="createLgBondYieldSpreadAreaView">
        create MATERIALIZED view yield_spread.${lgMvName}
        WITH (appendoptimized= true, orientation = column)
        as
        select spread_date
        <if test="comUniCode==null or comUniCode!=-1">
            , com_uni_code
        </if>
        <if test="comUniCode!=null and comUniCode==-1">
            ,-1 AS com_uni_code
        </if>
        , lg_bond_type
        , prepayment_status
        , fund_use_type
        <include refid="lgQueryFieldSql"></include>
        , grouping(lg_bond_type) as using_lg_bond_type
        , grouping(prepayment_status) as using_prepayment_status
        , grouping(fund_use_type) as using_fund_use_type
        from lg_bond_yield_spread
        where spread_date <![CDATA[ <= ]]> '${endDate}'
        and spread_date <![CDATA[ >= ]]> '${startDate}'
        <if test="comUniCode!=null and comUniCode!=-1">
            and com_uni_code=#{comUniCode}
        </if>
        group by spread_date,
        <if test="comUniCode==null or comUniCode!=-1">
            com_uni_code,
        </if>
        CUBE ( lg_bond_type, prepayment_status, fund_use_type)
    </insert>
    <select id="listLgBondYieldSpreadAreaView"
            resultType="com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgLgBondYieldSpreadAreaViewDO">
        SELECT com_uni_code            AS comUniCode,
               spread_date             AS spreadDate,
               lg_bond_type            AS lgBondType,
               prepayment_status       AS prepaymentStatus,
               fund_use_type           AS fundUseType,
               using_lg_bond_type      AS usingLgBondType,
               using_prepayment_status AS usingPrepaymentStatus,
               using_fund_use_type     AS usingFundUseType,
               credit_spread_1m        AS creditSpread1M,
               credit_spread_tb_1m     AS creditSpreadTb1M,
               cb_yield_1m             AS cbYield1M,
               credit_spread_3m        AS creditSpread3M,
               credit_spread_tb_3m     AS creditSpreadTb3M,
               cb_yield_3m             AS cbYield3M,
               credit_spread_6m        AS creditSpread6M,
               credit_spread_tb_6m     AS creditSpreadTb6M,
               cb_yield_6m             AS cbYield6M,
               credit_spread_9m        AS creditSpread9M,
               credit_spread_tb_9m     AS creditSpreadTb9M,
               cb_yield_9m             AS cbYield9M,
               credit_spread_1y        AS creditSpread1Y,
               credit_spread_tb_1y     AS creditSpreadTb1Y,
               cb_yield_1y             AS cbYield1Y,
               credit_spread_2y        AS creditSpread2Y,
               credit_spread_tb_2y     AS creditSpreadTb2Y,
               cb_yield_2y             AS cbYield2Y,
               credit_spread_3y        AS creditSpread3Y,
               credit_spread_tb_3y     AS creditSpreadTb3Y,
               cb_yield_3y             AS cbYield3Y,
               credit_spread_5y        AS creditSpread5Y,
               credit_spread_tb_5y     AS creditSpreadTb5Y,
               cb_yield_5y             AS cbYield5Y,
               credit_spread_7y        AS creditSpread7Y,
               credit_spread_tb_7y     AS creditSpreadTb7Y,
               cb_yield_7y             AS cbYield7Y,
               credit_spread_10y       AS creditSpread10Y,
               credit_spread_tb_10y    AS creditSpreadTb10Y,
               cb_yield_10y            AS cbYield10Y,
               credit_spread_15y       AS creditSpread15Y,
               credit_spread_tb_15y    AS creditSpreadTb15Y,
               cb_yield_15y            AS cbYield15Y,
               credit_spread_20y       AS creditSpread20Y,
               credit_spread_tb_20y    AS creditSpreadTb20Y,
               cb_yield_20y            AS cbYield20Y,
               credit_spread_30y       AS creditSpread30Y,
               credit_spread_tb_30y    AS creditSpreadTb30Y,
               cb_yield_30y            AS cbYield30Y
        FROM ${lgMvName}
        WHERE spread_date = '${spreadDate}'
    </select>

</mapper>