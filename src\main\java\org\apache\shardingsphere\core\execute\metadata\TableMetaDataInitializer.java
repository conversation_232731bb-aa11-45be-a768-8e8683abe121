/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.shardingsphere.core.execute.metadata;

import com.google.common.base.Optional;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import org.apache.shardingsphere.core.execute.engine.ShardingExecuteEngine;
import org.apache.shardingsphere.core.metadata.datasource.DataSourceMetas;
import org.apache.shardingsphere.core.metadata.table.TableMetaData;
import org.apache.shardingsphere.core.rule.ShardingRule;
import org.apache.shardingsphere.spi.database.DataSourceMetaData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * sharding-jdbc-core 4.0.0
 * Table meta data initializer.
 * 主要通过多线程加载优化启动慢的问题
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@SuppressWarnings("all")
public final class TableMetaDataInitializer {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final DataSourceMetas dataSourceMetas;

    private final TableMetaDataConnectionManager connectionManager;

    private final TableMetaDataLoader tableMetaDataLoader;

    public TableMetaDataInitializer(final DataSourceMetas dataSourceMetas, final ShardingExecuteEngine executeEngine,
                                    final TableMetaDataConnectionManager connectionManager, final int maxConnectionsSizePerQuery, final boolean isCheckingMetaData) {
        this.dataSourceMetas = dataSourceMetas;
        this.connectionManager = connectionManager;
        tableMetaDataLoader = new TableMetaDataLoader(dataSourceMetas, executeEngine, connectionManager, maxConnectionsSizePerQuery, isCheckingMetaData);
    }

    /**
     * Load table meta data.
     *
     * @param logicTableName logic table name
     * @param shardingRule   sharding rule
     * @return table meta data
     * @throws SQLException SQL exception
     */
    public TableMetaData load(final String logicTableName, final ShardingRule shardingRule) throws SQLException {
        return tableMetaDataLoader.load(logicTableName, shardingRule);
    }

    /**
     * Load all table meta data.
     *
     * @param shardingRule sharding rule
     * @return all table meta data
     * @throws SQLException SQL exception
     */
    public Map<String, TableMetaData> load(final ShardingRule shardingRule) throws SQLException {
        final ExecutorService executorService = Executors.newFixedThreadPool(8);
        logger.info("TableMetaDataInitializer => loading...");
        Map<String, TableMetaData> result = new HashMap<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("loadShardingTables");
        result.putAll(this.loadShardingTables(shardingRule, executorService));
        stopWatch.stop();
        stopWatch.start("loadDefaultTables");
        result.putAll(this.loadDefaultTables(shardingRule, executorService));
        stopWatch.stop();
        logger.info("TableMetaDataInitializer => load success result：{} {}", result.size(), stopWatch.prettyPrint());
        executorService.shutdown();
        return result;
    }

    private Map<String, TableMetaData> loadShardingTables(final ShardingRule shardingRule, final ExecutorService executorService) throws SQLException {
        Map<String, TableMetaData> result = new ConcurrentHashMap<>(shardingRule.getTableRules().size(), 1);
        SwThreadPoolWorker swThreadPoolWorker = new SwThreadPoolWorker();
        CompletableFuture[] completableFutures = shardingRule.getTableRules().stream().map(each -> {
            return CompletableFuture.runAsync(() -> {
                try {
                    result.put(each.getLogicTable(), tableMetaDataLoader.load(each.getLogicTable(), shardingRule));
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
            }, executorService);
        }).toArray(CompletableFuture[]::new);
        swThreadPoolWorker.doWorks(completableFutures);
        return result;
    }


    private Map<String, TableMetaData> loadDefaultTables(final ShardingRule shardingRule, final ExecutorService executorService) throws SQLException {
        Map<String, TableMetaData> result = new ConcurrentHashMap<>(shardingRule.getTableRules().size(), 1);
        SwThreadPoolWorker swThreadPoolWorker = new SwThreadPoolWorker();
        Optional<String> actualDefaultDataSourceName = shardingRule.findActualDefaultDataSourceName();
        if (actualDefaultDataSourceName.isPresent()) {
//            Collection<String> allTableNames = this.getAllTableNames(actualDefaultDataSourceName.get());
//            CompletableFuture[] completableFutures = allTableNames.stream().map(each -> {
//                return CompletableFuture.runAsync(() -> {
//                    try {
//                        result.put(each, tableMetaDataLoader.load(each, shardingRule));
//                    } catch (SQLException e) {
//                        throw new RuntimeException(e);
//                    }
//                }, executorService);
//            }).toArray(CompletableFuture[]::new);
//            swThreadPoolWorker.doWorks(completableFutures);
        }
        return result;
    }

    private Collection<String> getAllTableNames(final String dataSourceName) throws SQLException {
        Collection<String> result = new LinkedHashSet<>();
        DataSourceMetaData dataSourceMetaData = this.dataSourceMetas.getDataSourceMetaData(dataSourceName);
        String catalog = null == dataSourceMetaData ? null : dataSourceMetaData.getCatalog();
        String schemaName = null == dataSourceMetaData ? null : dataSourceMetaData.getSchema();
        try (Connection connection = connectionManager.getConnection(dataSourceName);
             ResultSet resultSet = connection.getMetaData().getTables(catalog, schemaName, null, new String[]{"TABLE"})) {
            while (resultSet.next()) {
                String tableName = resultSet.getString("TABLE_NAME");
                if (!tableName.contains("$") && !tableName.contains("/")) {
                    result.add(tableName);
                }
            }
        }
        return result;
    }

}
