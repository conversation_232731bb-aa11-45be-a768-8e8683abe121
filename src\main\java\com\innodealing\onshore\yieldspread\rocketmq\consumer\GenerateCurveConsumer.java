package com.innodealing.onshore.yieldspread.rocketmq.consumer;

import com.innodealing.onshore.bondmetadata.constant.MqConsumerGroups;
import com.innodealing.onshore.bondmetadata.constant.MqTags;
import com.innodealing.onshore.bondmetadata.constant.MqTopics;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.service.impl.CustomCurveServiceImpl;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 生成自定义曲线的消费者
 *
 * <AUTHOR>
 */
@Component
@RocketMQMessageListener(
        topic = MqTopics.ONSHORE_YIELD_SPREAD,
        consumerGroup = MqConsumerGroups.YIELD_SPREAD_CUSTOM_CURVE_GENERATE,
        selectorExpression = MqTags.ONSHORE_YIELD_SPREAD_CUSTOM_CURVE_GENERATE,
        consumeThreadMax = YieldSpreadConst.GENERATE_CURVE_MAX_CONCURRENCY)
public class GenerateCurveConsumer implements RocketMQListener<MessageExt> {

    public static final Logger LOGGER = LoggerFactory.getLogger(GenerateCurveConsumer.class);

    @Resource
    private CustomCurveServiceImpl customCurveService;

    @Override
    public void onMessage(MessageExt message) {
        long curveId = Long.parseLong(new String(message.getBody(), StandardCharsets.UTF_8));
        LOGGER.info("GenerateCurve_{} begin", curveId);
        try {
            customCurveService.generateCurve(curveId);
        } catch (Exception e) {
            String throwMsgStr = String.format("GenerateCurve_%s error,", curveId);
            LOGGER.error(throwMsgStr, e);
            throw new BusinessException(throwMsgStr + e);
        }
    }

}
