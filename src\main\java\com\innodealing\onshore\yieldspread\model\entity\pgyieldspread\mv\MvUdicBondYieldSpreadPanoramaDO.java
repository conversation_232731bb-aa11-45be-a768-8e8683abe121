package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;

/**
 * 城投债利差全景-物化视图
 *
 * <AUTHOR>
 **/
@Table(name = "mv_udic_bond_yield_spread_panorama")
public class MvUdicBondYieldSpreadPanoramaDO {

    /**
     * 省份编码
     */
    @Column
    private Long provinceUniCode;
    /**
     * 地级市编码
     */
    @Column
    private Long cityUniCode;

    /**
     * 行政区划
     */
    @Column
    private Integer administrativeDivision;
    /**
     * 利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer spreadBondType;
    /**
     * 隐含评级标签
     */
    @Column
    private Integer bondImpliedRatingMappingTag;
    /**
     * yy评级标签
     */
    @Column
    private Integer comYyRatingMappingTag;
    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;
    /**
     * 担保状态: 0: 无; 1: 有
     */
    @Column
    private Integer guaranteedStatus;
    /**
     * 债券外部评级映射
     */
    @Column
    private Integer bondExtRatingMapping;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 债券信用利差中位数;单位(BP)
     */
    @Column
    private Long bondCreditSpread;
    /**
     * 债券超额利差中位数;单位(BP)
     */
    @Column
    private Long bondExcessSpread;
    /**
     * 债券信用利差count
     */
    @Column
    private Integer bondCreditSpreadCount;
    /**
     * 债券超额利差count
     */
    @Column
    private Integer bondExcessSpreadCount;

    /**
     * 是否使用 行政区划
     */
    @Column
    private Integer usingAdministrativeDivision;
    /**
     * 是否使用 利差债券类别
     */
    @Column
    private Integer usingSpreadBondType;
    /**
     * 是否使用 地级市编码标签
     */
    @Column
    private Integer usingCityUniCode;
    /**
     * 是否根据隐含评级标签进行分组，0：是，1：否
     */
    @Column
    private Integer usingBondImpliedRatingMappingTag;
    /**
     * 是否根据yy评级标签进行分组，0：是，1：否
     */
    @Column
    private Integer usingComYyRatingMappingTag;
    /**
     * 是否使用 利差剩余期限标签
     */
    @Column
    private Integer usingSpreadRemainingTenorTag;
    /**
     * 是否使用 担保状态
     */
    @Column
    private Integer usingGuaranteedStatus;
    /**
     * 是否使用 债券外部评级映射
     */
    @Column
    private Integer usingBondExtRatingMapping;

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }

    public Integer getAdministrativeDivision() {
        return administrativeDivision;
    }

    public void setAdministrativeDivision(Integer administrativeDivision) {
        this.administrativeDivision = administrativeDivision;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public Long getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(Long bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public Long getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(Long bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getUsingAdministrativeDivision() {
        return usingAdministrativeDivision;
    }

    public void setUsingAdministrativeDivision(Integer usingAdministrativeDivision) {
        this.usingAdministrativeDivision = usingAdministrativeDivision;
    }

    public Integer getUsingSpreadBondType() {
        return usingSpreadBondType;
    }

    public void setUsingSpreadBondType(Integer usingSpreadBondType) {
        this.usingSpreadBondType = usingSpreadBondType;
    }

    public Integer getUsingCityUniCode() {
        return usingCityUniCode;
    }

    public void setUsingCityUniCode(Integer usingCityUniCode) {
        this.usingCityUniCode = usingCityUniCode;
    }

    public Integer getUsingSpreadRemainingTenorTag() {
        return usingSpreadRemainingTenorTag;
    }

    public void setUsingSpreadRemainingTenorTag(Integer usingSpreadRemainingTenorTag) {
        this.usingSpreadRemainingTenorTag = usingSpreadRemainingTenorTag;
    }

    public Integer getUsingGuaranteedStatus() {
        return usingGuaranteedStatus;
    }

    public void setUsingGuaranteedStatus(Integer usingGuaranteedStatus) {
        this.usingGuaranteedStatus = usingGuaranteedStatus;
    }

    public Integer getUsingBondExtRatingMapping() {
        return usingBondExtRatingMapping;
    }

    public void setUsingBondExtRatingMapping(Integer usingBondExtRatingMapping) {
        this.usingBondExtRatingMapping = usingBondExtRatingMapping;
    }

    public Integer getBondImpliedRatingMappingTag() {
        return bondImpliedRatingMappingTag;
    }

    public void setBondImpliedRatingMappingTag(Integer bondImpliedRatingMappingTag) {
        this.bondImpliedRatingMappingTag = bondImpliedRatingMappingTag;
    }

    public Integer getComYyRatingMappingTag() {
        return comYyRatingMappingTag;
    }

    public void setComYyRatingMappingTag(Integer comYyRatingMappingTag) {
        this.comYyRatingMappingTag = comYyRatingMappingTag;
    }

    public Integer getUsingBondImpliedRatingMappingTag() {
        return usingBondImpliedRatingMappingTag;
    }

    public void setUsingBondImpliedRatingMappingTag(Integer usingBondImpliedRatingMappingTag) {
        this.usingBondImpliedRatingMappingTag = usingBondImpliedRatingMappingTag;
    }

    public Integer getUsingComYyRatingMappingTag() {
        return usingComYyRatingMappingTag;
    }

    public void setUsingComYyRatingMappingTag(Integer usingComYyRatingMappingTag) {
        this.usingComYyRatingMappingTag = usingComYyRatingMappingTag;
    }
}