package com.innodealing.onshore.yieldspread.model.dto.request.excel;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.util.Objects;

/**
 * 利差追踪导出请求dto
 *
 * <AUTHOR>
 * @date 2024/4/28 19:03
 **/
public class BondYieldTraceExportReqDTO {

    @ApiModelProperty(value = "债券类型(1 国债 2 国开债 4 中短期票据,  5 产业债, 6 城投, 7 银行普通债, 8 银行二级资本债, 9 银行永续债, 10 证券公司债, 11 证券次级债, 12 证券永续债, 13 同业存单，14 保险资本补充)")
    @NotNull
    private Integer bondType;
    @ApiModelProperty(value = "利率时间")
    @NotNull
    private Date spreadDate;

    @ApiModelProperty(value = "变动类型(1 一周变动 2 一月变动 3 三月变动 4 六月变动 5 一年变动 6 一日变动 7 两周变动)")
    private Integer changeType;

    @ApiModelProperty(value = "区间变动自定义范围开始时间")
    private Date changeStartDate;

    @ApiModelProperty(value = "区间变动自定义范围结束时间")
    private Date changeEndDate;


    @ApiModelProperty(value = "分位类型 1:3年，2:5年")
    private Integer quantileType;
    @ApiModelProperty(value = "历史分位自定义范围开始时间")
    private Date quantileStartDate;
    @ApiModelProperty(value = "历史分位自定义范围开始时间")
    private Date quantileEndDate;

    public Integer getBondType() {
        return bondType;
    }

    public void setBondType(Integer bondType) {
        this.bondType = bondType;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = new Date(spreadDate.getTime());
    }

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Date getChangeStartDate() {
        return Objects.isNull(changeStartDate) ? null : new Date(changeStartDate.getTime());
    }

    public void setChangeStartDate(Date changeStartDate) {
        this.changeStartDate = Objects.isNull(changeStartDate) ? null : new Date(changeStartDate.getTime());
    }

    public Date getChangeEndDate() {
        return Objects.isNull(changeEndDate) ? null : new Date(changeEndDate.getTime());
    }

    public void setChangeEndDate(Date changeEndDate) {
        this.changeEndDate = Objects.isNull(changeEndDate) ? null : new Date(changeEndDate.getTime());
    }

    public Integer getQuantileType() {
        return quantileType;
    }

    public void setQuantileType(Integer quantileType) {
        this.quantileType = quantileType;
    }

    public Date getQuantileStartDate() {
        return Objects.isNull(quantileStartDate) ? null : new Date(quantileStartDate.getTime());
    }

    public void setQuantileStartDate(Date quantileStartDate) {
        this.quantileStartDate = Objects.isNull(quantileStartDate) ? null : new Date(quantileStartDate.getTime());
    }

    public Date getQuantileEndDate() {
        return Objects.isNull(quantileEndDate) ? null : new Date(quantileEndDate.getTime());
    }

    public void setQuantileEndDate(Date quantileEndDate) {
        this.quantileEndDate = Objects.isNull(quantileEndDate) ? null : new Date(quantileEndDate.getTime());
    }
}
