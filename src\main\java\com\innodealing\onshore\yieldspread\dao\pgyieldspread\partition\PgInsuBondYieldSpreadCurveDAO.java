package com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.yieldspread.dao.pgshard.partition.InsuBondShardYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvInsuBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.partition.InsuShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition.PgInsuBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.InsuBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.InsuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.InsuShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.EmptyRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.between;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.divideHundredThousand;

/**
 * 保险利差曲线DAO
 *
 * <AUTHOR>
 */
@Repository
public class PgInsuBondYieldSpreadCurveDAO {

    @Resource
    private PgInsuBondYieldSpreadCurveMapper pgInsuBondYieldSpreadCurveMapper;

    @Resource
    private InsuShardBondYieldSpreadCurveMapper insuShardBondYieldSpreadCurveMapper;

    @Resource
    private MvInsuBondYieldSpreadCurveDAO mvInsuBondYieldSpreadCurveDAO;

    @Resource
    private InsuBondShardYieldSpreadCurveRepository insuYieldSpreadCurveRepository;

    @Resource
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;

    /**
     * 同步物化视图到pg
     *
     * @param router 路由
     * @param param  pg创建参数
     */
    public void syncCurveShardInsuForMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        InsuBondYieldSpreadCurveParameter parameter = builderParameter(router);
        if (param.isTableRefresh()) {
            pgInsuBondYieldSpreadCurveMapper.refreshTable(parameter.getTableName(), parameter);
        }
        DynamicQuery<InsuShardBondYieldSpreadCurveDO> query = builderDynamicQuery(router);
        int count = insuShardBondYieldSpreadCurveMapper.selectCountByDynamicQueryRouter(query, router);
        if (count > 0) {
            insuShardBondYieldSpreadCurveMapper.deleteByDynamicQueryRouter(query, router);
        }
        //同步最新数据
        pgInsuBondYieldSpreadCurveMapper.syncCurveIncrFromMV(parameter.getTableName(), mvInsuBondYieldSpreadCurveDAO.getMvName(router));
    }

    private InsuBondYieldSpreadCurveParameter builderParameter(AbstractRatingRouter router) {
        InsuBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, InsuBondYieldSpreadCurveParameter.class);
        parameter.setTableName(getTableName(router));
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (spreadDateRange != null) {
            parameter.setStartDate(spreadDateRange.getStartDate());
            parameter.setEndDate(spreadDateRange.getEndDate());
        }
        return parameter;
    }

    private DynamicQuery<InsuShardBondYieldSpreadCurveDO> builderDynamicQuery(AbstractRatingRouter router) {
        DynamicQuery<InsuShardBondYieldSpreadCurveDO> query = DynamicQuery.createQuery(InsuShardBondYieldSpreadCurveDO.class);
        if (Objects.nonNull(router.getSpreadDateRange()) &&
                Objects.nonNull(router.getSpreadDateRange().getStartDate()) &&
                Objects.nonNull(router.getSpreadDateRange().getEndDate())) {
            query.and(InsuShardBondYieldSpreadCurveDO::getSpreadDate,
                    between(router.getSpreadDateRange().getStartDate(), router.getSpreadDateRange().getEndDate()));
        }
        return query;
    }

    private String getTableName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()), insuShardBondYieldSpreadCurveMapper.getLogicTable(),
                RatingCombinationHelper.getTableNameSuffix(router));
    }

    /**
     * 查询保险利差
     *
     * @param params 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listInsuYieldSpreads(InsuYieldSearchParam params) {
        List<InsuShardBondYieldSpreadCurveDO> yieldSpreads = insuYieldSpreadCurveRepository
                .listInsuYieldSpreads(params, this.getRatingRouter(params.getBondImpliedRatingMappings()));
        List<BondYieldSpreadBO> bondYieldSpreadBOList = BeanCopyUtils.copyList(yieldSpreads, BondYieldSpreadBO.class);
        if (CollectionUtils.isNotEmpty(bondYieldSpreadBOList)) {
            bondYieldSpreadBOList.forEach(ys -> {
                ObjectExtensionUtils.ifNonNull(ys.getBondCreditSpread(), v -> divideHundredThousand(v).ifPresent(ys::setBondCreditSpread));
                ObjectExtensionUtils.ifNonNull(ys.getBondExcessSpread(), v -> divideHundredThousand(v).ifPresent(ys::setBondExcessSpread));
                ObjectExtensionUtils.ifNonNull(ys.getCbYield(), v -> divideHundredThousand(v, YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE).ifPresent(ys::setCbYield));
                ObjectExtensionUtils.ifNonNull(ys.getAvgBondCreditSpread(), v -> divideHundredThousand(v).ifPresent(ys::setAvgBondCreditSpread));
                ObjectExtensionUtils.ifNonNull(ys.getAvgBondExcessSpread(), v -> divideHundredThousand(v).ifPresent(ys::setAvgBondExcessSpread));
                ObjectExtensionUtils.ifNonNull(ys.getAvgCbYield(), v -> divideHundredThousand(v, YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE).ifPresent(ys::setAvgCbYield));
            });
        }
        return bondYieldSpreadBOList;
    }

    private AbstractRatingRouter getRatingRouter(Integer[] bondImpliedRatingMappings) {
        return ArrayUtils.isEmpty(bondImpliedRatingMappings) ? new EmptyRouter() : implicitRatingRouterFactory.newRatingRouter(bondImpliedRatingMappings);
    }

}
