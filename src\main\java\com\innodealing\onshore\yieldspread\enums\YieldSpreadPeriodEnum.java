package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 利差期限枚举
 *
 * <AUTHOR>
 */
public enum YieldSpreadPeriodEnum implements ITextValueEnum {
    /**
     * 期限
     */
    YTM_1Y(1, "1Y"),
    YTM_2Y(2, "2Y"),
    YTM_3Y(3, "3Y"),
    YTM_5Y(5, "5Y"),
    YTM_7Y(7, "7Y");

    private final Integer code;
    private final String text;

    /**
     * 构造函数
     *
     * @param code           code
     * @param text           text
     */
    YieldSpreadPeriodEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }

}
