package com.innodealing.onshore.yieldspread.mapper.pgyieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgPanoramaQuantileStatisticsViewDO;
import org.apache.ibatis.annotations.Param;

import java.sql.Date;
import java.util.List;

/**
 * 债券收益率全景-绝对值Mapper
 *
 * <AUTHOR>
 */
public interface PgBondYieldPanoramaAbsMapper extends DynamicQueryMapper<PgBondYieldPanoramaAbsDO> {
    /**
     * 获取指定时间范围内的某个发行时间 对应的分位统计的数据
     *
     * @param bondTypeList 债券品种
     * @param startDate    时间范围开始
     * @param endDate      时间范围结束
     * @param issueDate    发行时间
     * @return 分位统计的数据
     */
    List<PgPanoramaQuantileStatisticsViewDO> listPanoramaQuantileStatisticsViews(
            @Param("curveCodes") List<Integer> curveCodes,
            @Param("bondTypeList") List<Integer> bondTypeList,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("issueDate") Date issueDate);
}
