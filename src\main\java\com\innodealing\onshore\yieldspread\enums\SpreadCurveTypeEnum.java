package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;

import java.util.Optional;

/**
 * 利差曲线类型枚举类
 *
 * <AUTHOR>
 */
public enum SpreadCurveTypeEnum implements ITextValueEnum {
    /**
     * 信用利差
     * 默认-信用利差(减国开)
     */
    CREDIT_SPREAD(1, "信用利差"),
    /**
     * 超额利差
     */
    EXCESS_SPREAD(2, "超额利差"),
    /**
     * 估值收益率
     */
    CB_YIELD_SPREAD(3, "估值收益率"),
    /**
     * 信用利差(减国债)
     */
    CREDIT_SPREAD_TB(4, "信用利差(减国债)"),
    ;

    private Integer code;
    private String text;

    SpreadCurveTypeEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    /**
     * 获取 地方债-利差曲线类型枚举类
     *
     * @return 地方债-利差曲线类型枚举类
     */
    public LgSpreadCurveTypeEnum getLgSpreadCurveTypeEnum(){
        Optional<LgSpreadCurveTypeEnum> lgSpreadCurveTypeEnumOptional = LgSpreadCurveTypeEnum.getLgSpreadCurveTypeEnum(this);
        return lgSpreadCurveTypeEnumOptional.orElseThrow(() -> new TipsException("地方债-利差 曲线类型不存在"));
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return code;
    }
}
