package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.BankSeniorityRankingEnum;
import com.innodealing.onshore.bondmetadata.enums.BankTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadFinancialBondImpliedRatingMappingEnum;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * 银行利差曲线生成条件
 *
 * <AUTHOR>
 */
public class BankCurveGenerateConditionReqDTO extends AbstractCurveGenerateConditionReqDTO {

    private static final String CURVE_NAME_PREFIX = "银行";

    private static final Map<Integer, String> BOND_TYPE_NAME_MAP = initBondTypeNameMap();

    private static final Map<Integer, String> BANK_TYPE_NAME_MAP = initBankTypeNameMap();

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.BankSeniorityRankingEnum
     */
    @ApiModelProperty("债券类型 1:普通,2:二级资本债,3:永续")
    private Integer spreadBondType;

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.BankTypeEnum
     */
    @ApiModelProperty("银行类型 2:国有银行,3:股份制银行,4:城商行,5:农商行")
    private List<Integer> bankTypes;

    @Override
    public String getCurveName() {
        return CURVE_NAME_PREFIX +
                super.jointShortName() +
                this.jointBondTypeName() +
                this.jointBankTypesName() +
                super.jointBondImpliedRatingName(SpreadFinancialBondImpliedRatingMappingEnum.class) +
                super.jointRemainingTenorName();

    }

    private String jointBondTypeName() {
        return Objects.nonNull(spreadBondType) ? (SEPARATOR + BOND_TYPE_NAME_MAP.get(this.spreadBondType)) : "";
    }

    private String jointBankTypesName() {
        if (CollectionUtils.isEmpty(this.bankTypes)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (Integer bankType : this.bankTypes) {
            sb.append(BANK_TYPE_NAME_MAP.get(bankType)).append("/");
        }
        return SEPARATOR + sb.substring(0, sb.length() - 1);
    }

    private static Map<Integer, String> initBondTypeNameMap() {
        Map<Integer, String> bondTypeNameMap = new HashMap<>(BankSeniorityRankingEnum.values().length);
        bondTypeNameMap.put(BankSeniorityRankingEnum.NORMAL.getValue(), "普通债");
        bondTypeNameMap.put(BankSeniorityRankingEnum.TIER_2_BOND.getValue(), "二级资本债");
        bondTypeNameMap.put(BankSeniorityRankingEnum.PERPETUA.getValue(), "永续债");
        return bondTypeNameMap;
    }

    private static Map<Integer, String> initBankTypeNameMap() {
        Map<Integer, String> bankTypeNameMap = new HashMap<>(BankTypeEnum.values().length);
        bankTypeNameMap.put(BankTypeEnum.STATE_OWNED_COMMERCIAL_BANK.getValue(), "国有");
        bankTypeNameMap.put(BankTypeEnum.JOINT_STOCK_COMMERCIAL_BANK.getValue(), "股份");
        bankTypeNameMap.put(BankTypeEnum.CITY_COMMERCIAL_BANK.getValue(), "城商");
        bankTypeNameMap.put(BankTypeEnum.RURAL_COMMERCIAL_BANK.getValue(), "农商");
        return bankTypeNameMap;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public List<Integer> getBankTypes() {
        return Objects.isNull(bankTypes) ? new ArrayList<>() : new ArrayList<>(bankTypes);
    }

    public void setBankTypes(List<Integer> bankTypes) {
        this.bankTypes = Objects.isNull(bankTypes) ? new ArrayList<>() : new ArrayList<>(bankTypes);
    }

}
