package com.innodealing.onshore.yieldspread.mapper.dmdc;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.BondInterestCtzDO;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.group.BondInterestCtzGroupDO;

/**
 * 城投债利差(老表)
 *
 * <AUTHOR>
 **/
public interface BondInterestCtzMapper extends SelectByGroupedQueryMapper<BondInterestCtzGroupDO, BondInterestCtzDO> {
}
