package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import org.springframework.lang.NonNull;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 债券隐含评级和curveCode映射
 *
 * <AUTHOR>
 */
public enum BondImpliedRatingCurveCodeMappingEnum implements ITextValueEnum {
    /**
     * 债券隐含评级和curveCode映射
     */
    INDU_BOND_AAA(20, 100, "AAA"),
    INDU_BOND_AAA_MINUS(30, 101, "AAA-"),
    INDU_BOND_AA_PLUS(40, 102, "AA+"),
    INDU_BOND_AA(50, 103, "AA"),;

    private final Integer value;
    private final String text;
    private final int curveCode;

    private static final Map<Integer, Integer> INDU_BOND_MAPPING = new ConcurrentHashMap<>();

    static {
        Arrays.stream(values()).forEach(indu -> INDU_BOND_MAPPING.put(indu.getValue(), indu.getCurveCode()));
    }

    /**
     * 根据评级获取curveCode
     *
     * @param rating 评级
     * @return {@link Integer} curveCode
     */
    public static Integer getInduBondCurveCodeByRating(@NonNull Integer rating) {
        return INDU_BOND_MAPPING.get(rating);
    }

    /**
     * 获得债券隐含评级code
     *
     * @return {@link Set}<{@link Integer}> 隐含评级
     */
    public static Set<Integer> getBondImpliedRating() {
        return new HashSet<>(INDU_BOND_MAPPING.keySet());
    }


    /**
     * 获取债券curveCodes
     *
     * @return {@link Set}<{@link Integer}> curveCodes
     */
    public static Set<Integer> getInduCurveCodes() {
        return new HashSet<>(INDU_BOND_MAPPING.values());
    }

    BondImpliedRatingCurveCodeMappingEnum(Integer value, int curveCode, String text) {
        this.value = value;
        this.text = text;
        this.curveCode = curveCode;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public int getCurveCode() {
        return this.curveCode;
    }
}
