package com.innodealing.onshore.yieldspread.processor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.innodealing.onshore.bondmetadata.enums.CurveCode;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.enums.PeriodEnum;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.bondmetadata.enums.CurveCode.*;
import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.*;

/**
 * 利差追踪-条款利差处理器
 *
 * <AUTHOR>
 */
@Component
public class YieldSpreadTraceTermsProcessor implements YieldSpreadTraceProcessor {

    private static final Set<YieldPanoramaBondTypeEnum> SUPPORT_BANK_BOND_CODES = EnumSet.of(BANK_SECONDARY_CAPITAL_BOND, BANK_PERPETUAL_BOND);
    private static final Set<YieldPanoramaBondTypeEnum> SUPPORT_SECURITIES_BOND_CODES = EnumSet.of(SECURITIES_SUB_BOND, SECURITIES_PERPETUAL_BOND);

    private static final Map<Integer, Integer> CURVE_CODE_MAP = Maps.newHashMap();
    private static final Set<Integer> AVG_CURVE_CODES =
            Sets.newHashSet(SECURITIES_BOND_AAA.getValue(), SECURITIES_BOND_AAA_SUB.getValue(), SECURITIES_BOND_AA_PLUS.getValue());
    private static final Integer TWO_SCALE = 2;

    static {
        CURVE_CODE_MAP.put(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AAA_SUB.getValue(), CurveCode.GENERAL_BANK_BOND_AAA_SUB.getValue());
        CURVE_CODE_MAP.put(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA_PLUS.getValue(), CurveCode.GENERAL_BANK_BOND_AA_PLUS.getValue());
        CURVE_CODE_MAP.put(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA.getValue(), CurveCode.GENERAL_BANK_BOND_AA.getValue());
        CURVE_CODE_MAP.put(CurveCode.BANK_SECONDARY_CAPITAL_BOND_AA_SUB.getValue(), CurveCode.GENERAL_BANK_BOND_AA_SUB.getValue());

        CURVE_CODE_MAP.put(CurveCode.BANK_PERPETUAL_BOND_AAA_SUB.getValue(), CurveCode.GENERAL_BANK_BOND_AAA_SUB.getValue());
        CURVE_CODE_MAP.put(CurveCode.BANK_PERPETUAL_BOND_AA_PLUS.getValue(), CurveCode.GENERAL_BANK_BOND_AA_PLUS.getValue());
        CURVE_CODE_MAP.put(CurveCode.BANK_PERPETUAL_BOND_AA.getValue(), CurveCode.GENERAL_BANK_BOND_AA.getValue());
        CURVE_CODE_MAP.put(CurveCode.BANK_PERPETUAL_BOND_AA_SUB.getValue(), CurveCode.GENERAL_BANK_BOND_AA_SUB.getValue());
    }

    @Override
    public Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum) {
        return SUPPORT_BANK_BOND_CODES.contains(traceBondTypeEnum) || SUPPORT_SECURITIES_BOND_CODES.contains(traceBondTypeEnum);
    }

    @Override
    public List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context) {
        YieldPanoramaBondTypeEnum bondTypeEnum = context.getBondTypeEnum();
        List<PgBondYieldSpreadTraceAbsDO> dataList = Lists.newArrayListWithCapacity(context.getAbsBondYieldPanoramas().size());
        if (SUPPORT_SECURITIES_BOND_CODES.contains(bondTypeEnum)) {
            List<PgBondYieldPanoramaAbsDO> securitiesBondYieldPanoramas = context.getSecuritiesBondYieldPanoramas();
            Map<Integer, List<BigDecimal>> securitiesYtmMap = Maps.newHashMap();
            securitiesBondYieldPanoramas.stream().filter(secu -> AVG_CURVE_CODES.contains(secu.getCurveCode())).forEach(secu -> {
                securitiesYtmMap.computeIfAbsent(PeriodEnum.ONE_MONTH.getValue(), e -> new ArrayList<>()).add(secu.getYtm1M());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.THREE_MONTHS.getValue(), e -> new ArrayList<>()).add(secu.getYtm3M());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.SIX_MONTHS.getValue(), e -> new ArrayList<>()).add(secu.getYtm6M());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.NINE_MONTHS.getValue(), e -> new ArrayList<>()).add(secu.getYtm9M());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.ONE_YEAR.getValue(), e -> new ArrayList<>()).add(secu.getYtm1Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.TWO_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm2Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.THREE_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm3Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.FIVE_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm5Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.SEVEN_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm7Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.TEN_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm10Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.FIFTEEN_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm15Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.TWENTY_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm20Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.THIRTY_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm30Y());
                securitiesYtmMap.computeIfAbsent(PeriodEnum.FIFTY_YEARS.getValue(), e -> new ArrayList<>()).add(secu.getYtm50Y());
            });
            Map<Integer, BigDecimal> securitiesAvgMap = Maps.newHashMap();
            securitiesYtmMap.forEach((key, value) -> securitiesAvgMap.put(key, this.safeAvg(value)));
            Map<Integer, PgBondYieldPanoramaAbsDO> absCurveCodeMap =
                    context.getAbsBondYieldPanoramas().stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
            for (Map.Entry<Integer, PgBondYieldPanoramaAbsDO> absEntry : absCurveCodeMap.entrySet()) {
                PgBondYieldPanoramaAbsDO abs = absEntry.getValue();
                PgBondYieldSpreadTraceAbsDO pgBondYieldSpreadTraceAbsDO = new PgBondYieldSpreadTraceAbsDO();
                pgBondYieldSpreadTraceAbsDO.setBondType(context.getBondTypeEnum().getValue());
                pgBondYieldSpreadTraceAbsDO.setChartType(YieldSpreadChartTypeEnum.TERM_SPREAD.getValue());
                pgBondYieldSpreadTraceAbsDO.setCurveCode(absEntry.getKey());
                pgBondYieldSpreadTraceAbsDO.setYtm1M(this.safeSubtract(abs.getYtm1M(), securitiesAvgMap.get(PeriodEnum.ONE_MONTH.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm3M(this.safeSubtract(abs.getYtm3M(), securitiesAvgMap.get(PeriodEnum.THREE_MONTHS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm6M(this.safeSubtract(abs.getYtm6M(), securitiesAvgMap.get(PeriodEnum.SIX_MONTHS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm9M(this.safeSubtract(abs.getYtm9M(), securitiesAvgMap.get(PeriodEnum.NINE_MONTHS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm1Y(this.safeSubtract(abs.getYtm1Y(), securitiesAvgMap.get(PeriodEnum.ONE_YEAR.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm2Y(this.safeSubtract(abs.getYtm2Y(), securitiesAvgMap.get(PeriodEnum.TWO_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm3Y(this.safeSubtract(abs.getYtm3Y(), securitiesAvgMap.get(PeriodEnum.THREE_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm5Y(this.safeSubtract(abs.getYtm5Y(), securitiesAvgMap.get(PeriodEnum.FIVE_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm7Y(this.safeSubtract(abs.getYtm7Y(), securitiesAvgMap.get(PeriodEnum.SEVEN_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm10Y(this.safeSubtract(abs.getYtm10Y(), securitiesAvgMap.get(PeriodEnum.TEN_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm15Y(this.safeSubtract(abs.getYtm15Y(), securitiesAvgMap.get(PeriodEnum.FIFTEEN_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm20Y(this.safeSubtract(abs.getYtm20Y(), securitiesAvgMap.get(PeriodEnum.TWENTY_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm30Y(this.safeSubtract(abs.getYtm30Y(), securitiesAvgMap.get(PeriodEnum.THIRTY_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setYtm50Y(this.safeSubtract(abs.getYtm50Y(), securitiesAvgMap.get(PeriodEnum.FIFTY_YEARS.getValue())));
                pgBondYieldSpreadTraceAbsDO.setIssueDate(context.getIssueDate());
                pgBondYieldSpreadTraceAbsDO.setDeleted(Deleted.NO_DELETED.getValue());
                dataList.add(pgBondYieldSpreadTraceAbsDO);
            }
            return dataList;
        }
        List<PgBondYieldPanoramaAbsDO> generalBankBondYieldPanoramas = context.getGeneralBankBondYieldPanoramas();
        if (CollectionUtils.isEmpty(generalBankBondYieldPanoramas)) {
            return Collections.emptyList();
        }
        Map<Integer, PgBondYieldPanoramaAbsDO> generalBankCurveCodeMap =
                generalBankBondYieldPanoramas.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        Map<Integer, PgBondYieldPanoramaAbsDO> absCurveCodeMap =
                context.getAbsBondYieldPanoramas().stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        for (Map.Entry<Integer, PgBondYieldPanoramaAbsDO> absEntry : absCurveCodeMap.entrySet()) {
            PgBondYieldPanoramaAbsDO abs = absEntry.getValue();
            Integer curveCodeMapping = CURVE_CODE_MAP.getOrDefault(absEntry.getKey(), -1);
            PgBondYieldPanoramaAbsDO generalBankBond = generalBankCurveCodeMap.get(curveCodeMapping);
            if (Objects.isNull(generalBankBond)) {
                continue;
            }
            PgBondYieldSpreadTraceAbsDO pgBondYieldSpreadTraceAbsDO
                    = convertAbsSubtractToTrace(abs, generalBankBond, context.getBondTypeEnum(),
                    context.getIssueDate(), absEntry.getKey(), YieldSpreadChartTypeEnum.TERM_SPREAD);

            dataList.add(pgBondYieldSpreadTraceAbsDO);
        }
        return dataList;
    }

    private BigDecimal safeAvg(List<BigDecimal> bigDecimals) {
        if (CollectionUtils.isEmpty(bigDecimals)) {
            return null;
        }
        int num = bigDecimals.size();
        bigDecimals = bigDecimals.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bigDecimals)) {
            return null;
        }
        return bigDecimals.stream().reduce(BigDecimal::add).map(sum -> sum.divide(BigDecimal.valueOf(num), TWO_SCALE, RoundingMode.HALF_UP)).orElse(null);
    }
}
