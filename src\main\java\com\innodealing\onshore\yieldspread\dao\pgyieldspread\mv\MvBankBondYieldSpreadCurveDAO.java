package com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv;

import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.mapper.pgsharding.mv.MvBankShardBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvBankBondYieldSpreadCurveMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvBankBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 银行利差曲线DAO
 * <AUTHOR>
 */
@Repository
public class MvBankBondYieldSpreadCurveDAO {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MvBankBondYieldSpreadCurveMapper mvBankBondYieldSpreadCurveMapper;

    @Resource
    private MvBankShardBondYieldSpreadCurveMapper mvBankShardBondYieldSpreadCurveMapper;

    /**
     * 创建或刷新银行利差曲线物化视图
     *
     * @param router 路由
     * @param param  刷新参数
     */
    public void createOrRefreshBankCurveMv(AbstractRatingRouter router, RefreshYieldCurveParam param) {
        String tableName = this.getMvName(router);
        logger.info("[refreshMvInduBondYieldSpreadBondCurveRouter]开始创建银行评级分片物化视图:{}", tableName);
        MvBankBondYieldSpreadCurveParameter parameter = this.builderShardMvParam(router);
        parameter.setTableName(tableName);
        if (param.isMvRefresh()) {
            mvBankBondYieldSpreadCurveMapper.dropMv(tableName);
            mvBankBondYieldSpreadCurveMapper.createMvRatingRouter(parameter);
        } else {
            mvBankBondYieldSpreadCurveMapper.refreshMvInduBondYieldSpreadCurve(tableName);
        }
    }

    /**
     * 时间范围不为空，删除临时表
     *
     * @param router 路由
     */
    public void droTempMv(AbstractRatingRouter router) {
        AbstractRatingRouter.SpreadDateRange spreadDateRange = router.getSpreadDateRange();
        if (Objects.nonNull(spreadDateRange)) {
            mvBankBondYieldSpreadCurveMapper.dropMv(this.getMvName(router));
        }
    }

    private MvBankBondYieldSpreadCurveParameter builderShardMvParam(AbstractRatingRouter router) {
        MvBankBondYieldSpreadCurveParameter parameter = BeanCopyUtils.copyProperties(router, MvBankBondYieldSpreadCurveParameter.class);
        parameter.setImpliedRatingMappings(router.getRatings());
        return parameter;
    }


    /**
     * 根据路由获取物化视图名称
     *
     * @param router 路由
     * @return mvName
     */
    public String getMvName(AbstractRatingRouter router) {
        return String.format(RatingCombinationHelper.getTableFormat(router.getClass()),
                mvBankShardBondYieldSpreadCurveMapper.getLogicTable(), RatingCombinationHelper.getMvNameSuffix(router));
    }
}
