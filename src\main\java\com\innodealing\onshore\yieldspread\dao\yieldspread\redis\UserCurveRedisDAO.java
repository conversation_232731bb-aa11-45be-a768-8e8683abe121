package com.innodealing.onshore.yieldspread.dao.yieldspread.redis;

import com.innodealing.commons.encrypt.MD5Utils;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserCurveDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionBO;
import com.innodealing.onshore.yieldspread.model.dto.response.CurveDataResDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RFuture;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 用户曲线RedisDAO
 *
 * <AUTHOR>
 */
@Repository
public class UserCurveRedisDAO {

    private static final String PATTERN = "yyyyMMdd";

    private static final int RANDOM_SECOND_LIMIT = 100;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private UserCurveDAO userCurveDAO;

    /**
     * 判断是否在redis存在了
     *
     * @param curveId 曲线id
     * @return true/false
     */
    public boolean exist(Long curveId) {
        return redissonClient.getBucket(getCurveKey(curveId)).isExists();
    }

    /**
     * 缓存债曲线数据
     *
     * @param curveId   曲线id
     * @param curveData 曲线数据
     */
    public void cacheCurve(Long curveId, List<CurveDataResDTO> curveData) {
        cache(getCurveKey(curveId), curveData);
    }

    /**
     * 获取债券曲线数据
     *
     * @param curveId   曲线id
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @return 曲线数据
     */
    public List<CurveDataResDTO> listCurveData(@NotNull Long curveId, @NotNull Date startDate, @NotNull Date endDate) {
        return listCurveData(getCurveKey(curveId), startDate, endDate);
    }

    /**
     * 获取债券曲线所有数据
     *
     * @param curveId 曲线id
     * @return 曲线所有数据
     */
    public List<CurveDataResDTO> listCurveAllData(@NotNull Long curveId) {
        RScoredSortedSet<CurveDataResDTO> scoredSortedSet = redissonClient.getScoredSortedSet(getCurveKey(curveId));
        Collection<CurveDataResDTO> values = scoredSortedSet.readAll();
        if (CollectionUtils.isNotEmpty(values)) {
            return new ArrayList<>(values);
        }
        return Collections.emptyList();
    }

    /**
     * 移除曲线缓存
     *
     * @param curveId 曲线id
     * @return 操作结果
     */
    public RFuture<Boolean> removeCurveCache(Long curveId) {
        return redissonClient.getSortedSet(getCurveKey(curveId)).deleteAsync();
    }

    private void cache(String key, List<CurveDataResDTO> curveData) {
        if (CollectionUtils.isEmpty(curveData)) {
            return;
        }
        RScoredSortedSet<CurveDataResDTO> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        Map<CurveDataResDTO, Double> values = new HashMap<>(curveData.size());
        curveData.forEach(c -> values.put(c, convertDateToDoubleScore(c.getSpreadDate())));
        scoredSortedSet.addAll(values);
        scoredSortedSet.expire(Duration.ofSeconds(getExpirationTimeSecond()));
    }

    private List<CurveDataResDTO> listCurveData(String key, Date startDate, Date endDate) {
        RScoredSortedSet<CurveDataResDTO> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        Collection<CurveDataResDTO> values = scoredSortedSet
                .valueRange(convertDateToDoubleScore(startDate), true, convertDateToDoubleScore(endDate), true);
        if (CollectionUtils.isNotEmpty(values)) {
            return new ArrayList<>(values);
        }
        return Collections.emptyList();
    }

    /**
     * 把日期转换为数值
     *
     * @param date 日期
     * @return 日期对应的数值分数
     */
    private double convertDateToDoubleScore(@NotNull Date date) {
        LocalDate localDate = date.toLocalDate();
        return Double.parseDouble(localDate.format(DateTimeFormatter.ofPattern(PATTERN)));
    }

    private long getExpirationTimeSecond() {
        LocalDateTime midnight = LocalDateTime.now().plusDays(1).withHour(YieldSpreadCacheConst.EXPIRATION_HOUR).withMinute(0).withSecond(0).withNano(0);
        long expirationTime = ChronoUnit.SECONDS.between(LocalDateTime.now(), midnight);
        return expirationTime + ThreadLocalRandom.current().nextInt(RANDOM_SECOND_LIMIT);
    }

    private String getCurveKey(Long curveId, CurveTypeEnum curveType, String filterConditionJsonStr) {
        if (curveType == CurveTypeEnum.CUSTOMIZATION || curveType == CurveTypeEnum.CB) {
            return String.format(YieldSpreadCacheConst.SPREAD_CURVE_KEY, curveType.getValue(), curveId);
        }
        String md5String = MD5Utils.getMD5String(filterConditionJsonStr);
        return String.format(YieldSpreadCacheConst.SPREAD_CURVE_KEY, curveType.getValue(), md5String);
    }

    private String getCurveKey(Long curveId) {
        CurveDefinitionBO curveBaseData = this.getCurveDefinition(curveId);
        CurveTypeEnum curveType = EnumUtils.getEnum(CurveTypeEnum.class, curveBaseData.getSpreadCurveType());
        return getCurveKey(curveId, curveType, curveBaseData.getFilterCondition());
    }

    private CurveDefinitionBO getCurveDefinition(Long curveId) {
        Optional<CurveDefinitionBO> curveOptional = userCurveDAO.getCurveDefinitionBOContainDeleted(curveId);
        if (!curveOptional.isPresent()) {
            throw new BusinessException("curve(" + curveId + ") doesn't exist");
        }
        return curveOptional.get();
    }

}
