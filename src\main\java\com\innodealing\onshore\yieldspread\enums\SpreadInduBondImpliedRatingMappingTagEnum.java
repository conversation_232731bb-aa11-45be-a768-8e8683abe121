package com.innodealing.onshore.yieldspread.enums;

import java.util.Objects;

/**
 * 行业债券隐含评级映射标签
 *
 * <AUTHOR>
 */
public enum SpreadInduBondImpliedRatingMappingTagEnum implements IBondImpliedRating {
    /**
     * AAA级
     */
    AAA(1, new Integer[]{10, 20, 30}, "AAA级"),
    /**
     * AA级
     */
    AA(2, new Integer[]{40, 50, 60}, "AA级"),
    /**
     * A级
     */
    A(3, new Integer[]{70, 80, 90}, "A级");

    private Integer tag;

    private String text;

    private Integer[] mapping;

    SpreadInduBondImpliedRatingMappingTagEnum(Integer tag, Integer[] mapping, String text) {
        this.tag = tag;
        this.mapping = mapping.clone();
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return tag;
    }

    @Override
    public Integer[] getMapping() {
        return Objects.isNull(mapping) ? new Integer[0] : mapping.clone();
    }

}
