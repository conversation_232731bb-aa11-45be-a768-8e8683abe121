package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.BankComYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.bo.BankComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadChangeBO;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.bo.MixYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.dto.BankYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.BankComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.SPREAD_KEEP_SCALE;

/**
 * 银行主体利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class BankComYieldSpreadDAO {

    @Resource
    private BankComYieldSpreadMapper bankComYieldSpreadMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 批量更新
     *
     * @param spreadDate               利差日期
     * @param bankComYieldSpreadDOList 银行主体利差列表
     * @return 受影响的行数
     */
    public int saveBankComYieldSpreadDOList(Date spreadDate, List<BankComYieldSpreadDO> bankComYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(bankComYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> comUniCodes = bankComYieldSpreadDOList.stream().map(BankComYieldSpreadDO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        AtomicInteger effectRows = new AtomicInteger();
        // 批量查询已存在的数据
        DynamicQuery<BankComYieldSpreadDO> query = DynamicQuery.createQuery(BankComYieldSpreadDO.class)
                .select(BankComYieldSpreadDO::getId, BankComYieldSpreadDO::getComUniCode,
                        BankComYieldSpreadDO::getSpreadDate, BankComYieldSpreadDO::getInduLevel1Code,
                        BankComYieldSpreadDO::getInduLevel1Name, BankComYieldSpreadDO::getInduLevel2Code,
                        BankComYieldSpreadDO::getInduLevel2Name, BankComYieldSpreadDO::getBankType)
                .and(BankComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(BankComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<BankComYieldSpreadDO> existDataList = bankComYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(bankComYieldSpreadDOList));
        } else {
            Map<String, BankComYieldSpreadDO> existBankComYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                    x.getComUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<BankComYieldSpreadDO> insertList = new ArrayList<>();
            List<BankComYieldSpreadDO> updateList = new ArrayList<>();
            for (BankComYieldSpreadDO bankComYieldSpreadDO : bankComYieldSpreadDOList) {
                BankComYieldSpreadDO existBankComYieldSpreadDO = existBankComYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                bankComYieldSpreadDO.getComUniCode(), bankComYieldSpreadDO.getSpreadDate().getTime()));
                if (Objects.isNull(existBankComYieldSpreadDO)) {
                    insertList.add(bankComYieldSpreadDO);
                } else {
                    bankComYieldSpreadDO.setId(existBankComYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existBankComYieldSpreadDO.getInduLevel1Code(), bankComYieldSpreadDO::setInduLevel1Code);
                    ObjectExtensionUtils.ifNonNull(existBankComYieldSpreadDO.getInduLevel1Name(), bankComYieldSpreadDO::setInduLevel1Name);
                    ObjectExtensionUtils.ifNonNull(existBankComYieldSpreadDO.getInduLevel2Code(), bankComYieldSpreadDO::setInduLevel2Code);
                    ObjectExtensionUtils.ifNonNull(existBankComYieldSpreadDO.getInduLevel2Name(), bankComYieldSpreadDO::setInduLevel2Name);
                    ObjectExtensionUtils.ifNonNull(existBankComYieldSpreadDO.getBankType(), bankComYieldSpreadDO::setBankType);
                    updateList.add(bankComYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 银行主体利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<BankComYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<BankComYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(BankComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (BankComYieldSpreadDO bankComYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<BankComYieldSpreadDO> updateQuery = DynamicQuery.createQuery(BankComYieldSpreadDO.class)
                        .and(BankComYieldSpreadDO::getId, isEqual(bankComYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(bankComYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 银行主体利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<BankComYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<BankComYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(BankComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (BankComYieldSpreadDO bankComYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(bankComYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 查询债券主体利差
     *
     * @param spreadDate     利差日期
     * @param comUniCodeList 主体列表
     * @return ComYieldSpreadChangeBO
     */
    public List<ComYieldSpreadChangeBO> listBankComYieldSpreads(Date spreadDate, Set<Long> comUniCodeList) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<BankComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(BankComYieldSpreadDO.class)
                .select(BankComYieldSpreadDO::getComUniCode, BankComYieldSpreadDO::getSpreadDate,
                        BankComYieldSpreadDO::getComCreditSpread, BankComYieldSpreadDO::getComExcessSpread)
                .and(BankComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(CollectionUtils.isNotEmpty(comUniCodeList),
                        BankComYieldSpreadDO::getComUniCode, in(comUniCodeList));
        return BeanCopyUtils.copyList(bankComYieldSpreadMapper.selectByDynamicQuery(groupQuery), ComYieldSpreadChangeBO.class);
    }


    /**
     * 获取银行主体利差某一天的 所有 主题
     *
     * @param spreadDate 利差日期
     * @return comUniCodeList 主体列表
     */
    public List<Long> listBankComYieldSpreadComUniCodes(@NonNull Date spreadDate) {
        DynamicQuery<BankComYieldSpreadDO> query = DynamicQuery.createQuery(BankComYieldSpreadDO.class)
                .select(BankComYieldSpreadDO::getComUniCode)
                .and(BankComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(BankComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return bankComYieldSpreadMapper.selectByDynamicQuery(query).stream().map(BankComYieldSpreadDO::getComUniCode).collect(Collectors.toList());
    }

    /**
     * 获取历史分位的统计数据
     *
     * @param startDate      时间范围 开始时间
     * @param endDate        时间范围结束时间
     * @param issueDate      利差日期
     * @param comUniCodeList 主体列表
     * @return 分位统计数据
     */
    public List<ComYieldSpreadQuantileViewDO> listBankComYieldQuantileStatistics(@NonNull Date startDate, @NonNull Date endDate,
                                                                                 @NonNull Date issueDate, List<Long> comUniCodeList) {
        return bankComYieldSpreadMapper.listComYieldQuantileStatisticsViews(startDate, endDate, issueDate, comUniCodeList);
    }

    /**
     * 获取最新利差日期
     *
     * @return 最新利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<BankComYieldSpreadDO> query = DynamicQuery.createQuery(BankComYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt = bankComYieldSpreadMapper.selectMaxByDynamicQuery(BankComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }


    /**
     * 从主库获取最新利差日期
     *
     * @return 最新利差日期
     */
    public Optional<Date> getMaxSpreadDateForMaster() {
        DynamicQuery<BankComYieldSpreadDO> query = DynamicQuery.createQuery(BankComYieldSpreadDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER);
        Optional<java.util.Date> dateOpt = bankComYieldSpreadMapper.selectMaxByDynamicQuery(BankComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 获取主体利差
     *
     * @param isNewest 是否为最新一天
     * @param param    请求参数
     * @return 主体利差
     */
    public List<BankComYieldSpreadBO> listComYieldSpreads(boolean isNewest, BankYieldSearchParam param) {
        return bankComYieldSpreadMapper.listComYieldSpreads(isNewest, param);
    }

    /**
     * 获取主体数量
     *
     * @param param 请求参数
     * @return 主体数量
     */
    public Long countComYieldSpread(BankYieldSearchParam param) {
        return bankComYieldSpreadMapper.countComYieldSpread(param);
    }


    /**
     * 查询主体利差数据集合
     *
     * @param spreadDate  利差日期
     * @param comUniCodes 主体唯一编码集合
     * @return {@link List}<{@link BankComYieldSpreadDO}>
     */
    public List<BankComYieldSpreadDO> listComYieldSpreads(@NonNull Date spreadDate, @NonNull Collection<Long> comUniCodes) {
        DynamicQuery<BankComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(BankComYieldSpreadDO.class)
                .and(BankComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(BankComYieldSpreadDO::getComUniCode, in(comUniCodes));
        return BeanCopyUtils.copyList(bankComYieldSpreadMapper.selectByDynamicQuery(groupQuery), BankComYieldSpreadDO.class);
    }

    /**
     * 查询银行主体利差数据集
     *
     * @param spreadDate 利差日期
     * @return {@link List}<{@link ComYieldSpreadShortBO}> 城投主体利差响应数据集
     */
    public List<ComYieldSpreadShortBO> listShortInfosBySpreadDate(Date spreadDate) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<BankComYieldSpreadDO> query = DynamicQuery.createQuery(BankComYieldSpreadDO.class)
                .select(BankComYieldSpreadDO::getSpreadDate,
                        BankComYieldSpreadDO::getComUniCode,
                        BankComYieldSpreadDO::getComCreditSpread,
                        BankComYieldSpreadDO::getComExcessSpread,
                        BankComYieldSpreadDO::getComCbYield)
                .and(BankComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(BankComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return BeanCopyUtils.copyList(bankComYieldSpreadMapper.selectByDynamicQuery(query), ComYieldSpreadShortBO.class);
    }


    /**
     * 获取主体利差
     *
     * @param comUniCodes 主体列表
     * @return 主体利差列表
     */
    public List<MixYieldSpreadShortBO> listAllYieldSpreads(List<Long> comUniCodes) {
        if (!ObjectExtensionUtils.isAllNotEmpty(comUniCodes)) {
            return Collections.emptyList();
        }
        DynamicQuery<BankComYieldSpreadDO> query = DynamicQuery.createQuery(BankComYieldSpreadDO.class)
                .select(BankComYieldSpreadDO::getSpreadDate, BankComYieldSpreadDO::getComUniCode,
                        BankComYieldSpreadDO::getComCreditSpread, BankComYieldSpreadDO::getComExcessSpread,
                        BankComYieldSpreadDO::getComSeniorCreditSpread, BankComYieldSpreadDO::getComSeniorExcessSpread,
                        BankComYieldSpreadDO::getComTier2CreditSpread, BankComYieldSpreadDO::getComTier2ExcessSpread,
                        BankComYieldSpreadDO::getComPerpetualCreditSpread, BankComYieldSpreadDO::getComPerpetualExcessSpread)
                .and(BankComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<BankComYieldSpreadDO> comYieldSpreadList = bankComYieldSpreadMapper.selectByDynamicQuery(query);
        return comYieldSpreadList.stream().map(com -> {
            MixYieldSpreadShortBO mixYieldSpreadShortBO = new MixYieldSpreadShortBO();
            mixYieldSpreadShortBO.setComUniCode(com.getComUniCode());
            mixYieldSpreadShortBO.setSpreadDate(com.getSpreadDate());
            handlePrecision(mixYieldSpreadShortBO, com);
            return mixYieldSpreadShortBO;
        }).collect(Collectors.toList());
    }

    private void handlePrecision(MixYieldSpreadShortBO mixYieldSpreadShortBO, BankComYieldSpreadDO comYieldSpreadDO) {
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setComCreditSpread);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setComExcessSpread);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComSeniorCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldOne);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComSeniorExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldOne);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComTier2CreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldTwo);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComTier2ExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldTwo);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPerpetualCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldTree);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPerpetualExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldTree);
    }
}
