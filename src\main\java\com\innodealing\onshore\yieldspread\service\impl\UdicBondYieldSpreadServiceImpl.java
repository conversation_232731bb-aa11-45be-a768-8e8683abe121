package com.innodealing.onshore.yieldspread.service.impl;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.lambda.GetPropertyFunction;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.constant.RedisKeys;
import com.innodealing.onshore.bondmetadata.dto.area.AreaInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationShortInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondImpliedRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComYyRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.udic.UdicComInfoForSpreadDTO;
import com.innodealing.onshore.bondmetadata.dto.yieldspread.AreaYieldSpreadDTO;
import com.innodealing.onshore.bondmetadata.enums.AreaTypeEnum;
import com.innodealing.onshore.bondmetadata.enums.EmbeddedOption;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.dao.dmdc.BondInterestCtzDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgUdicBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvUdicBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvUdicBondYieldSpreadPanoramaDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition.PgUdicBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.UdicBondYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UdicAreaYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UdicBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UdicComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.*;
import com.innodealing.onshore.yieldspread.model.bo.*;
import com.innodealing.onshore.yieldspread.model.dto.*;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.BondInterestCtzDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgUdicBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicAreaYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.factory.YyRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.YyRatingRouter;
import com.innodealing.onshore.yieldspread.service.RedisService;
import com.innodealing.onshore.yieldspread.service.UdicBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.YieldSpreadCommonService;
import com.innodealing.onshore.yieldspread.service.internal.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.github.wz2cool.dynamic.helper.CommonsHelper.getPropertyName;
import static com.innodealing.commons.object.ObjectExtensionUtils.isAllEmpty;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.*;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.subtract;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.*;

/**
 * 城投债利差 Service
 *
 * <AUTHOR>
 **/
@SuppressWarnings({"squid:S1854", "squid:S1481", "java:S107", "squid:S00107"})
@Service
public class UdicBondYieldSpreadServiceImpl extends AbstractBondCurveService implements UdicBondYieldSpreadService {

    private final ExecutorService executorService;

    private final ExecutorService shardExecutorService;

    protected UdicBondYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("UdicBondYieldSpreadServiceImpl-pool-").build());

        shardExecutorService = TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(SHARD_WORK_THREAD_NUM, SHARD_WORK_THREAD_NUM, 0,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("ShardUdicBondYieldSpreadServiceImpl-pool-").build()));
    }

    private static final SortDTO DEFAULT_SORT;

    private static final Comparator<UdicSpreadPanoramaResponseDTO> DEFAULT_COMPARATOR;

    private static final Map<String, Map<SortDirection, Comparator<UdicSpreadPanoramaResponseDTO>>> COMPARATOR_MAP;

    private static final Comparator<BigDecimal> NULLS_LAST = Comparator.nullsLast(BigDecimal::compareTo);

    private static final Comparator<BigDecimal> NULLS_FIRST = Comparator.nullsFirst(BigDecimal::compareTo);

    private static final Set<String> HISTORY_ORDER_FIELD_SET;
    /**
     * 不同口径的城投表名集合
     */
    private static final Set<String> UDIC_CALIBER_TABLE_NAME_SET;

    @Resource
    private UdicBondYieldSpreadDAO udicBondYieldSpreadDAO;

    @Resource
    private PgUdicBondYieldSpreadDAO pgUdicBondYieldSpreadDAO;

    @Resource
    private MvUdicBondYieldSpreadPanoramaDAO mvUdicBondYieldSpreadPanoramaDAO;

    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private BondRatingService bondRatingService;

    @Resource
    private RedisService redisService;

    @Resource
    private UdicInfoService udicInfoService;

    @Resource
    private BondInterestCtzDAO bondInterestCtzDAO;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private PgUdicBondYieldSpreadCurveDAO pgUdicBondYieldSpreadCurveDAO;

    @Resource
    private MvUdicBondYieldSpreadCurveDAO mvUdicBondYieldSpreadCurveDAO;

    @Resource
    private UdicComYieldSpreadDAO udicComYieldSpreadDAO;

    @Value("${sharding.yield.spread}")
    private Date initStartDate;

    @Resource
    private YyRatingRouterFactory yyRatingRouterFactory;

    @Resource
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;

    @Resource
    private UdicBondYieldSpreadRedisDAO udicBondYieldSpreadRedisDAO;

    @Resource
    private HolidayService holidayService;

    @Resource
    private UserService userService;

    @Resource
    private UdicAreaYieldSpreadDAO udicAreaYieldSpreadDAO;

    static {
        COMPARATOR_MAP = Maps.newConcurrentMap();
        HISTORY_ORDER_FIELD_SET = new HashSet<>();
        DEFAULT_COMPARATOR = Comparator.comparing(UdicSpreadPanoramaResponseDTO::getBondCreditSpread, NULLS_FIRST.reversed());
        COMPARATOR_MAP.put(getPropertyName(UdicSpreadPanoramaResponseDTO::getBondCreditSpread),
                buildComparatorMap(UdicSpreadPanoramaResponseDTO::getBondCreditSpread));
        COMPARATOR_MAP.put(getPropertyName(UdicSpreadPanoramaResponseDTO::getBondCreditSpreadChange90),
                buildComparatorMap(UdicSpreadPanoramaResponseDTO::getBondCreditSpreadChange90));
        COMPARATOR_MAP.put(getPropertyName(UdicSpreadPanoramaResponseDTO::getBondCreditSpreadChange180),
                buildComparatorMap(UdicSpreadPanoramaResponseDTO::getBondCreditSpreadChange180));
        COMPARATOR_MAP.put(getPropertyName(UdicSpreadPanoramaResponseDTO::getBondExcessSpread),
                buildComparatorMap(UdicSpreadPanoramaResponseDTO::getBondExcessSpread));
        COMPARATOR_MAP.put(getPropertyName(UdicSpreadPanoramaResponseDTO::getBondExcessSpreadChange90),
                buildComparatorMap(UdicSpreadPanoramaResponseDTO::getBondExcessSpreadChange90));
        COMPARATOR_MAP.put(getPropertyName(UdicSpreadPanoramaResponseDTO::getBondExcessSpreadChange180),
                buildComparatorMap(UdicSpreadPanoramaResponseDTO::getBondExcessSpreadChange180));
        DEFAULT_SORT = new SortDTO(getPropertyName(UdicSpreadPanoramaResponseDTO::getBondCreditSpread), SortDirection.DESC);
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicBondYieldSpreadResponseDTO::getBondCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(UdicBondYieldSpreadResponseDTO::getBondExcessSpread));

        UDIC_CALIBER_TABLE_NAME_SET = new HashSet<>();
        UDIC_CALIBER_TABLE_NAME_SET.add(UDIC_TABLE_NAME);
        UDIC_CALIBER_TABLE_NAME_SET.add(UDIC_DM_CALIBER_TABLE_NAME);
    }

    private static Map<SortDirection, Comparator<UdicSpreadPanoramaResponseDTO>> buildComparatorMap(
            GetPropertyFunction<UdicSpreadPanoramaResponseDTO, BigDecimal> function) {
        Map<SortDirection, Comparator<UdicSpreadPanoramaResponseDTO>> comparatorMap = Maps.newHashMap();
        comparatorMap.put(SortDirection.ASC, Comparator.comparing(function, NULLS_LAST));
        comparatorMap.put(SortDirection.DESC, Comparator.comparing(function, NULLS_FIRST.reversed()));
        return comparatorMap;
    }

    /**
     * 检查分片表
     */
    //@Scheduled(initialDelay = 1000, fixedDelay = 3_600_000)
    public void checkShardingTables() {
        Date currentDate = new Date(System.currentTimeMillis());
        Date endDate = new Date(DateUtils.addDays(currentDate, 1).getTime());
        createShardingTables(initStartDate, endDate);
    }

    private void createShardingTables(Date startDate, Date endDate) {
        UDIC_CALIBER_TABLE_NAME_SET.forEach(tableName -> {
            Collection<String> shardingTableNames = ShardingUtils.getYearShardingTableNames(tableName, startDate, endDate);
            for (String shardingTableName : shardingTableNames) {
                udicBondYieldSpreadDAO.createShardingTable(shardingTableName);
            }
        });
    }

    @Override
    public int calcUdicBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs,
                                                    Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                                    Date spreadDate, Boolean isEnableOldData) {
        if (CollectionUtils.isEmpty(onshoreBondInfoDTOs)) {
            return 0;
        }
        Set<Long> bondUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> comUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        // 获取主体信息
        CompletableFuture<List<UdicComInfoForSpreadDTO>> submitUdicComInfoForSpreadDTO = of.submit(() ->
                udicInfoService.listComInfoForSpreadDTOs(new ArrayList<>(comUniCodes)));
        // 获取中债估值信息
        CompletableFuture<Map<Long, CbValuationShortInfoResponseDTO>> submitCbValuationShortInfo = of.submit(() ->
                bondPriceService.getCbValuationShortInfoMap(spreadDate, bondUniCodes));
        // 获取评级信息
        CompletableFuture<Map<Long, BondImpliedRatingDTO>> submitBondImpliedRatingDTO = of.submit(() ->
                bondRatingService.getBondImpliedRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, BondExternalCreditRatingDTO>> submitBondExternalCreditRatingDTO = of.submit(() ->
                bondRatingService.getBondExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, ComYyRatingDTO>> submitComYyRatingDTO = of.submit(() ->
                bondRatingService.getComYyRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        CompletableFuture<Map<Long, ComExternalCreditRatingDTO>> submitComExternalCreditRatingDTO = of.submit(() ->
                bondRatingService.getComExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        CompletableFuture<List<BondInterestCtzDO>> submitbondInterestCtzDAO = of.submit(() ->
                bondInterestCtzDAO.listBondInterestCtzDOByInterestDate(spreadDate, bondUniCodes));

        of.doWorks(submitUdicComInfoForSpreadDTO, submitCbValuationShortInfo, submitBondImpliedRatingDTO, submitBondExternalCreditRatingDTO,
                submitComYyRatingDTO, submitComExternalCreditRatingDTO, submitbondInterestCtzDAO);
        return parseUdicBondYieldSpreadDO(onshoreBondInfoDTOs, submitUdicComInfoForSpreadDTO.join(), submitCbValuationShortInfo.join(),
                submitBondImpliedRatingDTO.join(), submitBondExternalCreditRatingDTO.join(),
                submitComExternalCreditRatingDTO.join(), submitComYyRatingDTO.join(),
                submitbondInterestCtzDAO.join(), bondYieldCurveMap, spreadDate, isEnableOldData);
    }

    private int parseUdicBondYieldSpreadDO(List<OnshoreBondInfoDTO> onshoreBondInfos,
                                           List<UdicComInfoForSpreadDTO> listComInfoForSpreadDTOs,
                                           Map<Long, CbValuationShortInfoResponseDTO> cbMap,
                                           Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap,
                                           Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                           Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap,
                                           Map<Long, ComYyRatingDTO> comYyRatingMap,
                                           List<BondInterestCtzDO> bondInterestCtzDOList,
                                           Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                           Date spreadDate, Boolean isEnableOldData) {
        List<UdicBondYieldSpreadDO> udicBondYieldSpreadDOs = new ArrayList<>();
        Set<Long> areaUniCodes = listComInfoForSpreadDTOs.stream().map(UdicComInfoForSpreadDTO::getAreaUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Long, BondInterestCtzDO> bondInterestCtzDOMap = bondInterestCtzDOList.stream()
                .collect(Collectors.toMap(BondInterestCtzDO::getBondUniCode, Function.identity(), (x1, x2) -> x2));
        Map<Long, AreaInfoResponseDTO> areaInfoResponseDTOMap = areaService.getAreaInfoMap(areaUniCodes);
        List<YieldSpreadBondDO> yieldSpreadBonds = Lists.newArrayList();
        for (OnshoreBondInfoDTO onshoreBondInfoDTO : onshoreBondInfos) {
            UdicBondYieldSpreadDO udicBondYieldSpreadDO = BeanCopyUtils.copyProperties(onshoreBondInfoDTO, UdicBondYieldSpreadDO.class);
            udicBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getPublicOffering());
            udicBondYieldSpreadDO.setSpreadDate(spreadDate);
            if (!DateExtensionUtils.isSameDay(spreadDate, YieldSpreadHelper.getYesterDay())) {
                udicBondYieldSpreadDO.setLatestCouponRate(null);
                udicBondYieldSpreadDO.setBondBalance(null);
            }
            if (Objects.equals(onshoreBondInfoDTO.getEmbeddedOption(), EmbeddedOption.PERPETUAL.getValue())) {
                udicBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getEmbeddedOption());
            }
            // 利差剩余期限标签
            udicBondYieldSpreadDO.setSpreadRemainingTenorTag(YieldSpreadHelper.
                    getSpreadRemainingTenorTag(udicBondYieldSpreadDO.getRemainingTenorDay()));
            udicBondYieldSpreadDO = fillUdicComInfoColumn(udicBondYieldSpreadDO, listComInfoForSpreadDTOs, areaInfoResponseDTOMap);
            udicBondYieldSpreadDO = fillRatingColumn(udicBondYieldSpreadDO, bondImpliedRatingMap, bondExternalCreditRatingMap,
                    comExternalCreditRatingMap, comYyRatingMap);
            udicBondYieldSpreadDO = fillCbColumn(udicBondYieldSpreadDO, cbMap);
            udicBondYieldSpreadDO = fillLerpYieldColumn(udicBondYieldSpreadDO, bondYieldCurveMap);
            udicBondYieldSpreadDO = fillSpreadColumn(udicBondYieldSpreadDO);
            if (isEnableOldData) {
                udicBondYieldSpreadDO = fillOldColumn(udicBondYieldSpreadDO, bondInterestCtzDOMap);
            }
            //这里如果超额利差和信用利差都为空的情况下 过滤掉
            if (Objects.nonNull(udicBondYieldSpreadDO.getBondCreditSpread()) ||
                    Objects.nonNull(udicBondYieldSpreadDO.getBondExcessSpread())) {
                udicBondYieldSpreadDO.setId(redisService.generatePk(RedisKeys.YIELD_SPREAD_UDIC_BOND_YIELD_SPREAD_FLOW_ID,
                        udicBondYieldSpreadDO.getSpreadDate()));
                udicBondYieldSpreadDO.setDeleted(0);
                udicBondYieldSpreadDOs.add(udicBondYieldSpreadDO);
                YieldSpreadBondDO yieldSpreadBondDO = com.innodealing.commons.object.BeanCopyUtils.copyProperties(udicBondYieldSpreadDO, YieldSpreadBondDO.class);
                yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.UDIC.getValue());
                yieldSpreadBonds.add(yieldSpreadBondDO);
            }
        }
        udicBondYieldSpreadDAO.saveUdicBondYieldSpreadDOList(spreadDate, spreadDate, udicBondYieldSpreadDOs);
        if (StringUtils.isBlank(ShardingHindStrParamUtil.getHindStrParam())) {
            //默认 逻辑
            super.saveYieldSpreadBonds(yieldSpreadBonds);
        }

        return savePgUdicBondYieldSpreadDOLists(spreadDate, udicBondYieldSpreadDOs);
    }

    private int savePgUdicBondYieldSpreadDOLists(Date spreadDate, List<UdicBondYieldSpreadDO> udicBondYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(udicBondYieldSpreadDOs)) {
            return 0;
        }
        List<PgUdicBondYieldSpreadDO> pgUdicBondYieldSpreadDOs = udicBondYieldSpreadDOs.stream().map(x -> {
            PgUdicBondYieldSpreadDO result = BeanCopyUtils.copyProperties(x, PgUdicBondYieldSpreadDO.class);
            if (Objects.isNull(result.getGuaranteedStatus())) {
                // gp表的担保字段 null 转换为0
                result.setGuaranteedStatus(0);
            }
            if (Objects.nonNull(result.getBondImpliedRatingMapping())) {
                result.setBondImpliedRatingMappingTag(getBondImpliedRatingMappingTagMap().get(result.getBondImpliedRatingMapping()));
            }
            if (Objects.nonNull(result.getComYyRatingMapping())) {
                result.setComYyRatingMappingTag(getUdicComYyRatingMappingTagMap().get(result.getComYyRatingMapping()));
            }
            if (Objects.isNull(result.getBondImpliedRatingMappingTag())) {
                result.setBondImpliedRatingMappingTag(BondImpliedRatingMappingTagTypeEnum.OTHER.getValue());
            }
            if (Objects.isNull(result.getComYyRatingMappingTag())) {
                result.setComYyRatingMappingTag(ComYyRatingMappingTagTypeEnum.OTHER.getValue());
            }
            return result;
        }).collect(Collectors.toList());
        return pgUdicBondYieldSpreadDAO.savePgUdicBondYieldSpreadDOList(spreadDate, pgUdicBondYieldSpreadDOs);
    }

    /**
     * 填充城投相关信息
     *
     * @param udicBondYieldSpreadDO    城投债利差
     * @param listComInfoForSpreadDTOs 城投主体信息
     * @param listComInfoForSpreadDTOs 区域信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private UdicBondYieldSpreadDO fillUdicComInfoColumn(UdicBondYieldSpreadDO udicBondYieldSpreadDO,
                                                        List<UdicComInfoForSpreadDTO> listComInfoForSpreadDTOs,
                                                        Map<Long, AreaInfoResponseDTO> areaInfoResponseDTOMap) {
        UdicBondYieldSpreadDO result = BeanCopyUtils.copyProperties(udicBondYieldSpreadDO, UdicBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(listComInfoForSpreadDTOs)) {
            return result;
        }
        Map<Long, UdicComInfoForSpreadDTO> udicComInfoForSpreadDTOMap = listComInfoForSpreadDTOs.stream()
                .collect(Collectors.toMap(UdicComInfoForSpreadDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
        UdicComInfoForSpreadDTO udicComInfoForSpreadDTO = udicComInfoForSpreadDTOMap.get(result.getComUniCode());
        if (Objects.nonNull(udicComInfoForSpreadDTO)) {
            Integer administrativeRegion = udicComInfoForSpreadDTO.getAdministrativeRegion();
            if (Objects.nonNull(administrativeRegion)) {
                result.setAdministrativeRegion(administrativeRegion);
                result.setAdministrativeDivision(YieldSpreadHelper.getAdministrativeDivisionMap().get(administrativeRegion));
            }
            AreaInfoResponseDTO areaInfoResponseDTO = areaInfoResponseDTOMap.get(udicComInfoForSpreadDTO.getAreaUniCode());
            if (Objects.nonNull(areaInfoResponseDTO)) {
                result.setCityName(areaInfoResponseDTO.getCityName());
                result.setCityUniCode(areaInfoResponseDTO.getCityUniCode());
                result.setProvinceName(areaInfoResponseDTO.getProvinceName());
                result.setProvinceUniCode(areaInfoResponseDTO.getProvinceUniCode());
                result.setDistrictName(areaInfoResponseDTO.getDistrictName());
                result.setDistrictUniCode(areaInfoResponseDTO.getDistrictUniCode());
            }
        }
        return result;
    }

    /**
     * 填充中债估值相关信息
     *
     * @param udicBondYieldSpreadDO 城投债利差
     * @param cbMap                 中债估值
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private UdicBondYieldSpreadDO fillCbColumn(UdicBondYieldSpreadDO udicBondYieldSpreadDO,
                                               Map<Long, CbValuationShortInfoResponseDTO> cbMap) {
        UdicBondYieldSpreadDO result = BeanCopyUtils.copyProperties(udicBondYieldSpreadDO, UdicBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(cbMap)) {
            return result;
        }
        CbValuationShortInfoResponseDTO cb = cbMap.get(udicBondYieldSpreadDO.getBondUniCode());
        if (Objects.nonNull(cb)) {
            result.setCbYield(cb.getYield());
        }
        return result;
    }

    /**
     * 填充评级相关信息
     *
     * @param udicBondYieldSpreadDO       城投债利差
     * @param bondImpliedRatingMap        债券隐含评级
     * @param bondExternalCreditRatingMap 主体外部评级
     * @param comExternalCreditRatingMap  主体YY评级
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private UdicBondYieldSpreadDO fillRatingColumn(UdicBondYieldSpreadDO udicBondYieldSpreadDO,
                                                   Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap,
                                                   Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                                   Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap,
                                                   Map<Long, ComYyRatingDTO> comYyRatingMap) {
        UdicBondYieldSpreadDO result = BeanCopyUtils.copyProperties(udicBondYieldSpreadDO, UdicBondYieldSpreadDO.class);
        if (ObjectUtils.isNotEmpty(bondImpliedRatingMap)) {
            BondImpliedRatingDTO bondImpliedRatingDTO = bondImpliedRatingMap.get(udicBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondImpliedRatingDTO)) {
                result.setBondImpliedRatingMapping(bondImpliedRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(bondExternalCreditRatingMap)) {
            BondExternalCreditRatingDTO bondExternalCreditRatingDTO = bondExternalCreditRatingMap.
                    get(udicBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondExternalCreditRatingDTO)) {
                result.setBondExtRatingMapping(bondExternalCreditRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(comExternalCreditRatingMap)) {
            ComExternalCreditRatingDTO comExternalCreditRatingDTO = comExternalCreditRatingMap.get(udicBondYieldSpreadDO.getComUniCode());
            if (Objects.nonNull(comExternalCreditRatingDTO)) {
                result.setComExtRatingMapping(comExternalCreditRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(comYyRatingMap)) {
            ComYyRatingDTO comYyRatingDTO = comYyRatingMap.get(udicBondYieldSpreadDO.getComUniCode());
            if (Objects.nonNull(comYyRatingDTO)) {
                result.setComYyRatingMapping(comYyRatingDTO.getRatingMapping());
            }
        }
        return result;
    }

    /**
     * 填充插值收益率
     *
     * @param udicBondYieldSpreadDO 产业债利差
     * @param bondYieldCurveMap     收益率曲线信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private UdicBondYieldSpreadDO fillLerpYieldColumn(UdicBondYieldSpreadDO udicBondYieldSpreadDO,
                                                      Map<Integer, BondYieldCurveDTO> bondYieldCurveMap) {
        UdicBondYieldSpreadDO result = BeanCopyUtils.copyProperties(udicBondYieldSpreadDO, UdicBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(bondYieldCurveMap)) {
            return result;
        }
        //国开插值收益率;单位(%)
        BondYieldCurveDTO bondYieldCurveDTO = bondYieldCurveMap.get(YieldSpreadHelper.CDB_YIELD_CURVE);
        if (Objects.nonNull(bondYieldCurveDTO)) {
            BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(bondYieldCurveDTO, BondYieldCurveBO.class);
            result.setCdbLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
        }
        //隐含评级对应曲线插值收益率;单位(%)
        if (Objects.nonNull(result.getBondImpliedRatingMapping())) {
            BondYieldCurveDTO impliedRatingLerpYieldCurveDTO = bondYieldCurveMap
                    .get(YieldSpreadHelper.getBondYieldCurveUdicMap().get(result.getBondImpliedRatingMapping()));
            if (Objects.nonNull(impliedRatingLerpYieldCurveDTO)) {
                BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(impliedRatingLerpYieldCurveDTO, BondYieldCurveBO.class);
                result.setImpliedRatingLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
            }
        }
        return result;
    }

    /**
     * 填充利差数据
     *
     * @param udicBondYieldSpreadDO 产业债利差
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicBondYieldSpreadDO
     * <AUTHOR>
     */
    private UdicBondYieldSpreadDO fillSpreadColumn(UdicBondYieldSpreadDO udicBondYieldSpreadDO) {
        UdicBondYieldSpreadDO result = BeanCopyUtils.copyProperties(udicBondYieldSpreadDO, UdicBondYieldSpreadDO.class);
        if (Objects.nonNull(result.getCbYield())) {
            if (Objects.nonNull(result.getCdbLerpYield())
                    && result.getCdbLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondCreditSpread(result.getCbYield()
                        .subtract(result.getCdbLerpYield())
                        .multiply(YieldSpreadHelper.BP_WEIGHT).setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.nonNull(result.getImpliedRatingLerpYield()) &&
                    result.getImpliedRatingLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondExcessSpread(result.getCbYield()
                        .subtract(result.getImpliedRatingLerpYield())
                        .multiply(YieldSpreadHelper.BP_WEIGHT).setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                result.setExcessSpreadStatus(0);
            } else {
                result.setExcessSpreadStatus(1);
            }
        }
        return result;
    }

    private UdicBondYieldSpreadDO fillOldColumn(UdicBondYieldSpreadDO udicBondYieldSpreadDO,
                                                Map<Long, BondInterestCtzDO> bondInterestCtzDOMap) {
        UdicBondYieldSpreadDO result = BeanCopyUtils.copyProperties(udicBondYieldSpreadDO, UdicBondYieldSpreadDO.class);
        if (MapUtils.isEmpty(bondInterestCtzDOMap)) {
            return result;
        }
        BondInterestCtzDO bondInterestCtzDO = bondInterestCtzDOMap.get(result.getBondUniCode());
        if (Objects.nonNull(bondInterestCtzDO)) {
            result.setProvinceUniCode(bondInterestCtzDO.getAreaUniCode1());
            result.setProvinceName(bondInterestCtzDO.getAreaName1());
            result.setCityUniCode(bondInterestCtzDO.getAreaUniCode2());
            result.setCityName(bondInterestCtzDO.getAreaName2());
            result.setAdministrativeRegion(bondInterestCtzDO.getAreaLevelId());
            // 这里原来的字段没有 所以我们通过  AdministrativeRegion 字段映射
            if (Objects.nonNull(result.getAdministrativeRegion())) {
                result.setAdministrativeDivision(YieldSpreadHelper.getAdministrativeDivisionMap().get(result.getAdministrativeRegion()));
            }
        }
        return result;
    }

    @Override
    public Optional<InduPanoramaDTO> getInduPanorama(InduPanoramaRequestDTO req) {
        Date spreadDate = req.getSpreadDate();
        if (Objects.isNull(spreadDate) || spreadDate.toLocalDate().compareTo(LocalDate.now()) == 0) {
            spreadDate = this.getMaxSpreadDate();
        }
        UdicCurveCompositionConditionDTO compositionConditionDTO = BeanCopyUtils.copyProperties(req, UdicCurveCompositionConditionDTO.class);
        UdicBondYieldSpreadParamDTO searchParameter = UdicBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now())).build();
        // 1. 根据所传日期计算3个月前、6个月前的日期,需要过滤掉假期，如果是假期，则往前推最近的工作日
        SpreadDateDTO spreadDateDTO = yieldSpreadCommonService.getSpreadDate(spreadDate);
        // 2. 查询物化视图数据，从物化视图中查询数据
        Optional<InduSpreadPanoramaBO> induSpread = pgUdicBondYieldSpreadCurveDAO.getInduBondYieldSpreadPanorama(searchParameter);
        if (!induSpread.isPresent()) {
            return Optional.empty();
        }
        // 3. 查询3个月前物化视图数据
        UdicBondYieldSpreadParamDTO before90SearchParameter = UdicBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spreadDateOrDefault(spreadDateDTO.getBefore90Date(), Date.valueOf(LocalDate.now())).build();
        Optional<InduSpreadPanoramaBO> before90InduSpread = pgUdicBondYieldSpreadCurveDAO.getInduBondYieldSpreadPanorama(before90SearchParameter);
        // 4. 查询6个月前物化视图数据
        UdicBondYieldSpreadParamDTO before180SearchParameter = UdicBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spreadDateOrDefault(spreadDateDTO.getBefore180Date(), Date.valueOf(LocalDate.now())).build();
        Optional<InduSpreadPanoramaBO> before180IndSpread = pgUdicBondYieldSpreadCurveDAO.getInduBondYieldSpreadPanorama(before180SearchParameter);
        // 5. 转为响应DTO
        return Optional.of(toInduPanoramaDTO(spreadDate, induSpread.get(),
                before90InduSpread.orElse(null), before180IndSpread.orElse(null)));
    }

    @Override
    public List<InduPanoramaExportExcelDTO> listInduPanoramaExcels(InduPanoramaRequestDTO request, Date startDate, Date endDate) {
        // 获取三月，六月日期集合
        Map<Date, SpreadDateDTO> spreadDateMap = yieldSpreadCommonService.getSpreadDateMap(startDate, endDate);
        Date before90StartDate = spreadDateMap.get(startDate).getBefore90Date();
        Date before90EndDate = spreadDateMap.get(endDate).getBefore90Date();
        Date before180StartDate = spreadDateMap.get(startDate).getBefore180Date();
        Date before180EndDate = spreadDateMap.get(endDate).getBefore180Date();
        UdicCurveCompositionConditionDTO compositionConditionDTO = BeanCopyUtils.copyProperties(request, UdicCurveCompositionConditionDTO.class);
        UdicBondYieldSpreadParamDTO parameter = UdicBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spanSpreadDate(startDate, endDate).build();
        List<InduSpreadPanoramaBO> induPanoramaList = pgUdicBondYieldSpreadCurveDAO.listInduBondYieldSpreadPanoramas(parameter);
        UdicBondYieldSpreadParamDTO before90Parameter = UdicBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spanSpreadDate(before90StartDate, before90EndDate).build();
        List<InduSpreadPanoramaBO> before90InduPanoramaList = pgUdicBondYieldSpreadCurveDAO.listInduBondYieldSpreadPanoramas(before90Parameter);
        UdicBondYieldSpreadParamDTO before180Parameter = UdicBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spanSpreadDate(before180StartDate, before180EndDate).build();
        List<InduSpreadPanoramaBO> before180InduPanoramaList = pgUdicBondYieldSpreadCurveDAO.listInduBondYieldSpreadPanoramas(before180Parameter);
        if (CollectionUtils.isEmpty(induPanoramaList)) {
            return Collections.emptyList();
        }
        Map<Date, InduSpreadPanoramaBO> induDateMap = induPanoramaList.stream().collect(Collectors.toMap(InduSpreadPanoramaBO::getSpreadDate, Function.identity(), (o, v) -> o));
        Map<Date, InduSpreadPanoramaBO> before90InduDateMap =
                before90InduPanoramaList.stream().collect(Collectors.toMap(InduSpreadPanoramaBO::getSpreadDate, Function.identity(), (o, v) -> o));
        Map<Date, InduSpreadPanoramaBO> before180InduDateMap =
                before180InduPanoramaList.stream().collect(Collectors.toMap(InduSpreadPanoramaBO::getSpreadDate, Function.identity(), (o, v) -> o));
        return induDateMap.entrySet().stream().map(dateEntry -> {
            Date date = dateEntry.getKey();
            SpreadDateDTO spreadDateDTO = spreadDateMap.get(date);
            Date before90Date = spreadDateDTO.getBefore90Date();
            Date before180Date = spreadDateDTO.getBefore180Date();
            InduPanoramaDTO induPanoramaDTO = toInduPanoramaDTO(date, dateEntry.getValue(), before90InduDateMap.get(before90Date), before180InduDateMap.get(before180Date));
            InduPanoramaExportExcelDTO excel = BeanCopyUtils.copyProperties(induPanoramaDTO, InduPanoramaExportExcelDTO.class);
            excel.setIndustryName1(DEFAULT_INDUSTRY_NAME);
            return excel;
        }).collect(Collectors.toList());
    }

    private InduPanoramaDTO toInduPanoramaDTO(Date spreadDate, InduSpreadPanoramaBO induSpread, InduSpreadPanoramaBO before90InduSpread,
                                              InduSpreadPanoramaBO before180InduSpread) {
        InduPanoramaDTO response = new InduPanoramaDTO();
        response.setIndustryCode(DEFAULT_INDUSTRY_CODE);
        response.setIndustryName(DEFAULT_INDUSTRY_NAME);
        response.setSpreadDate(spreadDate);
        if (Objects.nonNull(induSpread.getBondCreditSpreadCount()) && induSpread.getBondCreditSpreadCount() >= MIN_BOND_SIZE) {
            response.setBondCreditSpread(induSpread.getBondCreditSpread());
        }
        if (Objects.nonNull(induSpread.getBondExcessSpreadCount()) && induSpread.getBondExcessSpreadCount() >= MIN_BOND_SIZE) {
            response.setBondExcessSpread(induSpread.getBondExcessSpread());
        }
        if (Objects.nonNull(before90InduSpread)) {
            response.setBondCreditSpreadBefore90(before90InduSpread.getBondCreditSpread());
            BigDecimal bondCreditSpreadChange90 =
                    subtract(response.getBondCreditSpread(), before90InduSpread.getBondCreditSpread()).orElse(null);
            response.setBondCreditSpreadChange90(bondCreditSpreadChange90);
            response.setBondExcessSpreadBefore90(before90InduSpread.getBondExcessSpread());
            BigDecimal bondExcessSpreadChange90 =
                    subtract(response.getBondExcessSpread(), before90InduSpread.getBondExcessSpread()).orElse(null);
            response.setBondExcessSpreadChange90(bondExcessSpreadChange90);
        }
        if (Objects.nonNull(before180InduSpread)) {
            response.setBondCreditSpreadBefore180(before180InduSpread.getBondCreditSpread());
            BigDecimal bondCreditSpreadChange180 =
                    subtract(response.getBondCreditSpread(), before180InduSpread.getBondCreditSpread()).orElse(null);
            response.setBondCreditSpreadChange180(bondCreditSpreadChange180);
            response.setBondExcessSpreadBefore180(before180InduSpread.getBondExcessSpread());
            BigDecimal bondExcessSpreadChange180 =
                    subtract(response.getBondExcessSpread(), before180InduSpread.getBondExcessSpread()).orElse(null);
            response.setBondExcessSpreadChange180(bondExcessSpreadChange180);
        }
        return response;
    }

    @Override
    public Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_UDIC_BOND_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = pgUdicBondYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_UDIC_BOND_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }

    @Override
    public List<UdicSpreadPanoramaResponseDTO> listUdicPanoramas(UdicPanoramaRequestDTO req) {
        if (Objects.isNull(req)) {
            return Collections.emptyList();
        }
        Date spreadDate = req.getSpreadDate();
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
        }
        UdicPanoramaRequestDTO request = BeanCopyUtils.copyProperties(req, UdicPanoramaRequestDTO.class);
        request.setSort(Objects.isNull(request.getSort()) ? DEFAULT_SORT : request.getSort());
        request.setSpreadDate(spreadDate);
        // 从redis中取数据
        String redisKey = String.format(LIST_UDIC_SPREAD_PANORAMAS_KEY, request.toString().hashCode());
        List<UdicSpreadPanoramaResponseDTO> cacheDataList = this.listUdicSpreadPanoramasFromRedis(redisKey, request.getSort());
        if (CollectionUtils.isNotEmpty(cacheDataList)) {
            return cacheDataList;
        }
        // 1. 根据所传日期计算3个月前、6个月前的日期,需要过滤掉假期，如果是假期，则往前推最近的工作日
        SpreadDateDTO spreadDateDTO = yieldSpreadCommonService.getSpreadDate(request.getSpreadDate());
        // 查询物化视图
        List<UdicSpreadPanoramaResponseDTO> responses = listPanoramasFromBasicTable(request, spreadDateDTO);
        this.doSort(request.getSort(), responses);
        stringRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(responses), CACHE_FOUR_HOURS, TimeUnit.HOURS);
        return responses;
    }

    private List<UdicSpreadPanoramaResponseDTO> listPanoramasFromBasicTable(UdicPanoramaRequestDTO request, SpreadDateDTO spreadDateDTO) {
        final Long provinceUniCode = request.getProvinceUniCode();
        List<UdicSpreadPanoramaBO> udicSpreadPanoramaBOList;
        List<UdicSpreadPanoramaBO> before90DayUdicSpreadPanoramaBOList;
        List<UdicSpreadPanoramaBO> before180DayUdicSpreadPanoramaBOList;
        udicSpreadPanoramaBOList = pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreads(request);
        if (CollectionUtils.isEmpty(udicSpreadPanoramaBOList)) {
            return BeanCopyUtils.copyList(udicSpreadPanoramaBOList, UdicSpreadPanoramaResponseDTO.class);
        }
        UdicPanoramaRequestDTO request90 = BeanCopyUtils.copyProperties(request, UdicPanoramaRequestDTO.class);
        request90.setSpreadDate(spreadDateDTO.getBefore90Date());
        before90DayUdicSpreadPanoramaBOList = pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreads(request90);
        UdicPanoramaRequestDTO request180 = BeanCopyUtils.copyProperties(request, UdicPanoramaRequestDTO.class);
        request180.setSpreadDate(spreadDateDTO.getBefore180Date());
        before180DayUdicSpreadPanoramaBOList = pgUdicBondYieldSpreadDAO.listUdicBondYieldInduSpreads(request180);
        return fillSpreadChange(udicSpreadPanoramaBOList, before90DayUdicSpreadPanoramaBOList,
                before180DayUdicSpreadPanoramaBOList, provinceUniCode);
    }

    @SuppressWarnings("squid:UnusedPrivateMethod")
    private List<UdicSpreadPanoramaResponseDTO> listPanoramasFromMV(UdicPanoramaRequestDTO request, SpreadDateDTO spreadDateDTO) {
        final Date spreadDate = request.getSpreadDate();
        final Integer bondExtRatingMapping = request.getBondExtRatingMapping();
        final Integer spreadBondType = request.getSpreadBondType();
        final Integer guaranteeStatus = request.getGuaranteeStatus();
        final Integer spreadRemainingTenorTag = request.getSpreadRemainingTenorTag();
        final Integer bondImpliedRatingMappingTag = request.getBondImpliedRatingMappingTag();
        final Integer comYyRatingMappingTag = request.getComYyRatingMappingTag();
        final Integer administrativeDivision = request.getAdministrativeDivision();
        final Long provinceUniCode = request.getProvinceUniCode();
        List<UdicSpreadPanoramaBO> udicSpreadPanoramaBOList;
        List<UdicSpreadPanoramaBO> before90DayUdicSpreadPanoramaBOList;
        List<UdicSpreadPanoramaBO> before180DayUdicSpreadPanoramaBOList;
        udicSpreadPanoramaBOList = mvUdicBondYieldSpreadPanoramaDAO.
                listMvUdicBondYieldSpreadPanoramas(spreadDate, administrativeDivision, spreadBondType,
                        spreadRemainingTenorTag, guaranteeStatus, provinceUniCode, bondExtRatingMapping, bondImpliedRatingMappingTag, comYyRatingMappingTag);
        if (CollectionUtils.isEmpty(udicSpreadPanoramaBOList)) {
            return BeanCopyUtils.copyList(udicSpreadPanoramaBOList, UdicSpreadPanoramaResponseDTO.class);
        }
        before90DayUdicSpreadPanoramaBOList = mvUdicBondYieldSpreadPanoramaDAO.
                listMvUdicBondYieldSpreadPanoramas(spreadDateDTO.getBefore90Date(), administrativeDivision, spreadBondType,
                        spreadRemainingTenorTag, guaranteeStatus, provinceUniCode, bondExtRatingMapping, bondImpliedRatingMappingTag, comYyRatingMappingTag);
        before180DayUdicSpreadPanoramaBOList = mvUdicBondYieldSpreadPanoramaDAO.
                listMvUdicBondYieldSpreadPanoramas(spreadDateDTO.getBefore180Date(), administrativeDivision, spreadBondType,
                        spreadRemainingTenorTag, guaranteeStatus, provinceUniCode, bondExtRatingMapping, bondImpliedRatingMappingTag, comYyRatingMappingTag);
        return fillSpreadChange(udicSpreadPanoramaBOList, before90DayUdicSpreadPanoramaBOList,
                before180DayUdicSpreadPanoramaBOList, provinceUniCode);
    }

    /**
     * 填充城投利差变动
     *
     * @param udicSpreadPanoramaBOList             城投当前利差
     * @param before90DayUdicSpreadPanoramaBOList  城投90天前利差
     * @param before180DayUdicSpreadPanoramaBOList 城投180天前利差
     * @param provinceUniCode                      省份编码
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.UdicBondYieldSpreadDO
     */
    private List<UdicSpreadPanoramaResponseDTO> fillSpreadChange(List<UdicSpreadPanoramaBO> udicSpreadPanoramaBOList,
                                                                 List<UdicSpreadPanoramaBO> before90DayUdicSpreadPanoramaBOList,
                                                                 List<UdicSpreadPanoramaBO> before180DayUdicSpreadPanoramaBOList,
                                                                 Long provinceUniCode) {
        if (CollectionUtils.isEmpty(udicSpreadPanoramaBOList)) {
            return Collections.emptyList();
        }
        List<UdicSpreadPanoramaResponseDTO> udicSpreadPanoramaResponseDTOList = udicSpreadPanoramaBOList.stream().map(x -> {
            UdicSpreadPanoramaResponseDTO result = BeanCopyUtils.copyProperties(x, UdicSpreadPanoramaResponseDTO.class);
            if (x.getBondCreditSpreadCount() < YieldSpreadHelper.MIN_BOND_SIZE) {
                result.setBondCreditSpread(null);
            }
            if (x.getBondExcessSpreadCount() < YieldSpreadHelper.MIN_BOND_SIZE) {
                result.setBondExcessSpread(null);
            }
            return result;
        }).collect(Collectors.toList());
        Map<Long, UdicSpreadPanoramaBO> before90Map;
        Map<Long, UdicSpreadPanoramaBO> before180Map;
        Map<Long, AreaInfoResponseDTO> areaInfoResponseDTOMap;
        if (Objects.isNull(provinceUniCode)) {
            before90Map = before90DayUdicSpreadPanoramaBOList.stream()
                    .collect(Collectors.toMap(UdicSpreadPanoramaBO::getProvinceUniCode, Function.identity(), (x1, x2) -> x2));
            before180Map = before180DayUdicSpreadPanoramaBOList.stream()
                    .collect(Collectors.toMap(UdicSpreadPanoramaBO::getProvinceUniCode, Function.identity(), (x1, x2) -> x2));
            Set<Long> provinceAreaUniCodes = udicSpreadPanoramaResponseDTOList.stream()
                    .map(UdicSpreadPanoramaResponseDTO::getProvinceUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            areaInfoResponseDTOMap = areaService.getAreaInfoMap(provinceAreaUniCodes);
            udicSpreadPanoramaResponseDTOList = udicSpreadPanoramaResponseDTOList.stream().map(x -> {
                UdicSpreadPanoramaResponseDTO result = BeanCopyUtils.copyProperties(x, UdicSpreadPanoramaResponseDTO.class);
                AreaInfoResponseDTO areaInfoResponseDTO = areaInfoResponseDTOMap.get(result.getProvinceUniCode());
                if (Objects.nonNull(areaInfoResponseDTO)) {
                    result.setProvinceName(areaInfoResponseDTO.getProvinceName());
                    result.setAdministrativeRegion(areaInfoResponseDTO.getAreaLevelId());
                }
                return result;
            }).collect(Collectors.toList());
        } else {
            before90Map = before90DayUdicSpreadPanoramaBOList.stream()
                    .collect(Collectors.toMap(UdicSpreadPanoramaBO::getCityUniCode, Function.identity(), (x1, x2) -> x2));
            before180Map = before180DayUdicSpreadPanoramaBOList.stream()
                    .collect(Collectors.toMap(UdicSpreadPanoramaBO::getCityUniCode, Function.identity(), (x1, x2) -> x2));
            Set<Long> cityAreaUniCodes = udicSpreadPanoramaResponseDTOList.stream().map(UdicSpreadPanoramaResponseDTO::getCityUniCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            areaInfoResponseDTOMap = areaService.getAreaInfoMap(cityAreaUniCodes);
            udicSpreadPanoramaResponseDTOList = udicSpreadPanoramaResponseDTOList.stream().map(x -> {
                UdicSpreadPanoramaResponseDTO result = BeanCopyUtils.copyProperties(x, UdicSpreadPanoramaResponseDTO.class);
                AreaInfoResponseDTO areaInfoResponseDTO = areaInfoResponseDTOMap.get(result.getCityUniCode());
                if (Objects.nonNull(areaInfoResponseDTO)) {
                    result.setCityName(areaInfoResponseDTO.getCityName());
                    result.setAdministrativeRegion(areaInfoResponseDTO.getAreaLevelId());
                }
                return result;
            }).collect(Collectors.toList());
        }

        List<UdicSpreadPanoramaResponseDTO> responseDTOList = new ArrayList<>();
        for (UdicSpreadPanoramaResponseDTO udicSpreadPanoramaResponseDTO : udicSpreadPanoramaResponseDTOList) {
            UdicSpreadPanoramaResponseDTO responseDTO = calcSpreadChange(udicSpreadPanoramaResponseDTO, before90Map,
                    before180Map, provinceUniCode);
            responseDTOList.add(responseDTO);
        }
        return responseDTOList;
    }

    private UdicSpreadPanoramaResponseDTO calcSpreadChange(UdicSpreadPanoramaResponseDTO udicSpreadPanoramaResponseDTO,
                                                           Map<Long, UdicSpreadPanoramaBO> before90Map,
                                                           Map<Long, UdicSpreadPanoramaBO> before180Map,
                                                           Long provinceUniCode) {
        UdicSpreadPanoramaResponseDTO result = BeanCopyUtils.copyProperties(udicSpreadPanoramaResponseDTO,
                UdicSpreadPanoramaResponseDTO.class);
        UdicSpreadPanoramaBO before90;
        UdicSpreadPanoramaBO before180;
        // 这里用来区分是计算省份利差变动 还是城市利差变动
        if (Objects.isNull(provinceUniCode)) {
            before90 = before90Map.get(result.getProvinceUniCode());
            before180 = before180Map.get(result.getProvinceUniCode());
        } else {
            before90 = before90Map.get(result.getCityUniCode());
            before180 = before180Map.get(result.getCityUniCode());
        }
        // 计算90天前利差变动  小于五条的不计算
        if (Objects.nonNull(before90)) {
            if (before90.getBondCreditSpreadCount() >= YieldSpreadHelper.MIN_BOND_SIZE) {
                BigDecimal bondCreditSpreadChange90 =
                        subtract(result.getBondCreditSpread(),
                                before90.getBondCreditSpread()).orElse(null);
                result.setBondCreditSpreadChange90(bondCreditSpreadChange90);
            }
            if (before90.getBondExcessSpreadCount() >= YieldSpreadHelper.MIN_BOND_SIZE) {
                BigDecimal bondExcessSpreadChange90 =
                        subtract(result.getBondExcessSpread(),
                                before90.getBondExcessSpread()).orElse(null);
                result.setBondExcessSpreadChange90(bondExcessSpreadChange90);
            }
        }
        // 计算180天前利差变动  小于五条的不计算
        if (Objects.nonNull(before180)) {
            if (before180.getBondCreditSpreadCount() >= YieldSpreadHelper.MIN_BOND_SIZE) {
                BigDecimal bondCreditSpreadChange180 =
                        subtract(result.getBondCreditSpread(),
                                before180.getBondCreditSpread()).orElse(null);
                result.setBondCreditSpreadChange180(bondCreditSpreadChange180);
            }
            if (before180.getBondExcessSpreadCount() >= YieldSpreadHelper.MIN_BOND_SIZE) {
                BigDecimal bondExcessSpreadChange180 =
                        subtract(result.getBondExcessSpread(),
                                before180.getBondExcessSpread()).orElse(null);
                result.setBondExcessSpreadChange180(bondExcessSpreadChange180);
            }
        }
        return result;
    }

    /**
     * 从redis中查询城投利差全景数据
     *
     * @param redisKey redisKey
     * @param sort     排序DTO
     * @return {@link List}<{@link UdicSpreadPanoramaResponseDTO}> 城投利差全景数据响应集
     */
    private List<UdicSpreadPanoramaResponseDTO> listUdicSpreadPanoramasFromRedis(String redisKey, SortDTO sort) {
        String value = stringRedisTemplate.opsForValue().get(redisKey);
        return Optional.ofNullable(value).map(val -> {
            List<UdicSpreadPanoramaResponseDTO> responses = JSON.parseArray(val, UdicSpreadPanoramaResponseDTO.class);
            this.doSort(sort, responses);
            return responses;
        }).orElse(Collections.emptyList());
    }

    private void doSort(SortDTO sort, List<UdicSpreadPanoramaResponseDTO> responses) {
        Comparator<UdicSpreadPanoramaResponseDTO> comparator = Optional.ofNullable(sort)
                .map(so -> COMPARATOR_MAP.get(sort.getPropertyName()).get(sort.getSortDirection()))
                .orElse(DEFAULT_COMPARATOR);
        responses.sort(comparator);
    }

    @Override
    public void refreshMvUdicBondYieldSpreadPanorama() {
        mvUdicBondYieldSpreadPanoramaDAO.refreshMvUdicBondYieldSpreadPanorama();
    }

    @Override
    public NormPagingResult<UdicBondYieldSpreadResponseDTO> getBondYieldSpreadPaging(UdicListRequestDTO request) {
        Date spreadDate = request.getSpreadDate();
        boolean isToday = false;
        SortDTO sort = request.getSort();
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
            isToday = true;
        }
        if (!isToday) {
            sort = Objects.nonNull(sort) && HISTORY_ORDER_FIELD_SET.contains(sort.getPropertyName()) ? sort : DEFAULT_SORT;
        }
        // 转换请求DTO
        UdicBondYieldSpreadParamDTO searchParameter = UdicBondYieldSpreadParamDTO.builder()
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now()))
                .sortOrDefault(sort, DEFAULT_SORT)
                .page(request.getPageNum(), request.getPageSize())
                .searchCode(request.getCodeType(), request.getSearchCode())
                .comUniCodes(request.getComUniCodes())
                .bondUniCodes(request.getBondUniCodes())
                .compositionCondition(request.getCompositionCondition()).build();
        return listSingleBondYieldSpreads(searchParameter);
    }

    private NormPagingResult<UdicBondYieldSpreadResponseDTO> listSingleBondYieldSpreads(UdicBondYieldSpreadParamDTO searchParameter) {
        NormPagingResult<UdicBondYieldSpreadDO> pagingResult = udicBondYieldSpreadDAO.getBondYieldSpreadPaging(searchParameter);
        if (CollectionUtils.isEmpty(pagingResult.getList())) {
            return pagingResult.convert(bond -> BeanCopyUtils.copyProperties(bond, UdicBondYieldSpreadResponseDTO.class));
        }
        Long[] bondUniCodes = pagingResult.getList().stream().map(UdicBondYieldSpreadDO::getBondUniCode).distinct().toArray(Long[]::new);
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        return pagingResult.convert(bond -> {
            UdicBondYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(bond, UdicBondYieldSpreadResponseDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            String ratingStr = RatingUtils.getRating(bond.getComExtRatingMapping()) + "/" + RatingUtils.getRating(bond.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            response.setBondExtRating(RatingUtils.getRating(response.getBondExtRatingMapping()));
            EnumUtils.getEnumByValue(response.getGuaranteedStatus(), GuaranteedStatusEnum.class).ifPresent(guarantee -> response.setGuaranteedStatusText(guarantee.getText()));
            response.setBondImpliedRating(RatingUtils.getRating(response.getBondImpliedRatingMapping()));
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            response.setCdbLerpYield(CommonUtils.formatDecimal(bond.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            response.setImpliedRatingLerpYield(CommonUtils.formatDecimal(bond.getImpliedRatingLerpYield(), FOUR_DECIMAL_PLACE));
            return response;
        });
    }

    @Override
    public List<UdicCurveResponseDTO> listCurves(UdicCurveRequestDTO request) {
        UdicBondYieldSpreadParamDTO searchParameter = UdicBondYieldSpreadParamDTO.builder()
                .compositionCondition(request.getCompositionCondition())
                .comSpread(request.getComSpread()).bondSpread(request.getBondSpread())
                .spanSpreadDate(request.getStartSpreadDate(), request.getEndSpreadDate())
                .spreadCurve(request.getSpreadCurveType(), request.getDisplayCdbBenchmarkCurve()).build();
        // 1. 从缓存中拿数据
        String cacheKey = String.format(LIST_UDIC_SPREAD_CURVES_KEY, request.toString().hashCode());
        List<UdicCurveResponseDTO> cacheResponses = redisService.listUdicCurvesFromCache(cacheKey);
        if (CollectionUtils.isNotEmpty(cacheResponses)) {
            return cacheResponses;
        }
        SpreadRequestTypeEnum requestType = ITextValueEnum.getEnum(SpreadRequestTypeEnum.class, request.getSpreadRequestType());
        // 2. 组合查询方式，查询并计算中位数
        List<BondYieldSpreadCurveBO> response;
        if (SpreadRequestTypeEnum.GROUP_CONDITION.equals(requestType)) {
            response = this.listCurvesForMultiCondition(searchParameter);
        } else if (SpreadRequestTypeEnum.COM_SPREAD.equals(requestType)) {
            // 3. 主体利差查询方式，查询并计算中位数
            response = this.listCurvesForComSpread(searchParameter);
        } else {
            // 4. 单券利差查询方式，查询数据，不需要计算中位数
            response = this.listCurvesForBondSpread(searchParameter);
        }
        response.sort(Comparator.comparing(BondYieldSpreadCurveBO::getSpreadDate));
        redisService.set(cacheKey, JSON.toJSONString(response), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return BeanCopyUtils.copyList(response, UdicCurveResponseDTO.class);
    }

    @Override
    public void refreshMvUdicBondYieldSpreadCurve(UdicRegionEnum udicRegionEnum) {
        mvUdicBondYieldSpreadCurveDAO.refreshMvUdicBondYieldSpreadCurve(udicRegionEnum);
        pgUdicBondYieldSpreadCurveDAO.refreshUdicBondYieldSpreadCurveFromMV(udicRegionEnum);
    }

    @Override
    public void refreshCurveYesterday() {
        mvUdicBondYieldSpreadCurveDAO.refreshCurveYesterday();
        pgUdicBondYieldSpreadCurveDAO.syncCurveIncrFromMV();
    }

    private List<BondYieldSpreadCurveBO> listCurvesForBondSpread(UdicBondYieldSpreadParamDTO searchParameter) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("list_udic_curves_for_bond_spread_task");
        final Long bondUniCode = searchParameter.getBondUniCode();
        final Date startSpreadDate = searchParameter.getStartSpreadDate();
        final Date endSpreadDate = searchParameter.getEndSpreadDate();
        List<BondYieldSpreadCurveBO> curveList =
                pgUdicBondYieldSpreadDAO.listYieldSpreadCurvesByBond(bondUniCode, startSpreadDate, endSpreadDate);
        stopWatch.stop();
        logger.info(SPEND_TIME_LOG, stopWatch.getLastTaskName(), stopWatch.getTotalTimeMillis());
        return curveList;
    }

    private List<BondYieldSpreadCurveBO> listCurvesForComSpread(UdicBondYieldSpreadParamDTO searchParameter) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("list_udic_curves_for_com_spread_task");
        final Long comUniCode = searchParameter.getComUniCode();
        final Integer spreadBondType = searchParameter.getSpreadBondType();
        final Date startSpreadDate = searchParameter.getStartSpreadDate();
        final Date endSpreadDate = searchParameter.getEndSpreadDate();
        List<BondYieldSpreadCurveBO> curveList = udicComYieldSpreadDAO.listYieldSpreadCurves(comUniCode, spreadBondType, startSpreadDate, endSpreadDate);
        if (CollectionUtils.isEmpty(curveList)) {
            throw new TipsException(NOT_HAS_BONDS_MSG);
        }
        stopWatch.stop();
        logger.info(SPEND_TIME_LOG, stopWatch.getLastTaskName(), stopWatch.getTotalTimeMillis());
        Predicate<BondYieldSpreadCurveBO> anyOneNotEmptyPre =
                curve -> !isAllEmpty(curve.getBondCreditSpread(), curve.getBondExcessSpread(), curve.getCbYield());
        List<BondYieldSpreadCurveBO> responses = curveList.stream().filter(anyOneNotEmptyPre).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(responses)) {
            throw new TipsException(NOT_HAS_BONDS_MSG);
        }
        return responses;
    }

    private List<BondYieldSpreadCurveBO> listCurvesForMultiCondition(UdicBondYieldSpreadParamDTO searchParameter) {
        StopWatch stopWatch = new StopWatch();
        List<BondYieldSpreadCurveBO> responses;
        final Long[] bondUniCodes = searchParameter.getBondUniCodes();
        final Long[] comUniCodes = searchParameter.getComUniCodes();
        // 1. 从分区表中取数据
        if (isAllEmpty(bondUniCodes, comUniCodes)) {
            stopWatch.start("list_udic_curves_for_multi_condition_from_partition_task");
            responses = this.listCurvesForMultiConditionFromPartition(searchParameter);
        } else {
            // 2. 从基表(postgresql)中取数据
            stopWatch.start("list_udic_curves_for_multi_condition_from_pg_task");
            responses = this.listCurvesForMultiConditionFromPG(searchParameter);
        }
        stopWatch.stop();
        logger.info(SPEND_TIME_LOG, stopWatch.getLastTaskName(), stopWatch.getTotalTimeMillis());
        if (CollectionUtils.isEmpty(responses)) {
            throw new TipsException(NOT_HAS_FIVE_BONDS_MSG);
        }
        return responses;
    }

    private List<BondYieldSpreadCurveBO> listCurvesForMultiConditionFromPartition(UdicBondYieldSpreadParamDTO searchParameter) {
        List<BondYieldSpreadCurveBO> curvesList = pgUdicBondYieldSpreadCurveDAO.listYieldSpreadCurves(searchParameter);
        if (CollectionUtils.isEmpty(curvesList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> responseList = Lists.newArrayList();
        for (BondYieldSpreadCurveBO partitionCurve : curvesList) {
            BondYieldSpreadCurveBO curve = new BondYieldSpreadCurveBO();
            curve.setSpreadDate(partitionCurve.getSpreadDate());
            if (isMatch(partitionCurve.getBondCreditSpread(), partitionCurve.getBondCreditSpreadCount())) {
                curve.setBondCreditSpread(partitionCurve.getBondCreditSpread());
            }
            if (isMatch(partitionCurve.getBondExcessSpread(), partitionCurve.getBondExcessSpreadCount())) {
                curve.setBondExcessSpread(partitionCurve.getBondExcessSpread());
            }
            if (isMatch(partitionCurve.getCbYield(), partitionCurve.getCbYieldCount())) {
                curve.setCbYield(partitionCurve.getCbYield());
            }
            if (isMatch(partitionCurve.getCdbLerpYield(), partitionCurve.getCdbLerpYieldCount())) {
                curve.setCdbLerpYield(partitionCurve.getCdbLerpYield());
            }
            if (!isAllEmpty(curve.getBondCreditSpread(), curve.getBondExcessSpread(), curve.getCbYield(), curve.getCdbLerpYield())) {
                responseList.add(curve);
            }
        }
        return responseList;
    }

    private boolean isMatch(BigDecimal value, Integer count) {
        return Objects.nonNull(value) && Objects.nonNull(count) && count >= MIN_BOND_SIZE;
    }

    private List<BondYieldSpreadCurveBO> listCurvesForMultiConditionFromPG(UdicBondYieldSpreadParamDTO searchParameter) {
        List<BondYieldSpreadCurveBO> curvesGroupingList = pgUdicBondYieldSpreadDAO.listYieldSpreadCurvesByMultiCondition(searchParameter);
        if (CollectionUtils.isEmpty(curvesGroupingList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> responseList = Lists.newArrayList();
        for (BondYieldSpreadCurveBO curveGroup : curvesGroupingList) {
            BondYieldSpreadCurveBO curve = new BondYieldSpreadCurveBO();
            curve.setSpreadDate(curveGroup.getSpreadDate());
            if (isMatch(curveGroup.getBondCreditSpread(), curveGroup.getBondCreditSpreadCount())) {
                curve.setBondCreditSpread(curveGroup.getBondCreditSpread());
            }
            if (isMatch(curveGroup.getBondExcessSpread(), curveGroup.getBondExcessSpreadCount())) {
                curve.setBondExcessSpread(curveGroup.getBondExcessSpread());
            }
            if (isMatch(curveGroup.getCbYield(), curveGroup.getCbYieldCount())) {
                curve.setCbYield(curveGroup.getCbYield());
            }
            if (isMatch(curveGroup.getCdbLerpYield(), curveGroup.getCdbLerpYieldCount())) {
                curve.setCdbLerpYield(curveGroup.getCdbLerpYield());
            }
            if (!isAllEmpty(curve.getBondCreditSpread(), curve.getBondExcessSpread(), curve.getCbYield(), curve.getCdbLerpYield())) {
                responseList.add(curve);
            }
        }
        return responseList;
    }

    @Override
    public void refreshMvUdicBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh) {
        final List<AbstractRatingRouter.SpreadDateRange> dateRangeList = splitDateRange(LocalDate.parse("2019-06-01"), LocalDate.now().minusDays(1));
        SwThreadPoolWorker<Object> swThreadPoolWorker = SwThreadPoolWorker.of(shardExecutorService);
        swThreadPoolWorker.doWorks(dateRangeList.stream().flatMap(dateRange -> {
            final RefreshYieldCurveParam param = RefreshYieldCurveParam.builder().dateRange(dateRange).initRefresh(true, isTableRefresh).build();
            return Stream.of(
                    swThreadPoolWorker.submit(() -> refresh(param, YieldSpreadCurveShardEnum.ALL.getText())),
                    swThreadPoolWorker.submit(() -> refresh(param, YieldSpreadCurveShardEnum.OPERATOR_CITY.getText())),
                    swThreadPoolWorker.submit(() -> refresh(param, YieldSpreadCurveShardEnum.OPERATOR_PROVINCE.getText())));
        }).toArray(CompletableFuture[]::new));
    }

    private void refresh(RefreshYieldCurveParam param, String operator) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("refreshMvUdicBondYieldSpreadBondYyRatingMappingCurve:" + operator);
        this.refreshMvUdicBondYieldSpreadBondYyRatingMappingCurve(operator, param);
        stopWatch.stop();
        logger.info("[refreshMvUdicBondYieldSpreadBondYyRatingMappingCurve] Yy评级数据刷新完成 operator:{} ,dateRange:{}, {}s",
                operator, param.getStartDate() + "-" + param.getEndDate(), stopWatch.getTotalTimeSeconds());
        stopWatch.start("refreshMvUdicBondYieldSpreadBondImpliedRatingMappingCurve");
        this.refreshMvUdicBondYieldSpreadBondImpliedRatingMappingCurve(operator, param);
        stopWatch.stop();
        logger.info("[refreshMvUdicBondYieldSpreadBondImpliedRatingMappingCurve] 隐含评级数据刷新完成 operator:{} ,dateRange:{}, {}s",
                operator, param.getStartDate() + "-" + param.getEndDate(), stopWatch.getTotalTimeSeconds());
    }

    @Override
    public void refreshMvUdicBondYieldSpreadRatingCurve(RefreshYieldCurveParam param) {
        SwThreadPoolWorker<Object> swThreadPoolWorker = SwThreadPoolWorker.of(shardExecutorService);
        swThreadPoolWorker.doWorks(Stream.of(YieldSpreadCurveShardEnum.ALL.getText(), YieldSpreadCurveShardEnum.OPERATOR_CITY.getText(),
                        YieldSpreadCurveShardEnum.OPERATOR_PROVINCE.getText())
                .map(operatorLevel -> swThreadPoolWorker.submit(() -> refresh(param, operatorLevel))).toArray(CompletableFuture[]::new));
    }

    private void refreshMvUdicBondYieldSpreadBondYyRatingMappingCurve(String operator, RefreshYieldCurveParam param) {
        Set<String> udicInvestmentRatingCombination = RatingCombinationHelper.getUdicInvestmentRatingCombination();
        Set<YyRatingRouter> yyRatingRouters = yyRatingRouterFactory.newRatingRouterList(udicInvestmentRatingCombination);
        for (YyRatingRouter yyRatingRouter : yyRatingRouters) {
            yyRatingRouter.setLevel(operator);
            try {
                Optional.ofNullable(param)
                        .ifPresent(p -> yyRatingRouter.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
                mvUdicBondYieldSpreadCurveDAO.createOrRefreshUdicCurveMv(yyRatingRouter, param);
                pgUdicBondYieldSpreadCurveDAO.syncCurveShardUdicForMv(yyRatingRouter, param);
            } finally {
                mvUdicBondYieldSpreadCurveDAO.droTempMv(yyRatingRouter);
            }
        }
    }

    private void refreshMvUdicBondYieldSpreadBondImpliedRatingMappingCurve(String operator, RefreshYieldCurveParam param) {
        Set<String> implicitRatingCombination = RatingCombinationHelper.getUdicImplicitRatingCombination();
        Set<ImplicitRatingRouter> implicitRatingRouters = implicitRatingRouterFactory.newRatingRouterList(implicitRatingCombination);
        for (ImplicitRatingRouter implicitRatingRouter : implicitRatingRouters) {
            implicitRatingRouter.setLevel(operator);
            try {
                Optional.ofNullable(param)
                        .ifPresent(p -> implicitRatingRouter.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
                mvUdicBondYieldSpreadCurveDAO.createOrRefreshUdicCurveMv(implicitRatingRouter, param);
                pgUdicBondYieldSpreadCurveDAO.syncCurveShardUdicForMv(implicitRatingRouter, param);
            } finally {
                mvUdicBondYieldSpreadCurveDAO.droTempMv(implicitRatingRouter);

            }
        }
    }

    /**
     * 计算城投区域利差
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    @Override
    public void calcUdicAreaYieldSpreadsBySpreadDate(Date startDate, Date endDate) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate)) {
            startDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
            endDate = DateExtensionUtils.addDays(new Date(System.currentTimeMillis()), -1);
        }
        List<Date> spreadDates = DateExtensionUtils.listRangeDate(startDate, endDate);
        if (CollectionUtils.isEmpty(spreadDates)) {
            return;
        }
        for (Date spreadDate : spreadDates) {
            if (holidayService.isHoliday(spreadDate)) {
                continue;
            }
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("calcUdicBondYieldSpread");

            calcUdicAreaBondYieldSpread(spreadDate);

            stopWatch.stop();
            logger.info(SPEND_TIME_LOG, stopWatch.getLastTaskName(), stopWatch.getTotalTimeMillis());
        }
    }

    private void calcUdicAreaBondYieldSpread(Date spreadDate) {
        //根据利差日期查询 这个日期的所有区域 对应的信用利差数据   因为所有区域只有3000多,所以可以一次性查完
        List<PgUdicAreaYieldSpreadBO> pgUdicAreaYieldSpreadBOs = pgUdicBondYieldSpreadDAO.listAreaBondYieldSpreads(spreadDate);
        if (CollectionUtils.isEmpty(pgUdicAreaYieldSpreadBOs)) {
            return;
        }
        List<UdicAreaYieldSpreadDO> allUdicAreaYieldSpreadDOs = BeanCopyUtils.copyList(pgUdicAreaYieldSpreadBOs, UdicAreaYieldSpreadDO.class)
                .stream().peek(udicAreaYieldSpreadDO -> udicAreaYieldSpreadDO.setSpreadDate(spreadDate)).collect(Collectors.toList());

        List<List<UdicAreaYieldSpreadDO>> udicAreaYieldSpreadDOsList = Lists.partition(allUdicAreaYieldSpreadDOs, BATCH_SIZE);
        for (List<UdicAreaYieldSpreadDO> udicAreaYieldSpreadDOs : udicAreaYieldSpreadDOsList) {
            Map<Long, AreaInfoResponseDTO> areaInfoMap = areaService.getAreaInfoMap(
                    udicAreaYieldSpreadDOs.stream().map(UdicAreaYieldSpreadDO::getAreaUniCode).collect(Collectors.toSet()));
            udicAreaYieldSpreadDOs = udicAreaYieldSpreadDOs.stream().peek(udicAreaYieldSpreadDO ->
                            udicAreaYieldSpreadDO.setAreaName(areaInfoMap.get(udicAreaYieldSpreadDO.getAreaUniCode()).getAreaName()))
                    .collect(Collectors.toList());
            udicAreaYieldSpreadDAO.saveBatchUdicAreaYieldSpreadDOs(udicAreaYieldSpreadDOs, spreadDate);
        }
    }

    @Override
    public boolean saveCurve(Long userid, Long curveGroupId, UdicCurveGenerateConditionReqDTO request) {
        return super.generalSaveCurve(userid, curveGroupId, request);
    }

    @Override
    public boolean updateCurve(Long userid, Long curveId, UdicCurveGenerateConditionReqDTO request) {
        return super.generalUpdateCurve(userid, curveId, request);
    }

    @Override
    public NormPagingResult<UdicBondYieldSpreadResponseDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        UdicCurveGenerateConditionReqDTO generateRequest = super.getCurveGenerateCondition(userid, request.getCurveId(), UdicCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return new NormPagingResult<>();
        }
        UdicYieldSearchParam param = this.buildUdicYieldSearchParam(request, generateRequest);
        UdicBondYieldSpreadParamDTO searchParam = BeanCopyUtils.copyProperties(param, UdicBondYieldSpreadParamDTO.class);
        searchParam.setGuaranteeStatus(param.getGuaranteedStatus());
        searchParam.setSpreadRemainingTenorTag(param.getRemainingTenor());
        NormPagingResult<UdicBondYieldSpreadResponseDTO> pagingResult = listSingleBondYieldSpreads(searchParam);
        // 权限控制
        pagingResult.setList(this.permissionProcessing(userid, request.getSpreadDate(), pagingResult.getList()));
        return pagingResult;
    }

    private List<UdicBondYieldSpreadResponseDTO> permissionProcessing(Long userid, Date spreadDate, List<UdicBondYieldSpreadResponseDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(UdicBondYieldSpreadResponseDTO::getBondUniCode).collect(Collectors.toList()));
            for (UdicBondYieldSpreadResponseDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRating(CommonUtils.desensitized(yieldSpread.getBondImpliedRating(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();

        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (UdicBondYieldSpreadResponseDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
            yieldSpread.setImpliedRatingLerpYield(CommonUtils.desensitized(yieldSpread.getImpliedRatingLerpYield(), hasPermission));
        }

        return list;
    }

    @Override
    public List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate) {
        return udicBondYieldSpreadRedisDAO.listCurves(bondUniCode, startDate, endDate);
    }

    @Override
    public List<UdicBondYieldSpreadResponseDTO> listBonds(Date spreadDate, @Nullable Set<Long> bondUniCodes) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        spreadDate = super.isToday(spreadDate) ? this.getMaxSpreadDate() : spreadDate;
        List<UdicBondYieldSpreadDO> udicBondYieldSpreadList = udicBondYieldSpreadDAO.listUdicBondYieldSpreads(spreadDate, bondUniCodes);
        if (CollectionUtils.isEmpty(udicBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        List<UdicBondYieldSpreadResponseDTO> responseList = Lists.newArrayListWithExpectedSize(udicBondYieldSpreadList.size());
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes.toArray(new Long[0])).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        for (UdicBondYieldSpreadDO udicBondYieldSpread : udicBondYieldSpreadList) {
            UdicBondYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(udicBondYieldSpread, UdicBondYieldSpreadResponseDTO.class);
            response.setCdbLerpYield(CommonUtils.formatDecimal(udicBondYieldSpread.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            response.setImpliedRatingLerpYield(CommonUtils.formatDecimal(udicBondYieldSpread.getImpliedRatingLerpYield(), FOUR_DECIMAL_PLACE));
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            String ratingStr = RatingUtils.getRating(udicBondYieldSpread.getComExtRatingMapping()) + "/" +
                    RatingUtils.getRating(udicBondYieldSpread.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            response.setBondExtRating(RatingUtils.getRating(response.getBondExtRatingMapping()));
            EnumUtils.getEnumByValue(response.getGuaranteedStatus(), GuaranteedStatusEnum.class)
                    .ifPresent(guarantee -> response.setGuaranteedStatusText(guarantee.getText()));
            response.setBondImpliedRating(RatingUtils.getRating(response.getBondImpliedRatingMapping()));
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            responseList.add(response);
        }
        return responseList;
    }

    @Override
    public AreaYieldSpreadDTO areaYieldSpread(Date spreadDate, Long areaCode) {
        AreaInfoResponseDTO areaInfo = areaService.getAreaInfoMap(Sets.newHashSet(areaCode)).get(areaCode);
        if (Objects.isNull(areaInfo)) {
            logger.info("buildAreaCodeForSearch is null.area code is:{}", areaCode);
            return new AreaYieldSpreadDTO();
        }
        AreaTypeEnum areaType = convertToAreaType(areaInfo);
        spreadDate = Objects.isNull(spreadDate) ? holidayService.lastWorkDay(Date.valueOf(LocalDate.now())) : spreadDate;
        AreaYieldSpreadDTO areaYieldSpreadDTO = BeanCopyUtils
                .copyProperties(pgUdicBondYieldSpreadCurveDAO.getDivisionYieldSpread(spreadDate, areaCode, areaType), AreaYieldSpreadDTO.class);
        areaYieldSpreadDTO.setSpreadDate(spreadDate);
        return areaYieldSpreadDTO;
    }

    private UdicYieldSearchParam buildUdicYieldSearchParam(YieldSpreadSearchReqDTO request, UdicCurveGenerateConditionReqDTO generateRequest) {
        UdicYieldSearchParam param = super
                .buildBondYieldSearchParam(request, generateRequest, UdicYieldSearchParam.class, ObjectExtensionUtils.getOrDefault(request.getSort(), DEFAULT_SORT));
        Long areaCode = generateRequest.getAreaCode();
        super.buildUdicYieldSearchParamForArea(param, areaCode);
        return param;
    }

    @Override
    public List<CurveDataResDTO> listCurveData(Long curveId) {
        UdicCurveGenerateConditionReqDTO conditionDTO = super.getCurveGenerateCondition(curveId, UdicCurveGenerateConditionReqDTO.class);
        return listCurveData(conditionDTO);
    }

    @Override
    protected List<CurveDataResDTO> listCurveData(ICurveGenerateReq curveGenerateParam) {
        UdicCurveGenerateConditionReqDTO conditionDTO = (UdicCurveGenerateConditionReqDTO) curveGenerateParam;
        ComOrBondConditionReqDTO comOrBondCondition = conditionDTO.getComOrBondCondition();
        boolean isComOrBond = Objects.nonNull(comOrBondCondition) && Objects.nonNull(comOrBondCondition.getConditionType());
        UdicYieldSearchParam searchParam = BeanCopyUtils.copyProperties(conditionDTO, UdicYieldSearchParam.class);
        super.buildUdicYieldSearchParamForArea(searchParam, conditionDTO.getAreaCode());
        List<BondYieldSpreadBO> yieldSpreads;
        if (isComOrBond) {
            searchParam.setComOrBondUniCode(comOrBondCondition.getConditionType(), comOrBondCondition.getUniCode());
            yieldSpreads = pgUdicBondYieldSpreadDAO.listBondYieldSpreads(searchParam);
        } else {
            yieldSpreads = pgUdicBondYieldSpreadCurveDAO.listBondYieldSpreads(searchParam);
        }
        return super.convertToCurveDataResDTOsAndFilterData(yieldSpreads, super.getMinSampleBondSize(isComOrBond));
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.UDIC;
    }

}
