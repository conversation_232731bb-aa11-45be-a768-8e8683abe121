package com.innodealing.onshore.yieldspread.config.datasource.interceptor;

import com.innodealing.onshore.yieldspread.helper.DynamicTableNameParamUtil;
import com.innodealing.onshore.yieldspread.model.bo.DynamicTableNameBO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.*;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.Objects;
import java.util.Properties;

/**
 * mybatis动态表名拦截器,仅解决参数hint强制分表问题,不支持sql的逻辑表同时存在多个真实分片表 并且暂不支持 参数中含有表名
 *
 * <AUTHOR>
 * @date 2024/6/17 15:25
 **/
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class DynamicTableNameInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        DynamicTableNameBO dynamicTableNameParam = DynamicTableNameParamUtil.getDynamicTableNameParam();
        if (Objects.nonNull(dynamicTableNameParam)) {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            BoundSql boundSql = statementHandler.getBoundSql();
            String sql = boundSql.getSql();
            String logicTableName = dynamicTableNameParam.getLogicTableName();
            String realTableName = dynamicTableNameParam.getRealTableName();
            if (StringUtils.isNotBlank(logicTableName) && StringUtils.isNotBlank(realTableName)
                    && sql.contains(logicTableName) && !sql.contains(realTableName)) {
                sql = sql.replaceAll(logicTableName, realTableName);
                Field field = boundSql.getClass().getDeclaredField("sql");
                field.setAccessible(true);
                field.set(boundSql, sql);
            }
        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object o) {
        // 使用 Plugin.wrap 方法生成代理对象
        return Plugin.wrap(o, this);
    }

    @Override
    public void setProperties(Properties properties) {
        //属性值不做更改
    }
}
