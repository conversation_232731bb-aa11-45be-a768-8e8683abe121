<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgYieldSpreadCompositeSearchMapper">
    <sql id="bondYieldSpreadClums">
        bond_uni_code
        ,spread_date,bond_credit_spread,bond_excess_spread,cb_yield
    </sql>
    <sql id="foreachSql">
        <foreach collection="bondUniCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </sql>

    <select id="listBondYieldSpreads" resultType="com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO">
        select
        spread_date spreadDate,
        MEDIAN((bond_credit_spread * 100000::numeric)::bigint) bondCreditSpread,
        MEDIAN((bond_excess_spread * 100000::numeric)::bigint) bondExcessSpread,
        MEDIAN((cb_yield * 100000::numeric)::bigint) cbYield,
        AVG(bond_credit_spread) avgBondCreditSpread,
        AVG(bond_excess_spread) avgBondExcessSpread,
        AVG(cb_yield) avgCbYield,
        count(bond_credit_spread) bondCreditSpreadCount,
        count(bond_excess_spread) bondExcessSpreadCount,
        count(cb_yield) cbYieldCount
        from (
        select *,row_number() OVER (PARTITION BY spread_date, bond_uni_code order by order_colum) as order_num from (
        select
        <include refid="bondYieldSpreadClums"/>,1 as order_colum
        from yield_spread.udic_bond_yield_spread
        where bond_uni_code in
        <include refid="foreachSql"/>
        <if test="spreadDate != null">
            and spread_date = #{spreadDate}
        </if>
        union all
        select
        <include refid="bondYieldSpreadClums"/>,2 as order_colum
        from yield_spread.bank_bond_yield_spread
        where bond_uni_code in
        <include refid="foreachSql"/>
        <if test="spreadDate != null">
            and spread_date = #{spreadDate}
        </if>
        union all
        select
        <include refid="bondYieldSpreadClums"/>,3 as order_colum
        from yield_spread.secu_bond_yield_spread
        where bond_uni_code in
        <include refid="foreachSql"/>
        <if test="spreadDate != null">
            and spread_date = #{spreadDate}
        </if>
        union all
        select
        <include refid="bondYieldSpreadClums"/>,4 as order_colum
        from yield_spread.insu_bond_yield_spread
        where bond_uni_code in
        <include refid="foreachSql"/>
        <if test="spreadDate != null">
            and spread_date = #{spreadDate}
        </if>
        union all
        select
        <include refid="bondYieldSpreadClums"/>,5 as order_colum
        from yield_spread.indu_bond_yield_spread
        where bond_uni_code in
        <include refid="foreachSql"/>
        <if test="spreadDate != null">
            and spread_date = #{spreadDate}
        </if>
        ) t
        )a where order_num = 1
        group by spread_date
        order by spread_date
    </select>

    <update id="setStatementTimeout">
        SET LOCAL statement_timeout = ${timeOut};
    </update>

    <select id="getStatementTimeout" resultType="string">
        show
        STATEMENT_TIMEOUT;
    </select>
</mapper>