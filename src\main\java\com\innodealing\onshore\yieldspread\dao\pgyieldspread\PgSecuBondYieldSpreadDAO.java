package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgSecuBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgSecuBondYieldSpreadCbYieldGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgSecuBondYieldSpreadCreditYieldGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group.PgSecuBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.SecuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgSecuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadCbYieldGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadCreditYieldGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadGroupDO;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.SPREAD_KEEP_SCALE;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE;
import static java.util.Objects.nonNull;

/**
 * 证券债利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class PgSecuBondYieldSpreadDAO {

    @Resource
    private PgSecuBondYieldSpreadMapper pgSecuBondYieldSpreadMapper;

    @Resource
    private PgSecuBondYieldSpreadGroupMapper pgSecuBondYieldSpreadGroupMapper;

    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private PgSecuBondYieldSpreadCbYieldGroupMapper pgSecuBondYieldSpreadCbYieldGroupMapper;

    @Resource
    private PgSecuBondYieldSpreadCreditYieldGroupMapper pgSecuBondYieldSpreadCreditYieldGroupMapper;

    /**
     * 计算中债估值中位数
     *
     * @param comUniCodes              发行人代码
     * @param securitySeniorityRanking 证券公司债求偿顺序   1:普通（剔除永续）、2:次级（剔除永续）、 3:永续"
     * @param spreadDate               利差日期
     * @return key 发行人代码,行业债利差
     */
    public Map<Long, PgSecuBondYieldSpreadGroupDO> getSecuBondYieldSpreadMap(Set<Long> comUniCodes, Integer securitySeniorityRanking,
                                                                             Date spreadDate) {
        GroupedQuery<PgSecuBondYieldSpreadDO, PgSecuBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgSecuBondYieldSpreadDO.class, PgSecuBondYieldSpreadGroupDO.class)
                        .select(PgSecuBondYieldSpreadGroupDO::getComUniCode, PgSecuBondYieldSpreadGroupDO::getCbYield,
                                PgSecuBondYieldSpreadGroupDO::getBondCreditSpread, PgSecuBondYieldSpreadGroupDO::getBondExcessSpread)
                        .and(PgSecuBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(PgSecuBondYieldSpreadDO::getComUniCode, in(comUniCodes))
                        .and(Objects.nonNull(securitySeniorityRanking), PgSecuBondYieldSpreadDO::getSecuritySeniorityRanking, isEqual(securitySeniorityRanking))
                        .groupBy(PgSecuBondYieldSpreadDO::getComUniCode);
        List<PgSecuBondYieldSpreadGroupDO> pgInduBondYieldSpreadGroupDs = pgSecuBondYieldSpreadGroupMapper.
                selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(pgInduBondYieldSpreadGroupDs)) {
            return Collections.emptyMap();
        }
        return pgInduBondYieldSpreadGroupDs.stream()
                .filter(Objects::nonNull)
                .map(x -> {
                    PgSecuBondYieldSpreadGroupDO result = BeanCopyUtils.copyProperties(x, PgSecuBondYieldSpreadGroupDO.class);
                    if (Objects.nonNull(x.getCbYield())) {
                        result.setCbYield(x.getCbYield().setScale(YIELD_SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondCreditSpread())) {
                        result.setBondCreditSpread(x.getBondCreditSpread()
                                .setScale(SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    if (Objects.nonNull(x.getBondExcessSpread())) {
                        result.setBondExcessSpread(x.getBondExcessSpread()
                                .setScale(SPREAD_KEEP_SCALE, RoundingMode.HALF_UP));
                    }
                    return result;
                }).collect(Collectors.toMap(PgSecuBondYieldSpreadGroupDO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

    /**
     * 批量更新
     *
     * @param pgSecuBondYieldSpreadDOList 证券债利差列表
     * @param spreadDate                  利差日期
     * @return 受影响的行数
     */
    @Transactional(transactionManager = PgYieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public int savePgSecuBondYieldSpreadDOList(Date spreadDate, List<PgSecuBondYieldSpreadDO> pgSecuBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(pgSecuBondYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> bondUniCodes = pgSecuBondYieldSpreadDOList.stream().map(PgSecuBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<PgSecuBondYieldSpreadDO> query = DynamicQuery.createQuery(PgSecuBondYieldSpreadDO.class)
                .select(PgSecuBondYieldSpreadDO::getId, PgSecuBondYieldSpreadDO::getBondUniCode, PgSecuBondYieldSpreadDO::getSpreadDate)
                .and(PgSecuBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(PgSecuBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<PgSecuBondYieldSpreadDO> existDataList = pgSecuBondYieldSpreadMapper.selectByDynamicQuery(query);
        Map<String, PgSecuBondYieldSpreadDO> existPgSecuBondYieldSpreadDO = existDataList.stream().collect(Collectors.toMap(x ->
                        String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                Function.identity(), (x1, x2) -> x2));
        MapperBatchAction<PgSecuBondYieldSpreadMapper> batchAction = MapperBatchAction.
                create(PgSecuBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgSecuBondYieldSpreadDO pgSecuBondYieldSpreadDO : pgSecuBondYieldSpreadDOList) {
            PgSecuBondYieldSpreadDO existData = existPgSecuBondYieldSpreadDO.get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                    pgSecuBondYieldSpreadDO.getBondUniCode(),
                    pgSecuBondYieldSpreadDO.getSpreadDate().getTime()));
            batchAction.addAction(mapper -> {
                if (Objects.nonNull(existData)) {
                    pgSecuBondYieldSpreadDO.setId(existData.getId());
                    mapper.updateByPrimaryKey(pgSecuBondYieldSpreadDO);
                } else {
                    mapper.insert(pgSecuBondYieldSpreadDO);
                }
            });
        }
        List<BatchResult> batchResults = batchAction.doBatchActionWithResults();
        return batchResults.stream().mapToInt(x -> {
            int effectRows = Arrays.stream(x.getUpdateCounts()).sum();
            if (effectRows > 0) {
                return effectRows;
            } else {
                return x.getParameterObjects().size();
            }
        }).sum();
    }

    /**
     * 查询利差中债收益率中位数集合
     *
     * @param spreadDate                利差日期
     * @param securitySeniorityRankings 证券公司债求偿顺序   1:普通（剔除永续）、2:次级（剔除永续）、 3:永续"
     * @return {@link List}<{@link PgSecuBondYieldSpreadCbYieldGroupDO}> 利差中债收益率中位数集合
     */
    public List<PgSecuBondYieldSpreadCbYieldGroupDO> listYieldSpreadCbYieldMedians(@NonNull Date spreadDate,
                                                                                   Collection<Integer> securitySeniorityRankings) {
        GroupedQuery<PgSecuBondYieldSpreadDO, PgSecuBondYieldSpreadCbYieldGroupDO> query =
                GroupByQuery.createQuery(PgSecuBondYieldSpreadDO.class, PgSecuBondYieldSpreadCbYieldGroupDO.class)
                        .and(PgSecuBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(!CollectionUtils.isEmpty(securitySeniorityRankings), PgSecuBondYieldSpreadDO::getSecuritySeniorityRanking, in(securitySeniorityRankings))
                        .groupBy(PgSecuBondYieldSpreadDO::getSpreadDate, PgSecuBondYieldSpreadDO::getSecuritySeniorityRanking);
        return pgSecuBondYieldSpreadCbYieldGroupMapper.selectByGroupedQuery(query);
    }

    /**
     * 查询利差信用利差中位数集合
     *
     * @param spreadDate                利差日期
     * @param securitySeniorityRankings 证券公司债求偿顺序   1:普通（剔除永续）、2:次级（剔除永续）、 3:永续"
     * @return {@link List}<{@link PgSecuBondYieldSpreadCreditYieldGroupDO}> 利差信用利差中位数集合
     */
    public List<PgSecuBondYieldSpreadCreditYieldGroupDO> listYieldSpreadCreditYieldMedians(@NonNull Date spreadDate,
                                                                                           Collection<Integer> securitySeniorityRankings) {
        GroupedQuery<PgSecuBondYieldSpreadDO, PgSecuBondYieldSpreadCreditYieldGroupDO> query =
                GroupByQuery.createQuery(PgSecuBondYieldSpreadDO.class, PgSecuBondYieldSpreadCreditYieldGroupDO.class)
                        .and(PgSecuBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(!CollectionUtils.isEmpty(securitySeniorityRankings), PgSecuBondYieldSpreadDO::getSecuritySeniorityRanking, in(securitySeniorityRankings))
                        .groupBy(PgSecuBondYieldSpreadDO::getSpreadDate, PgSecuBondYieldSpreadDO::getSecuritySeniorityRanking);
        return pgSecuBondYieldSpreadCreditYieldGroupMapper.selectByGroupedQuery(query);
    }

    /**
     * 查询银行利差
     *
     * @param params 查询参数
     * @return 利差数据
     */
    public List<BondYieldSpreadBO> listBondYieldSpreads(SecuYieldSearchParam params) {
        GroupedQuery<PgSecuBondYieldSpreadDO, PgSecuBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(PgSecuBondYieldSpreadDO.class, PgSecuBondYieldSpreadGroupDO.class)
                        .select(PgSecuBondYieldSpreadGroupDO::getSpreadDate,
                                PgSecuBondYieldSpreadGroupDO::getBondCreditSpread,
                                PgSecuBondYieldSpreadGroupDO::getBondExcessSpread,
                                PgSecuBondYieldSpreadGroupDO::getCbYield,
                                PgSecuBondYieldSpreadGroupDO::getAvgBondCreditSpread,
                                PgSecuBondYieldSpreadGroupDO::getAvgBondExcessSpread,
                                PgSecuBondYieldSpreadGroupDO::getAvgCbYield,
                                PgSecuBondYieldSpreadGroupDO::getBondCreditSpreadCount,
                                PgSecuBondYieldSpreadGroupDO::getBondExcessSpreadCount,
                                PgSecuBondYieldSpreadGroupDO::getCbYieldCount)
                        .and(Objects.nonNull(params.getSpreadBondType()), PgSecuBondYieldSpreadDO::getSecuritySeniorityRanking, isEqual(params.getSpreadBondType()))
                        .and(Objects.nonNull(params.getRemainingTenor()), PgSecuBondYieldSpreadDO::getSpreadRemainingTenorTag, isEqual(params.getRemainingTenor()))
                        .and(ArrayUtils.isNotEmpty(params.getBondImpliedRatingMappings()),
                                PgSecuBondYieldSpreadDO::getBondImpliedRatingMapping, in(params.getBondImpliedRatingMappings()))
                        .and(Objects.nonNull(params.getComUniCode()), PgSecuBondYieldSpreadDO::getComUniCode, isEqual(params.getComUniCode()))
                        .and(Objects.nonNull(params.getBondUniCode()), PgSecuBondYieldSpreadDO::getBondUniCode, isEqual(params.getBondUniCode()))
                        .groupBy(PgSecuBondYieldSpreadDO::getSpreadDate)
                        .orderBy(PgSecuBondYieldSpreadGroupDO::getSpreadDate, asc());
        List<PgSecuBondYieldSpreadGroupDO> bondYieldSpreads = pgSecuBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(bondYieldSpreads)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(bondYieldSpreads, BondYieldSpreadBO.class);
    }

    /**
     * 获取最新利差日期
     *
     * @return 最新利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgSecuBondYieldSpreadDO> query = DynamicQuery.createQuery(PgSecuBondYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt = pgSecuBondYieldSpreadMapper.selectMaxByDynamicQuery(PgSecuBondYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 查询并计算利差曲线数据-单券利差方式
     *
     * @param bondUniCode     债券唯一编码
     * @param startSpreadDate 利差开始日期
     * @param endSpreadDate   利差结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 债券每天中位数数据集合
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurvesByBond(Long bondUniCode, Date startSpreadDate, Date endSpreadDate) {
        if (Objects.isNull(bondUniCode)) {
            return Collections.emptyList();
        }
        DynamicQuery<PgSecuBondYieldSpreadDO> query = DynamicQuery.createQuery(PgSecuBondYieldSpreadDO.class)
                .select(PgSecuBondYieldSpreadDO::getSpreadDate, PgSecuBondYieldSpreadDO::getBondCreditSpread,
                        PgSecuBondYieldSpreadDO::getBondExcessSpread, PgSecuBondYieldSpreadDO::getCbYield)
                .and(PgSecuBondYieldSpreadDO::getBondUniCode, isEqual(bondUniCode))
                .and(nonNull(startSpreadDate), PgSecuBondYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startSpreadDate))
                .and(nonNull(endSpreadDate), PgSecuBondYieldSpreadDO::getSpreadDate, lessThanOrEqual(endSpreadDate))
                .orderBy(PgSecuBondYieldSpreadDO::getSpreadDate, SortDirections::asc);
        List<PgSecuBondYieldSpreadDO> pgUdicBondYieldSpreadList = pgSecuBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(pgUdicBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> copyList = Lists.newArrayListWithExpectedSize(pgUdicBondYieldSpreadList.size());
        for (PgSecuBondYieldSpreadDO pgSecuBondYieldSpreadDO : pgUdicBondYieldSpreadList) {
            BondYieldSpreadCurveBO clone = BeanCopyUtils.copyProperties(pgSecuBondYieldSpreadDO, BondYieldSpreadCurveBO.class);
            BigDecimalUtils.handlerPrecision(clone.getBondCreditSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondCreditSpread);
            BigDecimalUtils.handlerPrecision(clone.getBondExcessSpread(), SPREAD_KEEP_SCALE).ifPresent(clone::setBondExcessSpread);
            BigDecimalUtils.handlerPrecision(clone.getCbYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCbYield);
            BigDecimalUtils.handlerPrecision(clone.getCdbLerpYield(), YIELD_SPREAD_KEEP_SCALE).ifPresent(clone::setCdbLerpYield);
            copyList.add(clone);
        }
        return copyList;
    }

}

