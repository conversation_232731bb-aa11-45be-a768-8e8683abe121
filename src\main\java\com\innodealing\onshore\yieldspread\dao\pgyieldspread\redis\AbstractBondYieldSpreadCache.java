package com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis;

import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldSpreadCurveDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.lang.NonNull;

import java.sql.Date;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 抽象单券利差缓存对象
 *
 * <AUTHOR>
 */
public abstract class AbstractBondYieldSpreadCache {

    protected static final int FOUR_HOURS = 4;
    protected static final int ZERO_MINUTE = 0;
    protected static final int ZERO_SECOND = 0;
    protected static final int ZERO_NANO = 0;
    protected static final long EARLY_EXPIRED_TIME = 60 * 1000 * 10L;
    protected static final int EARLY_EXPIRED_TIME_END_HOUR = 8;
    protected static final int EARLY_EXPIRED_TIME_START_HOUR = 4;
    protected StringRedisTemplate stringRedisTemplate;

    protected AbstractBondYieldSpreadCache(@NonNull StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 查询曲线数据
     *
     * @param bondUniCode 债券唯一编码
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveDTO}>
     */
    public List<BondYieldSpreadCurveDTO> listCurves(Long bondUniCode, Date startDate, Date endDate) {
        final String cacheKey = this.cacheKey(bondUniCode);
        final double minScore = this.getScore(startDate);
        final double maxScore = this.getScore(endDate);
        Set<String> curves = stringRedisTemplate.opsForZSet().rangeByScore(cacheKey, minScore, maxScore);
        if (CollectionUtils.isNotEmpty(curves)) {
            return curves.stream().map(curve -> JSON.parseObject(curve, BondYieldSpreadCurveDTO.class)).collect(Collectors.toList());
        }
        List<BondYieldSpreadCurveBO> bondYieldSpreadCurveList = this.fromDB(bondUniCode);
        if (CollectionUtils.isEmpty(bondYieldSpreadCurveList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveDTO> curveList = BeanCopyUtils.copyList(bondYieldSpreadCurveList, BondYieldSpreadCurveDTO.class);
        Set<ZSetOperations.TypedTuple<String>> typedTuples = curveList.stream()
                .map(curve -> new DefaultTypedTuple<>(JSON.toJSONString(curve), this.getScore(curve.getSpreadDate())))
                .collect(Collectors.toSet());
        stringRedisTemplate.opsForZSet().add(cacheKey, typedTuples);
        stringRedisTemplate.expire(cacheKey, this.expiredTime(), TimeUnit.MILLISECONDS);
        return curveList.stream()
                .filter(curve -> curve.getSpreadDate().compareTo(startDate) >= 0 && curve.getSpreadDate().compareTo(endDate) <= 0)
                .sorted(Comparator.comparing(BondYieldSpreadCurveDTO::getSpreadDate))
                .collect(Collectors.toList());
    }

    /**
     * 缓存key
     *
     * @param bondUniCode 债券唯一编码
     * @return {@link String}
     */
    protected abstract String cacheKey(@NonNull Long bondUniCode);

    /**
     * 从数据库中查询数据
     *
     * @param bondUniCode 债券唯一编码
     * @return {@link List}<{@link BondYieldSpreadCurveBO}>
     */
    protected abstract List<BondYieldSpreadCurveBO> fromDB(@NonNull Long bondUniCode);

    /**
     * 过期时间，单位：毫秒
     *
     * @return long
     */
    protected abstract long expiredTime();

    /**
     * 根据日期获取分数
     *
     * @param date 日期
     * @return {@link Double}
     */
    protected double getScore(@NonNull Date date) {
        return Double.parseDouble(date.toLocalDate().format(DateTimeFormatter.BASIC_ISO_DATE));
    }
}
