package com.innodealing.onshore.yieldspread.processor;

import com.google.common.collect.Lists;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 利差追踪上下文
 *
 * <AUTHOR>
 */
public class YieldSpreadTraceContext {
    /**
     * 发行日期
     */
    private Date issueDate;
    /**
     * 利差追踪债券枚举类型
     */
    private YieldPanoramaBondTypeEnum bondTypeEnum;

    /**
     * 收益率全景-绝对值-国债
     */
    private PgBondYieldPanoramaAbsDO chinaBondYieldPanorama;

    /**
     * 收益率全景-绝对值-国开债
     */
    private PgBondYieldPanoramaAbsDO chinaBondKaiYieldPanorama;
    /**
     * 收益率全景-绝对值-中短期票据
     */
    private List<PgBondYieldPanoramaAbsDO> chinaBondMidYieldPanoramas;
    /**
     * 收益率全景-绝对值-银行普通债
     */
    private List<PgBondYieldPanoramaAbsDO> generalBankBondYieldPanoramas;
    /**
     * 收益率全景-绝对值-证券公司债
     */
    private List<PgBondYieldPanoramaAbsDO> securitiesBondYieldPanoramas;
    /**
     * 收益率全景-绝对值
     */
    private List<PgBondYieldPanoramaAbsDO> absBondYieldPanoramas;

    /**
     * 获取所有参考利差收益率数据
     *
     * @return 曲线收益率数据
     */
    public List<PgBondYieldPanoramaAbsDO> getReferentBondYieldPanoramas() {
        List<PgBondYieldPanoramaAbsDO> pgBondYieldPanoramaAbsDOList = Lists.newArrayList();
        Optional.ofNullable(chinaBondMidYieldPanoramas).ifPresent(pgBondYieldPanoramaAbsDOList::addAll);
        Optional.ofNullable(chinaBondYieldPanorama).ifPresent(pgBondYieldPanoramaAbsDOList::add);
        Optional.ofNullable(chinaBondKaiYieldPanorama).ifPresent(pgBondYieldPanoramaAbsDOList::add);
        Optional.ofNullable(generalBankBondYieldPanoramas).ifPresent(pgBondYieldPanoramaAbsDOList::addAll);
        Optional.ofNullable(securitiesBondYieldPanoramas).ifPresent(pgBondYieldPanoramaAbsDOList::addAll);
        return pgBondYieldPanoramaAbsDOList;
    }


    public Date getIssueDate() {
        return Objects.isNull(issueDate) ? null : new Date(issueDate.getTime());
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = Objects.isNull(issueDate) ? null : new Date(issueDate.getTime());
    }

    public YieldPanoramaBondTypeEnum getBondTypeEnum() {
        return bondTypeEnum;
    }

    public void setBondTypeEnum(YieldPanoramaBondTypeEnum bondTypeEnum) {
        this.bondTypeEnum = bondTypeEnum;
    }


    public PgBondYieldPanoramaAbsDO getChinaBondYieldPanorama() {
        return chinaBondYieldPanorama;
    }

    public void setChinaBondYieldPanorama(PgBondYieldPanoramaAbsDO chinaBondYieldPanorama) {
        this.chinaBondYieldPanorama = chinaBondYieldPanorama;
    }

    public PgBondYieldPanoramaAbsDO getChinaBondKaiYieldPanorama() {
        return chinaBondKaiYieldPanorama;
    }

    public void setChinaBondKaiYieldPanorama(PgBondYieldPanoramaAbsDO chinaBondKaiYieldPanorama) {
        this.chinaBondKaiYieldPanorama = chinaBondKaiYieldPanorama;
    }

    public List<PgBondYieldPanoramaAbsDO> getChinaBondMidYieldPanoramas() {
        return Objects.isNull(chinaBondMidYieldPanoramas) ? new ArrayList<>() : new ArrayList<>(chinaBondMidYieldPanoramas);
    }

    public void setChinaBondMidYieldPanoramas(List<PgBondYieldPanoramaAbsDO> chinaBondMidYieldPanoramas) {
        this.chinaBondMidYieldPanoramas = Objects.isNull(chinaBondMidYieldPanoramas) ? new ArrayList<>() : new ArrayList<>(chinaBondMidYieldPanoramas);
    }

    public List<PgBondYieldPanoramaAbsDO> getGeneralBankBondYieldPanoramas() {
        return Objects.isNull(generalBankBondYieldPanoramas) ? new ArrayList<>() : new ArrayList<>(generalBankBondYieldPanoramas);
    }

    public void setGeneralBankBondYieldPanoramas(List<PgBondYieldPanoramaAbsDO> generalBankBondYieldPanoramas) {
        this.generalBankBondYieldPanoramas = Objects.isNull(generalBankBondYieldPanoramas) ? new ArrayList<>() : new ArrayList<>(generalBankBondYieldPanoramas);
    }

    public List<PgBondYieldPanoramaAbsDO> getSecuritiesBondYieldPanoramas() {
        return Objects.isNull(securitiesBondYieldPanoramas) ? new ArrayList<>() : new ArrayList<>(securitiesBondYieldPanoramas);
    }

    public void setSecuritiesBondYieldPanoramas(List<PgBondYieldPanoramaAbsDO> securitiesBondYieldPanoramas) {
        this.securitiesBondYieldPanoramas = Objects.isNull(securitiesBondYieldPanoramas) ? new ArrayList<>() : new ArrayList<>(securitiesBondYieldPanoramas);
    }

    public List<PgBondYieldPanoramaAbsDO> getAbsBondYieldPanoramas() {
        return Objects.isNull(absBondYieldPanoramas) ? new ArrayList<>() : new ArrayList<>(absBondYieldPanoramas);
    }

    public void setAbsBondYieldPanoramas(List<PgBondYieldPanoramaAbsDO> absBondYieldPanoramas) {
        this.absBondYieldPanoramas = Objects.isNull(absBondYieldPanoramas) ? new ArrayList<>() : new ArrayList<>(absBondYieldPanoramas);
    }
}
