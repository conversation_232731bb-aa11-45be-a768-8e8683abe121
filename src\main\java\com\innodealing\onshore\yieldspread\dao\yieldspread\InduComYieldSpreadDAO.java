package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.QueryHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.InduComYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadChangeBO;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.bo.MixYieldSpreadShortBO;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadQueryParameter;
import com.innodealing.onshore.yieldspread.model.dto.InduYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.ComYieldSpreadChangeDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import com.innodealing.onshore.yieldspread.model.view.InduComYieldSpreadView;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static com.github.wz2cool.dynamic.helper.CommonsHelper.getPropertyName;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.SPREAD_KEEP_SCALE;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.YIELD_SPREAD_KEEP_SCALE;

/**
 * 产业主体利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class InduComYieldSpreadDAO {

    @Resource
    private InduComYieldSpreadMapper induComYieldSpreadMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    private final QueryHelper queryHelper = new QueryHelper();

    private static final Set<String> CHANGE_ORDER_PROPERTY_SET;

    static {
        CHANGE_ORDER_PROPERTY_SET = new HashSet<>();
        CHANGE_ORDER_PROPERTY_SET.add(getPropertyName(ComYieldSpreadChangeDO::getCreditSpreadChange3M));
        CHANGE_ORDER_PROPERTY_SET.add(getPropertyName(ComYieldSpreadChangeDO::getCreditSpreadChange6M));
        CHANGE_ORDER_PROPERTY_SET.add(getPropertyName(ComYieldSpreadChangeDO::getExcessSpreadChange3M));
        CHANGE_ORDER_PROPERTY_SET.add(getPropertyName(ComYieldSpreadChangeDO::getExcessSpreadChange6M));
    }

    /**
     * 查询行业主体利差
     *
     * @param spreadDate     利差日期
     * @param comUniCodeList 主体列表
     * @return ComYieldSpreadChangeBO
     */
    public List<ComYieldSpreadChangeBO> listInduComYieldSpreads(Date spreadDate, Set<Long> comUniCodeList) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<InduComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(InduComYieldSpreadDO.class)
                .select(InduComYieldSpreadDO::getComUniCode, InduComYieldSpreadDO::getSpreadDate,
                        InduComYieldSpreadDO::getComCreditSpread, InduComYieldSpreadDO::getComExcessSpread)
                .and(InduComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(CollectionUtils.isNotEmpty(comUniCodeList),
                        InduComYieldSpreadDO::getComUniCode, in(comUniCodeList));
        return BeanCopyUtils.copyList(induComYieldSpreadMapper.selectByDynamicQuery(groupQuery), ComYieldSpreadChangeBO.class);
    }

    /**
     * 获取产业主体利差某一天的 所有 主题
     *
     * @param spreadDate 利差日期
     * @return comUniCodeList 主体列表
     */
    public List<Long> listInduComYieldSpreadComUniCodes(@NonNull Date spreadDate) {
        DynamicQuery<InduComYieldSpreadDO> query = DynamicQuery.createQuery(InduComYieldSpreadDO.class)
                .select(InduComYieldSpreadDO::getComUniCode)
                .and(InduComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(InduComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return induComYieldSpreadMapper.selectByDynamicQuery(query).stream().map(InduComYieldSpreadDO::getComUniCode).collect(Collectors.toList());
    }

    /**
     * 获取历史分位的统计数据
     *
     * @param startDate      时间范围 开始时间
     * @param endDate        时间范围结束时间
     * @param issueDate      利差日期
     * @param comUniCodeList 主体列表
     * @return 分位统计数据
     */
    public List<ComYieldSpreadQuantileViewDO> listInduComYieldQuantileStatistics(@NonNull Date startDate, @NonNull Date endDate,
                                                                                 @NonNull Date issueDate, List<Long> comUniCodeList) {
        return induComYieldSpreadMapper.listComYieldQuantileStatisticsViews(startDate, endDate, issueDate, comUniCodeList);
    }

    /**
     * 批量更新
     *
     * @param spreadDate               利差日期
     * @param induComYieldSpreadDOList 产业主体利差列表
     * @return 受影响的行数
     */
    public int saveInduComYieldSpreadDOList(Date spreadDate, List<InduComYieldSpreadDO> induComYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(induComYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> comUniCodes = induComYieldSpreadDOList.stream().map(InduComYieldSpreadDO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        AtomicInteger effectRows = new AtomicInteger();
        // 批量查询已存在的数据
        DynamicQuery<InduComYieldSpreadDO> query = DynamicQuery.createQuery(InduComYieldSpreadDO.class)
                .select(InduComYieldSpreadDO::getId, InduComYieldSpreadDO::getComUniCode,
                        InduComYieldSpreadDO::getSpreadDate, InduComYieldSpreadDO::getInduLevel1Code,
                        InduComYieldSpreadDO::getInduLevel1Name, InduComYieldSpreadDO::getInduLevel2Code,
                        InduComYieldSpreadDO::getInduLevel2Name, InduComYieldSpreadDO::getBusinessNature)
                .and(InduComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(InduComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<InduComYieldSpreadDO> existDataList = induComYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(induComYieldSpreadDOList));
        } else {
            Map<String, InduComYieldSpreadDO> existInduComYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                    x.getComUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<InduComYieldSpreadDO> insertList = new ArrayList<>();
            List<InduComYieldSpreadDO> updateList = new ArrayList<>();
            for (InduComYieldSpreadDO induComYieldSpreadDO : induComYieldSpreadDOList) {
                InduComYieldSpreadDO existInduComYieldSpreadDO = existInduComYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                induComYieldSpreadDO.getComUniCode(), induComYieldSpreadDO.getSpreadDate().getTime()));
                if (Objects.isNull(existInduComYieldSpreadDO)) {
                    insertList.add(induComYieldSpreadDO);
                } else {
                    induComYieldSpreadDO.setId(existInduComYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existInduComYieldSpreadDO.getInduLevel1Code(), induComYieldSpreadDO::setInduLevel1Code);
                    ObjectExtensionUtils.ifNonNull(existInduComYieldSpreadDO.getInduLevel1Name(), induComYieldSpreadDO::setInduLevel1Name);
                    ObjectExtensionUtils.ifNonNull(existInduComYieldSpreadDO.getInduLevel2Code(), induComYieldSpreadDO::setInduLevel2Code);
                    ObjectExtensionUtils.ifNonNull(existInduComYieldSpreadDO.getInduLevel2Name(), induComYieldSpreadDO::setInduLevel2Name);
                    ObjectExtensionUtils.ifNonNull(existInduComYieldSpreadDO.getBusinessNature(), induComYieldSpreadDO::setBusinessNature);
                    updateList.add(induComYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 产业主体利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<InduComYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<InduComYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(InduComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (InduComYieldSpreadDO induComYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<InduComYieldSpreadDO> updateQuery = DynamicQuery.createQuery(InduComYieldSpreadDO.class)
                        .and(InduComYieldSpreadDO::getId, isEqual(induComYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(induComYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 产业主体利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<InduComYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<InduComYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(InduComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (InduComYieldSpreadDO induComYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(induComYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 行业主体利差分页查询数据
     *
     * @param comUniCodes 唯一编码集合
     * @param spreadDate  利差日期
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param sort        排序
     * @return {@link NormPagingResult}<{@link InduComYieldSpreadDO}> 分页查询行业主体利差数据响应集合
     */
    public NormPagingResult<InduComYieldSpreadDO> getComYieldSpreadPaging(Collection<Long> comUniCodes, Date spreadDate,
                                                                          int pageNum, int pageSize, SortDTO sort) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return new NormPagingResult<>();
        }
        String sortProperty = queryHelper.getQueryColumnByProperty(InduComYieldSpreadDO.class, sort.getPropertyName());
        String sortDirection = sort.getSortDirection().name();
        Integer startIndex = (pageNum - 1) * pageSize;
        List<InduComYieldSpreadDO> comYieldSpreadList =
                induComYieldSpreadMapper.getComYieldSpreadPaging(comUniCodes, spreadDate, startIndex, pageSize, sortProperty, sortDirection);
        NormPagingResult<InduComYieldSpreadDO> pagingResult = new NormPagingResult<>();
        pagingResult.setPageNum(pageNum);
        pagingResult.setPageSize(pageSize);
        pagingResult.setList(comYieldSpreadList);
        return pagingResult;
    }

    /**
     * 查询主体行业利差数据集合
     *
     * @param comUniCodes 主体唯一编码
     * @param spreadDate  利差日期
     * @return {@link List}<{@link InduComYieldSpreadDO}> 行业利差主体响应数据集
     */
    public List<InduComYieldSpreadDO> listComYieldSpreads(@NonNull Date spreadDate, @NonNull Collection<Long> comUniCodes) {
        DynamicQuery<InduComYieldSpreadDO> query = DynamicQuery.createQuery(InduComYieldSpreadDO.class)
                .and(InduComYieldSpreadDO::getComUniCode, in(comUniCodes))
                .and(InduComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(InduComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return induComYieldSpreadMapper.selectByDynamicQuery(query);
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<InduComYieldSpreadDO> query = DynamicQuery.createQuery(InduComYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt = induComYieldSpreadMapper.selectMaxByDynamicQuery(InduComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 从主库获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDateForMaster() {
        DynamicQuery<InduComYieldSpreadDO> query = DynamicQuery.createQuery(InduComYieldSpreadDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER);
        Optional<java.util.Date> dateOpt = induComYieldSpreadMapper.selectMaxByDynamicQuery(InduComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 行业主体利差分页查询数据
     *
     * @param request 行业主体利差分页查询数据请求参数
     * @return {@link NormPagingResult}<{@link InduComYieldSpreadDO}> 行业主体利差分页查询响应数据
     */
    public List<InduComYieldSpreadDO> getComYieldSpreadPagingByJoin(InduBondYieldSpreadParamDTO request) {
        InduBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(request);
        return induComYieldSpreadMapper.getComYieldSpreadPagingByJoin(parameter);
    }

    /**
     * 行业主体利差分页查询数据-包含变动数据
     *
     * @param request 行业主体利差分页查询数据请求参数
     * @return {@link NormPagingResult}<{@link InduComYieldSpreadDO}> 行业主体利差分页查询响应数据
     */
    public List<InduComYieldSpreadView> getComYieldSpreadChangePagingByJoin(InduBondYieldSpreadParamDTO request) {
        InduBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(request);
        return induComYieldSpreadMapper.getComYieldSpreadChangePagingByJoin(parameter);
    }

    private InduBondYieldSpreadQueryParameter buildPagingQueryParameter(InduBondYieldSpreadParamDTO request) {
        SortDTO sort = request.getSort();
        InduBondYieldSpreadQueryParameter clone = BeanCopyUtils.copyProperties(request, InduBondYieldSpreadQueryParameter.class);
        String propertyName;
        if (Objects.nonNull(sort)) {
            if (CHANGE_ORDER_PROPERTY_SET.contains(sort.getPropertyName())) {
                propertyName = queryHelper.getQueryColumnByProperty(ComYieldSpreadChangeDO.class, sort.getPropertyName());
            } else {
                propertyName = queryHelper.getQueryColumnByProperty(InduComYieldSpreadDO.class, sort.getPropertyName());
            }
            clone.setPropertyName(propertyName);
            clone.setSortDirection(sort.getSortDirection().name());
        }
        Integer startIndex = (request.getPageNum() - 1) * request.getPageSize();
        clone.setStartIndex(startIndex);
        clone.setYear(request.getSpreadDate().toLocalDate().getYear());
        return clone;
    }

    /**
     * 行业主体利差分页查询总数量
     *
     * @param searchParameter 行业主体利差分页查询总数量请求参数
     * @return {@link Long} 总条数
     */
    public Long getComYieldSpreadPagingCount(InduBondYieldSpreadParamDTO searchParameter) {
        InduBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(searchParameter);
        return induComYieldSpreadMapper.getComYieldSpreadPagingCount(parameter);
    }

    /**
     * 行业主体利差分页查询数据
     *
     * @param request 行业主体利差分页查询数据请求参数
     * @return {@link NormPagingResult}<{@link InduComYieldSpreadDO}> 行业主体利差分页查询响应数据
     */
    public NormPagingResult<InduComYieldSpreadDO> getComYieldSpreadPagingByExists(InduBondYieldSpreadParamDTO request) {
        InduBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(request);
        List<InduComYieldSpreadDO> comYieldSpreadList = induComYieldSpreadMapper.getComYieldSpreadPagingByExists(parameter);
        NormPagingResult<InduComYieldSpreadDO> pagingResult = new NormPagingResult<>();
        pagingResult.setPageNum(parameter.getPageNum());
        pagingResult.setPageSize(parameter.getPageSize());
        pagingResult.setList(comYieldSpreadList);
        return pagingResult;
    }

    /**
     * 行业主体利差分页查询数据-包含变动数据
     *
     * @param request 行业主体利差分页查询数据请求参数
     * @return {@link NormPagingResult}<{@link InduComYieldSpreadDO}> 行业主体利差分页查询响应数据
     */
    public NormPagingResult<InduComYieldSpreadView> getComYieldSpreadChangePagingByExists(InduBondYieldSpreadParamDTO request) {
        InduBondYieldSpreadQueryParameter parameter = this.buildPagingQueryParameter(request);
        List<InduComYieldSpreadView> comYieldSpreadList = induComYieldSpreadMapper.getComYieldSpreadChangePagingByExists(parameter);
        NormPagingResult<InduComYieldSpreadView> pagingResult = new NormPagingResult<>();
        pagingResult.setPageNum(parameter.getPageNum());
        pagingResult.setPageSize(parameter.getPageSize());
        pagingResult.setList(comYieldSpreadList);
        return pagingResult;
    }

    /**
     * 查询并计算利差曲线数据-主体利差方式
     *
     * @param comUniCode      发行人唯一编码
     * @param spreadBondType  债券类型, 根据不同类型，查询不同字段，并不是全部返回
     * @param startSpreadDate 利差开始日期
     * @param endSpreadDate   利差结束日期
     * @return {@link List}<{@link BondYieldSpreadCurveBO}> 债券每天中位数数据集合
     */
    public List<BondYieldSpreadCurveBO> listYieldSpreadCurves(@NonNull Long comUniCode, Integer spreadBondType,
                                                              @NonNull Date startSpreadDate, @NonNull Date endSpreadDate) {
        if (!ObjectExtensionUtils.isAllNotEmpty(comUniCode, startSpreadDate, endSpreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<InduComYieldSpreadDO> query = DynamicQuery.createQuery(InduComYieldSpreadDO.class)
                .select(InduComYieldSpreadDO::getSpreadDate)
                .and(InduComYieldSpreadDO::getComUniCode, isEqual(comUniCode))
                .and(InduComYieldSpreadDO::getSpreadDate, greaterThanOrEqual(startSpreadDate))
                .and(InduComYieldSpreadDO::getSpreadDate, lessThanOrEqual(endSpreadDate));
        Optional<SpreadBondTypeEnum> spreadBondTypeEnumOpt = EnumUtils.getEnumByValue(spreadBondType, SpreadBondTypeEnum.class);
        if (!spreadBondTypeEnumOpt.isPresent()) {
            query.addSelectedProperties(getPropertyName(InduComYieldSpreadDO::getComCreditSpread),
                    getPropertyName(InduComYieldSpreadDO::getComExcessSpread), getPropertyName(InduComYieldSpreadDO::getComCbYield));
            List<InduComYieldSpreadDO> comYieldSpreadList = induComYieldSpreadMapper.selectByDynamicQuery(query);
            return comYieldSpreadList.stream().map(com -> {
                BondYieldSpreadCurveBO bondYieldSpreadCurveBO = new BondYieldSpreadCurveBO();
                bondYieldSpreadCurveBO.setSpreadDate(com.getSpreadDate());
                handlePrecision(bondYieldSpreadCurveBO, com.getComCreditSpread(), com.getComExcessSpread(), com.getComCbYield());
                return bondYieldSpreadCurveBO;
            }).collect(Collectors.toList());
        }
        SpreadBondTypeEnum spreadBondTypeEnum = spreadBondTypeEnumOpt.get();
        if (SpreadBondTypeEnum.SPREAD_NOT_PUBLIC_OFFERING.equals(spreadBondTypeEnum)) {
            query.addSelectedProperties(getPropertyName(InduComYieldSpreadDO::getComPrivateCreditSpread),
                    getPropertyName(InduComYieldSpreadDO::getComPrivateExcessSpread), getPropertyName(InduComYieldSpreadDO::getComPrivateCbYield));
            List<InduComYieldSpreadDO> comYieldSpreadList = induComYieldSpreadMapper.selectByDynamicQuery(query);
            return comYieldSpreadList.stream().map(com -> {
                BondYieldSpreadCurveBO bondYieldSpreadCurveBO = new BondYieldSpreadCurveBO();
                bondYieldSpreadCurveBO.setSpreadDate(com.getSpreadDate());
                handlePrecision(bondYieldSpreadCurveBO, com.getComPrivateCreditSpread(), com.getComPrivateExcessSpread(), com.getComPrivateCbYield());
                return bondYieldSpreadCurveBO;
            }).collect(Collectors.toList());
        }
        if (SpreadBondTypeEnum.SPREAD_IS_PUBLIC_OFFERING.equals(spreadBondTypeEnum)) {
            query.addSelectedProperties(getPropertyName(InduComYieldSpreadDO::getComPublicCreditSpread),
                    getPropertyName(InduComYieldSpreadDO::getComPublicExcessSpread), getPropertyName(InduComYieldSpreadDO::getComPublicCbYield));
            List<InduComYieldSpreadDO> comYieldSpreadList = induComYieldSpreadMapper.selectByDynamicQuery(query);
            return comYieldSpreadList.stream().map(com -> {
                BondYieldSpreadCurveBO bondYieldSpreadCurveBO = new BondYieldSpreadCurveBO();
                bondYieldSpreadCurveBO.setSpreadDate(com.getSpreadDate());
                handlePrecision(bondYieldSpreadCurveBO, com.getComPublicCreditSpread(), com.getComPublicExcessSpread(), com.getComPublicCbYield());
                return bondYieldSpreadCurveBO;
            }).collect(Collectors.toList());
        }
        query.addSelectedProperties(getPropertyName(InduComYieldSpreadDO::getComPerpetualCreditSpread),
                getPropertyName(InduComYieldSpreadDO::getComPerpetualExcessSpread), getPropertyName(InduComYieldSpreadDO::getComPerpetualCbYield));
        List<InduComYieldSpreadDO> comYieldSpreadList = induComYieldSpreadMapper.selectByDynamicQuery(query);
        return comYieldSpreadList.stream().map(com -> {
            BondYieldSpreadCurveBO bondYieldSpreadCurveBO = new BondYieldSpreadCurveBO();
            bondYieldSpreadCurveBO.setSpreadDate(com.getSpreadDate());
            handlePrecision(bondYieldSpreadCurveBO, com.getComPerpetualCreditSpread(), com.getComPerpetualExcessSpread(), com.getComPerpetualCbYield());
            return bondYieldSpreadCurveBO;
        }).collect(Collectors.toList());
    }

    private void handlePrecision(BondYieldSpreadCurveBO bondYieldSpreadCurveBO, BigDecimal comCreditSpread, BigDecimal comExcessSpread, BigDecimal comCbYield) {
        BigDecimalUtils.handlerPrecision(comCreditSpread, SPREAD_KEEP_SCALE).ifPresent(bondYieldSpreadCurveBO::setBondCreditSpread);
        BigDecimalUtils.handlerPrecision(comExcessSpread, SPREAD_KEEP_SCALE).ifPresent(bondYieldSpreadCurveBO::setBondExcessSpread);
        BigDecimalUtils.handlerPrecision(comCbYield, YIELD_SPREAD_KEEP_SCALE).ifPresent(bondYieldSpreadCurveBO::setCbYield);
    }

    /**
     * 查询产业主体利差数据集
     *
     * @param spreadDate 利差日期
     * @return {@link List}<{@link ComYieldSpreadShortBO}> 产业主体利差响应数据集
     */
    public List<ComYieldSpreadShortBO> listShortInfosBySpreadDate(Date spreadDate) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<InduComYieldSpreadDO> query = DynamicQuery.createQuery(InduComYieldSpreadDO.class)
                .select(InduComYieldSpreadDO::getSpreadDate,
                        InduComYieldSpreadDO::getComUniCode,
                        InduComYieldSpreadDO::getComCreditSpread,
                        InduComYieldSpreadDO::getComExcessSpread,
                        InduComYieldSpreadDO::getComCbYield)
                .and(InduComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(InduComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return BeanCopyUtils.copyList(induComYieldSpreadMapper.selectByDynamicQuery(query), ComYieldSpreadShortBO.class);
    }

    /**
     * 获取主体利差
     *
     * @param isNewest 是否为最新一天
     * @param param    请求参数
     * @return 主体利差
     */
    public List<InduComYieldSpreadView> listComYieldSpreads(boolean isNewest, InduYieldSearchParam param) {
        return induComYieldSpreadMapper.listComYieldSpreads(isNewest, param);
    }

    /**
     * 获取主体数量
     *
     * @param param 请求参数
     * @return 主体数量
     */
    public Long countComYieldSpread(InduYieldSearchParam param) {
        return induComYieldSpreadMapper.countComYieldSpread(param);
    }

    /**
     * 获取主体利差
     *
     * @param comUniCodes 主体列表
     * @return 主体利差列表
     */
    public List<MixYieldSpreadShortBO> listAllYieldSpreads(List<Long> comUniCodes) {
        if (!ObjectExtensionUtils.isAllNotEmpty(comUniCodes)) {
            return Collections.emptyList();
        }
        DynamicQuery<InduComYieldSpreadDO> query = DynamicQuery.createQuery(InduComYieldSpreadDO.class)
                .select(InduComYieldSpreadDO::getSpreadDate, InduComYieldSpreadDO::getComUniCode,
                        InduComYieldSpreadDO::getComCreditSpread, InduComYieldSpreadDO::getComExcessSpread,
                        InduComYieldSpreadDO::getComPrivateCreditSpread, InduComYieldSpreadDO::getComPrivateExcessSpread,
                        InduComYieldSpreadDO::getComPublicCreditSpread, InduComYieldSpreadDO::getComPublicExcessSpread,
                        InduComYieldSpreadDO::getComPerpetualCreditSpread, InduComYieldSpreadDO::getComPerpetualExcessSpread)
                .and(InduComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<InduComYieldSpreadDO> comYieldSpreadList = induComYieldSpreadMapper.selectByDynamicQuery(query);
        return comYieldSpreadList.stream().map(com -> {
            MixYieldSpreadShortBO mixYieldSpreadShortBO = new MixYieldSpreadShortBO();
            mixYieldSpreadShortBO.setComUniCode(com.getComUniCode());
            mixYieldSpreadShortBO.setSpreadDate(com.getSpreadDate());
            handlePrecision(mixYieldSpreadShortBO, com);
            return mixYieldSpreadShortBO;
        }).collect(Collectors.toList());
    }

    private void handlePrecision(MixYieldSpreadShortBO mixYieldSpreadShortBO, InduComYieldSpreadDO comYieldSpreadDO) {
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setComCreditSpread);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setComExcessSpread);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPublicCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldOne);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPublicExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldOne);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPrivateCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldTwo);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPrivateExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldTwo);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPerpetualCreditSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondCreditYieldTree);
        BigDecimalUtils.handlerPrecision(comYieldSpreadDO.getComPerpetualExcessSpread(), SPREAD_KEEP_SCALE)
                .ifPresent(mixYieldSpreadShortBO::setSecondExcessYieldTree);
    }
}
