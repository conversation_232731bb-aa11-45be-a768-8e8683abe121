package com.innodealing.onshore.yieldspread.config.swagger;

import com.fasterxml.classmate.TypeResolver;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldLgExportReqDTO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import javax.annotation.Resource;

/**
 * swagger 配置
 *
 * <AUTHOR>
 */
@Configuration
public class SwaggerConfiguration {

    @Resource
    private TypeResolver typeResolver;

    /**
     * 解析类型
     *
     * @return docket
     */
    @Bean
    public Docket docket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .additionalModels(typeResolver.resolve(InduPanoramaRequestDTO.class))
                .additionalModels(typeResolver.resolve(InduListRequestDTO.class))
                .additionalModels(typeResolver.resolve(LgSpreadBaseRequestDTO.class))
                .additionalModels(typeResolver.resolve(LgSpreadQuantileRequestDTO.class))
                .additionalModels(typeResolver.resolve(LgSpreadChangeRequestDTO.class))
                .additionalModels(typeResolver.resolve(BondYieldLgExportReqDTO.class))
                .additionalModels(typeResolver.resolve(LgSpreadLineAreaRequestDTO.class))
                .additionalModels(typeResolver.resolve(LgSpreadLinePeriodRequestDTO.class))
                ;
    }

}