package com.innodealing.onshore.yieldspread.model.bo;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.BenchmarkCurveResponseDTO;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * 基准曲线定义信息
 *
 * <AUTHOR>
 */
public class BenchmarkCurveDefinitionBO {

    /**
     * 曲线代码 1:中债国债，4:中债国开债
     */
    private final Integer curveCode;

    /**
     * ytm字段
     */
    private final String ytm;

    /**
     * 获取属性值的function
     */
    private final Function<BenchmarkCurveResponseDTO, BigDecimal> function;

    /**
     * 构造器
     *
     * @param curveCode 曲线代码 1:中债国债，4:中债国开债
     * @param ytm       ytm字段
     * @param function  获取属性值的function
     */
    public BenchmarkCurveDefinitionBO(Integer curveCode, String ytm, Function<BenchmarkCurveResponseDTO, BigDecimal> function) {
        this.curveCode = curveCode;
        this.ytm = ytm;
        this.function = function;
    }

    public Integer getCurveCode() {
        return curveCode;
    }

    public String getYtm() {
        return ytm;
    }

    public Function<BenchmarkCurveResponseDTO, BigDecimal> getFunction() {
        return function;
    }

}


