package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;


/**
 * 曲线请求DTO-关注组类型（城投|行业公用）
 *
 * <AUTHOR>
 * @date 2022-09-26
 */
public class CurveFavoriteGroupDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主体唯一编码集合")
    private Long[] comUniCodes;
    @ApiModelProperty("债券唯一编码集合")
    private Long[] bondUniCodes;
    @ApiModelProperty("曲线名称(导出时前端出入)")
    private String curveName;

    public Long[] getComUniCodes() {
        return Objects.isNull(comUniCodes) ? new Long[0] : comUniCodes.clone();
    }

    public void setComUniCodes(Long[] comUniCodes) {
        this.comUniCodes = Objects.isNull(comUniCodes) ? new Long[0] : comUniCodes.clone();
    }

    public Long[] getBondUniCodes() {
        return Objects.isNull(bondUniCodes) ? new Long[0] : bondUniCodes.clone();
    }

    public void setBondUniCodes(Long[] bondUniCodes) {
        this.bondUniCodes = Objects.isNull(bondUniCodes) ? new Long[0] : bondUniCodes.clone();
    }

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

    @Override
    public String toString() {
        return "CurveFavoriteGroupDTO{" +
                "comUniCodes=" + Arrays.toString(comUniCodes) +
                ", bondUniCodes=" + Arrays.toString(bondUniCodes) +
                '}';
    }
}
