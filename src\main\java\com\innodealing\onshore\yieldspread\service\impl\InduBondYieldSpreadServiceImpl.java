package com.innodealing.onshore.yieldspread.service.impl;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.lambda.GetPropertyFunction;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.constant.RedisKeys;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.CbValuationShortInfoResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.BondImpliedRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComExternalCreditRatingDTO;
import com.innodealing.onshore.bondmetadata.dto.bondrating.response.ComYyRatingDTO;
import com.innodealing.onshore.bondmetadata.enums.*;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondmetadata.utils.RatingUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.dao.dmdc.BondInterestInduDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgInduBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvInduBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.mv.MvInduBondYieldSpreadPanoramaDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.partition.PgInduBondYieldSpreadCurveDAO;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis.InduBondYieldSpreadRedisDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InduBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.InduComYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.enums.GuaranteedStatusEnum;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.CommonUtils;
import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.bo.InduSpreadPanoramaBO;
import com.innodealing.onshore.yieldspread.model.dto.*;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.entity.dmdc.BondInterestInduDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgInduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.YieldSpreadBondDO;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.factory.YyRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.ImplicitRatingRouter;
import com.innodealing.onshore.yieldspread.router.shard.YyRatingRouter;
import com.innodealing.onshore.yieldspread.service.InduBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.RedisService;
import com.innodealing.onshore.yieldspread.service.UdicBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.YieldSpreadCommonService;
import com.innodealing.onshore.yieldspread.service.internal.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.github.wz2cool.dynamic.helper.CommonsHelper.getPropertyName;
import static com.innodealing.commons.object.ObjectExtensionUtils.isAllEmpty;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.*;
import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.*;
import static com.innodealing.onshore.yieldspread.helper.CalculationHelper.subtract;
import static com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper.*;

/**
 * 产业债利差 Service
 *
 * <AUTHOR>
 **/
@SuppressWarnings({"squid:S1854", "squid:S1481", "java:S107", "squid:S00107"})
@Service
public class InduBondYieldSpreadServiceImpl extends AbstractBondCurveService implements InduBondYieldSpreadService {

    private final ExecutorService executorService;

    private final ExecutorService shardExecutorService;

    private static final SortDTO DEFAULT_SORT;

    private static final Comparator<InduPanoramaResponseDTO> DEFAULT_COMPARATOR;

    private static final Map<String, Map<SortDirection, Comparator<InduPanoramaResponseDTO>>> COMPARATOR_MAP;

    private static final Comparator<BigDecimal> NULLS_LAST = Comparator.nullsLast(BigDecimal::compareTo);

    private static final Comparator<BigDecimal> NULLS_FIRST = Comparator.nullsFirst(BigDecimal::compareTo);

    protected InduBondYieldSpreadServiceImpl() {
        executorService = new ThreadPoolExecutor(WORK_THREAD_NUM, WORK_THREAD_NUM, 0,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("InduBondYieldSpreadServiceImpl-pool-").build());

        shardExecutorService = TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(SHARD_WORK_THREAD_NUM, SHARD_WORK_THREAD_NUM, 0,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("ShardInduBondYieldSpreadServiceImpl-pool-").build()));
    }

    @Resource
    private InduBondYieldSpreadDAO induBondYieldSpreadDAO;

    @Resource
    private PgInduBondYieldSpreadDAO pgInduBondYieldSpreadDAO;

    @Resource
    private BondPriceService bondPriceService;

    @Resource
    private BondRatingService bondRatingService;

    @Value("${sharding.yield.spread}")
    private Date initStartDate;

    @Resource
    private RedisService redisService;

    @Resource
    private ComService comService;

    @Resource
    private BondInterestInduDAO bondInterestInduDAO;

    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;

    @Resource
    private UdicBondYieldSpreadService udicBondYieldSpreadService;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private MvInduBondYieldSpreadPanoramaDAO mvInduBondYieldSpreadPanoramaDAO;

    @Resource
    private PgInduBondYieldSpreadCurveDAO pgInduBondYieldSpreadCurveDAO;

    @Resource
    private MvInduBondYieldSpreadCurveDAO mvInduBondYieldSpreadCurveDAO;

    @Resource
    private InduComYieldSpreadDAO induComYieldSpreadDAO;

    @Resource
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;

    @Resource
    private YyRatingRouterFactory yyRatingRouterFactory;

    @Resource
    private InduBondYieldSpreadRedisDAO induBondYieldSpreadRedisDAO;

    @Resource
    private UserService userService;

    private static final Set<String> HISTORY_ORDER_FIELD_SET;

    static {
        COMPARATOR_MAP = Maps.newConcurrentMap();
        HISTORY_ORDER_FIELD_SET = new HashSet<>();
        DEFAULT_COMPARATOR = Comparator.comparing(InduPanoramaResponseDTO::getBondCreditSpread, NULLS_FIRST.reversed());
        COMPARATOR_MAP.put(getPropertyName(InduPanoramaResponseDTO::getBondCreditSpreadChange90),
                buildComparatorMap(InduPanoramaResponseDTO::getBondCreditSpreadChange90));
        COMPARATOR_MAP.put(getPropertyName(InduPanoramaResponseDTO::getBondCreditSpreadChange180),
                buildComparatorMap(InduPanoramaResponseDTO::getBondCreditSpreadChange180));
        COMPARATOR_MAP.put(getPropertyName(InduPanoramaResponseDTO::getBondExcessSpread),
                buildComparatorMap(InduPanoramaResponseDTO::getBondExcessSpread));
        COMPARATOR_MAP.put(getPropertyName(InduPanoramaResponseDTO::getBondExcessSpreadChange90),
                buildComparatorMap(InduPanoramaResponseDTO::getBondExcessSpreadChange90));
        COMPARATOR_MAP.put(getPropertyName(InduPanoramaResponseDTO::getBondExcessSpreadChange180),
                buildComparatorMap(InduPanoramaResponseDTO::getBondExcessSpreadChange180));
        COMPARATOR_MAP.put(getPropertyName(InduPanoramaResponseDTO::getBondCreditSpread),
                buildComparatorMap(InduPanoramaResponseDTO::getBondCreditSpread));
        DEFAULT_SORT = new SortDTO(getPropertyName(InduBondYieldSpreadResponseDTO::getBondCreditSpread), SortDirection.DESC);
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduBondYieldSpreadResponseDTO::getBondCreditSpread));
        HISTORY_ORDER_FIELD_SET.add(getPropertyName(InduBondYieldSpreadResponseDTO::getBondExcessSpread));
    }

    private static Map<SortDirection, Comparator<InduPanoramaResponseDTO>> buildComparatorMap(
            GetPropertyFunction<InduPanoramaResponseDTO, BigDecimal> function) {
        Map<SortDirection, Comparator<InduPanoramaResponseDTO>> comparatorMap = Maps.newHashMap();
        comparatorMap.put(SortDirection.ASC, Comparator.comparing(function, NULLS_LAST));
        comparatorMap.put(SortDirection.DESC, Comparator.comparing(function, NULLS_FIRST.reversed()));
        return comparatorMap;
    }

    /**
     * 检查分片
     */
    //@Scheduled(initialDelay = 1000, fixedDelay = 3_600_000)
    public void checkShardingTables() {
        Date currentDate = new Date(System.currentTimeMillis());
        Date endDate = new Date(DateUtils.addDays(currentDate, 1).getTime());
        createShardingTables(initStartDate, endDate);
    }

    private void createShardingTables(Date startDate, Date endDate) {
        Collection<String> shardingTableNames = ShardingUtils.getYearShardingTableNames(INDU_TABLE_NAME, startDate, endDate);
        for (String shardingTableName : shardingTableNames) {
            induBondYieldSpreadDAO.createShardingTable(shardingTableName);
        }
    }

    @Override
    public int calcInduBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOs,
                                                    Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                                    Date spreadDate, Boolean isEnableOldData) {
        if (CollectionUtils.isEmpty(onshoreBondInfoDTOs)) {
            return 0;
        }
        Set<Long> bondUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> comUniCodes = onshoreBondInfoDTOs.stream().map(OnshoreBondInfoDTO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        SwThreadPoolWorker<Object> of = SwThreadPoolWorker.of(executorService);
        CompletableFuture<Map<Long, ComShortInfoDTO>> submitComShortInfoDTO = of.submit(() ->
                comService.getComShortInfoByUniCodeMap(comUniCodes));
        CompletableFuture<Map<Long, CbValuationShortInfoResponseDTO>> submitCbValuationShortInfo = of.submit(() ->
                bondPriceService.getCbValuationShortInfoMap(spreadDate, bondUniCodes));
        CompletableFuture<Map<Long, BondImpliedRatingDTO>> submitBondImpliedRatingDTO = of.submit(() ->
                bondRatingService.getBondImpliedRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, BondExternalCreditRatingDTO>> submitBondExternalCreditRatingDTO = of.submit(() ->
                bondRatingService.getBondExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), bondUniCodes));
        CompletableFuture<Map<Long, ComYyRatingDTO>> submitComYyRatingDTO = of.submit(() ->
                bondRatingService.getComYyRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        CompletableFuture<Map<Long, ComExternalCreditRatingDTO>> submitComExternalCreditRatingDTO = of.submit(() ->
                bondRatingService.getComExternalCreditRatingMap(new Timestamp(spreadDate.getTime()), comUniCodes));
        CompletableFuture<List<BondInterestInduDO>> submitBondInterestInduDO = of.submit(() ->
                bondInterestInduDAO.listBondInterestInduDOByInterestDate(spreadDate, bondUniCodes));
        of.doWorks(submitComShortInfoDTO, submitCbValuationShortInfo, submitBondImpliedRatingDTO, submitBondExternalCreditRatingDTO,
                submitComYyRatingDTO, submitComExternalCreditRatingDTO, submitBondInterestInduDO);
        // 获取主体信息
        Map<Long, ComShortInfoDTO> comShortInfoDTOMap = submitComShortInfoDTO.join();
        // 获取中债估值信息
        Map<Long, CbValuationShortInfoResponseDTO> cbMap = submitCbValuationShortInfo.join();
        // 获取评级信息
        Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap = submitBondImpliedRatingDTO.join();
        Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap = submitBondExternalCreditRatingDTO.join();
        Map<Long, ComYyRatingDTO> comYyRatingMap = submitComYyRatingDTO.join();
        Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap = submitComExternalCreditRatingDTO.join();
        return parseInduBondYieldSpreadDO(onshoreBondInfoDTOs, comShortInfoDTOMap, cbMap,
                bondImpliedRatingMap, bondExternalCreditRatingMap, comExternalCreditRatingMap, comYyRatingMap,
                bondYieldCurveMap, spreadDate, submitBondInterestInduDO.join(), isEnableOldData);
    }

    private int parseInduBondYieldSpreadDO(List<OnshoreBondInfoDTO> onshoreBondInfos,
                                           Map<Long, ComShortInfoDTO> comShortInfoMap,
                                           Map<Long, CbValuationShortInfoResponseDTO> cbMap,
                                           Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap,
                                           Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                           Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap,
                                           Map<Long, ComYyRatingDTO> comYyRatingMap,
                                           Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                           Date spreadDate, List<BondInterestInduDO> bondInterestInduDOList,
                                           boolean isEnableOldData) {
        Map<Long, BondInterestInduDO> bondInterestCtzDOMap = bondInterestInduDOList.stream()
                .collect(Collectors.toMap(BondInterestInduDO::getBondUniCode, Function.identity(), (x1, x2) -> x2));
        List<InduBondYieldSpreadDO> induBondYieldSpreadDOs = new ArrayList<>();
        List<YieldSpreadBondDO> yieldSpreadBonds = Lists.newArrayList();
        for (OnshoreBondInfoDTO onshoreBondInfoDTO : onshoreBondInfos) {
            InduBondYieldSpreadDO induBondYieldSpreadDO = BeanCopyUtils.copyProperties(onshoreBondInfoDTO, InduBondYieldSpreadDO.class);
            induBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getPublicOffering());
            induBondYieldSpreadDO.setSpreadDate(spreadDate);
            if (!DateExtensionUtils.isSameDay(spreadDate, YieldSpreadHelper.getYesterDay())) {
                induBondYieldSpreadDO.setLatestCouponRate(null);
                induBondYieldSpreadDO.setBondBalance(null);
            }
            if (Objects.equals(onshoreBondInfoDTO.getEmbeddedOption(), EmbeddedOption.PERPETUAL.getValue())) {
                induBondYieldSpreadDO.setSpreadBondType(onshoreBondInfoDTO.getEmbeddedOption());
            }
            // 利差剩余期限标签
            induBondYieldSpreadDO.setSpreadRemainingTenorTag(YieldSpreadHelper.
                    getSpreadRemainingTenorTag(induBondYieldSpreadDO.getRemainingTenorDay()));
            induBondYieldSpreadDO = fillComInfoColumn(induBondYieldSpreadDO, comShortInfoMap);
            induBondYieldSpreadDO = fillRatingColumn(induBondYieldSpreadDO, bondImpliedRatingMap, bondExternalCreditRatingMap,
                    comExternalCreditRatingMap, comYyRatingMap);
            induBondYieldSpreadDO = fillCbColumn(induBondYieldSpreadDO, cbMap);
            induBondYieldSpreadDO = fillLerpYieldColumn(induBondYieldSpreadDO, bondYieldCurveMap, onshoreBondInfoDTO.getBondType());
            induBondYieldSpreadDO = fillSpreadColumn(induBondYieldSpreadDO, bondYieldCurveMap);
            if (isEnableOldData) {
                induBondYieldSpreadDO = fillOldColumn(induBondYieldSpreadDO, bondInterestCtzDOMap);
            }
            //这里如果超额利差和信用利差都为空的情况下 过滤掉
            if (Objects.nonNull(induBondYieldSpreadDO.getBondCreditSpread()) ||
                    Objects.nonNull(induBondYieldSpreadDO.getBondExcessSpread())) {
                induBondYieldSpreadDO.setId(redisService.generatePk(RedisKeys.YIELD_SPREAD_INDU_BOND_YIELD_SPREAD_FLOW_ID,
                        induBondYieldSpreadDO.getSpreadDate()));
                induBondYieldSpreadDO.setDeleted(0);
                induBondYieldSpreadDOs.add(induBondYieldSpreadDO);
                YieldSpreadBondDO yieldSpreadBondDO = BeanCopyUtils.copyProperties(induBondYieldSpreadDO, YieldSpreadBondDO.class);
                yieldSpreadBondDO.setComSpreadSector(ComYieldSpreadSectorEnum.INDU.getValue());
                yieldSpreadBonds.add(yieldSpreadBondDO);
            }
        }
        induBondYieldSpreadDAO.saveInduBondYieldSpreadDOList(spreadDate, spreadDate, induBondYieldSpreadDOs);
        super.saveYieldSpreadBonds(yieldSpreadBonds);
        return savePgInduBondYieldSpreadDOLists(spreadDate, induBondYieldSpreadDOs);
    }

    private int savePgInduBondYieldSpreadDOLists(Date spreadDate, List<InduBondYieldSpreadDO> induBondYieldSpreadDOs) {
        if (CollectionUtils.isEmpty(induBondYieldSpreadDOs)) {
            return 0;
        }
        List<PgInduBondYieldSpreadDO> pgInduBondYieldSpreadDOs = induBondYieldSpreadDOs.stream().map(x -> {
            PgInduBondYieldSpreadDO result = BeanCopyUtils.copyProperties(x, PgInduBondYieldSpreadDO.class);
            if (Objects.isNull(result.getGuaranteedStatus())) {
                // gp表的担保字段 null 转换为0
                result.setGuaranteedStatus(0);
            }
            if (Objects.nonNull(result.getBondImpliedRatingMapping())) {
                result.setBondImpliedRatingMappingTag(getBondImpliedRatingMappingTagMap().get(result.getBondImpliedRatingMapping()));
            }
            if (Objects.nonNull(result.getComYyRatingMapping())) {
                result.setComYyRatingMappingTag(getComYyRatingMappingTagMap().get(result.getComYyRatingMapping()));
            }
            if (Objects.isNull(result.getBondImpliedRatingMappingTag())) {
                result.setBondImpliedRatingMappingTag(BondImpliedRatingMappingTagTypeEnum.OTHER.getValue());
            }
            if (Objects.isNull(result.getComYyRatingMappingTag())) {
                result.setComYyRatingMappingTag(ComYyRatingMappingTagTypeEnum.OTHER.getValue());
            }
            return result;
        }).collect(Collectors.toList());
        return pgInduBondYieldSpreadDAO.savePgInduBondYieldSpreadDOList(spreadDate, pgInduBondYieldSpreadDOs);

    }

    /**
     * 填充发行人信息
     *
     * @param induBondYieldSpreadDO 产业债利差
     * @param comShortInfoMap       主体信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private InduBondYieldSpreadDO fillComInfoColumn(InduBondYieldSpreadDO induBondYieldSpreadDO,
                                                    Map<Long, ComShortInfoDTO> comShortInfoMap) {
        InduBondYieldSpreadDO result = BeanCopyUtils.copyProperties(induBondYieldSpreadDO, InduBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(comShortInfoMap)) {
            return result;
        }
        ComShortInfoDTO comShortInfoDTO = comShortInfoMap.get(result.getComUniCode());
        if (Objects.nonNull(comShortInfoDTO)) {
            //主体基础信息
            result.setBusinessNature(comShortInfoDTO.getBusinessNature());
            result.setBusinessFilterNature(
                    BusinessNatureEnum.getBusinessFilterNatureEnum(comShortInfoDTO.getBusinessNature()).getValue()
            );
            result.setInduLevel1Code(comShortInfoDTO.getInduLevel1Code());
            result.setInduLevel1Name(comShortInfoDTO.getInduLevel1Name());
            result.setInduLevel2Code(comShortInfoDTO.getInduLevel2Code());
            result.setInduLevel2Name(comShortInfoDTO.getInduLevel2Name());
        }
        return result;
    }

    /**
     * 填充中债估值相关信息
     *
     * @param induBondYieldSpreadDO 产业债利差
     * @param cbMap                 中债估值
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private InduBondYieldSpreadDO fillCbColumn(InduBondYieldSpreadDO induBondYieldSpreadDO,
                                               Map<Long, CbValuationShortInfoResponseDTO> cbMap) {
        InduBondYieldSpreadDO result = BeanCopyUtils.copyProperties(induBondYieldSpreadDO, InduBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(cbMap)) {
            return result;
        }
        CbValuationShortInfoResponseDTO cb = cbMap.get(induBondYieldSpreadDO.getBondUniCode());
        if (Objects.nonNull(cb)) {
            result.setCbYield(cb.getYield());
        }
        return result;
    }

    /**
     * 填充评级相关信息
     *
     * @param induBondYieldSpreadDO       产业债利差
     * @param bondImpliedRatingMap        债券隐含评级
     * @param bondExternalCreditRatingMap 主体外部评级
     * @param comExternalCreditRatingMap  主体YY评级
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private InduBondYieldSpreadDO fillRatingColumn(InduBondYieldSpreadDO induBondYieldSpreadDO,
                                                   Map<Long, BondImpliedRatingDTO> bondImpliedRatingMap,
                                                   Map<Long, BondExternalCreditRatingDTO> bondExternalCreditRatingMap,
                                                   Map<Long, ComExternalCreditRatingDTO> comExternalCreditRatingMap,
                                                   Map<Long, ComYyRatingDTO> comYyRatingMap) {
        InduBondYieldSpreadDO result = BeanCopyUtils.copyProperties(induBondYieldSpreadDO, InduBondYieldSpreadDO.class);
        if (ObjectUtils.isNotEmpty(bondImpliedRatingMap)) {
            BondImpliedRatingDTO bondImpliedRatingDTO = bondImpliedRatingMap.get(induBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondImpliedRatingDTO)) {
                result.setBondImpliedRatingMapping(bondImpliedRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(bondExternalCreditRatingMap)) {
            BondExternalCreditRatingDTO bondExternalCreditRatingDTO = bondExternalCreditRatingMap.
                    get(induBondYieldSpreadDO.getBondUniCode());
            if (Objects.nonNull(bondExternalCreditRatingDTO)) {
                result.setBondExtRatingMapping(bondExternalCreditRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(comExternalCreditRatingMap)) {
            ComExternalCreditRatingDTO comExternalCreditRatingDTO = comExternalCreditRatingMap.get(induBondYieldSpreadDO.getComUniCode());
            if (Objects.nonNull(comExternalCreditRatingDTO)) {
                result.setComExtRatingMapping(comExternalCreditRatingDTO.getRatingMapping());
            }
        }
        if (ObjectUtils.isNotEmpty(comYyRatingMap)) {
            ComYyRatingDTO comYyRatingDTO = comYyRatingMap.get(induBondYieldSpreadDO.getComUniCode());
            if (Objects.nonNull(comYyRatingDTO)) {
                result.setComYyRatingMapping(comYyRatingDTO.getRatingMapping());
            }
        }
        return result;
    }

    /**
     * 填充插值收益率
     *
     * @param induBondYieldSpreadDO 产业债利差
     * @param bondYieldCurveMap     收益率曲线信息
     * @param bondType              债券类型
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private InduBondYieldSpreadDO fillLerpYieldColumn(InduBondYieldSpreadDO induBondYieldSpreadDO,
                                                      Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                                      Integer bondType) {
        InduBondYieldSpreadDO result = BeanCopyUtils.copyProperties(induBondYieldSpreadDO, InduBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(bondYieldCurveMap)) {
            return result;
        }
        //国开插值收益率;单位(%)
        BondYieldCurveDTO bondYieldCurveDTO = bondYieldCurveMap.get(YieldSpreadHelper.CDB_YIELD_CURVE);
        if (Objects.nonNull(bondYieldCurveDTO)) {
            BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(bondYieldCurveDTO, BondYieldCurveBO.class);
            result.setCdbLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
        }
        //隐含评级对应曲线插值收益率;单位(%)
        if (Objects.nonNull(result.getBondImpliedRatingMapping())) {
            //如果bond_type_par = 12 ，计算超额利差时，我们取曲线12
            BondYieldCurveDTO impliedRatingLerpYieldCurveDTO;
            if (Objects.equals(bondType, BondType.COMMERCIAL_BANK_ORDINARY_BONDS.getValue())) {
                impliedRatingLerpYieldCurveDTO = bondYieldCurveMap
                        .get(CurveCode.CHINA_BOND_ORD.getValue());
            } else {
                impliedRatingLerpYieldCurveDTO = bondYieldCurveMap
                        .get(YieldSpreadHelper.getBondYieldCurveInduMap().get(result.getBondImpliedRatingMapping()));
            }
            if (Objects.nonNull(impliedRatingLerpYieldCurveDTO)) {
                BondYieldCurveBO bondYieldCurveBO = BeanCopyUtils.copyProperties(impliedRatingLerpYieldCurveDTO, BondYieldCurveBO.class);
                result.setImpliedRatingLerpYield(YieldSpreadHelper.getLerpYield(result.getRemainingTenorDay(), bondYieldCurveBO));
            }
        }
        return result;
    }

    /**
     * 填充利差数据
     *
     * @param induBondYieldSpreadDO 产业债利差
     * @param bondYieldCurveMap     收益率曲线信息
     * @return com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO
     * <AUTHOR>
     */
    private InduBondYieldSpreadDO fillSpreadColumn(InduBondYieldSpreadDO induBondYieldSpreadDO,
                                                   Map<Integer, BondYieldCurveDTO> bondYieldCurveMap) {
        InduBondYieldSpreadDO result = BeanCopyUtils.copyProperties(induBondYieldSpreadDO, InduBondYieldSpreadDO.class);
        if (ObjectUtils.isEmpty(bondYieldCurveMap)) {
            return result;
        }
        if (Objects.nonNull(result.getCbYield())) {
            if (Objects.nonNull(result.getCdbLerpYield())
                    && result.getCdbLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondCreditSpread(result.getCbYield()
                        .subtract(result.getCdbLerpYield())
                        .multiply(YieldSpreadHelper.BP_WEIGHT).setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.nonNull(result.getImpliedRatingLerpYield()) &&
                    result.getImpliedRatingLerpYield().compareTo(BigDecimal.ZERO) > 0) {
                result.setBondExcessSpread(result.getCbYield()
                        .subtract(result.getImpliedRatingLerpYield())
                        .multiply(YieldSpreadHelper.BP_WEIGHT).setScale(YieldSpreadHelper.SPREAD_KEEP_SCALE, BigDecimal.ROUND_HALF_UP));
                result.setExcessSpreadStatus(0);
            } else {
                result.setExcessSpreadStatus(1);
            }
        }
        return result;
    }

    private InduBondYieldSpreadDO fillOldColumn(InduBondYieldSpreadDO induBondYieldSpreadDO,
                                                Map<Long, BondInterestInduDO> bondInterestCtzDOMap) {
        InduBondYieldSpreadDO result = BeanCopyUtils.copyProperties(induBondYieldSpreadDO, InduBondYieldSpreadDO.class);
        if (org.springframework.util.CollectionUtils.isEmpty(bondInterestCtzDOMap)) {
            return result;
        }
        BondInterestInduDO bondInterestInduDO = bondInterestCtzDOMap.get(result.getBondUniCode());
        if (Objects.nonNull(bondInterestInduDO)) {
            result.setInduLevel1Code(bondInterestInduDO.getIndustryCode1());
            result.setInduLevel1Name(bondInterestInduDO.getIndustryName1());
            result.setInduLevel2Code(bondInterestInduDO.getIndustryCode2());
            result.setInduLevel2Name(bondInterestInduDO.getIndustryName2());
            result.setBusinessNature(bondInterestInduDO.getEnterpriseType());
            // 这里原来的字段没有 所以我们通过  BusinessNature 字段映射
            if (Objects.nonNull(result.getBusinessNature())) {
                result.setBusinessFilterNature(
                        BusinessNatureEnum.getBusinessFilterNatureEnum(result.getBusinessNature()).getValue()
                );
            }
        }
        return result;
    }

    @Override
    public Date getMaxSpreadDate() {
        String maxSpreadDate = stringRedisTemplate.opsForValue().get(MAX_INDU_BOND_SPREAD_DATE_KEY);
        if (StringUtils.isNotBlank(maxSpreadDate)) {
            return Date.valueOf(maxSpreadDate);
        }
        Optional<Date> maxSpreadDateOpt = pgInduBondYieldSpreadDAO.getMaxSpreadDate();
        if (!maxSpreadDateOpt.isPresent()) {
            return Date.valueOf(LocalDate.now());
        }
        Date spreadDate = maxSpreadDateOpt.get();
        stringRedisTemplate.opsForValue().set(MAX_INDU_BOND_SPREAD_DATE_KEY, spreadDate.toString(), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return spreadDate;
    }

    @Override
    public void refreshMvInduBondYieldSpreadPanorama() {
        mvInduBondYieldSpreadPanoramaDAO.refreshMvInduBondYieldSpreadPanorama();
    }

    @Override
    public List<IndustryResponseDTO> listIndustries() {
        return YieldSpreadHelper.getIndustryResponseList();
    }

    @Override
    public NormPagingResult<InduBondYieldSpreadResponseDTO> getBondYieldSpreadPaging(InduListRequestDTO request) {
        Date spreadDate = request.getSpreadDate();
        SortDTO sort = request.getSort();
        boolean isToday = false;
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
            isToday = true;
        }
        if (!isToday) {
            sort = Objects.nonNull(sort) && HISTORY_ORDER_FIELD_SET.contains(sort.getPropertyName()) ? sort : DEFAULT_SORT;
        }
        // 转换请求DTO
        InduBondYieldSpreadParamDTO searchParameter = InduBondYieldSpreadParamDTO.builder()
                .spreadDateOrDefault(spreadDate, Date.valueOf(LocalDate.now()))
                .sortOrDefault(sort, DEFAULT_SORT)
                .page(request.getPageNum(), request.getPageSize())
                .searchCode(request.getCodeType(), request.getSearchCode())
                .comUniCodes(request.getComUniCodes())
                .bondUniCodes(request.getBondUniCodes())
                .compositionCondition(request.getCompositionCondition()).build();
        return listSingleBondYieldSpreads(searchParameter);
    }

    private NormPagingResult<InduBondYieldSpreadResponseDTO> listSingleBondYieldSpreads(InduBondYieldSpreadParamDTO searchParameter) {
        NormPagingResult<InduBondYieldSpreadDO> pagingResult = induBondYieldSpreadDAO.getBondYieldSpreadPaging(searchParameter);
        if (CollectionUtils.isEmpty(pagingResult.getList())) {
            return pagingResult.convert(bond -> BeanCopyUtils.copyProperties(bond, InduBondYieldSpreadResponseDTO.class));
        }
        Long[] bondUniCodes = pagingResult.getList().stream().map(InduBondYieldSpreadDO::getBondUniCode).distinct().toArray(Long[]::new);
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        return pagingResult.convert(bond -> {
            InduBondYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(bond, InduBondYieldSpreadResponseDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            response.setCdbLerpYield(CommonUtils.formatDecimal(bond.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            String ratingStr = RatingUtils.getRating(bond.getComExtRatingMapping()) + "/" + RatingUtils.getRating(bond.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            response.setBondExtRating(RatingUtils.getRating(response.getBondExtRatingMapping()));
            EnumUtils.getEnumByValue(response.getGuaranteedStatus(), GuaranteedStatusEnum.class).ifPresent(guarantee -> response.setGuaranteedStatusText(guarantee.getText()));
            response.setBondImpliedRating(RatingUtils.getRating(response.getBondImpliedRatingMapping()));
            BusinessNatureEnum.getBusinessNature(response.getBusinessNature())
                    .ifPresent(nature -> response.setBusinessNatureName(nature.getText()));
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            return response;
        });
    }

    @Override
    public List<InduCurveResponseDTO> listCurves(InduCurveRequestDTO request) {
        InduBondYieldSpreadParamDTO searchParameter = InduBondYieldSpreadParamDTO.builder()
                .compositionCondition(request.getCompositionCondition())
                .comSpread(request.getComSpread()).bondSpread(request.getBondSpread())
                .spanSpreadDate(request.getStartSpreadDate(), request.getEndSpreadDate()).build();
        // 1. 从缓存中拿数据
        String cacheKey = String.format(LIST_INDU_SPREAD_CURVES_KEY, request.toString().hashCode());
        logger.info("curve data cacheKey:{}", cacheKey);
        List<InduCurveResponseDTO> cacheResponses = redisService.listInduCurvesFromCache(cacheKey);
        if (CollectionUtils.isNotEmpty(cacheResponses)) {
            return cacheResponses;
        }
        SpreadRequestTypeEnum requestType = ITextValueEnum.getEnum(SpreadRequestTypeEnum.class, request.getSpreadRequestType());
        // 2. 组合查询方式，查询并计算中位数
        List<BondYieldSpreadCurveBO> response;
        if (SpreadRequestTypeEnum.GROUP_CONDITION.equals(requestType)) {
            response = this.listCurvesForMultiCondition(searchParameter);
        } else if (SpreadRequestTypeEnum.COM_SPREAD.equals(requestType)) {
            // 3. 主体利差查询方式，查询并计算中位数
            response = this.listCurvesForComSpread(searchParameter);
        } else {
            // 4. 单券利差查询方式，查询数据，不需要计算中位数
            response = this.listCurvesForBondSpread(searchParameter);
        }
        response.sort(Comparator.comparing(BondYieldSpreadCurveBO::getSpreadDate));
        redisService.set(cacheKey, JSON.toJSONString(response), CACHE_ONE_DAYS, TimeUnit.DAYS);
        return BeanCopyUtils.copyList(response, InduCurveResponseDTO.class);
    }

    @Override
    public void refreshMvInduBondYieldSpreadCurve() {
        mvInduBondYieldSpreadCurveDAO.refreshMvInduBondYieldSpreadCurve();
        pgInduBondYieldSpreadCurveDAO.refreshMvInduBondYieldSpreadCurveFromMV();
    }

    /**
     * 每日刷新
     */
    @Override
    public void refreshMvInduBondYieldSpreadRatingCurve(RefreshYieldCurveParam param) {
        SwThreadPoolWorker<Object> swThreadPoolWorker = SwThreadPoolWorker.of(shardExecutorService);
        swThreadPoolWorker.doWorks(Stream.of(YieldSpreadCurveShardEnum.ALL.getText(), YieldSpreadCurveShardEnum.INDU_LEVEL_1.getText(),
                        YieldSpreadCurveShardEnum.INDU_LEVEL_2.getText())
                .map(operatorLevel -> swThreadPoolWorker.submit(() -> refresh(param, operatorLevel))).toArray(CompletableFuture[]::new));
    }

    /**
     * 初始化历史所有
     */
    @Override
    public void refreshMvInduBondYieldSpreadRatingCurveHistory(Boolean isTableRefresh) {
        final List<AbstractRatingRouter.SpreadDateRange> dateRangeList = splitDateRange(LocalDate.parse("2019-06-01"), LocalDate.now().minusDays(1));
        SwThreadPoolWorker<Object> swThreadPoolWorker = SwThreadPoolWorker.of(shardExecutorService);
        swThreadPoolWorker.doWorks(dateRangeList.stream().flatMap(dateRange -> {
            final RefreshYieldCurveParam param = RefreshYieldCurveParam.builder().dateRange(dateRange).initRefresh(true, isTableRefresh).build();
            return Stream.of(
                    swThreadPoolWorker.submit(() -> refresh(param, YieldSpreadCurveShardEnum.ALL.getText())),
                    swThreadPoolWorker.submit(() -> refresh(param, YieldSpreadCurveShardEnum.INDU_LEVEL_1.getText())),
                    swThreadPoolWorker.submit(() -> refresh(param, YieldSpreadCurveShardEnum.INDU_LEVEL_2.getText()))
            );
        }).toArray(CompletableFuture[]::new));
    }

    private void refresh(RefreshYieldCurveParam param, String operator) {
        this.refreshMvInduBondYieldSpreadBondYyRatingMappingCurve(operator, param);
        this.refreshMvInduBondYieldSpreadBondImpliedRatingMappingCurve(operator, param);
    }

    private void refreshMvInduBondYieldSpreadBondImpliedRatingMappingCurve(String induLevel, RefreshYieldCurveParam param) {
        Set<String> induImplicitRatingCombination = RatingCombinationHelper.getInduImplicitRatingCombination();
        Set<ImplicitRatingRouter> implicitRatingRouters = implicitRatingRouterFactory.newRatingRouterList(induImplicitRatingCombination);
        for (ImplicitRatingRouter implicitRatingRouter : implicitRatingRouters) {
            implicitRatingRouter.setLevel(induLevel);
            try {
                Optional.ofNullable(param)
                        .ifPresent(p -> implicitRatingRouter.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
                mvInduBondYieldSpreadCurveDAO.createOrRefreshInduCurveMv(implicitRatingRouter, param);
                pgInduBondYieldSpreadCurveDAO.syncCurveShardInduForMv(implicitRatingRouter, param);
            } finally {
                mvInduBondYieldSpreadCurveDAO.droTempMv(implicitRatingRouter);
            }
        }
    }

    private void refreshMvInduBondYieldSpreadBondYyRatingMappingCurve(String induLevel, RefreshYieldCurveParam param) {
        Set<String> induInvestmentRatingCombination = RatingCombinationHelper.getInduInvestmentRatingCombination();
        Set<YyRatingRouter> yyRatingRouters = yyRatingRouterFactory.newRatingRouterList(induInvestmentRatingCombination);
        for (YyRatingRouter yyRatingRouter : yyRatingRouters) {
            yyRatingRouter.setLevel(induLevel);
            try {
                Optional.ofNullable(param)
                        .ifPresent(p -> yyRatingRouter.setSpreadDateRange(p.getStartDate(), p.getEndDate()));
                mvInduBondYieldSpreadCurveDAO.createOrRefreshInduCurveMv(yyRatingRouter, param);
                pgInduBondYieldSpreadCurveDAO.syncCurveShardInduForMv(yyRatingRouter, param);
            } finally {
                mvInduBondYieldSpreadCurveDAO.droTempMv(yyRatingRouter);
            }
        }
    }

    @Override
    public void refreshCurveYesterday() {
        mvInduBondYieldSpreadCurveDAO.refreshCurveYesterday();
        pgInduBondYieldSpreadCurveDAO.syncCurveIncrFromMV();
    }

    private List<BondYieldSpreadCurveBO> listCurvesForBondSpread(InduBondYieldSpreadParamDTO searchParameter) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("list_indu_curves_for_bond_spread_task");
        final Long bondUniCode = searchParameter.getBondUniCode();
        final Date startSpreadDate = searchParameter.getStartSpreadDate();
        final Date endSpreadDate = searchParameter.getEndSpreadDate();
        List<BondYieldSpreadCurveBO> curveList =
                pgInduBondYieldSpreadDAO.listYieldSpreadCurvesByBond(bondUniCode, startSpreadDate, endSpreadDate);
        stopWatch.stop();
        logger.info(SPEND_TIME_LOG, stopWatch.getLastTaskName(), stopWatch.getTotalTimeMillis());
        return curveList;
    }

    private List<BondYieldSpreadCurveBO> listCurvesForComSpread(InduBondYieldSpreadParamDTO searchParameter) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("list_indu_curves_for_com_spread_task");
        final Long comUniCode = searchParameter.getComUniCode();
        final Integer spreadBondType = searchParameter.getSpreadBondType();
        final Date startSpreadDate = searchParameter.getStartSpreadDate();
        final Date endSpreadDate = searchParameter.getEndSpreadDate();
        List<BondYieldSpreadCurveBO> curveList = induComYieldSpreadDAO.listYieldSpreadCurves(comUniCode, spreadBondType, startSpreadDate, endSpreadDate);
        if (CollectionUtils.isEmpty(curveList)) {
            throw new TipsException(NOT_HAS_BONDS_MSG);
        }
        stopWatch.stop();
        logger.info(SPEND_TIME_LOG, stopWatch.getLastTaskName(), stopWatch.getTotalTimeMillis());
        Predicate<BondYieldSpreadCurveBO> anyOneNotEmptyPre =
                curve -> !isAllEmpty(curve.getBondCreditSpread(), curve.getBondExcessSpread(), curve.getCbYield());
        List<BondYieldSpreadCurveBO> responses = curveList.stream().filter(anyOneNotEmptyPre).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(responses)) {
            throw new TipsException(NOT_HAS_BONDS_MSG);
        }
        return responses;
    }

    private List<BondYieldSpreadCurveBO> listCurvesForMultiCondition(InduBondYieldSpreadParamDTO searchParameter) {
        StopWatch stopWatch = new StopWatch();
        List<BondYieldSpreadCurveBO> responses;
        final Long[] bondUniCodes = searchParameter.getBondUniCodes();
        final Long[] comUniCodes = searchParameter.getComUniCodes();
        final Integer[] businessFilterNatures = searchParameter.getBusinessFilterNatures();
        // 1. 从分区表中取数据
        if (isAllEmpty(bondUniCodes, comUniCodes, businessFilterNatures)) {
            stopWatch.start("list_indu_curves_for_multi_condition_from_partition_task");
            responses = this.listCurvesForMultiConditionFromPartition(searchParameter);
        } else {
            // 2. 从基表(postgresql)中取数据
            stopWatch.start("list_indu_curves_for_multi_condition_from_pg_task");
            responses = this.listCurvesForMultiConditionFromPG(searchParameter);
        }
        stopWatch.stop();
        logger.info(SPEND_TIME_LOG, stopWatch.getLastTaskName(), stopWatch.getTotalTimeMillis());
        if (CollectionUtils.isEmpty(responses)) {
            throw new TipsException(NOT_HAS_FIVE_BONDS_MSG);
        }
        return responses;
    }

    private List<BondYieldSpreadCurveBO> listCurvesForMultiConditionFromPartition(InduBondYieldSpreadParamDTO searchParameter) {
        List<BondYieldSpreadCurveBO> curvesList = pgInduBondYieldSpreadCurveDAO.listYieldSpreadCurves(searchParameter);
        if (CollectionUtils.isEmpty(curvesList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> responseList = Lists.newArrayList();
        for (BondYieldSpreadCurveBO curve : curvesList) {
            BondYieldSpreadCurveBO matchCurve = new BondYieldSpreadCurveBO();
            matchCurve.setSpreadDate(curve.getSpreadDate());
            if (isMatch(curve.getBondCreditSpread(), curve.getBondCreditSpreadCount())) {
                matchCurve.setBondCreditSpread(curve.getBondCreditSpread());
            }
            if (isMatch(curve.getBondExcessSpread(), curve.getBondExcessSpreadCount())) {
                matchCurve.setBondExcessSpread(curve.getBondExcessSpread());
            }
            if (isMatch(curve.getCbYield(), curve.getCbYieldCount())) {
                matchCurve.setCbYield(curve.getCbYield());
            }
            if (isMatch(curve.getCdbLerpYield(), curve.getCdbLerpYieldCount())) {
                matchCurve.setCdbLerpYield(curve.getCdbLerpYield());
            }
            if (!isAllEmpty(matchCurve.getBondCreditSpread(), matchCurve.getBondExcessSpread(), matchCurve.getCbYield(), matchCurve.getCdbLerpYield())) {
                responseList.add(matchCurve);
            }
        }
        return responseList;
    }

    private boolean isMatch(BigDecimal value, Integer count) {
        return Objects.nonNull(value) && Objects.nonNull(count) && count >= MIN_BOND_SIZE;
    }

    private List<BondYieldSpreadCurveBO> listCurvesForMultiConditionFromPG(InduBondYieldSpreadParamDTO searchParameter) {
        List<BondYieldSpreadCurveBO> curvesGroupingList = pgInduBondYieldSpreadDAO.listYieldSpreadCurvesByMultiCondition(searchParameter);
        if (CollectionUtils.isEmpty(curvesGroupingList)) {
            return Collections.emptyList();
        }
        List<BondYieldSpreadCurveBO> responseList = Lists.newArrayList();
        for (BondYieldSpreadCurveBO curve : curvesGroupingList) {
            BondYieldSpreadCurveBO matchCurve = new BondYieldSpreadCurveBO();
            matchCurve.setSpreadDate(curve.getSpreadDate());
            if (isMatch(curve.getBondCreditSpread(), curve.getBondCreditSpreadCount())) {
                matchCurve.setBondCreditSpread(curve.getBondCreditSpread());
            }
            if (isMatch(curve.getBondExcessSpread(), curve.getBondExcessSpreadCount())) {
                matchCurve.setBondExcessSpread(curve.getBondExcessSpread());
            }
            if (isMatch(curve.getCbYield(), curve.getCbYieldCount())) {
                matchCurve.setCbYield(curve.getCbYield());
            }
            if (isMatch(curve.getCdbLerpYield(), curve.getCdbLerpYieldCount())) {
                matchCurve.setCdbLerpYield(curve.getCdbLerpYield());
            }
            if (!isAllEmpty(matchCurve.getBondCreditSpread(), matchCurve.getBondExcessSpread(), matchCurve.getCbYield(), matchCurve.getCdbLerpYield())) {
                responseList.add(matchCurve);
            }
        }
        return responseList;
    }

    @Override
    public List<InduPanoramaResponseDTO> listPanoramas(InduPanoramaRequestDTO req) {
        Date spreadDate = req.getSpreadDate();
        if (Objects.isNull(spreadDate) || DateExtensionUtils.isSameDay(spreadDate, new Date(System.currentTimeMillis()))) {
            spreadDate = this.getMaxSpreadDate();
        }
        InduPanoramaRequestDTO request = BeanCopyUtils.copyProperties(req, InduPanoramaRequestDTO.class);
        request.setSpreadDate(spreadDate);
        // 从缓存中取数据
        String cacheKey = String.format(LIST_INDU_SPREAD_PANORAMAS_KEY, request.toString().hashCode());
        List<InduPanoramaResponseDTO> cacheDataList = this.listPanoramasFromRedis(cacheKey, request.getSort());
        if (CollectionUtils.isNotEmpty(cacheDataList)) {
            return cacheDataList;
        }
        // 从物化视图中查数据
        List<InduPanoramaDTO> panoramaList = this.listPanoramasFromBasicTable(request);
        // 查询整个城投数据,作为一个行业
        udicBondYieldSpreadService.getInduPanorama(request).ifPresent(panoramaList::add);
        List<InduPanoramaResponseDTO> responseList = BeanCopyUtils.copyList(panoramaList, InduPanoramaResponseDTO.class);
        this.doSort(request.getSort(), responseList);
        stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(panoramaList), CACHE_FOUR_HOURS, TimeUnit.HOURS);
        return responseList;
    }

    /**
     * 从redis中查询行业利差全景数据
     *
     * @param cacheKey 缓存键
     * @param sort     排序DTO
     * @return {@link List}<{@link InduPanoramaResponseDTO}> 利差全景数据响应集
     */
    private List<InduPanoramaResponseDTO> listPanoramasFromRedis(String cacheKey, SortDTO sort) {
        String value = stringRedisTemplate.opsForValue().get(cacheKey);
        return Optional.ofNullable(value).map(val -> {
            List<InduPanoramaDTO> panoramaList = JSON.parseArray(val, InduPanoramaDTO.class);
            List<InduPanoramaResponseDTO> responses = BeanCopyUtils.copyList(panoramaList, InduPanoramaResponseDTO.class);
            this.doSort(sort, responses);
            return responses;
        }).orElse(Collections.emptyList());
    }

    /**
     * 从基础表列表查询行业全景图数据
     *
     * @param request 请求DTO
     * @return {@link List}<{@link InduPanoramaDTO}> 行业全景响应数据集
     */
    private List<InduPanoramaDTO> listPanoramasFromBasicTable(InduPanoramaRequestDTO request) {
        List<InduPanoramaDTO> responses = Lists.newArrayList();
        // 1. 根据所传日期计算3个月前、6个月前的日期,需要过滤掉假期，如果是假期，则往前推最近的工作日
        SpreadDateDTO spreadDateDTO = yieldSpreadCommonService.getSpreadDate(request.getSpreadDate());
        // 2. 查询指定日期pg基表数据
        Long[] indu2Unicodes = YieldSpreadHelper.getIndu2UnicodeList().toArray(new Long[0]);
        InduCurveCompositionConditionDTO compositionConditionDTO = BeanCopyUtils.copyProperties(request, InduCurveCompositionConditionDTO.class);
        InduBondYieldSpreadParamDTO searchParameter = InduBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spreadDateOrDefault(request.getSpreadDate(), Date.valueOf(LocalDate.now())).build();
        List<InduSpreadPanoramaBO> bondYieldIndu1Spreads = pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu1(searchParameter);
        List<InduSpreadPanoramaBO> bondYieldIndu2Spreads = pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu2(searchParameter, indu2Unicodes);
        if (CollectionUtils.isEmpty(bondYieldIndu1Spreads) && CollectionUtils.isEmpty(bondYieldIndu2Spreads)) {
            return responses;
        }
        // 3. 查询3个月前gp基表数据
        InduBondYieldSpreadParamDTO before90SearchParameter = InduBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spreadDateOrDefault(spreadDateDTO.getBefore90Date(), Date.valueOf(LocalDate.now())).build();
        List<InduSpreadPanoramaBO> before90bondYieldIndu1Spreads = pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu1(before90SearchParameter);
        List<InduSpreadPanoramaBO> before90bondYieldIndu2Spreads = pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu2(before90SearchParameter, indu2Unicodes);
        // 4. 查询6个月前gp基表数据
        InduBondYieldSpreadParamDTO before180SearchParameter = InduBondYieldSpreadParamDTO.builder().compositionCondition(compositionConditionDTO)
                .spreadDateOrDefault(spreadDateDTO.getBefore180Date(), Date.valueOf(LocalDate.now())).build();
        List<InduSpreadPanoramaBO> before180bondYieldIndu1Spreads = pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu1(before180SearchParameter);
        List<InduSpreadPanoramaBO> before180bondYieldIndu2Spreads = pgInduBondYieldSpreadCurveDAO.listYieldSpreadPanoramasForIndu2(before180SearchParameter, indu2Unicodes);
        // 5. 分组，聚合
        Map<Long, InduSpreadPanoramaBO> indu1Map = indu1ToMap(bondYieldIndu1Spreads);
        Map<Long, InduSpreadPanoramaBO> indu2Map = indu2ToMap(bondYieldIndu2Spreads);
        Map<Long, InduSpreadPanoramaBO> before90Indu1Map = indu1ToMap(before90bondYieldIndu1Spreads);
        Map<Long, InduSpreadPanoramaBO> before90Indu2Map = indu2ToMap(before90bondYieldIndu2Spreads);
        Map<Long, InduSpreadPanoramaBO> before180Indu1Map = indu1ToMap(before180bondYieldIndu1Spreads);
        Map<Long, InduSpreadPanoramaBO> before180Indu2Map = indu2ToMap(before180bondYieldIndu2Spreads);
        for (Map.Entry<Long, InduSpreadPanoramaBO> entry : indu1Map.entrySet()) {
            responses.add(toPanoramaResponseDTO(request.getSpreadDate(), before90Indu1Map, before180Indu1Map, entry));
        }
        List<InduPanoramaDTO> indu2ResponseList = indu2Map.entrySet().stream()
                .map(entry -> toPanoramaResponseDTO(request.getSpreadDate(), before90Indu2Map, before180Indu2Map, entry))
                .collect(Collectors.toList());
        responses.addAll(indu2ResponseList);
        return responses;
    }

    private Map<Long, InduSpreadPanoramaBO> indu1ToMap(List<InduSpreadPanoramaBO> yieldSpreads) {
        return yieldSpreads.stream().filter(spread -> Objects.nonNull(spread.getInduLevel1Code()))
                .collect(Collectors.toMap(InduSpreadPanoramaBO::getInduLevel1Code, spread -> spread, (o, v) -> o));
    }

    private Map<Long, InduSpreadPanoramaBO> indu2ToMap(List<InduSpreadPanoramaBO> yieldSpreads) {
        return yieldSpreads.stream().filter(spread -> Objects.nonNull(spread.getInduLevel2Code()))
                .collect(Collectors.toMap(InduSpreadPanoramaBO::getInduLevel2Code, spread -> spread, (o, v) -> o));
    }

    private InduPanoramaDTO toPanoramaResponseDTO(Date spreadDate,
                                                  Map<Long, InduSpreadPanoramaBO> before90InduLevelCodeMap,
                                                  Map<Long, InduSpreadPanoramaBO> before180InduLevelCodeMap,
                                                  Map.Entry<Long, InduSpreadPanoramaBO> curInduLevelCodeMap) {
        Long indu1Code = curInduLevelCodeMap.getKey();
        InduSpreadPanoramaBO spread = curInduLevelCodeMap.getValue();
        Optional<InduSpreadPanoramaBO> before90SpreadOpt = Optional.ofNullable(before90InduLevelCodeMap.get(indu1Code));
        Optional<InduSpreadPanoramaBO> before180SpreadOpt = Optional.ofNullable(before180InduLevelCodeMap.get(indu1Code));
        InduPanoramaDTO response = new InduPanoramaDTO();
        response.setIndustryCode(indu1Code);
        response.setIndustryName(YieldSpreadHelper.getInduUnicodeMap().get(indu1Code));
        response.setSpreadDate(spreadDate);
        if (Objects.nonNull(spread.getBondCreditSpreadCount()) && spread.getBondCreditSpreadCount() >= MIN_BOND_SIZE) {
            response.setBondCreditSpread(spread.getBondCreditSpread());
        }
        if (Objects.nonNull(spread.getBondExcessSpreadCount()) && spread.getBondExcessSpreadCount() >= MIN_BOND_SIZE) {
            response.setBondExcessSpread(spread.getBondExcessSpread());
        }
        before90SpreadOpt.ifPresent(before90 -> {
            response.setBondCreditSpreadBefore90(before90.getBondCreditSpread());
            BigDecimal creditChange90 = subtract(response.getBondCreditSpread(), before90.getBondCreditSpread()).orElse(null);
            response.setBondCreditSpreadChange90(creditChange90);
            response.setBondExcessSpreadBefore90(before90.getBondExcessSpread());
            BigDecimal excessChange90 = subtract(response.getBondExcessSpread(), before90.getBondExcessSpread()).orElse(null);
            response.setBondExcessSpreadChange90(excessChange90);
        });
        before180SpreadOpt.ifPresent(before180 -> {
            response.setBondCreditSpreadBefore180(before180.getBondCreditSpread());
            BigDecimal creditChange180 = subtract(response.getBondCreditSpread(), before180.getBondCreditSpread()).orElse(null);
            response.setBondCreditSpreadChange180(creditChange180);
            response.setBondExcessSpreadBefore180(before180.getBondExcessSpread());
            BigDecimal excessChange180 = subtract(response.getBondExcessSpread(), before180.getBondExcessSpread()).orElse(null);
            response.setBondExcessSpreadChange180(excessChange180);
        });
        return response;
    }

    private void doSort(SortDTO sort, List<InduPanoramaResponseDTO> responses) {
        Comparator<InduPanoramaResponseDTO> comparator = Optional.ofNullable(sort)
                .map(so -> COMPARATOR_MAP.get(sort.getPropertyName()).get(sort.getSortDirection()))
                .orElse(DEFAULT_COMPARATOR);
        responses.sort(comparator);
    }

    @Override
    public List<CurveDataResDTO> listCurveData(Long curveId) {
        InduCurveGenerateConditionReqDTO conditionDTO = super.getCurveGenerateCondition(curveId, InduCurveGenerateConditionReqDTO.class);
        return listCurveData(conditionDTO);
    }

    @Override
    protected List<CurveDataResDTO> listCurveData(ICurveGenerateReq curveGenerateParam) {
        InduCurveGenerateConditionReqDTO conditionDTO = (InduCurveGenerateConditionReqDTO) curveGenerateParam;
        ComOrBondConditionReqDTO comOrBondCondition = conditionDTO.getComOrBondCondition();
        boolean isComOrBond = Objects.nonNull(comOrBondCondition) && Objects.nonNull(comOrBondCondition.getConditionType());
        boolean isRealTimeCalculate = isComOrBond || CollectionUtils.isNotEmpty(conditionDTO.getBusinessFilterNatures());
        InduYieldSearchParam searchParam = convertToInduYieldSearchParam(conditionDTO);
        List<BondYieldSpreadBO> yieldSpreads;
        if (isRealTimeCalculate) {
            if (isComOrBond) {
                searchParam.setComOrBondUniCode(comOrBondCondition.getConditionType(), comOrBondCondition.getUniCode());
            }
            yieldSpreads = pgInduBondYieldSpreadDAO.listBondYieldSpreads(searchParam);
        } else {
            yieldSpreads = pgInduBondYieldSpreadCurveDAO.listBondYieldSpreads(searchParam);
        }
        return super.convertToCurveDataResDTOsAndFilterData(yieldSpreads, super.getMinSampleBondSize(isComOrBond));
    }

    private InduYieldSearchParam convertToInduYieldSearchParam(InduCurveGenerateConditionReqDTO conditionDTO) {
        InduYieldSearchParam induYieldSearchParam = BeanCopyUtils.copyProperties(conditionDTO, InduYieldSearchParam.class);
        Long industryCode = conditionDTO.getIndustryCode();
        if (Objects.nonNull(industryCode)) {
            if (getIndu2UnicodeList().contains(industryCode)) {
                induYieldSearchParam.setIndustryCode2(industryCode);
            } else {
                induYieldSearchParam.setIndustryCode1(industryCode);
            }
        }
        return induYieldSearchParam;
    }

    @Override
    public boolean saveCurve(Long userid, Long curveGroupId, InduCurveGenerateConditionReqDTO request) {
        return super.generalSaveCurve(userid, curveGroupId, request);
    }

    @Override
    public boolean updateCurve(Long userid, Long curveId, InduCurveGenerateConditionReqDTO request) {
        return super.generalUpdateCurve(userid, curveId, request);
    }

    @Override
    public NormPagingResult<InduBondYieldSpreadResponseDTO> pagingSingleBondYieldSpreads(Long userid, YieldSpreadSearchReqDTO request) {
        InduCurveGenerateConditionReqDTO generateRequest = super.getCurveGenerateCondition(userid, request.getCurveId(), InduCurveGenerateConditionReqDTO.class);
        if (super.isOutOfSearchRange(request.getUniCode(), request.getUniCodeType(), generateRequest.getComOrBondCondition())) {
            return new NormPagingResult<>();
        }
        InduYieldSearchParam param = super
                .buildBondYieldSearchParam(request, generateRequest, InduYieldSearchParam.class, ObjectExtensionUtils.getOrDefault(request.getSort(), DEFAULT_SORT));
        Long industryCode = generateRequest.getIndustryCode();
        if (Objects.nonNull(industryCode)) {
            if (getIndu2UnicodeList().contains(industryCode)) {
                param.setIndustryCode2(industryCode);
            } else {
                param.setIndustryCode1(industryCode);
            }
        }
        InduBondYieldSpreadParamDTO searchParam = BeanCopyUtils.copyProperties(param, InduBondYieldSpreadParamDTO.class);
        searchParam.setGuaranteeStatus(param.getGuaranteedStatus());
        searchParam.setSpreadRemainingTenorTag(param.getRemainingTenor());
        searchParam.setBusinessFilterNatures(param.getBusinessFilterNatures().toArray(new Integer[0]));
        NormPagingResult<InduBondYieldSpreadResponseDTO> pagingResult = listSingleBondYieldSpreads(searchParam);
        // 权限控制
        pagingResult.setList(this.permissionProcessing(userid, request.getSpreadDate(), pagingResult.getList()));
        return pagingResult;
    }

    private List<InduBondYieldSpreadResponseDTO> permissionProcessing(Long userid, Date spreadDate, List<InduBondYieldSpreadResponseDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (DateExtensionUtils.isSameDay(spreadDate, Date.valueOf(LocalDate.now()))) {
            boolean hasImpliedRatingPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_IMPLIED_RATING);
            Map<Long, Boolean> bondTodayCbPermissionMap = userService
                    .getBondTodayCbPermissionMap(userid, list.stream().map(InduBondYieldSpreadResponseDTO::getBondUniCode).collect(Collectors.toList()));
            for (InduBondYieldSpreadResponseDTO yieldSpread : list) {
                yieldSpread.setBondImpliedRating(CommonUtils.desensitized(yieldSpread.getBondImpliedRating(), hasImpliedRatingPermission));
                Boolean hasCbPermission = bondTodayCbPermissionMap.get(yieldSpread.getBondUniCode());
                yieldSpread.setCbYieldStr(CommonUtils.formatOrDesensitized(yieldSpread.getCbYield(), FOUR_DECIMAL_PLACE, Objects.equals(hasCbPermission, Boolean.TRUE)));
                yieldSpread.setCbYield(null);
            }
        }
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();

        final boolean hasPermission = spreadDate.before(cbPermissionDividingDate) ?
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE) :
                userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        for (InduBondYieldSpreadResponseDTO yieldSpread : list) {
            yieldSpread.setCdbLerpYield(CommonUtils.desensitized(yieldSpread.getCdbLerpYield(), hasPermission));
            yieldSpread.setImpliedRatingLerpYield(CommonUtils.desensitized(yieldSpread.getImpliedRatingLerpYield(), hasPermission));
        }

        return list;
    }

    @Override
    public List<BondYieldSpreadCurveDTO> curves(Long bondUniCode, Date startDate, Date endDate) {
        return induBondYieldSpreadRedisDAO.listCurves(bondUniCode, startDate, endDate);
    }

    @Override
    public List<InduBondYieldSpreadResponseDTO> listBonds(Date spreadDate, @Nullable Set<Long> bondUniCodes) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        spreadDate = super.isToday(spreadDate) ? this.getMaxSpreadDate() : spreadDate;
        List<InduBondYieldSpreadDO> induBondYieldSpreadDOList = induBondYieldSpreadDAO.listInduBondYieldSpreads(spreadDate, bondUniCodes);
        if (CollectionUtils.isEmpty(induBondYieldSpreadDOList)) {
            return Collections.emptyList();
        }
        Map<Long, BondShortInfoDTO> bondShortInfoMap = bondInfoService.listBondShortInfoListByUniCodes(bondUniCodes.toArray(new Long[0])).
                stream().collect(Collectors.toMap(BondShortInfoDTO::getBondUniCode, Function.identity(), (o, v) -> o));
        List<InduBondYieldSpreadResponseDTO> responseList = Lists.newArrayListWithExpectedSize(induBondYieldSpreadDOList.size());
        for (InduBondYieldSpreadDO bond : induBondYieldSpreadDOList) {
            InduBondYieldSpreadResponseDTO response = BeanCopyUtils.copyProperties(bond, InduBondYieldSpreadResponseDTO.class);
            if (bondShortInfoMap.containsKey(response.getBondUniCode())) {
                BondShortInfoDTO bondShortInfo = bondShortInfoMap.get(response.getBondUniCode());
                response.setBondShortName(bondShortInfo.getBondShortName());
                response.setComUniName(bondShortInfo.getComFullName());
            }
            response.setCdbLerpYield(CommonUtils.formatDecimal(bond.getCdbLerpYield(), FOUR_DECIMAL_PLACE));
            String ratingStr = RatingUtils.getRating(bond.getComExtRatingMapping()) + "/" + RatingUtils.getRating(bond.getBondExtRatingMapping());
            response.setRatingStr(ratingStr);
            response.setComExtRating(RatingUtils.getRating(response.getComExtRatingMapping()));
            response.setBondExtRating(RatingUtils.getRating(response.getBondExtRatingMapping()));
            EnumUtils.getEnumByValue(response.getGuaranteedStatus(), GuaranteedStatusEnum.class)
                    .ifPresent(guarantee -> response.setGuaranteedStatusText(guarantee.getText()));
            response.setBondImpliedRating(RatingUtils.getRating(response.getBondImpliedRatingMapping()));
            BusinessNatureEnum.getBusinessNature(response.getBusinessNature())
                    .ifPresent(nature -> response.setBusinessNatureName(nature.getText()));
            CalculationHelper.divideTenThousand(response.getBondBalance()).ifPresent(response::setBondBalance);
            responseList.add(response);
        }
        return responseList;
    }

    @Override
    protected CurveTypeEnum getCurveType() {
        return CurveTypeEnum.INDU;
    }

}
