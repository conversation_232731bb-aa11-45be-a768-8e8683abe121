package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * 证券债利差
 *
 * <AUTHOR>
 **/
@Table(name = "secu_bond_yield_spread")
public class SecuBondYieldSpreadDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;
    /**
     * 债券统一编码
     */
    @Column
    private Long bondUniCode;
    /**
     * 债券编码
     */
    @Column
    private String bondCode;
    /**
     * 一级行业编码
     */
    @Column
    private Long induLevel1Code;
    /**
     * 一级行业名称
     */
    @Column
    private String induLevel1Name;
    /**
     * 二级行业编码
     */
    @Column
    private Long induLevel2Code;
    /**
     * 二级行业名称
     */
    @Column
    private String induLevel2Name;
    /**
     * 企业类型(性质)
     */
    @Column
    private Integer businessNature;
    /**
     * 证券公司债求偿顺序   1:普通（剔除永续）、2:次级（剔除永续）、 3:永续"
     */
    @Column
    private Integer securitySeniorityRanking;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 债券信用利差;单位(BP)
     */
    @Column
    private BigDecimal bondCreditSpread;
    /**
     * 债券超额利差;单位(BP)
     */
    @Column
    private BigDecimal bondExcessSpread;
    /**
     * 中债收益率;单位(%)
     */
    @Column
    private BigDecimal cbYield;
    /**
     * 国开插值收益率;单位(%)
     */
    @Column
    private BigDecimal cdbLerpYield;
    /**
     * 隐含评级对应曲线插值收益率;单位(%)
     */
    @Column
    private BigDecimal impliedRatingLerpYield;
    /**
     * 超额利差数据状态 0有效 1没有评级曲线
     */
    @Column
    private Integer excessSpreadStatus;
    /**
     * 债券外部评级映射
     */
    @Column
    private Integer bondExtRatingMapping;
    /**
     * 债项隐含评级映射
     */
    @Column
    private Integer bondImpliedRatingMapping;
    /**
     * yy评级(投资级:1、2、3、4、5,投机级:6、7、8、风险级:9、10)
     */
    @Column
    private Integer comYyRatingMapping;
    /**
     * 主体外部评级映射
     */
    @Column
    private Integer comExtRatingMapping;
    /**
     * 利差债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer spreadBondType;
    /**
     * 利差剩余期限标签
     */
    @Column
    private Integer spreadRemainingTenorTag;
    /**
     * 剩余期限
     */
    @Column
    private String remainingTenor;
    /**
     * 剩余期限天数
     */
    @Column
    private Integer remainingTenorDay;
    /**
     * 最新票面利率;单位(%)
     */
    @Column
    private BigDecimal latestCouponRate;
    /**
     * 债券余额(万)
     */
    @Column
    private BigDecimal bondBalance;
    /**
     * 是否删除：0： 未删除;1：已删除
     */
    @Column
    private Integer deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }

    public String getInduLevel1Name() {
        return induLevel1Name;
    }

    public void setInduLevel1Name(String induLevel1Name) {
        this.induLevel1Name = induLevel1Name;
    }

    public Long getInduLevel2Code() {
        return induLevel2Code;
    }

    public void setInduLevel2Code(Long induLevel2Code) {
        this.induLevel2Code = induLevel2Code;
    }

    public String getInduLevel2Name() {
        return induLevel2Name;
    }

    public void setInduLevel2Name(String induLevel2Name) {
        this.induLevel2Name = induLevel2Name;
    }

    public Integer getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(Integer businessNature) {
        this.businessNature = businessNature;
    }

    public Integer getSecuritySeniorityRanking() {
        return securitySeniorityRanking;
    }

    public void setSecuritySeniorityRanking(Integer securitySeniorityRanking) {
        this.securitySeniorityRanking = securitySeniorityRanking;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }

    public BigDecimal getCdbLerpYield() {
        return cdbLerpYield;
    }

    public void setCdbLerpYield(BigDecimal cdbLerpYield) {
        this.cdbLerpYield = cdbLerpYield;
    }

    public BigDecimal getImpliedRatingLerpYield() {
        return impliedRatingLerpYield;
    }

    public void setImpliedRatingLerpYield(BigDecimal impliedRatingLerpYield) {
        this.impliedRatingLerpYield = impliedRatingLerpYield;
    }

    public Integer getExcessSpreadStatus() {
        return excessSpreadStatus;
    }

    public void setExcessSpreadStatus(Integer excessSpreadStatus) {
        this.excessSpreadStatus = excessSpreadStatus;
    }

    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public Integer getBondImpliedRatingMapping() {
        return bondImpliedRatingMapping;
    }

    public void setBondImpliedRatingMapping(Integer bondImpliedRatingMapping) {
        this.bondImpliedRatingMapping = bondImpliedRatingMapping;
    }

    public Integer getComYyRatingMapping() {
        return comYyRatingMapping;
    }

    public void setComYyRatingMapping(Integer comYyRatingMapping) {
        this.comYyRatingMapping = comYyRatingMapping;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public Integer getSpreadBondType() {
        return spreadBondType;
    }

    public void setSpreadBondType(Integer spreadBondType) {
        this.spreadBondType = spreadBondType;
    }

    public Integer getSpreadRemainingTenorTag() {
        return spreadRemainingTenorTag;
    }

    public void setSpreadRemainingTenorTag(Integer spreadRemainingTenorTag) {
        this.spreadRemainingTenorTag = spreadRemainingTenorTag;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public Integer getRemainingTenorDay() {
        return remainingTenorDay;
    }

    public void setRemainingTenorDay(Integer remainingTenorDay) {
        this.remainingTenorDay = remainingTenorDay;
    }

    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
    
}