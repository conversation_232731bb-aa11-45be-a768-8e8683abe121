package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

/**
 * 地方债区域利差数据
 *
 * <AUTHOR>
 */
public class YieldLgDataResponseDTO extends YieldPeriodDataResponseDTO {

    @ApiModelProperty("发行人代码")
    private Long comUniCode;
    @ApiModelProperty("地方债地区")
    private String lgAreaName;

    @JsonIgnore
    private Integer areaSort;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getLgAreaName() {
        return lgAreaName;
    }

    public void setLgAreaName(String lgAreaName) {
        this.lgAreaName = lgAreaName;
    }

    public Integer getAreaSort() {
        return areaSort;
    }

    public void setAreaSort(Integer areaSort) {
        this.areaSort = areaSort;
    }
}
