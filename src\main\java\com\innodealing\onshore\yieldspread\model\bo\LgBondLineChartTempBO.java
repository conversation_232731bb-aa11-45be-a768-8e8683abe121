package com.innodealing.onshore.yieldspread.model.bo;

import java.util.List;

/**
 * 地方债曲线数据 处理过程用的对象
 *
 * <AUTHOR>
 * @date 2024/11/18 20:08
 **/
public class LgBondLineChartTempBO {

    /**
     * 类型code
     */
    private Integer typeCode;

    /**
     * 游标位置
     */
    private Integer cursor;

    /**
     * 数据列表
     */
    private List<String> dataList;

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public Integer getCursor() {
        return cursor;
    }

    public void setCursor(Integer cursor) {
        this.cursor = cursor;
    }

    public List<String> getDataList() {
        return dataList;
    }

    public void setDataList(List<String> dataList) {
        this.dataList = dataList;
    }
}
