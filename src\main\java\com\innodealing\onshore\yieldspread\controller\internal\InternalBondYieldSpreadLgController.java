package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.enums.SpreadQuantileTypeEnum;
import com.innodealing.onshore.yieldspread.service.CalcYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.LgYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.List;
import java.util.Optional;

/**
 * (内部) 地方债利差
 *
 * <AUTHOR>
 * @create: 2024-11-04
 */
@Api(tags = "(内部) 地方债利差")
@RestController
@RequestMapping("internal/lg/yield-spread")
public class InternalBondYieldSpreadLgController {

    @Resource
    private CalcYieldSpreadService calcYieldSpreadService;
    @Resource
    private LgYieldSpreadService lgYieldSpreadService;

    @ApiOperation(value = "根据日期计算地方债证券利差(债券,主体) (不传日期默认跑前一天的数据)")
    @PostMapping("/calc/spread")
    public int calcLgBondYieldSpreadsBySpreadDate(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "截止日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "bondUniCodeList", value = "债券唯一代码集合")
            @RequestParam(required = false) List<Long> bondUniCodeList
    ) {
        return calcYieldSpreadService.calcLgBondYieldSpreadsBySpreadDate(startDate, endDate, bondUniCodeList);
    }

    @ApiOperation(value = "同步地方债区域利差(历史)")
    @GetMapping("/sync/history")
    public int syncHistBondYieldSpread(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate) {
        return lgYieldSpreadService.syncHisLgBondYieldSpread(startDate);
    }

    @ApiOperation(value = "同步地方债区域利差(增量),不传日期默认同步上一天数据")
    @PostMapping("/sync/area/incr")
    public int syncLgAreaBondYieldSpread(
            @ApiParam(name = "startDate", value = "开始日期") @RequestParam(required = false) Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam(required = false) Date endDate) {
        return lgYieldSpreadService.syncLgAreaBondYieldSpread(startDate, endDate);
    }

    @ApiOperation(value = "缓存地方债区域利差-折线图")
    @GetMapping("/cache/cacheLgLineChart")
    public void cacheLgLineChart() {
        lgYieldSpreadService.cacheLgLineChart();
    }

    @ApiOperation(value = "同步地方债区域利差-base(历史)")
    @GetMapping("/sync/history/base")
    public int syncBondYieldSpreadBase(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam(required = false) Date endDate) {
        return lgYieldSpreadService.syncLgBondYieldSpreadBase(startDate, endDate);
    }

    @ApiOperation(value = "同步地方债区域利差-历史分位(历史)")
    @GetMapping("/sync/history/quantile")
    public int syncBondYieldSpreadQuantile(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam(required = false) Date endDate,
            @ApiParam(name = "spreadQuantileType", value = "历史分位类型 1：三年历史分位 2：五年历史分位 3：一年历史分位")
            @RequestParam(required = false) Integer spreadQuantileType
    ) {
        Optional<SpreadQuantileTypeEnum> spreadQuantileTypeOptional = EnumUtils.getEnumByValue(spreadQuantileType, SpreadQuantileTypeEnum.class);
        return spreadQuantileTypeOptional
                // 按指定历史分位同步
                .map(x -> lgYieldSpreadService.syncLgBondYieldSpreadQuantile(startDate, endDate, x))
                // 全类型历史分位同步
                .orElseGet(() -> lgYieldSpreadService.syncLgBondYieldSpreadQuantile(startDate, endDate));
    }

    @ApiOperation(value = "同步地方债区域利差-区间变动(历史)")
    @GetMapping("/sync/history/change")
    public int syncLgBondYieldSpreadChange(
            @ApiParam(name = "startDate", value = "开始日期", required = true) @RequestParam Date startDate,
            @ApiParam(name = "endDate", value = "结束日期") @RequestParam(required = false) Date endDate) {
        return lgYieldSpreadService.syncLgBondYieldSpreadChange(startDate, endDate);
    }

}
