package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.InsuComYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.InsuComYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.bo.ComCreditSpreadBO;
import com.innodealing.onshore.yieldspread.model.bo.ComYieldSpreadChangeBO;
import com.innodealing.onshore.yieldspread.model.bo.InsuComYieldSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.InsuYieldSearchParam;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InsuComYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.InsuComYieldSpreadGroupMaxDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.view.ComYieldSpreadQuantileViewDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 保险主体利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class InsuComYieldSpreadDAO {

    @Resource
    private InsuComYieldSpreadMapper insuComYieldSpreadMapper;

    @Resource
    private InsuComYieldSpreadGroupMapper insuComYieldSpreadGroupMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 批量更新
     *
     * @param spreadDate               利差日期
     * @param insuComYieldSpreadDOList 保险主体利差列表
     * @return 受影响的行数
     */
    public int saveInsuComYieldSpreadDOList(Date spreadDate, List<InsuComYieldSpreadDO> insuComYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(insuComYieldSpreadDOList)) {
            return 0;
        }
        Set<Long> comUniCodes = insuComYieldSpreadDOList.stream().map(InsuComYieldSpreadDO::getComUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        AtomicInteger effectRows = new AtomicInteger();
        // 批量查询已存在的数据
        DynamicQuery<InsuComYieldSpreadDO> query = DynamicQuery.createQuery(InsuComYieldSpreadDO.class)
                .select(InsuComYieldSpreadDO::getId, InsuComYieldSpreadDO::getComUniCode,
                        InsuComYieldSpreadDO::getSpreadDate, InsuComYieldSpreadDO::getInduLevel1Code,
                        InsuComYieldSpreadDO::getInduLevel1Name, InsuComYieldSpreadDO::getInduLevel2Code,
                        InsuComYieldSpreadDO::getInduLevel2Name, InsuComYieldSpreadDO::getBusinessNature)
                .and(InsuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(InsuComYieldSpreadDO::getComUniCode, in(comUniCodes));
        List<InsuComYieldSpreadDO> existDataList = insuComYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(insuComYieldSpreadDOList));
        } else {
            Map<String, InsuComYieldSpreadDO> existInsuComYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                    x.getComUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<InsuComYieldSpreadDO> insertList = new ArrayList<>();
            List<InsuComYieldSpreadDO> updateList = new ArrayList<>();
            for (InsuComYieldSpreadDO insuComYieldSpreadDO : insuComYieldSpreadDOList) {
                InsuComYieldSpreadDO existInsuComYieldSpreadDO = existInsuComYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER,
                                insuComYieldSpreadDO.getComUniCode(), insuComYieldSpreadDO.getSpreadDate().getTime()));
                if (Objects.isNull(existInsuComYieldSpreadDO)) {
                    insertList.add(insuComYieldSpreadDO);
                } else {
                    insuComYieldSpreadDO.setId(existInsuComYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existInsuComYieldSpreadDO.getInduLevel1Code(), insuComYieldSpreadDO::setInduLevel1Code);
                    ObjectExtensionUtils.ifNonNull(existInsuComYieldSpreadDO.getInduLevel1Name(), insuComYieldSpreadDO::setInduLevel1Name);
                    ObjectExtensionUtils.ifNonNull(existInsuComYieldSpreadDO.getInduLevel2Code(), insuComYieldSpreadDO::setInduLevel2Code);
                    ObjectExtensionUtils.ifNonNull(existInsuComYieldSpreadDO.getInduLevel2Name(), insuComYieldSpreadDO::setInduLevel2Name);
                    ObjectExtensionUtils.ifNonNull(existInsuComYieldSpreadDO.getBusinessNature(), insuComYieldSpreadDO::setBusinessNature);
                    updateList.add(insuComYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 保险主体利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<InsuComYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<InsuComYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(InsuComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (InsuComYieldSpreadDO insuComYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<InsuComYieldSpreadDO> updateQuery = DynamicQuery.createQuery(InsuComYieldSpreadDO.class)
                        .and(InsuComYieldSpreadDO::getId, isEqual(insuComYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(insuComYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 证券主体利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<InsuComYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<InsuComYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(InsuComYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (InsuComYieldSpreadDO insuComYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(insuComYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 获取最新主体利差日期
     *
     * @return
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<InsuComYieldSpreadDO> query = DynamicQuery.createQuery(InsuComYieldSpreadDO.class);
        Optional<java.util.Date> dateOpt = insuComYieldSpreadMapper.selectMaxByDynamicQuery(InsuComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 从主库获取最新主体利差日期
     *
     * @return
     */
    public Optional<Date> getMaxSpreadDateForMaster() {
        DynamicQuery<InsuComYieldSpreadDO> query = DynamicQuery.createQuery(InsuComYieldSpreadDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER);
        Optional<java.util.Date> dateOpt = insuComYieldSpreadMapper.selectMaxByDynamicQuery(InsuComYieldSpreadDO::getSpreadDate, query);
        return dateOpt.map(date -> new Date(date.getTime()));
    }

    /**
     * 查询保险主体最新利差
     *
     * @param comUniCodeList 主体列表
     * @return ComYieldSpreadChangeBO
     */
    public List<ComCreditSpreadBO> listComCreditSpreads(Set<Long> comUniCodeList) {
        if (CollectionUtils.isEmpty(comUniCodeList)) {
            return Collections.emptyList();
        }
        GroupedQuery<InsuComYieldSpreadDO, InsuComYieldSpreadGroupMaxDO> groupedQuery = GroupByQuery.createQuery(InsuComYieldSpreadDO.class, InsuComYieldSpreadGroupMaxDO.class)
                .select(InsuComYieldSpreadGroupMaxDO::getMaxSpreadDate, InsuComYieldSpreadGroupMaxDO::getComUniCode)
                .and(InsuComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                .and(InsuComYieldSpreadDO::getComUniCode, in(comUniCodeList))
                .groupBy(InsuComYieldSpreadDO::getComUniCode);

        List<InsuComYieldSpreadGroupMaxDO> insuComYieldSpreadGroupMaxDOList = insuComYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
        if (CollectionUtils.isEmpty(insuComYieldSpreadGroupMaxDOList)) {
            return Collections.emptyList();
        }
        DynamicQuery<InsuComYieldSpreadDO> query = DynamicQuery.createQuery(InsuComYieldSpreadDO.class)
                .select(InsuComYieldSpreadDO::getSpreadDate,
                        InsuComYieldSpreadDO::getComUniCode,
                        InsuComYieldSpreadDO::getComCreditSpread)
                .and(InsuComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        getComYieldSpreadMaxFilterGroup(insuComYieldSpreadGroupMaxDOList).ifPresent(query::addFilters);
        return BeanCopyUtils.copyList(insuComYieldSpreadMapper.selectByDynamicQuery(query), ComCreditSpreadBO.class);
    }

    /**
     * 主体 利差日期筛选
     *
     * @param insuComYieldSpreadGroupMaxDOS 筛选BO请求参数
     * @return 主体 利差日期筛选
     */
    private Optional<FilterGroupDescriptor<InsuComYieldSpreadDO>> getComYieldSpreadMaxFilterGroup(List<InsuComYieldSpreadGroupMaxDO> insuComYieldSpreadGroupMaxDOS) {
        if (CollectionUtils.isEmpty(insuComYieldSpreadGroupMaxDOS)) {
            return Optional.empty();
        }
        FilterGroupDescriptor<InsuComYieldSpreadDO> filterGroup = new FilterGroupDescriptor<>();
        filterGroup.setCondition(FilterCondition.AND);
        for (InsuComYieldSpreadGroupMaxDO insuComYieldSpreadGroupMaxDO : insuComYieldSpreadGroupMaxDOS) {
            FilterGroupDescriptor<InsuComYieldSpreadDO> currentfilterGroup = new FilterGroupDescriptor<>();
            currentfilterGroup.setCondition(FilterCondition.OR);
            currentfilterGroup.and(InsuComYieldSpreadDO::getComUniCode, isEqual(insuComYieldSpreadGroupMaxDO.getComUniCode()))
                    .and(InsuComYieldSpreadDO::getSpreadDate, isEqual(insuComYieldSpreadGroupMaxDO.getMaxSpreadDate()));
            filterGroup.addFilters(currentfilterGroup);
        }
        return Optional.of(filterGroup);
    }

    /**
     * 获取保险主体利差
     *
     * @param isNewest 是否为最新一天
     * @param param    请求参数
     * @return 主体利差
     */
    public List<InsuComYieldSpreadBO> listComYieldSpreads(boolean isNewest, InsuYieldSearchParam param) {
        return insuComYieldSpreadMapper.listComYieldSpreads(isNewest, param);
    }

    /**
     * 查询主体利差数据集合
     *
     * @param spreadDate  利差日期
     * @param comUniCodes 主体唯一编码集合
     * @return {@link List}<{@link InsuComYieldSpreadDO}>
     */
    public List<InsuComYieldSpreadDO> listComYieldSpreads(@NonNull Date spreadDate, @NonNull Collection<Long> comUniCodes) {
        DynamicQuery<InsuComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(InsuComYieldSpreadDO.class)
                .and(InsuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(InsuComYieldSpreadDO::getComUniCode, in(comUniCodes));
        return BeanCopyUtils.copyList(insuComYieldSpreadMapper.selectByDynamicQuery(groupQuery), InsuComYieldSpreadDO.class);
    }

    /**
     * 获取保险主体利差某一天的 所有 主题
     *
     * @param spreadDate 利差日期
     * @return comUniCodeList 主体列表
     */
    public List<Long> listInsuComYieldSpreadComUniCodes(@NonNull Date spreadDate) {
        DynamicQuery<InsuComYieldSpreadDO> query = DynamicQuery.createQuery(InsuComYieldSpreadDO.class)
                .select(InsuComYieldSpreadDO::getComUniCode)
                .and(InsuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(InsuComYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return insuComYieldSpreadMapper.selectByDynamicQuery(query).stream().map(InsuComYieldSpreadDO::getComUniCode).collect(Collectors.toList());
    }

    /**
     * 获取保险历史分位的统计数据
     *
     * @param startDate      时间范围 开始时间
     * @param endDate        时间范围结束时间
     * @param issueDate      利差日期
     * @param comUniCodeList 主体列表
     * @return 分位统计数据
     */
    public List<ComYieldSpreadQuantileViewDO> listInsuComYieldQuantileStatistics(@NonNull Date startDate, @NonNull Date endDate,
                                                                                 @NonNull Date issueDate, List<Long> comUniCodeList) {
        return insuComYieldSpreadMapper.listComYieldQuantileStatisticsViews(startDate, endDate, issueDate, comUniCodeList);
    }

    /**
     * 获取主体数量
     *
     * @param param 请求参数
     * @return 主体数量
     */
    public Long countComYieldSpread(InsuYieldSearchParam param) {
        return insuComYieldSpreadMapper.countComYieldSpread(param);
    }

    /**
     * 查询保险主体利差
     *
     * @param spreadDate     利差日期
     * @param comUniCodeList 主体列表
     * @return 保险利差变动 所需的 收益率bo数据
     */
    public List<ComYieldSpreadChangeBO> listInsuComYieldSpreads(Date spreadDate, Set<Long> comUniCodeList) {
        if (Objects.isNull(spreadDate)) {
            return Collections.emptyList();
        }
        DynamicQuery<InsuComYieldSpreadDO> groupQuery = DynamicQuery.createQuery(InsuComYieldSpreadDO.class)
                .select(InsuComYieldSpreadDO::getComUniCode, InsuComYieldSpreadDO::getSpreadDate,
                        InsuComYieldSpreadDO::getComCreditSpread, InsuComYieldSpreadDO::getComExcessSpread)
                .and(InsuComYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(CollectionUtils.isNotEmpty(comUniCodeList),
                        InsuComYieldSpreadDO::getComUniCode, in(comUniCodeList));
        return BeanCopyUtils.copyList(insuComYieldSpreadMapper.selectByDynamicQuery(groupQuery), ComYieldSpreadChangeBO.class);
    }
}
