package com.innodealing.onshore.yieldspread.model.bo;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * dm城投口径区域利差表实体对象
 *
 * <AUTHOR>
 */
public class UdicAreaYieldSpreadBO {

    /**
     * 区域编码
     */
    private Long areaUniCode;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    private BigDecimal bondCreditSpread;


    public Long getAreaUniCode() {
        return areaUniCode;
    }

    public void setAreaUniCode(Long areaUniCode) {
        this.areaUniCode = areaUniCode;
    }


    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }


    public Date getSpreadDate() {
        return java.util.Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = java.util.Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }


    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

}

