package com.innodealing.onshore.yieldspread.builder;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.helper.CalculationHelper;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldPanoramaBO;
import com.innodealing.onshore.yieldspread.model.bo.PgBondYieldSpreadTraceBO;
import com.innodealing.onshore.yieldspread.model.bo.SpreadStatisticsBO;
import com.innodealing.onshore.yieldspread.model.bo.YieldLgDataBO;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldPanoramaTraceSpreadDateDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 利差变动响应数据构造器
 *
 * <AUTHOR>
 */
public class BondYieldSpreadBuilder {
    public static final String STATISTICAL_DATE_RANGE = "统计期：%s 至 %s";
    public static final String STATISTICAL_DATE = "统计期：%s";
    private static final String NOT_PERMISSION_VALUE = YieldSpreadConst.NO_PERMISSIONS_PLACEHOLDER;


    /**
     * 小数点精度位数
     */
    private static final Integer DECIMAL_SCALE = 2;

    /**
     * 无参构造
     */
    BondYieldSpreadBuilder() {
        // 构造器
    }

    /**
     * 利差全景导出响应值构造
     *
     * @param pgBondYieldPanoramaBO 利差全景数据
     * @param hasPermission         是否有中债权限
     * @return 利差全景响应值
     */
    public static YieldPanoramaTypeDataExportExcelDTO excelBuilder(Boolean hasPermission, PgBondYieldPanoramaBO pgBondYieldPanoramaBO) {
        YieldPanoramaTypeDataExportExcelDTO yieldPanoramaTypeDataExportExcel = new YieldPanoramaTypeDataExportExcelDTO();
        Optional<YieldSpreadCurveCodeEnum> curveCodeEnumOptional =
                EnumUtils.getEnumByValue(pgBondYieldPanoramaBO.getCurveCode(), YieldSpreadCurveCodeEnum.class);
        if (curveCodeEnumOptional.isPresent()) {
            YieldSpreadCurveCodeEnum curveCodeEnum = curveCodeEnumOptional.get();
            yieldPanoramaTypeDataExportExcel.setTypeName(curveCodeEnum.getBondType().getText());
            yieldPanoramaTypeDataExportExcel.setDataSort(curveCodeEnum.ordinal());
            EnumUtils.getEnumByTextValue(curveCodeEnum.getText(), YieldSpreadRatingEnum.class)
                    .map(YieldSpreadRatingEnum::getText)
                    .ifPresent(yieldPanoramaTypeDataExportExcel::setRatingName);

        }
        yieldPanoramaTypeDataExportExcel.setYield1M(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm1M()));
        yieldPanoramaTypeDataExportExcel.setYield3M(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm3M()));
        yieldPanoramaTypeDataExportExcel.setYield6M(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm6M()));
        yieldPanoramaTypeDataExportExcel.setYield9M(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm9M()));
        yieldPanoramaTypeDataExportExcel.setYield1Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm1Y()));
        yieldPanoramaTypeDataExportExcel.setYield2Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm2Y()));
        yieldPanoramaTypeDataExportExcel.setYield3Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm3Y()));
        yieldPanoramaTypeDataExportExcel.setYield5Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm5Y()));
        yieldPanoramaTypeDataExportExcel.setYield7Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm7Y()));
        yieldPanoramaTypeDataExportExcel.setYield10Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm10Y()));
        yieldPanoramaTypeDataExportExcel.setYield15Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm15Y()));
        yieldPanoramaTypeDataExportExcel.setYield20Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm20Y()));
        yieldPanoramaTypeDataExportExcel.setYield30Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm30Y()));
        yieldPanoramaTypeDataExportExcel.setYield50Y(getExcelValue(hasPermission, pgBondYieldPanoramaBO.getYtm50Y()));
        return yieldPanoramaTypeDataExportExcel;
    }

    /**
     * 利差全景响应值构造
     *
     * @param pgBondYieldPanoramaBO 利差全景数据
     * @param hasPermission         是否有中债权限
     * @return 利差全景响应值
     */
    public static YieldPanoramaTypeDataResponseDTO builder(Boolean hasPermission, PgBondYieldPanoramaBO pgBondYieldPanoramaBO) {
        YieldPanoramaTypeDataResponseDTO yieldPanoramaTypeDataResponseDTO = new YieldPanoramaTypeDataResponseDTO();
        Optional<YieldSpreadCurveCodeEnum> curveCodeEnumOptional =
                EnumUtils.getEnumByValue(pgBondYieldPanoramaBO.getCurveCode(), YieldSpreadCurveCodeEnum.class);
        if (curveCodeEnumOptional.isPresent()) {
            YieldSpreadCurveCodeEnum curveCodeEnum = curveCodeEnumOptional.get();
            yieldPanoramaTypeDataResponseDTO.setTypeName(curveCodeEnum.getBondType().getText());
            EnumUtils.getEnumByTextValue(curveCodeEnum.getText(), YieldSpreadRatingEnum.class)
                    .map(YieldSpreadRatingEnum::getText)
                    .ifPresent(yieldPanoramaTypeDataResponseDTO::setRatingName);
            yieldPanoramaTypeDataResponseDTO.setDataSort(curveCodeEnum.ordinal());
        }
        yieldPanoramaTypeDataResponseDTO.setYield1M(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm1M()));
        yieldPanoramaTypeDataResponseDTO.setYield3M(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm3M()));
        yieldPanoramaTypeDataResponseDTO.setYield6M(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm6M()));
        yieldPanoramaTypeDataResponseDTO.setYield9M(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm9M()));
        yieldPanoramaTypeDataResponseDTO.setYield1Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm1Y()));
        yieldPanoramaTypeDataResponseDTO.setYield2Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm2Y()));
        yieldPanoramaTypeDataResponseDTO.setYield3Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm3Y()));
        yieldPanoramaTypeDataResponseDTO.setYield5Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm5Y()));
        yieldPanoramaTypeDataResponseDTO.setYield7Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm7Y()));
        yieldPanoramaTypeDataResponseDTO.setYield10Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm10Y()));
        yieldPanoramaTypeDataResponseDTO.setYield15Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm15Y()));
        yieldPanoramaTypeDataResponseDTO.setYield20Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm20Y()));
        yieldPanoramaTypeDataResponseDTO.setYield30Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm30Y()));
        yieldPanoramaTypeDataResponseDTO.setYield50Y(getValue(hasPermission, pgBondYieldPanoramaBO.getYtm50Y()));
        List<BigDecimal> yieldList = Stream.of(pgBondYieldPanoramaBO.getYtm1M(), pgBondYieldPanoramaBO.getYtm3M(),
                        pgBondYieldPanoramaBO.getYtm6M(), pgBondYieldPanoramaBO.getYtm9M(), pgBondYieldPanoramaBO.getYtm1Y(),
                        pgBondYieldPanoramaBO.getYtm2Y(), pgBondYieldPanoramaBO.getYtm3Y(), pgBondYieldPanoramaBO.getYtm5Y(),
                        pgBondYieldPanoramaBO.getYtm7Y(), pgBondYieldPanoramaBO.getYtm10Y(), pgBondYieldPanoramaBO.getYtm15Y(),
                        pgBondYieldPanoramaBO.getYtm20Y(), pgBondYieldPanoramaBO.getYtm30Y(), pgBondYieldPanoramaBO.getYtm50Y())
                .filter(Objects::nonNull).collect(Collectors.toList());
        yieldPanoramaTypeDataResponseDTO.setMinYield(yieldList.stream().min(BigDecimal::compareTo).orElse(null));
        yieldPanoramaTypeDataResponseDTO.setMaxYield(yieldList.stream().max(BigDecimal::compareTo).orElse(null));
        yieldPanoramaTypeDataResponseDTO.setYields(yieldList);
        return yieldPanoramaTypeDataResponseDTO;
    }

    /**
     * 地方债响应值构造
     *
     * @param yieldLgDataBO          地方债数据
     * @param spreadCurveTypeEnum    {@link SpreadCurveTypeEnum }
     * @param bondYieldTableTypeEnum {@link BondYieldTableTypeEnum}
     * @return 利差全景响应值
     */
    public static YieldLgDataResponseDTO builder(YieldLgDataBO yieldLgDataBO, SpreadCurveTypeEnum spreadCurveTypeEnum,
                                                 BondYieldTableTypeEnum bondYieldTableTypeEnum) {
        YieldLgDataResponseDTO yieldLgDataResponseDTO = new YieldLgDataResponseDTO();
        yieldLgDataResponseDTO.setComUniCode(yieldLgDataBO.getComUniCode());
        yieldLgDataResponseDTO.setLgAreaName(yieldLgDataBO.getLgAreaName());
        Integer scale = YieldSpreadConst.TWO_DECIMAL_PLACE;
        if (SpreadCurveTypeEnum.CB_YIELD_SPREAD.equals(spreadCurveTypeEnum) &&
                BondYieldTableTypeEnum.ABS.equals(bondYieldTableTypeEnum)) {
            scale = YieldSpreadConst.FOUR_DECIMAL_PLACE;
        }
        yieldLgDataResponseDTO.setYield1M(getValue(Boolean.TRUE, yieldLgDataBO.getYield1M(), scale));
        yieldLgDataResponseDTO.setYield3M(getValue(Boolean.TRUE, yieldLgDataBO.getYield3M(), scale));
        yieldLgDataResponseDTO.setYield6M(getValue(Boolean.TRUE, yieldLgDataBO.getYield6M(), scale));
        yieldLgDataResponseDTO.setYield9M(getValue(Boolean.TRUE, yieldLgDataBO.getYield9M(), scale));
        yieldLgDataResponseDTO.setYield1Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield1Y(), scale));
        yieldLgDataResponseDTO.setYield2Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield2Y(), scale));
        yieldLgDataResponseDTO.setYield3Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield3Y(), scale));
        yieldLgDataResponseDTO.setYield5Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield5Y(), scale));
        yieldLgDataResponseDTO.setYield7Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield7Y(), scale));
        yieldLgDataResponseDTO.setYield10Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield10Y(), scale));
        yieldLgDataResponseDTO.setYield15Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield15Y(), scale));
        yieldLgDataResponseDTO.setYield20Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield20Y(), scale));
        yieldLgDataResponseDTO.setYield30Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield30Y(), scale));
        yieldLgDataResponseDTO.setYield50Y(getValue(Boolean.TRUE, yieldLgDataBO.getYield50Y(), scale));
        EnumUtils.getEnumByValue(yieldLgDataBO.getComUniCode().intValue(), SpreadComAreaEnum.class).
                ifPresent(spreadComAreaEnum -> yieldLgDataResponseDTO.setAreaSort(spreadComAreaEnum.ordinal()));
        return yieldLgDataResponseDTO;
    }

    /**
     * 利差变动excel响应构造
     *
     * @param pgBondYieldSpreadTraceBO 利差变动数据
     * @return 利差变动响应
     */
    public static YieldSpreadTraceDataExportExcelDTO excelBuilder(PgBondYieldSpreadTraceBO pgBondYieldSpreadTraceBO) {
        return excelBuilder(true, pgBondYieldSpreadTraceBO);
    }

    /**
     * 利差变动excel响应构造
     *
     * @param pgBondYieldSpreadTraceBO 利差变动数据
     * @param hasPermission            是否有中债权限
     * @return 利差变动响应
     */
    public static YieldSpreadTraceDataExportExcelDTO excelBuilder(Boolean hasPermission, PgBondYieldSpreadTraceBO pgBondYieldSpreadTraceBO) {
        YieldSpreadTraceDataExportExcelDTO yieldSpreadTraceDataExportExcel = new YieldSpreadTraceDataExportExcelDTO();
        Optional<YieldSpreadCurveCodeEnum> curveCodeEnum = EnumUtils.getEnumByValue(pgBondYieldSpreadTraceBO.getCurveCode(), YieldSpreadCurveCodeEnum.class);
        curveCodeEnum.map(YieldSpreadCurveCodeEnum::getText).ifPresent(yieldSpreadTraceDataExportExcel::setTypeName);
        curveCodeEnum.map(YieldSpreadCurveCodeEnum::ordinal).ifPresent(yieldSpreadTraceDataExportExcel::setSort);
        // 只有到期收益率才会判断权限，其他都不判断，直接展示
        Integer tableType = pgBondYieldSpreadTraceBO.getTableType();
        Integer chartType = pgBondYieldSpreadTraceBO.getChartType();
        Integer scale = tableType.equals(BondYieldTableTypeEnum.ABS.getValue()) &&
                !chartType.equals(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue()) ? DECIMAL_SCALE : null;
        yieldSpreadTraceDataExportExcel.setYield1M(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm1M(), scale));
        yieldSpreadTraceDataExportExcel.setYield3M(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm3M(), scale));
        yieldSpreadTraceDataExportExcel.setYield6M(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm6M(), scale));
        yieldSpreadTraceDataExportExcel.setYield9M(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm9M(), scale));
        yieldSpreadTraceDataExportExcel.setYield1Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm1Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield2Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm2Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield3Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm3Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield5Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm5Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield7Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm7Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield10Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm10Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield15Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm15Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield20Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm20Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield30Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm30Y(), scale));
        yieldSpreadTraceDataExportExcel.setYield50Y(getExcelValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm50Y(), scale));
        return yieldSpreadTraceDataExportExcel;
    }

    /**
     * 利差变动响应构造,有权限
     *
     * @param pgBondYieldSpreadTraceBO 利差变动数据
     * @return 利差变动响应
     */
    public static YieldSpreadTraceDataResponseDTO builder(PgBondYieldSpreadTraceBO pgBondYieldSpreadTraceBO) {
        return builder(true, pgBondYieldSpreadTraceBO);
    }

    /**
     * 利差变动响应构造
     *
     * @param hasPermission            是否有中债权限
     * @param pgBondYieldSpreadTraceBO 利差变动数据
     * @return 利差变动响应
     */
    public static YieldSpreadTraceDataResponseDTO builder(Boolean hasPermission, PgBondYieldSpreadTraceBO pgBondYieldSpreadTraceBO) {
        YieldSpreadTraceDataResponseDTO yieldSpreadTraceDataResponseDTO = new YieldSpreadTraceDataResponseDTO();
        Optional<YieldSpreadCurveCodeEnum> curveCodeEnum = EnumUtils.getEnumByValue(pgBondYieldSpreadTraceBO.getCurveCode(), YieldSpreadCurveCodeEnum.class);
        curveCodeEnum.map(YieldSpreadCurveCodeEnum::getText).ifPresent(yieldSpreadTraceDataResponseDTO::setTypeName);
        curveCodeEnum.map(YieldSpreadCurveCodeEnum::ordinal).ifPresent(yieldSpreadTraceDataResponseDTO::setSort);

        Integer tableType = pgBondYieldSpreadTraceBO.getTableType();
        Integer chartType = pgBondYieldSpreadTraceBO.getChartType();
        Integer scale = tableType.equals(BondYieldTableTypeEnum.ABS.getValue()) &&
                !chartType.equals(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue()) ? DECIMAL_SCALE : null;
        yieldSpreadTraceDataResponseDTO.setYield1M(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm1M(), scale));
        yieldSpreadTraceDataResponseDTO.setYield3M(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm3M(), scale));
        yieldSpreadTraceDataResponseDTO.setYield6M(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm6M(), scale));
        yieldSpreadTraceDataResponseDTO.setYield9M(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm9M(), scale));
        yieldSpreadTraceDataResponseDTO.setYield1Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm1Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield2Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm2Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield3Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm3Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield5Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm5Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield7Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm7Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield10Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm10Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield15Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm15Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield20Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm20Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield30Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm30Y(), scale));
        yieldSpreadTraceDataResponseDTO.setYield50Y(getValue(hasPermission, pgBondYieldSpreadTraceBO.getYtm50Y(), scale));
        List<BigDecimal> yieldList = Stream.of(pgBondYieldSpreadTraceBO.getYtm1Y(), pgBondYieldSpreadTraceBO.getYtm2Y(),
                        pgBondYieldSpreadTraceBO.getYtm3Y(), pgBondYieldSpreadTraceBO.getYtm5Y(), pgBondYieldSpreadTraceBO.getYtm7Y())
                .filter(Objects::nonNull).collect(Collectors.toList());
        yieldSpreadTraceDataResponseDTO.setMaxYield(yieldList.stream().max(BigDecimal::compareTo).orElse(null));
        yieldSpreadTraceDataResponseDTO.setMinYield(yieldList.stream().min(BigDecimal::compareTo).orElse(null));
        yieldSpreadTraceDataResponseDTO.setYields(yieldList);
        return yieldSpreadTraceDataResponseDTO;
    }

    /**
     * 利差变动响应构造,有权限
     *
     * @param spreadDate                   issueTime
     * @param bondType                     债券类型
     * @param chartType                    利差类型
     * @param pgBondYieldSpreadTraceBOList 利差变动数据
     * @param changeStartDate              区间变动开始日期
     * @param quantileStartDate            年分位开始日期
     * @param spreadDateDTO                缓存中的最大利差日期，包含绝对值，3年历史分位，区间变动
     * @return 利差变动响应
     */
    @SuppressWarnings("java:S107")
    public static YieldSpreadTraceResponseDTO builder(Date spreadDate, Integer bondType, Integer chartType,
                                                      List<PgBondYieldSpreadTraceBO> pgBondYieldSpreadTraceBOList,
                                                      Date changeStartDate, Date quantileStartDate, BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO) {
        return builder(true, spreadDate, bondType, chartType, pgBondYieldSpreadTraceBOList, changeStartDate, quantileStartDate, spreadDateDTO);
    }

    /**
     * 利差变动响应构造
     *
     * @param spreadDate                   issueTime
     * @param bondType                     债券类型
     * @param chartType                    利差类型
     * @param pgBondYieldSpreadTraceBOList 利差变动数据
     * @param changeStartDate              区间变动开始日期
     * @param quantileStartDate            年分位开始日期
     * @param spreadDateDTO                缓存中的最大利差日期，包含绝对值，3年历史分位，区间变动
     * @param hasPermission                是否有中债权限
     * @return 利差变动响应
     */
    @SuppressWarnings("java:S107")
    public static YieldSpreadTraceResponseDTO builder(Boolean hasPermission, Date spreadDate, Integer bondType, Integer chartType,
                                                      List<PgBondYieldSpreadTraceBO> pgBondYieldSpreadTraceBOList,
                                                      Date changeStartDate, Date quantileStartDate, BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO) {
        YieldSpreadTraceResponseDTO yieldSpreadTraceResponseDTO = new YieldSpreadTraceResponseDTO();
        String quantileDate = spreadDateDTO.getOrDefaultQuantile(spreadDate).toLocalDate().format(DateTimeFormatter.ISO_DATE);
        String changeDate = spreadDateDTO.getOrDefaultChange(spreadDate).toLocalDate().format(DateTimeFormatter.ISO_DATE);
        String rangeChangeDateStr = String.format(STATISTICAL_DATE_RANGE, changeStartDate.toLocalDate().format(DateTimeFormatter.ISO_DATE), changeDate);
        String rangeQuantileDateStr = String.format(STATISTICAL_DATE_RANGE, quantileStartDate.toLocalDate().format(DateTimeFormatter.ISO_DATE), quantileDate);
        yieldSpreadTraceResponseDTO.setThreeYearHisRangeDate(rangeQuantileDateStr);
        yieldSpreadTraceResponseDTO.setIntervalVarRangeDate(rangeChangeDateStr);
        YieldSpreadChartTypeEnum chartTypeEnum = ITextValueEnum.getEnum(YieldSpreadChartTypeEnum.class, chartType);
        YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);
        Map<Integer, List<PgBondYieldSpreadTraceBO>> tableTypeToSpreadTraceBOListMap =
                pgBondYieldSpreadTraceBOList.stream().collect(Collectors.groupingBy(PgBondYieldSpreadTraceBO::getTableType));
        List<YieldSpreadCurveCodeEnum> spreadCurveCodeEnumList = YieldSpreadCurveCodeEnum.getCurveCodesEnumByBondType(bondTypeEnum);
        for (Map.Entry<Integer, List<PgBondYieldSpreadTraceBO>> spreadTraceBOEntry : tableTypeToSpreadTraceBOListMap.entrySet()) {
            List<PgBondYieldSpreadTraceBO> yieldSpreadTraceBOList = spreadTraceBOEntry.getValue();

            List<YieldSpreadTraceDataResponseDTO> yieldSpreadTraceDataResponseDTOList = yieldSpreadTraceBOList.stream()
                    .map(trace -> BondYieldSpreadBuilder.builder(hasPermission, trace))
                    .sorted(Comparator.comparing(YieldSpreadTraceDataResponseDTO::getSort)).collect(Collectors.toList());
            BigDecimal maxYield = yieldSpreadTraceDataResponseDTOList.stream().map(YieldSpreadTraceDataResponseDTO::getMaxYield)
                    .filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(null);
            BigDecimal minYield = yieldSpreadTraceDataResponseDTOList.stream().map(YieldSpreadTraceDataResponseDTO::getMinYield)
                    .filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(null);
            BigDecimal[] yields = yieldSpreadTraceDataResponseDTOList.stream().flatMap(trace -> trace.getYields().stream()).toArray(BigDecimal[]::new);
            Optional<BigDecimal> medianYield = CalculationHelper.calcMedian(yields);
            if (YieldSpreadChartTypeEnum.GRADE_SPREAD.getValue() == chartType) {
                String maxRating = spreadCurveCodeEnumList.stream().min(Comparator.comparing(YieldSpreadCurveCodeEnum::ordinal))
                        .map(YieldSpreadCurveCodeEnum::getText).orElse(StringUtils.EMPTY);
                yieldSpreadTraceDataResponseDTOList.stream().filter(x -> !Objects.equals(maxRating, x.getTypeName()))
                        .forEach(x -> x.setTypeName(String.format("(%s)-(%s)", x.getTypeName(), maxRating)));
            }
            if (spreadTraceBOEntry.getKey() == BondYieldTableTypeEnum.ABS.getValue()) {
                yieldSpreadTraceResponseDTO.setMaxTraceYield(maxYield);
                yieldSpreadTraceResponseDTO.setMinTraceYield(minYield);
                medianYield.ifPresent(yieldSpreadTraceResponseDTO::setMedianTraceYield);
                yieldSpreadTraceResponseDTO.setYieldSpreadTraceData(yieldSpreadTraceDataResponseDTOList);
            } else if (spreadTraceBOEntry.getKey() == BondYieldTableTypeEnum.HIST_QUANTILE.getValue()) {
                yieldSpreadTraceResponseDTO.setMinThreeYearYield(minYield);
                yieldSpreadTraceResponseDTO.setMaxThreeYearYield(maxYield);
                medianYield.ifPresent(yieldSpreadTraceResponseDTO::setMedianThreeYearYield);
                yieldSpreadTraceResponseDTO.setThreeYearHistorical(yieldSpreadTraceDataResponseDTOList);
            } else {
                yieldSpreadTraceResponseDTO.setMinIntervalVarYield(minYield);
                yieldSpreadTraceResponseDTO.setMaxIntervalVarYield(maxYield);
                medianYield.ifPresent(yieldSpreadTraceResponseDTO::setMedianIntervalVarYield);
                yieldSpreadTraceResponseDTO.setIntervalVariation(yieldSpreadTraceDataResponseDTOList);
            }
        }
        yieldSpreadTraceResponseDTO.setTypeSort(YieldSpreadChartTypeEnum.getTypeSortByChartType(chartType).orElse(null));

        if (Objects.equals(YieldPanoramaBondTypeEnum.SECURITIES_SUB_BOND, bondTypeEnum)
                && Objects.equals(chartTypeEnum, YieldSpreadChartTypeEnum.TERM_SPREAD)) {
            yieldSpreadTraceResponseDTO.setTypeName(String.format(chartTypeEnum.getExcelDataDec(), bondTypeEnum.getText(), "证券公司"));
        } else if (Arrays.asList(YieldPanoramaBondTypeEnum.BANK_SECONDARY_CAPITAL_BOND, YieldPanoramaBondTypeEnum.BANK_PERPETUAL_BOND)
                .contains(bondTypeEnum) && Objects.equals(chartTypeEnum, YieldSpreadChartTypeEnum.TERM_SPREAD)) {
            yieldSpreadTraceResponseDTO.setTypeName(String.format(chartTypeEnum.getExcelDataDec(), bondTypeEnum.getText(), "银行"));
        } else {
            yieldSpreadTraceResponseDTO.setTypeName(String.format(chartTypeEnum.getExcelDataDec(), bondTypeEnum.getText()));
        }
        yieldSpreadTraceResponseDTO.setChartType(chartType);
        return yieldSpreadTraceResponseDTO;
    }

    /**
     * 利差变动响应构造
     *
     * @param hasPermission                是否有中债权限
     * @param bondType                     债券类型
     * @param chartType                    利差类型
     * @param pgBondYieldSpreadTraceBOList bo数据
     * @param startDate                    日期范围开始时间
     * @param endDate                      日期范围结束时间
     * @param periodConfigList             期限配置
     * @return 利差变动响应
     */
    @SuppressWarnings("java:S107")
    public static YieldSpreadTraceTabResponseDTO builder(Boolean hasPermission, Integer bondType, Integer chartType,
                                                         List<PgBondYieldSpreadTraceBO> pgBondYieldSpreadTraceBOList,
                                                         Date startDate, Date endDate, List<Integer> periodConfigList) {

        YieldSpreadTraceTabResponseDTO yieldSpreadTraceTabResponseDTO = new YieldSpreadTraceTabResponseDTO();
        if (ObjectUtils.allNotNull(startDate, endDate)) {
            yieldSpreadTraceTabResponseDTO.setStatisticalDateStart(startDate.toLocalDate().format(DateTimeFormatter.ISO_DATE));
            yieldSpreadTraceTabResponseDTO.setStatisticalDateEnd(endDate.toLocalDate().format(DateTimeFormatter.ISO_DATE));
            yieldSpreadTraceTabResponseDTO.setStatisticalDateRange(String.format(
                    STATISTICAL_DATE_RANGE,
                    startDate.toLocalDate().format(DateTimeFormatter.ISO_DATE),
                    endDate.toLocalDate().format(DateTimeFormatter.ISO_DATE)));
        }


        YieldSpreadChartTypeEnum chartTypeEnum = ITextValueEnum.getEnum(YieldSpreadChartTypeEnum.class, chartType);
        YieldPanoramaBondTypeEnum bondTypeEnum = ITextValueEnum.getEnum(YieldPanoramaBondTypeEnum.class, bondType);

        List<YieldSpreadCurveCodeEnum> spreadCurveCodeEnumList = YieldSpreadCurveCodeEnum.getCurveCodesEnumByBondType(bondTypeEnum);
        List<YieldSpreadTraceDataResponseDTO> yieldSpreadTraceDataResponseDTOList = pgBondYieldSpreadTraceBOList.stream()
                .map(trace -> BondYieldSpreadBuilder.builder(hasPermission, trace))
                .sorted(Comparator.comparing(YieldSpreadTraceDataResponseDTO::getSort)).collect(Collectors.toList());

        if (YieldSpreadChartTypeEnum.GRADE_SPREAD.getValue() == chartType) {
            String maxRating = spreadCurveCodeEnumList.stream().min(Comparator.comparing(YieldSpreadCurveCodeEnum::ordinal))
                    .map(YieldSpreadCurveCodeEnum::getText).orElse(StringUtils.EMPTY);
            yieldSpreadTraceDataResponseDTOList.stream().filter(x -> !Objects.equals(maxRating, x.getTypeName()))
                    .forEach(x -> x.setTypeName(String.format("(%s)-(%s)", x.getTypeName(), maxRating)));
        } else if (YieldSpreadChartTypeEnum.VARIETY_SPREAD_SUB_CB.getValue() == chartType) {
            yieldSpreadTraceDataResponseDTOList.forEach(x ->
                    x.setTypeName(String.format(YieldSpreadChartTypeEnum.VARIETY_SPREAD_SUB_CB.getExcelDataDec(), x.getTypeName()))
            );
        }

        if (YieldSpreadChartTypeEnum.TENOR_SPREAD.equals(chartTypeEnum)) {
            List<PeriodEnum> periodEnumList = PeriodEnum.getPeriodEnumByValues(periodConfigList);
            periodConfigList = PeriodEnum.listPeriodTraceBy(periodEnumList, bondTypeEnum).stream().map(PeriodEnum::getValue).collect(Collectors.toList());
        }
        SpreadStatisticsBO spreadStatisticsBO = YieldSpreadHelper.statisticsSpreadByPeriod(pgBondYieldSpreadTraceBOList, periodConfigList);
        yieldSpreadTraceTabResponseDTO.setMaxYield(spreadStatisticsBO.getMaxYield());
        yieldSpreadTraceTabResponseDTO.setMinYield(spreadStatisticsBO.getMinYield());
        yieldSpreadTraceTabResponseDTO.setMedianYield(spreadStatisticsBO.getMedianYield());
        yieldSpreadTraceTabResponseDTO.setYieldSpreadTraceDataList(yieldSpreadTraceDataResponseDTOList);


        yieldSpreadTraceTabResponseDTO.setTypeSort(YieldSpreadChartTypeEnum.getTypeSortByChartType(chartType).orElse(null));
        if (Objects.equals(YieldPanoramaBondTypeEnum.SECURITIES_SUB_BOND, bondTypeEnum)
                && Objects.equals(chartTypeEnum, YieldSpreadChartTypeEnum.TERM_SPREAD)) {
            yieldSpreadTraceTabResponseDTO.setTypeName(String.format(chartTypeEnum.getExcelDataDec(), bondTypeEnum.getText(), "证券公司"));
        } else if (Arrays.asList(YieldPanoramaBondTypeEnum.BANK_SECONDARY_CAPITAL_BOND, YieldPanoramaBondTypeEnum.BANK_PERPETUAL_BOND)
                .contains(bondTypeEnum) && Objects.equals(chartTypeEnum, YieldSpreadChartTypeEnum.TERM_SPREAD)) {
            yieldSpreadTraceTabResponseDTO.setTypeName(String.format(chartTypeEnum.getExcelDataDec(), bondTypeEnum.getText(), "银行"));
        } else if (Objects.equals(bondTypeEnum, YieldPanoramaBondTypeEnum.INTEREST_RATE_BOND)) {
            yieldSpreadTraceTabResponseDTO.setTypeName(chartTypeEnum.getText());
        } else {
            yieldSpreadTraceTabResponseDTO.setTypeName(String.format(chartTypeEnum.getExcelDataDec(), bondTypeEnum.getText()));
        }
        yieldSpreadTraceTabResponseDTO.setChartType(chartType);
        return yieldSpreadTraceTabResponseDTO;
    }

    private static String getValue(Boolean hasPermission, BigDecimal value) {
        return getValue(hasPermission, value, null);
    }

    /**
     * 数值转换
     *
     * @param hasPermission 是否有权限
     * @param value         数据小数值
     * @param scale         保留小数点位,为null则默认
     * @return
     */
    private static String getValue(Boolean hasPermission, BigDecimal value, Integer scale) {
        return Boolean.TRUE.equals(hasPermission) ?
                Optional.ofNullable(value).map(x -> {
                    if (Objects.nonNull(scale)) {
                        //舍入方式任意不会对业务造成影响
                        x = x.setScale(scale, RoundingMode.HALF_UP);
                    }
                    return x.toPlainString();
                }).orElse(StringUtils.EMPTY) : NOT_PERMISSION_VALUE;
    }

    private static BigDecimal getExcelValue(Boolean hasPermission, BigDecimal value) {
        return getExcelValue(hasPermission, value, null);
    }

    private static BigDecimal getExcelValue(Boolean hasPermission, BigDecimal value, Integer scale) {
        return Boolean.TRUE.equals(hasPermission) ?
                Optional.ofNullable(value).map(x -> {
                    if (Objects.nonNull(scale)) {
                        //舍入方式任意不会对业务造成影响
                        x = x.setScale(scale, RoundingMode.HALF_UP);
                    }
                    return x;
                }).orElse(null) : null;
    }
}
