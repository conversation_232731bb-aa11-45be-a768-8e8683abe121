package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.ComYieldSpreadListRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.request.TrendReplayYieldSpreadRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.BondYieldSpreadListResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.ComYieldSpreadCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.ComYieldSpreadListResponseDTO;

import java.sql.Date;
import java.util.List;

/**
 * 主体利差服务
 *
 * <AUTHOR>
 */
public interface ComYieldSpreadService {
    /**
     * 查询曲线列表数据
     *
     * @param comUniCode        主体唯一编码
     * @param curveType         曲线类型
     * @param spreadBondRanking 利差债券类型,求偿顺序 0:私募债(产业、城投)|普通债(银行、证券), 1:公募债(产业、城投)|次级债(银行、证券), 2:永续债(产业、城投、银行、证券)
     * @param startDate         开始日期
     * @param endDate           结束日期
     * @return {@link List}{@link ComYieldSpreadCurveResponseDTO}
     * @see com.innodealing.onshore.yieldspread.enums.CurveTypeEnum
     * @see com.innodealing.onshore.yieldspread.enums.SpreadBondTypeEnum
     * @see com.innodealing.onshore.bondmetadata.enums.BankSeniorityRankingEnum
     * @see com.innodealing.onshore.bondmetadata.enums.SecuritySeniorityRankingEnum
     */
    List<ComYieldSpreadCurveResponseDTO> listCurves(Long comUniCode, Integer curveType, Integer spreadBondRanking, Date startDate, Date endDate);

    /**
     * 查询列表主体数据集合，包含产业债、城投债、银行债、证券债、自选债
     *
     * @param request 查询列表主体请求参数
     * @return {@link BondYieldSpreadListResponseDTO}
     */
    ComYieldSpreadListResponseDTO listComs(ComYieldSpreadListRequestDTO request);

    /**
     * 刷新物化视图
     */
    void refreshMv();

    /**
     * 走势复盘查询主体利差信用利差(全部)数据
     *
     * @param requestDTO 请求体
     * @return 列表
     */
    List<ComCreditSpreadDTO> trendReplayCurves(TrendReplayYieldSpreadRequestDTO requestDTO);
}
