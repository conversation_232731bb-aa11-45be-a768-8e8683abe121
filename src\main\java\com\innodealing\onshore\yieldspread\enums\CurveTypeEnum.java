package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 曲线类型
 *
 * <AUTHOR>
 */
public enum CurveTypeEnum implements ITextValueEnum {
    /**
     * 产业债
     */
    INDU(1, "产业"),
    UDIC(2, "城投"),
    BANK(3, "银行"),
    SECURITY(4, "证券"),
    CUSTOMIZATION(5, "自定义"),
    CB(6, "中债曲线"),
    INSURANCE(7, "保险");

    CurveTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    private final int value;

    private final String text;

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }
}
