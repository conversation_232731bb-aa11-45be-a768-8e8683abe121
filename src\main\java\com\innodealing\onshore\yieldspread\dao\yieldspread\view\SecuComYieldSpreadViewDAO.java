package com.innodealing.onshore.yieldspread.dao.yieldspread.view;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.view.SecuComYieldSpreadViewMapper;
import com.innodealing.onshore.yieldspread.model.view.SecuComYieldSpreadDynamicView;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.Collection;
import java.util.List;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;


/**
 * 行业主体利差视图
 *
 * <AUTHOR>
 */
@Repository
public class SecuComYieldSpreadViewDAO {
    @Resource
    private SecuComYieldSpreadViewMapper secuComYieldSpreadViewMapper;

    /**
     * 查询主体利差数据集
     *
     * @param spreadDate 利差日期
     * @param udicComs   主体唯一编码集合
     * @return {@link List}<{@link SecuComYieldSpreadDynamicView}>
     */
    public List<SecuComYieldSpreadDynamicView> listComYieldSpreads(@NonNull Date spreadDate, @NonNull Collection<Long> udicComs) {
        DynamicQuery<SecuComYieldSpreadDynamicView> query = DynamicQuery.createQuery(SecuComYieldSpreadDynamicView.class)
                .and(SecuComYieldSpreadDynamicView::getSpreadDate, isEqual(spreadDate))
                .and(SecuComYieldSpreadDynamicView::getComUniCode, in(udicComs));
        return secuComYieldSpreadViewMapper.selectByDynamicQuery(query);
    }
}
