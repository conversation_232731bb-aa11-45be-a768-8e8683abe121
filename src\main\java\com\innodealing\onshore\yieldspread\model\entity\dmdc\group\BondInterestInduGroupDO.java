package com.innodealing.onshore.yieldspread.model.entity.dmdc.group;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 产业债利差(老表)
 *
 * <AUTHOR>
 * @date 2022/08/17
 **/
@Table(name = "bond_interest_indu")
public class BondInterestInduGroupDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 公司统一编码
     */
    @Column
    private Long comUniCode;
    /**
     * 企业类型(性质)  1央企,2国企,6民企 t_pub_par.par_sys_code=1062
     */
    @Column
    private Integer enterpriseType;
    /**
     * 债券统一编码
     */
    @Column
    private Long bondUniCode;
    /**
     * 一级行业编码
     */
    @Column
    private Long industryCode1;
    /**
     * 一级行业名称
     */
    @Column
    private String industryName1;
    /**
     * 二级行业编码
     */
    @Column
    private Long industryCode2;
    /**
     * 二级行业名称
     */
    @Column
    private String industryName2;
    /**
     * 利差日期
     */
    @Column
    private Date interestDate;
    /**
     * 信用利差
     */
    @Column
    private BigDecimal creditInterest;
    /**
     * 超额利差
     */
    @Column
    private BigDecimal excessInterest;
    /**
     * 中债估值
     */
    @Column
    private BigDecimal chinaBondValuation;
    /**
     * 国开(国债)收益率
     */
    @Column
    private BigDecimal cdbYield;
    /**
     * 超额利差数据状态 0有效 1没有评级曲线
     */
    @Column
    private Integer excessStatus;
    /**
     * 债项评级
     */
    @Column
    private String bondRating;
    /**
     * 债项隐含评级
     */
    @Column
    private String bondImpliedRating;
    /**
     * 当时剩余期限
     */
    @Column
    private Integer remainPeriod;
    /**
     * 创建时间
     */
    @Column
    private Timestamp gmtCreate;
    /**
     * 更新时间
     */
    @Column
    private Timestamp gmtModified;
    /**
     * yy评级(投资级:1、2、3、4、5,投机级:6、7、8、风险级:9、10)
     */
    @Column
    private Integer yyRating;
    /**
     * 债券类别 0 私募债(剔除永续债),1 公募债(剔除永续债), 2 永续债(公私募都有)
     */
    @Column
    private Integer bondType;
    /**
     * 隐含评级对应曲线收益率
     */
    @Column
    private BigDecimal impliedRatingYield;
    /**
     * 当前剩余期限文本
     */
    @Column
    private String remainPeriodTxt;
    /**
     * 主体评级
     */
    @Column
    private String comRating;
    /**
     * 公司名称
     */
    @Column
    private String comUniName;
    /**
     * 债券编码
     */
    @Column
    private String bondCode;
    /**
     * 债券简称
     */
    @Column
    private String bondShortName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(Integer enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Long getIndustryCode1() {
        return industryCode1;
    }

    public void setIndustryCode1(Long industryCode1) {
        this.industryCode1 = industryCode1;
    }

    public String getIndustryName1() {
        return industryName1;
    }

    public void setIndustryName1(String industryName1) {
        this.industryName1 = industryName1;
    }

    public Long getIndustryCode2() {
        return industryCode2;
    }

    public void setIndustryCode2(Long industryCode2) {
        this.industryCode2 = industryCode2;
    }

    public String getIndustryName2() {
        return industryName2;
    }

    public void setIndustryName2(String industryName2) {
        this.industryName2 = industryName2;
    }

    public Date getInterestDate() {
        return interestDate == null ? null : new Date(interestDate.getTime());
    }

    public void setInterestDate(Date interestDate) {
        this.interestDate = interestDate == null ? null : new Date(interestDate.getTime());
    }

    public BigDecimal getCreditInterest() {
        return creditInterest;
    }

    public void setCreditInterest(BigDecimal creditInterest) {
        this.creditInterest = creditInterest;
    }

    public BigDecimal getExcessInterest() {
        return excessInterest;
    }

    public void setExcessInterest(BigDecimal excessInterest) {
        this.excessInterest = excessInterest;
    }

    public BigDecimal getChinaBondValuation() {
        return chinaBondValuation;
    }

    public void setChinaBondValuation(BigDecimal chinaBondValuation) {
        this.chinaBondValuation = chinaBondValuation;
    }

    public BigDecimal getCdbYield() {
        return cdbYield;
    }

    public void setCdbYield(BigDecimal cdbYield) {
        this.cdbYield = cdbYield;
    }

    public Integer getExcessStatus() {
        return excessStatus;
    }

    public void setExcessStatus(Integer excessStatus) {
        this.excessStatus = excessStatus;
    }

    public String getBondRating() {
        return bondRating;
    }

    public void setBondRating(String bondRating) {
        this.bondRating = bondRating;
    }

    public String getBondImpliedRating() {
        return bondImpliedRating;
    }

    public void setBondImpliedRating(String bondImpliedRating) {
        this.bondImpliedRating = bondImpliedRating;
    }

    public Integer getRemainPeriod() {
        return remainPeriod;
    }

    public void setRemainPeriod(Integer remainPeriod) {
        this.remainPeriod = remainPeriod;
    }

    public Timestamp getGmtCreate() {
        return gmtCreate == null ? null : new Timestamp(gmtCreate.getTime());
    }

    public void setGmtCreate(Timestamp gmtCreate) {
        this.gmtCreate = gmtCreate == null ? null : new Timestamp(gmtCreate.getTime());
    }

    public Timestamp getGmtModified() {
        return gmtModified == null ? null : new Timestamp(gmtModified.getTime());
    }

    public void setGmtModified(Timestamp gmtModified) {
        this.gmtModified = gmtModified == null ? null : new Timestamp(gmtModified.getTime());
    }

    public Integer getYyRating() {
        return yyRating;
    }

    public void setYyRating(Integer yyRating) {
        this.yyRating = yyRating;
    }

    public Integer getBondType() {
        return bondType;
    }

    public void setBondType(Integer bondType) {
        this.bondType = bondType;
    }

    public BigDecimal getImpliedRatingYield() {
        return impliedRatingYield;
    }

    public void setImpliedRatingYield(BigDecimal impliedRatingYield) {
        this.impliedRatingYield = impliedRatingYield;
    }

    public String getRemainPeriodTxt() {
        return remainPeriodTxt;
    }

    public void setRemainPeriodTxt(String remainPeriodTxt) {
        this.remainPeriodTxt = remainPeriodTxt;
    }

    public String getComRating() {
        return comRating;
    }

    public void setComRating(String comRating) {
        this.comRating = comRating;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

}