package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 基础请求DTO
 *
 * <AUTHOR>
 */
public class BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final int PAGE_SIZE = 50;

    @ApiModelProperty("页码")
    protected Integer pageNum = 1;

    @ApiModelProperty("每页数据量")
    protected Integer pageSize = PAGE_SIZE;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

}
