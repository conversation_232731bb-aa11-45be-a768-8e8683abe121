package com.innodealing.onshore.yieldspread.service.impl;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.collect.Sets;
import com.innodealing.commons.json.JSON;
import com.innodealing.onshore.bondmetadata.constant.PermissionConstant;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.BeanCopyUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.RedisLockConst;
import com.innodealing.onshore.yieldspread.consts.TipsConst;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.yieldspread.CurveGroupDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserCurveDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.UserSelectedCurveDAO;
import com.innodealing.onshore.yieldspread.dao.yieldspread.redis.UserCurveRedisDAO;
import com.innodealing.onshore.yieldspread.enums.CurveGenerateStatusEnum;
import com.innodealing.onshore.yieldspread.enums.CurveGroupCategoryEnum;
import com.innodealing.onshore.yieldspread.enums.CurveGroupTypeEnum;
import com.innodealing.onshore.yieldspread.enums.CurveTypeEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.AssertUtil;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionBO;
import com.innodealing.onshore.yieldspread.model.bo.CurveDefinitionWithImportedBondBO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.CurveGroupDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.UserSelectedCurveDO;
import com.innodealing.onshore.yieldspread.service.CurvePoolService;
import com.innodealing.onshore.yieldspread.service.internal.BondInfoService;
import com.innodealing.onshore.yieldspread.service.internal.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.RedisLockConst.CUSTOM_CURVE_SELECTED;

/**
 * 曲线池
 *
 * <AUTHOR>
 */
@Service
public class CurvePoolServiceImpl implements CurvePoolService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CurvePoolServiceImpl.class);

    private static final int PAGING_CURVES_PAGE_SIZE = 2000;

    private static final int DENOMINATOR_TWO = 2;

    @Resource
    private UserCurveDAO userCurveDAO;

    @Resource
    private UserSelectedCurveDAO userSelectedCurveDAO;

    @Resource
    private BondInfoService bondInfoService;

    @Resource
    private UserCurveRedisDAO userCurveRedisDAO;

    @Resource
    private CurveServiceImplFactory curveServiceImplFactory;

    @Resource
    protected RedissonClient redissonClient;

    @Resource
    private CurveGroupDAO curveGroupDAO;

    @Resource
    private UserService userService;

    @Override
    public CurvePoolListResDTO listCurveDefinitions(Long userid, @Nullable String curveName, Boolean justLookSelected) {
        getDefaultGroupIdOrAddIfNotExist(userid);
        //曲线组
        List<CurveGroupDO> curveGroupDOList = curveGroupDAO.listByUserid(userid, null);
        List<CurveGroupResDTO> curveGroups = BeanCopyUtils.copyList(curveGroupDOList, CurveGroupResDTO.class);
        //曲线
        List<Long> groupIds = curveGroups.stream().map(CurveGroupResDTO::getId).collect(Collectors.toList());
        List<CurveDefinitionBO> curveBOList = userCurveDAO.listCurvesByGroupIds(groupIds, null, curveName);
        List<CurveDefinitionBasicInfoResDTO> curveDefinitionBasicInfos = convertCurveBOToBasicInfoResDTO(userid, justLookSelected, curveBOList);
        Map<Long, List<CurveDefinitionBasicInfoResDTO>> curveMap = curveDefinitionBasicInfos.stream()
                .collect(Collectors.groupingBy(CurveDefinitionBasicInfoResDTO::getCurveGroupId));

        for (CurveGroupResDTO curveGroup : curveGroups) {
            curveGroup.setCurves(curveMap.get(curveGroup.getId()));
        }
        Map<Integer, List<CurveGroupResDTO>> groupCategoryMap = curveGroups.stream().collect(Collectors.groupingBy(CurveGroupResDTO::getCurveGroupCategory));
        return new CurvePoolListResDTO(groupCategoryMap.get(CurveGroupCategoryEnum.MY_GROUP.getValue()), groupCategoryMap.get(CurveGroupCategoryEnum.BENCHMARK_GROUP.getValue()));

    }

    @Override
    public CurveDefinitionResDTO getCurveDefinition(Long userid, Long curveId) {
        Optional<CurveDefinitionWithImportedBondBO> userCurveOptional = userCurveDAO.getCurveDefinitionWithImportedBondBO(userid, curveId);
        CurveDefinitionWithImportedBondBO userCurveBO = userCurveOptional.orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        CurveDefinitionResDTO curveDefinitionResDTO = BeanCopyUtils.copyProperties(userCurveBO, CurveDefinitionResDTO.class);
        if (!userCurveBO.getSpreadCurveType().equals(CurveTypeEnum.CUSTOMIZATION.getValue())) {
            return curveDefinitionResDTO;
        }
        List<Long> bondUniCodes = JSON.parseArray(userCurveBO.getImportedBond(), Long.class);
        List<OnshoreBondInfoDTO> onshoreBondInfos = bondInfoService.listOnshoreBondInfoDTOs(bondUniCodes);
        List<BondBasicInfoResDTO> importedBonds = BeanCopyUtils.copyList(onshoreBondInfos, BondBasicInfoResDTO.class);
        curveDefinitionResDTO.setImportedBond(importedBonds);
        return curveDefinitionResDTO;
    }

    @Override
    public boolean setUpSelected(Long userid, Long curveId) {
        RLock lock = redissonClient.getLock(String.format(CUSTOM_CURVE_SELECTED, userid));
        lock.lock();
        try {
            Optional<UserSelectedCurveDO> selectedCurveOptional = userSelectedCurveDAO.getByUserid(userid);
            if (!selectedCurveOptional.isPresent()) {
                UserSelectedCurveDO selectedCurveDO = new UserSelectedCurveDO();
                selectedCurveDO.setUserId(userid);
                selectedCurveDO.setSelectedCurve(JSON.toJSONString(Sets.newHashSet(curveId)));
                return userSelectedCurveDAO.insert(selectedCurveDO);
            }
            UserSelectedCurveDO selectedCurveDO = selectedCurveOptional.get();
            Set<Long> selectedCurveIds = new HashSet<>(JSON.parseArray(selectedCurveDO.getSelectedCurve(), Long.class));
            boolean isAdd = !selectedCurveIds.contains(curveId);
            if (isAdd) {
                if (selectedCurveIds.size() >= YieldSpreadConst.SELECTED_CURVE_UPPER_LIMIT) {
                    throw new TipsException("选择的曲线已达上限");
                }
                selectedCurveIds.add(curveId);
            } else {
                selectedCurveIds.remove(curveId);
            }
            selectedCurveDO.setSelectedCurve(JSON.toJSONString(selectedCurveIds));
            selectedCurveDO.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
            return userSelectedCurveDAO.updateByPrimaryKeySelective(selectedCurveDO);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean selectedCurves(Long userid, List<Long> curveIds) {
        RLock lock = redissonClient.getLock(String.format(CUSTOM_CURVE_SELECTED, userid));
        lock.lock();
        try {
            Set<Long> curveIdSet = new LinkedHashSet<>(curveIds).stream().limit(YieldSpreadConst.SELECTED_CURVE_UPPER_LIMIT).collect(Collectors.toSet());
            String curveIdsJsonStr = JSON.toJSONString(curveIdSet);
            Optional<UserSelectedCurveDO> selectedCurveOptional = userSelectedCurveDAO.getByUserid(userid);
            if (!selectedCurveOptional.isPresent()) {
                UserSelectedCurveDO selectedCurveDO = new UserSelectedCurveDO();
                selectedCurveDO.setUserId(userid);
                selectedCurveDO.setSelectedCurve(curveIdsJsonStr);
                return userSelectedCurveDAO.insert(selectedCurveDO);
            }
            UserSelectedCurveDO selectedCurveDO = selectedCurveOptional.get();
            selectedCurveDO.setSelectedCurve(curveIdsJsonStr);
            return userSelectedCurveDAO.updateByPrimaryKeySelective(selectedCurveDO);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean clearAllSelected(Long userid) {
        Optional<UserSelectedCurveDO> selectedCurveOptional = userSelectedCurveDAO.getByUserid(userid);
        UserSelectedCurveDO selectedCurveDO = selectedCurveOptional.orElseThrow(() -> new TipsException("用户没有勾选的曲线"));
        selectedCurveDO.setSelectedCurve(JSON.toJSONString(Collections.emptySet()));
        return userSelectedCurveDAO.updateByPrimaryKeySelective(selectedCurveDO);
    }

    @Override
    public boolean removeCurve(Long userid, Long curveId) {
        if (!userCurveDAO.isExist(curveId, userid, false)) {
            throw new TipsException(TipsConst.CURVE_NOT_EXIST);
        }
        UserCurveDO userCurveDO = new UserCurveDO();
        userCurveDO.setId(curveId);
        userCurveDO.setDeleted(Deleted.DELETED.getValue());
        boolean result = userCurveDAO.updateByPrimaryKeySelective(userCurveDO);
        userCurveRedisDAO.removeCurveCache(curveId);
        Optional<UserSelectedCurveDO> selectedCurveOptional = userSelectedCurveDAO.getByUserid(userid);
        if (selectedCurveOptional.isPresent()) {
            UserSelectedCurveDO userSelectedCurveDO = selectedCurveOptional.get();
            String selectedCurve = userSelectedCurveDO.getSelectedCurve();
            if (StringUtils.isNotBlank(selectedCurve)) {
                HashSet<Long> selectedCurves = new HashSet<>(JSON.parseArray(selectedCurve, Long.class));
                if (selectedCurves.contains(curveId)) {
                    selectedCurves.remove(curveId);
                    userSelectedCurveDO.setSelectedCurve(JSON.toJSONString(selectedCurves));
                    userSelectedCurveDAO.updateByPrimaryKeySelective(userSelectedCurveDO);
                }
            }
        }
        return result;
    }

    /**
     * 批量删除曲线
     *
     * @param userid   用户id
     * @param curveIds 曲线列表
     * @return 执行结果
     */
    @Override
    public Integer batchRemoveCurve(Long userid, List<Long> curveIds) {
        if (Objects.isNull(userid) || CollectionUtils.isEmpty(curveIds)) {
            return YieldSpreadConst.INT_ZERO;
        }
        int updateNum = userCurveDAO.updateDeletedByIds(curveIds, Deleted.DELETED);
        curveIds.forEach(userCurveRedisDAO::removeCurveCache);
        Optional<UserSelectedCurveDO> selectedCurveOptional = userSelectedCurveDAO.getByUserid(userid);
        if (selectedCurveOptional.isPresent()) {
            UserSelectedCurveDO userSelectedCurveDO = selectedCurveOptional.get();
            String selectedCurve = userSelectedCurveDO.getSelectedCurve();
            if (StringUtils.isNotBlank(selectedCurve)) {
                HashSet<Long> selectedCurves = new HashSet<>(JSON.parseArray(selectedCurve, Long.class));
                int beforeSize = selectedCurves.size();
                curveIds.forEach(selectedCurves::remove);
                if (beforeSize != selectedCurves.size()) {
                    userSelectedCurveDO.setSelectedCurve(JSON.toJSONString(selectedCurves));
                    userSelectedCurveDAO.updateByPrimaryKeySelective(userSelectedCurveDO);
                }
            }
        }
        return updateNum;
    }

    @Override
    public CurveResDTO getCurve(Long userid, Long curveId, Date startDate, Date endDate) {
        return getCurve(userid, curveId, startDate, endDate, true);
    }

    @Override
    public CurveResDTO getCurve(Long userid, Long curveId, Date startDate, Date endDate, Boolean containPercentile) {
        CurveDefinitionBO curveDefinitionBO = userCurveDAO.getCurveDefinitionBO(curveId)
                .orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        List<CurveDataResDTO> userCurveData = this.listDesensitizationCurveData(userid, curveId, startDate, endDate);
        CurveResDTO curveResDTO = new CurveResDTO();
        curveResDTO.setCurveId(curveId);
        curveResDTO.setCurveName(curveDefinitionBO.getSpreadCurveName());
        curveResDTO.setCurveType(curveDefinitionBO.getSpreadCurveType());
        if (CollectionUtils.isEmpty(userCurveData)) {
            return curveResDTO;
        }
        curveResDTO.setCurveData(userCurveData);
        if (Boolean.TRUE.equals(containPercentile)) {
            this.setCurvePercentile(userCurveData, curveResDTO);
        }
        return curveResDTO;
    }

    @SuppressWarnings("squid:MethodCyclomaticComplexity")
    private List<CurveDataResDTO> permissionProcessing(Long userid, CurveTypeEnum curveType, List<CurveDataResDTO> userCurveData) {
        if (CollectionUtils.isEmpty(userCurveData) || CurveTypeEnum.CB != curveType) {
            return userCurveData;
        }
        boolean hasCurrentCbPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_BOND_CURRENT_YIELD_CURVE);
        boolean hasHistoryCbPermission = userService.hasCbPermission(userid, PermissionConstant.RESOURCE_CODE_HISTORY_BOND_YIELD_CURVE);
        // 如果都有权限，则不用做处理
        if (hasHistoryCbPermission && hasCurrentCbPermission) {
            return userCurveData;
        }
        // 如果都没权限，则返回空
        if (!hasHistoryCbPermission && !hasCurrentCbPermission) {
            return Collections.emptyList();
        }
        // 中债权限分割时间点
        Date cbPermissionDividingDate = userService.getCbPermissionDividingDate();
        if (Objects.isNull(cbPermissionDividingDate)) {
            return Collections.emptyList();
        }
        List<CurveDataResDTO> result = new ArrayList<>();
        for (CurveDataResDTO userCurveDatum : userCurveData) {
            boolean isHistoryAndHasCbPermission = userCurveDatum.getSpreadDate().before(cbPermissionDividingDate) && hasHistoryCbPermission;
            boolean isCurrentAndHasCbPermission = !userCurveDatum.getSpreadDate().before(cbPermissionDividingDate) && hasCurrentCbPermission;
            if (isHistoryAndHasCbPermission || isCurrentAndHasCbPermission) {
                result.add(userCurveDatum);
            }
        }
        return result;
    }

    @Override
    public List<CurveDataResDTO> listCurveData(Long curveId, Date startDate, Date endDate) {
        List<CurveDataResDTO> userCurveData;
        if (!userCurveRedisDAO.exist(curveId)) {
            CurveTypeEnum curveType = userCurveDAO.getCurveType(curveId).orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
            userCurveData = curveServiceImplFactory.getBondCurveService(curveType).listCurveData(curveId);
            userCurveRedisDAO.cacheCurve(curveId, userCurveData);
        }
        return userCurveRedisDAO.listCurveData(curveId, startDate, endDate);
    }

    @Override
    public List<CurveDataResDTO> listDesensitizationCurveData(Long userid, Long curveId, Date startDate, Date endDate) {
        List<CurveDataResDTO> userCurveData;
        CurveTypeEnum curveType = userCurveDAO.getCurveType(curveId).orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
        if (!userCurveRedisDAO.exist(curveId)) {
            userCurveData = curveServiceImplFactory.getBondCurveService(curveType).listCurveData(curveId);
            userCurveRedisDAO.cacheCurve(curveId, userCurveData);
        }
        List<CurveDataResDTO> curveDataResDTOs = userCurveRedisDAO.listCurveData(curveId, startDate, endDate);
        return permissionProcessing(userid, curveType, curveDataResDTOs);
    }

    @Override
    public boolean clearCurveCache(Long userid, Long curveId) {
        if (Objects.isNull(userid) && Objects.isNull(curveId)) {
            return false;
        }
        if (Objects.nonNull(curveId)) {
            try {
                return userCurveRedisDAO.removeCurveCache(curveId).get();
            } catch (Exception e) {
                LOGGER.error("clearCurveCache error.userid:{},curveId:{}", userid, curveId, e);
                return false;
            }
        }
        List<CurveDefinitionBO> curves = userCurveDAO.listCurves(userid, null);
        for (CurveDefinitionBO curve : curves) {
            try {
                userCurveRedisDAO.removeCurveCache(curve.getId()).get();
            } catch (Exception e) {
                LOGGER.error("clearCurveCache error.userid:{},curveId:{}", userid, curve.getId(), e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean clearBenchmarkCurveCache() {
        List<CurveDefinitionBO> curveDefinitions = userCurveDAO.listCurvesByType(YieldSpreadConst.BENCHMARK_CURVE_USER_ID, null);
        for (CurveDefinitionBO curveDefinition : curveDefinitions) {
            try {
                userCurveRedisDAO.removeCurveCache(curveDefinition.getId()).get();
            } catch (Exception e) {
                LOGGER.error("clearBenchmarkCurveCache error.curveId:{}", curveDefinition.getId(), e);
                return false;
            }
        }
        return true;
    }

    @Override
    public Long addGroup(Long userid, String groupName) {
        RLock lock = redissonClient.getLock(String.format(RedisLockConst.GROUP_ALTERATION, userid));
        lock.lock();
        try {
            AssertUtil.isTrue(curveGroupDAO.countGroup(userid) < YieldSpreadConst.GROUP_COUNT_UPPER_LIMIT, TipsConst.GROUP_COUNT_REMIND_UPPER_LIMIT);
            AssertUtil.isTrue(!curveGroupDAO.isExistContainBenchmarkCurveGroup(userid, groupName), TipsConst.GROUP_ALREADY_EXIST);
            return this.addGroup(userid, groupName, CurveGroupCategoryEnum.MY_GROUP, CurveGroupTypeEnum.ORDINARY_GROUP);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Boolean updateGroup(Long userid, Long groupId, String groupName) {
        CurveGroupDO curveGroup = getAndCheckGroupIsExist(groupId, userid);
        RLock lock = redissonClient.getLock(String.format(RedisLockConst.GROUP_ALTERATION, userid));
        lock.lock();
        try {
            AssertUtil.isFalse(curveGroupDAO.isExistContainBenchmarkCurveGroup(userid, groupName), TipsConst.GROUP_ALREADY_EXIST);
            AssertUtil.isTrue(Objects.equals(CurveGroupTypeEnum.ORDINARY_GROUP.getValue(), curveGroup.getCurveGroupType()), TipsConst.DEFAULT_GROUP_CANNOT_UPDATE);
            curveGroup.setCurveGroupName(groupName);
            return curveGroupDAO.updateByPrimaryKeySelective(curveGroup);
        } finally {
            lock.unlock();
        }
    }

    @Override
    @Transactional(transactionManager = YieldSpreadDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public Boolean deleteGroup(Long userid, Long groupId) {
        CurveGroupDO curveGroup = getAndCheckGroupIsExist(groupId, userid);
        AssertUtil.isTrue(Objects.equals(CurveGroupTypeEnum.ORDINARY_GROUP.getValue(), curveGroup.getCurveGroupType()), TipsConst.DEFAULT_GROUP_CANNOT_DELETE);
        //删除组下曲线和已选中曲线
        Optional<UserSelectedCurveDO> selectedCurveOptional = userSelectedCurveDAO.getByUserid(userid);
        boolean thisGroupHasCurve = true;
        if (selectedCurveOptional.isPresent()) {
            UserSelectedCurveDO userSelectedCurve = selectedCurveOptional.get();
            String selectedCurveIdsStr = userSelectedCurve.getSelectedCurve();
            if (Objects.nonNull(selectedCurveIdsStr)) {
                Set<Long> selectedCurveIds = new HashSet<>(JSON.parseArray(selectedCurveIdsStr, Long.class));
                //有选择的曲线
                if (CollectionUtils.isNotEmpty(selectedCurveIds)) {
                    int originalSelectedCurveIdSize = selectedCurveIds.size();
                    List<Long> curveIds = userCurveDAO.listCurveIdsByGroupId(groupId);
                    thisGroupHasCurve = CollectionUtils.isNotEmpty(curveIds);
                    curveIds.forEach(selectedCurveIds::remove);
                    //表示选择的曲线有要删除的曲线,所以要更新选择曲线表
                    if (selectedCurveIds.size() < originalSelectedCurveIdSize) {
                        userSelectedCurve.setSelectedCurve(JSON.toJSONString(selectedCurveIds));
                        userSelectedCurveDAO.updateByPrimaryKeySelective(userSelectedCurve);
                    }
                }
            }
        }
        if (thisGroupHasCurve) {
            userCurveDAO.deleteByGroupId(groupId);
        }
        return curveGroupDAO.delete(groupId);
    }

    @Override
    public Boolean initializeGroup() {
        RLock lock = redissonClient.getLock(RedisLockConst.GROUP_INITIALIZE);
        lock.lock();
        try {
            return doInitializeGroup();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Boolean moveCurveLocation(Long userid, Long curveId, Long targetGroupId, @Nullable Long targetLocationPreCurveId) {
        RLock lock = redissonClient.getLock(String.format(RedisLockConst.GROUP_ALTERATION, userid));
        lock.lock();
        try {
            CurveDefinitionBO curveDefinition = userCurveDAO.getCurveDefinitionBO(curveId).orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST));
            AssertUtil.isTrue(Objects.equals(userid, curveDefinition.getUserId()), TipsConst.CURVE_NOT_EXIST);
            if (!Objects.equals(curveDefinition.getCurveGroupId(), targetGroupId)) {
                CurveGroupDO curveGroup = curveGroupDAO.get(targetGroupId).orElseThrow(() -> new TipsException(TipsConst.GROUP_NOT_EXIST));
                curveDefinition.setCurveGroupId(curveGroup.getId());
            }
            curveDefinition.setCurveOrder(this.getCurveOrderForMoveLocation(curveDefinition.getCurveGroupId(), targetLocationPreCurveId));
            return userCurveDAO.updateByPrimaryKeySelective(BeanCopyUtils.copyProperties(curveDefinition, UserCurveDO.class));
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Boolean moveGroupLocation(Long userid, Long groupId, Long targetLocationPreGroupId) {
        //如果targetLocationPreGroupId为空，代表移到到第一位，也就是默认组上方
        AssertUtil.isTrue(Objects.nonNull(targetLocationPreGroupId), "无法将曲线组拖动到默认组上方，请调整拖动位置");
        RLock lock = redissonClient.getLock(String.format(RedisLockConst.GROUP_ALTERATION, userid));
        lock.lock();
        try {
            CurveGroupDO curveGroup = curveGroupDAO.get(groupId).orElseThrow(() -> new TipsException(TipsConst.GROUP_NOT_EXIST));
            AssertUtil.isTrue(Objects.equals(userid, curveGroup.getUserId()), TipsConst.GROUP_NOT_EXIST);
            AssertUtil.isTrue(Objects.equals(curveGroup.getCurveGroupType(), CurveGroupTypeEnum.ORDINARY_GROUP.getValue()), "只能移动我的曲线组中的非默认组");
            curveGroup.setCurveGroupOrder(this.getGroupOrderForMoveLocation(userid, targetLocationPreGroupId));
            return curveGroupDAO.updateByPrimaryKeySelective(curveGroup);
        } finally {
            lock.unlock();
        }

    }

    @Override
    public Long getDefaultGroupIdOrAddIfNotExist(Long userid) {
        RLock lock = redissonClient.getLock(String.format(RedisLockConst.GROUP_ALTERATION, userid));
        lock.lock();
        try {
            // 如果用户还没有默认组，需要加默认组
            return curveGroupDAO.getDefaultGroupId(userid)
                    .orElseGet(() -> this.addGroup(userid, YieldSpreadConst.DEFAULT_GROUP_NAME, CurveGroupCategoryEnum.MY_GROUP, CurveGroupTypeEnum.DEFAULT_GROUP));
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Integer getCurveNextOrder(Long groupId) {
        Optional<Integer> nextOrderOptional = userCurveDAO.getMaxOrder(groupId);
        if (nextOrderOptional.isPresent()) {
            int nextOrder = nextOrderOptional.get();
            //用户可以创建(Integer.MAX_VALUE-1)/YieldSpreadConst.CURVE_ORDER_STEP = 32767个曲线
            if (nextOrder >= Integer.MAX_VALUE - YieldSpreadConst.CURVE_ORDER_STEP) {
                return this.rebuildCurveOrder(groupId) + YieldSpreadConst.CURVE_ORDER_STEP;
            }
            return nextOrder + YieldSpreadConst.CURVE_ORDER_STEP;
        }
        return YieldSpreadConst.CURVE_ORDER_STEP;
    }

    private int getGroupNextOrder(Long userid, CurveGroupCategoryEnum curveGroupCategory) {
        CurveGroupTypeEnum groupType = CurveGroupCategoryEnum.MY_GROUP == curveGroupCategory ? CurveGroupTypeEnum.ORDINARY_GROUP : CurveGroupTypeEnum.BENCHMARK_GROUP;
        Optional<Integer> nextOrderOptional = curveGroupDAO.getMaxOrder(userid, groupType);
        if (nextOrderOptional.isPresent()) {
            int nextOrder = nextOrderOptional.get();
            //用户可以创建(Integer.MAX_VALUE-1)/YieldSpreadConst.GROUP_ORDER_STEP = 32767个组
            if (nextOrder >= YieldSpreadConst.DEFAULT_GROUP_ORDER - YieldSpreadConst.GROUP_ORDER_STEP) {
                return this.rebuildGroupOrder(userid, CurveGroupCategoryEnum.MY_GROUP) + YieldSpreadConst.GROUP_ORDER_STEP;
            }
            return nextOrder + YieldSpreadConst.GROUP_ORDER_STEP;
        }
        return YieldSpreadConst.GROUP_ORDER_STEP;
    }

    /**
     * 从移动的目标位置获取曲线序号
     *
     * @param targetGroupId            目标组id
     * @param targetLocationPreCurveId 目标位置的前一个曲线id
     * @return 曲线序号
     */
    private int getCurveOrderForMoveLocation(Long targetGroupId, @Nullable Long targetLocationPreCurveId) {
        //表示第一个位置
        if (Objects.isNull(targetLocationPreCurveId)) {
            return getCurveNextOrder(targetGroupId);
        }
        //否则获取两个order取中间值
        int preCurveOrder = userCurveDAO.getCurveDefinitionBO(targetLocationPreCurveId).orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST)).getCurveOrder();
        int afterCurveOrder = userCurveDAO.getLessCurveOrder(targetGroupId, preCurveOrder).orElse(0);
        int resultOrder = (preCurveOrder + afterCurveOrder) / DENOMINATOR_TWO;
        //如果这两个值相等，说明中间没有值可以用来作为序号了，所以要重建序号
        if (resultOrder == afterCurveOrder) {
            //重建曲线排序
            this.rebuildCurveOrder(targetGroupId);
            //再获取一次最新的order
            //下面逻辑可以递归，但不递归可以少查一次数据库
            preCurveOrder = userCurveDAO.getCurveDefinitionBO(targetLocationPreCurveId).orElseThrow(() -> new TipsException(TipsConst.CURVE_NOT_EXIST)).getCurveOrder();
            afterCurveOrder = preCurveOrder - YieldSpreadConst.CURVE_ORDER_STEP;
            resultOrder = (preCurveOrder + afterCurveOrder) / DENOMINATOR_TWO;
        }
        return resultOrder;
    }

    /**
     * 从移动的目标位置获取曲线组序号
     *
     * @param userid                   用户id
     * @param targetLocationPreGroupId 目标位置的前一个曲线组id
     * @return 曲线组序号
     */
    private int getGroupOrderForMoveLocation(Long userid, Long targetLocationPreGroupId) {
        //表示第一个位置
        if (Objects.isNull(targetLocationPreGroupId)) {
            return this.getGroupNextOrder(userid, CurveGroupCategoryEnum.MY_GROUP);
        }
        //如果目标位置组是默认组，相当于是第一的位置，因为默认组永远置顶
        CurveGroupDO curveGroupDO = curveGroupDAO.get(targetLocationPreGroupId).orElseThrow(() -> new TipsException(TipsConst.GROUP_NOT_EXIST));
        if (Objects.equals(curveGroupDO.getCurveGroupType(), CurveGroupTypeEnum.DEFAULT_GROUP.getValue())) {
            return this.getGroupNextOrder(userid, CurveGroupCategoryEnum.MY_GROUP);
        }
        //否则获取两个order取中间值
        int preGroupOrder = curveGroupDO.getCurveGroupOrder();
        int afterGroupOrder = curveGroupDAO.getLessGroupOrder(userid, preGroupOrder).orElse(0);
        int resultOrder = (preGroupOrder + afterGroupOrder) / DENOMINATOR_TWO;
        if (resultOrder == afterGroupOrder) {
            //重建组排序
            this.rebuildGroupOrder(userid, CurveGroupCategoryEnum.MY_GROUP);
            //再获取一次最新的order
            //下面逻辑可以递归，但不递归可以少查一次数据库
            preGroupOrder = curveGroupDAO.get(targetLocationPreGroupId).orElseThrow(() -> new TipsException(TipsConst.GROUP_NOT_EXIST)).getCurveGroupOrder();
            afterGroupOrder = preGroupOrder - YieldSpreadConst.CURVE_ORDER_STEP;
            resultOrder = (preGroupOrder + afterGroupOrder) / DENOMINATOR_TWO;
        }
        return resultOrder;
    }

    private Boolean doInitializeGroup() {
        Set<Long> alreadyInitializeGroupUserIds = new HashSet<>();
        int pageNum = 1;
        while (true) {
            NormPagingResult<UserCurveDO> curvesPagingResult = userCurveDAO.pageCurves(pageNum, PAGING_CURVES_PAGE_SIZE);
            List<UserCurveDO> curves = curvesPagingResult.getList();
            for (UserCurveDO curve : curves) {
                Long userid = curve.getUserId();
                if (alreadyInitializeGroupUserIds.contains(userid)) {
                    continue;
                }
                //防止初始化过程中有用户插入数据
                RLock userLock = redissonClient.getLock(String.format(RedisLockConst.GROUP_ALTERATION, userid));
                userLock.lock();
                try {
                    //如果是基准曲线
                    if (Objects.equals(YieldSpreadConst.BENCHMARK_CURVE_USER_ID, userid)) {
                        this.initBenchmarkGroup();
                    } else {
                        this.initMyGroup(userid);
                    }
                } finally {
                    userLock.unlock();
                }
                alreadyInitializeGroupUserIds.add(userid);
            }
            if (!curvesPagingResult.isHasNextPage()) {
                return Boolean.TRUE;
            }
            pageNum++;
        }
    }

    private void initBenchmarkGroup() {
        Long userid = YieldSpreadConst.BENCHMARK_CURVE_USER_ID;
        List<CurveDefinitionBO> userCurves = userCurveDAO.listCurves(userid, null);
        Map<String, CurveDefinitionBO> curveMap = userCurves.stream().collect(Collectors.toMap(CurveDefinitionBO::getSpreadCurveName, Function.identity(), (k1, k2) -> k1));
        // 曲线组名:曲线组id
        Map<String, Long> groupIdMap = new HashMap<>(userCurves.size());
        //组id:order
        Map<Long, Integer> curveOrderMap = new HashMap<>(userCurves.size());
        for (Map.Entry<String, List<String>> entry : ChinaBondCurveService.getBenchmarkGroupCurveMap().entrySet()) {
            String groupName = entry.getKey();
            Long groupId = groupIdMap.get(groupName);
            if (Objects.isNull(groupId)) {
                Optional<CurveGroupDO> curveGroupOptional = curveGroupDAO.get(userid, groupName);
                groupId = curveGroupOptional.isPresent() ?
                        curveGroupOptional.get().getId() : this.addGroup(userid, groupName, CurveGroupCategoryEnum.BENCHMARK_GROUP, CurveGroupTypeEnum.BENCHMARK_GROUP);
                groupIdMap.put(groupName, groupId);
            }
            for (String curveName : entry.getValue()) {
                CurveDefinitionBO curveDefinitionBO = curveMap.get(curveName);
                if (Objects.isNull(curveDefinitionBO)) {
                    continue;
                }
                curveDefinitionBO.setCurveGroupId(groupId);
                Integer order = curveOrderMap.get(groupId);
                order = Objects.isNull(order) ? YieldSpreadConst.CURVE_ORDER_STEP : (YieldSpreadConst.CURVE_ORDER_STEP + order);
                curveOrderMap.put(groupId, order);
                curveDefinitionBO.setCurveOrder(order);
                userCurveDAO.updateByPrimaryKeySelective(BeanCopyUtils.copyProperties(curveDefinitionBO, UserCurveDO.class));

            }
        }
    }

    private void initMyGroup(Long userid) {
        Optional<Long> defaultGroupIdOptional = curveGroupDAO.getDefaultGroupId(userid);
        Long groupId = defaultGroupIdOptional
                .orElseGet(() -> this.addGroup(userid, YieldSpreadConst.DEFAULT_GROUP_NAME, CurveGroupCategoryEnum.MY_GROUP, CurveGroupTypeEnum.DEFAULT_GROUP));
        List<CurveDefinitionBO> userCurves = userCurveDAO.listCurves(userid, null);
        Map<Long, Integer> curveOrderMap = new HashMap<>(userCurves.size());
        for (int i = userCurves.size() - 1; i >= 0; i--) {
            CurveDefinitionBO userCurve = userCurves.get(i);
            Long oldGroupId = userCurve.getCurveGroupId();
            if (Objects.isNull(oldGroupId)) {
                userCurve.setCurveGroupId(groupId);
            } else if (!Objects.equals(groupId, oldGroupId)) {
                continue;
            }
            Integer order = curveOrderMap.get(groupId);
            order = Objects.isNull(order) ? YieldSpreadConst.CURVE_ORDER_STEP : (YieldSpreadConst.CURVE_ORDER_STEP + order);
            curveOrderMap.put(groupId, order);
            userCurve.setCurveOrder(order);
            userCurveDAO.updateByPrimaryKeySelective(BeanCopyUtils.copyProperties(userCurve, UserCurveDO.class));
        }
    }

    private Long addGroup(Long userid, String groupName, CurveGroupCategoryEnum groupCategory, CurveGroupTypeEnum groupType) {
        CurveGroupDO curveGroup = new CurveGroupDO();
        curveGroup.setUserId(userid);
        curveGroup.setCurveGroupName(groupName);
        curveGroup.setCurveGroupCategory(groupCategory.getValue());
        curveGroup.setCurveGroupType(groupType.getValue());
        curveGroup.setCurveGroupOrder(CurveGroupTypeEnum.DEFAULT_GROUP == groupType ? YieldSpreadConst.DEFAULT_GROUP_ORDER : this.getGroupNextOrder(userid, groupCategory));
        curveGroup.setDeleted(Deleted.NO_DELETED.getValue());
        AssertUtil.isTrue(curveGroupDAO.insert(curveGroup), TipsConst.GROUP_ADD_FAIL);
        return curveGroup.getId();
    }

    private List<CurveDefinitionBasicInfoResDTO> convertCurveBOToBasicInfoResDTO(Long userid, Boolean justLookSelected, List<CurveDefinitionBO> curveBOList) {
        Set<Long> selectedCurveIds = new HashSet<>();
        Optional<UserSelectedCurveDO> selectedCurveOptional = userSelectedCurveDAO.getByUserid(userid);
        if (selectedCurveOptional.isPresent() && StringUtils.isNotBlank(selectedCurveOptional.get().getSelectedCurve())) {
            selectedCurveIds = new HashSet<>(JSON.parseArray(selectedCurveOptional.get().getSelectedCurve(), Long.class));
        }
        List<CurveDefinitionBasicInfoResDTO> result = new ArrayList<>();
        for (CurveDefinitionBO curve : curveBOList) {
            //如果是自选债，只显示生成成功的；如果只看已选，就只显示用户选择了的
            boolean isCustomizationAndNotSucceed = curve.getSpreadCurveType().equals(CurveTypeEnum.CUSTOMIZATION.getValue()) &&
                    !curve.getGenerateStatus().equals(CurveGenerateStatusEnum.SUCCEED.getValue());
            boolean isNotSelected = justLookSelected && !selectedCurveIds.contains(curve.getId());
            if (isCustomizationAndNotSucceed || isNotSelected) {
                continue;
            }
            CurveDefinitionBasicInfoResDTO curveBasicInfo = BeanCopyUtils.copyProperties(curve, CurveDefinitionBasicInfoResDTO.class);
            curveBasicInfo.setSelected(selectedCurveIds.contains(curve.getId()));
            result.add(curveBasicInfo);
        }
        return result;
    }

    /**
     * 重建组排序
     *
     * @param curveGroupCategory 组类别
     * @param userid             用户id
     * @return 重建后最大的序号
     */
    private int rebuildGroupOrder(Long userid, CurveGroupCategoryEnum curveGroupCategory) {
        List<CurveGroupDO> curveGroups = curveGroupDAO.listByUserid(userid, curveGroupCategory);
        int order = 0;
        if (CollectionUtils.isEmpty(curveGroups)) {
            return order;
        }
        for (int i = curveGroups.size() - 1; i >= 0; i--) {
            CurveGroupDO curveGroup = curveGroups.get(i);
            if (Objects.equals(CurveGroupTypeEnum.DEFAULT_GROUP.getValue(), curveGroup.getCurveGroupType())) {
                continue;
            }
            order += YieldSpreadConst.GROUP_ORDER_STEP;
            curveGroup.setCurveGroupOrder(order);
            curveGroupDAO.updateByPrimaryKeySelective(curveGroup);
        }
        return order;
    }

    /**
     * 重建曲线排序
     *
     * @param groupId 组id
     * @return 重建后最大的序号
     */
    private int rebuildCurveOrder(Long groupId) {
        List<CurveDefinitionBO> curves = userCurveDAO.listCurvesByGroupIds(Collections.singletonList(groupId), null, null);
        int order = 0;
        if (CollectionUtils.isEmpty(curves)) {
            return order;
        }
        for (int i = curves.size() - 1; i >= 0; i--) {
            order += YieldSpreadConst.CURVE_ORDER_STEP;
            CurveDefinitionBO curve = curves.get(i);
            curve.setCurveOrder(order);
            userCurveDAO.updateByPrimaryKeySelective(BeanCopyUtils.copyProperties(curve, UserCurveDO.class));
        }
        return order;
    }

    private CurveGroupDO getAndCheckGroupIsExist(Long groupId, Long userid) {
        Optional<CurveGroupDO> curveGroupOptional = curveGroupDAO.get(groupId);
        CurveGroupDO curveGroup = curveGroupOptional.orElseThrow(() -> new TipsException(TipsConst.GROUP_NOT_EXIST));
        AssertUtil.isTrue(Objects.equals(curveGroup.getUserId(), userid), TipsConst.GROUP_NOT_EXIST);
        return curveGroup;
    }

    private void setCurvePercentile(List<CurveDataResDTO> curveData, CurveResDTO curveResDTO) {
        int size = curveData.size();
        List<BigDecimal> creditSpreads = new ArrayList<>(size);
        List<BigDecimal> excessSpreads = new ArrayList<>(size);
        List<BigDecimal> cbYields = new ArrayList<>(size);
        List<BigDecimal> avgCreditSpreads = new ArrayList<>(size);
        List<BigDecimal> avgExcessSpreads = new ArrayList<>(size);
        List<BigDecimal> avgCbYields = new ArrayList<>(size);
        for (CurveDataResDTO curveDatum : curveData) {
            Optional.ofNullable(curveDatum.getBondCreditSpread()).ifPresent(creditSpreads::add);
            Optional.ofNullable(curveDatum.getBondExcessSpread()).ifPresent(excessSpreads::add);
            Optional.ofNullable(curveDatum.getCbYield()).ifPresent(cbYields::add);
            Optional.ofNullable(curveDatum.getAvgBondCreditSpread()).ifPresent(avgCreditSpreads::add);
            Optional.ofNullable(curveDatum.getAvgBondExcessSpread()).ifPresent(avgExcessSpreads::add);
            Optional.ofNullable(curveDatum.getAvgCbYield()).ifPresent(avgCbYields::add);
        }
        curveResDTO.setMedianPercentile(getPercentile(creditSpreads, excessSpreads, cbYields));
        curveResDTO.setAvgPercentile(getPercentile(avgCreditSpreads, avgExcessSpreads, avgCbYields));
    }

    private CurvePercentileResDTO getPercentile(List<BigDecimal> creditSpreads, List<BigDecimal> excessSpreads, List<BigDecimal> cbYields) {
        CurvePercentileResDTO percentile = new CurvePercentileResDTO();
        percentile.setCreditSpreadPercentile25(YieldSpreadHelper.getPercentile(creditSpreads, YieldSpreadConst.PERCENTILE25, true));
        percentile.setCreditSpreadPercentile75(YieldSpreadHelper.getPercentile(creditSpreads, YieldSpreadConst.PERCENTILE75, true));
        percentile.setExcessSpreadPercentile25(YieldSpreadHelper.getPercentile(excessSpreads, YieldSpreadConst.PERCENTILE25, true));
        percentile.setExcessSpreadPercentile75(YieldSpreadHelper.getPercentile(excessSpreads, YieldSpreadConst.PERCENTILE75, true));
        percentile.setCbYieldPercentile25(YieldSpreadHelper.getPercentile(cbYields, YieldSpreadConst.PERCENTILE25, true));
        percentile.setCbYieldPercentile75(YieldSpreadHelper.getPercentile(cbYields, YieldSpreadConst.PERCENTILE75, true));
        return percentile;
    }

}
