package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.ImplicitRatingTagEnum;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.yieldspread.enums.IBondImpliedRating;
import com.innodealing.onshore.yieldspread.enums.SpreadRemainingTenorTagEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 曲线生成请求参数基类
 *
 * <AUTHOR>
 */
public abstract class AbstractCurveGenerateConditionReqDTO implements ICurveGenerateReq {

    /**
     * @see com.innodealing.onshore.bondmetadata.enums.ImplicitRatingTagEnum
     */
    @ApiModelProperty("中债隐含评级 10:AAA+,20:AAA,30:AAA-,40:AA+,50:AA,55:AA(2),60:AA-,70:A+,80:A,90:A-")
    private Integer[] bondImpliedRatingMappings;

    /**
     * @see SpreadRemainingTenorTagEnum
     */
    @ApiModelProperty("剩余期限(1,2,3,4,5)")
    private Integer remainingTenor;

    @ApiModelProperty("搜索主体或债券，没有时不传值")
    private ComOrBondConditionReqDTO comOrBondCondition;

    protected static final String SEPARATOR = ",";

    protected static final String OBLIQUE_LINE = "/";

    protected <T extends IBondImpliedRating> String jointBondImpliedRatingName(Class<T> clazz) {
        if (ArrayUtils.isEmpty(this.bondImpliedRatingMappings)) {
            return "";
        }
        Integer impliedRating = YieldSpreadHelper.getBondImpliedRatingMappingTagMap().get(this.bondImpliedRatingMappings[0]);
        IBondImpliedRating impliedEnum = EnumUtils.getEnumByValue(impliedRating, clazz).orElseThrow(() -> new TipsException("隐含评级不正确"));
        return this.jointBondImpliedRatingName(impliedEnum);
    }

    private String jointBondImpliedRatingName(IBondImpliedRating impliedEnum) {
        Integer[] mapping = impliedEnum.getMapping();
        Arrays.sort(this.bondImpliedRatingMappings);
        boolean isSame = Arrays.equals(mapping, this.bondImpliedRatingMappings);
        StringBuilder sb = new StringBuilder(SEPARATOR).append("中债隐含");
        if (isSame) {
            return sb.append(impliedEnum.getText()).toString();
        } else {
            for (Integer rating : this.bondImpliedRatingMappings) {
                sb.append(EnumUtils.getEnum(ImplicitRatingTagEnum.class, rating).getText()).append(OBLIQUE_LINE);
            }
            return sb.substring(0, sb.length() - 1);
        }
    }

    protected String jointRemainingTenorName() {
        return Objects.nonNull(this.remainingTenor) ?
                (SEPARATOR + EnumUtils.getEnum(SpreadRemainingTenorTagEnum.class, this.remainingTenor).getDesc()) : "";
    }

    protected String jointShortName() {
        return Objects.isNull(this.comOrBondCondition) ? "" : (SEPARATOR + this.comOrBondCondition.getShortName());
    }

    public Integer[] getBondImpliedRatingMappings() {
        return Objects.isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public void setBondImpliedRatingMappings(Integer[] bondImpliedRatingMappings) {
        this.bondImpliedRatingMappings = Objects.isNull(bondImpliedRatingMappings) ? new Integer[0] : bondImpliedRatingMappings.clone();
    }

    public Integer getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(Integer remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public ComOrBondConditionReqDTO getComOrBondCondition() {
        return comOrBondCondition;
    }

    public void setComOrBondCondition(ComOrBondConditionReqDTO comOrBondCondition) {
        this.comOrBondCondition = comOrBondCondition;
    }

}
