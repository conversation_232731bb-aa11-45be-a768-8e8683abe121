<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.yieldspread.LgBondYieldSpreadMapper">
    <update id="createShardingTable" parameterType="String">
        CREATE TABLE IF NOT EXISTS ${tableName}(
            `id` bigint(20) unsigned NOT NULL COMMENT '主键id',
            `com_uni_code` bigint(20) DEFAULT NULL COMMENT '发行人代码',
            `lg_area_name` varchar(10) DEFAULT NULL COMMENT '地方债地区',
            `bond_uni_code` bigint(20) NOT NULL COMMENT '债券统一编码',
            `bond_code` varchar(64) DEFAULT NULL COMMENT '债券编码',
            `spread_date` date NOT NULL COMMENT '利差日期',
            `cb_yield` decimal(12,4) DEFAULT NULL COMMENT '中债收益率;单位(%)',
            `cdb_lerp_yield` decimal(12,4) DEFAULT NULL COMMENT '国开插值收益率;单位(%)',
            `bond_credit_spread` decimal(12,4) DEFAULT NULL COMMENT '债券信用利差;单位(BP)',
            `tb_lerp_yield` decimal(12,4) DEFAULT NULL COMMENT '国债插值收益率;单位(%)',
            `bond_credit_spread_tb` decimal(12,4) DEFAULT NULL COMMENT '国债-信用利差;单位(BP)',
            `bond_excess_spread` decimal(12,4) DEFAULT NULL COMMENT '债券超额利差;单位(BP)',
            `lg_bond_type` tinyint(4) DEFAULT NULL COMMENT '地方债类型： 1 一般债; 2 专项债;  99 其他',
            `prepayment_status` tinyint(4) unsigned DEFAULT NULL COMMENT '提前还本状态 0: 不提前还本 1: 提前还本',
            `fund_use_type` tinyint(4) unsigned DEFAULT NULL COMMENT '资金用途性质: 1 新增; 2 再融资; 3 置换;4 特殊再融资;  99 其他',
            `excess_spread_status` tinyint(3) unsigned DEFAULT NULL COMMENT '超额利差数据状态 0有效 1没有评级曲线',
            `bond_ext_rating_mapping` smallint(5) unsigned DEFAULT NULL COMMENT '债券外部评级映射',
            `com_ext_rating_mapping` smallint(5) unsigned DEFAULT NULL COMMENT '主体外部评级映射',
            `spread_remaining_tenor_tag` smallint(5) unsigned DEFAULT NULL COMMENT '利差剩余期限标签',
            `remaining_tenor` varchar(20) DEFAULT NULL COMMENT '剩余期限',
            `remaining_tenor_day` int(11) DEFAULT NULL COMMENT '剩余期限天数',
            `lg_remaining_grade` smallint(5) DEFAULT NULL COMMENT '期限档位',
            `lg_remaining_grade_name` varchar(10) DEFAULT NULL COMMENT '期限档位名称',
            `bond_balance` decimal(16,2) DEFAULT NULL COMMENT '债券余额(万)',
            `deleted` tinyint(3) unsigned NOT NULL COMMENT '是否删除：0： 未删除;1：已删除',
            `create_time` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
            `update_time` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_spread_date_bond_uni_code` (`spread_date`,`bond_uni_code`),
            KEY `idx_bond_uni_code` (`bond_uni_code`)
            )  COMMENT='地方债利差';
    </update>
</mapper>