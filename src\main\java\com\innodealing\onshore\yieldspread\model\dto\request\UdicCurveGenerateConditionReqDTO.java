package com.innodealing.onshore.yieldspread.model.dto.request;

import com.innodealing.onshore.bondmetadata.enums.AreaTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadUdicBondImpliedRatingMappingTagEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadUdicComYyRatingMappingTagEnum;
import com.innodealing.onshore.yieldspread.exception.TipsException;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 城投曲线生成筛选条件
 *
 * <AUTHOR>
 */
public class UdicCurveGenerateConditionReqDTO extends AbstractInduAndUdicPartCommonConditionReqDTO {

    @ApiModelProperty("省市区县编码")
    private Long areaCode;

    @ApiModelProperty("省市区县名称")
    private String areaName;

    /**
     * @see AreaTypeEnum
     */
    @ApiModelProperty("行政区划")
    private Integer administrativeDivision;

    private static final String CURVE_NAME_PREFIX = "城投债";

    private static final Map<Integer, String> ADMINISTRATIVE_DIVISION_NAME_MAP = initAdministrativeDivisionNameMap();

    private static Map<Integer, String> initAdministrativeDivisionNameMap() {
        Map<Integer, String> map = new HashMap<>(AreaTypeEnum.values().length);
        map.put(AreaTypeEnum.PROVINCE.getValue(), "省级");
        map.put(AreaTypeEnum.CITY.getValue(), "市级");
        map.put(AreaTypeEnum.DISTRICT.getValue(), "区/县级");
        map.put(AreaTypeEnum.ZONE.getValue(), "园区");
        return map;
    }

    @Override
    public String getCurveName() {
        return (StringUtils.isBlank(areaName) ? CURVE_NAME_PREFIX : areaName) +
                super.jointShortName() +
                super.jointSpreadBondTypeName() +
                super.jointGuaranteeStatusName() +
                super.jointExtRatingName() +
                super.jointBondImpliedRatingName(SpreadUdicBondImpliedRatingMappingTagEnum.class) +
                this.jointYyRatingName() +
                this.jointAdministrativeDivisionName() +
                super.jointRemainingTenorName();
    }

    private String jointYyRatingName() {
        if (ArrayUtils.isEmpty(this.comYyRatingMappings)) {
            return "";
        }
        SpreadUdicComYyRatingMappingTagEnum comYyRatingMappingEnum = SpreadUdicComYyRatingMappingTagEnum
                .getComYyRatingEnum(comYyRatingMappings[0]).orElseThrow(() -> new TipsException("YY评级不正确"));
        Integer[] mapping = comYyRatingMappingEnum.getMapping();
        Arrays.sort(comYyRatingMappings);
        boolean isSame = Arrays.equals(mapping, comYyRatingMappings);
        StringBuilder sb = new StringBuilder(SEPARATOR).append("YY");
        if (isSame) {
            return sb.append(comYyRatingMappingEnum.getText()).toString();
        } else {
            for (Integer comYyRatingMapping : comYyRatingMappings) {
                sb.append(comYyRatingMapping).append(OBLIQUE_LINE);
            }
            return sb.substring(0, sb.length() - 1);
        }
    }

    private String jointAdministrativeDivisionName() {
        return Objects.nonNull(administrativeDivision) ? (SEPARATOR + ADMINISTRATIVE_DIVISION_NAME_MAP.get(administrativeDivision)) : "";
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getAdministrativeDivision() {
        return administrativeDivision;
    }

    public void setAdministrativeDivision(Integer administrativeDivision) {
        this.administrativeDivision = administrativeDivision;
    }

    public Long getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(Long areaCode) {
        this.areaCode = areaCode;
    }

}
