package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * 城投主体利差
 *
 * <AUTHOR>
 **/
@Table(name = "udic_com_yield_spread")
public class UdicComYieldSpreadDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 发行人代码
     */
    @Column
    private Long comUniCode;
    /**
     * 省份编码
     */
    @Column
    private Long provinceUniCode;
    /**
     * 省份名称
     */
    @Column
    private String provinceName;
    /**
     * 地级市编码
     */
    @Column
    private Long cityUniCode;
    /**
     * 城市名称
     */
    @Column
    private String cityName;
    /**
     * 区县编码
     */
    @Column
    private Long districtUniCode;
    /**
     * 区县名称
     */
    @Column
    private String districtName;
    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;
    /**
     * 实际控制人全称
     */
    @Column
    private String actualControllerFullName;
    /**
     * 行政区域
     */
    @Column
    private Integer administrativeRegion;
    /**
     * 区域名称
     */
    @Column
    private String areaName;
    /**
     * 主体外部评级映射
     */
    @Column
    private Integer comExtRatingMapping;
    /**
     * 信用利差(全部债券);单位(BP)
     */
    @Column
    private BigDecimal comCreditSpread;
    /**
     * 超额利差(全部债券);单位(BP)
     */
    @Column
    private BigDecimal comExcessSpread;
    /**
     * 信用利差(公募);单位(BP)
     */
    @Column
    private BigDecimal comPublicCreditSpread;
    /**
     * 超额利差(公募);单位(BP)
     */
    @Column
    private BigDecimal comPublicExcessSpread;
    /**
     * 信用利差(私募);单位(BP)
     */
    @Column
    private BigDecimal comPrivateCreditSpread;
    /**
     * 超额利差(私募);单位(BP)
     */
    @Column
    private BigDecimal comPrivateExcessSpread;
    /**
     * 信用利差(永续);单位(BP)
     */
    @Column
    private BigDecimal comPerpetualCreditSpread;
    /**
     * 超额利差(永续);单位(BP)
     */
    @Column
    private BigDecimal comPerpetualExcessSpread;
    /**
     * 估值收益率(全部债券);单位(%)
     */
    @Column
    private BigDecimal comCbYield;
    /**
     * 估值收益率(公募);单位(%)
     */
    @Column
    private BigDecimal comPublicCbYield;
    /**
     * 估值收益率(私募);单位(%)
     */
    @Column
    private BigDecimal comPrivateCbYield;
    /**
     * 估值收益率(永续);单位(%)
     */
    @Column
    private BigDecimal comPerpetualCbYield;
    /**
     * 债券余额(万)
     */
    @Column
    private BigDecimal bondBalance;
    /**
     * 城投有息债务(单位:万元)
     */
    @Column
    private BigDecimal hideDebt;
    /**
     * 总资产(万元)
     */
    @Column
    private BigDecimal totalAssets;
    /**
     * 资产负债率%
     */
    @Column
    private BigDecimal assetLiabilityRatio;
    /**
     * 是否删除：0： 未删除;1：已删除
     */
    @Column
    private Integer deleted;

    public Long getDistrictUniCode() {
        return districtUniCode;
    }

    public void setDistrictUniCode(Long districtUniCode) {
        this.districtUniCode = districtUniCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Date getSpreadDate() {
        return spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = spreadDate == null ? null : new Date(spreadDate.getTime());
    }

    public String getActualControllerFullName() {
        return actualControllerFullName;
    }

    public void setActualControllerFullName(String actualControllerFullName) {
        this.actualControllerFullName = actualControllerFullName;
    }

    public Integer getAdministrativeRegion() {
        return administrativeRegion;
    }

    public void setAdministrativeRegion(Integer administrativeRegion) {
        this.administrativeRegion = administrativeRegion;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getComPublicCreditSpread() {
        return comPublicCreditSpread;
    }

    public void setComPublicCreditSpread(BigDecimal comPublicCreditSpread) {
        this.comPublicCreditSpread = comPublicCreditSpread;
    }

    public BigDecimal getComPublicExcessSpread() {
        return comPublicExcessSpread;
    }

    public void setComPublicExcessSpread(BigDecimal comPublicExcessSpread) {
        this.comPublicExcessSpread = comPublicExcessSpread;
    }

    public BigDecimal getComPrivateCreditSpread() {
        return comPrivateCreditSpread;
    }

    public void setComPrivateCreditSpread(BigDecimal comPrivateCreditSpread) {
        this.comPrivateCreditSpread = comPrivateCreditSpread;
    }

    public BigDecimal getComPrivateExcessSpread() {
        return comPrivateExcessSpread;
    }

    public void setComPrivateExcessSpread(BigDecimal comPrivateExcessSpread) {
        this.comPrivateExcessSpread = comPrivateExcessSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComPublicCbYield() {
        return comPublicCbYield;
    }

    public void setComPublicCbYield(BigDecimal comPublicCbYield) {
        this.comPublicCbYield = comPublicCbYield;
    }

    public BigDecimal getComPrivateCbYield() {
        return comPrivateCbYield;
    }

    public void setComPrivateCbYield(BigDecimal comPrivateCbYield) {
        this.comPrivateCbYield = comPrivateCbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public BigDecimal getHideDebt() {
        return hideDebt;
    }

    public void setHideDebt(BigDecimal hideDebt) {
        this.hideDebt = hideDebt;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getAssetLiabilityRatio() {
        return assetLiabilityRatio;
    }

    public void setAssetLiabilityRatio(BigDecimal assetLiabilityRatio) {
        this.assetLiabilityRatio = assetLiabilityRatio;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

}