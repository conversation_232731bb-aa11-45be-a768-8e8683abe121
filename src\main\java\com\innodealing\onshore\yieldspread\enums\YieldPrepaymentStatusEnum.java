package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 利差中对 地方债类型枚举的重新定义
 *
 * <AUTHOR>
 * @date 2024/11/18 18:00
 **/
public enum YieldPrepaymentStatusEnum implements ITextValueEnum {

    /**
     * 地方债类型枚举
     */
    MATU_REPAY(0, "到期还本"),
    ADVANCE_REPAY(1, "提前还本");

    private final Integer value;
    private final String text;

    YieldPrepaymentStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
