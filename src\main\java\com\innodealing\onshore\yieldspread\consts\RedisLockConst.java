package com.innodealing.onshore.yieldspread.consts;

/**
 * redis lock
 *
 * <AUTHOR>
 */
public final class RedisLockConst {

    private RedisLockConst() {
    }

    /**
     * onshoreYieldSpread:curve_pool:userid
     */
    public static final String CURVE_POOL_USER = "onshoreYieldSpread:curve_pool:lock:%d";

    /**
     * 自定义曲线中选中未选中某条曲线锁
     */
    public static final String CUSTOM_CURVE_SELECTED = "onshoreYieldSpread:curve_pool:selected:%d";

    /**
     * 曲线组新增或更新
     */
    public static final String GROUP_ALTERATION = "onshoreYieldSpread:curve_group:alteration:%d";

    /**
     * 初始化组和曲线
     */
    public static final String GROUP_INITIALIZE = "onshoreYieldSpread:curve_group:initialize";

    /**
     * 计算债券利差 %s:日期
     */
    public static final String CALC_BOND_YIELD_SPREAD = "onshoreYieldSpread:bond_yield_spread:calc:%s";

}
