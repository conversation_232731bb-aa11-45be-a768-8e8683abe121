package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * 主体利差列表响应DTO
 *
 * <AUTHOR>
 */
public class ComYieldSpreadItemListRequestDTO {

    @ApiModelProperty(value = "产业主体唯一编码")
    private Long comUniCode;
    @ApiModelProperty(value = "曲线id")
    private Long curveId;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Long getCurveId() {
        return curveId;
    }

    public void setCurveId(Long curveId) {
        this.curveId = curveId;
    }
}
