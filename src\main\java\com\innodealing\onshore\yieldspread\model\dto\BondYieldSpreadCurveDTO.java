package com.innodealing.onshore.yieldspread.model.dto;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 单券利差曲线DTO
 *
 * <AUTHOR>
 */
public class BondYieldSpreadCurveDTO {

    /**
     * 利差日期
     */
    private Date spreadDate;
    /**
     * 信用利差
     */
    private BigDecimal bondCreditSpread;
    /**
     * 超额利差
     */
    private BigDecimal bondExcessSpread;
    /**
     * 估值收益率
     */
    private BigDecimal cbYield;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getBondCreditSpread() {
        return bondCreditSpread;
    }

    public void setBondCreditSpread(BigDecimal bondCreditSpread) {
        this.bondCreditSpread = bondCreditSpread;
    }

    public BigDecimal getBondExcessSpread() {
        return bondExcessSpread;
    }

    public void setBondExcessSpread(BigDecimal bondExcessSpread) {
        this.bondExcessSpread = bondExcessSpread;
    }

    public BigDecimal getCbYield() {
        return cbYield;
    }

    public void setCbYield(BigDecimal cbYield) {
        this.cbYield = cbYield;
    }
}
