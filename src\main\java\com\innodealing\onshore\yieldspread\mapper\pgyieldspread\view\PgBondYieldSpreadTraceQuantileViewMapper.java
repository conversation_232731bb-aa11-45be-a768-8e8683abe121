package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.view;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgBondYieldSpreadTraceQuantileViewDO;


/**
 * 债券收益率全景分位数视图Mapper
 *
 * <AUTHOR>
 */
public interface PgBondYieldSpreadTraceQuantileViewMapper extends DynamicQueryMapper<PgBondYieldSpreadTraceQuantileViewDO> {

    /**
     * 创建利差全景历史分位视图
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    void createTraceQuantileView(String startDate, String endDate);

}
