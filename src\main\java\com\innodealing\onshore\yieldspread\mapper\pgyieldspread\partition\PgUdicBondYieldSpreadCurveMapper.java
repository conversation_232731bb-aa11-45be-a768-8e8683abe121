package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.partition;

import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.UdicBondYieldSpreadCurveParameter;
import org.apache.ibatis.annotations.Param;

/**
 * 城投利差曲线mapper
 * <AUTHOR>
 */
public interface PgUdicBondYieldSpreadCurveMapper extends PgBaseMapper<UdicBondYieldSpreadCurveParameter>  {
    /**
     * 创建实体表(评级分片)
     * @param parameter 参数
     */
    void createTableRatingRouter(@Param("parameter") UdicBondYieldSpreadCurveParameter parameter);

    /**
     * 同步数据
     * @param tableName 表名称
     * @param mvTableName 视图形成
     */
    void syncCurveIncrFromMV(@Param("tableName") String tableName, @Param("mvTableName") String mvTableName);

}
