package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;

/**
 * 走势复盘利差查询 - 主体列表
 *
 * <AUTHOR>
 */
public class TrendReplayComYieldSpreadRequestDTO {

    @ApiModelProperty("叠加输入框发行人唯一编码")
    @NotNull(message = "comUniCode不能为空")
    private Long comUniCode;

    @ApiModelProperty("主体行业类型 1:城投;2:产业;3:证券;4:银行")
    private Integer comSpreadSector;

    /**
     * 利差类型 1. 信用利差，2. 超额利差
     */
    @ApiModelProperty("利差类型 1. 信用利差，2. 超额利差")
    private Integer spreadCurveType;

    @ApiModelProperty(value = "银行主体: 0 全部 ， 1 普通，2 二级资本，3 永续；证券主体：0 全部，1 普通，2：次级 ，3 永续 ; 城投主体利差：0：全部，1 公募，2 私募，3 永续；行业：0：全部，1 公募，2 私募，3 永续 ")
    private Integer comYieldSpreadType;

    public Integer getSpreadCurveType() {
        return spreadCurveType;
    }

    public void setSpreadCurveType(Integer spreadCurveType) {
        this.spreadCurveType = spreadCurveType;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getComSpreadSector() {
        return comSpreadSector;
    }

    public void setComSpreadSector(Integer comSpreadSector) {
        this.comSpreadSector = comSpreadSector;
    }

    public Integer getComYieldSpreadType() {
        return comYieldSpreadType;
    }

    public void setComYieldSpreadType(Integer comYieldSpreadType) {
        this.comYieldSpreadType = comYieldSpreadType;
    }
}