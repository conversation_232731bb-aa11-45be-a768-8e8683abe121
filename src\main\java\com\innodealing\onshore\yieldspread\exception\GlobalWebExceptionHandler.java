package com.innodealing.onshore.yieldspread.exception;

import com.innodealing.commons.http.RestResponse;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondmetadata.utils.ClassInfoMapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 异常拦截器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalWebExceptionHandler {

    @Resource
    private HttpServletResponse response;

    private static final String INTERNAL_SERVER_ERROR = "内部错误:";

    private static final String BUSINESS_ERROR = "|业务错误:";

    /**
     * 跟踪线程id
     */
    private static final String UNIQUE_SIGN = "traceId";

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String EXCEPTION_HEADER = "exception:";

    @ExceptionHandler(BusinessException.class)
    void handleBusinessException(BusinessException e) throws IOException {
        String randomNumber = MDC.get(UNIQUE_SIGN);
        String message = e.getMessage();
        updateExceptionMessage(e, randomNumber);
        logger.error(EXCEPTION_HEADER, e);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, message + BUSINESS_ERROR + randomNumber);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = TipsException.class)
    RestResponse<String> exceptionHandler(TipsException e) {
        final String traceId = MDC.get(UNIQUE_SIGN);
        final String message = e.getMessage();
        logger.warn("TipsException: traceId=[{}]  msg=[{}]", traceId, message, e);
        return new RestResponse<>(e.getErrorCode(), e.getMessage(), traceId);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    RestResponse<String> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        final String traceId = MDC.get(UNIQUE_SIGN);
        final String message = e.getBindingResult().getAllErrors().stream()
                .map(ObjectError::getDefaultMessage).collect(Collectors.joining("; "));
        logger.warn("MethodArgumentNotValidException: traceId=[{}]  msg=[{}]", traceId, message);
        return new RestResponse<>(HttpServletResponse.SC_BAD_REQUEST, message, traceId);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = BindException.class)
    RestResponse<String> bindExceptionHandler(BindException e) {
        final String traceId = MDC.get(UNIQUE_SIGN);
        final String message = e.getBindingResult().getAllErrors().stream()
                .map(ObjectError::getDefaultMessage).collect(Collectors.joining("; "));
        logger.warn("BindException: traceId=[{}]  msg=[{}]", traceId, message);
        return new RestResponse<>(HttpServletResponse.SC_BAD_REQUEST, message, traceId);
    }

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(value = ConstraintViolationException.class)
    RestResponse<String> constraintViolationExceptionHandler(ConstraintViolationException e) {
        final String traceId = MDC.get(UNIQUE_SIGN);
        final String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining("; "));
        logger.warn("ConstraintViolationException: traceId=[{}]  msg=[{}]", traceId, message);
        return new RestResponse<>(HttpServletResponse.SC_BAD_REQUEST, message, traceId);
    }

    @ExceptionHandler(Exception.class)
    void handleException(Exception e) throws IOException {
        String randomNumber = MDC.get(UNIQUE_SIGN);
        updateExceptionMessage(e, randomNumber);
        logger.error(EXCEPTION_HEADER, e);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, INTERNAL_SERVER_ERROR + randomNumber);
    }

    private void updateExceptionMessage(Exception e, String exceptionNumber) {
        Map<String, Field> fieldMap = ClassInfoMapUtil.getInstance().getClassFieldMap(e.getClass());
        updateProperty(e, exceptionNumber, fieldMap, "DETAILMESSAGE");
        updateProperty(e, exceptionNumber, fieldMap, "MESSAGE");
    }

    @SuppressWarnings("squid:S3011")
    private void updateProperty(Exception e, String exceptionNumber, Map<String, Field> fieldMap, String property) {
        Field messageField = fieldMap.get(property);
        try {
            if (Objects.nonNull(messageField)) {
                messageField.setAccessible(true);
                messageField.set(e, exceptionNumber + ":" + e.getMessage());
            }
        } catch (IllegalAccessException e1) {
            logger.error(EXCEPTION_HEADER, e1);
        }
    }

}
