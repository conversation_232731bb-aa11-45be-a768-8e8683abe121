package com.innodealing.onshore.yieldspread.dao.pgyieldspread;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.github.wz2cool.dynamic.SortDirections;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.yieldspread.config.datasource.PgYieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.enums.BondYieldIntervalChangeTypeEnum;
import com.innodealing.onshore.yieldspread.enums.SpreadCurveTypeEnum;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgLgBondYieldSpreadChangeMapper;
import com.innodealing.onshore.yieldspread.model.bo.YieldLgDataBO;
import com.innodealing.onshore.yieldspread.model.dto.request.LgSpreadBaseRequestDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgLgBondYieldSpreadChangeDO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.in;
import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;

/**
 * 地方债区域利差历变动 DAO
 *
 * <AUTHOR>
 * @create: 2024-11-06
 */
@Repository
public class PgLgBondYieldSpreadChangeDAO {
    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d:%s:%s:%s:%d:%d:%d";
    private static final String LG_BOND_YIELD_SPREAD_CHANGE_PK = "yieldSpread:lgBondYieldSpreadChangePk";
    private static final String PLACEHOLDER = "-";
    @Resource
    private PgLgBondYieldSpreadChangeMapper pgLgBondYieldSpreadChangeMapper;
    @Resource(name = PgYieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;
    @Resource(name = PgYieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisService redisService;

    /**
     * 保存地方债区域利差-区间变动列表
     *
     * @param spreadDate            利差日期
     * @param yieldSpreadChangeList 地方债区域利差-区间变动列表
     * @return int 保存行数
     */
    public int saveLgBondYieldSpreadChangeList(@NonNull Date spreadDate, List<PgLgBondYieldSpreadChangeDO> yieldSpreadChangeList) {
        if (CollectionUtils.isEmpty(yieldSpreadChangeList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        DynamicQuery<PgLgBondYieldSpreadChangeDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadChangeDO.class)
                .and(PgLgBondYieldSpreadChangeDO::getSpreadDate, isEqual(spreadDate));
        Map<String, PgLgBondYieldSpreadChangeDO> existMap = pgLgBondYieldSpreadChangeMapper.selectByDynamicQuery(query).stream()
                .collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        List<PgLgBondYieldSpreadChangeDO> insertList = new ArrayList<>();
        List<PgLgBondYieldSpreadChangeDO> updateList = new ArrayList<>();
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        for (PgLgBondYieldSpreadChangeDO bondYieldSpreadChangeDO : yieldSpreadChangeList) {
            String key = this.getKey(bondYieldSpreadChangeDO);
            if (existMap.containsKey(key)) {
                PgLgBondYieldSpreadChangeDO existBondYieldPanoramaQuantile = existMap.get(key);
                bondYieldSpreadChangeDO.setId(existBondYieldPanoramaQuantile.getId());
                bondYieldSpreadChangeDO.setCreateTime(null);
                bondYieldSpreadChangeDO.setUpdateTime(now);
                updateList.add(bondYieldSpreadChangeDO);
            } else {
                bondYieldSpreadChangeDO.setId(redisService.generatePk(LG_BOND_YIELD_SPREAD_CHANGE_PK, bondYieldSpreadChangeDO.getSpreadDate()));
                bondYieldSpreadChangeDO.setUpdateTime(now);
                bondYieldSpreadChangeDO.setCreateTime(now);
                insertList.add(bondYieldSpreadChangeDO);
            }
        }
        transactionTemplate.execute(status -> {
            effectRows.addAndGet(this.doBatchInsert(insertList));
            effectRows.addAndGet(this.doBatchUpdate(updateList));
            return true;
        });
        return effectRows.get();
    }

    /**
     * 获取key
     *
     * @param pgLgBondYieldSpreadChangeDO 地方债区域利差变动DO
     * @return {@link String} key值
     */
    private String getKey(PgLgBondYieldSpreadChangeDO pgLgBondYieldSpreadChangeDO) {
        Integer changeType = pgLgBondYieldSpreadChangeDO.getChangeType();
        Date spreadDate = pgLgBondYieldSpreadChangeDO.getSpreadDate();
        Long comUniCode = pgLgBondYieldSpreadChangeDO.getComUniCode();
        String lgBondType = Optional.ofNullable(pgLgBondYieldSpreadChangeDO.getLgBondType()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        String prepaymentStatus = Optional.ofNullable(pgLgBondYieldSpreadChangeDO.getPrepaymentStatus()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        String fundUseType = Optional.ofNullable(pgLgBondYieldSpreadChangeDO.getFundUseType()).map(Object::toString).orElseGet(() -> PLACEHOLDER);
        Integer usingLgBondType = pgLgBondYieldSpreadChangeDO.getUsingLgBondType();
        Integer usingPrepaymentStatus = pgLgBondYieldSpreadChangeDO.getUsingPrepaymentStatus();
        Integer usingFundUseType = pgLgBondYieldSpreadChangeDO.getUsingFundUseType();
        return String.format(NAMESPACE_KEY_PLACEHOLDER, changeType, spreadDate.getTime(), comUniCode, lgBondType, prepaymentStatus, fundUseType,
                usingLgBondType, usingPrepaymentStatus, usingFundUseType);
    }

    /**
     * 批量更新
     *
     * @param updateList 利差追踪-区间变动列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<PgLgBondYieldSpreadChangeDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<PgLgBondYieldSpreadChangeMapper> updateBatchAction =
                MapperBatchAction.create(PgLgBondYieldSpreadChangeMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgLgBondYieldSpreadChangeDO change : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<PgLgBondYieldSpreadChangeDO> updateQuery = DynamicQuery.createQuery(PgLgBondYieldSpreadChangeDO.class)
                        .and(PgLgBondYieldSpreadChangeDO::getId, isEqual(change.getId()));
                mapper.updateSelectiveByDynamicQuery(change, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 利差追踪-区间变动列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<PgLgBondYieldSpreadChangeDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<PgLgBondYieldSpreadChangeMapper> insertBatchAction =
                MapperBatchAction.create(PgLgBondYieldSpreadChangeMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (PgLgBondYieldSpreadChangeDO change : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(change));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 获取地方债区域利差区间变动
     *
     * @param areaConfigList 区域列表
     * @param requestDTO     请求参数
     * @param changeTypeEnum 变动类型
     * @return 数据
     */
    public List<YieldLgDataBO> listYieldSpreadLgChange(@NotEmpty List<Long> areaConfigList,
                                                       @NotNull LgSpreadBaseRequestDTO requestDTO,
                                                       @NotNull BondYieldIntervalChangeTypeEnum changeTypeEnum) {
        FilterGroupDescriptor<PgLgBondYieldSpreadChangeDO> descriptor = requestDTO.listLgSpreadBaseFilters(PgLgBondYieldSpreadChangeDO.class);
        DynamicQuery<PgLgBondYieldSpreadChangeDO> query = DynamicQuery.createQuery(PgLgBondYieldSpreadChangeDO.class).ignore(PgLgBondYieldSpreadChangeDO::getId,
                        PgLgBondYieldSpreadChangeDO::getDeleted, PgLgBondYieldSpreadChangeDO::getCreateTime, PgLgBondYieldSpreadChangeDO::getUpdateTime)
                .and(PgLgBondYieldSpreadChangeDO::getComUniCode, in(areaConfigList))
                .and(PgLgBondYieldSpreadChangeDO::getChangeType, isEqual(changeTypeEnum.getValue()))
                .and(descriptor);
        List<PgLgBondYieldSpreadChangeDO> pgLgBondYieldSpreadChangeDOs = pgLgBondYieldSpreadChangeMapper.selectByDynamicQuery(query);
        SpreadCurveTypeEnum spreadCurveTypeEnum = ITextValueEnum.getEnum(SpreadCurveTypeEnum.class, requestDTO.getSpreadDataType());
        return pgLgBondYieldSpreadChangeDOs.stream()
                .map(baseDO -> baseDO.convertToBO(spreadCurveTypeEnum).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取最大利差日期
     *
     * @return {@link Optional}<{@link Date}> 最大利差日期
     */
    public Optional<Date> getMaxSpreadDate() {
        DynamicQuery<PgLgBondYieldSpreadChangeDO> dynamicQuery = DynamicQuery.createQuery(PgLgBondYieldSpreadChangeDO.class)
                .orderBy(PgLgBondYieldSpreadChangeDO::getSpreadDate, SortDirections::desc);
        return pgLgBondYieldSpreadChangeMapper.selectFirstByDynamicQuery(dynamicQuery).map(PgLgBondYieldSpreadChangeDO::getSpreadDate);
    }
}
