package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;
import java.util.Objects;

/**
 * pg债券收益率全景变动DO
 *
 * <AUTHOR>
 */
@Table(name = "bond_yield_panorama_change")
public class PgBondYieldPanoramaChangeDO extends PgBaseBondYieldPanoramaDO {

    /**
     * 变动类型 1 一周变动，2 一月变动 3 三月变动 4 六月变动 5 一年变动
     */
    @Column
    private Integer changeType;

    /**
     * 开始日期
     */
    @Column
    private Date startDate;

    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    public Date getStartDate() {
        return Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }

    public void setStartDate(Date startDate) {
        this.startDate = Objects.isNull(startDate) ? null : new Date(startDate.getTime());
    }
}