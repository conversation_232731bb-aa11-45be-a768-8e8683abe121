package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.CurveDataExportRequestDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.*;
import com.innodealing.onshore.yieldspread.model.dto.response.excel.*;

import java.sql.Date;
import java.util.List;
import java.util.Map;

/**
 * 导出服务
 *
 * <AUTHOR>
 */
public interface ExportService {

    /**
     * 行业-导出作图数据
     *
     * @param request 行业-导出作图数据请求参数
     * @return {@link List}<{@link InduCurveExportExcelDTO}> 导出作图响应数据
     */
    List<InduCurveExportExcelDTO> listInduCurveListExcels(InduCurveExportRequestDTO request);

    /**
     * 行业-导出主体成分数据
     *
     * @param request 导出主体成分数据请求参数
     * @return {@link List}<{@link InduComSpreadExportExcelDTO}> 导出主体成分响应数据
     */
    List<InduComSpreadExportExcelDTO> listInduComListExcels(InduListExportRequestDTO request);

    /**
     * 行业-导出单券成分数据
     *
     * @param request 导出单券成分数据请求参数
     * @return {@link List}<{@link InduBondSpreadExportExcelDTO}> 导出单券成分响应数据
     */
    List<InduBondSpreadExportExcelDTO> listInduBondListExcels(InduListExportRequestDTO request);

    /**
     * 城投利差作图数据导出
     *
     * @param request 城投利差作图数据导出请求参数
     * @return {@link List}<{@link UdicSpreadCurveExcelDTO}> 城投利差导出作图响应数据
     */
    List<UdicSpreadCurveExcelDTO> listUdicCurvesExcels(UdicCurveExportRequestDTO request);

    /**
     * 城投利差主体成分数据导出
     *
     * @param request 城投利差主体成分数据导出请求参数
     * @return {@link List}<{@link UdicComSpreadExportExcelDTO}> 城投利差导出主体成分响应数据
     */
    List<UdicComSpreadExportExcelDTO> listUdicComSpreadExcels(UdicListExportRequestDTO request);

    /**
     * 城投利差债券成分数据导出
     *
     * @param request 城投利差债券成分数据导出请求参数
     * @return {@link List}<{@link UdicBondSpreadExportExcelDTO}> 城投利差导出债券成分响应数据
     */
    List<UdicBondSpreadExportExcelDTO> listUdicBondSpreadExcels(UdicListExportRequestDTO request);

    /**
     * 行业利差全景数据导出
     *
     * @param request 行业利差全景数据导出请求参数
     * @return {@link List}<{@link InduPanoramaExportExcelDTO}> 行业利差全景数据导出响应数据
     */
    List<InduPanoramaExportExcelDTO> listInduPanoramaExcels(InduPanoramaExportRequestDTO request);

    /**
     * 行业利差单日全景数据导出
     *
     * @param request 行业利差单日全景数据导出请求参数
     * @return {@link List}<{@link InduPanoramaExportExcelDTO}> 行业利差全景数据导出响应数据
     */
    List<InduPanoramaExportExcelDTO> listInduDayPanoramaExcels(InduDayPanoramaExportRequestDTO request);

    /**
     * 城投利差全景数据导出
     *
     * @param request 城投利差全景数据导出请求参数
     * @return {@link List}<{@link UdicSpreadPanoramaExportExcelDTO}> 城投利差全景数据导出响应数据
     */
    List<UdicSpreadPanoramaExportExcelDTO> listUdicPanoramasExcels(UdicPanoramaExportRequestDTO request);

    /**
     * 城投利差单日全景数据导出
     *
     * @param request 城投利差单日全景数据导出请求参数
     * @return {@link List}<{@link UdicSpreadPanoramaExportExcelDTO}> 城投利差全景数据导出响应数据
     */
    List<UdicSpreadPanoramaExportExcelDTO> listUdicDayPanoramasExcels(UdicDayPanoramaExportRequestDTO request);


    /**
     * 导出利差曲线数据
     *
     * @param userId  用户id
     * @param request 导出请求参数
     * @return 利差曲线数据
     */
    List<CurveExportExcelDTO> exportCurveData(Long userId, CurveDataExportRequestDTO request);

    /**
     * 曲线动态导出
     *
     * @param curveExportExcelDTOS  原始固定导出集合
     * @return 曲线动态导出集合
     */
    List<DynCurveExportExcelDTO> dynExportCurveData(List<CurveExportExcelDTO> curveExportExcelDTOS);

    /**
     * 导出单券利差
     *
     * @param userid     用户id
     * @param curveIds   曲线id
     * @param spreadDate 利差日期
     * @return 利差数据
     */
    Map<String, List<BaseCurveYieldSpreadExcelDTO>> exportSingleBondYieldSpread(Long userid, List<Long> curveIds, Date spreadDate);

    /**
     * 导出主体利差
     *
     * @param userid     用户id
     * @param curveIds   曲线id
     * @param spreadDate 利差日期
     * @return 利差数据
     */
    Map<String, List<BaseCurveYieldSpreadExcelDTO>> exportComYieldSpread(Long userid, List<Long> curveIds, Date spreadDate);

}
