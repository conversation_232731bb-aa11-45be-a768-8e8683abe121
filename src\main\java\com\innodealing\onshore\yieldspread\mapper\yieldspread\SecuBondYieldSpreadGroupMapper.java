package com.innodealing.onshore.yieldspread.mapper.yieldspread;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.SecuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.SecuBondYieldSpreadGroupDO;

/**
 * 证券债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface SecuBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<SecuBondYieldSpreadDO,
        SecuBondYieldSpreadGroupDO> {

}
