package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBankBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgBankBondYieldSpreadGroupDO;


/**
 * pg银行债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface PgBankBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<PgBankBondYieldSpreadDO,
        PgBankBondYieldSpreadGroupDO> {

}
