package com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.partition;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Date;
import java.util.Objects;

/**
 * 城投债券区县利差曲线数据实体
 *
 * <AUTHOR>
 */
@Table(name = "udic_bond_yield_spread_curve_district")
public class PgUdicBondYieldSpreadCurveDistrictDO extends BaseYieldSpreadDataDO {

    /**
     * 利差日期
     */
    @Column
    private Date spreadDate;

    /**
     * 区县编码
     */
    @Column
    private Long districtUniCode;

    /**
     * 信用利差不为空的债券样本数量
     */
    @Column
    private Integer bondCreditSpreadCount;

    /**
     * 超额利差不为空的债券样本数量
     */
    @Column
    private Integer bondExcessSpreadCount;

    /**
     * 中债收益率不为空的债券样本数量
     */
    @Column
    private Integer cbYieldCount;

    /**
     * 担保状态: 0: 无; 1: 有
     */
    @Column
    private Integer guaranteedStatus;

    /**
     * 是否根据担保状态进行分组: 0：是，1：否
     */
    @Column
    private Integer usingGuaranteedStatus;

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public Long getDistrictUniCode() {
        return districtUniCode;
    }

    public void setDistrictUniCode(Long districtUniCode) {
        this.districtUniCode = districtUniCode;
    }

    public Integer getBondCreditSpreadCount() {
        return bondCreditSpreadCount;
    }

    public void setBondCreditSpreadCount(Integer bondCreditSpreadCount) {
        this.bondCreditSpreadCount = bondCreditSpreadCount;
    }

    public Integer getBondExcessSpreadCount() {
        return bondExcessSpreadCount;
    }

    public void setBondExcessSpreadCount(Integer bondExcessSpreadCount) {
        this.bondExcessSpreadCount = bondExcessSpreadCount;
    }

    public Integer getCbYieldCount() {
        return cbYieldCount;
    }

    public void setCbYieldCount(Integer cbYieldCount) {
        this.cbYieldCount = cbYieldCount;
    }

    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }

    public Integer getUsingGuaranteedStatus() {
        return usingGuaranteedStatus;
    }

    public void setUsingGuaranteedStatus(Integer usingGuaranteedStatus) {
        this.usingGuaranteedStatus = usingGuaranteedStatus;
    }

}