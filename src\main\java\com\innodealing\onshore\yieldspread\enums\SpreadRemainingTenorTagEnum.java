package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 利差剩余期限标签
 *
 * <AUTHOR>
 **/
public enum SpreadRemainingTenorTagEnum implements ITextValueEnum {
    /**
     * 剩余期限>=6个月且<1.5年 天数范围183-548
     */
    SPREAD_REMAINING_TENOR_TAG_ONE(1, "剩余期限>=6个月且<1.5年", "1Y"),
    /**
     * 剩余期限>=1.5年且<2.5年 天数范围548-913
     */
    SPREAD_REMAINING_TENOR_TAG_TWO(2, "剩余期限>=1.5年且<2.5年", "2Y"),
    /**
     * 剩余期限>=2.5年且<3.5年 天数范围913-1278
     */
    SPREAD_REMAINING_TENOR_TAG_THREE(3, "剩余期限>=2.5年且<3.5年", "3Y"),
    /**
     * 剩余期限>=3.5年且<4.5年 天数范围1278-1643
     */
    SPREAD_REMAINING_TENOR_TAG_FORE(4, "剩余期限>=3.5年且<4.5年", "4Y"),
    /**
     * 剩余期限>=4.5年且<6年 天数范围1643-2190
     */
    SPREAD_REMAINING_TENOR_TAG_FIVE(5, "剩余期限>=4.5年且<6年", "5Y");

    private final int value;
    private final String text;
    private final String desc;

    SpreadRemainingTenorTagEnum(int value, String text, String desc) {
        this.value = value;
        this.text = text;
        this.desc = desc;
    }


    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }
}
