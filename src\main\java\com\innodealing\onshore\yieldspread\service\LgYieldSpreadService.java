package com.innodealing.onshore.yieldspread.service;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondprice.BondYieldCurveDTO;
import com.innodealing.onshore.yieldspread.enums.SpreadQuantileTypeEnum;
import com.innodealing.onshore.yieldspread.model.dto.request.*;
import com.innodealing.onshore.yieldspread.model.dto.request.excel.BondYieldLgExportReqDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.LgComAreaResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldLgResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.YieldTraceLineCommonResponseDTO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Date;
import java.util.List;
import java.util.Map;

/**
 * 计算 地方债利差
 *
 * <AUTHOR>
 * @create: 2024-10-24
 */
public interface LgYieldSpreadService {
    /**
     * 地方债区域利差历史同步
     *
     * @param startDate 开始日期
     * @return 影响数
     */
    int syncHisLgBondYieldSpread(Date startDate);

    /**
     * 同步地方债区域利差(增量),不传日期默认同步上一天数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 影响数
     */
    int syncLgAreaBondYieldSpread(Date startDate, Date endDate);

    /**
     * 同步地方债区域利差-base(历史)
     *
     * @param startDate 开始日期
     * @param endDate   结束时间
     * @return 影响数
     */
    int syncLgBondYieldSpreadBase(Date startDate, Date endDate);

    /**
     * 同步地方债区域利差-历史分位(历史)
     *
     * @param startDate 开始日期
     * @param endDate   结束时间
     * @return 影响数
     */
    int syncLgBondYieldSpreadQuantile(Date startDate, Date endDate);

    /**
     * 同步地方债区域利差-历史分位(历史)
     *
     * @param startDate        开始日期
     * @param endDate          结束时间
     * @param quantileTypeEnum 历史分位统计类型
     * @return 影响数
     */
    int syncLgBondYieldSpreadQuantile(Date startDate, Date endDate, SpreadQuantileTypeEnum quantileTypeEnum);

    /**
     * 同步地方债区域利差-区间变动(历史)
     *
     * @param startDate 开始日期
     * @param endDate   结束时间
     * @return 影响数
     */
    int syncLgBondYieldSpreadChange(Date startDate, Date endDate);

    /**
     * 地方债利差计算
     *
     * @param onshoreBondInfoDTOS 债券信息
     * @param bondYieldCurveMap   曲线map
     * @param spreadDate          利差日期
     * @return 影响数
     */
    int calcLgBondYieldSpreadsBySpreadDate(List<OnshoreBondInfoDTO> onshoreBondInfoDTOS, Map<Integer, BondYieldCurveDTO> bondYieldCurveMap,
                                           Date spreadDate);

    /**
     * 缓存利地方债区域利差折线图
     */
    void cacheLgLineChart();

    /**
     * 获取地方债区域利差基础信息
     *
     * @param userId     用户id
     * @param requestDTO 请求参数
     * @return 数据值
     */
    YieldLgResponseDTO getYieldSpreadLgBase(Long userId, LgSpreadBaseRequestDTO requestDTO);

    /**
     * 获取地方债区域利差历史分位信息
     *
     * @param userId     用户id
     * @param requestDTO 请求参数
     * @return 数据值
     */
    YieldLgResponseDTO getYieldSpreadLgQuantile(Long userId, LgSpreadQuantileRequestDTO requestDTO);

    /**
     * 获取地方债区域利差区间变动信息
     *
     * @param userId     用户id
     * @param requestDTO 请求参数
     * @return 数据值
     */
    YieldLgResponseDTO listYieldSpreadLgChange(Long userId, LgSpreadChangeRequestDTO requestDTO);

    /**
     * 地方债区域利差到处
     *
     * @param httpServletResponse response对象
     * @param userId              用户id
     * @param requestDTO          请求对象
     * @throws IOException Exception
     */
    void exportYieldSpreadLg(HttpServletResponse httpServletResponse, Long userId, BondYieldLgExportReqDTO requestDTO) throws IOException;

    /**
     * 获取地方债利差有数据的最大日期
     *
     * @return 利差日期
     */
    Date maxSpreadDate();

    /**
     * 根据参数获取地区对比图
     *
     * @param userId     用户id
     * @param requestDTO 请求值
     * @return 数据
     */
    YieldTraceLineCommonResponseDTO lgLineChartWithPeriod(Long userId, LgSpreadLinePeriodRequestDTO requestDTO);

    /**
     * 根据参数获取同区域分层数据
     *
     * @param userId     用户id
     * @param requestDTO 请求参数
     * @return 数据
     */
    YieldTraceLineCommonResponseDTO lgLineChartWithArea(Long userId, LgSpreadLineAreaRequestDTO requestDTO);

    /**
     * 获取所有主体区域列表
     *
     * @return 主体区域列表
     */
    List<LgComAreaResponseDTO> listAllComArea();
}
