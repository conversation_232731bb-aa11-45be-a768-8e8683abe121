package com.innodealing.onshore.yieldspread.model.entity.yieldspread;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * 用户勾选的曲线
 *
 * <AUTHOR>
 */
@Table(name = "user_selected_curve")
public class UserSelectedCurveDO {

    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;

    /**
     * 用户id
     */
    @Column
    private Long userId;

    /**
     * 用户勾选的曲线json字符串
     */
    @Column
    private String selectedCurve;

    /**
     * 是否删除
     */
    @Column
    private Integer deleted;

    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;

    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSelectedCurve() {
        return selectedCurve;
    }

    public void setSelectedCurve(String selectedCurve) {
        this.selectedCurve = selectedCurve;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Timestamp getCreateTime() {
        return Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = Objects.isNull(createTime) ? null : new Timestamp(createTime.getTime());
    }

    public Timestamp getUpdateTime() {
        return Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = Objects.isNull(updateTime) ? null : new Timestamp(updateTime.getTime());
    }

}
