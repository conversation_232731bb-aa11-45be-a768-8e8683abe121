# syncHistBondYieldPanorama 方法详细文档

## 1. 方法概述

### 1.1 方法签名
```java
@Override
public int syncHistBondYieldPanorama(Date startDate)
```

### 1.2 功能描述
`syncHistBondYieldPanorama` 是债券收益率全景数据的历史同步方法，负责从指定开始日期到当前日期的所有债券收益率全景数据的同步计算和存储。

### 1.3 所属类
- **类名**: `BondYieldPanoramaServiceImpl`
- **包路径**: `com.innodealing.onshore.yieldspread.service.impl`
- **接口**: `BondYieldPanoramaService`

## 2. 方法实现逻辑

### 2.1 核心实现
```java
@Override
public int syncHistBondYieldPanorama(Date startDate) {
    Date endDate = Date.valueOf(LocalDate.now());
    return this.syncBondYieldPanorama(startDate, endDate);
}
```

### 2.2 执行流程
1. **设置结束日期**: 将结束日期设置为当前日期
2. **委托执行**: 调用 `syncBondYieldPanorama(startDate, endDate)` 方法执行具体的同步逻辑

## 3. 核心委托方法 - syncBondYieldPanorama

### 3.1 方法签名
```java
public int syncBondYieldPanorama(Date startDate, Date endDate)
```

### 3.2 详细实现逻辑

#### 3.2.1 参数处理
```java
AtomicInteger effectRows = new AtomicInteger();
startDate = Objects.isNull(startDate) ? Date.valueOf(LocalDate.now().minusDays(1)) : startDate;
endDate = Objects.isNull(endDate) ? Date.valueOf(LocalDate.now()) : endDate;
LocalDate localStartDate = startDate.toLocalDate();
LocalDate localEndDate = endDate.toLocalDate();
```

#### 3.2.2 日期循环处理
对从开始日期到结束日期的每一天执行以下操作：

```java
for (LocalDate beginDate = localStartDate; !beginDate.isAfter(localEndDate); beginDate = beginDate.plusDays(1)) {
    Date issueDate = Date.valueOf(beginDate);
    effectRows.addAndGet(this.syncBondYieldCurve(issueDate));
    effectRows.addAndGet(this.syncBondYieldUniCurve(issueDate));
    effectRows.addAndGet(this.syncInduBondYieldSpread(issueDate));
    effectRows.addAndGet(this.syncSecuBondYieldSpread(issueDate));
    effectRows.addAndGet(this.calHistQuantile(issueDate));
    effectRows.addAndGet(this.calIntervalChange(issueDate));
    logger.info("syncBondYieldPanorama同步全景利差日期:{}", issueDate);
}
```

## 4. 子方法详细说明

### 4.1 syncBondYieldCurve(Date issueDate)
**功能**: 同步债券收益率曲线数据

**执行步骤**:
1. 构建 `BondYieldCurveRequestDTO` 请求对象
2. 调用 `bondPriceService.listBondYieldCurves()` 获取收益率曲线数据
3. 按曲线代码去重，保留最新创建时间的数据
4. 转换为 `PgBondYieldPanoramaAbsDO` 对象
5. 调用 `pgBondYieldPanoramaAbsDAO.saveBondYieldAbsList()` 保存数据

### 4.2 syncBondYieldUniCurve(Date issueDate)
**功能**: 同步债券收益率统一曲线数据

**执行步骤**:
1. 调用 `bondPriceApolloService.getCurveMaturityStructureMap()` 获取曲线期限结构数据
2. 将数据转换为 `PgBondYieldPanoramaAbsDO` 对象
3. 调用 `pgBondYieldPanoramaAbsDAO.saveBondYieldAbsList()` 保存数据

### 4.3 syncInduBondYieldSpread(Date issueDate)
**功能**: 同步产业债收益率利差数据

**执行步骤**:
1. 调用 `pgInduBondYieldSpreadDAO.listYieldSpreadCbYieldMedians()` 获取产业债利差数据
2. 根据评级映射设置曲线代码
3. 转换为 `PgBondYieldPanoramaAbsDO` 对象并保存

### 4.4 syncSecuBondYieldSpread(Date issueDate)
**功能**: 同步证券公司债收益率利差数据

**执行步骤**:
1. 获取证券债利差数据（包括次级债和永续债）
2. 根据证券优先级排序设置曲线代码
3. 转换并保存数据

### 4.5 calHistQuantile(Date issueDate)
**功能**: 计算历史分位数据

**执行步骤**:
1. 遍历所有分位类型枚举 (`SpreadQuantileTypeEnum`)
   - `THREE_YEARS_QUANTILE`: 3年历史分位
   - `FIVE_YEARS_QUANTILE`: 5年历史分位
   - `ONE_YEARS_QUANTILE`: 1年历史分位
2. 为每种分位类型计算起始日期
3. 创建分位数据视图
4. 查询并保存分位数据

### 4.6 calIntervalChange(Date issueDate)
**功能**: 计算区间变动数据

**执行步骤**:
1. 获取当前日期的基础数据
2. 遍历所有区间变动类型 (`BondYieldIntervalChangeTypeEnum`)
   - `ONE_DAY_CHANGE`: 一日变动
   - `ONE_WEEK_CHANGE`: 一周变动
   - `ONE_MONTH_CHANGE`: 一月变动
   - `THREE_MONTH_CHANGE`: 三月变动
   - `SIX_MONTH_CHANGE`: 六月变动
   - `ONE_YEAR_CHANGE`: 一年变动
3. 计算各时间段的变动数据
4. 保存区间变动数据

## 5. 数据存储

### 5.1 主要数据表
- **pg_bond_yield_panorama_abs**: 债券收益率全景绝对值数据
- **pg_bond_yield_panorama_quantile**: 债券收益率全景历史分位数据
- **pg_bond_yield_panorama_change**: 债券收益率全景区间变动数据

### 5.2 数据分片策略
使用基于日期的分片策略，通过 `ShardingUtils` 工具类计算分片键。

## 6. 缓存机制

### 6.1 Redis缓存
- **缓存键**: `yield-spread:bond_yield_panorama:{date}`
- **缓存内容**: `BondYieldPanoramaTraceSpreadDateDTO`
- **过期时间**: 2天

### 6.2 缓存更新
在同步完成后，更新各类型数据的最大日期缓存：
```java
pgBondYieldPanoramaAbsDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
pgBondYieldPanoramaQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
pgBondYieldPanoramaChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
```

## 7. 外部服务依赖

### 7.1 BondPriceService
- **方法**: `listBondYieldCurves(BondYieldCurveRequestDTO)`
- **功能**: 获取债券收益率曲线数据

### 7.2 BondPriceApolloService
- **方法**: `getCurveMaturityStructureMap(List<Long>, Date)`
- **功能**: 获取曲线期限结构数据

## 8. 配置参数

### 8.1 曲线代码配置
- **curveCodes**: 标准曲线代码列表
- **uniCurveCodes**: 统一曲线代码列表

### 8.2 债券类型配置
- 产业债评级: `getBondImpliedRating()`
- 证券债优先级: `SecuritySeniorityRankingEnum.PERPETUA`, `SecuritySeniorityRankingEnum.SUBORDINATED`

## 9. 异常处理

### 9.1 事务管理
使用 `@Transactional` 注解确保数据一致性，回滚策略为 `rollbackFor = Exception.class`

### 9.2 数据校验
- 空集合检查: `CollectionUtils.isEmpty()`
- 空对象检查: `Objects.isNull()`

## 10. 性能优化

### 10.1 批量处理
- 使用批量插入减少数据库交互次数
- 分片存储提高查询性能

### 10.2 并发控制
- 使用 `AtomicInteger` 统计影响行数
- 日志记录同步进度

## 11. 枚举类型详解

### 11.1 SpreadQuantileTypeEnum (历史分位类型)
```java
THREE_YEARS_QUANTILE(1, "3年历史分位", 3)
FIVE_YEARS_QUANTILE(2, "5年历史分位", 5)
ONE_YEARS_QUANTILE(3, "1年历史分位", 1)
```

### 11.2 BondYieldIntervalChangeTypeEnum (区间变动类型)
```java
ONE_DAY_CHANGE(6, "一日", 3天最小间隔)
ONE_WEEK_CHANGE(1, "一周", 3天最小间隔)
ONE_MONTH_CHANGE(2, "一月", 7天最小间隔)
THREE_MONTH_CHANGE(3, "三月", 7天最小间隔)
SIX_MONTH_CHANGE(4, "六月", 7天最小间隔)
ONE_YEAR_CHANGE(5, "一年", 7天最小间隔)
```

### 11.3 BondYieldTableTypeEnum (表格类型)
```java
ABS(1, "到期收益率", "")
HIST_QUANTILE(2, "历史分位", "three")
INTERVAL_CHANGE(3, "区间变动", "change")
```

### 11.4 YieldPanoramaBondTypeEnum (债券类型)
```java
CHINA_BOND(1, "国债")
CHINA_DEVELOPMENT_BOND(2, "国开债")
LOCAL_GOVERNMENT_BOND(3, "地方政府债")
MEDIUM_AND_SHORT_TERMS_NOTE(4, "中短期票据")
INDUSTRIAL_BOND(5, "产业债")
URBAN_BOND(6, "城投")
BANK_ORDINARY_BOND(7, "银行普通债")
BANK_TIER2_BOND(8, "银行二级资本债")
BANK_PERPETUAL_BOND(9, "银行永续债")
SECURITIES_BOND(10, "证券公司债")
SECURITIES_SUBORDINATED_BOND(11, "证券次级债")
SECURITIES_PERPETUAL_BOND(12, "证券永续债")
NCD(13, "同业存单")
INSURANCE_BOND(14, "保险")
```

## 12. 工作日处理逻辑

### 12.1 HolidayService 依赖
- **方法**: `latestWorkDay(Date date, int offset)`
- **功能**: 获取指定日期最近的工作日
- **用途**: 确保计算的起始日期为有效的交易日

### 12.2 日期计算规则
```java
private Date getChangeStartDate(Date issueDate, BondYieldIntervalChangeTypeEnum changeType) {
    LocalDate startDate = changeType.getIntervalStartDate(issueDate.toLocalDate());
    // 获取最近一天的工作日，包含自己
    Date workDate = holidayService.latestWorkDay(Date.valueOf(startDate), 0);
    Date minStartDate = changeType.getIntervalMinStartDate(startDate);
    return workDate.before(minStartDate) ? minStartDate : workDate;
}
```

## 13. 数据流转图

```
syncHistBondYieldPanorama(startDate)
    ↓
syncBondYieldPanorama(startDate, endDate)
    ↓
日期循环 (startDate → endDate)
    ↓
并行执行六个同步任务:
    ├── syncBondYieldCurve(issueDate)          → pg_bond_yield_panorama_abs
    ├── syncBondYieldUniCurve(issueDate)       → pg_bond_yield_panorama_abs
    ├── syncInduBondYieldSpread(issueDate)     → pg_bond_yield_panorama_abs
    ├── syncSecuBondYieldSpread(issueDate)     → pg_bond_yield_panorama_abs
    ├── calHistQuantile(issueDate)             → pg_bond_yield_panorama_quantile
    └── calIntervalChange(issueDate)           → pg_bond_yield_panorama_change
    ↓
更新Redis缓存
    ↓
返回总影响行数
```

## 14. 错误处理和监控

### 14.1 日志记录
```java
logger.info("syncBondYieldPanorama同步全景利差日期:{}", issueDate);
logger.info("syncBondYieldPanoramaQuantile 同步全景利差分位日期:{}", beginDate);
```

### 14.2 数据完整性检查
- 空集合检查避免无效操作
- 分片键计算确保数据正确存储
- 事务回滚保证数据一致性

## 15. 相关方法对比

### 15.1 syncHistIntervalChange(Date startDate)
- **功能**: 仅同步历史区间变动数据
- **实现**: 日期循环调用 `calIntervalChange()`

### 15.2 syncHistQuantile(Date startDate)
- **功能**: 仅同步历史分位数据
- **实现**: 日期循环调用 `calHistQuantile()`

### 15.3 syncHistBondYieldPanorama(Date startDate)
- **功能**: 全量同步所有类型的全景数据
- **实现**: 调用 `syncBondYieldPanorama()` 执行完整同步流程

## 16. 使用建议

### 16.1 调用时机
- **首次部署**: 使用较早的开始日期进行历史数据同步
- **日常维护**: 使用昨日日期进行增量同步
- **数据修复**: 指定特定日期范围进行重新同步

### 16.2 性能考虑
- 大批量历史数据同步建议分批执行
- 监控数据库连接池和内存使用情况
- 考虑在业务低峰期执行同步任务

### 16.3 数据验证
- 同步完成后检查各表的数据完整性
- 验证缓存更新是否正确
- 确认日志记录无异常信息

## 17. 返回值
返回同步影响的总行数 (`int`)，包括所有子操作的累计影响行数。
