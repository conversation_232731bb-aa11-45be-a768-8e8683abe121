package com.innodealing.onshore.yieldspread.model.dto.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 曲线组
 *
 * <AUTHOR>
 */
public class CurveGroupResDTO {

    @ApiModelProperty("曲线id")
    private Long id;

    @ApiModelProperty("曲线组名称")
    private String curveGroupName;

    @ApiModelProperty("曲线组类别 1：我的曲线组，2：基准曲线组")
    private Integer curveGroupCategory;

    @ApiModelProperty("曲线组类型 1：默认组，2：普通组，3：基准曲线组")
    private Integer curveGroupType;

    @ApiModelProperty("曲线组顺序")
    private Integer curveGroupOrder;

    @ApiModelProperty("曲线")
    private List<CurveDefinitionBasicInfoResDTO> curves;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCurveGroupName() {
        return curveGroupName;
    }

    public void setCurveGroupName(String curveGroupName) {
        this.curveGroupName = curveGroupName;
    }

    public Integer getCurveGroupCategory() {
        return curveGroupCategory;
    }

    public void setCurveGroupCategory(Integer curveGroupCategory) {
        this.curveGroupCategory = curveGroupCategory;
    }

    public Integer getCurveGroupType() {
        return curveGroupType;
    }

    public void setCurveGroupType(Integer curveGroupType) {
        this.curveGroupType = curveGroupType;
    }

    public List<CurveDefinitionBasicInfoResDTO> getCurves() {
        return Objects.isNull(curves) ? new ArrayList<>() : new ArrayList<>(curves);
    }

    public void setCurves(List<CurveDefinitionBasicInfoResDTO> curves) {
        this.curves = Objects.isNull(curves) ? new ArrayList<>() : new ArrayList<>(curves);
    }

    public Integer getCurveGroupOrder() {
        return curveGroupOrder;
    }

    public void setCurveGroupOrder(Integer curveGroupOrder) {
        this.curveGroupOrder = curveGroupOrder;
    }

    @Override
    public String toString() {
        return "CurveGroupResDTO{" +
                "id=" + id +
                ", curveGroupName='" + curveGroupName + '\'' +
                ", curveGroupCategory=" + curveGroupCategory +
                ", curveGroupType=" + curveGroupType +
                ", curveGroupOrder=" + curveGroupOrder +
                ", curves=" + curves +
                '}';
    }

}
