package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgSecuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadCbYieldGroupDO;

/**
 * pg secu债券利差计算中债收益率中位数Mapper
 *
 * <AUTHOR>
 */
public interface PgSecuBondYieldSpreadCbYieldGroupMapper extends SelectByGroupedQueryMapper<PgSecuBondYieldSpreadDO,
        PgSecuBondYieldSpreadCbYieldGroupDO> {
}
