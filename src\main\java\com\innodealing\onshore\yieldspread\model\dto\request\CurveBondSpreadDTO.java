package com.innodealing.onshore.yieldspread.model.dto.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


/**
 * 曲线请求DTO-单券利差类型（城投|行业公用）
 *
 * <AUTHOR>
 * @date 2022-09-26
 */
public class CurveBondSpreadDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("债券唯一编码")
    private Long bondUniCode;
    @ApiModelProperty("曲线名称(导出时前端出入)")
    private String curveName;

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getCurveName() {
        return curveName;
    }

    public void setCurveName(String curveName) {
        this.curveName = curveName;
    }

    @Override
    public String toString() {
        return "CurveBondSpreadDTO{" +
                "bondUniCode=" + bondUniCode +
                '}';
    }
}
