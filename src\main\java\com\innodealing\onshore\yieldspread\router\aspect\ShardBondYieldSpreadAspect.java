package com.innodealing.onshore.yieldspread.router.aspect;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.innodealing.onshore.yieldspread.builder.ShardParamBuilder;
import com.innodealing.onshore.yieldspread.dao.pgshard.partition.InduBondShardYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.dao.pgshard.partition.UdicBondShardYieldSpreadCurveRepository;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadCurveShardEnum;
import com.innodealing.onshore.yieldspread.helper.ShardConverter;
import com.innodealing.onshore.yieldspread.model.dto.*;
import com.innodealing.onshore.yieldspread.model.dto.request.BasePanoramaRequestDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.InduShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.model.entity.pgsharding.table.UdicShardBondYieldSpreadCurveDO;
import com.innodealing.onshore.yieldspread.router.annotation.ShardYieldSpreadCurve;
import com.innodealing.onshore.yieldspread.router.factory.ImplicitRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.factory.YyRatingRouterFactory;
import com.innodealing.onshore.yieldspread.router.shard.EmptyRouter;
import com.innodealing.onshore.yieldspread.router.shard.AbstractRatingRouter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.WildcardType;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 分表逻辑
 *
 * <AUTHOR>
 */
@Aspect
@Order(3)
@Component
public class ShardBondYieldSpreadAspect {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private YyRatingRouterFactory yyRatingRouterFactory;
    @Resource
    private ImplicitRatingRouterFactory implicitRatingRouterFactory;
    @Resource
    private InduBondShardYieldSpreadCurveRepository induBondShardYieldSpreadCurveRepository;
    @Resource
    private UdicBondShardYieldSpreadCurveRepository udicBondShardYieldSpreadCurveRepository;

    private final Map<Type, Object> proxyMap = Maps.newHashMap();

    private final Map<Class<? extends AbstractShardParamDTO>, Supplier<ShardParamBuilder>> builderFactory = Maps.newHashMap();

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        proxyMap.put(induBondShardYieldSpreadCurveRepository.getClass().getGenericSuperclass(), induBondShardYieldSpreadCurveRepository);
        proxyMap.put(udicBondShardYieldSpreadCurveRepository.getClass().getGenericSuperclass(), udicBondShardYieldSpreadCurveRepository);
        builderFactory.put(InduBondShardYieldSpreadParamDTO.class, InduBondShardYieldSpreadParamDTO::builder);
        builderFactory.put(UdicBondShardYieldSpreadParamDTO.class, UdicBondShardYieldSpreadParamDTO::builder);
    }

    /**
     * 注解切面
     */
    @Pointcut("@annotation(com.innodealing.onshore.yieldspread.router.annotation.ShardYieldSpreadCurve)")
    public void induShardAnnotation() {
        // the pointcut expression
    }


    /**
     * 环绕通知
     *
     * @param point 切点
     * @return object
     * @throws Throwable 异常
     */
    @Around("induShardAnnotation()")
    public Object doSharding(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method repositoryMethod = signature.getMethod();
        ShardYieldSpreadCurve annotation = repositoryMethod.getAnnotation(ShardYieldSpreadCurve.class);
        Class<?> annotationClass = annotation.type();
        String annotationMethod = annotation.method();
        YieldSpreadCurveShardEnum shardEnum = annotation.level();
        Map<String, Object> args = getParam(point);
        for (Object arg : args.values()) {
            // 产业利差查询
            if (isShardParam(arg) && isShard(arg)) {
                Method[] methods = annotationClass.getMethods();
                annotationMethod = getAnnotationMethod(repositoryMethod, annotationMethod);
                for (Method method : methods) {
                    if (method.getName().equals(annotationMethod)) {
                        Class<?> parameterType = method.getParameterTypes()[0];
                        AbstractShardParamDTO shardParam = createShardParam(arg, shardEnum, (JSONObject) JSONObject.toJSON(args), parameterType);
                        Object invoke = method.invoke(proxyMap.get(annotationClass), shardParam, createRouter(shardParam));
                        return responseDTO(invoke, repositoryMethod, method);
                    }
                }
            }
        }
        return point.proceed(point.getArgs());
    }

    private static String getAnnotationMethod(Method repositoryMethod, String annotationMethod) {
        return StringUtils.isBlank(annotationMethod) ? repositoryMethod.getName() : annotationMethod;
    }

    private boolean isShardParam(Object arg) {
        return arg instanceof InduBondYieldSpreadParamDTO
                || arg instanceof UdicBondYieldSpreadParamDTO
                || arg instanceof BasePanoramaRequestDTO
                || arg instanceof UniversalYieldSpreadSearchParam;
    }

    private AbstractShardParamDTO createShardParam(Object arg, YieldSpreadCurveShardEnum shardEnum, JSONObject json, Class<?> parameterType) {
        return builderFactory.get(parameterType).get()
                .searchQueryParam(arg)
                .level(shardEnum)
                .extra(json)
                .build();
    }

    private Object responseDTO(Object invoke, Method repositoryMethod, Method shardMethod) {
        try {
            // shard方法放回值类型
            Class<?> shardResponseClass = getClazz(shardMethod.getGenericReturnType());
            Class<?> clazz = getClazz(repositoryMethod.getGenericReturnType());
            if (invoke instanceof List) {
                if (shardResponseClass.isAssignableFrom(InduShardBondYieldSpreadCurveDO.class)) {
                    List<InduShardBondYieldSpreadCurveDO> shardResult = (List<InduShardBondYieldSpreadCurveDO>) invoke;
                    return shardResult.stream().map(induShardDo -> ShardConverter.converterInduToPg(induShardDo, clazz)).collect(Collectors.toList());
                }
                if (shardResponseClass.isAssignableFrom(UdicShardBondYieldSpreadCurveDO.class)) {
                    List<UdicShardBondYieldSpreadCurveDO> shardResult = (List<UdicShardBondYieldSpreadCurveDO>) invoke;
                    return shardResult.stream().map(udicShardDo -> ShardConverter.converterUdicToPg(udicShardDo, clazz)).collect(Collectors.toList());
                }

            }
            if (invoke instanceof Optional) {
                if (shardResponseClass.isAssignableFrom(InduShardBondYieldSpreadCurveDO.class)) {
                    Optional<InduShardBondYieldSpreadCurveDO> shardResult = (Optional<InduShardBondYieldSpreadCurveDO>) invoke;
                    return Optional.ofNullable(ShardConverter.converterInduToPg(shardResult.orElse(null), clazz));
                }
                if (shardResponseClass.isAssignableFrom(UdicShardBondYieldSpreadCurveDO.class)) {
                    Optional<UdicShardBondYieldSpreadCurveDO> shardResult = (Optional<UdicShardBondYieldSpreadCurveDO>) invoke;
                    return Optional.ofNullable(ShardConverter.converterUdicToPg(shardResult.orElse(null), clazz));
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return invoke;
    }

    private Class<?> getClazz(Type genericSuperType) {
        if (isParameterizedType(genericSuperType)) {
            ParameterizedType returnType = (ParameterizedType) genericSuperType;
            Type responseType = returnType.getActualTypeArguments()[0];
            if (responseType instanceof WildcardType) {
                return (Class<?>) ((WildcardType) responseType).getUpperBounds()[0];
            }
            if (responseType instanceof Class) {
                return (Class<?>) responseType;
            }
        }
        return (Class<?>) genericSuperType;
    }

    private boolean isParameterizedType(Type genericType) {
        return genericType instanceof ParameterizedType;
    }


    private AbstractRatingRouter createRouter(AbstractShardParamDTO searchParameter) {
        AbstractRatingRouter router = new EmptyRouter();
        Integer[] bondImpliedRatingMappings = searchParameter.getBondImpliedRatingMappings();
        if (isNotEmpty(bondImpliedRatingMappings)) {
            router = implicitRatingRouterFactory.newRatingRouter(bondImpliedRatingMappings);
        }
        Integer[] comYyRatingMappings = searchParameter.getComYyRatingMappings();
        if (isNotEmpty(comYyRatingMappings)) {
            router = yyRatingRouterFactory.newRatingRouter(comYyRatingMappings);
        }
        if (searchParameter.getStartSpreadDate() != null && searchParameter.getEndSpreadDate() != null) {
            router.setSpreadDateRange(searchParameter.getStartSpreadDate(), searchParameter.getEndSpreadDate());
        }
        if (searchParameter.getSpreadDate() != null) {
            router.setSpreadDateRange(searchParameter.getSpreadDate(), searchParameter.getSpreadDate());
        }
        router.setLevel(searchParameter.getLevel());
        return router;
    }

    private boolean isShard(Object param) {
        JSONObject json = (JSONObject) JSONObject.toJSON(param);
        JSONArray bondImpliedRatingMappings = json.getJSONArray("bondImpliedRatingMappings");
        JSONArray comYyRatingMappings = json.getJSONArray("comYyRatingMappings");
        return CollectionUtils.isNotEmpty(bondImpliedRatingMappings) || CollectionUtils.isNotEmpty(comYyRatingMappings);

    }

    /**
     * 构建Map参数
     *
     * @param proceedingJoinPoint 切点
     * @return map
     */
    public Map<String, Object> getParam(ProceedingJoinPoint proceedingJoinPoint) {
        Map<String, Object> map = Maps.newHashMap();
        Object[] values = proceedingJoinPoint.getArgs();
        String[] names = ((CodeSignature) proceedingJoinPoint.getSignature()).getParameterNames();
        for (int i = 0; i < names.length; i++) {
            map.put(names[i], values[i]);
        }
        return map;
    }

}