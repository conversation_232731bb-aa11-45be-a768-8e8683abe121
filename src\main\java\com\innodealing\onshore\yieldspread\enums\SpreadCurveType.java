package com.innodealing.onshore.yieldspread.enums;


import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 1 信用利差，2 超额利差
 *
 * <AUTHOR>
 */
public enum SpreadCurveType implements ITextValueEnum {
    /**
     * 信用利差
     */
    NET_PRICE(1, "信用利差"),
    /**
     * 超额利差
     */
    FULL_PRICE(2, "超额利差"),
    ;

    private final int value;
    private final String text;

    SpreadCurveType(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }
}
