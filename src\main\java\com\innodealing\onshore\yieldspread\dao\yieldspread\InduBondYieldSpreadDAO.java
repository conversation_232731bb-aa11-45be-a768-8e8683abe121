package com.innodealing.onshore.yieldspread.dao.yieldspread;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.QueryHelper;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.config.datasource.YieldSpreadDataSourceConfig;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.helper.YieldSpreadHelper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.InduBondYieldSpreadGroupMapper;
import com.innodealing.onshore.yieldspread.mapper.yieldspread.InduBondYieldSpreadMapper;
import com.innodealing.onshore.yieldspread.model.bo.BondCreditSpreadBO;
import com.innodealing.onshore.yieldspread.model.dto.InduBondYieldSpreadParamDTO;
import com.innodealing.onshore.yieldspread.model.dto.SortDTO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.InduBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.yieldspread.group.InduBondYieldSpreadGroupDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.*;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

/**
 * 产业债利差 DAO
 *
 * <AUTHOR>
 **/
@Repository
public class InduBondYieldSpreadDAO {

    @Resource
    private InduBondYieldSpreadMapper induBondYieldSpreadMapper;

    @Resource
    private InduBondYieldSpreadGroupMapper induBondYieldSpreadGroupMapper;

    @Qualifier(YieldSpreadDataSourceConfig.SESSION_FACTORY_NAME)
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Qualifier(YieldSpreadDataSourceConfig.TRANSACTION_TEMPLATE_NAME)
    @Resource
    private TransactionTemplate transactionTemplate;

    private QueryHelper queryHelper = new QueryHelper();

    /**
     * 查询发行人唯一编码
     *
     * @param searchParameter 查询发行人唯一编码请求参数
     * @return {@link Long[]} 发行人数据集
     */
    public Set<Long> listComUniCodes(InduBondYieldSpreadParamDTO searchParameter) {
        if (isNull(searchParameter)) {
            return Collections.emptySet();
        }
        if (Objects.nonNull(searchParameter.getComUniCode())) {
            return SetUtils.hashSet(searchParameter.getComUniCode());
        }
        if (ArrayUtils.isNotEmpty(searchParameter.getComUniCodes())) {
            return SetUtils.hashSet(searchParameter.getComUniCodes());
        }
        DynamicQuery<InduBondYieldSpreadDO> query = DynamicQuery.createQuery(InduBondYieldSpreadDO.class)
                .selectDistinct(InduBondYieldSpreadDO::getComUniCode)
                .and(this.listCommonFilters(searchParameter));
        return induBondYieldSpreadMapper.selectByDynamicQuery(query).stream().map(InduBondYieldSpreadDO::getComUniCode).collect(Collectors.toSet());
    }

    /**
     * 创建分片表
     *
     * @param tableName 表名
     */
    public void createShardingTable(String tableName) {
        induBondYieldSpreadMapper.createShardingTable(tableName);
    }

    /**
     * 根据主体分组获取城投债利差
     *
     * @param spreadDate 开始日期
     * @return 城投债利差
     */
    public List<InduBondYieldSpreadGroupDO> listInduBondYieldSpreadGroupDOs(Date spreadDate) {
        long startPk = ShardingUtils.getMinPkOfDate(spreadDate);
        long endPk = ShardingUtils.getMaxPkOfDate(spreadDate);
        GroupedQuery<InduBondYieldSpreadDO, InduBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(InduBondYieldSpreadDO.class, InduBondYieldSpreadGroupDO.class).first(YieldSpreadConst.RDS_MYSQL_HINT_FORCE_TO_MASTER)
                        .select(InduBondYieldSpreadGroupDO::getComUniCode,
                                InduBondYieldSpreadGroupDO::getInduLevel1Code, InduBondYieldSpreadGroupDO::getInduLevel1Name,
                                InduBondYieldSpreadGroupDO::getInduLevel2Code,
                                InduBondYieldSpreadGroupDO::getInduLevel2Name, InduBondYieldSpreadGroupDO::getBusinessNature,
                                InduBondYieldSpreadGroupDO::getComExtRatingMapping, InduBondYieldSpreadGroupDO::getSpreadDate)
                        .and(InduBondYieldSpreadDO::getId, between(startPk, endPk))
                        .and(InduBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                        .and(g -> g.and(InduBondYieldSpreadDO::getBondCreditSpread, notEqual(null))
                                .or(InduBondYieldSpreadDO::getBondExcessSpread, notEqual(null)))
                        .groupBy(InduBondYieldSpreadDO::getComUniCode);
        return induBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 批量更新
     *
     * @param startDate                 开始日期
     * @param endDate                   结束日期
     * @param induBondYieldSpreadDOList 产业债利差列表
     * @return 受影响的行数
     */
    public int saveInduBondYieldSpreadDOList(Date startDate, Date endDate, List<InduBondYieldSpreadDO> induBondYieldSpreadDOList) {
        if (CollectionUtils.isEmpty(induBondYieldSpreadDOList)) {
            return 0;
        }
        AtomicInteger effectRows = new AtomicInteger();
        long startPk = ShardingUtils.getMinPkOfDate(startDate);
        long endPk = ShardingUtils.getMaxPkOfDate(endDate);
        Set<Long> bondUniCodes = induBondYieldSpreadDOList.stream().map(InduBondYieldSpreadDO::getBondUniCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 批量查询已存在的数据
        DynamicQuery<InduBondYieldSpreadDO> query = DynamicQuery.createQuery(InduBondYieldSpreadDO.class)
                .select(InduBondYieldSpreadDO::getId, InduBondYieldSpreadDO::getBondUniCode,
                        InduBondYieldSpreadDO::getSpreadDate, InduBondYieldSpreadDO::getInduLevel1Code,
                        InduBondYieldSpreadDO::getInduLevel1Name, InduBondYieldSpreadDO::getInduLevel2Code,
                        InduBondYieldSpreadDO::getInduLevel2Name, InduBondYieldSpreadDO::getBusinessFilterNature,
                        InduBondYieldSpreadDO::getBusinessNature)
                .and(InduBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(InduBondYieldSpreadDO::getSpreadDate, isEqual(startDate))
                .and(InduBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<InduBondYieldSpreadDO> existDataList = induBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(existDataList)) {
            effectRows.addAndGet(doBatchInsert(induBondYieldSpreadDOList));
        } else {
            Map<String, InduBondYieldSpreadDO> existInduBondYieldSpreadDOMap = existDataList.stream().collect(Collectors.toMap(x ->
                            String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, x.getBondUniCode(), x.getSpreadDate().getTime()),
                    Function.identity(), (x1, x2) -> x2));
            List<InduBondYieldSpreadDO> insertList = new ArrayList<>();
            List<InduBondYieldSpreadDO> updateList = new ArrayList<>();
            for (InduBondYieldSpreadDO induBondYieldSpreadDO : induBondYieldSpreadDOList) {
                InduBondYieldSpreadDO existInduBondYieldSpreadDO = existInduBondYieldSpreadDOMap.
                        get(String.format(YieldSpreadHelper.NAMESPACE_KEY_PLACEHOLDER, induBondYieldSpreadDO.getBondUniCode(),
                                induBondYieldSpreadDO.getSpreadDate().getTime()));
                if (isNull(existInduBondYieldSpreadDO)) {
                    insertList.add(induBondYieldSpreadDO);
                } else {
                    induBondYieldSpreadDO.setId(existInduBondYieldSpreadDO.getId());
                    // 这里的列是需要保留历史数据的 所以我们更新的时候不能改变
                    ObjectExtensionUtils.ifNonNull(existInduBondYieldSpreadDO.getInduLevel1Code(),
                            induBondYieldSpreadDO::setInduLevel1Code);
                    ObjectExtensionUtils.ifNonNull(existInduBondYieldSpreadDO.getInduLevel1Name(),
                            induBondYieldSpreadDO::setInduLevel1Name);
                    ObjectExtensionUtils.ifNonNull(existInduBondYieldSpreadDO.getInduLevel2Code(),
                            induBondYieldSpreadDO::setInduLevel2Code);
                    ObjectExtensionUtils.ifNonNull(existInduBondYieldSpreadDO.getInduLevel2Name(),
                            induBondYieldSpreadDO::setInduLevel2Name);
                    ObjectExtensionUtils.ifNonNull(existInduBondYieldSpreadDO.getBusinessFilterNature(),
                            induBondYieldSpreadDO::setBusinessFilterNature);
                    ObjectExtensionUtils.ifNonNull(existInduBondYieldSpreadDO.getBusinessNature(),
                            induBondYieldSpreadDO::setBusinessNature);
                    updateList.add(induBondYieldSpreadDO);
                }
            }
            // 开启事务执行
            transactionTemplate.execute(transactionStatus -> {
                // 批量操作
                effectRows.addAndGet(doBatchUpdate(updateList));
                effectRows.addAndGet(doBatchInsert(insertList));
                return true;
            });
        }
        return effectRows.get();
    }

    /**
     * 批量更新
     *
     * @param updateList 产业债利差列表
     * @return 受影响的行数
     */
    private int doBatchUpdate(List<InduBondYieldSpreadDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        MapperBatchAction<InduBondYieldSpreadMapper> updateBatchAction =
                MapperBatchAction.create(InduBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (InduBondYieldSpreadDO induBondYieldSpreadDO : updateList) {
            updateBatchAction.addAction(mapper -> {
                DynamicQuery<InduBondYieldSpreadDO> updateQuery = DynamicQuery.createQuery(InduBondYieldSpreadDO.class)
                        .and(InduBondYieldSpreadDO::getId, isEqual(induBondYieldSpreadDO.getId()));
                mapper.updateSelectiveByDynamicQuery(induBondYieldSpreadDO, updateQuery);
            });
        }
        return updateBatchAction.doBatchActions();
    }

    /**
     * 批量插入
     *
     * @param insertList 产业债利差列表
     * @return 受影响的行数
     */
    private int doBatchInsert(List<InduBondYieldSpreadDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        MapperBatchAction<InduBondYieldSpreadMapper> insertBatchAction =
                MapperBatchAction.create(InduBondYieldSpreadMapper.class, sqlSessionFactory, YieldSpreadHelper.BATCH_SIZE);
        for (InduBondYieldSpreadDO induBondYieldSpreadDO : insertList) {
            insertBatchAction.addAction(mapper -> mapper.insert(induBondYieldSpreadDO));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 分页查询行业单券利差数据
     *
     * @param searchParameter 分页查询行业单券利差请求参数
     * @return {@link NormPagingResult}<{@link InduBondYieldSpreadDO}> 分页查询行业单券利差请求响应数据
     */
    public NormPagingResult<InduBondYieldSpreadDO> getBondYieldSpreadPaging(InduBondYieldSpreadParamDTO searchParameter) {
        if (Objects.isNull(searchParameter)) {
            return new NormPagingResult<>();
        }
        NormPagingQuery<InduBondYieldSpreadDO> query = NormPagingQuery
                .createQuery(InduBondYieldSpreadDO.class, searchParameter.getPageNum(), searchParameter.getPageSize())
                .and(this.listCommonFilters(searchParameter));
        if (Objects.nonNull(searchParameter.getSort())) {
            SortDTO sort = searchParameter.getSort();
            String columnName = queryHelper.getQueryColumnByProperty(InduBondYieldSpreadDO.class, sort.getPropertyName());
            query.addSorts(new CustomSortDescriptor(columnName + " IS NULL"));
            query.addSorts(new SortDescriptor(sort.getPropertyName(), sort.getSortDirection()));
        }
        return induBondYieldSpreadMapper.selectByNormalPaging(query);
    }

    private BaseFilterDescriptor<InduBondYieldSpreadDO>[] listCommonFilters(InduBondYieldSpreadParamDTO searchParameter) {
        long startPk = ShardingUtils.getMinPkOfDate(searchParameter.getSpreadDate());
        long endPk = ShardingUtils.getMaxPkOfDate(searchParameter.getSpreadDate());
        FilterGroupDescriptor<InduBondYieldSpreadDO> filterGroup = FilterGroupDescriptor.create(InduBondYieldSpreadDO.class)
                .and(InduBondYieldSpreadDO::getId, between(startPk, endPk))
                .and(isNotEmpty(searchParameter.getComUniCodes()), InduBondYieldSpreadDO::getComUniCode, in(searchParameter.getComUniCodes()))
                .and(isNotEmpty(searchParameter.getBondUniCodes()), InduBondYieldSpreadDO::getBondUniCode, in(searchParameter.getBondUniCodes()))
                .and(InduBondYieldSpreadDO::getSpreadDate, isEqual(searchParameter.getSpreadDate()))
                .and(nonNull(searchParameter.getComUniCode()), InduBondYieldSpreadDO::getComUniCode, isEqual(searchParameter.getComUniCode()))
                .and(nonNull(searchParameter.getBondUniCode()), InduBondYieldSpreadDO::getBondUniCode, isEqual(searchParameter.getBondUniCode()))
                .and(nonNull(searchParameter.getBondExtRatingMapping()), InduBondYieldSpreadDO::getBondExtRatingMapping, isEqual(searchParameter.getBondExtRatingMapping()))
                .and(nonNull(searchParameter.getSpreadBondType()), InduBondYieldSpreadDO::getSpreadBondType, isEqual(searchParameter.getSpreadBondType()))
                .and(nonNull(searchParameter.getGuaranteeStatus()) && 0 == searchParameter.getGuaranteeStatus(),
                        g -> g.and(InduBondYieldSpreadDO::getGuaranteedStatus, isEqual(searchParameter.getGuaranteeStatus()))
                                .or(InduBondYieldSpreadDO::getGuaranteedStatus, isEqual(null)))
                .and(nonNull(searchParameter.getGuaranteeStatus()) && 1 == searchParameter.getGuaranteeStatus(),
                        g -> g.and(InduBondYieldSpreadDO::getGuaranteedStatus, isEqual(searchParameter.getGuaranteeStatus())))
                .and(nonNull(searchParameter.getIndustryCode1()), InduBondYieldSpreadDO::getInduLevel1Code, isEqual(searchParameter.getIndustryCode1()))
                .and(nonNull(searchParameter.getIndustryCode2()), InduBondYieldSpreadDO::getInduLevel2Code, isEqual(searchParameter.getIndustryCode2()))
                .and(nonNull(searchParameter.getSpreadRemainingTenorTag()), InduBondYieldSpreadDO::getSpreadRemainingTenorTag,
                        isEqual(searchParameter.getSpreadRemainingTenorTag()))
                .and(isNotEmpty(searchParameter.getComYyRatingMappings()), InduBondYieldSpreadDO::getComYyRatingMapping, in(searchParameter.getComYyRatingMappings()))
                .and(isNotEmpty(searchParameter.getBondImpliedRatingMappings()), InduBondYieldSpreadDO::getBondImpliedRatingMapping,
                        in(searchParameter.getBondImpliedRatingMappings()))
                .and(isNotEmpty(searchParameter.getBusinessFilterNatures()), InduBondYieldSpreadDO::getBusinessFilterNature, in(searchParameter.getBusinessFilterNatures()))
                .and(InduBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()));
        return filterGroup.getFilters();
    }

    /**
     * 查询单券信用利差
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}<{@link BondCreditSpreadBO}> 信用利差数据集
     */
    public List<BondCreditSpreadBO> listBondCreditSpreads(Date spreadDate, Set<Long> bondUniCodes) {
        if (Objects.isNull(spreadDate) || org.springframework.util.CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        Long minId = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxId = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<InduBondYieldSpreadDO> query = DynamicQuery.createQuery(InduBondYieldSpreadDO.class)
                .select(InduBondYieldSpreadDO::getBondUniCode, InduBondYieldSpreadDO::getSpreadDate,
                        InduBondYieldSpreadDO::getBondCreditSpread)
                .and(InduBondYieldSpreadDO::getId, between(minId, maxId))
                .and(InduBondYieldSpreadDO::getSpreadDate, isEqual(spreadDate))
                .and(InduBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        List<InduBondYieldSpreadDO> udicBondYieldSpreadList = induBondYieldSpreadMapper.selectByDynamicQuery(query);
        if (CollectionUtils.isEmpty(udicBondYieldSpreadList)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(udicBondYieldSpreadList, BondCreditSpreadBO.class);
    }

    /**
     * 获取债券
     *
     * @return 债券
     */
    public List<InduBondYieldSpreadGroupDO> listBonds() {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(YieldSpreadConst.CURVE_START_DATE);
        Long manPkOfDate = ShardingUtils.getMaxPkOfDate(Date.valueOf(LocalDate.now()));
        GroupedQuery<InduBondYieldSpreadDO, InduBondYieldSpreadGroupDO> groupedQuery =
                GroupByQuery.createQuery(InduBondYieldSpreadDO.class, InduBondYieldSpreadGroupDO.class)
                        .select(InduBondYieldSpreadGroupDO::getComUniCode,
                                InduBondYieldSpreadGroupDO::getBondUniCode,
                                InduBondYieldSpreadGroupDO::getBondCode)
                        .and(InduBondYieldSpreadDO::getId, between(minPkOfDate, manPkOfDate))
                        .and(InduBondYieldSpreadDO::getDeleted, isEqual(Deleted.NO_DELETED.getValue()))
                        .groupBy(InduBondYieldSpreadDO::getBondUniCode);
        return induBondYieldSpreadGroupMapper.selectByGroupedQuery(groupedQuery);
    }

    /**
     * 查询产业利差数据集合
     *
     * @param spreadDate   利差日期
     * @param bondUniCodes 债券唯一编码集合
     * @return {@link List}{@link InduBondYieldSpreadDO}
     */
    public List<InduBondYieldSpreadDO> listInduBondYieldSpreads(@NonNull Date spreadDate, @NonNull Set<Long> bondUniCodes) {
        Long minPkOfDate = ShardingUtils.getMinPkOfDate(spreadDate);
        Long maxPkOfDate = ShardingUtils.getMaxPkOfDate(spreadDate);
        DynamicQuery<InduBondYieldSpreadDO> query = DynamicQuery.createQuery(InduBondYieldSpreadDO.class)
                .and(InduBondYieldSpreadDO::getId, between(minPkOfDate, maxPkOfDate))
                .and(InduBondYieldSpreadDO::getBondUniCode, in(bondUniCodes));
        return induBondYieldSpreadMapper.selectByDynamicQuery(query);
    }

}
