package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgUdicBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgUdicBondYieldSpreadGroupDO;


/**
 * pg产业债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface PgUdicBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<PgUdicBondYieldSpreadDO,
        PgUdicBondYieldSpreadGroupDO> {

}
