package com.innodealing.onshore.yieldspread.model.dto.request;

import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.innodealing.onshore.yieldspread.enums.SpreadFieldGroupUseStatusEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBaseLgBondYieldSpreadDO;
import io.swagger.annotations.ApiModelProperty;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.isEqual;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * 地方债区域利差请求筛选条件参数
 *
 * <AUTHOR>
 */
public class LgSpreadSelectedRequestDTO {


    @ApiModelProperty("地方债类型： 1 一般债; 2 专项债;  99 其他")
    private Integer lgBondType;

    @ApiModelProperty("提前还本状态 0: 到期还本 1: 提前还本")
    private Integer prepaymentStatus;

    @ApiModelProperty("资金用途性质: 1 新增; 2 再融资; 3 置换; 4 特殊再融资 99 其他")
    private Integer fundUseType;

    public Integer getLgBondType() {
        return lgBondType;
    }

    public void setLgBondType(Integer lgBondType) {
        this.lgBondType = lgBondType;
    }

    public Integer getPrepaymentStatus() {
        return prepaymentStatus;
    }

    public void setPrepaymentStatus(Integer prepaymentStatus) {
        this.prepaymentStatus = prepaymentStatus;
    }

    public Integer getFundUseType() {
        return fundUseType;
    }

    public void setFundUseType(Integer fundUseType) {
        this.fundUseType = fundUseType;
    }

    /**
     * 根据请求对象获取筛选条件
     *
     * @param clazz 数据所在类
     * @param <T>   泛型
     * @return 返回筛选条件
     */
    public <T extends PgBaseLgBondYieldSpreadDO> FilterGroupDescriptor<T> listLgSpreadSelectedFilters(Class<T> clazz) {
        return FilterGroupDescriptor.create(clazz)
                .and(isNull(this.getLgBondType()), T::getUsingLgBondType, isEqual(SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(this.getLgBondType()), T::getUsingLgBondType, isEqual(SpreadFieldGroupUseStatusEnum.USE_FIELD_GROUP.getValue()))
                .and(nonNull(this.getLgBondType()), T::getLgBondType, isEqual(this.getLgBondType()))
                .and(isNull(this.getPrepaymentStatus()), T::getUsingPrepaymentStatus, isEqual(SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(this.getPrepaymentStatus()), T::getUsingPrepaymentStatus, isEqual(SpreadFieldGroupUseStatusEnum.USE_FIELD_GROUP.getValue()))
                .and(nonNull(this.getPrepaymentStatus()), T::getPrepaymentStatus, isEqual(this.getPrepaymentStatus()))
                .and(isNull(this.getFundUseType()), T::getUsingFundUseType, isEqual(SpreadFieldGroupUseStatusEnum.UNUSED_FIELD_GROUP.getValue()))
                .and(nonNull(this.getFundUseType()), T::getUsingFundUseType, isEqual(SpreadFieldGroupUseStatusEnum.USE_FIELD_GROUP.getValue()))
                .and(nonNull(this.getFundUseType()), T::getFundUseType, isEqual(this.getFundUseType()));
    }

    /**
     * 新建builder
     *
     * @return Builder对象
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构造参数
     */
    public static class Builder {

        /**
         * 地方债类型： 1 一般债; 2 专项债;  99 其他
         */
        private Integer lgBondType;

        /**
         * 提前还本状态 0: 不提前还本 1: 提前还本
         */
        private Integer prepaymentStatus;

        /**
         * 资金用途性质: 1 新增; 2 再融资; 3 置换;  99 其他
         */
        private Integer fundUseType;


        /**
         * 构建 lgBondType
         *
         * @param lgBondType 地方债类型
         * @return Builder
         */
        public Builder lgBondType(Integer lgBondType) {
            this.lgBondType = lgBondType;
            return this;
        }

        /**
         * 构建 prepaymentStatus
         *
         * @param prepaymentStatus 提前还本状态
         * @return Builder
         */
        public Builder prepaymentStatus(Integer prepaymentStatus) {
            this.prepaymentStatus = prepaymentStatus;
            return this;
        }

        /**
         * 构建 fundUseType
         *
         * @param fundUseType 资金用途性质
         * @return Builder
         */
        public Builder fundUseType(Integer fundUseType) {
            this.fundUseType = fundUseType;
            return this;
        }


        /**
         * 构建对象
         *
         * @return 请求对象
         */
        public LgSpreadSelectedRequestDTO build() {
            LgSpreadSelectedRequestDTO lgSpreadSelectedRequestDTO = new LgSpreadSelectedRequestDTO();
            lgSpreadSelectedRequestDTO.setLgBondType(this.lgBondType);
            lgSpreadSelectedRequestDTO.setPrepaymentStatus(this.prepaymentStatus);
            lgSpreadSelectedRequestDTO.setFundUseType(this.fundUseType);
            return lgSpreadSelectedRequestDTO;
        }
    }
}
