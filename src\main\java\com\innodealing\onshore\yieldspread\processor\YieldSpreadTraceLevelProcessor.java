package com.innodealing.onshore.yieldspread.processor;

import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.enums.YieldSpreadChartTypeEnum;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldPanoramaAbsDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgBondYieldSpreadTraceAbsDO;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.*;

/**
 * 利差追踪-等级利差处理器
 *
 * <AUTHOR>
 */
@Component
public class YieldSpreadTraceLevelProcessor implements YieldSpreadTraceProcessor {

    private static final Set<YieldPanoramaBondTypeEnum> SUPPORT_BOND_TYPES = EnumSet.of(INDUSTRIAL_BOND, URBAN_BOND, MEDIUM_AND_SHORT_TERMS_NOTE,
            GENERAL_BANK_BOND, BANK_SECONDARY_CAPITAL_BOND, BANK_PERPETUAL_BOND, SECURITIES_BOND, NCD, INSU_CAPITAL_SUPPLEMENT);

    @Override
    public Boolean supportBondType(@NonNull YieldPanoramaBondTypeEnum traceBondTypeEnum) {
        return SUPPORT_BOND_TYPES.contains(traceBondTypeEnum);
    }

    @Override
    public List<PgBondYieldSpreadTraceAbsDO> processYieldSpreadTraceYtm(YieldSpreadTraceContext context) {
        final YieldPanoramaBondTypeEnum bondTypeEnum = context.getBondTypeEnum();
        if (Objects.isNull(bondTypeEnum.getTopRating())) {
            return Collections.emptyList();
        }
        // 基础数据，根据基础数据计算等级利差
        List<PgBondYieldPanoramaAbsDO> absBondYieldPanoramas = context.getAbsBondYieldPanoramas();
        Map<Integer, PgBondYieldPanoramaAbsDO> curveCodeQuantileMap =
                absBondYieldPanoramas.stream().collect(Collectors.toMap(PgBondYieldPanoramaAbsDO::getCurveCode, Function.identity(), (o, v) -> o));
        PgBondYieldPanoramaAbsDO topRatingAbs = curveCodeQuantileMap.getOrDefault(bondTypeEnum.getTopRating(), new PgBondYieldPanoramaAbsDO());
        return curveCodeQuantileMap.entrySet().stream().filter(entry -> !entry.getKey().equals(bondTypeEnum.getTopRating()))
                .map(entry -> convertAbsSubtractToTrace(entry.getValue(), topRatingAbs, bondTypeEnum, context.getIssueDate(),
                        entry.getKey(), YieldSpreadChartTypeEnum.GRADE_SPREAD)).
                collect(Collectors.toList());
    }

}
