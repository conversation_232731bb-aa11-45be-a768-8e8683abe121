package com.innodealing.onshore.yieldspread.dao.pgyieldspread.redis;

import com.innodealing.onshore.yieldspread.dao.pgyieldspread.PgSecuBondYieldSpreadDAO;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadCacheConst.LIST_BOND_SECU_SPREAD_CURVES_KEY;

/**
 * 证券单券利差缓存DAO
 *
 * <AUTHOR>
 */
@Repository
public class SecuBondYieldSpreadRedisDAO extends AbstractBondYieldSpreadCache {

    @Resource
    private PgSecuBondYieldSpreadDAO pgSecuBondYieldSpreadDAO;
    @Resource
    private HolidayService holidayService;

    /**
     * 构造函数
     *
     * @param stringRedisTemplate 操作redis的对象
     */
    protected SecuBondYieldSpreadRedisDAO(StringRedisTemplate stringRedisTemplate) {
        super(stringRedisTemplate);
    }

    @Override
    protected String cacheKey(@NonNull Long bondUniCode) {
        return String.format(LIST_BOND_SECU_SPREAD_CURVES_KEY, bondUniCode);
    }

    @Override
    protected List<BondYieldSpreadCurveBO> fromDB(@NonNull Long bondUniCode) {
        return pgSecuBondYieldSpreadDAO.listYieldSpreadCurvesByBond(bondUniCode, null, null);
    }

    @Override
    protected long expiredTime() {
        // 4-8点的时候缓存时间为10分钟一次，防止这个时间点增量数据进来以后，没有缓存进去
        int hour = LocalDateTime.now().getHour();
        if (hour >= EARLY_EXPIRED_TIME_START_HOUR && hour <= EARLY_EXPIRED_TIME_END_HOUR) {
            return EARLY_EXPIRED_TIME;
        }
        LocalDateTime midnight = LocalDateTime.of(holidayService.getNextWorkDay(Date.valueOf(LocalDate.now())).toLocalDate(),
                LocalTime.now().withHour(FOUR_HOURS).withMinute(ZERO_MINUTE).withSecond(ZERO_SECOND).withNano(ZERO_NANO));
        return ChronoUnit.MILLIS.between(LocalDateTime.now(), midnight);
    }
}
