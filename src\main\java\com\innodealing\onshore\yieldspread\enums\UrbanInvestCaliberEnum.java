package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

/**
 * 城投口径枚举
 *
 * <AUTHOR>
 * @date 2024/6/11 10:26
 **/
public enum UrbanInvestCaliberEnum implements ITextValueEnum {

    /**
     * 老口径城投, 默认
     */
    UDIC(1, ""),

    /**
     * DM口径城投
     */
    DM_UDIC(2, "dm");

    /**
     * 枚举值
     */
    private final int value;
    /**
     * 分片参数
     */
    private final String text;

    UrbanInvestCaliberEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
