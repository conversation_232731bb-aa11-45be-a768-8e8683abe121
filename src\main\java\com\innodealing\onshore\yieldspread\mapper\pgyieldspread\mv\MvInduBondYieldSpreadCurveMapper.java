package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.yieldspread.mapper.pgyieldspread.PgBaseMapper;
import com.innodealing.onshore.yieldspread.model.dto.MvInduBondYieldSpreadCurveParameter;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.mv.MvInduBondYieldSpreadCurveAllDO;
import org.apache.ibatis.annotations.Param;

/**
 * 行业利差曲线-物化视图
 *
 * <AUTHOR>
 */

public interface MvInduBondYieldSpreadCurveMapper extends PgBaseMapper<MvInduBondYieldSpreadCurveParameter>, DynamicQueryMapper<MvInduBondYieldSpreadCurveAllDO> {

    /**
     * 创建物化视图
     * @param parameter 构建参数
     */
    void createMvRatingRouter(@Param("parameter") MvInduBondYieldSpreadCurveParameter parameter);

    /**
     * 刷新行业利差曲线物化视图(不包含行业1，行业2)
     */
    void refreshMvInduBondYieldSpreadCurveAll();

    /**
     * 刷新行业利差曲线物化视图(包含行业1)
     */
    void refreshMvInduBondYieldSpreadCurveIndu1();

    /**
     * 刷新行业利差曲线物化视图(包含行业2)
     */
    void refreshMvInduBondYieldSpreadCurveIndu2();


    /**
     * 刷新行业利差曲线物化视图(不包含行业1，行业2), 昨日
     */
    void refreshMvInduBondYieldSpreadCurveAllYesterday();


    /**
     * 刷新行业利差曲线物化视图(包含行业1)，昨日
     */
    void refreshMvInduBondYieldSpreadCurveIndu1Yesterday();


    /**
     * 刷新行业利差曲线物化视图(包含行业2)，昨日
     */
    void refreshMvInduBondYieldSpreadCurveIndu2Yesterday();
}
