package com.innodealing.onshore.yieldspread.service.impl;

import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.utils.DatePatternUtils;
import com.innodealing.onshore.bondmetadata.utils.ShardingUtils;
import com.innodealing.onshore.yieldspread.model.bo.BondYieldSpreadCurveBO;
import com.innodealing.onshore.yieldspread.model.dto.response.InduCurveResponseDTO;
import com.innodealing.onshore.yieldspread.model.dto.response.UdicCurveResponseDTO;
import com.innodealing.onshore.yieldspread.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * redis 服务
 *
 * <AUTHOR>
 */
@Service
public class RedisServiceImpl implements RedisService {

    private static final long FLOW_ID_HOUR_WIGHT = 1_000_000L;

    private static final long FLOW_ID_GENERATE_START_TIME = Date.valueOf("2023-01-01").getTime();

    private static final long FLOW_ID_GENERATE_SPAN = 3_600_000L;

    /**
     * 缓存的 redis pk
     * key:redisKey
     */
    private static final Map<String, LinkedList<Long>> REDIS_PK_CACHE_MAP = new ConcurrentHashMap<>();

    /**
     * 缓存 redis pk 的数量
     */
    private static final int CACHE_REDIS_PK_QUANTITY = 100;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public long generatePk(String redisKey, Date spreadDate) {
        FastDateFormat fastDateFormat = DatePatternUtils.PURE_DATE_FORMAT;
        String key = String.format("%s:%s", redisKey, fastDateFormat.format(spreadDate));
        long flowId = generateFlowId(key);
        return ShardingUtils.getPageId(spreadDate, flowId);
    }

    @Override
    public List<UdicCurveResponseDTO> listUdicCurvesFromCache(String cacheKey) {
        String value = stringRedisTemplate.opsForValue().get(cacheKey);
        return Optional.ofNullable(value).map(val -> {
            List<BondYieldSpreadCurveBO> cacheList = JSON.parseArray(val, BondYieldSpreadCurveBO.class);
            return BeanCopyUtils.copyList(cacheList, UdicCurveResponseDTO.class);
        }).orElse(Collections.emptyList());
    }

    @Override
    public List<InduCurveResponseDTO> listInduCurvesFromCache(String cacheKey) {
        String value = stringRedisTemplate.opsForValue().get(cacheKey);
        return Optional.ofNullable(value).map(val -> {
            List<BondYieldSpreadCurveBO> cacheList = JSON.parseArray(val, BondYieldSpreadCurveBO.class);
            return BeanCopyUtils.copyList(cacheList, InduCurveResponseDTO.class);
        }).orElse(Collections.emptyList());
    }

    @Override
    public void set(String key, String value, long timeout, TimeUnit unit) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        stringRedisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    /**
     * 批量设置值到缓存中
     *
     * @param dataMap key-value数值
     * @param timeout 超时时间
     * @param unit    超时单位
     */
    @Override
    public void multiSet(Map<String, String> dataMap, long timeout, TimeUnit unit) {
        if (dataMap.isEmpty()) {
            return;
        }
        dataMap.forEach((key, value) -> stringRedisTemplate.opsForValue().set(key, value, timeout, unit));
    }

    /**
     * 批量设置值到缓存中,不设过期时间
     *
     * @param dataMap key-value数值
     */
    @Override
    public void multiSet(Map<String, String> dataMap) {
        if (dataMap.isEmpty()) {
            return;
        }
        stringRedisTemplate.opsForValue().multiSet(dataMap);
    }

    /**
     * 缓存值
     *
     * @param key 键
     */
    @Override
    public Optional<String> get(String key) {
        if (StringUtils.isBlank(key)) {
            return Optional.empty();
        }
        return Optional.ofNullable(stringRedisTemplate.opsForValue().get(key));
    }

    @Override
    public List<Date> listAllSpreadDates(String cacheKey) {
        String dateStr = stringRedisTemplate.opsForValue().get(cacheKey);
        return StringUtils.isNotBlank(dateStr) ? JSON.parseArray(dateStr, Date.class) : Collections.emptyList();
    }

    /**
     * 根据key删除
     *
     * @param key key
     */
    @Override
    public void delete(String key) {
        stringRedisTemplate.delete(key);
    }

    /**
     * 批量根据key删除
     *
     * @param keys key列表
     */
    @Override
    public Long delete(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return 0L;
        }
        return stringRedisTemplate.delete(keys);
    }

    /**
     * 今日计数器
     *
     * @param key key
     * @return 计数器
     */
    private long generateFlowId(String key) {
        return getFlowIdHourPrefix() + getRedisId(key);
    }

    /**
     * 每次都去redis拿太慢了(发现dev环境拿一个id要25ms)，所以每次拿CACHE_REDIS_PK_QUANTITY个
     *
     * @param key redis key
     * @return pk
     */
    private long getRedisId(String key) {
        LinkedList<Long> redisPks = REDIS_PK_CACHE_MAP.computeIfAbsent(key, k -> new LinkedList<>());
        synchronized (redisPks) {
            if (CollectionUtils.isEmpty(redisPks)) {
                RedisConnectionFactory redisConnectionFactory = stringRedisTemplate.getRequiredConnectionFactory();
                RedisAtomicLong counter = new RedisAtomicLong(key, redisConnectionFactory);
                //每个key最大不能超过FLOW_ID_HOUR_WIGHT，每个小时最多允许FLOW_ID_HOUR_WIGHT-1个key
                if (counter.get() >= FLOW_ID_HOUR_WIGHT) {
                    counter.set(0L);
                }
                long redisPkStartingPoint = counter.getAndAdd(CACHE_REDIS_PK_QUANTITY);
                for (int i = 1; i <= CACHE_REDIS_PK_QUANTITY; i++) {
                    redisPks.add(redisPkStartingPoint + i);
                }
            }
            return redisPks.pollFirst();
        }
    }

    /**
     * 除生产环境以外的其他环境会存在一个问题，redis key可能被剔除，这就可能导致同一个日期拿到的id是相同的，
     * 插入数据库时会导致主键冲突，所以加了一个以小时为单位的前缀，减少这种情况的发生
     *
     * @return 小时前缀
     */
    private long getFlowIdHourPrefix() {
        long hourDiff = (System.currentTimeMillis() - FLOW_ID_GENERATE_START_TIME) / FLOW_ID_GENERATE_SPAN;
        return hourDiff * FLOW_ID_HOUR_WIGHT;
    }

}
