package com.innodealing.onshore.yieldspread.enums;

import com.innodealing.commons.object.ObjectExtensionUtils;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

import java.sql.Date;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;

/**
 * 利差年跨度
 *
 * <AUTHOR>
 */
public enum SpreadYearSpanEnum implements ITextValueEnum {
    /**
     * 上半年
     */
    FIRST_HALF_YEAR(1, "上半年") {
        @Override
        public Date getStartDate(Integer year) {
            return Date.valueOf(LocalDate.now().withYear(year(year)).with(TemporalAdjusters.firstDayOfYear()));
        }

        @Override
        public Date getEndDate(Integer year) {
            return Date.valueOf(LocalDate.now().withYear(year(year)).withMonth(JUNE_MONTH).with(TemporalAdjusters.lastDayOfMonth()));
        }
    },
    /**
     * 下半年
     */
    NEXT_HALF_YEAR(2, "下半年") {
        @Override
        public Date getStartDate(Integer year) {
            return Date.valueOf(LocalDate.now().withYear(year(year)).withMonth(JUNE_MONTH).with(TemporalAdjusters.firstDayOfNextMonth()));
        }

        @Override
        public Date getEndDate(Integer year) {
            return Date.valueOf(LocalDate.now().withYear(year(year)).with(TemporalAdjusters.lastDayOfYear()));
        }
    };

    /**
     * 6月
     */
    private static final int JUNE_MONTH = 6;
    private Integer code;
    private String text;

    SpreadYearSpanEnum(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.code;
    }

    /**
     * 年或者默认今年
     *
     * @param year 年
     * @return {@link Integer} 年
     */
    public Integer year(Integer year) {
        return ObjectExtensionUtils.getOrDefault(year, LocalDate.now().getYear());
    }

    /**
     * 开始日期
     * @param year 年
     *
     * @return {@link Date} 开始日期
     */
    public abstract Date getStartDate(Integer year);

    /**
     * 结束日期
     * @param year 年
     *
     * @return {@link Date} 结束日期
     */
    public abstract Date getEndDate(Integer year);
}
