package com.innodealing.onshore.yieldspread.controller.internal;

import com.innodealing.onshore.bondmetadata.dto.yieldspread.ComCreditSpreadDTO;
import com.innodealing.onshore.yieldspread.helper.ValidationUtil;
import com.innodealing.onshore.yieldspread.model.dto.request.RefreshYieldCurveParam;
import com.innodealing.onshore.yieldspread.service.InsuBondYieldSpreadService;
import com.innodealing.onshore.yieldspread.service.InsuComYieldSpreadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * (内部)保险利差分析接口
 *
 * <AUTHOR>
 **/
@Api(tags = "(内部)利差分析-保险")
@RestController
@RequestMapping("internal/insu/yield-spread")
@Validated
public class InternalInsuYieldSpreadController {

    @Resource
    private InsuComYieldSpreadService insuComYieldSpreadService;

    @Resource
    private InsuBondYieldSpreadService insuBondYieldSpreadService;

    @PostMapping("/list/com-credit-spread")
    @ApiOperation(value = "最新的主体信用利差,上限200")
    public List<ComCreditSpreadDTO> listCreditSpreadByComUniCodes(
            @ApiParam(name = "comUniCodes", value = "发行人代码", required = true) @RequestBody Set<Long> comUniCodes) {
        return insuComYieldSpreadService.listCreditSpreadByComUniCodes(comUniCodes);
    }

    @ApiOperation(value = "保险利差分析-刷新昨日行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard-yesterday")
    public void refreshCurveRatingShardYesterday() {
        RefreshYieldCurveParam param = new RefreshYieldCurveParam();
        param.setMvRefresh(true);
        param.setStartDate(Date.valueOf(LocalDate.now().minusDays(1)));
        param.setEndDate(Date.valueOf(LocalDate.now().minusDays(1)));
        insuBondYieldSpreadService.refreshMvInsuBondYieldSpreadRatingCurve(param);
    }

    @ApiOperation(value = "保险利差分析-刷新历史行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard-history")
    public void refreshCurveRatingShardHistory(@RequestParam("isTableRefresh") Boolean isTableRefresh) {
        insuBondYieldSpreadService.refreshMvInsuBondYieldSpreadRatingCurveHistory(isTableRefresh);
    }

    @ApiOperation(value = "保险利差分析-刷新行业利差评级分片曲线数据")
    @PostMapping("/refresh/curve/rating-shard")
    public void refreshCurveRatingShard(@RequestBody RefreshYieldCurveParam param) {
        ValidationUtil.valid(param);
        insuBondYieldSpreadService.refreshMvInsuBondYieldSpreadRatingCurve(param);
    }
}
