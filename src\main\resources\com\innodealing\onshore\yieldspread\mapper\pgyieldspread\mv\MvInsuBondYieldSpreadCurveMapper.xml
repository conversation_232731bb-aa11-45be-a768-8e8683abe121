<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.yieldspread.mapper.pgyieldspread.mv.MvInsuBondYieldSpreadCurveMapper">


    <sql id="ratingRouterQuerySql">
        SELECT max(insu_bond_yield_spread.id) AS id,
        MEDIAN((insu_bond_yield_spread.bond_credit_spread * 100000::numeric)::bigint) AS bond_credit_spread,
        MEDIAN((insu_bond_yield_spread.bond_excess_spread * 100000::numeric)::bigint) AS bond_excess_spread,
        MEDIAN((insu_bond_yield_spread.cb_yield * 100000::numeric)::bigint) AS cb_yield,
        AVG(((insu_bond_yield_spread.bond_credit_spread * (100000)::numeric))::bigint) AS avg_bond_credit_spread,
        AVG(((insu_bond_yield_spread.bond_excess_spread * (100000)::numeric))::bigint) AS avg_bond_excess_spread,
        AVG(((insu_bond_yield_spread.cb_yield * (100000)::numeric))::bigint) AS avg_cb_yield,
        count(insu_bond_yield_spread.bond_credit_spread) AS bond_credit_spread_count,
        count(insu_bond_yield_spread.bond_excess_spread) AS bond_excess_spread_count,
        count(insu_bond_yield_spread.cb_yield) AS cb_yield_count,
        insu_bond_yield_spread.spread_date,
        insu_bond_yield_spread.insurance_seniority_ranking,
        insu_bond_yield_spread.business_filter_nature,
        insu_bond_yield_spread.spread_remaining_tenor_tag,
        grouping(insu_bond_yield_spread.insurance_seniority_ranking) AS using_insurance_seniority_ranking,
        grouping(insu_bond_yield_spread.business_filter_nature) AS using_business_filter_nature,
        grouping(insu_bond_yield_spread.spread_remaining_tenor_tag) AS using_spread_remaining_tenor_tag
        FROM yield_spread.insu_bond_yield_spread
        where 1=1
        <if test="parameter.spreadDateRange == null">
            AND insu_bond_yield_spread.spread_date = (('now'::text)::date - 1)
        </if>
        <if test="parameter.spreadDateRange != null">
            AND insu_bond_yield_spread.spread_date BETWEEN '${parameter.spreadDateRange.startDate}' and
            '${parameter.spreadDateRange.endDate}'
        </if>
        <if test="parameter.impliedRatingMappings != null and parameter.impliedRatingMappings.size() > 0">
            AND insu_bond_yield_spread.bond_implied_rating_mapping in
            <foreach collection="parameter.impliedRatingMappings" item="item" open="(" close=")" separator=",">
                '${item}'
            </foreach>
        </if>
        GROUP BY insu_bond_yield_spread.spread_date, CUBE (
        insu_bond_yield_spread.insurance_seniority_ranking,insu_bond_yield_spread.business_filter_nature,
        insu_bond_yield_spread.spread_remaining_tenor_tag)
    </sql>

    <update id="createMvRatingRouter">
        create MATERIALIZED view yield_spread.${parameter.tableName}
        WITH (appendoptimized= true, orientation = column)
        as
        <include refid="ratingRouterQuerySql"></include>
    </update>

</mapper>