package com.innodealing.onshore.yieldspread.service.impl;

import com.innodealing.onshore.yieldspread.config.SyncConfiguration;
import com.innodealing.onshore.yieldspread.controller.MaterializedViewRefreshController;
import com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum;
import com.innodealing.onshore.yieldspread.executor.ParallelSyncExecutor;
import com.innodealing.onshore.yieldspread.service.BondYieldSpreadTraceService;
import com.innodealing.onshore.yieldspread.service.OptimizedBondYieldSpreadTraceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 优化的债券收益率利差追踪服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class OptimizedBondYieldSpreadTraceServiceImpl implements OptimizedBondYieldSpreadTraceService {
    
    @Resource
    private BondYieldSpreadTraceService bondYieldSpreadTraceService;
    
    @Resource
    private ParallelSyncExecutor parallelSyncExecutor;
    
    @Resource
    private MaterializedViewRefreshController materializedViewRefreshController;
    
    @Override
    public int syncBondYieldSpreadTraceWithConfig(SyncConfiguration config) {
        String validationResult = validateSyncConfiguration(config);
        if (!validationResult.isEmpty()) {
            throw new IllegalArgumentException("同步配置验证失败: " + validationResult);
        }
        
        log.info("开始使用配置同步利差追踪数据: {}", config);
        
        // 控制物化视图刷新
        if (config.isEnableMvRefreshControl()) {
            setupMaterializedViewRefreshControl();
        }
        
        try {
            if (config.isParallel()) {
                return executeParallelSync(config);
            } else {
                return executeSerialSync(config);
            }
        } finally {
            // 更新缓存
            if (config.isUpdateCache()) {
                try {
                    bondYieldSpreadTraceService.cacheLineChart();
                    log.info("缓存更新完成");
                } catch (Exception e) {
                    log.error("缓存更新失败", e);
                }
            }
        }
    }
    
    @Override
    public int syncBondYieldSpreadTraceByBondTypes(Date startDate, Date endDate, List<YieldPanoramaBondTypeEnum> bondTypes) {
        SyncConfiguration config = SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .bondTypes(bondTypes)
                .build();
        
        return syncBondYieldSpreadTraceWithConfig(config);
    }
    
    @Override
    public int syncBondYieldSpreadTraceByCurveCodes(Date startDate, Date endDate, List<Integer> curveCodes) {
        SyncConfiguration config = SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .curveCodes(curveCodes)
                .build();
        
        return syncBondYieldSpreadTraceWithConfig(config);
    }
    
    @Override
    public int syncBondYieldSpreadTraceParallel(Date startDate, Date endDate, int parallelism) {
        SyncConfiguration config = SyncConfiguration.builder()
                .startDate(startDate)
                .endDate(endDate)
                .parallel(true)
                .parallelism(parallelism)
                .build();
        
        return syncBondYieldSpreadTraceWithConfig(config);
    }
    
    @Override
    public int syncBondYieldSpreadTraceForSingleBondType(Date startDate, Date endDate, YieldPanoramaBondTypeEnum bondType) {
        return syncBondYieldSpreadTraceByBondTypes(startDate, endDate, Collections.singletonList(bondType));
    }

    @Override
    public int syncBondYieldSpreadTraceForSingleCurveCode(Date startDate, Date endDate, Integer curveCode) {
        return syncBondYieldSpreadTraceByCurveCodes(startDate, endDate, Collections.singletonList(curveCode));
    }
    
    @Override
    public int syncBondYieldSpreadTraceAbsOnly(SyncConfiguration config) {
        SyncConfiguration absConfig = SyncConfiguration.builder()
                .startDate(config.getStartDate())
                .endDate(config.getEndDate())
                .bondTypes(config.getBondTypes())
                .curveCodes(config.getCurveCodes())
                .parallel(config.isParallel())
                .parallelism(config.getParallelism())
                .syncAbs(true)
                .syncQuantile(false)
                .syncChange(false)
                .build();
        
        return syncBondYieldSpreadTraceWithConfig(absConfig);
    }
    
    @Override
    public int syncBondYieldSpreadTraceQuantileOnly(SyncConfiguration config) {
        SyncConfiguration quantileConfig = SyncConfiguration.builder()
                .startDate(config.getStartDate())
                .endDate(config.getEndDate())
                .bondTypes(config.getBondTypes())
                .curveCodes(config.getCurveCodes())
                .parallel(config.isParallel())
                .parallelism(config.getParallelism())
                .syncAbs(false)
                .syncQuantile(true)
                .syncChange(false)
                .build();
        
        return syncBondYieldSpreadTraceWithConfig(quantileConfig);
    }
    
    @Override
    public int syncBondYieldSpreadTraceChangeOnly(SyncConfiguration config) {
        SyncConfiguration changeConfig = SyncConfiguration.builder()
                .startDate(config.getStartDate())
                .endDate(config.getEndDate())
                .bondTypes(config.getBondTypes())
                .curveCodes(config.getCurveCodes())
                .parallel(config.isParallel())
                .parallelism(config.getParallelism())
                .syncAbs(false)
                .syncQuantile(false)
                .syncChange(true)
                .build();
        
        return syncBondYieldSpreadTraceWithConfig(changeConfig);
    }
    
    @Override
    public String getSyncProgress(SyncConfiguration config) {
        long totalDays = ChronoUnit.DAYS.between(
                config.getStartDate().toLocalDate(), 
                config.getEndDate().toLocalDate()
        ) + 1;
        
        int bondTypeCount = config.getBondTypes() != null ? config.getBondTypes().size() :
                YieldPanoramaBondTypeEnum.getTraceBondTypeEnums().size();
        
        return String.format("同步范围: %s - %s, 总天数: %d, 债券类型数: %d, 并行模式: %s", 
                config.getStartDate(), config.getEndDate(), totalDays, bondTypeCount, 
                config.isParallel() ? "是" : "否");
    }
    
    @Override
    public long estimateSyncTime(SyncConfiguration config) {
        long totalDays = ChronoUnit.DAYS.between(
                config.getStartDate().toLocalDate(), 
                config.getEndDate().toLocalDate()
        ) + 1;
        
        // 基础估算：每天每个债券类型约需要1秒
        int bondTypeCount = config.getBondTypes() != null ? config.getBondTypes().size() :
                YieldPanoramaBondTypeEnum.getTraceBondTypeEnums().size();
        
        long baseTime = totalDays * bondTypeCount;
        
        // 并行处理可以减少时间
        if (config.isParallel()) {
            baseTime = baseTime / config.getParallelism();
        }
        
        return Math.max(baseTime, 1); // 至少1秒
    }
    
    @Override
    public String validateSyncConfiguration(SyncConfiguration config) {
        if (config == null) {
            return "同步配置不能为空";
        }
        
        if (config.getStartDate() == null || config.getEndDate() == null) {
            return "开始日期和结束日期不能为空";
        }
        
        if (config.getStartDate().after(config.getEndDate())) {
            return "开始日期不能晚于结束日期";
        }
        
        if (config.getParallelism() <= 0) {
            return "并行度必须大于0";
        }
        
        if (config.getBatchSize() <= 0) {
            return "批处理大小必须大于0";
        }
        
        return ""; // 验证通过
    }
    
    /**
     * 执行并行同步
     */
    private int executeParallelSync(SyncConfiguration config) {
        return parallelSyncExecutor.executeParallelSync(config, date -> {
            return executeSingleDateSync(date, config);
        });
    }
    
    /**
     * 执行串行同步
     */
    private int executeSerialSync(SyncConfiguration config) {
        int totalEffectRows = 0;
        LocalDate start = config.getStartDate().toLocalDate();
        LocalDate end = config.getEndDate().toLocalDate();
        
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            Date issueDate = Date.valueOf(date);
            totalEffectRows += executeSingleDateSync(issueDate, config);
        }
        
        return totalEffectRows;
    }
    
    /**
     * 执行单日同步
     */
    private int executeSingleDateSync(Date issueDate, SyncConfiguration config) {
        // 这里需要调用原有的同步方法，但是需要根据配置进行过滤
        // 由于原有方法不支持过滤，这里暂时调用原有方法
        // 在实际实现中，需要重构原有的calBondYieldSpreadTraceAbs等方法来支持过滤
        return bondYieldSpreadTraceService.syncBondYieldSpreadTrace(issueDate, issueDate);
    }
    
    /**
     * 设置物化视图刷新控制
     */
    private void setupMaterializedViewRefreshControl() {
        // 禁用频繁的物化视图刷新，1小时内只允许刷新一次
        materializedViewRefreshController.disableMaterializedViewRefresh("bond_yield_spread_trace", 3600);
    }
}
