package com.innodealing.onshore.yieldspread.service.impl;

import com.google.common.collect.Lists;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.service.CacheOptionalService;
import com.innodealing.onshore.yieldspread.config.SyncConfiguration;
import com.innodealing.onshore.yieldspread.consts.YieldSpreadConst;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.*;
import com.innodealing.onshore.yieldspread.dao.pgyieldspread.view.PgBondYieldSpreadTraceQuantileViewDAO;
import com.innodealing.onshore.yieldspread.enums.*;
import com.innodealing.onshore.yieldspread.model.dto.BondYieldPanoramaTraceSpreadDateDTO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.*;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.view.PgBondYieldSpreadTraceQuantileViewDO;
import com.innodealing.onshore.yieldspread.processor.YieldSpreadTraceContext;
import com.innodealing.onshore.yieldspread.processor.YieldSpreadTraceProcessor;
import com.innodealing.onshore.yieldspread.service.MaterializedViewRefreshService;
import com.innodealing.onshore.yieldspread.service.BondYieldSpreadTraceService;
import com.innodealing.onshore.yieldspread.service.OptimizedBondYieldSpreadTraceService;
import com.innodealing.onshore.yieldspread.service.YieldSpreadCommonService;
import com.innodealing.onshore.yieldspread.service.internal.HolidayService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.yieldspread.consts.YieldSpreadConst.ONE_HUNDRED;
import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.*;
import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.GENERAL_BANK_BOND;
import static com.innodealing.onshore.yieldspread.enums.YieldPanoramaBondTypeEnum.SECURITIES_BOND;

/**
 * 优化的债券收益率利差追踪服务实现
 *
 * <AUTHOR>
 */
@Service
public class OptimizedBondYieldSpreadTraceServiceImpl implements OptimizedBondYieldSpreadTraceService, InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(OptimizedBondYieldSpreadTraceServiceImpl.class);
    @Resource
    private BondYieldSpreadTraceService bondYieldSpreadTraceService;
    @Resource
    private PgBondYieldSpreadTraceAbsDAO pgBondYieldSpreadTraceAbsDAO;
    @Resource
    private PgBondYieldPanoramaAbsDAO pgBondYieldPanoramaAbsDAO;
    @Resource
    private MaterializedViewRefreshService materializedViewRefreshService;
    @Resource
    private YieldSpreadCommonService yieldSpreadCommonService;
    @Resource
    private PgBondYieldSpreadTraceQuantileViewDAO pgBondYieldSpreadTraceQuantileViewDAO;
    @Resource
    private PgBondYieldSpreadTraceQuantileDAO pgBondYieldSpreadTraceQuantileDAO;
    @Resource
    private PgBondYieldPanoramaQuantileDAO pgBondYieldPanoramaQuantileDAO;
    @Resource
    private PgBondYieldPanoramaChangeDAO pgBondYieldPanoramaChangeDAO;
    @Resource
    private PgBondYieldSpreadTraceChangeDAO pgBondYieldSpreadTraceChangeDAO;
    @Resource
    private CacheOptionalService cacheOptionalService;

    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private HolidayService holidayService;
    private final Map<YieldPanoramaBondTypeEnum, List<YieldSpreadTraceProcessor>> processorMap = new ConcurrentHashMap<>();
    private static final String TRACE_SPREAD_DATE_KEY = "yield-spread:bond_yield_spread_trace:%s";
    private static final String NAMESPACE_KEY_PLACEHOLDER = "%d:%d:%d";
    private static final int TRACE_INTERVAL_CHANGE_SCALE = 2;
    private static final Integer THREAD_POOL_QUEUE_CAPACITY = 5000;
    private static final Integer THREAD_POOL_KEEP_ALIVE_SECONDS = 60;
    private static final ExecutorService TASK_SYNC_THREAD = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors(),
            THREAD_POOL_KEEP_ALIVE_SECONDS,
            TimeUnit.SECONDS, new LinkedBlockingQueue<>(THREAD_POOL_QUEUE_CAPACITY), r -> {
        String threadName = String.format("yield-spread-bond_yield_spread_trace%s", r.hashCode());
        return new Thread(r, threadName);
    }, new ThreadPoolExecutor.CallerRunsPolicy());


    @Override
    public int syncBondYieldSpreadTraceWithConfig(SyncConfiguration config) {
        String validationResult = validateSyncConfiguration(config);
        if (StringUtils.isNotBlank(validationResult)) {
            throw new IllegalArgumentException("同步配置验证失败: " + validationResult);
        }
        logger.info("开始使用配置同步利差追踪数据: {}", config);
        // 控制物化视图刷新
        if (config.isEnableMvRefreshControl()) {
            setupMaterializedViewRefreshControl();
        }
        try {
            AtomicInteger effectRows = new AtomicInteger();
            Date startDate = Objects.isNull(config.getStartDate()) ? Date.valueOf(LocalDate.now().minusDays(1)) : config.getStartDate();
            Date endDate = Objects.isNull(config.getEndDate()) ? Date.valueOf(LocalDate.now()) : config.getEndDate();
            List<Date> issueDates = new ArrayList<>();
            for (LocalDate localStartDate = startDate.toLocalDate(); !localStartDate.isAfter(endDate.toLocalDate()); localStartDate = localStartDate.plusDays(1)) {
                Date issueDate = Date.valueOf(localStartDate);
                issueDates.add(issueDate);
            }
            return config.isParallel()
                    ? executeParallelSync(issueDates,config)
                    : effectRows.addAndGet(issueDates.stream().map(v -> executeSerialSync(v, config)).mapToInt(Integer::intValue).sum());
        } finally {
            // 更新缓存
            if (config.isUpdateCache()) {
                try {
                    String now = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
                    String redisKey = String.format(TRACE_SPREAD_DATE_KEY, now);
                    BondYieldPanoramaTraceSpreadDateDTO spreadDateDTO = new BondYieldPanoramaTraceSpreadDateDTO();
                    pgBondYieldSpreadTraceAbsDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setAbsSpreadDate);
                    pgBondYieldSpreadTraceQuantileDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setQuantileSpreadDate);
                    pgBondYieldSpreadTraceChangeDAO.getMaxSpreadDate().ifPresent(spreadDateDTO::setChangeSpreadDate);
                    cacheOptionalService.getStringRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(spreadDateDTO), YieldSpreadConst.TWO_DAYS, TimeUnit.DAYS);
                    bondYieldSpreadTraceService.cacheLineChart();
                    logger.info("缓存更新完成");
                } catch (Exception e) {
                    logger.error("缓存更新失败", e);
                }
            }
        }
    }

    private int executeParallelSync(List<Date> issueDates, SyncConfiguration config) {
        SwThreadPoolWorker<Integer> sw = SwThreadPoolWorker.of(TASK_SYNC_THREAD);
        issueDates.forEach(issueDate -> sw.addWork(() -> executeSerialSync(issueDate, config)));
        List<Integer> rows = sw.doWorks();
        return rows.stream().mapToInt(Integer::intValue).sum();
    }


    public SyncConfiguration beforeHandleParams(SyncConfiguration config) {
        SyncConfiguration syncConfiguration = BeanCopyUtils.copyProperties(config, SyncConfiguration.class);
        // 这里先处理下curveCode
        if (CollectionUtils.isNotEmpty(syncConfiguration.getBondTypes()) && CollectionUtils.isEmpty(syncConfiguration.getCurveCodes())) {
            List<Integer> collect = syncConfiguration.getBondTypes().stream().map(YieldSpreadCurveCodeEnum::getCurveCodesEnumByBondType)
                    .flatMap(List::stream)
                    .map(YieldSpreadCurveCodeEnum::getValue)
                    .distinct().collect(Collectors.toList());
            config.setCurveCodes(collect);
        }
        if (CollectionUtils.isNotEmpty(syncConfiguration.getCurveCodes()) && CollectionUtils.isEmpty(syncConfiguration.getBondTypes())) {
            List<YieldPanoramaBondTypeEnum> yieldPanoramaBondTypeEnums = YieldSpreadCurveCodeEnum.toEnums(syncConfiguration.getCurveCodes()).stream()
                    .map(YieldSpreadCurveCodeEnum::getBondType)
                    .distinct()
                    .collect(Collectors.toList());
            config.setBondTypes(yieldPanoramaBondTypeEnums);
        }
        // 如果两者都不为空。互相补全参数

        return syncBondYieldSpreadTraceWithConfig(config);
    }


    private String validateSyncConfiguration(SyncConfiguration config) {
        if (config == null) {
            return "同步配置不能为空";
        }
        if (config.getStartDate() == null || config.getEndDate() == null) {
            return "开始日期和结束日期不能为空";
        }
        if (config.getStartDate().after(config.getEndDate())) {
            return "开始日期不能晚于结束日期";
        }
        return "";
    }

    /**
     * 执行串行同步
     * BondYieldSpreadTraceServiceImpl
     */
    private int executeSerialSync(Date issueDate, SyncConfiguration config) {
        AtomicInteger effectRows = new AtomicInteger();
        StopWatch stopWatch = new StopWatch();
        if (config.isSyncAbs()) {
            stopWatch.start("executeParallelSync#calBondYieldSpreadTraceAbs");
            effectRows.addAndGet(this.calBondYieldSpreadTraceAbs(issueDate, config));
            stopWatch.stop();
            logger.info("executeParallelSync#calBondYieldSpreadTraceAbs,issueDate:{},cost:{}ms", issueDate, stopWatch.getLastTaskTimeMillis());
        }
        if (config.isSyncQuantile()) {
            stopWatch.start("executeParallelSync#calBondYieldSpreadTraceQuantile");
            effectRows.addAndGet(this.calBondYieldSpreadTraceQuantile(issueDate, config));
            stopWatch.stop();
            logger.info("executeParallelSync#calBondYieldSpreadTraceQuantile,issueDate:{},cost:{}ms", issueDate, stopWatch.getLastTaskTimeMillis());
        }
        if (config.isSyncChange()) {
            stopWatch.start("executeParallelSync#calBondYieldSpreadTraceChange");
            effectRows.addAndGet(this.calBondYieldSpreadTraceChange(issueDate, config));
            stopWatch.stop();
            logger.info("executeParallelSync#calBondYieldSpreadTraceChange,issueDate:{},cost:{}ms", issueDate, stopWatch.getLastTaskTimeMillis());
            logger.info("executeSerialSync end...issueDate:{}", issueDate);
        }
        return effectRows.get();
    }


    private int calBondYieldSpreadTraceAbs(Date issueDate, final SyncConfiguration config) {
        // 查询基础数据，为后续做准备
        Optional<YieldSpreadTraceContext> parameter = this.prepareParameter(issueDate, config);
        if (!parameter.isPresent()) {
            return 0;
        }
        List<PgBondYieldSpreadTraceAbsDO> absBondYieldSpreadTraceList = Lists.newArrayList();
        for (YieldPanoramaBondTypeEnum bondType : config.getBondTypes()) {
            YieldSpreadTraceContext context = BeanCopyUtils.copyProperties(parameter.get(), YieldSpreadTraceContext.class);
            context.setBondTypeEnum(bondType);
            List<PgBondYieldPanoramaAbsDO> matchAbs = context.getAbsBondYieldPanoramas().stream()
                    .filter(abs -> bondType.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
            context.setAbsBondYieldPanoramas(matchAbs);
            List<YieldSpreadTraceProcessor> processors = processorMap.getOrDefault(bondType, Collections.emptyList());
            processors.forEach(processor -> absBondYieldSpreadTraceList.addAll(processor.processYieldSpreadTraceYtm(context)));
        }
        return pgBondYieldSpreadTraceAbsDAO.saveBondYieldSpreadTraceAbsList(issueDate, absBondYieldSpreadTraceList);
    }

    private Optional<YieldSpreadTraceContext> prepareParameter(Date issueDate, final SyncConfiguration config) {
        List<Integer> bondTypeList = config.getBondTypes().stream().map(YieldPanoramaBondTypeEnum::getValue).collect(Collectors.toList());
        // 查询基础数据,包含国债
        List<PgBondYieldPanoramaAbsDO> bondYieldPanoramaAbsList = pgBondYieldPanoramaAbsDAO.listBondYieldPanoramaQuantiles(bondTypeList, issueDate);
        if (CollectionUtils.isEmpty(bondYieldPanoramaAbsList)) {
            return Optional.empty();
        }
        PgBondYieldPanoramaAbsDO chinaBond = bondYieldPanoramaAbsList.stream()
                .filter(abs -> CHINA_BOND.getCurveCodes().contains(abs.getCurveCode())).findFirst().orElse(null);
        PgBondYieldPanoramaAbsDO chinaBondKai = bondYieldPanoramaAbsList.stream()
                .filter(abs -> CD_BOND.getCurveCodes().contains(abs.getCurveCode())).findFirst().orElse(null);
        List<PgBondYieldPanoramaAbsDO> chinaBondMidYieldPanoramas = bondYieldPanoramaAbsList.stream()
                .filter(abs -> MEDIUM_AND_SHORT_TERMS_NOTE.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
        List<PgBondYieldPanoramaAbsDO> generalBankBondYieldPanoramas = bondYieldPanoramaAbsList.stream()
                .filter(abs -> GENERAL_BANK_BOND.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
        List<PgBondYieldPanoramaAbsDO> securitiesBondYieldPanoramas = bondYieldPanoramaAbsList.stream()
                .filter(abs -> SECURITIES_BOND.getCurveCodes().contains(abs.getCurveCode())).collect(Collectors.toList());
        YieldSpreadTraceContext context = new YieldSpreadTraceContext();
        context.setIssueDate(issueDate);
        context.setChinaBondYieldPanorama(chinaBond);
        context.setChinaBondKaiYieldPanorama(chinaBondKai);
        context.setChinaBondMidYieldPanoramas(chinaBondMidYieldPanoramas);
        context.setGeneralBankBondYieldPanoramas(generalBankBondYieldPanoramas);
        context.setSecuritiesBondYieldPanoramas(securitiesBondYieldPanoramas);
        context.setAbsBondYieldPanoramas(bondYieldPanoramaAbsList);
        return Optional.of(context);
    }

    private int calBondYieldSpreadTraceQuantile(Date issueDate, final SyncConfiguration config) {
        List<PgBondYieldSpreadTraceQuantileDO> quantileList = Lists.newArrayList();
        // 同一个曲线同一个分位类型不会重复
        List<PgBondYieldPanoramaQuantileDO> bondYieldPanoramaQuantiles =
                pgBondYieldPanoramaQuantileDAO.listBondYieldPanoramaQuantiles(new ArrayList<>(config.getCurveCodes()), issueDate);
        Map<Integer, List<PgBondYieldPanoramaQuantileDO>> curveMap = bondYieldPanoramaQuantiles.stream()
                .collect(Collectors.groupingBy(PgBondYieldPanoramaQuantileDO::getCurveCode));
        for (YieldPanoramaBondTypeEnum bondType : YieldPanoramaBondTypeEnum.getTraceBondTypeEnums()) {
            Set<Integer> curCurveCodes = bondType.getCurveCodes();
            if (CollectionUtils.isEmpty(curCurveCodes)) {
                continue;
            }
            for (Integer curCurveCode : curCurveCodes) {
                List<PgBondYieldPanoramaQuantileDO> bondYieldPanoramaQuantileList = curveMap.get(curCurveCode);
                if (CollectionUtils.isEmpty(bondYieldPanoramaQuantileList)) {
                    continue;
                }
                List<PgBondYieldSpreadTraceQuantileDO> traceQuantileDOList = bondYieldPanoramaQuantileList.stream().map(bondYieldPanoramaQuantile -> {
                    PgBondYieldSpreadTraceQuantileDO pgBondYieldSpreadTraceQuantileDO
                            = BeanCopyUtils.copyProperties(bondYieldPanoramaQuantile, PgBondYieldSpreadTraceQuantileDO.class);
                    pgBondYieldSpreadTraceQuantileDO.setBondType(bondType.getValue());
                    pgBondYieldSpreadTraceQuantileDO.setChartType(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue());
                    pgBondYieldSpreadTraceQuantileDO.setCurveCode(curCurveCode);
                    pgBondYieldSpreadTraceQuantileDO.setIssueDate(issueDate);
                    pgBondYieldSpreadTraceQuantileDO.setDeleted(Deleted.NO_DELETED.getValue());
                    pgBondYieldSpreadTraceQuantileDO.setStartDate(bondYieldPanoramaQuantile.getStartDate());
                    return pgBondYieldSpreadTraceQuantileDO;
                }).collect(Collectors.toList());

                quantileList.addAll(traceQuantileDOList);
            }
        }

        for (SpreadQuantileTypeEnum quantileTypeEnum : SpreadQuantileTypeEnum.values()) {
            Date startDate = yieldSpreadCommonService.getQuantileStartDate(issueDate, quantileTypeEnum);
            pgBondYieldSpreadTraceQuantileViewDAO.createTraceQuantileView(startDate, issueDate);
            List<PgBondYieldSpreadTraceQuantileViewDO> bondYieldSpreadTraceQuantileViewList = pgBondYieldSpreadTraceQuantileViewDAO.listBondYieldSpreadTraceQuantiles(issueDate);
            for (PgBondYieldSpreadTraceQuantileViewDO pgBondYieldPanoramaQuantileViewDO : bondYieldSpreadTraceQuantileViewList) {
                PgBondYieldSpreadTraceQuantileDO bondYieldSpreadTraceQuantile =
                        BeanCopyUtils.copyProperties(pgBondYieldPanoramaQuantileViewDO, PgBondYieldSpreadTraceQuantileDO.class);
                bondYieldSpreadTraceQuantile.setStartDate(startDate);
                bondYieldSpreadTraceQuantile.setQuantileType(quantileTypeEnum.getValue());
                bondYieldSpreadTraceQuantile.setDeleted(Deleted.NO_DELETED.getValue());
                quantileList.add(bondYieldSpreadTraceQuantile);
            }
        }
        return pgBondYieldSpreadTraceQuantileDAO.saveBondYieldSpreadTraceQuantileList(issueDate, quantileList);
    }

    private int calBondYieldSpreadTraceChange(Date issueDate, final SyncConfiguration config) {
        List<PgBondYieldSpreadTraceChangeDO> changeList = Lists.newArrayList();
        // 同一个曲线同一个变动类型不会重复
        List<PgBondYieldPanoramaChangeDO> pgBondYieldPanoramaChanges =
                pgBondYieldPanoramaChangeDAO.listBondYieldPanoramaChanges(new ArrayList<>(config.getCurveCodes()), issueDate);

        Map<Integer, List<PgBondYieldPanoramaChangeDO>> curveMap = pgBondYieldPanoramaChanges.stream()
                .collect(Collectors.groupingBy(PgBondYieldPanoramaChangeDO::getCurveCode));
        for (YieldPanoramaBondTypeEnum bondType : YieldPanoramaBondTypeEnum.getTraceBondTypeEnums()) {
            Set<Integer> curCurveCodes = bondType.getCurveCodes();
            if (CollectionUtils.isEmpty(curCurveCodes)) {
                continue;
            }
            for (Integer curCurveCode : curCurveCodes) {
                List<PgBondYieldPanoramaChangeDO> bondYieldPanoramaChangeDOList = curveMap.get(curCurveCode);
                if (CollectionUtils.isEmpty(bondYieldPanoramaChangeDOList)) {
                    continue;
                }
                List<PgBondYieldSpreadTraceChangeDO> traceChangeDOList = bondYieldPanoramaChangeDOList.stream().map(bondYieldPanoramaChange -> {
                    PgBondYieldSpreadTraceChangeDO pgBondYieldSpreadTraceChangeDO = BeanCopyUtils.copyProperties(bondYieldPanoramaChange, PgBondYieldSpreadTraceChangeDO.class);
                    pgBondYieldSpreadTraceChangeDO.setBondType(bondType.getValue());
                    pgBondYieldSpreadTraceChangeDO.setChartType(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue());
                    pgBondYieldSpreadTraceChangeDO.setCurveCode(curCurveCode);
                    pgBondYieldSpreadTraceChangeDO.setIssueDate(issueDate);
                    return pgBondYieldSpreadTraceChangeDO;
                }).collect(Collectors.toList());

                changeList.addAll(traceChangeDOList);
            }
        }

        List<Integer> chartTypes = Arrays.stream(YieldSpreadChartTypeEnum.values()).map(YieldSpreadChartTypeEnum::getValue)
                .filter(chart -> YieldSpreadChartTypeEnum.MATU_SPREAD.getValue() != chart).collect(Collectors.toList());
        // 区间变动不需要国债数据
        List<PgBondYieldSpreadTraceAbsDO> currentBondYieldSpreadTraceList =
                pgBondYieldSpreadTraceAbsDAO.listBondYieldSpreadTraces(issueDate, chartTypes, null);
        Map<String, PgBondYieldSpreadTraceAbsDO> currentTraceAbsMap =
                currentBondYieldSpreadTraceList.stream().collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
        for (BondYieldIntervalChangeTypeEnum changeTypeEnum : BondYieldIntervalChangeTypeEnum.values()) {
            // 获取最近一天的工作日，包含自己
            Date startDate = this.getChangeStartDate(issueDate, changeTypeEnum);
            List<PgBondYieldSpreadTraceAbsDO> beforeBondYieldSpreadTraceList = pgBondYieldSpreadTraceAbsDAO.listBondYieldSpreadTraces(startDate, chartTypes, null);
            if (CollectionUtils.isEmpty(beforeBondYieldSpreadTraceList)) {
                continue;
            }
            Map<String, PgBondYieldSpreadTraceAbsDO> beforeTraceAbsMap =
                    beforeBondYieldSpreadTraceList.stream().collect(Collectors.toMap(this::getKey, Function.identity(), (o, v) -> o));
            for (Map.Entry<String, PgBondYieldSpreadTraceAbsDO> curentEntry : currentTraceAbsMap.entrySet()) {
                PgBondYieldSpreadTraceAbsDO beforeTraceAbs = beforeTraceAbsMap.get(curentEntry.getKey());
                PgBondYieldSpreadTraceAbsDO endTraceAbs = curentEntry.getValue();
                Optional<PgBondYieldSpreadTraceChangeDO> change =
                        buildBondYieldSpreadTraceChange(endTraceAbs, beforeTraceAbs, changeTypeEnum.getValue(), startDate,
                                getSafeSubtractFunction(endTraceAbs.getChartType()));
                change.ifPresent(changeList::add);
            }
        }
        return pgBondYieldSpreadTraceChangeDAO.saveBondYieldSpreadTraceChangeList(issueDate, changeList);
    }

    /**
     * 设置物化视图刷新控制
     */
    private void setupMaterializedViewRefreshControl() {
        // 禁用频繁的物化视图刷新，1小时内只允许刷新一次
        materializedViewRefreshService.disableMaterializedViewRefresh("bond_yield_spread_trace", 3600);
    }

    private Date getChangeStartDate(Date issueDate, BondYieldIntervalChangeTypeEnum changeType) {
        LocalDate startDate = changeType.getIntervalStartDate(issueDate.toLocalDate());
        // 获取最近一天的工作日，包含自己
        Date workDate = holidayService.latestWorkDay(Date.valueOf(startDate), 0);
        Date minStartDate = changeType.getIntervalMinStartDate(startDate);
        return workDate.before(minStartDate) ? minStartDate : workDate;
    }

    private String getKey(PgBondYieldSpreadTraceAbsDO abs) {
        return String.format(NAMESPACE_KEY_PLACEHOLDER, abs.getBondType(), abs.getChartType(), abs.getCurveCode());
    }

    private BiFunction<BigDecimal, BigDecimal, Optional<BigDecimal>> getSafeSubtractFunction(Integer chartType) {
        if (Integer.valueOf(YieldSpreadChartTypeEnum.MATU_SPREAD.getValue()).equals(chartType)) {
            return this::safeSubtractToBp;
        } else {
            return this::safeSubtract;
        }
    }

    private Optional<BigDecimal> safeSubtract(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return Optional.empty();
        }
        return Optional.of(value1.subtract(value2).setScale(TRACE_INTERVAL_CHANGE_SCALE, RoundingMode.HALF_UP));
    }

    private Optional<BigDecimal> safeSubtractToBp(BigDecimal value1, BigDecimal value2) {
        if (Objects.isNull(value1) || Objects.isNull(value2)) {
            return Optional.empty();
        }
        return Optional.of(value1.subtract(value2).multiply(ONE_HUNDRED).setScale(TRACE_INTERVAL_CHANGE_SCALE, RoundingMode.HALF_UP));
    }

    private Optional<PgBondYieldSpreadTraceChangeDO> buildBondYieldSpreadTraceChange(PgBondYieldSpreadTraceAbsDO currentAbsTrace, PgBondYieldSpreadTraceAbsDO beforeAbsTrace,
                                                                                     Integer changeType, Date startDate,
                                                                                     BiFunction<BigDecimal, BigDecimal, Optional<BigDecimal>> safeSubtraceFunction) {
        if (Objects.isNull(currentAbsTrace) || Objects.isNull(beforeAbsTrace)) {
            return Optional.empty();
        }
        PgBondYieldSpreadTraceChangeDO pgBondYieldTraceChangeDO = new PgBondYieldSpreadTraceChangeDO();
        pgBondYieldTraceChangeDO.setBondType(currentAbsTrace.getBondType());
        Integer chartType = currentAbsTrace.getChartType();
        pgBondYieldTraceChangeDO.setChartType(chartType);
        pgBondYieldTraceChangeDO.setCurveCode(currentAbsTrace.getCurveCode());
        pgBondYieldTraceChangeDO.setYtm1M(safeSubtraceFunction.apply(currentAbsTrace.getYtm1M(), beforeAbsTrace.getYtm1M()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm3M(safeSubtraceFunction.apply(currentAbsTrace.getYtm3M(), beforeAbsTrace.getYtm3M()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm6M(safeSubtraceFunction.apply(currentAbsTrace.getYtm6M(), beforeAbsTrace.getYtm6M()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm9M(safeSubtraceFunction.apply(currentAbsTrace.getYtm9M(), beforeAbsTrace.getYtm9M()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm1Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm1Y(), beforeAbsTrace.getYtm1Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm2Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm2Y(), beforeAbsTrace.getYtm2Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm3Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm3Y(), beforeAbsTrace.getYtm3Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm5Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm5Y(), beforeAbsTrace.getYtm5Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm7Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm7Y(), beforeAbsTrace.getYtm7Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm10Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm10Y(), beforeAbsTrace.getYtm10Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm15Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm15Y(), beforeAbsTrace.getYtm15Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm20Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm20Y(), beforeAbsTrace.getYtm20Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm30Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm30Y(), beforeAbsTrace.getYtm30Y()).orElse(null));
        pgBondYieldTraceChangeDO.setYtm50Y(safeSubtraceFunction.apply(currentAbsTrace.getYtm50Y(), beforeAbsTrace.getYtm50Y()).orElse(null));

        pgBondYieldTraceChangeDO.setIssueDate(currentAbsTrace.getIssueDate());
        pgBondYieldTraceChangeDO.setDeleted(Deleted.NO_DELETED.getValue());
        pgBondYieldTraceChangeDO.setChangeType(changeType);
        pgBondYieldTraceChangeDO.setStartDate(startDate);
        return Optional.of(pgBondYieldTraceChangeDO);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Collection<YieldSpreadTraceProcessor> processors = applicationContext.getBeansOfType(YieldSpreadTraceProcessor.class).values();
        for (YieldPanoramaBondTypeEnum bondTypeEnum : values()) {
            processorMap.put(bondTypeEnum, processors.stream().filter(processor -> processor.supportBondType(bondTypeEnum)).collect(Collectors.toList()));
        }
    }
}
