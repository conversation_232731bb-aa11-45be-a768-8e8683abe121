package com.innodealing.onshore.yieldspread.config;


import com.innodealing.onshore.yieldspread.helper.RatingCombinationHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


/**
 * 刷新历史数据上下文填充
 * <AUTHOR>
 */
@Aspect
@Order(1)
@Component
public class TableRefreshAspect {
    /**
     * 历史数据切点
     */
    @Pointcut("execution(* com.innodealing.onshore.yieldspread.service.*BondYieldSpreadService.*BondYieldSpreadRatingCurveHistory(..))")
    public void pointcut() {
        // the pointcut expression
    }

    /**
     * 环绕通知
     *
     * @param point 切点
     * @return object
     * @throws Throwable 异常
     */
    @Around("pointcut()")
    public Object doSharding(ProceedingJoinPoint point) throws Throwable {
        try {
            RatingCombinationHelper.init();
            return point.proceed();
        } finally {
            RatingCombinationHelper.clear();
        }
    }
}
