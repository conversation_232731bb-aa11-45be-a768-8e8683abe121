package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgSecuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgSecuBondYieldSpreadGroupDO;


/**
 * pg证券债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface PgSecuBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<PgSecuBondYieldSpreadDO,
        PgSecuBondYieldSpreadGroupDO> {

}
