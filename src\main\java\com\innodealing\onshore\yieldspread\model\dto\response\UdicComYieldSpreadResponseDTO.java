package com.innodealing.onshore.yieldspread.model.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayStringJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number2ScaleJsonSerializer;
import com.innodealing.onshore.bondmetadata.json.serializer.Number4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.Objects;

/**
 * 城投主体利差响应DTO
 *
 * <AUTHOR>
 */
public class UdicComYieldSpreadResponseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("利差日期")
    private Date spreadDate;
    @ApiModelProperty("公司统一编码")
    private Long comUniCode;
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    @ApiModelProperty("发行人")
    private String comUniName;
    @ApiModelProperty("主体评级映射")
    private Integer comExtRatingMapping;
    @ApiModelProperty("主体评级文本")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String comExtRating;
    @ApiModelProperty("所属地区")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String areaName;
    @ApiModelProperty("实际控制人")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String actualControllerFullName;
    @ApiModelProperty("行政级别(数值类型)")
    private Integer administrativeRegion;
    @ApiModelProperty("行政级别文本(前端展示)")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String administrativeRegionName;
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    @ApiModelProperty("债券余额(亿)")
    private BigDecimal bondBalance;
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    @ApiModelProperty("有息债务(亿)")
    private BigDecimal hideDebt;
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    @ApiModelProperty("总资产(亿)")
    private BigDecimal totalAssets;
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    @ApiModelProperty("资产负债率(%)")
    private BigDecimal assetLiabilityRatio;
    @ApiModelProperty("信用利差(全部债券);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comCreditSpread;
    @ApiModelProperty("信用利差 近三月变动(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadChange3M;
    @ApiModelProperty("信用利差 近六月变动(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadChange6M;

    @ApiModelProperty("信用利差3年历史分位")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadQuantile3Y;

    @ApiModelProperty("信用利差5年历史分位")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal creditSpreadQuantile5Y;

    @ApiModelProperty("超额利差(全部债券);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comExcessSpread;
    @ApiModelProperty("超额利差 近三月变动(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadChange3M;
    @ApiModelProperty("超额利差 近六月变动(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadChange6M;

    @ApiModelProperty("超额利差3年历史分位")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadQuantile3Y;

    @ApiModelProperty("超额利差5年历史分位")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal excessSpreadQuantile5Y;
    @ApiModelProperty("信用利差(公募);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comPublicCreditSpread;
    @ApiModelProperty("超额利差(公募);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comPublicExcessSpread;
    @ApiModelProperty("信用利差(私募);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comPrivateCreditSpread;
    @ApiModelProperty("超额利差(私募);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comPrivateExcessSpread;
    @ApiModelProperty("信用利差(永续);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comPerpetualCreditSpread;
    @ApiModelProperty("超额利差(永续);单位(BP)")
    @JsonSerialize(using = Number2ScaleJsonSerializer.class, nullsUsing = Number2ScaleJsonSerializer.class)
    private BigDecimal comPerpetualExcessSpread;
    @ApiModelProperty("估值收益率(全部债券);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comCbYield;
    @ApiModelProperty("估值收益率(公募);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comPublicCbYield;
    @ApiModelProperty("估值收益率(私募);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comPrivateCbYield;
    @ApiModelProperty("估值收益率(永续);单位(%)")
    @JsonSerialize(using = Number4ScaleJsonSerializer.class, nullsUsing = Number4ScaleJsonSerializer.class)
    private BigDecimal comPerpetualCbYield;
    @ApiModelProperty("曲线id")
    private Long curveId;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getComUniName() {
        return comUniName;
    }

    public void setComUniName(String comUniName) {
        this.comUniName = comUniName;
    }

    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public String getComExtRating() {
        return comExtRating;
    }

    public void setComExtRating(String comExtRating) {
        this.comExtRating = comExtRating;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getActualControllerFullName() {
        return actualControllerFullName;
    }

    public void setActualControllerFullName(String actualControllerFullName) {
        this.actualControllerFullName = actualControllerFullName;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public BigDecimal getHideDebt() {
        return hideDebt;
    }

    public void setHideDebt(BigDecimal hideDebt) {
        this.hideDebt = hideDebt;
    }

    public BigDecimal getTotalAssets() {
        return totalAssets;
    }

    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }

    public BigDecimal getAssetLiabilityRatio() {
        return assetLiabilityRatio;
    }

    public void setAssetLiabilityRatio(BigDecimal assetLiabilityRatio) {
        this.assetLiabilityRatio = assetLiabilityRatio;
    }

    public BigDecimal getComCreditSpread() {
        return comCreditSpread;
    }

    public void setComCreditSpread(BigDecimal comCreditSpread) {
        this.comCreditSpread = comCreditSpread;
    }

    public BigDecimal getCreditSpreadChange3M() {
        return creditSpreadChange3M;
    }

    public void setCreditSpreadChange3M(BigDecimal creditSpreadChange3M) {
        this.creditSpreadChange3M = creditSpreadChange3M;
    }

    public BigDecimal getCreditSpreadChange6M() {
        return creditSpreadChange6M;
    }

    public void setCreditSpreadChange6M(BigDecimal creditSpreadChange6M) {
        this.creditSpreadChange6M = creditSpreadChange6M;
    }

    public BigDecimal getComExcessSpread() {
        return comExcessSpread;
    }

    public void setComExcessSpread(BigDecimal comExcessSpread) {
        this.comExcessSpread = comExcessSpread;
    }

    public BigDecimal getExcessSpreadChange3M() {
        return excessSpreadChange3M;
    }

    public void setExcessSpreadChange3M(BigDecimal excessSpreadChange3M) {
        this.excessSpreadChange3M = excessSpreadChange3M;
    }

    public BigDecimal getExcessSpreadChange6M() {
        return excessSpreadChange6M;
    }

    public void setExcessSpreadChange6M(BigDecimal excessSpreadChange6M) {
        this.excessSpreadChange6M = excessSpreadChange6M;
    }

    public BigDecimal getComPublicCreditSpread() {
        return comPublicCreditSpread;
    }

    public void setComPublicCreditSpread(BigDecimal comPublicCreditSpread) {
        this.comPublicCreditSpread = comPublicCreditSpread;
    }

    public BigDecimal getComPublicExcessSpread() {
        return comPublicExcessSpread;
    }

    public void setComPublicExcessSpread(BigDecimal comPublicExcessSpread) {
        this.comPublicExcessSpread = comPublicExcessSpread;
    }

    public BigDecimal getComPrivateCreditSpread() {
        return comPrivateCreditSpread;
    }

    public void setComPrivateCreditSpread(BigDecimal comPrivateCreditSpread) {
        this.comPrivateCreditSpread = comPrivateCreditSpread;
    }

    public BigDecimal getComPrivateExcessSpread() {
        return comPrivateExcessSpread;
    }

    public void setComPrivateExcessSpread(BigDecimal comPrivateExcessSpread) {
        this.comPrivateExcessSpread = comPrivateExcessSpread;
    }

    public BigDecimal getComPerpetualCreditSpread() {
        return comPerpetualCreditSpread;
    }

    public void setComPerpetualCreditSpread(BigDecimal comPerpetualCreditSpread) {
        this.comPerpetualCreditSpread = comPerpetualCreditSpread;
    }

    public BigDecimal getComPerpetualExcessSpread() {
        return comPerpetualExcessSpread;
    }

    public void setComPerpetualExcessSpread(BigDecimal comPerpetualExcessSpread) {
        this.comPerpetualExcessSpread = comPerpetualExcessSpread;
    }

    public Integer getAdministrativeRegion() {
        return administrativeRegion;
    }

    public void setAdministrativeRegion(Integer administrativeRegion) {
        this.administrativeRegion = administrativeRegion;
    }

    public String getAdministrativeRegionName() {
        return administrativeRegionName;
    }

    public void setAdministrativeRegionName(String administrativeRegionName) {
        this.administrativeRegionName = administrativeRegionName;
    }

    public Date getSpreadDate() {
        return Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public void setSpreadDate(Date spreadDate) {
        this.spreadDate = Objects.isNull(spreadDate) ? null : new Date(spreadDate.getTime());
    }

    public BigDecimal getComCbYield() {
        return comCbYield;
    }

    public void setComCbYield(BigDecimal comCbYield) {
        this.comCbYield = comCbYield;
    }

    public BigDecimal getComPublicCbYield() {
        return comPublicCbYield;
    }

    public void setComPublicCbYield(BigDecimal comPublicCbYield) {
        this.comPublicCbYield = comPublicCbYield;
    }

    public BigDecimal getComPrivateCbYield() {
        return comPrivateCbYield;
    }

    public void setComPrivateCbYield(BigDecimal comPrivateCbYield) {
        this.comPrivateCbYield = comPrivateCbYield;
    }

    public BigDecimal getComPerpetualCbYield() {
        return comPerpetualCbYield;
    }

    public void setComPerpetualCbYield(BigDecimal comPerpetualCbYield) {
        this.comPerpetualCbYield = comPerpetualCbYield;
    }

    public Long getCurveId() {
        return curveId;
    }

    public void setCurveId(Long curveId) {
        this.curveId = curveId;
    }

    public BigDecimal getCreditSpreadQuantile3Y() {
        return creditSpreadQuantile3Y;
    }

    public void setCreditSpreadQuantile3Y(BigDecimal creditSpreadQuantile3Y) {
        this.creditSpreadQuantile3Y = creditSpreadQuantile3Y;
    }

    public BigDecimal getCreditSpreadQuantile5Y() {
        return creditSpreadQuantile5Y;
    }

    public void setCreditSpreadQuantile5Y(BigDecimal creditSpreadQuantile5Y) {
        this.creditSpreadQuantile5Y = creditSpreadQuantile5Y;
    }

    public BigDecimal getExcessSpreadQuantile3Y() {
        return excessSpreadQuantile3Y;
    }

    public void setExcessSpreadQuantile3Y(BigDecimal excessSpreadQuantile3Y) {
        this.excessSpreadQuantile3Y = excessSpreadQuantile3Y;
    }

    public BigDecimal getExcessSpreadQuantile5Y() {
        return excessSpreadQuantile5Y;
    }

    public void setExcessSpreadQuantile5Y(BigDecimal excessSpreadQuantile5Y) {
        this.excessSpreadQuantile5Y = excessSpreadQuantile5Y;
    }
}
