package com.innodealing.onshore.yieldspread.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondfinance.response.ComFinanceSheetResponseDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 财报服务
 *
 * <AUTHOR>
 **/
@FeignClient(name = "bondFinanceService", url = "${bond.finance.api.url}", path = "/internal")
public interface BondFinanceService {
    /**
     * 根据发行人和日期获取相关行业最新年报的财报数据
     *
     * @param reportMaxDate 报表开始日期
     * @param comUniCodes   发行人唯一编码
     * @return 财报数据
     */
    @PostMapping("com/finance/com/finance/latest/year/report")
    List<ComFinanceSheetResponseDTO> listComFinanceLatestYearReports(@RequestParam Date reportMaxDate,
                                                                     @RequestBody Long[] comUniCodes);

    /**
     * 根据发行人和日期获取相关行业最新年报的财报数据
     *
     * @param reportMaxDate 报表开始日期
     * @param comUniCodes   发行人唯一编码
     * @return key 发行人唯一编码,value 财报数据
     */
    default Map<Long, ComFinanceSheetResponseDTO> getComFinanceLatestYearReportMap(Date reportMaxDate, Set<Long> comUniCodes) {
        if (CollectionUtils.isEmpty(comUniCodes)) {
            return Collections.emptyMap();
        }
        return listComFinanceLatestYearReports(reportMaxDate, comUniCodes.stream().toArray(Long[]::new)).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ComFinanceSheetResponseDTO::getComUniCode, Function.identity(), (x1, x2) -> x2));
    }

}
