package com.innodealing.onshore.yieldspread.model.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 银行主体利差
 *
 * <AUTHOR>
 */
public class BankComYieldSpreadBO extends BaseFinanceComYieldSpreadBO {

    @ApiModelProperty("银行类型 2:国有银行,3:股份制银行,4:城商行,5:农商行")
    private Integer bankType;

    @ApiModelProperty("不良贷款率(%)")
    private BigDecimal nplRatio;

    @ApiModelProperty("杠杆率(%)")
    private BigDecimal leverageRatio;

    @ApiModelProperty("资本充足率(%)")
    private BigDecimal car;

    @ApiModelProperty("贷款拨备率(%)")
    private BigDecimal loanProvisRatio;

    @ApiModelProperty("主体信用利差(二级资本);单位(BP)")
    private BigDecimal comTier2CreditSpread;

    @ApiModelProperty("主体超额利差(二级资本);单位(BP)")
    private BigDecimal comTier2ExcessSpread;

    @ApiModelProperty("主体估值收益率(二级资本);单位(%)")
    private BigDecimal comTier2CbYield;

    public Integer getBankType() {
        return bankType;
    }

    public void setBankType(Integer bankType) {
        this.bankType = bankType;
    }

    public BigDecimal getNplRatio() {
        return nplRatio;
    }

    public void setNplRatio(BigDecimal nplRatio) {
        this.nplRatio = nplRatio;
    }

    public BigDecimal getLeverageRatio() {
        return leverageRatio;
    }

    public void setLeverageRatio(BigDecimal leverageRatio) {
        this.leverageRatio = leverageRatio;
    }

    public BigDecimal getCar() {
        return car;
    }

    public void setCar(BigDecimal car) {
        this.car = car;
    }

    public BigDecimal getLoanProvisRatio() {
        return loanProvisRatio;
    }

    public void setLoanProvisRatio(BigDecimal loanProvisRatio) {
        this.loanProvisRatio = loanProvisRatio;
    }

    public BigDecimal getComTier2CreditSpread() {
        return comTier2CreditSpread;
    }

    public void setComTier2CreditSpread(BigDecimal comTier2CreditSpread) {
        this.comTier2CreditSpread = comTier2CreditSpread;
    }

    public BigDecimal getComTier2ExcessSpread() {
        return comTier2ExcessSpread;
    }

    public void setComTier2ExcessSpread(BigDecimal comTier2ExcessSpread) {
        this.comTier2ExcessSpread = comTier2ExcessSpread;
    }

    public BigDecimal getComTier2CbYield() {
        return comTier2CbYield;
    }

    public void setComTier2CbYield(BigDecimal comTier2CbYield) {
        this.comTier2CbYield = comTier2CbYield;
    }

}
