package com.innodealing.onshore.yieldspread.mapper.pgyieldspread.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.PgInsuBondYieldSpreadDO;
import com.innodealing.onshore.yieldspread.model.entity.pgyieldspread.group.PgInsuBondYieldSpreadGroupDO;


/**
 * pg保险债利差group Mapper
 *
 * <AUTHOR>
 **/
public interface PgInsuBondYieldSpreadGroupMapper extends SelectByGroupedQueryMapper<PgInsuBondYieldSpreadDO,
        PgInsuBondYieldSpreadGroupDO> {

}
