package com.innodealing.onshore.yieldspread.controller;

/**
 * 物化视图刷新控制接口
 * 使用Redis变量控制物化视图是否刷新，减少不必要的刷新操作
 * 
 * <AUTHOR>
 */
public interface MaterializedViewRefreshController {
    
    /**
     * 检查是否应该刷新物化视图
     * 
     * @param viewName 物化视图名称
     * @return true表示应该刷新，false表示跳过刷新
     */
    boolean shouldRefreshMaterializedView(String viewName);
    
    /**
     * 标记物化视图刷新完成
     * 
     * @param viewName 物化视图名称
     */
    void markMaterializedViewRefreshed(String viewName);
    
    /**
     * 强制启用物化视图刷新
     * 
     * @param viewName 物化视图名称
     * @param ttlSeconds 生效时间（秒）
     */
    void enableMaterializedViewRefresh(String viewName, long ttlSeconds);
    
    /**
     * 禁用物化视图刷新
     * 
     * @param viewName 物化视图名称
     * @param ttlSeconds 生效时间（秒）
     */
    void disableMaterializedViewRefresh(String viewName, long ttlSeconds);
    
    /**
     * 获取物化视图刷新状态
     * 
     * @param viewName 物化视图名称
     * @return 刷新状态描述
     */
    String getMaterializedViewRefreshStatus(String viewName);
    
    /**
     * 清除物化视图刷新控制缓存
     * 
     * @param viewName 物化视图名称
     */
    void clearMaterializedViewRefreshControl(String viewName);
    
    /**
     * 批量检查是否应该刷新物化视图
     * 
     * @param viewNames 物化视图名称列表
     * @return 需要刷新的视图名称列表
     */
    java.util.List<String> shouldRefreshMaterializedViews(java.util.List<String> viewNames);
}
